package com.linkBuy.internalService.job;

import com.linkBuy.mysql.dao.service.biz.BizCompanySkuService;
import com.linkBuy.mysql.dao.service.biz.BizCompanySpuService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 客户SPU价格更新定时任务
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CompanySpuJob {

    private final BizCompanySpuService bizCompanySpuService;
    private final BizCompanySkuService bizCompanySkuService;

    /**
     * 每分钟更新客户SPU的价格和利润率信息
     * 使用SQL直接更新，避免大量数据在程序中处理
     * 更新顺序：
     * 1. 先更新CompanySku的利润率（基于SupplierSku成本价）
     * 2. 再更新CompanySpu的价格和利润率范围（基于CompanySku数据）
     * 3. 最后更新CompanySpu的划线价（基于SupplierSku数据）
     */
    @Scheduled(fixedRate = 60000) // 60秒 = 1分钟
    @Transactional(rollbackFor = Exception.class)
    public void updateCompanySpuPriceInfo() {
        log.info("开始执行客户SPU价格信息更新任务");

        try {
            // 1. 先更新客户SKU的利润率（基于供应商SKU的成本价）
            int profitRateUpdatedCount = bizCompanySkuService.updateProfitRateFromSupplierCost();
            log.info("成功更新{}条客户SKU的利润率信息", profitRateUpdatedCount);

            // 2. 更新客户SPU的价格和利润率范围（基于客户SKU数据）
            int priceAndProfitUpdatedCount = bizCompanySpuService.updateSpuPriceAndProfitFromSku();
            log.info("成功更新{}条客户SPU的价格和利润率信息", priceAndProfitUpdatedCount);

            // 3. 更新客户SPU的划线价（基于供应商SKU数据的最小值）
            int strikethroughPriceUpdatedCount = bizCompanySpuService.updateSpuStrikethroughPriceFromSupplierSku();
            log.info("成功更新{}条客户SPU的划线价信息", strikethroughPriceUpdatedCount);

            log.info("客户SPU价格信息更新任务执行完成 - SKU利润率更新: {}条, SPU价格利润率更新: {}条, SPU划线价更新: {}条", 
                    profitRateUpdatedCount, priceAndProfitUpdatedCount, strikethroughPriceUpdatedCount);

        } catch (Exception e) {
            log.error("客户SPU价格信息更新任务执行失败", e);
            throw e;
        }
    }
}
