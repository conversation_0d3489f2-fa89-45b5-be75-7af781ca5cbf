import { defineStore } from 'pinia'
import { getRegionTree, type RegionInfo } from '@/api/region'

interface RegionState {
  regionTree: RegionInfo[]
  loading: boolean
  loaded: boolean
}

export const useRegionStore = defineStore('region', {
  state: (): RegionState => ({
    regionTree: [],
    loading: false,
    loaded: false
  }),

  getters: {
    // 获取区域树数据
    getRegionTree: (state) => state.regionTree,
    
    // 检查是否正在加载
    isLoading: (state) => state.loading,
    
    // 检查是否已加载
    isLoaded: (state) => state.loaded,

    // 根据区域编码查找区域信息
    findRegionByCode: (state) => (regionCode: string): RegionInfo | null => {
      if (!regionCode || !state.regionTree.length) return null

      const findInTree = (nodes: RegionInfo[]): RegionInfo | null => {
        for (const node of nodes) {
          if (node.regionCode === regionCode) {
            return node
          }
          if (node.children && node.children.length > 0) {
            const found = findInTree(node.children)
            if (found) return found
          }
        }
        return null
      }

      return findInTree(state.regionTree)
    },

    // 根据区域编码查找完整路径
    findRegionPath: (state) => (regionCode: string): string[] => {
      if (!regionCode || !state.regionTree.length) return []

      const findPath = (nodes: RegionInfo[], targetCode: string, currentPath: string[] = []): string[] | null => {
        for (const node of nodes) {
          const newPath = [...currentPath, node.regionCode]

          if (node.regionCode === targetCode) {
            return newPath
          }

          if (node.children && node.children.length > 0) {
            const found = findPath(node.children, targetCode, newPath)
            if (found) return found
          }
        }
        return null
      }

      return findPath(state.regionTree, regionCode) || []
    }
  },

  actions: {
    // 加载区域数据
    async loadRegionTree() {
      // 如果已经加载过且不在加载中，直接返回
      if (this.loaded && !this.loading) {
        return this.regionTree
      }

      // 如果正在加载中，等待加载完成
      if (this.loading) {
        return new Promise((resolve) => {
          const checkLoaded = () => {
            if (!this.loading) {
              resolve(this.regionTree)
            } else {
              setTimeout(checkLoaded, 100)
            }
          }
          checkLoaded()
        })
      }

      this.loading = true
      
      try {
        const response: any = await getRegionTree()
        if (response.code === 200) {
          this.regionTree = response.data || []
          this.loaded = true
          return this.regionTree
        } else {
          throw new Error(response.message || '获取区域数据失败')
        }
      } catch (error) {
        console.error('加载区域数据失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    // 重新加载区域数据
    async reloadRegionTree() {
      this.loaded = false
      this.regionTree = []
      return await this.loadRegionTree()
    },

    // 清空区域数据
    clearRegionData() {
      this.regionTree = []
      this.loaded = false
      this.loading = false
    }
  },

  // 持久化配置
  persist: {
    enabled: true,
    strategies: [
      {
        key: 'region-store',
        storage: localStorage,
        paths: ['regionTree', 'loaded']
      }
    ]
  }
}) 