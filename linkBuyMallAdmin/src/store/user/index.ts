import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getUserInfo as getUserInfoApi } from '@/api/auth'
import { getCompanyInfo } from '@/api/common'
import type { IUser } from '@/api/auth'
import type { IDataType } from '@/service/request/types'
import { getCurrentScode } from '@/utils/url'

export const useUser = defineStore('user', () => {
  // 用户信息按scode分别存储，支持多租户
  const userInfoMap = ref<Record<string, IUser>>({})
  const companyNameMap = ref<Record<string, string>>({})
  const isFirst = ref<boolean>(true)

  function setUserInfo(info: IUser, scode?: string) {
    const currentScode = scode || getCurrentScode()
    if (currentScode) {
      userInfoMap.value[currentScode] = info
    }
  }
  
  function getUserInfo(scode?: string): IUser | undefined {
    const currentScode = scode || getCurrentScode()
    return currentScode ? userInfoMap.value[currentScode] : undefined
  }

  function getScode(): string | undefined {
    return getCurrentScode() || undefined
  }
  
  function setCompanyName(name: string, scode?: string) {
    const currentScode = scode || getCurrentScode()
    if (currentScode) {
      companyNameMap.value[currentScode] = name
    }
  }
  
  function getCompanyName(scode?: string): string | undefined {
    const currentScode = scode || getCurrentScode()
    return currentScode ? companyNameMap.value[currentScode] : undefined
  }

  function setIsFirst(is: boolean) {
    isFirst.value = is
  }

  function getIsFirst(): boolean {
    return isFirst.value
  }
  async function refreshUserInfoAction(scode?: string) {
    try {
      const response: IDataType<IUser> = await getUserInfoApi()
      if (response && response.code === 200) {
        setUserInfo(response.data, scode)
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
    }
  }

  // 清除指定scode的用户信息
  function clearUserInfo(scode?: string) {
    const currentScode = scode || getCurrentScode()
    if (currentScode) {
      delete userInfoMap.value[currentScode]
      delete companyNameMap.value[currentScode]
    }
  }

  // 获取所有已登录的scode列表
  function getLoggedScodes(): string[] {
    return Object.keys(userInfoMap.value)
  }

  return {
    userInfoMap,
    companyNameMap,
    getUserInfo,
    setUserInfo,
    refreshUserInfoAction,
    getScode,
    setCompanyName,
    getCompanyName,
    setIsFirst,
    getIsFirst,
    clearUserInfo,
    getLoggedScodes
  }
}, {
  persist: {
    enabled: true,
    strategies: [
      {
        key: 'mall-admin-user-store',
        storage: localStorage,
        paths: ['userInfoMap', 'companyNameMap']
      }
    ]
  }
})