import type { IDataType, IPageData } from '@/service/request/types'
import $http from '@/service'

// 客户商品信息类型定义
export interface ICompanyProduct {
  id: number
  productName: string
  productCode: string
  supplierId: number
  supplierName: string
  categoryId: number
  categoryName: string
  status: number
  salePrice: number
  createTime: string
  [key: string]: any
}

// 商品统计类型定义
export interface IProductStatistics {
  totalCount: number
  onlineCount: number
  offlineCount: number
  pendingCount: number
}

// 供应商选项类型定义
export interface ISupplierOption {
  id: number
  supplierName: string
  supplierCode: string
}

// 获取客户商品列表
export function getProductList(params: any) {
  return $http.get<IDataType<IPageData<ICompanyProduct>>>({
    url: '/company-product-management/list',
    params
  })
}

// 获取客户商品详情
export function getProductDetail(id: number) {
  return $http.get<IDataType<ICompanyProduct>>({
    url: `/company-product-management/${id}`
  })
}

// 批量更新商品状态
export function batchUpdateStatus(data: any) {
  return $http.post<IDataType<boolean>>({
    url: '/company-product-management/batch/status',
    data
  })
}

// 批量调价
export function batchUpdatePrice(data: any) {
  return $http.post<IDataType<boolean>>({
    url: '/company-product-management/batch/price',
    data
  })
}

// 获取统计数据
export function getStatistics() {
  return $http.get<IDataType<IProductStatistics>>({
    url: '/company-product-management/statistics'
  })
}

// 获取供应商选项
export function getSupplierOptions() {
  return $http.get<IDataType<IPageData<ISupplierOption>>>({
    url: '/supplier/list',
    params: {
      page: 1,
      size: 200
    }
  })
} 