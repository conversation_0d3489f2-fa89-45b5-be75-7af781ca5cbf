import type { IDataType, IPageData } from '@/service/request/types'
import $http from '@/service'

// 优惠券数据接口
export interface CouponData {
  id?: number
  mallId: number
  couponCode?: string // 优惠券编码
  name: string
  type: number // 1-满减券（固定为1）
  discountValue: number // 优惠金额
  minOrderAmount?: number
  totalQuantity: number
  quantityType: number // 总量限制类型：1-不限制，2-限制数量
  remainingQuantity?: number
  receiveLimitType?: number // 领取限制类型：1-不限制领取次数，2-限制领取，3-每天限制领取
  perUserTotalLimit?: number // 每人总共限领次数
  perUserDailyLimit?: number // 每人每天限领次数
  startTime: string
  endTime: string
  receiveStartTime?: string // 领取开始时间
  receiveEndTime?: string // 领取结束时间
  status: number // 0-未开始 1-进行中 2-已结束
  promotionType: number // 1-买家领取 2-卖家发放
  useScope?: number // 1-全场通用 2-部分商品可用 3-部分商品不可用
  validityType?: number // 1-固定日期 2-领券当日起 3-领券次日起
  validityDays?: number // 有效天数
  description?: string
  createTime?: string
  updateTime?: string
}

// 优惠券统计数据接口
export interface CouponStatistics {
  totalCount: number
  notStartedCount: number
  ongoingCount: number
  endedCount: number
}

// 获取优惠券列表
export function getCouponList(params: any) {
  return $http.get<IDataType<IPageData<CouponData>>>({
    url: '/coupon/page',
    params
  })
}

// 获取优惠券统计
export function getCouponStatistics(mallId: number) {
  return $http.get<IDataType<CouponStatistics>>({
    url: '/coupon/statistics',
    params: { mallId }
  })
}

// 获取优惠券详情
export function getCouponDetail(id: number) {
  return $http.get<IDataType<CouponData>>({
    url: `/coupon/${id}`
  })
}

// 创建优惠券
export function createCoupon(data: CouponData) {
  return $http.post<IDataType<CouponData>>({
    url: '/coupon',
    data
  })
}

// 更新优惠券
export function updateCoupon(id: number, data: CouponData) {
  return $http.put<IDataType<CouponData>>({
    url: `/coupon/${id}`,
    data
  })
}

// 删除优惠券
export function deleteCoupon(id: number) {
  return $http.delete<IDataType<boolean>>({
    url: `/coupon/${id}`
  })
}

// 批量删除优惠券
export function batchDeleteCoupon(ids: number[]) {
  return $http.delete<IDataType<boolean>>({
    url: '/coupon/batch',
    data: ids
  })
}

// 提前开始优惠券
export function startCoupon(id: number) {
  return $http.post<IDataType<boolean>>({
    url: `/coupon/${id}/start`
  })
}

// 提前结束优惠券
export function endCoupon(id: number) {
  return $http.post<IDataType<boolean>>({
    url: `/coupon/${id}/end`
  })
}

// 手动发放优惠券
export function sendCoupon(id: number, userIds: number[], remark?: string) {
  return $http.post<IDataType<boolean>>({
    url: `/coupon/${id}/send`,
    params: {
      userIds,
      remark
    }
  })
} 