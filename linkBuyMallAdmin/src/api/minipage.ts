import type { IDataType, IPageData } from '@/service/request/types'
import $http from '@/service'

// 微页面数据类型定义
export interface MiniPage {
  id: number
  mallId: number
  pageName: string
  pageCode?: string
  pageTitle?: string
  pageDescription?: string
  backgroundColor?: string
  pageConfig?: string
  isHomePage: number // 0-否，1-是
  status: number // 0-禁用，1-启用
  sortOrder?: number
  remark?: string
  createTime?: string
  updateTime?: string
}

// 页面组件数据类型
export interface PageWidget {
  id: number
  pageId: number
  widgetId: string
  widgetType: string
  widgetData: string
  sortOrder: number
  isHidden: number // 0-显示，1-隐藏
  createTime?: string
  updateTime?: string
}

// 微页面详情响应
export interface MiniPageDetailResponse {
  page: MiniPage
  widgets: PageWidget[]
}

// 创建微页面参数
export interface CreateMiniPageParams {
  name: string
  description?: string
  config?: string
}

// 更新微页面参数
export interface UpdateMiniPageParams {
  name: string
  description?: string
  config?: string
}

// 保存页面配置参数
export interface SavePageConfigParams {
  config?: string
  widgets?: {
    widgetId: string
    widgetType: string
    widgetData: string
    sortOrder?: number
    hidden?: boolean
  }[]
}

// 分页查询微页面列表
export function getMinipageList(mallId: string | number, params: any) {
  return $http.get<IDataType<IPageData<MiniPage>>>({
    url: `/mall/${mallId}/miniPage/list`,
    params
  })
}

// 获取微页面详情
export function getMinipageDetail(mallId: string | number, id: number) {
  return $http.get<IDataType<MiniPageDetailResponse>>({
    url: `/mall/${mallId}/miniPage/${id}`
  })
}

// 创建微页面
export function createMinipage(mallId: string | number, data: CreateMiniPageParams) {
  return $http.post<IDataType<MiniPage>>({
    url: `/mall/${mallId}/miniPage`,
    data
  })
}

// 更新微页面
export function updateMinipage(mallId: string | number, id: number, data: UpdateMiniPageParams) {
  return $http.put<IDataType<MiniPage>>({
    url: `/mall/${mallId}/miniPage/${id}`,
    data
  })
}

// 删除微页面
export function deleteMinipage(mallId: string | number, id: number) {
  return $http.delete<IDataType<boolean>>({
    url: `/mall/${mallId}/miniPage/${id}`
  })
}

// 设置为首页
export function setHomePage(mallId: string | number, id: number) {
  return $http.put<IDataType<boolean>>({
    url: `/mall/${mallId}/miniPage/${id}/setHomePage`
  })
}

// 保存页面配置和组件数据
export function savePageConfig(mallId: string | number, id: number | string, data: SavePageConfigParams) {
  return $http.post<IDataType<boolean>>({
    url: `/mall/${mallId}/miniPage/${id}/saveConfig`,
    data
  })
} 