import type { IDataType } from '@/service/request/types'
import $http from '@/service'

// 腾讯地图地址解析响应类型
export interface TencentMapGeocoderResponse {
  status: number
  message: string
  requestId?: string
  result?: {
    title: string
    location: {
      lat: number
      lng: number
    }
    addressComponents: {
      province: string
      city: string
      district: string
      street: string
      streetNumber: string
    }
  }
}

/**
 * 获取腾讯地图Key
 * @returns 腾讯地图Key
 */
export function getTencentMapKey() {
  return $http.get<IDataType<string>>({
    url: '/common/tencentMap/getMapKey'
  })
}

/**
 * 腾讯地图地址解析（地址转坐标）
 * @param address 地址信息
 * @returns 解析结果
 */
export function tencentMapGeocoder(address: string) {
  return $http.get<IDataType<TencentMapGeocoderResponse>>({
    url: '/common/tencentMap/geocoder',
    params: {
      address
    }
  })
} 