import $http from '@/service'
import type { IDataType, IPageData } from '@/service/request/types'

// 图片分组类型
export interface ImageGroup {
  id: number
  fsAccountCode: string
  name: string
  createTime: string
  updateTime: string
  isDeleted: number
}

// 图片文件类型
export interface ImageFile {
  id: number
  fsAccountCode: string
  type: string
  name: string
  path: string
  tagId: string
  createTime: string
  updateTime: string
  isDeleted: number
}

// 分组相关
export function getGroupList() {
  return $http.get<IDataType<ImageGroup[]>>({
    url: '/imageCenter/group/list'
  })
}

export function addGroup(data: { name: string }) {
  return $http.post<IDataType<ImageGroup>>({
    url: '/imageCenter/group/add',
    data
  })
}

export function renameGroup(data: { id: string, name: string }) {
  return $http.post<IDataType<boolean>>({
    url: '/imageCenter/group/rename',
    data
  })
}

export function deleteGroup(data: { id: string }) {
  return $http.post<IDataType<boolean>>({
    url: '/imageCenter/group/delete',
    data
  })
}

// 图片相关
export function getFileList(params: { tagId?: string, name?: string, page?: number, pageSize?: number }) {
  return $http.get<IDataType<IPageData<ImageFile>>>({
    url: '/imageCenter/file/list',
    params
  })
}

// 修复：单文件上传 - 使用FormData
export function uploadFile(file: File, tagId?: string, name?: string) {
  const formData = new FormData()
  formData.append('file', file)
  if (tagId) {
    formData.append('tagId', tagId)
  }
  if (name) {
    formData.append('name', name)
  }
  
  return $http.post<IDataType<ImageFile>>({
    url: '/imageCenter/file/upload',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 修复：批量文件上传 - 使用FormData
export function batchUploadFile(files: File[], tagId?: string) {
  const formData = new FormData()
  files.forEach(file => {
    formData.append('files', file)
  })
  if (tagId) {
    formData.append('tagId', tagId)
  }
  
  return $http.post<IDataType<ImageFile[]>>({
    url: '/imageCenter/file/batchUpload',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

export function deleteFile(data: { id: string }) {
  return $http.post<IDataType<boolean>>({
    url: '/imageCenter/file/delete',
    data
  })
}

export function batchDeleteFile(data: { ids: string[] }) {
  return $http.post<IDataType<boolean>>({
    url: '/imageCenter/file/batchDelete',
    data
  })
}

export function batchMoveFile(data: { ids: string[], targetTagId: string }) {
  return $http.post<IDataType<boolean>>({
    url: '/imageCenter/file/batchMove',
    data
  })
}

export function renameFile(data: { id: string, name: string }) {
  return $http.post<IDataType<boolean>>({
    url: '/imageCenter/file/rename',
    data
  })
}

