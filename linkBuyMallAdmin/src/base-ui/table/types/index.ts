interface ITableStatus {
  name: string
  value: number
  color?: string
}

interface IProp {
  prop: string
  propType?: string
  label: string
  labelTip?: string
  labelTipWidth?: number
  minWidth?: string
  slotName?: string
  fixed?: string
  isCopy?: boolean
  sortable?: boolean | string
  isShow?: boolean
  align?: string,
  type?: string,
  reserveSelection?: boolean,
  selectable?: (row: any,index:number) => boolean;
}

interface ITable {
  pageSizes?: number[] //页码可选项
  propList: IProp[] // 表头字段
  showFooter?: boolean // 是否展示底部分页组件
  childrenProps?: any // 表格的属性
  pageSize?: number // 默认pageSize
  isHiddenTooltip?: boolean // 是否隐藏tooltip
}

export {  ITableStatus, IProp, ITable }
