<template>
  <div class="user-info">
    <!-- <span class="user-info-item" @click="goHome">
      首页
    </span> -->
    <el-divider direction="vertical" />
    <!-- <el-select class="user-info-item orgination userlist" v-model="selectCompany" placeholder="请选择组织">
      <el-option v-for="item in company" :key="item.id" :label="item.name" :value="item.id" style="width:320px!important;"/>
      <el-divider style="margin: 4px 0;" />
      <el-option key="all" label="全部组织" value="all" class="all-org-option" />
    </el-select> -->
    <el-dropdown>
      <span class="el-dropdown-link">
        <img class="user-avatar" :src="avatar" alt="Profile Picture" />
      </span>
      <template #dropdown>
        <el-dropdown-menu class="custom-dropdown-menu" style="width:250px!important;">
          <p class="username">{{ userInfo && userInfo.firstName }} {{ userInfo && userInfo.lastName }}</p>
          <p class="email">{{ userInfo && userInfo.email }}</p>
          <el-dropdown-item @click="goAccountSettings">
            <span class="operate"><el-icon>
                <User />
              </el-icon> 账户信息</span>
          </el-dropdown-item>
          <el-dropdown-item @click="handleLogout">
            <span class="operate"><el-icon>
                <SwitchButton />
              </el-icon> 退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, ref, onMounted, watch } from 'vue'
import { logout } from '@/utils/auth'
import { useUser } from '@/store/user'
import { useRouter } from 'vue-router'
import { User, SwitchButton, OfficeBuilding } from '@element-plus/icons-vue'
import { getCurrentScode } from '@/utils/url'

export default defineComponent({
  name: 'user-info',
  components: {
    User,
    SwitchButton,
    OfficeBuilding
  },
  setup() {
    const router = useRouter()
    const avatar = ref('')
    const handleImgSrc = async () => {
      const m = await import('@/assets/images/common/avatar.png')
      avatar.value = m.default
    }

    const userStore = useUser()
    const userInfo = computed(() => userStore.getUserInfo())
    const company = {}

    const selectCompany = {}

    onMounted(async () => {
      handleImgSrc()
    })

    const handleLogout = () => {
      logout()
    }

    const goHome = () => {
      const scode = getCurrentScode()
      if (scode) {
        router.push({ path: '/', query: { scode } })
      } else {
        router.push('/')
      }
    }

    const goAccountSettings = () => {
      const scode = getCurrentScode()
      if (scode) {
        router.push({ path: '/account/edit-profile', query: { scode } })
      } else {
        router.push('/account/edit-profile')
      }
    }

    return {
      avatar,
      userInfo,
      company,
      selectCompany,
      handleLogout,
      goHome,
      goAccountSettings
    }
  }
})
</script>

<style lang="scss" scoped>
.user-info {
  line-height: 52px;

  :deep(.el-dropdown-link) {
    font-size: 14px;
    margin-top: 20px;
  }

  &-item {
    cursor: pointer;
    margin-right: 20px;
    font-size: 14px;

    &.language {
      margin-right: 0;
      margin-left: 30px;
    }

    &.orgination {
      width: 118px;

      :deep(.el-select__wrapper) {
        box-shadow: none;
        background-color: #F8F9FB !important;
        border-radius: 4px;
        height: 28px;
      }
    }
  }

  :deep(.el-dropdown-menu__item:not(.is-disabled):hover) {
    background-color: rgba(91, 143, 249, 0.04) !important;
  }

  :deep(.el-dropdown-menu__item) {
    white-space: unset;
    line-height: 20px;
    padding: 1px 16px !important;

    .username {
      color: var(--el-text-color-primary);
      font-weight: 700;
      font-size: 18px;
    }

    .email {
      font-size: 14px;
      color: var(--el-text-color-secondary);
    }
  }
}
.el-scrollbar__view ul{
  display: block;
  li{
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
.user-avatar {
  margin-right: 10px;
  transform: scale(1.3);
  width: 20px;
  height: 20px;
  margin-top: -3px;
}

.custom-dropdown-menu {
  .username {
    font-weight: bold;
    font-size: 16px;
    padding-left: 20px;
    margin-bottom: 10px;
  }

  .email {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    margin-bottom: 20px !important;
    padding-left: 20px;
  }

  :deep(.el-dropdown-menu__item) {
    width: 250px !important;

    &:not(.is-disabled):hover {
      background-color: rgba(91, 143, 249, 0.04) !important;
    }
  }

  .el-dropdown-menu__item {
    display: flex;
    align-items: center;
    padding: 10px 20px;
    width: 250px !important;

    .operate {
      display: inline-block;
      padding: 9px 0;
      font-size: 14px;
    }

    i {
      margin-right: 6px;
      top: 2px;
    }
  }
}

:deep(.el-select-dropdown__item.all-org-option) {
  font-weight: bold;
  color: var(--el-color-primary);
  display: flex;
  align-items: center;

  &::before {
    content: '🏢';
    margin-right: 6px;
  }
}
</style>
