<template>
  <div class="layout">
    <el-container class="page">
      <el-header class="page-header">
        <nav-header :top-menus="topMenus" @foldChange="handleFoldChange" />
      </el-header>
      <el-container class="layout-content">
        <el-aside v-if="menuStore.currentRoutePath != '/dashboard'" width="220px" style="padding-top: 5px;background-color: #f0f2f5;">
          <nav-menu :collapse="isCollapse" :side-menus="sideMenus" />
        </el-aside>
        <el-main class="page-content">
          <mob-breadcrumb v-if="menuStore.currentRoutePath != '/dashboard'" :breadcrumbs="breadcrumbs" />
          <div class="page-info" v-if="!$route.meta.hideWhiteBg">
            <router-view></router-view>
          </div>
          <router-view v-else></router-view>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted, watch } from 'vue'
import NavMenu from '@/layout/nav-menu/index.vue'
import NavHeader from '@/layout/nav-header/index.vue'
import MobBreadcrumb from '@/base-ui/breadcrumb'
import { useRoute } from 'vue-router'
import { useMenuStore } from '@/store/menu'
import { useUser } from '@/store/user'

export default defineComponent({
  name: 'layout',
  props: {},
  components: {
    NavMenu,
    NavHeader,
    MobBreadcrumb
  },
  setup() {
    const isCollapse = ref(false)
    const route = useRoute()
    const menuStore = useMenuStore()
    const userStore = useUser()

    const permissions = userStore.getUserInfo()?.permissions

    // 使用menu store中的topMenus
    const topMenus = computed(() => menuStore.topMenus)

    // 直接使用 menu store 中的 activeSideMenus
    const sideMenus = computed(() => menuStore.activeSideMenus)

    // 菜单展开/收起
    const handleFoldChange = (isFold: boolean) => {
      isCollapse.value = !isCollapse.value
    }

    // 获取面包屑导航
    const breadcrumbs = computed(() => {
      // 面包屑仍然可以使用 findMenuByPath 或 getBreadcrumbs 方法，因为它们查找的是完整的 menuList
      return menuStore.getBreadcrumbs(route.path)
    })

    onMounted(async () => {
      // 获取菜单数据
      if (!menuStore.isLoaded) {
        await menuStore.fetchMenu()
      }

      // menuStore.setCurrentRoutePath(route.path)
    })

    return {
      menuStore,
      isCollapse,
      topMenus,
      sideMenus,
      handleFoldChange,
      breadcrumbs
    }
  }
})
</script>

<style scoped lang="scss">
.layout {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.layout-content {
  height: calc(100% - 52px);
}

.page {
  height: 100%;

  &-header {
    background-color: var(--el-color-white);
  }
}

.page-content {
  height: 100%;
  // padding: 10px 20px 20px 20px;

  .page-info {
    background-color: var(--el-color-white);
    // padding: 10px;
    // border-radius: 5px;
    height: calc(100% - 34px);
    box-shadow: 0px 1px 6px 0px rgba(0, 0, 0, 0.06);
    overflow-y: scroll;
  }
}

.el-header,
.el-footer {
  display: flex;
  color: var(--el-text-color-regular);
  text-align: center;
  align-items: center;
}

.el-header {
  height: 52px !important;
}

.el-aside {
  overflow-x: hidden;
  overflow-y: auto;
  line-height: 42px;
  text-align: left;
  cursor: pointer;
  transition: width 0.3s linear;
  scrollbar-width: none;
  /* firefox */
  -ms-overflow-style: none;
  /* IE 10+ */

  &::-webkit-scrollbar {
    display: none;
  }
}

.el-main {
  color: var(--el-text-color-regular);
  background-color: var(--el-bg-color-base);
  --el-main-padding: 0px;
}
</style>