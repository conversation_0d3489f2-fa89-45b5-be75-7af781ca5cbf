interface IMeta {
  hidden?: boolean
  keepAlive?: boolean
  activeMenu?: string
  title?: string
}

interface IMenu {
  id: number
  icon?: string
  url: string
  name: string
  type: number
  children?: IMenu[]
  parentId?: number
  meta?: IMeta
  permission?: string
}

export { IMeta, IMenu }

export interface MenuDto {
  id: string
  name: string
  code: string
  type: string
  parentId: string
  functionId: string
  sortOrder: number
  icon: string
  path: string
  pageComponent: string
  isSelected: boolean
  isVisible: boolean
  isEnabled: boolean
  children?: MenuDto[]
}
