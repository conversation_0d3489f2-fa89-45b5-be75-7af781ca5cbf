<template>
  <div class="wechat-management">
    <div class="page-header">
      <h2>微信公众号</h2>
      <p>配置微信公众号接入</p>
    </div>
    
    <div class="content-area">
      <el-card>
        <div class="card-header">
          <span>公众号配置</span>
          <el-button type="primary">
            <el-icon><Setting /></el-icon>
            配置接入
          </el-button>
        </div>
        
        <div class="wechat-content">
          <p>微信公众号功能正在开发中...</p>
          <p>当前商城ID: {{ mallId }}</p>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { Setting } from '@element-plus/icons-vue'

export default {
  name: 'WechatManagement',
  components: {
    Setting
  },
  computed: {
    mallId() {
      return this.$route.query.mallId || this.$route.params.mallId
    }
  }
}
</script>

<style scoped>
.wechat-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 24px;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.content-area {
  background: #fff;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.wechat-content {
  padding: 40px;
  text-align: center;
  color: #666;
}
</style> 