<template>
  <div class="notice-management">
    <div class="page-header">
      <h2>商城公告</h2>
      <p>发布和管理商城公告信息</p>
    </div>
    
    <div class="content-area">
      <el-card>
        <div class="card-header">
          <span>公告列表</span>
          <el-button type="primary">
            <el-icon><Plus /></el-icon>
            新建公告
          </el-button>
        </div>
        
        <div class="notice-content">
          <p>商城公告功能正在开发中...</p>
          <p>当前商城ID: {{ mallId }}</p>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { Plus } from '@element-plus/icons-vue'

export default {
  name: 'NoticeManagement',
  components: {
    Plus
  },
  computed: {
    mallId() {
      return this.$route.query.mallId || this.$route.params.mallId
    }
  }
}
</script>

<style scoped>
.notice-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 24px;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.content-area {
  background: #fff;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.notice-content {
  padding: 40px;
  text-align: center;
  color: #666;
}
</style> 