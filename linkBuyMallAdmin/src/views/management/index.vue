<template>
  <div class="management-container">
    <!-- 左侧菜单 -->
    <div class="management-sidebar">
      <div class="sidebar-header">
        <div class="header-content">
          <h3>{{ mallName }}</h3>
          <el-button class="back-button" @click="handleBack" type="text" size="small">
            <el-icon>
              <ArrowLeft />
            </el-icon>
            返回
          </el-button>
        </div>
      </div>

      <div class="menu-list">
        <template v-for="menu in menuList" :key="menu.key">
          <!-- 主菜单项 -->
          <div class="menu-item" :class="{ active: activeMenu === menu.key }" @click="handleMenuClick(menu)">
            <div class="menu-icon">
              <el-icon>
                <component :is="menu.icon" />
              </el-icon>
            </div>
            <span class="menu-title">{{ menu.title }}</span>
            <el-icon v-if="menu.children && menu.children.length > 0" class="menu-arrow"
              :class="{ expanded: expandedMenus.includes(menu.key) }">
              <ArrowRight />
            </el-icon>
          </div>

          <!-- 对应的子菜单，紧跟在父菜单后面 -->
          <div v-if="menu.children && expandedMenus.includes(menu.key)" class="submenu-list">
            <div v-for="submenu in menu.children" :key="submenu.key" class="submenu-item"
              :class="{ active: activeMenu === submenu.key }" @click="handleMenuClick(submenu)">
              <div class="submenu-icon">
                <el-icon>
                  <component :is="submenu.icon" />
                </el-icon>
              </div>
              <span class="submenu-title">{{ submenu.title }}</span>
            </div>
          </div>
        </template>
      </div>
    </div>

    <!-- 右侧内容区域 -->
    <div class="management-content">
      <div class="content-header">
        <div class="breadcrumb">
          <span class="breadcrumb-item">商城管理</span>
          <el-icon class="breadcrumb-separator">
            <ArrowRight />
          </el-icon>
          <span class="breadcrumb-item active">{{ getCurrentMenuTitle() }}</span>
        </div>
      </div>

      <div class="content-main">
        <!-- 动态组件区域 -->
        <router-view v-if="showRouterView" />

        <!-- 默认欢迎页面 -->
        <div v-else class="welcome-content">
          <div class="welcome-card">
            <div class="welcome-icon">
              <el-icon>
                <Management />
              </el-icon>
            </div>
            <h2>欢迎使用商城管理</h2>
            <p>请从左侧菜单选择要管理的模块开始管理您的商城</p>

            <div class="quick-actions">
              <div class="action-grid">
                <div v-for="action in quickActions" :key="action.key" class="action-item"
                  @click="handleMenuClick(action)">
                  <div class="action-icon">
                    <el-icon>
                      <component :is="action.icon" />
                    </el-icon>
                  </div>
                  <div class="action-info">
                    <h4>{{ action.title }}</h4>
                    <p>{{ action.description }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  Grid,
  TrendCharts,
  Monitor,
  Share,
  Setting,
  ArrowRight,
  ArrowLeft,
  Management,
  Tickets,
  Timer,
  Trophy,
  Present,
  Bell,
  Document,
  Service,
  ChatLineRound,
  Iphone,
  ChromeFilled,
  Shop,
  Box,
  User,
  Message,
  Operation,
  Tools
} from '@element-plus/icons-vue'
import { mallDetail } from '@/api/mall'

export default {
  name: 'ManagementIndex',
  components: {
    Grid,
    TrendCharts,
    Monitor,
    Share,
    Setting,
    ArrowRight,
    ArrowLeft,
    Management,
    Tickets,
    Timer,
    Trophy,
    Present,
    Bell,
    Document,
    Service,
    ChatLineRound,
    Iphone,
    ChromeFilled,
    Shop,
    Box,
    User,
    Message,
    Operation,
    Tools
  },
  data() {
    return {
      activeMenu: '',
      expandedMenus: [], // 展开的菜单
      mallName: '商城管理', // 默认标题
      menuList: [
        {
          key: 'category',
          title: '分类管理',
          icon: 'Grid',
          route: '/management/category',
          description: '管理商品分类'
        },
        {
          key: 'marketing',
          title: '营销中心',
          icon: 'TrendCharts',
          description: '营销活动管理',
          children: [
            {
              key: 'coupon',
              title: '优惠券',
              icon: 'Tickets',
              route: '/management/marketing/coupon',
              description: '优惠券管理'
            },
            {
              key: 'seckill',
              title: '限时秒杀',
              icon: 'Timer',
              route: '/management/marketing/seckill',
              description: '秒杀活动管理'
            },
            {
              key: 'activity',
              title: '平台活动',
              icon: 'Trophy',
              route: '/management/marketing/activity',
              description: '平台活动管理'
            },
            {
              key: 'lottery',
              title: '幸运抽奖',
              icon: 'Present',
              route: '/management/marketing/lottery',
              description: '抽奖活动管理'
            }
          ]
        },
        {
          key: 'application',
          title: '应用中心',
          icon: 'Monitor',
          description: '应用功能管理',
          children: [
            {
              key: 'notice',
              title: '商城公告',
              icon: 'Bell',
              route: '/management/application/notice',
              description: '商城公告管理'
            },
            {
              key: 'news',
              title: '商城资讯',
              icon: 'Document',
              route: '/management/application/news',
              description: '商城资讯管理'
            },
            {
              key: 'service',
              title: '在线客服',
              icon: 'Service',
              route: '/management/application/service',
              description: '在线客服配置'
            }
          ]
        },
        {
          key: 'channel',
          title: '渠道',
          icon: 'Share',
          description: '渠道管理',
          children: [
            {
              key: 'wechat',
              title: '微信公众号',
              icon: 'ChatLineRound',
              route: '/management/channel/wechat',
              description: '微信公众号配置'
            },
            {
              key: 'miniprogram',
              title: '微信小程序',
              icon: 'Iphone',
              route: '/management/channel/miniprogram',
              description: '微信小程序配置'
            },
            {
              key: 'h5',
              title: 'H5',
              icon: 'ChromeFilled',
              route: '/management/channel/h5',
              description: 'H5页面配置'
            }
          ]
        },
        {
          key: 'settings',
          title: '设置',
          icon: 'Setting',
          description: '系统设置',
          children: [
            {
              key: 'mall',
              title: '商城设置',
              icon: 'Shop',
              route: '/management/settings/mall',
              description: '商城基本设置'
            },
            {
              key: 'product',
              title: '商品设置',
              icon: 'Box',
              route: '/management/settings/product',
              description: '商品相关设置'
            },
            {
              key: 'user',
              title: '用户设置',
              icon: 'User',
              route: '/management/settings/user',
              description: '用户相关设置'
            },
            {
              key: 'notify',
              title: '通知买家',
              icon: 'Message',
              route: '/management/settings/notify',
              description: '买家通知设置'
            },
            {
              key: 'trade',
              title: '交易设置',
              icon: 'Operation',
              route: '/management/settings/trade',
              description: '交易相关设置'
            },
            {
              key: 'maintenance',
              title: '系统维护',
              icon: 'Tools',
              route: '/management/settings/maintenance',
              description: '系统维护设置'
            }
          ]
        }
      ]
    }
  },
  computed: {
    showRouterView() {
      return this.$route.path !== '/management' && this.$route.path !== '/management/'
    },
    quickActions() {
      // 返回前4个常用功能作为快捷操作
      return [
        this.menuList[0], // 分类管理
        this.menuList[1].children[0], // 优惠券
        this.menuList[2].children[0], // 商城公告
        this.menuList[4].children[0]  // 商城设置
      ]
    }
  },
  watch: {
    '$route'(to) {
      // 根据当前路由设置活跃菜单
      this.setActiveMenuFromRoute(to.path)
    }
  },
  mounted() {
    // 初始化时根据当前路由设置活跃菜单
    this.setActiveMenuFromRoute(this.$route.path)
    // 获取商城详情
    this.fetchMallDetail()
  },
  methods: {
    handleMenuClick(menu) {
      // 如果有子菜单，切换展开状态
      if (menu.children && menu.children.length > 0) {
        const index = this.expandedMenus.indexOf(menu.key)
        if (index > -1) {
          this.expandedMenus.splice(index, 1)
        } else {
          this.expandedMenus.push(menu.key)
        }
        return
      }

      // 只有具有route属性的菜单项才进行路由跳转
      if (menu.route) {
        this.activeMenu = menu.key
        // 路由跳转，保持 mallId 参数
        if (this.$route.path !== menu.route) {
          this.$router.push({
            path: menu.route,
            query: {
              ...this.$route.query,
              mallId: this.$route.query.mallId
            }
          })
        }
      }
    },
    getCurrentMenuTitle() {
      // 先查找主菜单
      let currentMenu = this.menuList.find(menu => menu.key === this.activeMenu)
      if (currentMenu) {
        return currentMenu.title
      }

      // 再查找子菜单
      for (const menu of this.menuList) {
        if (menu.children) {
          const submenu = menu.children.find(child => child.key === this.activeMenu)
          if (submenu) {
            return submenu.title
          }
        }
      }

      return '商城管理'
    },
    setActiveMenuFromRoute(path) {
      // 先查找主菜单中有route的项
      let menu = this.menuList.find(menu => menu.route === path)
      if (menu) {
        this.activeMenu = menu.key
        return
      }

      // 再查找子菜单
      for (const mainMenu of this.menuList) {
        if (mainMenu.children) {
          const submenu = mainMenu.children.find(child => child.route === path)
          if (submenu) {
            this.activeMenu = submenu.key
            // 自动展开父菜单
            if (!this.expandedMenus.includes(mainMenu.key)) {
              this.expandedMenus.push(mainMenu.key)
            }
            return
          }
        }
      }

      this.activeMenu = ''
    },
    handleBack() {
      // 返回商城列表页面
      this.$router.push('/mall')
    },
    async fetchMallDetail() {
      try {
        // 从路由参数或其他方式获取商城ID
        const mallId = this.getMallId()
        if (mallId) {
          const response = await mallDetail(Number(mallId))
          if (response.code === 200 && response.data) {
            this.mallName = response.data.name
          }
        }
      } catch (error) {
        console.error('获取商城详情失败:', error)
        // 保持默认标题
      }
    },
    getMallId() {
      // 统一获取商城ID的方法
      return this.$route.query.mallId || this.$route.params.mallId
    }
  }
}
</script>

<style scoped>
.management-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  display: flex;
  background: #f5f5f5;
  overflow: hidden;
}

/* 左侧菜单样式 */
.management-sidebar {
  width: 240px;
  background: #fff;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 14px 20px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafafa;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.back-button {
  padding: 6px 8px;
  color: #666;
  font-size: 14px;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.back-button:hover {
  color: #409EFF;
  background: #f0f9ff;
}

.back-button .el-icon {
  margin-right: 6px;
  font-size: 14px;
}

.sidebar-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.menu-list {
  flex: 1;
  padding: 10px 0;
  overflow-y: auto;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
  position: relative;
}

.menu-item:hover {
  background: #f0f9ff;
  color: #409EFF;
}

.menu-item.active {
  background: #e6f7ff;
  color: #409EFF;
  border-left-color: #409EFF;
}

.menu-icon {
  margin-right: 12px;
  font-size: 18px;
  width: 20px;
  display: flex;
  justify-content: center;
}

.menu-title {
  font-size: 14px;
  font-weight: 500;
  flex: 1;
}

.menu-arrow {
  font-size: 12px;
  transition: transform 0.3s ease;
}

.menu-arrow.expanded {
  transform: rotate(90deg);
}

/* 子菜单样式 */
.submenu-list {
  background: #f8f9fa;
}

.submenu-item {
  display: flex;
  align-items: center;
  padding: 10px 20px 10px 40px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.submenu-item:hover {
  background: #e6f7ff;
  color: #409EFF;
}

.submenu-item.active {
  background: #d6f7ff;
  color: #409EFF;
  border-left-color: #409EFF;
}

.submenu-icon {
  margin-right: 10px;
  font-size: 16px;
  width: 18px;
  display: flex;
  justify-content: center;
}

.submenu-title {
  font-size: 13px;
  font-weight: 400;
}

/* 右侧内容区域样式 */
.management-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.content-header {
  background: #fff;
  padding: 16px 24px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.breadcrumb {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.breadcrumb-item {
  color: #666;
}

.breadcrumb-item.active {
  color: #333;
  font-weight: 500;
}

.breadcrumb-separator {
  margin: 0 8px;
  color: #ccc;
  font-size: 12px;
}

.content-main {
  color: var(--el-text-color-regular);
  background-color: var(--el-bg-color-base);
  --el-main-padding: 0px;

  background-color: var(--el-color-white);
  /* padding: 10px; */
  border-radius: 5px;
  /* margin-top: 10px; */
  height: calc(100% - 34px);
  box-shadow: 0px 1px 6px 0px rgba(0, 0, 0, 0.06);
  overflow-y: scroll;
}

/* 欢迎页面样式 */
.welcome-content {
  padding: 40px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 100%;
}

.welcome-card {
  background: #fff;
  border-radius: 12px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  max-width: 800px;
  width: 100%;
}

.welcome-icon {
  font-size: 64px;
  color: #409EFF;
  margin-bottom: 24px;
}

.welcome-card h2 {
  font-size: 24px;
  color: #333;
  margin: 0 0 12px 0;
  font-weight: 600;
}

.welcome-card p {
  font-size: 16px;
  color: #666;
  margin: 0 0 40px 0;
  line-height: 1.5;
}

.quick-actions {
  margin-top: 40px;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
}

.action-item:hover {
  background: #e6f7ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.action-icon {
  font-size: 32px;
  color: #409EFF;
  margin-right: 16px;
  flex-shrink: 0;
}

.action-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #333;
  font-weight: 600;
}

.action-info p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .management-sidebar {
    width: 200px;
  }

  .menu-item {
    padding: 10px 16px;
  }

  .menu-title {
    font-size: 13px;
  }

  .submenu-item {
    padding: 8px 16px 8px 32px;
  }

  .content-header {
    padding: 12px 16px;
  }

  .welcome-content {
    padding: 20px;
  }

  .welcome-card {
    padding: 30px 20px;
  }

  .action-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .management-container {
    flex-direction: column;
  }

  .management-sidebar {
    width: 100%;
    height: auto;
    max-height: 300px;
  }

  .menu-list {
    display: flex;
    overflow-x: auto;
    padding: 10px;
  }

  .menu-item {
    flex-direction: column;
    min-width: 80px;
    padding: 8px;
    text-align: center;
  }

  .menu-icon {
    margin-right: 0;
    margin-bottom: 4px;
  }

  .menu-title {
    font-size: 12px;
  }
}
</style>