<template>
  <div class="maintenance-settings">
    <div class="page-header">
      <h2>系统维护</h2>
      <p>系统维护和升级管理</p>
    </div>
    
    <div class="content-area">
      <el-card>
        <div class="card-header">
          <span>系统维护设置</span>
          <el-button type="primary">
            <el-icon><Setting /></el-icon>
            保存设置
          </el-button>
        </div>
        
        <div class="maintenance-settings-content">
          <p>系统维护功能正在开发中...</p>
          <p>当前商城ID: {{ mallId }}</p>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { Setting } from '@element-plus/icons-vue'

export default {
  name: 'MaintenanceSettings',
  components: {
    Setting
  },
  computed: {
    mallId() {
      return this.$route.query.mallId || this.$route.params.mallId
    }
  }
}
</script>

<style scoped>
.maintenance-settings {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 24px;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.content-area {
  background: #fff;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.maintenance-settings-content {
  padding: 40px;
  text-align: center;
  color: #666;
}
</style> 