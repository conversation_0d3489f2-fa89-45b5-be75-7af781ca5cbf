<template>
  <div class="notify-settings">
    <div class="page-header">
      <h2>通知买家</h2>
      <p>买家通知消息设置</p>
    </div>
    
    <div class="content-area">
      <el-card>
        <div class="card-header">
          <span>通知设置</span>
          <el-button type="primary">
            <el-icon><Setting /></el-icon>
            保存设置
          </el-button>
        </div>
        
        <div class="notify-settings-content">
          <p>通知买家功能正在开发中...</p>
          <p>当前商城ID: {{ mallId }}</p>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { Setting } from '@element-plus/icons-vue'

export default {
  name: 'NotifySettings',
  components: {
    Setting
  },
  computed: {
    mallId() {
      return this.$route.query.mallId || this.$route.params.mallId
    }
  }
}
</script>

<style scoped>
.notify-settings {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 24px;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.content-area {
  background: #fff;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.notify-settings-content {
  padding: 40px;
  text-align: center;
  color: #666;
}
</style> 