<template>
  <div class="seckill-management">
    <div class="page-header">
      <h2>限时秒杀</h2>
      <p>设置限时秒杀商品和活动</p>
    </div>
    
    <div class="content-area">
      <el-card>
        <div class="card-header">
          <span>秒杀活动列表</span>
          <el-button type="primary">
            <el-icon><Plus /></el-icon>
            新建秒杀活动
          </el-button>
        </div>
        
        <div class="seckill-content">
          <p>限时秒杀功能正在开发中...</p>
          <p>当前商城ID: {{ mallId }}</p>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { Plus } from '@element-plus/icons-vue'

export default {
  name: 'SeckillManagement',
  components: {
    Plus
  },
  computed: {
    mallId() {
      return this.$route.query.mallId || this.$route.params.mallId
    }
  }
}
</script>

<style scoped>
.seckill-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 24px;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.content-area {
  background: #fff;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.seckill-content {
  padding: 40px;
  text-align: center;
  color: #666;
}
</style> 