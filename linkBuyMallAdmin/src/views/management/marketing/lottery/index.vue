<template>
  <div class="lottery-management">
    <div class="page-header">
      <h2>幸运抽奖</h2>
      <p>创建抽奖活动吸引用户</p>
    </div>
    
    <div class="content-area">
      <el-card>
        <div class="card-header">
          <span>抽奖活动列表</span>
          <el-button type="primary">
            <el-icon><Plus /></el-icon>
            新建抽奖活动
          </el-button>
        </div>
        
        <div class="lottery-content">
          <p>幸运抽奖功能正在开发中...</p>
          <p>当前商城ID: {{ mallId }}</p>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { Plus } from '@element-plus/icons-vue'

export default {
  name: 'LotteryManagement',
  components: {
    Plus
  },
  computed: {
    mallId() {
      return this.$route.query.mallId || this.$route.params.mallId
    }
  }
}
</script>

<style scoped>
.lottery-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 24px;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.content-area {
  background: #fff;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.lottery-content {
  padding: 40px;
  text-align: center;
  color: #666;
}
</style> 