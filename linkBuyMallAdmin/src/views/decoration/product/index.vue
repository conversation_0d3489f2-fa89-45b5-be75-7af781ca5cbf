<template>
  <div class="product-decoration-container">
    <div class="page-header">
      <h2>商品组装修</h2>
      <p>自定义商品详情页面样式和布局</p>
    </div>
    
    <div class="content-area">
      <el-card>
        <div class="coming-soon">
          <el-icon size="64"><Tools /></el-icon>
          <h3>功能开发中...</h3>
          <p>商品详情页面装修功能正在开发中，敬请期待！</p>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { Tools } from '@element-plus/icons-vue'
import decorationMixin from '@/mixins/decorationMixin.js'

export default {
  name: 'ProductDecoration',
  components: {
    Tools
  },
  mixins: [decorationMixin],
  mounted() {
    console.log('商品详情装修页面已加载，mallId:', this.getMallId())
  }
}
</script>

<style scoped>
.product-decoration-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100%;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #333;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.content-area {
  max-width: 800px;
}

.coming-soon {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.coming-soon .el-icon {
  color: #409EFF;
  margin-bottom: 16px;
}

.coming-soon h3 {
  margin: 0 0 12px 0;
  font-size: 20px;
  color: #333;
}

.coming-soon p {
  margin: 0;
  font-size: 14px;
}
</style> 