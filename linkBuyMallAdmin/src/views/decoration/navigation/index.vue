<template>
  <div class="navigation-container">
    <div class="navigation-content">
      <!-- 左侧预览区域 -->
      <div class="preview-section">
        <PhonePreview>
          <!-- 底部导航栏 -->
          <template #bottom-nav>
            <BottomNavigation 
              :navigation-config="navigationConfig"
              :style-config="styleConfig"
              :active-nav-index="activeNavIndex"
              @nav-click="switchNav"
            />
          </template>
        </PhonePreview>
      </div>

      <!-- 右侧配置区域 -->
      <div class="config-section">
        <!-- Tab 切换 -->
        <div class="config-tabs">
          <div 
            class="tab-item"
            :class="{ active: activeTab === 'content' }"
            @click="activeTab = 'content'"
          >
            内容
          </div>
          <div 
            class="tab-item"
            :class="{ active: activeTab === 'style' }"
            @click="activeTab = 'style'"
          >
            样式
          </div>
        </div>

        <!-- 内容Tab -->
        <div v-show="activeTab === 'content'" class="tab-content">
          <!-- 底部导航开关 -->
          <div class="config-panel">
            <div class="panel-header">
              <h3>底部导航</h3>
            </div>
            
            <div class="panel-content">
              <div class="form-group">
                <div class="nav-toggle">
                  <el-switch 
                    v-model="navigationConfig.enabled" 
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 选择样式 -->
          <div v-if="navigationConfig.enabled" class="config-panel">
            <div class="panel-header">
              <h3>选择样式</h3>
            </div>
            
            <div class="panel-content">
              <div class="form-group">
                <div class="style-selector">
                  <div 
                    class="style-option"
                    :class="{ active: navigationConfig.displayType === 'icon-text' }"
                    @click="navigationConfig.displayType = 'icon-text'"
                  >
                    图片+文字
                  </div>
                  <div 
                    class="style-option"
                    :class="{ active: navigationConfig.displayType === 'icon' }"
                    @click="navigationConfig.displayType = 'icon'"
                  >
                    图片
                  </div>
                  <div 
                    class="style-option"
                    :class="{ active: navigationConfig.displayType === 'text' }"
                    @click="navigationConfig.displayType = 'text'"
                  >
                    文字
                  </div>
                </div>
              </div>
            </div>
          </div>



          <!-- 导航内容 -->
          <div v-if="navigationConfig.enabled" class="config-panel">
            <div class="panel-header">
              <h3>导航内容</h3>
            </div>
            
            <div class="panel-content">
              <!-- 导航列表 -->
              <div class="nav-list">
                <div 
                  v-for="(nav, index) in navigationConfig.navList" 
                  :key="index"
                  class="nav-config-item"
                >
                  <div class="nav-item-header">
                    <span class="nav-item-title">导航{{ index + 1 }}</span>
                    <el-button 
                      v-if="navigationConfig.navList.length > 2"
                      type="danger" 
                      text 
                      size="small"
                      @click="removeNav(index)"
                    >
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </div>
                  
                  <div class="nav-item-content">
                    <!-- 图标选择 -->
                    <div v-if="navigationConfig.displayType !== 'text'" class="icon-row">
                      <div class="icon-group">
                        <label class="icon-label">默认图标</label>
                        <div class="icon-selector" @click="selectIcon(index, 'normal')">
                          <img v-if="nav.icon" :src="nav.icon" alt="默认图标" class="icon-preview" />
                          <div v-else class="icon-placeholder">
                            <el-icon><House /></el-icon>
                          </div>
                        </div>
                      </div>
                      <div class="icon-group">
                        <label class="icon-label">选中图标</label>
                        <div class="icon-selector" @click="selectIcon(index, 'active')">
                          <img v-if="nav.activeIcon" :src="nav.activeIcon" alt="选中图标" class="icon-preview" />
                          <div v-else class="icon-placeholder">
                            <el-icon><House /></el-icon>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <!-- 文字输入 -->
                    <div v-if="navigationConfig.displayType !== 'icon'" class="text-input">
                      <el-input 
                        v-model="nav.text" 
                        placeholder="请输入导航文字"
                        size="small"
                        maxlength="4"
                        show-word-limit
                      />
                    </div>
                    
                    <!-- 跳转链接 -->
                    <div class="link-selector" @click="selectLink(index)">
                      <span>{{ nav.link || '请选择跳转链接' }}</span>
                      <el-icon><ArrowRight /></el-icon>
                    </div>
                  </div>
                </div>

                <!-- 添加导航按钮 -->
                <div 
                  v-if="navigationConfig.navList.length < 5" 
                  class="add-nav-button"
                  @click="addNav"
                >
                  <el-icon><Plus /></el-icon>
                  <span>添加导航 {{ navigationConfig.navList.length }}/5</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 样式Tab -->
        <div v-show="activeTab === 'style'" class="tab-content">
          <!-- 样式设置 -->
          <div class="config-panel">
            <div class="panel-header">
              <h3>样式设置</h3>
            </div>
            
            <div class="panel-content">
              <!-- 导航高度 -->
              <div class="form-group">
                <label class="form-label">导航高度</label>
                <div class="slider-control">
                  <el-slider 
                    v-model="styleConfig.navHeight" 
                    :min="50" 
                    :max="75"
                    :show-tooltip="false"
                  />
                  <el-input-number 
                    v-model="styleConfig.navHeight" 
                    :min="50" 
                    :max="75"
                    size="small"
                    controls-position="right"
                    class="slider-input"
                  />
                </div>
              </div>

              <!-- 字体大小 -->
              <div class="form-group">
                <label class="form-label">字体大小</label>
                <div class="slider-control">
                  <el-slider 
                    v-model="styleConfig.fontSize" 
                    :min="12" 
                    :max="18"
                    :show-tooltip="false"
                  />
                  <el-input-number 
                    v-model="styleConfig.fontSize" 
                    :min="12" 
                    :max="18"
                    size="small"
                    controls-position="right"
                    class="slider-input"
                  />
                </div>
              </div>

              <!-- 字体粗细 -->
              <div class="form-group">
                <label class="form-label">字体粗细</label>
                <div class="slider-control">
                  <el-slider 
                    v-model="styleConfig.fontWeight" 
                    :min="100" 
                    :max="900"
                    :step="100"
                    :show-tooltip="false"
                  />
                  <el-input-number 
                    v-model="styleConfig.fontWeight" 
                    :min="100" 
                    :max="900"
                    :step="100"
                    size="small"
                    controls-position="right"
                    class="slider-input"
                  />
                </div>
              </div>

              <!-- 文字上边距 -->
              <div class="form-group">
                <label class="form-label">文字上边距</label>
                <div class="slider-control">
                  <el-slider 
                    v-model="styleConfig.textMarginTop" 
                    :min="0" 
                    :max="10"
                    :show-tooltip="false"
                  />
                  <el-input-number 
                    v-model="styleConfig.textMarginTop" 
                    :min="0" 
                    :max="10"
                    size="small"
                    controls-position="right"
                    class="slider-input"
                  />
                </div>
              </div>

              <!-- 图片大小 -->
              <div class="form-group">
                <label class="form-label">图片大小</label>
                <div class="slider-control">
                  <el-slider 
                    v-model="styleConfig.iconSize" 
                    :min="22" 
                    :max="34"
                    :show-tooltip="false"
                  />
                  <el-input-number 
                    v-model="styleConfig.iconSize" 
                    :min="22" 
                    :max="34"
                    size="small"
                    controls-position="right"
                    class="slider-input"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 颜色设置 -->
          <div class="config-panel">
            <div class="panel-header">
              <h3>颜色设置</h3>
            </div>
            
            <div class="panel-content">
                             <!-- 背景颜色 -->
               <div class="form-group">
                 <label class="form-label">背景颜色</label>
                 <div class="color-control">
                   <el-color-picker 
                     v-model="styleConfig.backgroundColor"
                     size="small"
                     show-alpha
                     :predefine="predefineColors"
                   />
                   <el-input 
                     v-model="styleConfig.backgroundColor" 
                     placeholder="#fcfcfc"
                     size="small"
                     class="color-input"
                   />
                   <el-button type="primary" text size="small" @click="resetBackgroundColor">重置</el-button>
                 </div>
               </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 保存按钮 - 固定在底部 -->
    <div class="save-section">
      <el-button type="primary" size="large" @click="handleSave" :loading="loading" block>
        保存
      </el-button>
    </div>

    <!-- 图片选择器 -->
    <ImageSelector
      v-model:visible="imageSelectorVisible"
      title="选择导航图标"
      :multiple="false"
      @select="handleImageSelect"
      @cancel="imageSelectorVisible = false"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus, ArrowRight, Delete } from '@element-plus/icons-vue'
import { getNavigationConfig, saveNavigationConfig } from '@/api/decoration'
import ImageSelector from '@/components/ImageSelector/index.vue'
import PhonePreview from '@/components/PhonePreview/index.vue'
import BottomNavigation from '@/components/BottomNavigation/index.vue'

const route = useRoute()

// 响应式数据
const loading = ref(false)
const imageSelectorVisible = ref(false)
const currentIconIndex = ref(0)
const currentIconType = ref('normal') // normal | active
const activeNavIndex = ref(0)
const activeTab = ref('content') // content | style

// 预定义颜色
const predefineColors = ref([
  '#ffffff',
  '#fcfcfc',
  '#f8f9fa',
  '#f1f3f4',
  '#e8eaed',
  '#dadce0',
  '#bdc1c6',
  '#9aa0a6',
  '#5f6368',
  '#3c4043',
  '#202124',
  '#000000',
  '#ff6b6b',
  '#4ecdc4',
  '#45b7d1',
  '#96ceb4',
  '#feca57',
  '#ff9ff3',
  '#54a0ff',
  '#5f27cd'
])

const navigationConfig = reactive({
  enabled: true, // 是否显示底部导航
  displayType: 'icon-text', // 显示类型：icon-text, icon, text
  navList: [
    {
      id: 1,
      text: '首页',
      icon: '',
      activeIcon: '',
      link: '',
      linkType: 'page'
    },
    {
      id: 2,
      text: '分类',
      icon: '',
      activeIcon: '',
      link: '',
      linkType: 'page'
    },
    {
      id: 3,
      text: '购物车',
      icon: '',
      activeIcon: '',
      link: '',
      linkType: 'page'
    },
    {
      id: 4,
      text: '个人中心',
      icon: '',
      activeIcon: '',
      link: '',
      linkType: 'page'
    }
  ]
})

const styleConfig = reactive({
  navHeight: 60, // 导航高度 50-75
  fontSize: 12, // 字体大小 12-18
  fontWeight: 400, // 字体粗细 100-900
  textMarginTop: 2, // 文字上边距 0-10
  iconSize: 24, // 图片大小 22-34
  backgroundColor: '#fcfcfc' // 背景颜色
})

// 工具方法
const getMallId = () => {
  return route.query.mallId || route.params.mallId
}

const checkMallId = () => {
  const mallId = getMallId()
  if (!mallId) {
    ElMessage.error('缺少商城ID参数')
    return false
  }
  return mallId
}

// 业务方法
const fetchNavigationConfig = async () => {
  try {
    loading.value = true
    const mallId = getMallId()
    if (!mallId) {
      console.warn('缺少商城ID参数')
      return
    }
    
    const response = await getNavigationConfig(Number(mallId))
    if (response.data) {
      // 加载样式配置
      if (response.data.config) {
        navigationConfig.enabled = response.data.config.enabled
        navigationConfig.displayType = response.data.config.displayType
        styleConfig.navHeight = response.data.config.navHeight
        styleConfig.fontSize = response.data.config.fontSize
        styleConfig.fontWeight = response.data.config.fontWeight
        styleConfig.textMarginTop = response.data.config.textMarginTop
        styleConfig.iconSize = response.data.config.iconSize
        styleConfig.backgroundColor = response.data.config.backgroundColor
      }
      
      // 加载导航列表
      if (response.data.navList && response.data.navList.length > 0) {
        navigationConfig.navList = response.data.navList.map(nav => ({
          id: nav.id || Date.now(),
          text: nav.navText || '导航',
          icon: nav.iconUrl || '',
          activeIcon: nav.activeIconUrl || '',
          link: nav.linkUrl || '',
          linkType: nav.linkType || 'page'
        }))
      }
      
      console.log('已加载底部导航配置')
    }
  } catch (error) {
    console.error('获取底部导航配置失败:', error)
    ElMessage.error('获取导航配置失败')
  } finally {
    loading.value = false
  }
}

const addNav = () => {
  if (navigationConfig.navList.length >= 5) {
    ElMessage.error('最多只能添加5个导航')
    return
  }
  
  navigationConfig.navList.push({
    id: Date.now(),
    text: '导航',
    icon: '',
    activeIcon: '',
    link: '',
    linkType: 'page'
  })
}

const removeNav = (index) => {
  if (navigationConfig.navList.length <= 2) {
    ElMessage.error('至少保留2个导航')
    return
  }
  
  navigationConfig.navList.splice(index, 1)
  
  // 调整活跃导航索引
  if (activeNavIndex.value >= navigationConfig.navList.length) {
    activeNavIndex.value = navigationConfig.navList.length - 1
  }
}

const selectIcon = (index, type) => {
  currentIconIndex.value = index
  currentIconType.value = type
  imageSelectorVisible.value = true
}

const handleImageSelect = (image) => {
  if (image && image.url) {
    const nav = navigationConfig.navList[currentIconIndex.value]
    if (currentIconType.value === 'normal') {
      nav.icon = image.url
    } else {
      nav.activeIcon = image.url
    }
  }
  imageSelectorVisible.value = false
}

const selectLink = (index) => {
  // 这里可以打开链接选择器
  ElMessage.info('链接选择功能待开发')
}

const switchNav = (index) => {
  activeNavIndex.value = index
}

const resetBackgroundColor = () => {
  styleConfig.backgroundColor = '#fcfcfc'
}

// 将rgba格式转换为十六进制格式
const rgbaToHex = (rgba) => {
  if (!rgba || typeof rgba !== 'string') return rgba
  
  // 匹配rgba格式
  const rgbaMatch = rgba.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([\d.]+))?\)/)
  if (!rgbaMatch) return rgba
  
  const r = parseInt(rgbaMatch[1])
  const g = parseInt(rgbaMatch[2])
  const b = parseInt(rgbaMatch[3])
  const a = rgbaMatch[4] ? parseFloat(rgbaMatch[4]) : 1
  
  // 如果透明度不是1，保持rgba格式
  if (a !== 1) return rgba
  
  // 转换为十六进制
  const toHex = (n) => {
    const hex = n.toString(16)
    return hex.length === 1 ? '0' + hex : hex
  }
  
  return `#${toHex(r)}${toHex(g)}${toHex(b)}`
}

const handleSave = async () => {
  const mallId = checkMallId()
  if (!mallId) return
  
  // 验证配置
  if (navigationConfig.enabled) {
    // 检查导航数量
    if (!navigationConfig.navList || navigationConfig.navList.length < 2) {
      ElMessage.error('至少需要2个导航项')
      return
    }
    
    if (navigationConfig.navList.length > 5) {
      ElMessage.error('最多只能有5个导航项')
      return
    }
    
    // 检查文字导航的文本
    if (navigationConfig.displayType !== 'icon') {
      const hasEmptyText = navigationConfig.navList.some(nav => !nav.text || nav.text.trim() === '')
      if (hasEmptyText) {
        ElMessage.error('请为所有导航填写文字')
        return
      }
      
      // 检查文字长度
      const hasLongText = navigationConfig.navList.some(nav => nav.text && nav.text.length > 4)
      if (hasLongText) {
        ElMessage.error('导航文字不能超过4个字符')
        return
      }
    }
    
    console.log('底部导航配置验证通过')
  }
  
  try {
    loading.value = true
    
    // 构造保存数据
    const saveData = {
      enabled: navigationConfig.enabled,
      displayType: navigationConfig.displayType,
      navHeight: styleConfig.navHeight,
      fontSize: styleConfig.fontSize,
      fontWeight: styleConfig.fontWeight,
      textMarginTop: styleConfig.textMarginTop,
      iconSize: styleConfig.iconSize,
      backgroundColor: styleConfig.backgroundColor,
      navList: navigationConfig.navList.map(nav => ({
        navText: nav.text,
        iconUrl: nav.icon,
        activeIconUrl: nav.activeIcon,
        linkUrl: nav.link,
        linkType: nav.linkType || 'page'
      }))
    }
    
    const response = await saveNavigationConfig(Number(mallId), saveData)
    if (response) {
      ElMessage.success('底部导航配置保存成功！')
    } else {
      ElMessage.error('保存失败')
    }
  } catch (error) {
    console.error('保存底部导航配置失败:', error)
    ElMessage.error('保存失败：' + (error.message || '网络错误'))
  } finally {
    loading.value = false
  }
}

// 监听背景颜色变化，自动转换格式
watch(() => styleConfig.backgroundColor, (newColor) => {
  const hexColor = rgbaToHex(newColor)
  if (hexColor !== newColor) {
    styleConfig.backgroundColor = hexColor
  }
})

// 组件挂载时获取配置
onMounted(() => {
  fetchNavigationConfig()
})
</script>

<style scoped>
.navigation-container {
  background: #f8f9fa;
  overflow: hidden;
}

.navigation-content {
  display: flex;
  gap: 0;
  height: calc(100vh - 54px);
  margin: 0;
  max-width: none;
  overflow: hidden;
}

/* 左侧预览区域 */
.preview-section {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 20px;
  background: #f8f9fa;
  overflow: hidden;
}





/* 右侧配置区域 */
.config-section {
  width: 380px;
  background: #fff;
  border-left: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 54px);
  overflow: hidden;
}

/* Tab 切换 */
.config-tabs {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.tab-item {
  flex: 1;
  padding: 16px 20px;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 2px solid transparent;
}

.tab-item:hover {
  color: #3b82f6;
  background: #f0f9ff;
}

.tab-item.active {
  color: #3b82f6;
  background: #fff;
  border-bottom-color: #3b82f6;
}

/* Tab 内容 */
.tab-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding-bottom: 80px; /* 为固定的保存按钮留出空间 */
}

.config-panel {
  border-bottom: 1px solid #e5e7eb;
}

.panel-header {
  padding: 16px 20px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.panel-content {
  padding: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.nav-toggle {
  display: flex;
  align-items: center;
}

/* 样式选择器 */
.style-selector {
  display: flex;
  gap: 8px;
}

.style-option {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  text-align: center;
  font-size: 14px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fff;
}

.style-option:hover {
  border-color: #3b82f6;
  color: #3b82f6;
}

.style-option.active {
  border-color: #3b82f6;
  background: #3b82f6;
  color: #fff;
}



/* 导航配置 */
.nav-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.nav-config-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  background: #fff;
}

.nav-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.nav-item-title {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.nav-item-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.icon-row {
  display: flex;
  gap: 12px;
}

.icon-group {
  flex: 1;
}

.icon-label {
  display: block;
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 6px;
}

.icon-selector {
  width: 40px;
  height: 40px;
  border: 1px dashed #d1d5db;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.icon-selector:hover {
  border-color: #3b82f6;
}

.icon-preview {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.icon-selector .icon-placeholder {
  color: #9ca3af;
  font-size: 20px;
}

.text-input {
  width: 100%;
}

.link-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
  color: #6b7280;
}

.link-selector:hover {
  border-color: #3b82f6;
  background: #f0f9ff;
}

.link-selector span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 添加导航按钮 */
.add-nav-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  border: 1px dashed #d1d5db;
  border-radius: 8px;
  background: #f9fafb;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.add-nav-button:hover {
  border-color: #3b82f6;
  color: #3b82f6;
  background: #f0f9ff;
}

.add-nav-button .el-icon {
  font-size: 16px;
}

/* 样式控件 */
.slider-control {
  display: flex;
  align-items: center;
  gap: 12px;
}

.slider-control .el-slider {
  flex: 1;
}

.slider-input {
  width: 80px;
  flex-shrink: 0;
}

.color-control {
  display: flex;
  align-items: center;
  gap: 12px;
}

.color-input {
  flex: 1;
}

/* 保存区域 - 固定在底部 */
.save-section {
  position: fixed;
  bottom: 0;
  right: 0;
  width: 380px;
  padding: 20px;
  border-top: 1px solid #e5e7eb;
  background: #fff;
  z-index: 100;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .navigation-content {
    flex-direction: column;
    height: auto;
  }
  
  .preview-section {
    flex: none;
    height: auto;
    padding: 20px;
  }
  
  .config-section {
    width: 100%;
    height: auto;
    border-left: none;
    border-top: 1px solid #e5e7eb;
  }
  
  .save-section {
    width: 100%;
    right: 0;
  }
}

@media (max-width: 768px) {
  .navigation-container {
    padding: 0;
  }
  
  .preview-section {
    padding: 15px;
  }
  
  .panel-content {
    padding: 15px;
  }
  
  .save-section {
    padding: 15px;
  }
  
  .icon-row {
    flex-direction: column;
    gap: 8px;
  }
}
</style> 