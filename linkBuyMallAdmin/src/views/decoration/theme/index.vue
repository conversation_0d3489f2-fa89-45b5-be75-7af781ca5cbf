<template>
    <div class="theme-container" v-loading="loading" element-loading-text="正在加载商城主题...">
        <!-- 主题颜色选择 -->
        <div class="theme-colors-container">
            <div class="theme-colors">
                <div v-for="theme in themes" :key="theme.key" class="theme-color-item"
                    :class="{ active: selectedTheme === theme.key }" @click="selectTheme(theme.key)">
                    <div class="color-circle" :style="{ backgroundColor: theme.primary || '#E53E3E' }" :title="theme.primary"></div>
                    <span class="color-name">{{ theme.name || '未知主题' }}</span>
                </div>
            </div>

            <!-- 主题预览区域 -->
            <div class="theme-preview">
                <div class="preview-content">
                    <!-- 原有图片预览 -->
                    <div class="preview-images-container">
                        <div v-for="tab in previewTabs" :key="tab.key" class="preview-item">
                            <div class="preview-title">{{ tab.name }}</div>
                            <div class="preview-image-wrapper">
                                <img :src="getPreviewImage(tab.key)" :alt="`${getCurrentThemeName()}-${tab.name}`"
                                    class="preview-image" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="theme-actions">
                <el-button type="primary" @click="handleSave" :loading="loading">保存</el-button>
            </div>
        </div>
    </div>
</template>

<script setup>
// 静态导入所有主题图片
import redGoods from '@/assets/images/decoration/themes/red_theme_goods.png'
import redOrder from '@/assets/images/decoration/themes/red_theme_order.png'
import redUser from '@/assets/images/decoration/themes/red_theme_user.png'
import orangeGoods from '@/assets/images/decoration/themes/orange_theme_goods.png'
import orangeOrder from '@/assets/images/decoration/themes/orange_theme_order.png'
import orangeUser from '@/assets/images/decoration/themes/orange_theme_user.png'
import pinkGoods from '@/assets/images/decoration/themes/pink_theme_goods.png'
import pinkOrder from '@/assets/images/decoration/themes/pink_theme_order.png'
import pinkUser from '@/assets/images/decoration/themes/pink_theme_user.png'
import goldGoods from '@/assets/images/decoration/themes/gold_theme_goods.png'
import goldOrder from '@/assets/images/decoration/themes/gold_theme_order.png'
import goldUser from '@/assets/images/decoration/themes/gold_theme_user.png'
import blueGoods from '@/assets/images/decoration/themes/blue_theme_goods.png'
import blueOrder from '@/assets/images/decoration/themes/blue_theme_order.png'
import blueUser from '@/assets/images/decoration/themes/blue_theme_user.png'
import greenGoods from '@/assets/images/decoration/themes/green_theme_goods.png'
import greenOrder from '@/assets/images/decoration/themes/green_theme_order.png'
import greenUser from '@/assets/images/decoration/themes/green_theme_user.png'
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useThemeStore } from '@/store/theme'
import { mallDetail } from '@/api/mall'
import { saveTheme } from '@/api/decoration'
// 注释掉暂时不使用的组件
// import PhonePreview from '@/components/PhonePreview/index.vue'
// import ThemePreview from '@/components/ThemePreview/index.vue'

const route = useRoute()
const themeStore = useThemeStore()

// 响应式数据
const loading = ref(false)

// 从store获取所有主题
const themes = computed(() => {
    const allThemes = themeStore.getAllThemes()
    
    // 如果store返回空数组，使用fallback数据
    if (!allThemes || allThemes.length === 0) {
        return [
            { key: 'red', name: '经典红', primary: '#E53E3E' },
            { key: 'orange', name: '活力橙', primary: '#FF8C00' },
            { key: 'pink', name: '美妆色', primary: '#E91E63' },
            { key: 'gold', name: '高级金', primary: '#e0a356' },
            { key: 'blue', name: '科技蓝', primary: '#3182CE' },
            { key: 'green', name: '生鲜绿', primary: '#2ec840' }
        ]
    }
    
    return allThemes
})

// 当前选中的主题
const selectedTheme = ref('red')

const previewTabs = ref([
    { key: 'goods', name: '商品' },
    { key: 'order', name: '订单' },
    { key: 'user', name: '用户' }
])

// 图片映射表
const themeImages = {
    red: { goods: redGoods, order: redOrder, user: redUser },
    orange: { goods: orangeGoods, order: orangeOrder, user: orangeUser },
    pink: { goods: pinkGoods, order: pinkOrder, user: pinkUser },
    gold: { goods: goldGoods, order: goldOrder, user: goldUser },
    blue: { goods: blueGoods, order: blueOrder, user: blueUser },
    green: { goods: greenGoods, order: greenOrder, user: greenUser }
}

// 工具方法
const getMallId = () => {
    return route.query.mallId || route.params.mallId
}

const checkMallId = () => {
    const mallId = getMallId()
    if (!mallId) {
        ElMessage.error('缺少商城ID参数')
        return false
    }
    return mallId
}

// 业务方法
const selectTheme = (themeKey) => {
    selectedTheme.value = themeKey
    
    // 立即更新store中的主题
    const mallId = getMallId()
    if (mallId) {
        themeStore.setTheme(mallId, themeKey)
    }
}

const getPreviewImage = (tabKey) => {
    // 从图片映射表中获取对应的图片
    const themeImagesForTheme = themeImages[selectedTheme.value]
    if (themeImagesForTheme && themeImagesForTheme[tabKey]) {
        return themeImagesForTheme[tabKey]
    }
    return ''
}

const getCurrentThemeName = () => {
    const theme = themes.value.find(t => t.key === selectedTheme.value)
    return theme ? theme.name : ''
}

const handleSave = async () => {
    const mallId = checkMallId()
    if (!mallId) return
    
    try {
        loading.value = true
        
        // 调用后端API保存主题设置
        await saveTheme(Number(mallId), selectedTheme.value)
        
        // 保存成功后更新store缓存
        themeStore.setTheme(mallId, selectedTheme.value)
        
        console.log('保存主题成功:', {
            mallId: Number(mallId),
            theme: selectedTheme.value
        })
        
        ElMessage.success('主题保存成功！')
        
    } catch (error) {
        console.error('保存主题失败:', error)
        ElMessage.error('保存失败：' + (error.message || '网络错误'))
    } finally {
        loading.value = false
    }
}

const fetchMallDetail = async () => {
    try {
        loading.value = true
        const mallId = getMallId()
        if (!mallId) {
            console.warn('缺少商城ID参数')
            return
        }
        
        // 使用store的异步方法从服务端获取最新主题
        const serverTheme = await themeStore.getThemeAsync(mallId)
        selectedTheme.value = serverTheme
        
        console.log('已加载商城主题:', serverTheme)
        
    } catch (error) {
        console.error('获取商城主题失败:', error)
        ElMessage.error('获取商城主题失败')
        // 使用默认主题
        selectedTheme.value = 'red'
    } finally {
        loading.value = false
    }
}

// 组件挂载时初始化
onMounted(() => {
    // 初始化主题store
    themeStore.initThemes()
    // 获取商城详情并设置主题
    fetchMallDetail()
})
</script>

<style scoped>
.theme-container {
    padding: 20px;
    background: #f5f5f5;
    min-height: 100%;
}

.theme-colors-container {
    width: 1000px;
}

.theme-header {
    margin-bottom: 30px;
}

.theme-header h2 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

/* 主题颜色选择区域 */
.theme-colors {
    display: flex;
    gap: 20px;
    margin-bottom: 10px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.theme-color-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.theme-color-item:hover {
    background: #f8f9fa;
}

.theme-color-item.active {
    border-color: #409EFF;
    background: #f0f9ff;
}

.color-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.theme-color-item.active .color-circle {
    border-color: #409EFF;
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
    transform: scale(1.1);
}

.color-name {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.theme-color-item.active .color-name {
    color: #409EFF;
    font-weight: 600;
}

/* 预览区域 */
.theme-preview {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 30px;
}

.preview-content {
    padding: 20px;
    display: flex;
    gap: 30px;
    align-items: flex-start;
}

.phone-preview-wrapper {
    flex: 0 0 auto;
}

.preview-images-container {
    flex: 1;
    display: flex;
    gap: 20px;
    justify-content: space-between;
}

.preview-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.preview-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    text-align: center;
}

.preview-image-wrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
}

.preview-image {
    max-width: 100%;
    max-height: 540px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: transform 0.3s ease;
}

.preview-image:hover {
    transform: scale(1.02);
}

/* 操作按钮 */
.theme-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.theme-actions .el-button {
    padding: 12px 30px;
    font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .theme-colors {
        flex-wrap: wrap;
        gap: 15px;
    }

    .theme-color-item {
        flex: 1;
        min-width: calc(33.333% - 10px);
    }

    .preview-content {
        flex-direction: column;
        gap: 20px;
    }

    .phone-preview-wrapper {
        align-self: center;
    }

    .preview-images-container {
        flex-direction: column;
        gap: 30px;
    }

    .preview-item {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .theme-container {
        padding: 15px;
    }

    .theme-colors {
        padding: 15px;
    }

    .theme-color-item {
        min-width: calc(50% - 7.5px);
    }

    .color-circle {
        width: 35px;
        height: 35px;
    }

    .preview-content {
        padding: 15px;
    }

    .theme-actions {
        flex-direction: column;
    }

    .theme-actions .el-button {
        width: 100%;
    }
}
</style>
