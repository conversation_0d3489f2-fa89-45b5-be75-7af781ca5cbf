<template>
    <div class="miniprogram-editor">
        <!-- 左侧组件库 -->
        <WidgetSidebar @dragStart="handleDragStart" @dragEnd="handleDragEnd" />

        <!-- 中间预览区域 -->
        <div class="preview-section">
            <!-- 预览区域头部操作栏 -->
            <div class="preview-header">
                <div class="preview-title">
                    <h3>页面预览</h3>
                </div>
                <div class="preview-actions">
                    <el-button type="primary" :loading="isSaving" @click="savePage">
                        <el-icon v-if="!isSaving">
                            <DocumentAdd />
                        </el-icon>
                        {{ isSaving ? '保存中...' : '保存页面' }}
                    </el-button>
                </div>
            </div>

            <div class="preview-container" @click="handlePreviewClick">
                <PhonePreview :show-placeholder="false" @click.stop="handlePhonePreviewClick">
                    <!-- 页面内容区域 -->
                    <div class="page-content" :style="{ backgroundColor: pageConfig.backgroundColor }"
                        @dragover="handleDragOver" @drop="handleDrop" @dragenter="handleDragEnter"
                        @dragleave="handleDragLeave" @click.stop="handlePageContentClick">
                        <!-- 动态组件列表 -->
                        <div class="widget-list" @click.stop="handleWidgetListClick">
                            <!-- 拖拽提示区域 -->
                            <div v-if="isDragging && pageWidgets.length === 0" class="drop-placeholder">
                                <div class="drop-hint">
                                    <span class="drop-text">
                                        {{ draggedWidget?.type === 'search' ? '搜索组件将添加到页面顶部' : '释放鼠标将组建添加到此处' }}
                                    </span>
                                </div>
                            </div>

                            <!-- 顶部拖拽提示 -->
                            <div v-if="isDragging && dropPosition === 0 && pageWidgets.length > 0 && !(draggedWidget?.type === 'search' && pageWidgets[0]?.type === 'search')"
                                class="drop-hint-container drop-hint-top">
                                <div class="drop-hint-box">
                                    <span class="drop-hint-text">
                                        {{ draggedWidget?.type === 'search' ? '搜索组件将添加到页面顶部' : '释放鼠标将组建添加到此处' }}
                                    </span>
                                </div>
                            </div>

                            <div v-for="(widget, index) in pageWidgets" :key="widget.id" class="widget-container"
                                :class="{ active: selectedWidgetIndex === index, hidden: widget.hidden, dragging: isDragging }"
                                @click.stop="selectWidget(index)">



                                <!-- 动态组件渲染 -->
                                <component :is="getWidgetComponent(widget.type)" :data="widget.data"
                                    v-if="getWidgetComponent(widget.type)" />

                                <!-- 未知组件类型的占位符 -->
                                <div v-else class="unknown-widget">
                                    <div class="unknown-content">
                                        <el-icon class="unknown-icon">
                                            <Plus />
                                        </el-icon>
                                        <span class="unknown-text">未知组件类型: {{ widget.type }}</span>
                                    </div>
                                </div>

                                <!-- 组件间拖拽提示 -->
                                <div v-if="isDragging && dropPosition === index + 1 && index + 1 < pageWidgets.length"
                                    class="drop-hint-container drop-hint-between">
                                    <div class="drop-hint-box">
                                        <span class="drop-hint-text">释放鼠标将组建添加到此处</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 最后一个位置的拖拽提示 -->
                            <div v-if="isDragging && dropPosition === pageWidgets.length && pageWidgets.length > 0"
                                class="drop-hint-container drop-hint-bottom">
                                <div class="drop-hint-box">
                                    <span class="drop-hint-text">释放鼠标将组建添加到此处</span>
                                </div>
                            </div>

                            <!-- 空状态 -->
                            <div v-if="pageWidgets.length === 0 && !isDragging" class="empty-state">
                                <el-icon class="empty-icon">
                                    <Plus />
                                </el-icon>
                                <p>从左侧拖拽组件到此处开始设计</p>
                            </div>
                        </div>
                    </div>

                    <!-- 底部导航栏 -->
                    <template #bottom-nav>
                        <BottomNavigation v-if="pageConfig.showBottomNav" :navigation-config="bottomNavConfig"
                            :style-config="bottomNavStyle" :active-nav-index="activeNavIndex"
                            @nav-click="handleNavClick" />
                    </template>
                </PhonePreview>

                <!-- 组件工具栏 - 固定在顶部 -->
                <div v-if="selectedWidgetIndex >= 0 && !isDragging" class="widget-toolbar-external"
                    :style="{ top: '130px', left: '50%', transform: 'translateX(-50%)' }">
                    <div class="toolbar-buttons">
                        <el-button size="small" circle @click.stop="hideWidget(selectedWidgetIndex)"
                            :title="selectedWidget?.hidden ? '显示' : '隐藏'">
                            <el-icon v-if="selectedWidget?.hidden">
                                <View />
                            </el-icon>
                            <el-icon v-else>
                                <Hide />
                            </el-icon>
                        </el-button>
                        <el-button type="danger" size="small" circle @click.stop="removeWidget(selectedWidgetIndex)"
                            title="删除">
                            <el-icon>
                                <Delete />
                            </el-icon>
                        </el-button>
                        <el-button size="small" circle @click.stop="copyWidget(selectedWidgetIndex)" title="复制">
                            <el-icon>
                                <DocumentCopy />
                            </el-icon>
                        </el-button>
                        <el-button v-if="selectedWidget?.type !== 'search'" size="small" circle
                            @click.stop="moveWidgetUp(selectedWidgetIndex)" title="上移"
                            :disabled="selectedWidgetIndex === 0 || (selectedWidgetIndex === 1 && pageWidgets[0]?.type === 'search')">
                            <el-icon>
                                <ArrowUp />
                            </el-icon>
                        </el-button>
                        <el-button v-if="selectedWidget?.type !== 'search'" size="small" circle
                            @click.stop="moveWidgetDown(selectedWidgetIndex)" title="下移"
                            :disabled="selectedWidgetIndex === pageWidgets.length - 1">
                            <el-icon>
                                <ArrowDown />
                            </el-icon>
                        </el-button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧属性面板 -->
        <div class="property-panel">
            <div class="panel-header">
                <div class="panel-tabs">
                    <div class="tab-item" :class="{ active: activeTab === 'content' }" @click="activeTab = 'content'">
                        内容
                    </div>
                    <div class="tab-item" :class="{ active: activeTab === 'style' }" @click="activeTab = 'style'">
                        样式
                    </div>
                </div>
            </div>

            <div class="panel-content">
                <!-- 页面设置 -->
                <div v-if="selectedWidgetIndex === -1" class="page-settings">
                    <div class="config-section">
                        <h4 class="section-title">页面设置</h4>

                        <div class="form-group">
                            <label class="form-label">页面标题 <span style="color: red;">*</span></label>
                            <el-input v-model="pageConfig.title" placeholder="请输入页面标题" maxlength="50" show-word-limit />
                        </div>

                        <div class="form-group">
                            <label class="form-label">页面描述</label>
                            <el-input v-model="pageConfig.description" type="textarea" placeholder="请输入页面描述" :rows="3"
                                maxlength="200" show-word-limit />
                        </div>

                        <div class="form-group">
                            <label class="form-label">背景颜色</label>
                            <div class="color-control">
                                <el-color-picker v-model="pageConfig.backgroundColor" size="small" />
                                <el-input v-model="pageConfig.backgroundColor" placeholder="#f5f5f5" />
                                <el-button type="primary" text size="small" @click="resetBackgroundColor">重置</el-button>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">显示底部导航栏</label>
                            <div class="radio-group">
                                <el-radio v-model="pageConfig.showBottomNav" :label="true">显示</el-radio>
                                <el-radio v-model="pageConfig.showBottomNav" :label="false">隐藏</el-radio>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 组件属性 -->
                <div v-else-if="selectedWidget" class="widget-properties">
                    <div class="property-header">
                        <h3>{{ getWidgetTypeName(selectedWidget.type) }}</h3>
                    </div>

                    <!-- 使用动态配置组件 -->
                    <WidgetConfigContainer 
                        v-if="getMallId()"
                        :widget-type="selectedWidget.type" 
                        :widget-data="selectedWidget.data"
                        :active-tab="activeTab" 
                        :mall-id="getMallId()" 
                        @update="handleWidgetDataUpdate" />
                </div>

                <!-- 未选中状态 -->
                <div v-else class="no-selection">
                    <el-icon class="no-selection-icon"><Select /></el-icon>
                    <p>请选择一个组件进行编辑</p>
                </div>
            </div>
        </div>
    </div>


</template>

<script setup>
import { ref, computed, nextTick, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { savePageConfig, createMinipage, getMinipageDetail } from '@/api/minipage.ts'
import { ElMessage } from 'element-plus'
import {
    ArrowDown,
    ArrowUp,
    View,
    Hide,
    Delete,
    Plus,
    Select,
    DocumentCopy,
    DocumentAdd
} from '@element-plus/icons-vue'
import PhonePreview from '@/components/PhonePreview/index.vue'
import WidgetSidebar from '@/components/WidgetSidebar/index.vue'
import WidgetConfigContainer from '@/components/WidgetConfigs/WidgetConfigContainer.vue'
import BottomNavigation from '@/components/BottomNavigation/index.vue'
import { getWidgetComponent } from '@/components/widgets/index.js'
import { getNavigationConfig } from '@/api/decoration'
import { useTheme } from '@/composables/useTheme.js'
import { useThemeStore } from '@/store/theme'
import empty_product from '@/assets/images/decoration/empty_product.avif'

// 定义props
const props = defineProps({
    pageId: {
        type: [String, Number],
        default: null
    },
    mallId: {
        type: [String, Number],
        default: null
    },
    mode: {
        type: String,
        default: 'edit', // 'create' | 'edit'
        validator: (value) => ['create', 'edit'].includes(value)
    }
})

const route = useRoute()
const router = useRouter()

// 主题相关
const themeStore = useThemeStore()
const { themeConfig, getWidgetDefaultColors, applyThemeToWidgetData, updateWidgetsTheme, watchThemeChange } = useTheme()

// 响应式数据
const activeTab = ref('content')
const selectedWidgetIndex = ref(-1) // 默认不选中任何组件
const pageWidgets = ref([])

// 拖拽相关状态
const isDragging = ref(false)
const draggedWidget = ref(null)
const dropPosition = ref(-1)

// 工具条位置（已移除动态计算，固定在顶部）

// 保存状态
const isSaving = ref(false)

// 页面配置
const pageConfig = ref({
    title: '',
    description: '',
    backgroundColor: '#f5f5f5',
    showBottomNav: false // 是否显示底部导航栏
})

// 底部导航配置
const bottomNavConfig = ref({
    enabled: true,
    displayType: 'icon-text',
    navList: []
})

// 底部导航样式配置
const bottomNavStyle = ref({
    navHeight: 60,
    fontSize: 12,
    fontWeight: 400,
    textMarginTop: 2,
    iconSize: 24,
    backgroundColor: '#fcfcfc'
})

// 当前激活的导航索引
const activeNavIndex = ref(0)





// 计算属性
const selectedWidget = computed(() => {
    if (selectedWidgetIndex.value >= 0 && selectedWidgetIndex.value < pageWidgets.value.length) {
        return pageWidgets.value[selectedWidgetIndex.value]
    }
    return null
})

// 工具方法
const getMallId = () => {
    // 优先使用props中的mallId，然后是路由参数
    const mallId = props.mallId || route.query.mallId || route.params.mallId
    // 确保返回数字类型
    return mallId ? Number(mallId) : null
}

// 获取pageId - 优先使用props
const getPageId = () => {
    return props.pageId || route.query.pageId
}

// 获取模式 - 优先使用props
const getMode = () => {
    return props.mode || route.query.mode || 'edit'
}

// 重置背景颜色
const resetBackgroundColor = () => {
    pageConfig.value.backgroundColor = '#f5f5f5'
}

// 处理导航点击
const handleNavClick = (index) => {
    activeNavIndex.value = index
}

// 加载底部导航配置
const loadBottomNavConfig = async () => {
    const mallId = getMallId()
    if (!mallId) return

    try {
        const response = await getNavigationConfig(Number(mallId))
        if (response.data) {
            // 加载样式配置
            if (response.data.config) {
                bottomNavConfig.value.enabled = response.data.config.enabled
                bottomNavConfig.value.displayType = response.data.config.displayType
                bottomNavStyle.value.navHeight = response.data.config.navHeight
                bottomNavStyle.value.fontSize = response.data.config.fontSize
                bottomNavStyle.value.fontWeight = response.data.config.fontWeight
                bottomNavStyle.value.textMarginTop = response.data.config.textMarginTop
                bottomNavStyle.value.iconSize = response.data.config.iconSize
                bottomNavStyle.value.backgroundColor = response.data.config.backgroundColor
            }

            // 加载导航列表
            if (response.data.navList && response.data.navList.length > 0) {
                bottomNavConfig.value.navList = response.data.navList.map(nav => ({
                    id: nav.id || Date.now(),
                    text: nav.navText || '导航',
                    icon: nav.iconUrl || '',
                    activeIcon: nav.activeIconUrl || '',
                    link: nav.linkUrl || '',
                    linkType: nav.linkType || 'page'
                }))
            }
        }
    } catch (error) {
        console.error('获取底部导航配置失败:', error)
    }
}

const generateWidgetId = () => {
    return 'widget_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

const getWidgetTypeName = (type) => {
    const widgetTypeMap = {
        'search': '搜索框',
        'title': '标题',
        'banner': '轮播图',
        'menu-nav': '菜单导航',
        'image-magic': '图片魔方',
        'notice': '公告',
        'video': '视频',
        'rich-text': '图文',
        'product-group': '商品组',
        'product-recommend': '商品推荐',
        'product-card': '选项卡',
        'coupon': '优惠券',
        'seckill': '限时秒杀',
        'group-buy': '拼团活动',
        'export-frame': '弹出框',
        'blank-space': '空白间距',
        'divider': '分割线'
    }
    return widgetTypeMap[type] || '未知组件'
}

// 拖拽处理方法
const handleDragStart = (widget, event) => {
    isDragging.value = true
    draggedWidget.value = widget
    event.dataTransfer.effectAllowed = 'copy'
    event.dataTransfer.setData('text/plain', JSON.stringify(widget))
}

const handleDragEnd = () => {
    isDragging.value = false
    draggedWidget.value = null
    dropPosition.value = -1
}

const handleDragEnter = (event) => {
    event.preventDefault()
    // 当拖拽进入预览区域时，显示拖拽状态
    if (draggedWidget.value) {
        isDragging.value = true
    }
}

const handleDragLeave = (event) => {
    // 当离开预览区域时，隐藏拖拽状态
    if (!event.currentTarget.contains(event.relatedTarget)) {
        isDragging.value = false
        dropPosition.value = -1
    }
}

const handleDragOver = (event) => {
    event.preventDefault()
    event.dataTransfer.dropEffect = 'copy'

    // 如果拖拽的是搜索组件，不需要计算位置，直接放在顶部
    if (draggedWidget.value && draggedWidget.value.type === 'search') {
        dropPosition.value = 0
        return
    }

    // 计算拖拽位置
    const rect = event.currentTarget.getBoundingClientRect()
    const y = event.clientY - rect.top

    // 如果没有组件，直接显示在中间
    if (pageWidgets.value.length === 0) {
        dropPosition.value = 0
        return
    }

    // 计算应该插入的位置
    const widgets = event.currentTarget.querySelectorAll('.widget-container')
    let insertIndex = pageWidgets.value.length

    for (let i = 0; i < widgets.length; i++) {
        const widgetRect = widgets[i].getBoundingClientRect()
        const widgetY = widgetRect.top - rect.top
        const widgetHeight = widgetRect.height

        // 如果在组件的上半部分，插入到组件前面
        if (y < widgetY + widgetHeight / 2) {
            insertIndex = i
            break
        }
    }

    // 如果第一个位置是搜索组件，其他组件不能插入到第一个位置
    if (pageWidgets.value.length > 0 && pageWidgets.value[0].type === 'search' && insertIndex === 0) {
        insertIndex = 1
    }

    dropPosition.value = insertIndex
}

const handleDrop = (event) => {
    event.preventDefault()

    if (!draggedWidget.value) return

    const newWidget = {
        id: generateWidgetId(),
        type: draggedWidget.value.type,
        data: getDefaultWidgetData(draggedWidget.value.type)
    }

    // 搜索组件的特殊处理
    if (draggedWidget.value.type === 'search') {
        // 检查是否已经有搜索组件
        const hasSearchWidget = pageWidgets.value.some(widget => widget.type === 'search')
        if (hasSearchWidget) {
            ElMessage.warning('页面只能有一个搜索组件')
            handleDragEnd()
            return
        }

        // 搜索组件只能放在第一个位置
        pageWidgets.value.unshift(newWidget)
        selectedWidgetIndex.value = 0
        ElMessage.success('搜索组件已添加到页面顶部')
    } else {
        // 其他组件的正常处理
        let insertPosition = dropPosition.value

        // 如果第一个位置是搜索组件，其他组件不能插入到第一个位置
        if (pageWidgets.value.length > 0 && pageWidgets.value[0].type === 'search' && insertPosition === 0) {
            insertPosition = 1
        }

        // 在指定位置插入组件
        if (insertPosition >= 0 && insertPosition <= pageWidgets.value.length) {
            pageWidgets.value.splice(insertPosition, 0, newWidget)
            selectedWidgetIndex.value = insertPosition
        } else {
            pageWidgets.value.push(newWidget)
            selectedWidgetIndex.value = pageWidgets.value.length - 1
        }

        ElMessage.success(`已添加${draggedWidget.value.name}组件`)
    }

    // 重置拖拽状态
    handleDragEnd()
}

const getDefaultWidgetData = (type) => {
    // 获取主题默认颜色
    const themeColors = getWidgetDefaultColors(type)
    
    switch (type) {
        case 'search':
            return {
                title: "搜索框",
                name: "search",
                show: 1,
                content: {
                    text: "请输入关键字搜索",
                    show_address: 1,
                    data: [
                        {
                            url: "",
                            link: []
                        }
                    ]
                },
                styles: {
                    text_align: "left",
                    position: "top",
                    address_text_color: themeColors.address_text_color || "#303133",
                    address_icon_color: themeColors.address_icon_color || "#999999",
                    border_radius: 0,
                    root_bg_color: themeColors.root_bg_color || "",
                    root_bg_url: "",
                    bg_color: themeColors.bg_color || "#E5E5E5",
                    icon_color: themeColors.icon_color || "#999999",
                    color: themeColors.color || "#999999",
                    padding_top: 6,
                    padding_horizontal: 15,
                    padding_bottom: 6,
                    bg_image: ""
                }
            }
        case 'title':
            return {
                title: "标题",
                name: "title",
                show: 1,
                content: {
                    style: 1,
                    title: "主标题",
                    subtitle: "副标题",
                    show_subtitle: 1,
                    show_more: 0,
                    more_title: "更多",
                    link: []
                },
                styles: {
                    bg_color: themeColors.bg_color || "#F5F5F5",
                    root_bg_color: themeColors.root_bg_color || "",
                    title_color: themeColors.title_color || "#333333",
                    title_font_size: 14,
                    subtitle_color: themeColors.subtitle_color || "#999999",
                    subtitle_font_size: 12,
                    more_color: themeColors.more_color || "#999999",
                    padding_top: 0,
                    padding_horizontal: 0,
                    padding_bottom: 0,
                    border_radius_top: 0,
                    border_radius_bottom: 0
                }
            }
        case 'banner':
            return {
                title: "轮播图",
                name: "banner",
                show: 1,
                content: {
                    data: [
                        {
                            url: "",
                            show_item_bg_color: 0,
                            item_bg_color: "#fff",
                            link: {
                                path: "",
                                name: "",
                                params: {},
                                type: ""
                            },
                            height: 200
                        }
                    ]
                },
                styles: {
                    root_bg_color: themeColors.root_bg_color || "#f5f5f5",
                    root_bg_url: "",
                    banner_root_bg_color: themeColors.banner_root_bg_color || "",
                    border_radius: 0,
                    indicator_style: 1,
                    indicator_align: "left",
                    indicator_color: themeColors.indicator_color || "#FF2C3C",
                    padding_top: 0,
                    padding_horizontal: 0,
                    padding_bottom: 0,
                    imgRatio: 1
                },
                autoplay: true,
                interval: 3,
                showIndicators: true,
                arrow: 'hover'
            }
        case 'menu-nav':
            return {
                title: "菜单导航",
                name: "navigation",
                show: 1,
                content: {
                    style: 1,
                    data: [
                        {
                            url: "",
                            name: "导航",
                            link: {}
                        },
                        {
                            url: "",
                            name: "导航",
                            link: {}
                        },
                        {
                            url: "",
                            name: "导航",
                            link: {}
                        },
                        {
                            url: "",
                            name: "导航",
                            link: {}
                        }
                    ]
                },
                styles: {
                    nav_style: 1,
                    nav_line: 1,
                    nav_line_num: 5,
                    indicator_style: 1,
                    indicator_color: "#FF2C3C",
                    bg_color: "#E5E5E5",
                    root_bg_color: "",
                    color: "#333333",
                    padding_top: 0,
                    padding_horizontal: 0,
                    row_gap: 16,
                    padding_bottom: 0,
                    border_radius_top: 0,
                    border_radius_bottom: 0,
                    img_border_radius: 0,
                    inner_padding_top: 0,
                    inner_padding_bottom: 0,
                    inner_padding_horizontal: 0,
                    img_width: 40,
                    img_height: 40
                }
            }
        case 'image-magic':
            return {
                title: "图片魔方",
                name: "rubik",
                show: 1,
                content: {
                    style: 1,
                    data: [
                        {
                            url: "",
                            link: {
                                path: "",
                                name: "",
                                params: {},
                                type: ""
                            },
                            width: 375,
                            height: 133.4375,
                            ratio: 512400
                        }
                    ]
                },
                styles: {
                    border_radius: 0,
                    root_bg_color: "",
                    root_bg_url: "",
                    cube_border_radius: 0,
                    font_color: "#333",
                    padding_top: 0,
                    padding_horizontal: 0,
                    padding_bottom: 0,
                    margin_top: 0,
                    margin_bottom: 0,
                    margin_horizontal: 0
                }
            }
        case 'notice':
            return {
                content: '这里是公告内容',
                icon: 'bell',
                scrollable: true
            }
        case 'video':
            return {
                src: '',
                poster: '',
                autoPlay: false,
                controls: true
            }
        case 'rich-text':
            return {
                content: '<p>这里是图文内容</p>',
                images: []
            }
        case 'graphic':
            return {
                title: "图文",
                name: "graphic",
                show: 1,
                content: {
                    data: [
                        {
                            url: "",
                            title: "标题名称",
                            title_color: "#333333",
                            subtitle: "副标题名称",
                            subtitle_color: "#666666",
                            link: [],
                            bg_color: "#E5E5E5"
                        },
                        {
                            url: "",
                            title: "标题名称",
                            title_color: "#333333",
                            subtitle: "副标题名称",
                            subtitle_color: "#666666",
                            link: [],
                            bg_color: "#E5E5E5"
                        },
                        {
                            url: "",
                            title: "标题名称",
                            title_color: "#333333",
                            subtitle: "副标题名称",
                            subtitle_color: "#666666",
                            link: [],
                            bg_color: "#FFFFFF"
                        }
                    ]
                },
                styles: {
                    root_bg_color: "",
                    border_radius_top: 16,
                    border_radius_bottom: 19,
                    padding_top: 9,
                    padding_horizontal: 0,
                    padding_bottom: 12
                }
            }
        case 'product-group':
            return {
                title: "商品组",
                name: "goodsgroup",
                show: 1,
                content: {
                    style: 5,
                    goods_type: 1,
                    show_title: 1,
                    show_price: 1,
                    show_scribing_price: 1,
                    show_btn: 1,
                    show_tag: 0,
                    tag_type: 1,
                    custom_tag: "",
                    indicator_bg_color: 0,
                    btn_text: "购买",
                    category: {
                        categoryId: "",
                        source: "",
                        supplierId: []
                    },
                    data: []
                },
                styles: {
                    title_color: themeColors.title_color || "#333333",
                    scribing_price_color: themeColors.scribing_price_color || "#999999",
                    price_color: themeColors.price_color || "#FF2C3C",
                    btn_bg_color: themeColors.btn_bg_color || "#FF2C3C",
                    content_bg_color: themeColors.content_bg_color || "",
                    btn_color: themeColors.btn_color || "#FFFFFF",
                    btn_border_radius: 30,
                    btn_border_color: "",
                    root_bg_color: themeColors.root_bg_color || "",
                    bg_color: themeColors.bg_color || "#FFFFFF",
                    margin: 10,
                    padding: 10,
                    padding_top: 0,
                    padding_horizontal: 0,
                    padding_bottom: 0,
                    border_radius_top: 0,
                    border_radius_bottom: 0,
                    goods_border_radius: 0
                }
            }
        case 'product-recommend':
            return {
                title: "商品推荐",
                name: "product-recommend",
                show: 1,
                content: {
                    title: '商品推荐',
                    subtitle: '为您精选优质商品',
                    show_title: 1,
                    show_subtitle: 1,
                    show_more: 0,
                    more_text: '查看全部',
                    show_recommend_tag: 1,
                    recommend_tag_text: '推荐',
                    show_description: 0,
                    show_original_price: 1,
                    show_sales: 1,
                    show_reason: 0,
                    show_action_btn: 1,
                    action_btn_text: '购买',
                    show_load_more: 0,
                    recommend_type: 'hot',
                    max_display: 6,
                    data: [
                        {
                            id: 1,
                            name: '热销商品1',
                            description: '精选优质商品，品质保证',
                            image: empty_product,
                            sell_price: '99.00',
                            lineation_price: '199.00',
                            sales_num: '1234',
                            recommend_reason: '热销推荐',
                            source: 'CUSTOM'
                        },
                        {
                            id: 2,
                            name: '热销商品2',
                            description: '精选优质商品，品质保证',
                            image: empty_product,
                            sell_price: '159.00',
                            lineation_price: '299.00',
                            sales_num: '856',
                            recommend_reason: '新品推荐',
                            source: 'CUSTOM'
                        },
                        {
                            id: 3,
                            name: '热销商品1',
                            description: '精选优质商品，品质保证',
                            image: empty_product,
                            sell_price: '99.00',
                            lineation_price: '199.00',
                            sales_num: '1234',
                            recommend_reason: '热销推荐',
                            source: 'CUSTOM'
                        },
                        {
                            id: 4,
                            name: '热销商品2',
                            description: '精选优质商品，品质保证',
                            image: empty_product,
                            sell_price: '159.00',
                            lineation_price: '299.00',
                            sales_num: '856',
                            recommend_reason: '新品推荐',
                            source: 'CUSTOM'
                        },
                        {
                            id: 5,
                            name: '热销商品1',
                            description: '精选优质商品，品质保证',
                            image: empty_product,
                            sell_price: '99.00',
                            lineation_price: '199.00',
                            sales_num: '1234',
                            recommend_reason: '热销推荐',
                            source: 'CUSTOM'
                        },
                        {
                            id: 6,
                            name: '热销商品2',
                            description: '精选优质商品，品质保证',
                            image: empty_product,
                            sell_price: '159.00',
                            lineation_price: '299.00',
                            sales_num: '856',
                            recommend_reason: '新品推荐',
                            source: 'CUSTOM'
                        }
                    ]
                },
                styles: {
                    columns: 2,
                    bg_color: themeColors.bg_color || '#ffffff',
                    root_bg_color: themeColors.root_bg_color || '#f5f5f5',
                    title_color: themeColors.title_color || '#333333',
                    title_font_size: 16,
                    subtitle_color: themeColors.subtitle_color || '#666666',
                    subtitle_font_size: 12,
                    more_color: themeColors.more_color || '#409EFF',
                    name_color: themeColors.name_color || '#333333',
                    name_font_size: 14,
                    desc_color: themeColors.desc_color || '#666666',
                    desc_font_size: 12,
                    price_color: themeColors.price_color || '#ff4757',
                    scribing_price_color: themeColors.scribing_price_color || '#999999',
                    sales_color: themeColors.sales_color || '#999999',
                    reason_color: themeColors.reason_color || '#409EFF',
                    btn_type: 'primary',
                    btn_size: 'small',
                    btn_bg_color: themeColors.btn_bg_color || '#409EFF',
                    btn_color: themeColors.btn_color || '#ffffff',
                    tag_bg_color: themeColors.tag_bg_color || '#ff4757',
                    tag_text_color: themeColors.tag_text_color || '#ffffff',
                    border_radius: 8,
                    item_spacing: 10,
                    padding: 0,
                    padding_top: 0,
                    padding_horizontal: 0,
                    padding_bottom: 0
                }
            }
        case 'product-card':
            return {
                title: "选项卡",
                name: "tabs",
                show: 1,
                content: {
                    active: 0,
                    show_line: 1,
                    has_active_bg: 0,
                    max_product_count: 20,
                    data: [
                        {
                            name: "选项卡1",
                            style: 1,
                            goods_type: 1,
                            show_title: 1,
                            show_price: 1,
                            show_scribing_price: 1,
                            show_btn: 1,
                            btn_text: "购买",
                            show_tag: 0,
                            tag_type: 1,
                            custom_tag: "",
                            is_custom_tag: 0,
                            category: {
                                categoryId: "",
                                source: "",
                                supplierId: []
                            },
                            data: [],
                            title_color: themeColors.title_color || "#333333",
                            scribing_price_color: themeColors.scribing_price_color || "#999999",
                            price_color: themeColors.price_color || "#FF2C3C",
                            btn_bg_color: themeColors.btn_bg_color || "#FF2C3C",
                            btn_color: themeColors.btn_color || "#FFFFFF",
                            btn_border_radius: 30,
                            btn_border_color: "",
                            root_bg_color: "",
                            content_bg_color: "",
                            bg_color: "#FFFFFF",
                            padding: 0,
                            margin: 10,
                            padding_top: 10,
                            padding_horizontal: 10,
                            padding_bottom: 10,
                            goods_border_radius: 4,
                            tag_width: 0,
                            border_radius_top: 0,
                            border_radius_bottom: 0
                        }
                    ]
                },
                styles: {
                    root_bg_color: themeColors.root_bg_color || "",
                    bg_color: themeColors.bg_color || "#FFFFFF",
                    color: themeColors.color || "#333333",
                    active_color: themeColors.active_color || "#FF2C3C",
                    line_color: themeColors.line_color || "#FF2C3C",
                    active_bg_color: themeColors.active_bg_color || "",
                    // 选项卡样式
                    tab_gap: 8,
                    tab_padding_top: 12,
                    tab_padding_bottom: 12,
                    tab_padding_horizontal: 0,
                    tab_border_radius_top: 0,
                    tab_border_radius_bottom: 0,
                    // 商品样式
                    content_bg_color: themeColors.content_bg_color || "#FFFFFF",
                    margin: 10,
                    component_margin: 0,
                    content_padding_top: 10,
                    content_padding_bottom: 10,
                    content_padding_horizontal: 10,
                    content_border_radius_top: 0,
                    content_border_radius_bottom: 0,
                    goods_border_radius: 4
                }
            }
        case 'coupon':
            return {
                title: "优惠券",
                name: "coupon",
                show: 1,
                content: {
                    style: 1,
                    title: "超值优惠券",
                    bg_type: 1,
                    max_select: 10,
                    data: [
                        {
                            id: 'preview_1',
                            name: "新人专享券",
                            is_receive: 0,
                            is_available: 1,
                            use_type: "全场通用",
                            money: "5.00",
                            discount_content: "订单满100.00元, 减5.00元",
                            condition: "满100.00使用",
                            is_preview: true
                        },
                        {
                            id: 'preview_2',
                            name: "满减优惠券",
                            is_receive: 0,
                            is_available: 1,
                            use_type: "指定商品可用",
                            money: "10.00",
                            discount_content: "订单满200.00元, 减10.00元",
                            condition: "满200.00使用",
                            is_preview: true
                        },
                        {
                            id: 'preview_3',
                            name: "限时抢购券",
                            is_receive: 0,
                            is_available: 1,
                            use_type: "全场通用",
                            money: "20.00",
                            discount_content: "订单满300.00元, 减20.00元",
                            condition: "满300.00使用",
                            is_preview: true
                        }
                    ]
                },
                styles: {
                    root_bg_color: themeColors.root_bg_color || "#FFFFFF",
                    title_color: "#FFFFFF",
                    bg_color: themeColors.bg_color || "#FCE7E7",
                    bg_image: "",
                    text_color: themeColors.text_color || "#FF2C3C",
                    btn_bg_color: themeColors.btn_bg_color || "#FF2C3C",
                    btn_text_color: themeColors.btn_text_color || "#FFFFFF",
                    padding_top: 5,
                    padding_horizontal: 5,
                    padding_bottom: 5,
                    money_color: themeColors.money_color || "#FF2C3C",
                    condition_color: themeColors.condition_color || "#333333",
                    scene_color: themeColors.scene_color || "#999999"
                }
            }
        case 'seckill':
            return {
                title: '限时秒杀',
                endTime: '',
                products: []
            }
        case 'group-buy':
            return {
                title: '拼团活动',
                products: []
            }
        case 'export-frame':
            return {
                title: '弹出框',
                content: '弹出框内容',
                showClose: true
            }
        case 'blank-space':
            return {
                height: 41,
                background: '#b29999',
                rootBackground: '#df4949',
                marginTop: 24,
                marginBottom: 25,
                marginHorizontal: 24
            }
        case 'divider':
            return {
                style: 'dashed',
                color: '#E5E5E5',
                thickness: 1,
                backgroundColor: 'rgba(0,0,0,0)',
                marginTop: 10,
                marginBottom: 10,
                marginHorizontal: 0
            }
        default:
            return {}
    }
}

const selectWidget = (index) => {
    selectedWidgetIndex.value = index
}



// 处理预览区域点击事件，取消选中状态
const handlePreviewClick = (event) => {
    // 如果点击的是预览容器本身，则取消选中
    if (event.target === event.currentTarget) {
        selectedWidgetIndex.value = -1
    }
}

// 处理手机预览框点击事件
const handlePhonePreviewClick = (event) => {
    // 如果点击的是手机预览框的空白区域，则取消选中
    const pageContent = event.currentTarget.querySelector('.page-content')
    if (event.target === event.currentTarget ||
        (pageContent && event.target === pageContent)) {
        selectedWidgetIndex.value = -1
    }
}

// 处理页面内容区域点击事件
const handlePageContentClick = (event) => {
    // 如果点击的是页面内容区域本身，则取消选中
    if (event.target === event.currentTarget) {
        selectedWidgetIndex.value = -1
    }
}

// 处理组件列表区域点击事件
const handleWidgetListClick = (event) => {
    // 如果点击的是组件列表的空白区域（不是组件本身），则取消选中
    if (event.target === event.currentTarget) {
        selectedWidgetIndex.value = -1
    }
}

const removeWidget = (index) => {
    pageWidgets.value.splice(index, 1)
    if (selectedWidgetIndex.value >= pageWidgets.value.length) {
        selectedWidgetIndex.value = pageWidgets.value.length - 1
    }
    ElMessage.success('组件已删除')
}

// 隐藏组件
const hideWidget = (index) => {
    const widget = pageWidgets.value[index]
    widget.hidden = !widget.hidden
    ElMessage.success(widget.hidden ? '组件已隐藏' : '组件已显示')
}

// 复制组件
const copyWidget = (index) => {
    const widget = pageWidgets.value[index]
    const newWidget = {
        id: generateWidgetId(),
        type: widget.type,
        data: JSON.parse(JSON.stringify(widget.data)) // 深拷贝数据
    }

    // 在当前组件后面插入复制的组件
    pageWidgets.value.splice(index + 1, 0, newWidget)
    selectedWidgetIndex.value = index + 1
    ElMessage.success('组件已复制')
}

// 上移组件
const moveWidgetUp = (index) => {
    if (index > 0) {
        const widget = pageWidgets.value[index]

        // 搜索组件不能移动
        if (widget.type === 'search') {
            ElMessage.warning('搜索组件必须在页面顶部，不能移动')
            return
        }

        // 如果要移动到第一个位置，但第一个位置是搜索组件，则不能移动
        if (index === 1 && pageWidgets.value[0].type === 'search') {
            ElMessage.warning('搜索组件必须在页面顶部')
            return
        }

        pageWidgets.value.splice(index, 1)
        pageWidgets.value.splice(index - 1, 0, widget)
        selectedWidgetIndex.value = index - 1
        ElMessage.success('组件已上移')
    }
}

// 下移组件
const moveWidgetDown = (index) => {
    if (index < pageWidgets.value.length - 1) {
        const widget = pageWidgets.value[index]

        // 搜索组件不能移动
        if (widget.type === 'search') {
            ElMessage.warning('搜索组件必须在页面顶部，不能移动')
            return
        }

        pageWidgets.value.splice(index, 1)
        pageWidgets.value.splice(index + 1, 0, widget)
        selectedWidgetIndex.value = index + 1
        ElMessage.success('组件已下移')
    }
}

const previewPage = () => {
    ElMessage.info('预览功能开发中...')
}

const savePage = async () => {
    if (isSaving.value) return

    const mallId = getMallId()
    if (!mallId) {
        ElMessage.error('缺少商城ID参数')
        return
    }

    // 校验页面标题
    if (!pageConfig.value.title || pageConfig.value.title.trim() === '') {
        ElMessage.error('页面标题不能为空')
        return
    }

    if (pageConfig.value.title.length > 50) {
        ElMessage.error('页面标题不能超过50个字符')
        return
    }

    if (pageConfig.value.description && pageConfig.value.description.length > 200) {
        ElMessage.error('页面描述不能超过200个字符')
        return
    }

    isSaving.value = true

    const pageData = {
        mallId: mallId,
        config: pageConfig.value,
        widgets: pageWidgets.value.map(widget => ({
            id: widget.id,
            type: widget.type,
            data: widget.data,
            hidden: widget.hidden || false
        })),
        updateTime: new Date().toISOString()
    }

    try {
        console.log('保存页面数据:', pageData)

        let pageId = getPageId()
        const mode = getMode()

        // 如果是新建模式且没有pageId，先创建页面
        if (mode === 'create' && !pageId) {
            // 创建新页面
            const createPageData = {
                name: pageConfig.value.title || '未命名页面',
                description: pageConfig.value.description || '',
                config: JSON.stringify(pageConfig.value)
            }

            console.log('创建新页面:', createPageData)
            const createResponse = await createMinipage(mallId, createPageData)
            pageId = createResponse.data.id

            // 如果是通过路由访问的，更新路由参数，避免重复创建
            if (!props.pageId) {
                router.replace({
                    path: route.path,
                    query: {
                        ...route.query,
                        pageId: pageId,
                        mode: 'edit'
                    }
                })
            }

            console.log('页面创建成功，pageId:', pageId)
        }

        // 保存页面配置和组件数据
        if (pageId) {
            // 构建API需要的数据格式
            const apiData = {
                title: pageConfig.value.title || '未命名页面',
                description: pageConfig.value.description || '',
                config: JSON.stringify({
                    ...pageData.config,
                    showBottomNav: pageConfig.value.showBottomNav
                }),
                widgets: pageData.widgets.map((widget, index) => ({
                    widgetId: widget.id,
                    widgetType: widget.type,
                    widgetData: JSON.stringify(widget.data),
                    sortOrder: index + 1,
                    hidden: widget.hidden || false
                }))
            }
            console.log('保存页面配置:', apiData)
            await savePageConfig(mallId, pageId, apiData)
        }

        // 暂时保存到localStorage作为演示
        localStorage.setItem(`miniprogram_page_${mallId}`, JSON.stringify(pageData))

        const successMessage = mode === 'create' && !getPageId() ? '页面创建成功！' : '页面保存成功！'
        ElMessage.success(successMessage)

    } catch (error) {
        console.error('保存失败:', error)
        ElMessage.error('页面保存失败，请重试')
    } finally {
        isSaving.value = false
    }
}

// 从接口加载页面数据
const loadPageData = async () => {
    const mallId = getMallId()
    const pageId = getPageId()
    const mode = getMode()

    if (!mallId) {
        ElMessage.error('缺少商城ID参数')
        return false
    }

    // 如果是新建模式，不需要加载数据
    if (mode === 'create' || !pageId) {
        console.log('新建模式，不加载页面数据')
        return false
    }

    try {
        console.log('加载页面数据，mallId:', mallId, 'pageId:', pageId)
        const response = await getMinipageDetail(mallId, pageId)

        if (response.data) {
            const { page, widgets } = response.data

            // 恢复页面配置
            if (page) {
                pageConfig.value = {
                    title: page.pageName || '未命名页面',
                    description: page.pageDescription || '',
                    backgroundColor: page.backgroundColor || '#f5f5f5'
                }

                // 如果有页面配置JSON，解析并合并
                if (page.pageConfig) {
                    try {
                        const configData = JSON.parse(page.pageConfig)
                        pageConfig.value = {
                            ...pageConfig.value,
                            ...configData,
                            showBottomNav: configData.showBottomNav || false
                        }
                    } catch (error) {
                        console.warn('解析页面配置失败:', error)
                    }
                }
            }

            // 恢复组件数据
            if (widgets && Array.isArray(widgets)) {
                pageWidgets.value = widgets
                    .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0)) // 按排序顺序排列
                    .map(widget => {
                        let widgetData = {}
                        try {
                            widgetData = JSON.parse(widget.widgetData || '{}')
                        } catch (error) {
                            console.warn('解析组件数据失败:', error, widget)
                            widgetData = getDefaultWidgetData(widget.widgetType)
                        }

                        return {
                            id: widget.widgetId,
                            type: widget.widgetType,
                            data: widgetData,
                            hidden: widget.isHidden === 1
                        }
                    })

                console.log('页面数据加载成功，组件数量:', pageWidgets.value.length)
                ElMessage.success('页面数据加载成功')
                return true
            }
        }
    } catch (error) {
        console.error('加载页面数据失败:', error)
        ElMessage.error('加载页面数据失败')
    }

    return false
}



// 处理widget数据更新
const handleWidgetDataUpdate = (newData) => {
    if (selectedWidget.value) {
        console.log('编辑器 - 接收到配置更新:', newData)
        console.log('编辑器 - 更新前的数据:', selectedWidget.value.data)
        
        // 根据组件类型处理数据结构
        if (selectedWidget.value.type === 'divider' || selectedWidget.value.type === 'blank-space') {
            // 分割线和空白间距组件使用扁平数据结构
            selectedWidget.value.data = { ...newData }
        } else {
            // 其他组件使用content/styles结构
        selectedWidget.value.data = {
            content: { ...newData.content },
            styles: { ...newData.styles }
            }
        }
        
        console.log('编辑器 - 更新后的数据:', selectedWidget.value.data)
        
        // 触发响应式更新
        pageWidgets.value = [...pageWidgets.value]
    }
}

// 组件挂载时初始化
onMounted(async () => {
    // 初始化主题store
    themeStore.initThemes()
    
    // 获取并设置当前商城主题
    const mallId = getMallId()
    if (mallId) {
        await themeStore.getThemeAsync(mallId)
    }
    
    // 初始化空数据
    pageWidgets.value = []

    // 尝试加载页面数据
    await loadPageData()

    // 加载底部导航配置
    await loadBottomNavConfig()
    
    // 监听主题变化
    watchThemeChange((newTheme) => {
        console.log('主题已变化，更新组件颜色:', newTheme)
        // 更新所有组件的主题颜色
        pageWidgets.value = updateWidgetsTheme(pageWidgets.value)
        ElMessage.success('已应用新主题颜色')
    })
})
</script>

<style scoped>
.miniprogram-editor {
    display: flex;
    background: #f5f5f5;
    overflow: hidden;
}



/* 中间预览区域 */
.preview-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #f5f5f5;
    padding: 10px;
    overflow: hidden;
    position: relative;
}

/* 预览头部 */
.preview-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 60px;
    background: #fff;
    padding: 16px 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.preview-title h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.preview-actions {
    display: flex;
    gap: 12px;
}



.page-content {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    position: relative;
    /* 确保sticky定位能正常工作 */
    contain: layout style paint;
}



.widget-list {
    min-height: 400px;
    position: relative;
    overflow: visible;
}

.widget-container {
    position: relative;
    border: 1px solid transparent;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    overflow: visible;
}

.widget-container:hover {
    border-color: #409EFF;
}

.widget-container.active {
    border-color: #409EFF;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.widget-container.active.dragging {
    border-color: transparent;
    box-shadow: none;
}

.widget-container.hidden {
    opacity: 0.5;
    position: relative;
}

.widget-container.hidden::after {
    content: '隐藏';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 10;
}

/* 预览容器 */
.preview-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    overflow: visible;
}



/* 外部工具栏 */
.widget-toolbar-external {
    position: fixed;
    z-index: 300;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 8px;
    padding: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.widget-toolbar-external .toolbar-buttons {
    display: flex;
    flex-direction: row;
    gap: 6px;
}

.widget-toolbar-external .el-button {
    width: 32px;
    height: 32px;
    padding: 0;
    border: none;
    background: rgba(255, 255, 255, 0.9);
    color: #666;
    transition: all 0.2s ease;
    margin: 0;
    font-size: 14px;
}

.widget-toolbar-external .el-button:hover {
    background: #E5E5E5;
    color: #409EFF;
    transform: scale(1.1);
}

.widget-toolbar-external .el-button.is-disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: rgba(255, 255, 255, 0.5);
    color: #999;
}

.widget-toolbar-external .el-button.is-disabled:hover {
    transform: none;
    background: rgba(255, 255, 255, 0.5);
}

.widget-toolbar-external .el-button--danger {
    color: #f56565;
}

.widget-toolbar-external .el-button--danger:hover {
    color: #e53e3e;
}



.drop-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60px;
    border: 2px dashed #409EFF;
    border-radius: 4px;
    margin: 16px;
    background: rgba(64, 158, 255, 0.05);
    z-index: 200;
    position: relative;
}

.drop-hint {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.drop-text {
    color: #409EFF;
    font-size: 14px;
    text-align: center;
    padding: 16px;
}

.drop-hint-container {
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 200;
    width: 100%;

    position: relative;
}

.drop-hint-box {
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    border: 2px dashed #409EFF;
    border-radius: 4px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(4px);
    position: relative;
    z-index: 201;
}

.drop-hint-text {
    color: #409EFF;
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
    padding: 8px 16px;
    display: block;
}

.drop-hint-top {
    margin-bottom: 8px;
}

.drop-hint-between {
    margin: 8px 0;
}

.drop-hint-bottom {
    margin-top: 8px;
}

/* 未知组件样式 */
.unknown-widget {
    margin: 8px 16px;
    padding: 20px;
    border: 2px dashed #e0e0e0;
    border-radius: 8px;
    background: #f9f9f9;
}

.unknown-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #999;
    text-align: center;
}

.unknown-icon {
    font-size: 32px;
    margin-bottom: 8px;
}

.unknown-text {
    font-size: 14px;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #999;
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 12px;
}

/* 右侧属性面板 */
.property-panel {
    width: 380px;
    background: #fff;
    border-left: 1px solid #e4e7ed;
    display: flex;
    flex-direction: column;
}

.panel-header {
    padding: 0;
    border-bottom: 1px solid #e4e7ed;
}

.panel-tabs {
    display: flex;
}

.tab-item {
    flex: 1;
    padding: 16px 20px;
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 2px solid transparent;
}

.tab-item:hover {
    color: #3b82f6;
    background: #f0f9ff;
}

.tab-item.active {
    color: #3b82f6;
    background: #fff;
    border-bottom-color: #3b82f6;
}

.panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

.page-settings,
.widget-properties {
    height: 100%;
}

.setting-section,
.property-header {
    margin-bottom: 20px;
}

.setting-section h3,
.property-header h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

/* 配置区块样式 - 和TitleConfig保持一致 */
.config-section {
    margin-bottom: 20px;
    border-bottom: 1px solid #e4e7ed;
    padding-bottom: 16px;
}

.config-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.section-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin: 0 0 12px 0;
}

.form-group {
    margin-bottom: 16px;
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 6px;
}

.color-control {
    display: flex;
    align-items: center;
    gap: 8px;
}

.radio-group {
    display: flex;
    gap: 16px;
}



.property-placeholder,
.no-selection {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #999;
    text-align: center;
}

.property-placeholder .el-icon,
.no-selection-icon {
    font-size: 48px;
    margin-bottom: 12px;
}

.placeholder-text {
    font-size: 12px;
    margin-top: 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .property-panel {
        width: 280px;
    }
}



@media (max-width: 768px) {
    .miniprogram-editor {
        flex-direction: column;
        height: auto;
    }

    .preview-section {
        order: 1;
        padding: 10px;
    }

    .property-panel {
        width: 100%;
        order: 2;
    }
}
</style>
