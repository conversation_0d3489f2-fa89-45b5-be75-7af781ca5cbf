<template>
  <div class="miniprogram-container">
    <!-- 温馨提示 -->
    <el-alert title="温馨提示：微页面指定为商城首页后不能删除。" type="info" :closable="false" style="margin-bottom: 20px" />

    <!-- 搜索栏 -->
    <div class="header">
      <el-input v-model="searchKeyword" placeholder="请输入页面名称" clearable style="width: 200px; margin-right: 10px"
        @keyup.enter="handleSearchChange" @input="handleSearchChange" @clear="handleSearchClear" />
      <el-button type="primary" @click="handleSearch">搜索</el-button>
      <el-button @click="handleReset">重置</el-button>
      <el-button type="primary" @click="handleCreatePage">
        <el-icon>
          <CirclePlus />
        </el-icon>
        新建微页面
      </el-button>
    </div>

    <!-- 页面列表 -->
    <el-table height="calc(100vh - 280px)" :data="pageList" v-loading="loading" style="margin-top: 16px">
      <!-- 页面名称列 -->
      <el-table-column prop="name" label="页面名称" min-width="150">
        <template #default="{ row }">
          <div class="page-name-cell">
            <el-icon v-if="row.isHomePage" class="home-icon">
              <House />
            </el-icon>
            <span>{{ row.name }}</span>
          </div>
        </template>
      </el-table-column>

      <!-- 更新时间列 -->
      <el-table-column prop="updateTime" label="更新时间" width="180">
        <template #default="{ row }">
          {{ row.updateTime }}
        </template>
      </el-table-column>

      <!-- 操作列 -->
      <el-table-column label="操作" width="300" align="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button v-if="!row.isHomePage" link type="primary" size="small" @click="handleSetAsHomePage(row)">
              <el-icon>
                <House />
              </el-icon>
              设为首页
            </el-button>
            <el-button link type="primary" size="small" @click="handleEditPage(row)">
              <el-icon>
                <Edit />
              </el-icon>
              编辑界面
            </el-button>
            <el-button v-if="!row.isHomePage" link type="danger" size="small" @click="handleDeletePage(row)">
              <el-icon>
                <Delete />
              </el-icon>
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total" background
        :page-sizes="[10, 20, 50, 100]" @size-change="handleSizeChange" @current-change="fetchPageList" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { debounce } from 'lodash'
import {
  CirclePlus,
  House,
  Edit,
  Delete
} from '@element-plus/icons-vue'
import { getMinipageList, deleteMinipage, setHomePage } from '@/api/minipage.ts'

const router = useRouter()
const route = useRoute()

// 响应式数据
const pageList = ref<any[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const searchKeyword = ref('')
const loading = ref(false)

// 创建防抖搜索函数
const debouncedFetchPageList = debounce(() => {
  currentPage.value = 1 // 搜索时重置到第一页
  fetchPageList()
}, 600)

// 获取商城ID
const getMallId = () => {
  const mallId = route.query.mallId || route.params.mallId
  return Array.isArray(mallId) ? mallId[0] : mallId
}

// 获取页面列表
const fetchPageList = async () => {
  loading.value = true
  try {
    const mallId = getMallId()
    if (!mallId) {
      ElMessage.error('缺少商城ID参数')
      return
    }

    const params: any = {
      mallId,
      page: currentPage.value,
      pageSize: pageSize.value
    }

    if (searchKeyword.value) {
      params.keyword = searchKeyword.value
    }

    const response = await getMinipageList(mallId, params)

    // 处理响应数据
    pageList.value = response.data.records.map((item: any) => ({
      id: item.id,
      name: item.pageName,
      updateTime: formatDate(item.updateTime),
      isHomePage: item.isHomePage === 1
    }))

    total.value = response.data.total

  } catch (error) {
    console.error('获取页面列表失败:', error)
    ElMessage.error('获取页面列表失败')
  } finally {
    loading.value = false
  }
}

// 处理搜索输入变化
const handleSearchChange = () => {
  debouncedFetchPageList()
}

// 处理搜索清空
const handleSearchClear = () => {
  searchKeyword.value = ''
  currentPage.value = 1
  fetchPageList()
}

// 搜索按钮
const handleSearch = () => {
  currentPage.value = 1
  fetchPageList()
}

// 重置按钮
const handleReset = () => {
  searchKeyword.value = ''
  currentPage.value = 1
  fetchPageList()
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchPageList()
}

// 新建微页面
const handleCreatePage = () => {
  const mallId = getMallId()
  if (!mallId) {
    ElMessage.error('缺少商城ID参数')
    return
  }

  router.push({
    path: '/decoration/miniprogram/editer',
    query: { mallId, mode: 'create' }
  })
}

// 编辑页面
const handleEditPage = (row: any) => {
  const mallId = getMallId()
  if (!mallId) {
    ElMessage.error('缺少商城ID参数')
    return
  }

  router.push({
    path: '/decoration/miniprogram/editer',
    query: { mallId, pageId: row.id, mode: 'edit' }
  })
}

// 设为首页
const handleSetAsHomePage = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要将页面 "${row.name}" 设为首页吗？`,
      '设为首页',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const mallId = getMallId()
    if (!mallId) {
      ElMessage.error('缺少商城ID参数')
      return
    }

    await setHomePage(mallId, row.id)

    // 更新本地数据
    pageList.value.forEach(item => {
      item.isHomePage = item.id === row.id
    })

    ElMessage.success('设置成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('设为首页失败:', error)
      ElMessage.error('设为首页失败')
    }
  }
}

// 删除页面
const handleDeletePage = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除页面 "${row.name}" 吗？删除后无法恢复。`,
      '删除页面',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const mallId = getMallId()
    if (!mallId) {
      ElMessage.error('缺少商城ID参数')
      return
    }

    await deleteMinipage(mallId, row.id)

    ElMessage.success('删除成功')
    fetchPageList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

// 组件挂载时获取数据
onMounted(() => {
  console.log('微页面列表页面已加载，mallId:', getMallId())
  fetchPageList()
})
</script>

<style scoped lang="scss">
.miniprogram-container {
  padding: 20px;
}

.header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.page-name-cell {
  display: flex;
  align-items: center;
  gap: 6px;
}

.home-icon {
  color: #409EFF;
  font-size: 16px;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: flex-end;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

// 响应式设计
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .action-buttons {
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
  }
}
</style>