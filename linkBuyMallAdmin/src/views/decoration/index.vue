<template>
  <div class="decoration-container">
    <!-- 左侧菜单 -->
    <div class="decoration-sidebar">
      <div class="sidebar-header">
        <div class="header-content">
          <h3>{{ mallName }}</h3>
          <el-button class="back-button" @click="handleBack" type="text" size="small">
            <el-icon>
              <ArrowLeft />
            </el-icon>
            返回
          </el-button>
        </div>
      </div>

      <div class="menu-list">
        <div v-for="menu in menuList" :key="menu.key" class="menu-item" :class="{ active: activeMenu === menu.key }"
          @click="handleMenuClick(menu)">
          <div class="menu-icon">
            <el-icon>
              <component :is="menu.icon" />
            </el-icon>
          </div>
          <span class="menu-title">{{ menu.title }}</span>
        </div>
      </div>
    </div>

    <!-- 右侧内容区域 -->
    <div class="decoration-content">
      <div class="content-header">
        <div class="breadcrumb">
          <span class="breadcrumb-item">商城装修</span>
          <el-icon class="breadcrumb-separator">
            <ArrowRight />
          </el-icon>
          <span class="breadcrumb-item active">{{ getCurrentMenuTitle() }}</span>
        </div>
      </div>

      <div class="content-main">
        <!-- 动态组件区域 -->
        <router-view v-if="showRouterView" />

        <!-- 默认欢迎页面 -->
        <div v-else class="welcome-content">
          <div class="welcome-card">
            <div class="welcome-icon">
              <el-icon>
                <Setting />
              </el-icon>
            </div>
            <h2>欢迎使用商城装修</h2>
            <p>请从左侧菜单选择要装修的模块开始设计您的商城</p>

            <div class="quick-actions">
              <div class="action-grid">
                <div v-for="action in quickActions" :key="action.key" class="action-item"
                  @click="handleMenuClick(action)">
                  <div class="action-icon">
                    <el-icon>
                      <component :is="action.icon" />
                    </el-icon>
                  </div>
                  <div class="action-info">
                    <h4>{{ action.title }}</h4>
                    <p>{{ action.description }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  House,
  Grid,
  Document,
  ShoppingCart,
  User,
  Guide,
  Promotion,
  Monitor,
  Setting,
  ArrowRight,
  ArrowLeft,
  View,
  Check
} from '@element-plus/icons-vue'
import { mallDetail } from '@/api/mall'
import { useThemeStore } from '@/store/theme'

export default {
  name: 'DecorationIndex',
  components: {
    House,
    Grid,
    Document,
    ShoppingCart,
    User,
    Guide,
    Promotion,
    Monitor,
    Setting,
    ArrowRight,
    ArrowLeft,
    View,
    Check
  },
  data() {
    return {
      activeMenu: '',
      mallName: '商城装修', // 默认标题
      menuList: [
        {
          key: 'home',
          title: '商城主页',
          icon: 'House',
          route: '/decoration/home',
          description: '设置商城首页布局和内容'
        },
        {
          key: 'category',
          title: '商品分类',
          icon: 'Grid',
          route: '/decoration/category',
          description: '管理商品分类展示'
        },
        {
          key: 'product',
          title: '商品详情',
          icon: 'Document',
          route: '/decoration/product',
          description: '自定义商品详情页面'
        },
        {
          key: 'cart',
          title: '购物车',
          icon: 'ShoppingCart',
          route: '/decoration/cart',
          description: '设计购物车页面样式'
        },
        {
          key: 'profile',
          title: '个人中心',
          icon: 'User',
          route: '/decoration/profile',
          description: '个人中心页面装修'
        },
        {
          key: 'navigation',
          title: '底部导航',
          icon: 'Guide',
          route: '/decoration/navigation',
          description: '自定义底部导航栏'
        },
        {
          key: 'advertisement',
          title: '开屏广告',
          icon: 'Promotion',
          route: '/decoration/advertisement',
          description: '设置应用开屏广告'
        },
        {
          key: 'miniprogram',
          title: '微页面',
          icon: 'Monitor',
          route: '/decoration/miniprogram',
          description: '创建自定义微页面'
        },
        {
          key: 'theme',
          title: '商城主题',
          icon: 'Setting',
          route: '/decoration/theme',
          description: '选择商城主题'
        }
      ]
    }
  },
  computed: {
    showRouterView() {
      return this.$route.path !== '/decoration' && this.$route.path !== '/decoration/'
    },
    quickActions() {
      // 返回前4个常用功能作为快捷操作
      return this.menuList.slice(0, 4)
    }
  },
  watch: {
    '$route'(to) {
      // 根据当前路由设置活跃菜单
      this.setActiveMenuFromRoute(to.path)
    }
  },
  mounted() {
    // 初始化主题store
    const themeStore = useThemeStore()
    themeStore.initThemes()

    // 初始化时根据当前路由设置活跃菜单
    this.setActiveMenuFromRoute(this.$route.path)
    // 获取商城详情
    this.fetchMallDetail()
  },
  methods: {
    handleMenuClick(menu) {
      this.activeMenu = menu.key
      // 路由跳转，保持 mallId 参数
      if (menu.route && this.$route.path !== menu.route) {
        this.$router.push({
          path: menu.route,
          query: {
            ...this.$route.query,
            mallId: this.$route.query.mallId
          }
        })
      }
    },
    getCurrentMenuTitle() {
      const currentMenu = this.menuList.find(menu => menu.key === this.activeMenu)
      return currentMenu ? currentMenu.title : '商城装修'
    },
    setActiveMenuFromRoute(path) {
      const menu = this.menuList.find(menu => menu.route === path)
      if (menu) {
        this.activeMenu = menu.key
      } else {
        this.activeMenu = ''
      }
    },
    handlePreview() {
      // 预览功能
      this.$message.info('预览功能开发中...')
    },
    handleSave() {
      // 保存功能
      this.$message.success('保存成功！')
    },
    handleBack() {
      // 返回商城列表页面
      this.$router.push('/mall')
    },
    async fetchMallDetail() {
      try {
        // 从路由参数或其他方式获取商城ID
        const mallId = this.getMallId()
        if (mallId) {
          const response = await mallDetail(Number(mallId))
          if (response.code === 200 && response.data) {
            this.mallName = response.data.name
          }
        }
      } catch (error) {
        console.error('获取商城详情失败:', error)
        // 保持默认标题
      }
    },
    getMallId() {
      // 统一获取商城ID的方法
      return this.$route.query.mallId || this.$route.params.mallId
    }
  }
}
</script>

<style scoped>
.decoration-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  display: flex;
  background: #f5f5f5;
  overflow: hidden;
}

/* 左侧菜单样式 */
.decoration-sidebar {
  width: 240px;
  background: #fff;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 14px 20px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafafa;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.back-button {
  padding: 6px 8px;
  color: #666;
  font-size: 14px;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.back-button:hover {
  color: #409EFF;
  background: #f0f9ff;
}

.back-button .el-icon {
  margin-right: 6px;
  font-size: 14px;
}

.sidebar-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.menu-list {
  flex: 1;
  padding: 10px 0;
  overflow-y: auto;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.menu-item:hover {
  background: #f0f9ff;
  color: #409EFF;
}

.menu-item.active {
  background: #e6f7ff;
  color: #409EFF;
  border-left-color: #409EFF;
}

.menu-icon {
  margin-right: 12px;
  font-size: 18px;
  width: 20px;
  display: flex;
  justify-content: center;
}

.menu-title {
  font-size: 14px;
  font-weight: 500;
}

/* 右侧内容区域样式 */
.decoration-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.content-header {
  background: #fff;
  padding: 16px 24px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.breadcrumb {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.breadcrumb-item {
  color: #666;
}

.breadcrumb-item.active {
  color: #333;
  font-weight: 500;
}

.breadcrumb-separator {
  margin: 0 8px;
  color: #ccc;
  font-size: 12px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.header-actions .el-button {
  padding: 8px 16px;
  font-size: 14px;
}

.content-main {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  background: #f5f5f5;
}

/* 欢迎页面样式 */
.welcome-content {
  padding: 40px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 100%;
}

.welcome-card {
  background: #fff;
  border-radius: 12px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  max-width: 800px;
  width: 100%;
}

.welcome-icon {
  font-size: 64px;
  color: #409EFF;
  margin-bottom: 24px;
}

.welcome-card h2 {
  font-size: 24px;
  color: #333;
  margin: 0 0 12px 0;
  font-weight: 600;
}

.welcome-card p {
  font-size: 16px;
  color: #666;
  margin: 0 0 40px 0;
  line-height: 1.5;
}

.quick-actions {
  margin-top: 40px;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
}

.action-item:hover {
  background: #e6f7ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.action-icon {
  font-size: 32px;
  color: #409EFF;
  margin-right: 16px;
  flex-shrink: 0;
}

.action-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #333;
  font-weight: 600;
}

.action-info p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .decoration-sidebar {
    width: 200px;
  }

  .menu-item {
    padding: 10px 16px;
  }

  .menu-title {
    font-size: 13px;
  }

  .content-header {
    padding: 12px 16px;
  }

  .welcome-content {
    padding: 20px;
  }

  .welcome-card {
    padding: 30px 20px;
  }

  .action-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .decoration-container {
    flex-direction: column;
  }

  .decoration-sidebar {
    width: 100%;
    height: auto;
    max-height: 200px;
  }

  .menu-list {
    display: flex;
    overflow-x: auto;
    padding: 10px;
  }

  .menu-item {
    flex-direction: column;
    min-width: 80px;
    padding: 8px;
    text-align: center;
  }

  .menu-icon {
    margin-right: 0;
    margin-bottom: 4px;
  }

  .menu-title {
    font-size: 12px;
  }
}
</style>
