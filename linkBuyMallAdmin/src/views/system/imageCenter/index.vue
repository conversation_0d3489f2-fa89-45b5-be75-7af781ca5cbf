<template>
    <div class="image-center-container">
        <!-- 左侧分组列表 -->
        <div class="group-list">
            <el-menu :default-active="activeGroup" class="group-menu" @select="handleGroupSelect">
                <el-menu-item
                    style="height: 40px; position: relative; padding: 0 0px;"
                    v-for="group in groupList"
                    :key="group.id"
                    :index="group.id + ''"
                    @mouseenter="group.hover = true"
                    @mouseleave="group.hover = false"
                >
                    <div class="group-item-content" :class="{ active: activeGroup === group.id }">
                        <el-icon><Folder /></el-icon>
                        <div class="group-name-actions">
                            <template v-if="group.id !== 'all' && group.id !== 'ungroup'">
                                <template v-if="group.renameVisible">
                                    <el-popover
                                        placement="bottom"
                                        v-model:visible="group.renameVisible"
                                        :teleported="false"
                                        trigger="manual"
                                        popper-class="group-rename-popover"
                                    >
                                        <div class="rename-popover-content">
                                            <el-input
                                                v-model="renameGroupName"
                                                placeholder="分类名称"
                                                maxlength="20"
                                                show-word-limit
                                                style="margin-bottom: 16px;"
                                                @keyup.enter="handleRenameGroup(group)"
                                            />
                                            <div class="rename-popover-actions">
                                                <el-button type="primary" link @click="group.renameVisible = false" class="rename-cancel-btn">取消</el-button>
                                                <el-button type="primary" size="small" @click="handleRenameGroup(group)">确定</el-button>
                                            </div>
                                        </div>
                                        <template #reference>
                                            <span class="group-name">{{ group.name }}</span>
                                        </template>
                                    </el-popover>
                                </template>
                                <template v-else>
                                    <span class="group-name">{{ group.name }}</span>
                                </template>
                            </template>
                            <template v-else>
                                <span class="group-name">{{ group.name }}</span>
                            </template>
                        </div>
                        <template v-if="group.id !== 'all' && group.id !== 'ungroup'">
                            <el-popover
                                class="group-popover"
                                placement="bottom-end"
                                v-model:visible="group.moreVisible"
                                :teleported="false"
                                popper-class="group-action-popover"
                                trigger="hover"
                                :show-after="0"
                                :hide-after="0"
                            >
                                <div class="group-popover-menu">
                                    <div class="group-popover-item" @click.stop="handleGroupCommand('rename', group)">重命名</div>
                                    <div class="group-popover-item danger" @click.stop="handleGroupCommand('delete', group)">删除分组</div>
                                </div>
                                <template #reference>
                                    <span class="group-more">···</span>
                                </template>
                            </el-popover>
                        </template>
                    </div>
                </el-menu-item>
            </el-menu>
            <el-button class="add-group-btn" type="primary" plain @click="addGroupDialog = true">添加分组</el-button>
        </div>

        <!-- 右侧图片列表 -->
        <div class="image-list-wrapper">
            <div class="image-list">
                <div class="image-list-toolbar">
                    <el-input
                        v-model="filterName"
                        placeholder="请输入图片名称"
                        clearable
                        style="width: 220px; margin-right: 16px;"
                        @input="handleFilterChange"
                        @clear="handleSearchClear"
                    />
                    <el-button type="primary" @click="handleUploadImage">上传图片</el-button>
                </div>
                <div class="images" v-loading="loading">
                    <div v-for="img in images" :key="img.id" class="image-item">
                        <div class="image-thumb-wrapper" @click="toggleImageCheck(img)" @mouseenter="img.hover = true"
                            @mouseleave="img.hover = false" style="cursor: pointer;">
                            <img :src="img.url" :alt="img.name" class="image-thumb" />
                            <div v-if="img.checked" class="image-mask">
                                <svg class="check-icon" viewBox="0 0 48 48">
                                    <circle cx="24" cy="24" r="22" fill="none" />
                                    <polyline points="14,26 22,34 34,18" fill="none" stroke="#fff" stroke-width="4"
                                        stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                            </div>
                            <div class="image-actions" :class="{ show: img.hover }">
                                <el-tooltip content="重命名" placement="top" :enterable="false">
                                    <el-link type="primary" @click.stop="handleRename(img)">
                                        <el-icon><Edit /></el-icon>
                                    </el-link>
                                </el-tooltip>
                                <el-tooltip content="查看" placement="top" :enterable="false">
                                    <el-link type="primary" @click.stop="handleView(img)">
                                        <el-icon><View /></el-icon>
                                    </el-link>
                                </el-tooltip>
                                <el-tooltip content="删除" placement="top" :enterable="false">
                                    <el-link type="danger" @click.stop="handleDelete(img)">
                                        <el-icon><Delete /></el-icon>
                                    </el-link>
                                </el-tooltip>
                            </div>
                        </div>
                        <div class="image-name" :title="img.name">
                            <template v-if="img.renameVisible">
                                <el-popover
                                    placement="bottom"
                                    v-model:visible="img.renameVisible"
                                    :teleported="false"
                                    trigger="manual"
                                    popper-class="image-rename-popover"
                                >
                                    <div class="rename-popover-content">
                                        <el-input
                                            v-model="renameImageName"
                                            placeholder="图片名称"
                                            maxlength="50"
                                            show-word-limit
                                            style="margin-bottom: 16px;"
                                            @keyup.enter="handleRenameImage(img)"
                                        />
                                        <div class="rename-popover-actions">
                                            <el-button type="primary" link @click="img.renameVisible = false" class="rename-cancel-btn">取消</el-button>
                                            <el-button type="primary" size="small" @click="handleRenameImage(img)">确定</el-button>
                                        </div>
                                    </div>
                                    <template #reference>
                                        <span class="image-name-text">{{ img.name }}</span>
                                    </template>
                                </el-popover>
                            </template>
                            <template v-else>
                                <span class="image-name-text">{{ img.name }}</span>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
            <div class="pagination-bar">
                <div class="pagination-actions">
                    <el-checkbox v-model="selectAll" @change="handleSelectAll">当页全选</el-checkbox>
                    <el-button type="danger" @click="handleBatchDelete" :disabled="!selectedImages.length">删除</el-button>
                    <el-button type="primary" @click="openMoveDialog" :disabled="!selectedImages.length">移动</el-button>
                </div>
                <div class="pagination">
                    <el-pagination v-model:current-page="pagination.page" v-model:page-size="pagination.pageSize"
                        :total="pagination.total" :pager-count="7" background @size-change="handleSizeChange"
                        @current-change="fetchImages" />
                </div>
            </div>
        </div>

        <!-- 添加分组弹窗 -->
        <el-dialog v-model="addGroupDialog" title="添加分组" width="300px">
            <el-input v-model="newGroupName" placeholder="请输入分组名称" />
            <template #footer>
                <el-button @click="addGroupDialog = false">取消</el-button>
                <el-button type="primary" @click="handleAddGroup">确定</el-button>
            </template>
        </el-dialog>

        <el-image-viewer v-if="previewViewerVisible" :url-list="[previewImgUrl]" :z-index="3000"
            @close="previewViewerVisible = false" />

        <input
            type="file"
            ref="uploadInput"
            style="display: none"
            accept="image/*"
            multiple
            @change="onUploadFileChange"
        />

        <el-dialog v-model="moveDialogVisible" title="选择目标分组" width="300px">
            <el-select v-model="moveTargetTagId" placeholder="请选择分组" style="width: 100%">
                <el-option
                    v-for="group in groupList.filter(g => g && g.id && g.id !== 'all' && g.id !== 'ungroup' && g.id !== undefined)"
                    :key="group.id"
                    :label="group.name"
                    :value="group.id"
                />
            </el-select>
            <template #footer>
                <el-button @click="moveDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="confirmBatchMove">确定</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Folder, View, Edit, Delete } from '@element-plus/icons-vue'
import ElImageViewer from 'element-plus/es/components/image-viewer/index.mjs'
import { debounce } from 'lodash'
import {
  getGroupList,
  addGroup,
  renameGroup,
  deleteGroup,
  getFileList,
  uploadFile,
  batchUploadFile,
  deleteFile,
  batchDeleteFile,
  batchMoveFile,
  renameFile
} from '@/api/imageCenter'

// 分组数据
const groupList = ref([
])
const activeGroup = ref('all')

// 图片数据
const images = ref([])
const pagination = reactive({
    page: 1,
    pageSize: 55,
    total: 0,
})
const selectAll = ref(false)
const selectedImages = ref([])
const loading = ref(false)

// 添加分组弹窗
const addGroupDialog = ref(false)
const newGroupName = ref('')

// 预览相关
const previewViewerVisible = ref(false)
const previewImgUrl = ref('')

// 重命名相关
const renameGroupName = ref('')
const renameImageName = ref('')

// 过滤相关
const filterName = ref('')
const debouncedFetchImages = debounce(fetchImages, 600)

// 上传相关
const uploadInput = ref(null)

// 移动相关
const moveDialogVisible = ref(false)
const moveTargetTagId = ref('')

// 获取分组（可替换为API）
async function fetchGroups() {
    const res = await getGroupList()
    const defaultGroups = [
        { id: 'all', name: '全部', hover: false, moreVisible: false, renameVisible: false },
        { id: 'ungroup', name: '未分组', hover: false, moreVisible: false, renameVisible: false }
    ]
    if (res && res.data) {
        groupList.value = [
            ...defaultGroups,
            ...res.data.map(g => ({
                ...g,
                hover: false,
                moreVisible: false,
                renameVisible: false
            }))
        ]
        // 可选：设置 activeGroup
        if (groupList.value.length > 0) {
            activeGroup.value = groupList.value[0].id
        }
    } else {
        groupList.value = defaultGroups
        activeGroup.value = 'all'
    }
}

// 获取图片（可替换为API）
async function fetchImages() {
    loading.value = true
    const params = {
        tagId: activeGroup.value,
        name: filterName.value,
        page: pagination.page,
        pageSize: pagination.pageSize
    }
    try {
        const res = await getFileList(params)
        if (res && res.data) {
            const pageData = res.data;
            
            // 处理MyBatis-Plus的IPage返回格式
            if (pageData.records) {
                // 将图片数据转换为我们需要的格式
                images.value = pageData.records.map(img => ({
                    ...img,
                    url: img.path, // 兼容你的图片展示
                    checked: false,
                    hover: false,
                    renameVisible: false // 添加重命名状态
                }));
                
                // 更新分页信息
                pagination.total = pageData.total;
                
                // 确保分页参数与后端一致
                if (pageData.size && pageData.size !== pagination.pageSize) {
                    pagination.pageSize = pageData.size;
                }
                
                if (pageData.current && pageData.current !== pagination.page) {
                    pagination.page = pageData.current;
                }
            } else {
                images.value = [];
                console.error('返回的数据格式不符合预期');
            }
        }
    } catch (error) {
        console.error('获取图片列表失败:', error);
        ElMessage.error('获取图片列表失败');
        images.value = [];
    } finally {
        loading.value = false;
        
        // 如果没有选中的图片，重置全选状态
        if (images.value.length === 0) {
            selectAll.value = false;
        }
    }
}

onMounted(() => {
    fetchGroups()
    fetchImages()
})

// 分组切换
function handleGroupSelect(id) {
    activeGroup.value = id
    pagination.page = 1
    fetchImages()
}

// 批量选择
function handleSelectAll(val) {
    images.value.forEach(img => (img.checked = val))
    updateSelectedImages()
}
function toggleImageCheck(img) {
    img.checked = !img.checked
    updateSelectedImages()
    selectAll.value = images.value.length > 0 && images.value.every(i => i.checked)
}
function updateSelectedImages() {
    selectedImages.value = images.value.filter(i => i.checked)
}

// 批量删除
async function handleBatchDelete() {
    try {
        await ElMessageBox.confirm('确定要删除选中的图片吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })
        const ids = selectedImages.value.map(i => i.id)
        const res = await batchDeleteFile({ ids })
        if (res && res.data) {
    ElMessage.success('已删除')
    fetchImages()
}
    } catch (e) {
        // 用户取消，无需处理
    }
}

// 移动相关
function openMoveDialog() {
    moveDialogVisible.value = true
    moveTargetTagId.value = ''
}

async function confirmBatchMove() {
    if (!moveTargetTagId.value) {
        ElMessage.warning('请选择目标分组')
        return
    }
    const ids = selectedImages.value.map(i => i.id)
    const res = await batchMoveFile({ ids, targetTagId: moveTargetTagId.value })
    if (res && res.data) {
    ElMessage.success('已移动')
    fetchImages()
        moveDialogVisible.value = false
    }
}

// 单个操作
async function handleRename(img) {
    renameImageName.value = img.name
    img.renameVisible = true
    nextTick(() => {
        const input = document.querySelector('.image-rename-popover .el-input__inner')
        input?.focus()
    })
}

async function handleRenameImage(img) {
    if (!renameImageName.value.trim()) {
        ElMessage.warning('请输入图片名称')
        return
    }
    const res = await renameFile({ id: img.id, name: renameImageName.value })
    if (res && res.data) {
        ElMessage.success('重命名成功')
        img.renameVisible = false
        fetchImages()
    }
}

async function handleDelete(img) {
    try {
        await ElMessageBox.confirm('确定要删除该图片吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })
        const res = await deleteFile({ id: img.id })
        if (res && res.data) {
    ElMessage.success('已删除')
    fetchImages()
        }
    } catch (e) {
        // 用户取消，无需处理
    }
}
function handleView(img) {
    previewImgUrl.value = img.url
    previewViewerVisible.value = true
}

// 分页
function handleSizeChange(size) {
    pagination.pageSize = size
    pagination.page = 1
    fetchImages()
}

// 添加分组
async function handleAddGroup() {
    if (!newGroupName.value.trim()) {
        ElMessage.warning('请输入分组名称')
        return
    }
    const res = await addGroup({ name: newGroupName.value })
    if (res && res.data) {
    ElMessage.success('添加成功')
    addGroupDialog.value = false
    newGroupName.value = ''
        fetchGroups()
    }
}

async function handleGroupCommand(command, group) {
    if (command === 'rename') {
        renameGroupName.value = group.name
        group.renameVisible = true
        group.moreVisible = false
        nextTick(() => {
            const input = document.querySelector('.group-rename-popover .el-input__inner')
            input?.focus()
        })
    } else if (command === 'delete') {
        try {
            await ElMessageBox.confirm('确定要删除该分组吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
            const res = await deleteGroup({ id: group.id })
            if (res && res.data) {
                ElMessage.success('删除分组成功')
                fetchGroups()
            }
        } catch (e) {
            // 用户取消，无需处理
        }
    }
}

async function handleRenameGroup(group) {
    if (!renameGroupName.value.trim()) {
        ElMessage.warning('请输入分组名称')
        return
    }
    const res = await renameGroup({ id: group.id, name: renameGroupName.value })
    if (res && res.data) {
    ElMessage.success('重命名成功')
    group.renameVisible = false
        fetchGroups()
    }
}

function handleFilterChange() {
    debouncedFetchImages()
}

function handleSearchClear() {
    filterName.value = ''
    debouncedFetchImages()
}

function handleUploadImage() {
    uploadInput.value && uploadInput.value.click()
}

async function onUploadFileChange(e) {
    const files = e.target.files
    if (!files || files.length === 0) return
    
    const fileArray = Array.from(files)
    
    try {
        if (fileArray.length === 1) {
            // 单文件上传
            ElMessage.info('开始上传文件...')
            const file = fileArray[0]
            
            // 使用修复后的API
            const res = await uploadFile(
                file, 
                activeGroup.value !== 'all' && activeGroup.value !== 'ungroup' ? activeGroup.value : undefined,
                file.name
            )
            
            ElMessage.success('上传成功')
            fetchImages()
        } else {
            // 批量上传
            ElMessage.info(`开始批量上传 ${fileArray.length} 个文件...`)
            
            // 使用修复后的API
            const res = await batchUploadFile(
                fileArray,
                activeGroup.value !== 'all' && activeGroup.value !== 'ungroup' ? activeGroup.value : undefined
            )
            
            ElMessage.success(`成功上传 ${res.data.length} 个文件`)
            fetchImages()
        }
    } catch (error) {
        ElMessage.error(error.message || '上传失败')
        console.error('上传错误:', error)
    } finally {
        // 清空文件选择
        e.target.value = ''
    }
}
</script>

<style scoped>
.image-center-container {
    display: flex;
    height: calc(100vh - 90px); /* 减去头部和边距的高度 */
    padding: 20px;
    background-color: var(--el-bg-color-base);
    min-height: 500px; /* 设置最小高度 */
    overflow: hidden; /* 防止整体溢出 */
}

.group-list {
    width: 200px;
    height: 100%;
    border-right: 1px solid var(--el-border-color-base);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    background-color: var(--el-color-white);
    padding: 10px;
    overflow: hidden; /* 防止内容溢出 */
}

.group-menu {
    width: 100%;
    border: none;
    background: transparent;
    flex: 1 1 auto;
    overflow-y: auto; /* 添加垂直滚动 */
}

.add-group-btn {
    margin-top: 20px;
    width: 90%;
    margin-bottom: 20px;
}

.image-list-wrapper {
    flex: 1;
    padding: 16px; /* 减小内边距 */
    display: flex;
    flex-direction: column;
    background-color: var(--el-color-white);
    overflow: hidden;
    height: 100%;
}

.image-list {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.image-list-toolbar {
    display: flex;
    align-items: center;
    margin-bottom: 8px; /* 减小底部边距 */
}

.images {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
    overflow-y: auto;
    padding-right: 8px;
}

.image-item {
    width: 98px;
    display: flex;
    flex-direction: column;
    align-items: center;
    /* background: var(--el-bg-color-base); */
    position: relative;
    margin-bottom: 8px; /* 减小底部边距 */
}

.image-thumb-wrapper {
    position: relative;
    border-radius: 6px; /* 减小圆角 */
    border: 1px solid var(--el-border-color-base);
    width: 98px;
    height: 98px;
    cursor: pointer;
    overflow: hidden;
    background: #f5f7fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-thumb {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 4px;
    display: block;
    background: #f5f7fa;
}

.image-mask {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.check-icon {
    width: 40px;
    height: 40px;
    display: block;
}

.image-name {
    font-size: 12px; /* 减小字体大小 */
    margin-top: 3px; /* 减小上边距 */
    text-align: center;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--el-text-color-regular);
    line-height: 1.2; /* 减小行高 */
}

.image-actions {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 28px; /* 增加按钮高度 */
    background: rgba(0, 0, 0, 0.6); /* 更深的背景色，提高对比度 */
    display: flex;
    align-items: center;
    justify-content: space-around; /* 平均分布空间 */
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
}

.image-actions.show {
    opacity: 1;
    pointer-events: auto;
}

/* 添加图标样式 */
.image-actions .el-link {
    height: 28px;
    color: white !important; /* 白色文字，提高可读性 */
    font-size: 14px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    border-radius: 0;
    flex: 1;
    transition: all 0.2s;
}

.image-actions .el-link:hover {
    transform: scale(1.15);
    background-color: rgba(255, 255, 255, 0.2); /* 悬停时背景微亮 */
}

.image-actions .el-icon {
    font-size: 16px;
}

/* 为不同类型的操作按钮设置不同悬停色 */
.image-actions .el-link[type="primary"]:hover {
    color: var(--el-color-primary) !important;
}

.image-actions .el-link[type="danger"]:hover {
    color: var(--el-color-danger) !important;
}

.pagination-bar {
    margin-top: 12px; /* 减小顶部边距 */
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0; /* 防止分页器被压缩 */
}

.pagination-actions {
    display: flex;
    align-items: center;
    gap: 8px; /* 减小间距 */
}

.image-preview-dialog .el-dialog__body {
    padding: 0;
    background: transparent;
}

.group-item-content {
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;
    position: relative;
}

.group-name-actions {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
}

.group-name {
    margin-left: 8px;
    margin-right: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
    font-size: 14px;
    color: var(--el-text-color-regular);
    transition: color 0.2s;
}

.group-item-content.active .group-name {
    color: var(--el-color-primary);
    font-weight: bold;
}

.group-more {
    font-size: 10px;
    color: var(--el-text-color-placeholder);
    width: 28px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    border-radius: 4px;
    transition: background 0.2s;
    cursor: pointer;
    margin-left: auto;
}

.group-more:hover {
    background: var(--el-bg-color-base);
}

.group-item-content.active .group-more {
    color: var(--el-color-primary);
}

.group-popover {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
}

.group-popover-menu {
    padding: 4px 0;
    min-height: 60px;
    max-height: 120px;
    overflow-y: auto;
    background: var(--el-color-white);
}

.group-popover-item {
    padding: 10px 16px;
    cursor: pointer;
    font-size: 12px;
    transition: background 0.2s;
    color: var(--el-text-color-regular);
}

.group-popover-item:hover {
    background: var(--el-bg-color-base);
}

.group-popover-item.danger {
    color: var(--el-color-danger);
}

.group-popover-item.danger:hover {
    background: var(--el-color-danger-light);
}

::v-deep(.group-action-popover) {
    padding: 0 !important;
    width: 80px !important;
    min-width: 80px !important;
}

::v-deep(.group-rename-popover) {
    padding: 16px !important;
    min-width: 280px !important;
}

.rename-popover-content {
    width: 100%;
}

.rename-popover-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 8px;
}

.group-item-content.active {
    background: var(--el-color-primary-light-6);
}

::v-deep(.image-rename-popover) {
    padding: 16px !important;
    min-width: 280px !important;
}

.image-name-text {
    cursor: pointer;
    font-size: 12px; /* 减小字体大小 */
    color: var(--el-text-color-regular);
    transition: color 0.2s;
    line-height: 1.2; /* 减小行高 */
}

.image-name-text:hover {
    color: var(--el-color-primary);
}

/* 滚动条样式 */
.images::-webkit-scrollbar,
.group-menu::-webkit-scrollbar {
    width: 4px; /* 减小滚动条宽度 */
    background-color: transparent;
}

.images::-webkit-scrollbar-thumb,
.group-menu::-webkit-scrollbar-thumb {
    background-color: var(--el-border-color-lighter);
    border-radius: 2px;
}

.images::-webkit-scrollbar-thumb:hover,
.group-menu::-webkit-scrollbar-thumb:hover {
    background-color: var(--el-border-color);
}

.images,
.group-menu {
    scrollbar-width: thin; /* Firefox */
    scroll-behavior: smooth; /* 平滑滚动 */
}

/* 弹出框优化 */
::v-deep(.el-popover.image-rename-popover) {
    margin-top: 10px !important;
}

::v-deep(.el-popover.group-rename-popover) {
    margin-top: 10px !important;
}
</style>
