# 商品管理系统

基于`biz_company_spu`和`biz_company_sku`表的客户商品管理系统，将原有的商品管理功能拆分为三个专门的子模块。

## 系统架构

### 数据表关系
- **biz_company_spu**: 客户商品表，维护商品的销售价格范围和状态
- **biz_company_sku**: 客户可用SKU表，维护具体的销售价格和利润率
- **biz_supplier_spu**: 供应商商品表，商品的源头
- **biz_supplier_sku**: 供应商SKU表，价格变更的源头
- **biz_price_spec_change_request**: 价格变更申请表系列

## 三个子模块

### 1. 商品管理 (companyProduct)
**路径**: `/views/goods/companyProduct/index.vue`
**API**: `/api/companyProduct.ts`
**后端**: `CompanyProductManagementController`

**功能特性**:
- 基于`biz_company_spu`表的商品列表管理
- 批量商品状态更新（上下架）
- 多种调价方式：固定金额、百分比、设置价格
- 自动更新SPU价格范围计算
- 商品统计：总数、在售/下架数、供应商分类统计
- 完整的权限控制，只能操作自己公司的商品

**主要操作**:
- 商品列表查询和筛选
- 批量状态变更
- 批量调价操作
- 商品详情查看

### 2. 商品审核 (productReview)
**路径**: `/views/goods/productReview/index.vue`
**API**: `/api/productReview.ts`
**后端**: `CompanyProductReviewController`

**功能特性**:
- 只处理客户供应商（supplier_type=1）的商品审核
- 审核通过后自动创建`biz_company_spu`和`biz_company_sku`记录
- 支持单个和批量审核操作
- 审核统计：待审核、已通过、已拒绝数量
- 完整的权限验证和状态控制

**主要操作**:
- 待审核商品列表查看
- 商品详情查看（包含SKU信息）
- 单个商品审核（通过/拒绝）
- 批量审核操作
- 审核统计查看

### 3. 价格变更审核 (priceChangeReview)
**路径**: `/views/goods/priceChangeReview/index.vue`
**API**: `/api/priceChangeReview.ts`
**后端**: `CompanyPriceChangeReviewController`

**功能特性**:
- 基于`biz_price_spec_change_request`系列表处理价格变更申请
- 审核通过后自动应用价格变更到供应商SKU
- 同步更新客户SKU价格（如果存在）
- 重新计算客户SPU价格范围
- 支持批量价格变更审核

**主要操作**:
- 价格变更申请列表查看
- 价格对比展示（原价格 vs 新价格）
- 变更幅度计算和展示
- 单个申请审核（通过/拒绝）
- 批量审核操作
- 价格变更统计

## 技术实现

### 后端技术栈
- **框架**: Spring Boot + MyBatis-Plus
- **数据库**: MySQL
- **权限控制**: 基于session用户的公司ID
- **事务管理**: 完整的事务控制和错误处理
- **数据精度**: 价格计算使用BigDecimal确保精度

### 前端技术栈
- **框架**: Vue 3 + TypeScript
- **UI组件**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router
- **HTTP客户端**: Axios

### 关键特性
1. **职责分离**: 三个模块各司其职，功能清晰
2. **批量操作**: 支持高效的批量处理
3. **完整审核流**: 从申请到审核的完整工作流
4. **权限控制**: 严格的数据权限控制
5. **数据一致性**: 确保价格数据的一致性
6. **用户体验**: 现代化的用户界面和交互

## 使用说明

### 商品管理流程
1. 供应商提报商品 → 商品审核模块
2. 审核通过 → 自动创建客户商品记录
3. 日常运营 → 商品管理模块（调价、上下架等）
4. 供应商申请价格变更 → 价格变更审核模块
5. 价格变更审核通过 → 自动更新相关价格

### 权限说明
- 所有操作基于当前登录用户的公司ID
- 只能操作自己公司的商品和申请
- 审核操作需要相应的权限角色

### 数据流转
```
供应商商品 → 商品审核 → 客户商品库 → 商品管理 → 价格变更审核 → 价格更新
```

## 接口文档

### 商品管理接口
- `GET /company-product-management/list` - 获取商品列表
- `GET /company-product-management/{id}` - 获取商品详情
- `POST /company-product-management/batch/status` - 批量更新状态
- `POST /company-product-management/batch/price` - 批量调价
- `GET /company-product-management/statistics` - 获取统计数据

### 商品审核接口
- `GET /company-product-review/list` - 获取待审核商品列表
- `GET /company-product-review/detail/{id}` - 获取商品详情
- `POST /company-product-review/review/{id}` - 审核商品
- `POST /company-product-review/batch-review` - 批量审核
- `GET /company-product-review/statistics` - 获取审核统计

### 价格变更审核接口
- `GET /company-price-change-review/list` - 获取价格变更申请列表
- `GET /company-price-change-review/detail/{id}` - 获取申请详情
- `POST /company-price-change-review/review/{id}` - 审核价格变更
- `POST /company-price-change-review/batch-review` - 批量审核
- `GET /company-price-change-review/statistics` - 获取变更统计

## 注意事项

1. **数据一致性**: 所有价格变更操作都会同步更新相关表的数据
2. **权限控制**: 严格按照公司维度进行数据隔离
3. **审核流程**: 审核操作不可逆，请谨慎操作
4. **价格精度**: 所有价格计算保留2位小数
5. **日志记录**: 重要操作都有详细的日志记录

## 后续扩展

1. **审核流程**: 可以扩展为多级审核流程
2. **通知系统**: 审核结果通知供应商
3. **报表分析**: 商品销售和价格变化分析
4. **自动化**: 基于规则的自动审核
5. **移动端**: 移动端审核应用 