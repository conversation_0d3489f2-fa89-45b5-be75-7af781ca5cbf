<template>
  <div class="product-review-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>商品审核</h2>
        <p>审核供应商提报的商品，通过后自动加入商品库</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showBatchReviewDialog">批量审核</el-button>
        <el-button @click="refreshData">刷新</el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <el-card class="stat-card" @click="filterByStatistic('pending')">
        <div class="stat-content">
          <div class="stat-number">{{ statistics.pending }}</div>
          <div class="stat-label">待审核</div>
        </div>
      </el-card>
      <el-card class="stat-card" @click="filterByStatistic('approved')">
        <div class="stat-content">
          <div class="stat-number">{{ statistics.approved }}</div>
          <div class="stat-label">已通过</div>
        </div>
      </el-card>
      <el-card class="stat-card" @click="filterByStatistic('rejected')">
        <div class="stat-content">
          <div class="stat-number">{{ statistics.rejected }}</div>
          <div class="stat-label">已拒绝</div>
        </div>
      </el-card>
      <el-card class="stat-card" @click="filterByStatistic('all')">
        <div class="stat-content">
          <div class="stat-number">{{ statistics.total }}</div>
          <div class="stat-label">总数量</div>
        </div>
      </el-card>
    </div>

    <!-- 搜索栏 -->
    <div class="header">
      <el-input 
        v-model="searchForm.name" 
        placeholder="搜索商品名称" 
        clearable 
        style="width: 200px; margin-right: 10px"
        @input="handleSearch" 
        @clear="handleSearchClear" 
      />
      <el-select 
        v-model="searchForm.supplierId" 
        placeholder="选择供应商" 
        clearable 
        style="width: 200px; margin-right: 10px"
        @change="handleSearch"
      >
        <el-option 
          v-for="supplier in supplierOptions" 
          :key="supplier.id" 
          :label="supplier.name" 
          :value="supplier.id" 
        />
      </el-select>
      <el-select 
        v-model="searchForm.status" 
        placeholder="审核状态" 
        clearable 
        style="width: 150px; margin-right: 10px"
        @change="handleSearch"
      >
        <el-option label="审核中" :value="3" />
        <el-option label="已通过" :value="1" />
        <el-option label="已拒绝" :value="4" />
      </el-select>
      <el-button @click="resetSearch">重置</el-button>
    </div>

    <!-- 商品列表 -->
    <el-table 
      height="calc(100vh - 440px)" 
      :data="productList" 
      v-loading="loading"
      style="margin-top: 16px; width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="商品信息" min-width="300">
        <template #default="{ row }">
          <div class="product-info">
            <div class="product-image">
              <el-image 
                :src="row.mainImgUrl" 
                fit="cover" 
                style="width: 60px; height: 60px; border-radius: 4px;"
              >
                <template #error>
                  <div class="image-slot">
                    <el-icon><Picture /></el-icon>
                  </div>
                </template>
              </el-image>
            </div>
            <div class="product-details">
              <div class="product-name">{{ row.name }}</div>
              <div class="product-code">编码：{{ row.spuCode }}</div>
              <div class="product-supplier">
                供应商：{{ row.supplierName }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="成本价范围" width="150" align="center">
        <template #default="{ row }">
          <div>
            <div class="price" v-if="row.costPriceMin && row.costPriceMin > 0">
              ¥{{ row.costPriceMin }}
              <span v-if="row.costPriceMax && row.costPriceMax !== row.costPriceMin">- ¥{{ row.costPriceMax }}</span>
            </div>
            <div v-else class="price-pending">待定</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="采购价范围" width="150" align="center">
        <template #default="{ row }">
          <div>
            <div class="price" v-if="row.purchasePriceMin && row.purchasePriceMin > 0">
              ¥{{ row.purchasePriceMin }}
              <span v-if="row.purchasePriceMax && row.purchasePriceMax !== row.purchasePriceMin">- ¥{{ row.purchasePriceMax }}</span>
            </div>
            <div v-else class="price-pending">待定</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="SKU数量" width="100" align="center">
        <template #default="{ row }">
          <span>{{ row.skuCount }}个</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="审核状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="商品分类" width="150" align="center">
        <template #default="{ row }">
          {{ getCategoryDisplayName(row.categoryId) }}
        </template>
      </el-table-column>
      <el-table-column label="提交时间" width="180">
        <template #default="{ row }">
          {{ formatDateTime(row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" align="center">
        <template #default="{ row }">
          <el-button link type="primary" size="small" @click="viewProduct(row)">查看</el-button>
          <el-button 
            v-if="row.status === 3" 
            link 
            type="success" 
            size="small" 
            @click="reviewProduct(row, 1)"
          >
            通过
          </el-button>
          <el-button 
            v-if="row.status === 3" 
            link 
            type="danger" 
            size="small" 
            @click="reviewProduct(row, 2)"
          >
            拒绝
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination 
        v-model:current-page="pagination.page" 
        v-model:page-size="pagination.size" 
        :total="pagination.total" 
        background
        :page-sizes="[10, 20, 50, 100]" 
        @size-change="handleSizeChange" 
        @current-change="loadProductList" 
      />
    </div>

    <!-- 审核对话框 -->
    <el-dialog v-model="reviewDialogVisible" :title="reviewDialogTitle" width="800px" :before-close="handleCloseReview">
      <div v-if="reviewForm.reviewStatus === 1 && currentReviewProduct" class="review-approve-container">
        <!-- 商品基本信息 -->
        <div class="product-info-header">
          <div class="product-image">
            <el-image :src="currentReviewProduct.mainImgUrl || '/default-product.png'" fit="cover"
              style="width: 80px; height: 80px; border-radius: 8px;" />
          </div>
          <div class="product-details">
            <div class="product-name">{{ currentReviewProduct.name }}</div>
            <div class="product-meta">
              <span class="product-code">编码：{{ currentReviewProduct.spuCode }}</span>
              <span class="product-supplier">供应商：{{ currentReviewProduct.supplierName }}</span>
            </div>
          </div>
        </div>

        <!-- SKU列表 -->
        <div class="sku-list-section">
          <h4>SKU列表 - 设置销售价格</h4>
          <el-table :data="currentReviewProduct.skuList" border size="small" height="300">
            <el-table-column label="商品信息" width="200">
              <template #default="{ row }">
                <div class="product-info-cell">
                  <div class="product-image">
                    <el-image :src="row.imgUrl || currentReviewProduct.mainImgUrl || '/default-product.png'"
                      fit="cover" style="width: 40px; height: 40px; border-radius: 4px;" />
                  </div>
                  <div class="product-details">
                    <div class="product-name">{{ currentReviewProduct.name }}</div>
                    <div class="product-supplier">{{ currentReviewProduct.supplierName }}</div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="规格" width="120">
              <template #default="{ row }">
                <div class="spec-values">
                  <template v-if="row.spec1Value || row.spec2Value || row.spec3Value">
                    <el-tag v-if="row.spec1Value" size="small" class="spec-tag">{{ row.spec1Value }}</el-tag>
                    <el-tag v-if="row.spec2Value" size="small" class="spec-tag">{{ row.spec2Value }}</el-tag>
                    <el-tag v-if="row.spec3Value" size="small" class="spec-tag">{{ row.spec3Value }}</el-tag>
                  </template>
                  <template v-else>
                    <el-tag size="small" class="spec-tag default-spec">标准规格</el-tag>
                  </template>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="成本价" width="100">
              <template #default="{ row }">
                <span class="price-text cost-price">¥{{ formatPrice(row.costPrice) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="采购价" width="100">
              <template #default="{ row }">
                <span class="price-text purchase-price">¥{{ formatPrice(row.purchasePrice) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="划线价" width="100">
              <template #default="{ row }">
                <span class="price-text strike-price">¥{{ formatPrice(row.strikethroughPrice) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="设置销售价" width="150">
              <template #default="{ row, $index }">
                <el-input-number v-model="row.salePrice" :precision="2" :step="0.01" :min="0" size="small"
                  style="width: 130px" @change="handleReviewSkuPriceChange(row, $index)" />
              </template>
            </el-table-column>
            <el-table-column label="毛利" width="100">
              <template #default="{ row }">
                <span class="price-text profit" :class="getReviewProfitClass(row)">
                  ¥{{ formatPrice(calculateProfit(row.salePrice, row.costPrice)) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="毛利率" width="100">
              <template #default="{ row }">
                <span class="price-text profit-rate" :class="getReviewProfitRateClass(row)">
                  {{ formatProfitRate(calculateProfitRate(row.salePrice, row.costPrice)) }}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 价格设置公式 -->
        <div class="price-formula-section">
          <h4>价格设置公式</h4>
          <el-form :model="reviewPriceForm" label-width="100px">
            <el-form-item label="价格基准">
              <el-radio-group v-model="reviewPriceForm.priceBase" @change="reviewErrors.reviewRemark = ''">
                <el-radio label="costPrice">成本价</el-radio>
                <!-- <el-radio label="purchasePrice">采购价</el-radio> -->
                <el-radio label="strikethroughPrice">划线价</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="定价公式">
              <div class="formula-container">
                <span class="formula-text">销售价格 = </span>
                <span class="formula-base">{{ getReviewPriceBaseName() }}</span>
                <span class="formula-text"> × </span>
                <el-input-number v-model="reviewPriceForm.multiplier" :precision="2" :step="0.01" :min="0" size="small"
                  style="width: 120px" @change="reviewErrors.reviewRemark = ''" />
                <span class="formula-text"> + </span>
                <el-input-number v-model="reviewPriceForm.adjustment" :precision="2" :step="0.01" size="small"
                  style="width: 120px" @change="reviewErrors.reviewRemark = ''" />
                <span class="formula-text"> 元</span>
                <el-button type="primary" size="small" @click="applyReviewPriceFormula" style="margin-left: 15px">
                  应用到所有SKU
                </el-button>
              </div>
              <div class="formula-tip">
                <el-text type="info" size="small">
                  💡 设置好定价公式后，点击"应用到所有SKU"按钮批量设置价格，或在表格中手动编辑单个SKU的价格
                </el-text>
              </div>
            </el-form-item>
            <el-form-item label="审核意见" required>
              <el-input v-model="reviewForm.reviewRemark" type="textarea" :rows="3" 
                placeholder="请输入审核通过的意见（必填）"
                :class="{ 'is-error': reviewErrors.reviewRemark }" 
                @input="reviewErrors.reviewRemark = ''" />
              <div v-if="reviewErrors.reviewRemark" class="error-message">
                {{ reviewErrors.reviewRemark }}
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 拒绝审核的简单表单 -->
      <div v-else-if="reviewForm.reviewStatus === 2">
        <el-form :model="reviewForm" label-width="100px">
          <el-form-item label="审核结果">
            <el-tag type="danger">拒绝</el-tag>
          </el-form-item>
          <el-form-item label="审核意见" required>
            <el-input v-model="reviewForm.reviewRemark" type="textarea" :rows="4" 
              placeholder="请输入拒绝原因（必填）"
              :class="{ 'is-error': reviewErrors.reviewRemark }" 
              @input="reviewErrors.reviewRemark = ''" />
            <div v-if="reviewErrors.reviewRemark" class="error-message">
              {{ reviewErrors.reviewRemark }}
            </div>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="reviewDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmReview" :loading="reviewLoading">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量审核对话框 -->
    <el-dialog v-model="batchDialog.visible" :title="batchDialogTitle" width="800px" :before-close="handleCloseBatchReview">
      <!-- 批量拒绝的简单表单 -->
      <div v-if="batchDialog.form.reviewResult === 4">
        <el-form :model="batchDialog.form" label-width="100px">
          <el-form-item label="审核结果">
            <el-tag type="danger">拒绝</el-tag>
          </el-form-item>
          <el-form-item label="审核意见" required>
            <el-input v-model="batchDialog.form.reviewRemark" type="textarea" :rows="4" 
              placeholder="请输入拒绝原因（必填）"
              :class="{ 'is-error': batchReviewErrors.reviewRemark }" 
              @input="batchReviewErrors.reviewRemark = ''" />
            <div v-if="batchReviewErrors.reviewRemark" class="error-message">
              {{ batchReviewErrors.reviewRemark }}
            </div>
          </el-form-item>
        </el-form>

        <div class="selected-products">
          <p>已选择 {{ selectedProducts.length }} 个商品</p>
          <div class="product-list">
            <div v-for="product in selectedProducts" :key="product.id" class="product-item">
              <span>{{ product.name }}</span>
              <span>{{ product.spuCode }}</span>
              <el-tag :type="getStatusType(product.status)" size="small">
                {{ getStatusText(product.status) }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 批量审核通过的详细表单 -->
      <div v-else-if="batchDialog.form.reviewResult === 1 && batchReviewProducts.length > 0" class="batch-review-approve-container">
        <!-- 商品概览 -->
        <div class="batch-product-overview">
          <h4>批量审核通过 - {{ selectedProducts.length }}个商品</h4>
          <div class="overview-stats">
            <span class="stat-item">总SKU数: {{ batchReviewProducts.reduce((sum, p) => sum + (p.skuList?.length || 0), 0) }}个</span>
            <span class="stat-item">涉及供应商: {{ getUniqueSupplierCount() }}个</span>
          </div>
        </div>

        <!-- SKU列表 -->
        <div class="sku-list-section">
          <h4>SKU列表 - 设置销售价格</h4>
          <el-table :data="allBatchSkuList" border size="small" height="350">
            <el-table-column label="商品信息" width="200">
              <template #default="{ row }">
                <div class="product-info-cell">
                  <div class="product-image">
                    <el-image :src="row.mainImageUrl || '/default-product.png'"
                      fit="cover" style="width: 40px; height: 40px; border-radius: 4px;" />
                  </div>
                  <div class="product-details">
                    <div class="product-name">{{ row.productName }}</div>
                    <div class="product-supplier">{{ row.supplierName }}</div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="规格" width="120">
              <template #default="{ row }">
                <div class="spec-values">
                  <template v-if="row.spec1Value || row.spec2Value || row.spec3Value">
                    <el-tag v-if="row.spec1Value" size="small" class="spec-tag">{{ row.spec1Value }}</el-tag>
                    <el-tag v-if="row.spec2Value" size="small" class="spec-tag">{{ row.spec2Value }}</el-tag>
                    <el-tag v-if="row.spec3Value" size="small" class="spec-tag">{{ row.spec3Value }}</el-tag>
                  </template>
                  <template v-else>
                    <el-tag size="small" class="spec-tag default-spec">标准规格</el-tag>
                  </template>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="成本价" width="100">
              <template #default="{ row }">
                <span class="price-text cost-price">¥{{ formatPrice(row.costPrice) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="采购价" width="100">
              <template #default="{ row }">
                <span class="price-text purchase-price">¥{{ formatPrice(row.purchasePrice) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="划线价" width="100">
              <template #default="{ row }">
                <span class="price-text strike-price">¥{{ formatPrice(row.strikethroughPrice) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="设置销售价" width="150">
              <template #default="{ row, $index }">
                <el-input-number v-model="row.salePrice" :precision="2" :step="0.01" :min="0" size="small"
                  style="width: 130px" @change="handleBatchSkuPriceChange(row, $index)" />
              </template>
            </el-table-column>
            <el-table-column label="毛利" width="100">
              <template #default="{ row }">
                <span class="price-text profit" :class="getBatchProfitClass(row)">
                  ¥{{ formatPrice(calculateProfit(row.salePrice, row.costPrice)) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="毛利率" width="100">
              <template #default="{ row }">
                <span class="price-text profit-rate" :class="getBatchProfitRateClass(row)">
                  {{ formatProfitRate(calculateProfitRate(row.salePrice, row.costPrice)) }}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 价格设置公式 -->
        <div class="price-formula-section">
          <h4>价格设置公式</h4>
          <el-form :model="batchPriceForm" label-width="100px">
            <el-form-item label="价格基准">
              <el-radio-group v-model="batchPriceForm.priceBase" @change="batchReviewErrors.reviewRemark = ''">
                <el-radio label="costPrice">成本价</el-radio>
                <!-- <el-radio label="purchasePrice">采购价</el-radio> -->
                <el-radio label="strikethroughPrice">划线价</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="定价公式">
              <div class="formula-container">
                <span class="formula-text">销售价格 = </span>
                <span class="formula-base">{{ getBatchPriceBaseName() }}</span>
                <span class="formula-text"> × </span>
                <el-input-number v-model="batchPriceForm.multiplier" :precision="2" :step="0.01" :min="0" size="small"
                  style="width: 120px" @change="batchReviewErrors.reviewRemark = ''" />
                <span class="formula-text"> + </span>
                <el-input-number v-model="batchPriceForm.adjustment" :precision="2" :step="0.01" size="small"
                  style="width: 120px" @change="batchReviewErrors.reviewRemark = ''" />
                <span class="formula-text"> 元</span>
                <el-button type="primary" size="small" @click="applyBatchPriceFormula" style="margin-left: 15px">
                  应用到所有SKU
                </el-button>
              </div>
              <div class="formula-tip">
                <el-text type="info" size="small">
                  💡 设置好定价公式后，点击"应用到所有SKU"按钮批量设置价格，或在表格中手动编辑单个SKU的价格
                </el-text>
              </div>
            </el-form-item>
            <el-form-item label="审核意见" required>
              <el-input v-model="batchDialog.form.reviewRemark" type="textarea" :rows="3" 
                placeholder="请输入审核通过的意见（必填）"
                :class="{ 'is-error': batchReviewErrors.reviewRemark }" 
                @input="batchReviewErrors.reviewRemark = ''" />
              <div v-if="batchReviewErrors.reviewRemark" class="error-message">
                {{ batchReviewErrors.reviewRemark }}
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 审核结果选择（初始状态） -->
      <div v-else>
        <el-form :model="batchDialog.form" label-width="100px">
          <el-form-item label="审核结果">
            <el-radio-group v-model="batchDialog.form.reviewResult" @change="handleBatchReviewResultChange">
              <el-radio :label="1">通过</el-radio>
              <el-radio :label="4">拒绝</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>

        <div class="selected-products">
          <p>已选择 {{ selectedProducts.length }} 个商品</p>
          <div class="product-list">
            <div v-for="product in selectedProducts" :key="product.id" class="product-item">
              <span>{{ product.name }}</span>
              <span>{{ product.spuCode }}</span>
              <el-tag :type="getStatusType(product.status)" size="small">
                {{ getStatusText(product.status) }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchDialog.visible = false">取消</el-button>
          <el-button v-if="batchDialog.form.reviewResult === 0" type="primary" disabled>
            请先选择审核结果
          </el-button>
          <el-button v-else type="primary" @click="handleBatchReview" :loading="batchDialog.loading">
            确认审核
          </el-button>
        </div>
      </template>
    </el-dialog>



    <!-- 商品详情抽屉 -->
    <el-drawer v-model="detailDialog.visible" title="商品详情" size="80%" :before-close="handleCloseProductDetail">
      <div v-if="detailDialog.product" class="product-detail-container">
        <!-- 商品基本信息头部 -->
        <div class="product-header">
          <div class="product-main-info">
            <div class="product-image">
              <el-image :src="detailDialog.product.mainImgUrl || '/default-product.png'"
                :preview-src-list="productImages" fit="cover"
                style="width: 120px; height: 120px; border-radius: 8px;" />
            </div>
            <div class="product-basic">
              <h2 class="product-title">{{ detailDialog.product.name }}</h2>
              <div class="product-meta">
                <span class="product-id">商品ID: {{ detailDialog.product.id }}</span>
                <el-tag :type="getStatusType(detailDialog.product.status)" class="status-tag">
                  {{ getStatusText(detailDialog.product.status) }}
                </el-tag>
                <el-tag v-if="detailDialog.product.type === 1" type="info" class="type-tag">实物商品</el-tag>
                <el-tag v-else type="warning" class="type-tag">虚拟商品</el-tag>
              </div>
            </div>
          </div>
          <div class="product-price-info">
            <div class="price-item">
              <span class="price-label">成本价</span>
              <span class="price-value cost-price">
                {{ detailDialog.product.costPriceMin && detailDialog.product.costPriceMax 
                    ? (detailDialog.product.costPriceMin === detailDialog.product.costPriceMax 
                       ? `¥${formatPrice(detailDialog.product.costPriceMin)}` 
                       : `¥${formatPrice(detailDialog.product.costPriceMin)}-${formatPrice(detailDialog.product.costPriceMax)}`)
                    : '¥0.00' }}
              </span>
            </div>
            <div class="price-item">
              <span class="price-label">划线价</span>
              <span class="price-value strike-price">
                {{ getMaxStrikethroughPrice() ? `¥${formatPrice(getMaxStrikethroughPrice())}` : '¥0.00' }}
              </span>
            </div>
            <div class="price-item">
              <span class="price-label">采购价</span>
              <span class="price-value purchase-price">
                {{ detailDialog.product.purchasePriceMin && detailDialog.product.purchasePriceMax 
                    ? (detailDialog.product.purchasePriceMin === detailDialog.product.purchasePriceMax 
                       ? `¥${formatPrice(detailDialog.product.purchasePriceMin)}` 
                       : `¥${formatPrice(detailDialog.product.purchasePriceMin)}-${formatPrice(detailDialog.product.purchasePriceMax)}`)
                    : '¥0.00' }}
              </span>
            </div>
            <div class="stats-item">
              <span class="stats-label">销量</span>
              <span class="stats-value">{{ detailDialog.product.salesCount || 0 }}</span>
            </div>
            <div class="stats-item">
              <span class="stats-label">库存</span>
              <span class="stats-value">{{ getTotalStock() }}</span>
            </div>
          </div>
        </div>

        <!-- Tab切换区域 -->
        <el-tabs v-model="activeDetailTab" class="detail-tabs">
          <!-- 基本信息Tab -->
          <el-tab-pane label="基本信息" name="basic">
            <div class="basic-info-content">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="商品分类">{{ getCategoryDisplayName(detailDialog.product.categoryId) }}</el-descriptions-item>
                <el-descriptions-item label="条形码">{{ detailDialog.product.barcode || '无' }}</el-descriptions-item>
                <el-descriptions-item label="商品单位">{{ detailDialog.product.unit || '件' }}</el-descriptions-item>
                <el-descriptions-item label="商品类型">
                  {{ detailDialog.product.type === 1 ? '实物商品' : '虚拟商品' }}
                </el-descriptions-item>
                <el-descriptions-item label="是否推荐">
                  <el-tag :type="detailDialog.product.isRecommend ? 'success' : 'info'">
                    {{ detailDialog.product.isRecommend ? '是' : '否' }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="创建时间">{{ formatDateTime(detailDialog.product.createTime) }}</el-descriptions-item>
              </el-descriptions>

              <!-- 物理属性 -->
              <div class="physical-attrs" v-if="hasPhysicalAttrs">
                <h3>物理属性</h3>
                <el-descriptions :column="3" border>
                  <el-descriptions-item label="长度" v-if="detailDialog.product.length">{{ detailDialog.product.length }}cm</el-descriptions-item>
                  <el-descriptions-item label="宽度" v-if="detailDialog.product.wide">{{ detailDialog.product.wide }}cm</el-descriptions-item>
                  <el-descriptions-item label="高度" v-if="detailDialog.product.tall">{{ detailDialog.product.tall }}cm</el-descriptions-item>
                  <el-descriptions-item label="体积" v-if="detailDialog.product.volume">{{ detailDialog.product.volume }}cm³</el-descriptions-item>
                  <el-descriptions-item label="重量" v-if="detailDialog.product.weight">{{ detailDialog.product.weight }}kg</el-descriptions-item>
                </el-descriptions>
              </div>

              <!-- 物流设置 -->
              <div class="shipping-settings">
                <h3>物流设置</h3>
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="运费类型">
                    {{ detailDialog.product.shippingType === 'unified' ? '统一运费' : '运费模板' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="运费金额" v-if="detailDialog.product.shippingType === 'unified'">
                    ¥{{ detailDialog.product.shippingFee || '0.00' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="运费模板" v-if="detailDialog.product.shippingType === 'template'">
                    {{ getShippingTemplateName(detailDialog.product.shippingTemplateId) }}
                  </el-descriptions-item>
                  <el-descriptions-item label="售后服务">
                    {{ detailDialog.product.afterSaleType === 'no_return' ? '不可退换' : '支持退换' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="支持服务" v-if="detailDialog.product.afterSaleType === 'support_return'">
                    {{ formatAfterSaleServices(detailDialog.product.afterSaleServices) }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
              <!-- 商品图片 -->
              <div class="product-images-section" v-if="detailDialog.product.images && detailDialog.product.images.length > 0">
                <h3>商品图片</h3>
                <div class="images-gallery">
                  <div v-for="(image, index) in detailDialog.product.images" :key="index" class="image-item">
                    <el-image :src="image.imgUrl" :preview-src-list="productImages" fit="cover"
                      style="width: 100px; height: 100px; border-radius: 4px;" />
                    <el-tag v-if="image.isMain" type="success" size="small" class="main-tag">主图</el-tag>
                  </div>
                </div>
              </div>

              <!-- 商品描述 -->
              <div class="product-description" v-if="detailDialog.product.description">
                <h3>商品描述</h3>
                <div class="description-content" v-html="detailDialog.product.description"></div>
              </div>

              <!-- 商品特点 -->
              <div class="product-features" v-if="detailDialog.product.features">
                <h3>商品特点</h3>
                <div class="features-content">{{ detailDialog.product.features }}</div>
              </div>

              <!-- 使用说明 -->
              <div class="product-instructions" v-if="detailDialog.product.instructions">
                <h3>使用说明</h3>
                <div class="instructions-content">{{ detailDialog.product.instructions }}</div>
              </div>
            </div>
          </el-tab-pane>

          <!-- 规格库存Tab -->
          <el-tab-pane label="规格库存" name="spec">
            <div class="spec-stock-content">
              <div v-if="detailDialog.product.skuList && detailDialog.product.skuList.length > 0">
                <h3>SKU列表</h3>
                <el-table :data="detailDialog.product.skuList" border style="position: relative; z-index: 1;">
                  <!-- <el-table-column prop="skuCode" label="SKU编码" width="150" /> -->
                  <el-table-column label="规格" width="200">
                    <template #default="{ row }">
                      <div class="spec-values">
                        <template v-if="row.spec1Value || row.spec2Value || row.spec3Value">
                          <el-tag v-if="row.spec1Value" size="small" class="spec-tag">{{ row.spec1Value }}</el-tag>
                          <el-tag v-if="row.spec2Value" size="small" class="spec-tag">{{ row.spec2Value }}</el-tag>
                          <el-tag v-if="row.spec3Value" size="small" class="spec-tag">{{ row.spec3Value }}</el-tag>
                        </template>
                        <template v-else>
                          <el-tag size="small" class="spec-tag default-spec">标准规格</el-tag>
                        </template>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="成本价" width="100">
                    <template #default="{ row }">
                      <span class="price-text cost-price">¥{{ formatPrice(row.costPrice) }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="采购价" width="100">
                    <template #default="{ row }">
                      <span class="price-text purchase-price">¥{{ formatPrice(row.purchasePrice) }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="划线价" width="100">
                    <template #default="{ row }">
                      <span class="price-text strike-price">¥{{ formatPrice(row.strikethroughPrice) }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="库存信息" width="150">
                    <template #default="{ row }">
                      <div class="stock-info">
                        <div>总库存: {{ row.totalInventory || 0 }}</div>
                        <div>可用: {{ row.availableInventory || 0 }}</div>
                        <div>锁定: {{ row.blockedInventory || 0 }}</div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="安全库存" width="100">
                    <template #default="{ row }">
                      {{ row.safetyStock || 0 }}
                    </template>
                  </el-table-column>
                  <el-table-column label="SKU图片" width="100">
                    <template #default="{ row }">
                      <el-image v-if="row.imgUrl" :src="row.imgUrl" :preview-src-list="[row.imgUrl]" fit="cover"
                        style="width: 50px; height: 50px; border-radius: 4px;" :z-index="9999" preview-teleported />
                      <span v-else class="no-image">无图片</span>
                    </template>
                  </el-table-column>
                </el-table>

                <!-- 规格汇总信息 -->
                <div class="spec-summary">
                  <h4>规格汇总</h4>
                  <el-descriptions :column="3" border>
                    <el-descriptions-item label="SKU总数">{{ detailDialog.product.skuList.length }}</el-descriptions-item>
                    <el-descriptions-item label="成本价区间">
                      ¥{{ formatPrice(detailDialog.product.costPriceMin) }} - ¥{{ formatPrice(detailDialog.product.costPriceMax) }}
                    </el-descriptions-item>
                    <el-descriptions-item label="采购价区间">
                      ¥{{ formatPrice(detailDialog.product.purchasePriceMin) }} - ¥{{ formatPrice(detailDialog.product.purchasePriceMax) }}
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
              </div>
              <div v-else class="no-data">
                <el-empty description="暂无SKU数据" />
              </div>
            </div>
          </el-tab-pane>

          <!-- 小程序预览Tab -->
          <el-tab-pane label="小程序预览" name="detail">
            <div class="preview-only-container">
              <!-- 小程序预览 -->
              <div class="centered-phone-preview">
                <div class="preview-title">
                  <h3>小程序预览效果</h3>
                  <p>展示商品在小程序中的显示效果</p>
                </div>

                <div class="phone-container">
                  <!-- iPhone头部 -->
                  <div class="phone-header">
                    <div class="status-bar">
                      <div class="left-status">
                        <span class="signal-bars">
                          <span class="bar"></span>
                          <span class="bar"></span>
                          <span class="bar"></span>
                          <span class="bar"></span>
                        </span>
                        <span class="carrier">Sketch</span>
                        <span class="wifi-icon">📶</span>
                      </div>
                      <div class="center-time">1:21 AM</div>
                      <div class="right-status">
                        <span class="battery-percent">100%</span>
                        <span class="battery-icon">🔋</span>
                      </div>
                    </div>
                  </div>

                  <!-- 手机屏幕内容 -->
                  <div class="phone-screen">
                    <!-- 搜索栏 -->
                    <div class="search-header">
                      <div class="search-bar">
                        <span class="search-icon">🔍</span>
                        <span class="search-placeholder">请输入关键字搜索</span>
                      </div>
                    </div>

                    <!-- 商品信息区域 -->
                    <div class="product-info-area">
                      <!-- 商品轮播图 -->
                      <div class="product-carousel">
                        <div class="carousel-container">
                          <img 
                            v-if="productImages.length > 0" 
                            :src="productImages[0]"
                            :alt="detailDialog.product?.name || '商品图片'" 
                            class="product-main-image"
                            @error="handleImageError" 
                          />
                          <img 
                            v-else 
                            src="https://via.placeholder.com/375x300/f0f0f0/999?text=商品图片" 
                            alt="默认商品图片"
                            class="product-main-image" 
                          />
                          <!-- 轮播指示器 -->
                          <div v-if="productImages.length > 1" class="carousel-indicators">
                            <span 
                              v-for="(img, index) in productImages" 
                              :key="index" 
                              class="indicator"
                              :class="{ active: index === 0 }"
                            ></span>
                          </div>
                        </div>
                      </div>

                      <!-- 商品基本信息 -->
                      <div class="product-info-section">
                        <div class="product-price">
                          <span class="current-price">
                            ¥{{ formatPrice(getDetailPreviewPrice('purchase') || 99.00) }}
                          </span>
                          <span 
                            v-if="getDetailPreviewPrice('strikethrough') && getDetailPreviewPrice('strikethrough') > 0"
                            class="original-price"
                          >
                            ¥{{ formatPrice(getDetailPreviewPrice('strikethrough')) }}
                          </span>
                          <span v-else class="original-price">
                            ¥{{ formatPrice(199.00) }}
                          </span>
                        </div>
                        <div class="product-title">
                          {{ detailDialog.product?.name || '商品名称示例' }}
                        </div>

                        <!-- 库存显示 -->
                        <div class="product-stock">
                          库存：{{ getTotalStock() }}件
                        </div>

                        <!-- 多规格选择 -->
                        <div v-if="getDetailPreviewSpecs().length > 0" class="spec-selection">
                          <div v-for="spec in getDetailPreviewSpecs()" :key="spec.name" class="spec-group">
                            <div class="spec-label">{{ spec.name }}</div>
                            <div class="spec-options">
                              <div 
                                                                 v-for="value in spec.values" 
                                 :key="String(value)" 
                                 class="spec-option"
                                 :class="{ active: previewSpecSelections[spec.name] === value }"
                                 @click="handleDetailPreviewSpecChange(spec.name, String(value))"
                              >
                                {{ value }}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 商品详情内容 -->
                      <div class="product-detail-content">
                        <!-- 商品详情富文本 -->
                        <div class="detail-section">
                          <h4>商品详情</h4>
                          <div class="detail-html" v-if="detailDialog.product?.description">
                            <div v-html="detailDialog.product.description" class="rich-content"></div>
                          </div>
                          <div v-else class="detail-placeholder">
                            <p>这里展示商品的详细信息，包括产品特性、材质说明、尺寸规格等详细内容。</p>
                            <p>商品采用优质材料制作，工艺精良，品质保证。适用于各种场合，是您的理想选择。</p>
                            <p>产品经过严格质量检测，确保每一件商品都符合高标准要求。</p>
                          </div>
                        </div>

                        <!-- 商品参数 -->
                        <div class="detail-section">
                          <h4>商品参数</h4>
                          <div class="param-list">
                            <div class="param-item" v-if="detailDialog.product?.unit">
                              <span class="param-label">商品单位：</span>
                              <span class="param-value">{{ detailDialog.product.unit }}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- 审核记录Tab -->
          <el-tab-pane label="审核记录" name="review" v-if="detailDialog.product.reviewDescribe">
            <div class="review-record-content">
              <div class="review-record">
                <h3>审核信息</h3>
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="审核状态">
                    <el-tag :type="getStatusType(detailDialog.product.status)">
                      {{ getStatusText(detailDialog.product.status) }}
                    </el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="审核时间">
                    {{ formatDateTime(detailDialog.product.updateTime) }}
                  </el-descriptions-item>
                  <el-descriptions-item label="审核备注" span="2">
                    <div class="review-remark">{{ detailDialog.product.reviewDescribe }}</div>
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>

        <!-- 底部操作按钮 -->
        <div class="detail-footer" v-if="detailDialog.product?.status === 3">
          <el-button type="success" @click="reviewProduct(detailDialog.product, 1)">
            通过审核
          </el-button>
          <el-button type="danger" @click="reviewProduct(detailDialog.product, 2)">
            拒绝审核
          </el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Picture } from '@element-plus/icons-vue'
import * as productReviewApi from '@/api/productReview'
import { getStandardCategories, buildCategoryPath, type StandardCategory } from '@/api/category'

// 格式化时间函数
const formatDateTime = (date: string) => {
  if (!date) return ''
  return new Date(date).toLocaleString()
}

// 响应式数据
const loading = ref(false)
const productList = ref<any[]>([])
const selectedProducts = ref<any[]>([])
const supplierOptions = ref<any[]>([])
const statistics = ref<any>({})

// 搜索表单
const searchForm = reactive({
  name: '',
  supplierId: null as number | null,
  status: 3 as number | null // 默认显示审核中
})

// 防抖定时器
let searchTimer: number | null = null

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 批量审核对话框
const batchDialog = reactive({
  visible: false,
  loading: false,
  form: {
    reviewResult: 0, // 0-未选择, 1-通过, 4-拒绝
    reviewRemark: ''
  }
})

// 批量审核相关变量
const batchDialogTitle = computed(() => {
  if (batchDialog.form.reviewResult === 1) return '批量审核通过'
  if (batchDialog.form.reviewResult === 4) return '批量审核拒绝'
  return '批量审核'
})

const batchReviewProducts = ref<any[]>([])
const allBatchSkuList = ref<any[]>([])

// 批量审核错误状态
const batchReviewErrors = reactive({
  reviewRemark: ''
})

// 批量审核价格表单
const batchPriceForm = reactive({
  priceBase: 'costPrice', // 价格基准：costPrice 成本价, purchasePrice 采购价, strikethroughPrice 划线价
  multiplier: 1, // 倍数，默认1
  adjustment: 0 // 调整金额
})



// 详情对话框
const detailDialog = reactive({
  visible: false,
  product: null as any
})

// Tab状态
const activeDetailTab = ref('basic')

// 审核对话框相关
const reviewDialogVisible = ref(false)
const reviewDialogTitle = ref('商品审核')
const reviewLoading = ref(false)
const reviewForm = reactive({
  id: 0,
  reviewStatus: 1,
  reviewRemark: ''
})

// 添加审核相关的新变量
const currentReviewProduct = ref<any>(null)
const reviewPriceForm = reactive({
  priceBase: 'costPrice', // 价格基准：costPrice 成本价, purchasePrice 采购价, strikethroughPrice 划线价
  multiplier: 1, // 倍数，默认1.2倍
  adjustment: 0 // 调整金额
})

// 表单验证错误状态
const reviewErrors = reactive({
  reviewRemark: ''
})

// 分类相关数据
const categoryTree = ref<StandardCategory[]>([])
const categoryPathMap = ref(new Map<number, string>()) // 存储分类ID到路径的映射

// 初始化
onMounted(() => {
  loadProductList()
  loadStatistics()
  loadSupplierOptions()
  loadCategoryTree()
})

// 加载商品列表
const loadProductList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm
    }
    const response: any = await productReviewApi.getReviewProductList(params)
    productList.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('加载商品列表失败')
  } finally {
    loading.value = false
  }
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    const response: any = await productReviewApi.getReviewStatistics()
    statistics.value = response.data
    statistics.value.total = statistics.value.approved + statistics.value.rejected + statistics.value.pending
  } catch (error) {
    console.error('加载统计数据失败', error)
  }
}

// 加载供应商选项
const loadSupplierOptions = async () => {
  try {
    const response: any = await productReviewApi.getSupplierOptions()
    supplierOptions.value = response.data.records || []
  } catch (error) {
    console.error('加载供应商选项失败', error)
  }
}

// 获取状态类型
const getStatusType = (status: number) => {
  const statusMap: { [key: number]: string } = {
    0: 'info',     // 已下架
    1: 'success',  // 销售中
    2: 'warning',  // 草稿
    3: 'primary',  // 审核中
    4: 'danger'    // 审核拒绝
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: number) => {
  const statusMap: { [key: number]: string } = {
    0: '已下架',
    1: '销售中',
    2: '草稿',
    3: '审核中',
    4: '审核拒绝'
  }
  return statusMap[status] || '未知'
}

// 获取审核状态类型
const getReviewStatusType = (status: number) => {
  const statusMap: { [key: number]: string } = {
    0: 'warning',  // 待审核
    1: 'success',  // 已通过
    2: 'danger'    // 已拒绝
  }
  return statusMap[status] || 'info'
}

// 获取审核状态文本
const getReviewStatusText = (status: number) => {
  const statusMap: { [key: number]: string } = {
    0: '待审核',
    1: '已通过',
    2: '已拒绝'
  }
  return statusMap[status] || '未知'
}

// 处理搜索清空
const handleSearchClear = () => {
  resetSearch()
}

// 重置搜索
const resetSearch = () => {
  searchForm.name = ''
  searchForm.supplierId = null
  searchForm.status = 3
  pagination.page = 1
  loadProductList()
}

// 刷新数据
const refreshData = () => {
  loadProductList()
  loadStatistics()
}

// 选择变更
const handleSelectionChange = (selection: any[]) => {
  selectedProducts.value = selection
}

// 分页
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  loadProductList()
}

// 查看详情
const viewProduct = async (row: any) => {
  try {
    const response: any = await productReviewApi.getReviewProductDetail(row.id)
    detailDialog.product = response.data
    console.log(detailDialog.product.shippingType)
    detailDialog.visible = true
  } catch (error) {
    ElMessage.error('加载商品详情失败')
  }
}

// 审核商品
const reviewProduct = (row: any, status: number) => {
  reviewForm.id = row.id
  reviewForm.reviewStatus = status
  reviewForm.reviewRemark = ''
  reviewDialogTitle.value = status === 1 ? '审核通过' : '审核拒绝'
  
  // 如果是审核通过，需要加载商品详情以设置价格
  if (status === 1) {
    loadReviewProductDetail(row.id)
  } else {
    currentReviewProduct.value = null
  }
  
  reviewDialogVisible.value = true
}

// 加载审核商品详情
const loadReviewProductDetail = async (productId: number) => {
  try {
    const response: any = await productReviewApi.getReviewProductDetail(productId)
    currentReviewProduct.value = response.data
    
    // 为每个SKU初始化销售价格为空
    if (currentReviewProduct.value.skuList) {
      currentReviewProduct.value.skuList.forEach((sku: any) => {
        sku.salePrice = null
      })
    }
  } catch (error) {
    ElMessage.error('加载商品详情失败')
  }
}

// 关闭审核对话框
const handleCloseReview = () => {
  reviewDialogVisible.value = false
  currentReviewProduct.value = null
  reviewForm.reviewRemark = ''
  reviewErrors.reviewRemark = ''
  
  // 重置价格表单
  reviewPriceForm.priceBase = 'costPrice'
  reviewPriceForm.multiplier = 1.2
  reviewPriceForm.adjustment = 0
}

// 处理审核SKU价格变化
const handleReviewSkuPriceChange = (sku: any, index: number) => {
  // 确保价格不为负数，如果为负数则设为null
  if (sku.salePrice !== null && sku.salePrice !== undefined && sku.salePrice < 0) {
    sku.salePrice = null
  }
  
  // 清除错误提示
  reviewErrors.reviewRemark = ''
}

// 获取审核价格基准名称
const getReviewPriceBaseName = () => {
  const nameMap: Record<string, string> = {
    'costPrice': '成本价',
    'purchasePrice': '采购价',
    'strikethroughPrice': '划线价'
  }
  return nameMap[reviewPriceForm.priceBase] || '成本价'
}

// 应用审核价格公式
const applyReviewPriceFormula = () => {
  if (!currentReviewProduct.value || !currentReviewProduct.value.skuList) return

  currentReviewProduct.value.skuList.forEach((sku: any) => {
    const basePrice = sku[reviewPriceForm.priceBase] || 0
    const newPrice = basePrice * reviewPriceForm.multiplier + reviewPriceForm.adjustment
    sku.salePrice = newPrice > 0 ? Number(newPrice.toFixed(2)) : null // 如果计算结果<=0则设为null
  })

  // 清除错误提示
  reviewErrors.reviewRemark = ''
}

// 计算毛利
const calculateProfit = (salePrice: number | string | null | undefined, costPrice: number | string | null | undefined): number => {
  if (salePrice === null || salePrice === undefined || salePrice === '') return 0
  const sale = Number(salePrice) || 0
  const cost = Number(costPrice) || 0
  return sale - cost
}

// 计算毛利率
const calculateProfitRate = (salePrice: number | string | null | undefined, costPrice: number | string | null | undefined): number => {
  if (salePrice === null || salePrice === undefined || salePrice === '') return 0
  const sale = Number(salePrice) || 0
  const cost = Number(costPrice) || 0
  
  if (sale === 0) return 0
  
  return ((sale - cost) / sale) * 100
}

// 格式化毛利率为百分比
const formatProfitRate = (profitRate: number): string => {
  return `${profitRate.toFixed(2)}%`
}

// 获取审核毛利样式类
const getReviewProfitClass = (row: any): string => {
  const profit = calculateProfit(row.salePrice, row.costPrice)
  if (profit > 0) return 'profit-positive'
  if (profit < 0) return 'profit-negative'
  return 'profit-zero'
}

// 获取审核毛利率样式类
const getReviewProfitRateClass = (row: any): string => {
  const profitRate = calculateProfitRate(row.salePrice, row.costPrice)
  if (profitRate > 0) return 'profit-rate-positive'
  if (profitRate < 0) return 'profit-rate-negative'
  return 'profit-rate-zero'
}



// 显示批量审核对话框
const showBatchReviewDialog = () => {
  if (selectedProducts.value.length === 0) {
    ElMessage.warning('请先选择要审核的商品')
    return
  }

  // 检查是否都是待审核状态
  const hasNonPending = selectedProducts.value.some(p => p.status !== 3)
  if (hasNonPending) {
    ElMessage.warning('只能审核状态为"审核中"的商品')
    return
  }

  // 重置表单
  batchDialog.form.reviewResult = 0
  batchDialog.form.reviewRemark = ''
  batchReviewErrors.reviewRemark = ''
  batchReviewProducts.value = []
  allBatchSkuList.value = []
  
  batchDialog.visible = true
}

// 处理批量审核结果变化
const handleBatchReviewResultChange = async (reviewResult: number) => {
  if (reviewResult === 1) {
    // 审核通过，需要加载所有商品详情
    await loadBatchReviewProductDetails()
  } else {
    // 审核拒绝，清空商品详情
    batchReviewProducts.value = []
    allBatchSkuList.value = []
  }
}

// 加载批量审核商品详情
const loadBatchReviewProductDetails = async () => {
  try {
    batchReviewProducts.value = []
    allBatchSkuList.value = []

    for (const product of selectedProducts.value) {
      const response: any = await productReviewApi.getReviewProductDetail(product.id)
      const productDetail = response.data

      // 为每个SKU添加商品信息和初始化销售价格
      if (productDetail.skuList) {
        productDetail.skuList.forEach((sku: any) => {
          sku.productId = productDetail.id
          sku.productName = productDetail.name
          sku.supplierName = productDetail.supplierName
          sku.mainImageUrl = productDetail.mainImgUrl
          sku.salePrice = null // 初始化为空
          allBatchSkuList.value.push(sku)
        })
      }

      batchReviewProducts.value.push(productDetail)
    }

    // 重置价格表单
    batchPriceForm.priceBase = 'costPrice'
    batchPriceForm.multiplier = 1
    batchPriceForm.adjustment = 0
  } catch (error) {
    ElMessage.error('获取商品详情失败')
  }
}

// 关闭批量审核对话框
const handleCloseBatchReview = () => {
  batchDialog.visible = false
  batchDialog.form.reviewResult = 0
  batchDialog.form.reviewRemark = ''
  batchReviewErrors.reviewRemark = ''
  batchReviewProducts.value = []
  allBatchSkuList.value = []
  
  // 重置价格表单
  batchPriceForm.priceBase = 'costPrice'
  batchPriceForm.multiplier = 1
  batchPriceForm.adjustment = 0
}

// 获取唯一供应商数量
const getUniqueSupplierCount = () => {
  const supplierIds = new Set(batchReviewProducts.value.map(p => p.supplierId))
  return supplierIds.size
}

// 处理批量SKU价格变化
const handleBatchSkuPriceChange = (sku: any, index: number) => {
  // 确保价格不为负数，如果为负数则设为null
  if (sku.salePrice !== null && sku.salePrice !== undefined && sku.salePrice < 0) {
    sku.salePrice = null
  }
  
  // 清除错误提示
  batchReviewErrors.reviewRemark = ''
}

// 获取批量审核价格基准名称
const getBatchPriceBaseName = () => {
  const nameMap: Record<string, string> = {
    'costPrice': '成本价',
    'purchasePrice': '采购价',
    'strikethroughPrice': '划线价'
  }
  return nameMap[batchPriceForm.priceBase] || '成本价'
}

// 应用批量审核价格公式
const applyBatchPriceFormula = () => {
  if (allBatchSkuList.value.length === 0) return

  allBatchSkuList.value.forEach((sku: any) => {
    const basePrice = sku[batchPriceForm.priceBase] || 0
    const newPrice = basePrice * batchPriceForm.multiplier + batchPriceForm.adjustment
    sku.salePrice = newPrice > 0 ? Number(newPrice.toFixed(2)) : null
  })

  // 清除错误提示
  batchReviewErrors.reviewRemark = ''
}

// 获取批量审核毛利样式类
const getBatchProfitClass = (row: any): string => {
  const profit = calculateProfit(row.salePrice, row.costPrice)
  if (profit > 0) return 'profit-positive'
  if (profit < 0) return 'profit-negative'
  return 'profit-zero'
}

// 获取批量审核毛利率样式类
const getBatchProfitRateClass = (row: any): string => {
  const profitRate = calculateProfitRate(row.salePrice, row.costPrice)
  if (profitRate > 0) return 'profit-rate-positive'
  if (profitRate < 0) return 'profit-rate-negative'
  return 'profit-rate-zero'
}

// 处理批量审核
const handleBatchReview = async () => {
  // 清空之前的错误信息
  batchReviewErrors.reviewRemark = ''

  // 验证审核意见
  if (!batchDialog.form.reviewRemark || !batchDialog.form.reviewRemark.trim()) {
    batchReviewErrors.reviewRemark = '审核意见为必填项，请输入审核意见'
    return
  }

  // 如果是审核通过，验证价格设置
  if (batchDialog.form.reviewResult === 1) {
    // 检查是否所有SKU都设置了销售价格
    const hasInvalidPrice = allBatchSkuList.value.some((sku: any) => {
      return sku.salePrice === null || sku.salePrice === undefined || sku.salePrice === '' || sku.salePrice <= 0
    })

    if (hasInvalidPrice) {
      batchReviewErrors.reviewRemark = '请为所有SKU设置有效的销售价格（大于0）'
      return
    }
  }

  batchDialog.loading = true
  try {
    const reviewData: any = {
      ids: selectedProducts.value.map(p => p.id),
      reviewResult: batchDialog.form.reviewResult,
      reviewRemark: batchDialog.form.reviewRemark
    }

    // 如果是批量审核通过，添加所有SKU的价格信息
    if (batchDialog.form.reviewResult === 1) {
      reviewData.skuPriceList = allBatchSkuList.value.map((sku: any) => ({
        spuId: sku.productId,
        skuId: sku.id,
        salePrice: Number(sku.salePrice)
      }))
    }

    await productReviewApi.batchReviewProducts(reviewData)

    const action = batchDialog.form.reviewResult === 1 ? '通过' : '拒绝'
    ElMessage.success(`批量${action}成功`)
    batchDialog.visible = false
    handleCloseBatchReview()
    selectedProducts.value = []
    loadProductList()
    loadStatistics()
  } catch (error) {
    ElMessage.error('批量审核失败')
  } finally {
    batchDialog.loading = false
  }
}

// 确认审核
const confirmReview = async () => {
  // 清空之前的错误信息
  reviewErrors.reviewRemark = ''

  // 验证审核意见
  if (!reviewForm.reviewRemark || !reviewForm.reviewRemark.trim()) {
    reviewErrors.reviewRemark = '审核意见为必填项，请输入审核意见'
    return
  }

  // 如果是审核通过，验证价格设置
  if (reviewForm.reviewStatus === 1 && currentReviewProduct.value) {
    // 检查是否所有SKU都设置了销售价格
    const hasInvalidPrice = currentReviewProduct.value.skuList.some((sku: any) => {
      return sku.salePrice === null || sku.salePrice === undefined || sku.salePrice === '' || sku.salePrice <= 0
    })

    if (hasInvalidPrice) {
      reviewErrors.reviewRemark = '请为所有SKU设置有效的销售价格（大于0）'
      return
    }
  }

  reviewLoading.value = true
  try {
    const reviewData: any = {
      reviewResult: reviewForm.reviewStatus,
      reviewRemark: reviewForm.reviewRemark
    }

    // 如果是审核通过，添加价格信息
    if (reviewForm.reviewStatus === 1 && currentReviewProduct.value) {
      reviewData.skuPriceList = currentReviewProduct.value.skuList.map((sku: any) => ({
        spuId: currentReviewProduct.value.id,
        skuId: sku.id,
        salePrice: Number(sku.salePrice)
      }))
    }

    await productReviewApi.reviewProduct(reviewForm.id, reviewData)

    ElMessage.success('审核成功')
    reviewDialogVisible.value = false
    handleCloseReview()
    loadProductList()
    loadStatistics()
  } catch (error) {
    ElMessage.error('审核失败')
  } finally {
    reviewLoading.value = false
  }
}

// 防抖搜索
const handleSearch = () => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  searchTimer = setTimeout(() => {
    pagination.page = 1
    loadProductList()
  }, 300)
}

// 点击统计卡片筛选
const filterByStatistic = (type: string) => {
  // 重置搜索条件
  searchForm.name = ''
  searchForm.supplierId = null
  
  switch (type) {
    case 'all':
      searchForm.status = null
      break
    case 'pending':
      searchForm.status = 3 // 审核中
      break
    case 'approved':
      searchForm.status = 1 // 已通过
      break
    case 'rejected':
      searchForm.status = 4 // 已拒绝
      break
  }
  
  pagination.page = 1
  loadProductList()
}

// 处理关闭商品详情
const handleCloseProductDetail = () => {
  detailDialog.visible = false
  detailDialog.product = null
  activeDetailTab.value = 'basic'
}

// 格式化价格
const formatPrice = (price: number) => {
  return price ? Number(price).toFixed(2) : '0.00'
}

// 商品图片计算属性
const productImages = computed(() => {
  if (!detailDialog.product || !detailDialog.product.images) return []
  return detailDialog.product.images.map((img: any) => img.imgUrl)
})

// 获取最大划线价
const getMaxStrikethroughPrice = () => {
  if (!detailDialog.product || !detailDialog.product.skuList) return 0
  const prices = detailDialog.product.skuList
    .map((sku: any) => sku.strikethroughPrice || 0)
    .filter((price: number) => price > 0)
  return prices.length > 0 ? Math.max(...prices) : 0
}

// 获取总库存
const getTotalStock = () => {
  if (!detailDialog.product || !detailDialog.product.skuList) return 0
  return detailDialog.product.skuList.reduce((total: number, sku: any) => {
    return total + (sku.totalInventory || 0)
  }, 0)
}

// 检查是否有物理属性
const hasPhysicalAttrs = computed(() => {
  if (!detailDialog.product) return false
  const { length, wide, tall, volume, weight } = detailDialog.product
  return length || wide || tall || volume || weight
})

// 获取查看详情模式下的预览价格
const getDetailPreviewPrice = (type: string) => {
  if (!detailDialog.product) return 0

  // 检查是否为多规格商品
  const isMultiSpec = detailDialog.product.skuList && detailDialog.product.skuList.length > 1

  if (isMultiSpec) {
    // 多规格情况，使用选中的SKU或第一个SKU
    const targetSku = selectedPreviewSku.value || detailDialog.product.skuList[0]
    if (!targetSku) return 0

    switch (type) {
      case 'cost':
        return Number(targetSku.costPrice) || 0
      case 'purchase':
        return Number(targetSku.purchasePrice) || 0
      case 'strikethrough':
        return Number(targetSku.strikethroughPrice) || 0
      default:
        return 0
    }
  } else {
    // 单规格情况
    const sku = detailDialog.product.skuList[0]
    if (!sku) return 0

    switch (type) {
      case 'cost':
        return Number(sku.costPrice) || 0
      case 'purchase':
        return Number(sku.purchasePrice) || 0
      case 'strikethrough':
        return Number(sku.strikethroughPrice) || 0
      default:
        return 0
    }
  }
}

// 获取查看详情模式下的规格信息
const getDetailPreviewSpecs = () => {
  if (!detailDialog.product || !detailDialog.product.skuList) return []

  const specs = []
  const skuList = detailDialog.product.skuList

  // 提取规格名称和值
  if (detailDialog.product.spec1Name) {
    const values = [...new Set(skuList.map((sku: any) => sku.spec1Value).filter((v: any) => v))]
    if (values.length > 0) {
      specs.push({
        name: detailDialog.product.spec1Name,
        values: values
      })
    }
  }

  if (detailDialog.product.spec2Name) {
    const values = [...new Set(skuList.map((sku: any) => sku.spec2Value).filter((v: any) => v))]
    if (values.length > 0) {
      specs.push({
        name: detailDialog.product.spec2Name,
        values: values
      })
    }
  }

  if (detailDialog.product.spec3Name) {
    const values = [...new Set(skuList.map((sku: any) => sku.spec3Value).filter((v: any) => v))]
    if (values.length > 0) {
      specs.push({
        name: detailDialog.product.spec3Name,
        values: values
      })
    }
  }

  return specs
}

// 处理查看详情模式下的规格选择
const handleDetailPreviewSpecChange = (specName: string, specValue: string) => {
  previewSpecSelections.value[specName] = specValue

  // 根据选择的规格找到对应的SKU
  const matchedSku = detailDialog.product.skuList.find((sku: any) => {
    const selections = previewSpecSelections.value

    // 检查是否所有已选择的规格都匹配
    let matches = true

    if (selections[detailDialog.product.spec1Name] &&
      sku.spec1Value !== selections[detailDialog.product.spec1Name]) {
      matches = false
    }

    if (selections[detailDialog.product.spec2Name] &&
      sku.spec2Value !== selections[detailDialog.product.spec2Name]) {
      matches = false
    }

    if (selections[detailDialog.product.spec3Name] &&
      sku.spec3Value !== selections[detailDialog.product.spec3Name]) {
      matches = false
    }

    return matches
  })

  if (matchedSku) {
    selectedPreviewSku.value = matchedSku
  }
}

// 预览相关状态
const selectedPreviewSku = ref<any>(null)
const previewSpecSelections = ref<{ [key: string]: string }>({})

// 图片错误处理
const handleImageError = (event: any) => {
  console.log('图片加载失败:', event.target.src)
  event.target.style.display = 'none'
}

// 辅助方法
const getShippingTemplateName = (templateId: number) => {
  if (!templateId) return '无'
  // 这里可以根据实际需求从模板列表中查找
  return `模板${templateId}`
}

const formatAfterSaleServices = (services: string) => {
  if (!services) return '无'
  const serviceMap: any = {
    'return': '退货',
    'exchange': '换货',
    'refund_only': '仅退款',
    'cancel_order': '整单退款',
    'return_shipping': '退费'
  }
  const serviceList = services.split(',').filter((s: string) => s.trim())
  return serviceList.map((s: string) => serviceMap[s.trim()] || s).join('、')
}

const getCategoryDisplayName = (categoryId: number) => {
  if (!categoryId) return '未分类'

  // 从分类路径映射中获取完整路径
  const categoryPath = categoryPathMap.value.get(Number(categoryId))
  return categoryPath || `分类ID: ${categoryId}`
}

// 加载分类树
const loadCategoryTree = async () => {
  try {
    const response = await getStandardCategories()
    if (response.code === 200 && response.data) {
      categoryTree.value = response.data
      buildCategoryPathMap(response.data)
    }
  } catch (error) {
    console.error('加载分类树失败', error)
  }
}

// 构建分类路径映射（参考供应商系统实现）
const buildCategoryPathMap = (categories: StandardCategory[], parentPath: string[] = []) => {
  if (!categories || !Array.isArray(categories)) return

  categories.forEach(category => {
    // 兼容不同的字段名
    const categoryName = category.categoryName || category.categoryCode || `分类${category.id}`
    const categoryId = category.id
    const children = category.children || []

    if (categoryId) {
      const currentPath = [...parentPath, categoryName]
      const pathString = currentPath.join(' / ')
      categoryPathMap.value.set(categoryId, pathString)

      if (children && children.length > 0) {
        buildCategoryPathMap(children, currentPath)
      }
    }
  })
}
</script>

<style scoped lang="scss">
.product-review-container {
  padding: 20px;
  height: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .header-left {
    h2 {
      margin: 0 0 5px 0;
      color: #303133;
    }

    p {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }
}

.statistics-cards {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;

  .stat-card {
    flex: 1;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .stat-content {
      text-align: center;

      .stat-number {
        font-size: 24px;
        font-weight: bold;
        color: #409eff;
        margin-bottom: 8px;
      }

      .stat-label {
        font-size: 14px;
        color: #666;
      }
    }
  }
}

.header {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 20px;
}

.product-info {
  display: flex;
  align-items: flex-start;
  gap: 12px;

  .product-image {
    flex-shrink: 0;

    .image-slot {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 60px;
      height: 60px;
      background: #f5f7fa;
      color: #909399;
      font-size: 24px;
      border-radius: 4px;
    }
  }

  .product-details {
    flex: 1;
    min-width: 0;

    .product-name {
      font-weight: 500;
      color: #303133;
      margin-bottom: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .product-code {
      font-size: 12px;
      color: #909399;
      margin-bottom: 2px;
    }

    .product-supplier {
      font-size: 12px;
      color: #666;
    }
  }
}

.price {
  font-weight: 500;
  color: #409eff;
}

.price-pending {
  font-weight: 500;
  color: #909399;
  font-style: italic;
}

.pagination {
  margin-top: 20px;
  justify-content: flex-end;
  display: flex;
}

.product-detail {
  .detail-section {
    margin-bottom: 30px;

    h3 {
      margin: 0 0 15px 0;
      color: #303133;
      font-size: 16px;
      border-bottom: 2px solid #409eff;
      padding-bottom: 8px;
    }

    .detail-image {
      width: 180px;
      height: 180px;
      border-radius: 4px;
    }

    .info-grid {
      .info-item {
        display: flex;
        margin-bottom: 12px;

        label {
          width: 100px;
          color: #606266;
          font-weight: bold;
          flex-shrink: 0;
        }

        span {
          color: #303133;
          flex: 1;
        }
      }
    }

    .description-content {
      padding: 15px;
      background: #f5f7fa;
      border-radius: 4px;
      line-height: 1.6;
      color: #606266;
    }

    .sku-specs {
      color: #606266;
      font-size: 12px;
    }

    .review-record {
      padding: 15px;
      background: #f5f7fa;
      border-radius: 4px;

      p {
        margin: 0 0 10px 0;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

.detail-footer {
  text-align: right;

  .el-button {
    margin-left: 10px;
  }
}

.product-detail-container {
  .product-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .product-main-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .product-image {
        flex-shrink: 0;
      }

      .product-basic {
        flex: 1;
        min-width: 0;

        .product-title {
          margin: 0 0 5px 0;
          color: #303133;
        }

        .product-meta {
          display: flex;
          align-items: center;
          gap: 8px;

          .product-id {
            font-size: 12px;
            color: #909399;
          }

          .status-tag {
            margin-left: 8px;
          }

          .type-tag {
            margin-left: 8px;
          }
        }
      }
    }

    .product-price-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .price-item {
        display: flex;
        flex-direction: column;

        .price-label {
          font-size: 12px;
          color: #909399;
        }

        .price-value {
          font-size: 14px;
          font-weight: bold;
          color: #409eff;
        }
      }

      .stats-item {
        display: flex;
        flex-direction: column;

        .stats-label {
          font-size: 12px;
          color: #909399;
        }

        .stats-value {
          font-size: 14px;
          font-weight: bold;
          color: #409eff;
        }
      }
    }
  }

  .detail-tabs {
    .basic-info-content {
      .el-descriptions {
        margin-bottom: 20px;
      }

      .product-description {
        margin-bottom: 20px;

        h3 {
          margin: 0 0 10px 0;
          color: #303133;
          font-size: 16px;
          border-bottom: 2px solid #409eff;
          padding-bottom: 8px;
        }

        .description-content {
          padding: 15px;
          background: #f5f7fa;
          border-radius: 4px;
          line-height: 1.6;
          color: #606266;
        }
      }

      .product-features {
        margin-bottom: 20px;

        h3 {
          margin: 0 0 10px 0;
          color: #303133;
          font-size: 16px;
          border-bottom: 2px solid #409eff;
          padding-bottom: 8px;
        }

        .features-content {
          padding: 15px;
          background: #f5f7fa;
          border-radius: 4px;
          line-height: 1.6;
          color: #606266;
        }
      }

      .product-instructions {
        margin-bottom: 20px;

        h3 {
          margin: 0 0 10px 0;
          color: #303133;
          font-size: 16px;
          border-bottom: 2px solid #409eff;
          padding-bottom: 8px;
        }

        .instructions-content {
          padding: 15px;
          background: #f5f7fa;
          border-radius: 4px;
          line-height: 1.6;
          color: #606266;
        }
      }
    }

    .spec-stock-content {
      .el-table {
        margin-bottom: 20px;
      }

      .spec-summary {
        margin-bottom: 20px;
      }
    }

    .review-record-content {
      .review-record {
        margin-bottom: 20px;
      }
    }
  }

  .detail-footer {
    text-align: right;
  }
}

// 商品详情抽屉样式
.product-detail-container {
  height: calc(100vh - 120px);
  overflow-y: auto;
  padding: 0 20px 20px;

  .product-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 20px 0;
    border-bottom: 1px solid #e8e8e8;
    margin-bottom: 20px;

    .product-main-info {
      display: flex;
      gap: 20px;
      flex: 1;

      .product-image {
        flex-shrink: 0;
      }

      .product-basic {
        flex: 1;

        .product-title {
          margin: 0 0 12px 0;
          font-size: 20px;
          font-weight: 600;
          color: #333;
          line-height: 1.4;
        }

        .product-meta {
          display: flex;
          align-items: center;
          gap: 12px;
          flex-wrap: wrap;

          .product-id {
            color: #666;
            font-size: 14px;
          }

          .status-tag,
          .type-tag {
            font-size: 12px;
          }
        }
      }
    }

    .product-price-info {
      display: flex;
      gap: 24px;
      align-items: flex-start;
      flex-shrink: 0;

      .price-item,
      .stats-item {
        text-align: center;
        min-width: 80px;

        .price-label,
        .stats-label {
          display: block;
          font-size: 12px;
          color: #999;
          margin-bottom: 4px;
        }

        .price-value {
          display: block;
          font-size: 18px;
          font-weight: 600;

          &.cost-price {
            color: #f56c6c;
          }

          &.strike-price {
            color: #909399;
          }

          &.purchase-price {
            color: #67c23a;
          }
        }

        .stats-value {
          display: block;
          font-size: 18px;
          font-weight: 600;
          color: #409eff;
        }
      }
    }
  }

  .detail-tabs {
    .basic-info-content {
      .physical-attrs,
      .shipping-settings,
      .product-images-section,
      .product-description,
      .product-features,
      .product-instructions {
        margin-top: 24px;

        h3 {
          margin: 0 0 16px 0;
          font-size: 16px;
          font-weight: 600;
          color: #333;
          border-left: 4px solid #4A7BFF;
          padding-left: 12px;
        }
      }

      .description-content,
      .features-content,
      .instructions-content {
        padding: 15px;
        background: #f5f7fa;
        border-radius: 4px;
        line-height: 1.6;
        color: #606266;
      }

      .images-gallery {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;

        .image-item {
          position: relative;

          .main-tag {
            position: absolute;
            top: -8px;
            right: -8px;
            z-index: 1;
          }
        }
      }
    }

    .spec-stock-content {
      h3 {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
        border-left: 4px solid #4A7BFF;
        padding-left: 12px;
      }

      .spec-values {
        display: flex;
        gap: 4px;
        flex-wrap: wrap;

        .spec-tag {
          font-size: 12px;

          &.default-spec {
            background: #f0f0f0;
            color: #999;
            border-color: #e0e0e0;
          }
        }
      }

      .price-text {
        font-weight: 500;

        &.cost-price {
          color: #f56c6c;
        }

        &.purchase-price {
          color: #67c23a;
        }

        &.strike-price {
          color: #909399;
        }
      }

      .stock-info {
        font-size: 12px;
        line-height: 1.4;

        div {
          margin-bottom: 2px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }

      .no-image {
        font-size: 12px;
        color: #999;
      }

      .spec-summary {
        margin-top: 24px;

        h4 {
          margin: 0 0 12px 0;
          font-size: 14px;
          font-weight: 600;
          color: #666;
        }
      }
    }

    .no-data {
      padding: 40px 0;
      text-align: center;
    }
  }
}

// 预览专用容器样式
.preview-only-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 20px;
  min-height: 600px;

  .centered-phone-preview {
    display: flex;
    flex-direction: column;
    align-items: center;

    .preview-title {
      margin-bottom: 16px;
      text-align: center;

      h3 {
        margin: 0 0 4px 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
      }

      p {
        margin: 0;
        color: #909399;
        font-size: 12px;
      }
    }
  }
}

// 手机预览样式
.phone-container {
  width: 375px;
  background: #000;
  border-radius: 30px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  overflow: hidden;

  .phone-header {
    background: #000;
    padding: 8px 20px 4px;

    .status-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #fff;
      font-size: 14px;
      font-weight: 600;

      .left-status {
        display: flex;
        align-items: center;
        gap: 6px;

        .signal-bars {
          display: flex;
          gap: 2px;

          .bar {
            width: 3px;
            height: 8px;
            background: #fff;
            border-radius: 1px;

            &:nth-child(1) {
              height: 4px;
            }

            &:nth-child(2) {
              height: 6px;
            }

            &:nth-child(3) {
              height: 8px;
            }

            &:nth-child(4) {
              height: 8px;
            }
          }
        }

        .carrier {
          font-size: 14px;
        }
      }

      .center-time {
        font-size: 16px;
        font-weight: 600;
      }

      .right-status {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 14px;
      }
    }
  }

  .phone-screen {
    width: 100%;
    height: 667px;
    background: #fff;
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;

    // 搜索栏样式
    .search-header {
      background: #fff;
      padding: 12px 15px;
      border-bottom: 1px solid #e8e8e8;
      position: sticky;
      top: 0;
      z-index: 10;

      .search-bar {
        display: flex;
        align-items: center;
        gap: 8px;
        background: #f5f5f5;
        border-radius: 20px;
        padding: 8px 15px;

        .search-icon {
          font-size: 14px;
          color: #999;
        }

        .search-placeholder {
          font-size: 14px;
          color: #999;
        }
      }
    }

    // 商品信息区域
    .product-info-area {
      background: #f5f5f5;
      padding: 0;
      min-height: calc(667px - 60px); // 减去搜索栏高度

      // 商品轮播图
      .product-carousel {
        background: #fff;

        .carousel-container {
          position: relative;

          .product-main-image {
            width: 100%;
            height: 375px;
            object-fit: cover;
            display: block;
          }

          .carousel-indicators {
            position: absolute;
            bottom: 12px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 6px;

            .indicator {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              background: rgba(255, 255, 255, 0.5);
              cursor: pointer;
              transition: background 0.3s;

              &.active {
                background: #fff;
              }
            }
          }
        }
      }

      // 商品基本信息
      .product-info-section {
        background: #fff;
        padding: 16px 15px;
        border-bottom: 1px solid #e8e8e8;

        .product-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          line-height: 1.4;
          margin-bottom: 8px;
        }

        .product-price {
          display: flex;
          align-items: baseline;
          gap: 8px;
          margin-bottom: 8px;

          .current-price {
            font-size: 20px;
            font-weight: 600;
            color: #ff4d4f;
          }

          .original-price {
            font-size: 14px;
            color: #999;
            text-decoration: line-through;
          }
        }

        .product-stock {
          font-size: 14px;
          color: #666;
          margin-bottom: 16px;
        }

        .spec-selection {
          .spec-group {
            margin-bottom: 16px;

            &:last-child {
              margin-bottom: 0;
            }

            .spec-label {
              font-size: 14px;
              color: #333;
              font-weight: 500;
              margin-bottom: 8px;
            }

            .spec-options {
              display: flex;
              flex-wrap: wrap;
              gap: 8px;

              .spec-option {
                padding: 6px 12px;
                background: #f5f5f5;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                font-size: 13px;
                color: #666;
                cursor: pointer;
                transition: all 0.2s;

                &:hover {
                  background: #e8f4ff;
                  border-color: #409eff;
                  color: #409eff;
                }

                &.active {
                  background: #409eff;
                  border-color: #409eff;
                  color: #fff;
                }
              }
            }
          }
        }
      }

      // 商品详情内容
      .product-detail-content {
        background: #fff;
        margin-top: 8px;
        margin-bottom: 20px;

        .detail-section {
          padding: 16px 15px;
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          h4 {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin: 0 0 12px 0;
            padding-bottom: 8px;
            border-bottom: 2px solid #409eff;
            display: inline-block;
          }

          .detail-html {
            :deep(.rich-content) {
              font-size: 14px;
              color: #333;
              line-height: 1.6;

              p {
                margin: 0 !important;
                padding: 0 !important;
              }

              img {
                max-width: calc(100% + 30px) !important;
                width: calc(100% + 30px) !important;
                height: auto !important;
                border-radius: 0px !important;
                margin: 0px -15px !important;
                display: block !important;
                box-sizing: border-box !important;
              }
            }
          }

          .detail-placeholder {
            font-size: 14px;
            color: #666;
            line-height: 1.6;

            p {
              margin: 0 0 12px 0;

              &:last-child {
                margin-bottom: 0;
              }
            }
          }

          .param-list {
            .param-item {
              display: flex;
              justify-content: space-between;
              margin-bottom: 8px;
              padding: 6px 0;
              border-bottom: 1px solid #f5f5f5;

              &:last-child {
                border-bottom: none;
                margin-bottom: 0;
              }

              .param-label {
                color: #999;
                font-size: 13px;
                flex: 0 0 auto;
              }

              .param-value {
                color: #333;
                font-size: 13px;
                font-weight: 500;
                text-align: right;
              }
            }
          }
        }
      }
    }
  }
}

// 全局富文本内容样式
:deep(.rich-content) {
  p {
    margin: 0 !important;
    padding: 0 !important;
  }

  img {
    max-width: calc(100% + 30px) !important;
    width: calc(100% + 30px) !important;
    height: auto !important;
    border-radius: 4px !important;
    margin: 0px -15px !important;
    display: block !important;
    box-sizing: border-box !important;
  }
}

// 商品详情抽屉样式
.product-detail-drawer {
  .detail-header {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e4e7ed;
    
    .product-basic {
      display: flex;
      gap: 20px;
      
      .product-image {
        flex-shrink: 0;
        
        .main-image {
          width: 120px;
          height: 120px;
          border-radius: 8px;
          border: 1px solid #e4e7ed;
        }
      }
      
      .product-info {
        flex: 1;
        
        .product-name {
          font-size: 18px;
          font-weight: 600;
          color: #303133;
          margin: 0 0 16px 0;
          line-height: 1.4;
        }
        
        .product-meta {
          .meta-item {
            display: flex;
            margin-bottom: 8px;
            
            .label {
              font-weight: 500;
              color: #606266;
              min-width: 100px;
              flex-shrink: 0;
            }
            
            .value {
              color: #303133;
              flex: 1;
              
              &.price {
                color: #409eff;
                font-weight: 500;
              }
            }
          }
        }
      }
    }
  }
  
  .detail-tabs {
    .tab-content {
      padding: 20px 0;
      
      .info-grid {
        .info-row {
          display: flex;
          gap: 40px;
          margin-bottom: 16px;
          
          &.full-width {
            flex-direction: column;
            gap: 0;
          }
          
          .info-item {
            flex: 1;
            display: flex;
            
            .label {
              font-weight: 500;
              color: #606266;
              min-width: 100px;
              flex-shrink: 0;
            }
            
            .value {
              color: #303133;
              flex: 1;
              word-break: break-all;
            }
          }
        }
      }
      
      .price {
        color: #409eff;
        font-weight: 500;
      }
      
      .image-gallery {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 16px;
        
        .image-item {
          .gallery-image {
            width: 120px;
            height: 120px;
            border-radius: 8px;
            border: 1px solid #e4e7ed;
            cursor: pointer;
            transition: all 0.3s;
            
            &:hover {
              border-color: #409eff;
              transform: scale(1.05);
            }
          }
        }
      }
      
      .no-data {
        text-align: center;
        padding: 40px 0;
      }
    }
  }
}

// 审核弹窗样式
.review-approve-container {
  .product-info-header {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 20px;

    .product-image {
      flex-shrink: 0;
    }

    .product-details {
      flex: 1;

      .product-name {
        margin: 0 0 8px 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }

      .product-meta {
        display: flex;
        gap: 20px;

        .product-code,
        .product-supplier {
          font-size: 13px;
          color: #666;
        }
      }
    }
  }

  .sku-list-section {
    margin-bottom: 20px;

    h4 {
      margin: 0 0 15px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      border-left: 3px solid #409eff;
      padding-left: 10px;
    }

    .product-info-cell {
      display: flex;
      align-items: flex-start;
      gap: 8px;
      padding: 4px 0;

      .product-image {
        flex-shrink: 0;
        margin-top: 2px;
      }

      .product-details {
        flex: 1;
        min-width: 0;

        .product-name {
          font-size: 12px;
          font-weight: 500;
          color: #303133;
          line-height: 1.3;
          margin-bottom: 4px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .product-supplier {
          font-size: 11px;
          color: #909399;
          line-height: 1.2;
          padding: 1px 4px;
          background: #f5f7fa;
          border-radius: 2px;
          display: inline-block;
          max-width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .spec-values {
      display: flex;
      flex-wrap: wrap;
      gap: 2px;

      .spec-tag {
        margin: 0;
        font-size: 11px;

        &.default-spec {
          background-color: #f0f2f5;
          color: #666;
          border: 1px solid #d9d9d9;
        }
      }
    }

    .price-text {
      font-weight: 500;
      font-size: 13px;

      &.cost-price {
        color: #f56c6c;
      }

      &.purchase-price {
        color: #67c23a;
      }

      &.strike-price {
        color: #909399;
      }

      &.profit {
        font-weight: 600;
        
        &.profit-positive {
          color: #67c23a;
        }
        
        &.profit-negative {
          color: #f56c6c;
        }
        
        &.profit-zero {
          color: inherit;
        }
      }

      &.profit-rate {
        font-weight: 600;
        
        &.profit-rate-positive {
          color: #67c23a;
        }
        
        &.profit-rate-negative {
          color: #f56c6c;
        }
        
        &.profit-rate-zero {
          color: inherit;
        }
      }
    }
  }

  .price-formula-section {
    h4 {
      margin: 0 0 15px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      border-left: 3px solid #409eff;
      padding-left: 10px;
    }

    .formula-container {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 15px;
      background: #f8f9fa;
      border-radius: 6px;
      border: 1px solid #e4e7ed;

      .formula-text {
        font-size: 14px;
        color: #303133;
        font-weight: 500;
      }

      .formula-base {
        font-size: 14px;
        color: #409eff;
        font-weight: 600;
        background: #e8f4ff;
        padding: 2px 8px;
        border-radius: 4px;
      }
    }

    .formula-tip {
      margin-top: 10px;
      padding: 8px 12px;
      background: #f0f9ff;
      border: 1px solid #b3d8ff;
      border-radius: 4px;
    }
  }
}

// 错误信息样式
.error-message {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  margin-top: 4px;
}

// 输入框错误状态样式
:deep(.el-textarea.is-error .el-textarea__inner) {
  border-color: #f56c6c !important;
  box-shadow: 0 0 0 1px #f56c6c inset !important;
}

:deep(.el-textarea.is-error:hover .el-textarea__inner) {
  border-color: #f56c6c !important;
}

:deep(.el-textarea.is-error .el-textarea__inner:focus) {
  border-color: #f56c6c !important;
  box-shadow: 0 0 0 1px #f56c6c inset !important;
}

// 批量审核样式
.batch-review-approve-container {
  .batch-product-overview {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 20px;

    h4 {
      margin: 0 0 10px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .overview-stats {
      display: flex;
      gap: 20px;

      .stat-item {
        font-size: 13px;
        color: #666;
        background: #fff;
        padding: 4px 8px;
        border-radius: 4px;
        border: 1px solid #e4e7ed;
      }
    }
  }

  .sku-list-section {
    margin-bottom: 20px;

    h4 {
      margin: 0 0 15px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      border-left: 3px solid #409eff;
      padding-left: 10px;
    }

    .product-info-cell {
      display: flex;
      align-items: flex-start;
      gap: 8px;
      padding: 4px 0;

      .product-image {
        flex-shrink: 0;
        margin-top: 2px;
      }

      .product-details {
        flex: 1;
        min-width: 0;

        .product-name {
          font-size: 12px;
          font-weight: 500;
          color: #303133;
          line-height: 1.3;
          margin-bottom: 4px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .product-supplier {
          font-size: 11px;
          color: #909399;
          line-height: 1.2;
          padding: 1px 4px;
          background: #f5f7fa;
          border-radius: 2px;
          display: inline-block;
          max-width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .spec-values {
      display: flex;
      flex-wrap: wrap;
      gap: 2px;

      .spec-tag {
        margin: 0;
        font-size: 11px;

        &.default-spec {
          background-color: #f0f2f5;
          color: #666;
          border: 1px solid #d9d9d9;
        }
      }
    }

    .price-text {
      font-weight: 500;
      font-size: 13px;

      &.cost-price {
        color: #f56c6c;
      }

      &.purchase-price {
        color: #67c23a;
      }

      &.strike-price {
        color: #909399;
      }

      &.profit {
        font-weight: 600;
        
        &.profit-positive {
          color: #67c23a;
        }
        
        &.profit-negative {
          color: #f56c6c;
        }
        
        &.profit-zero {
          color: inherit;
        }
      }

      &.profit-rate {
        font-weight: 600;
        
        &.profit-rate-positive {
          color: #67c23a;
        }
        
        &.profit-rate-negative {
          color: #f56c6c;
        }
        
        &.profit-rate-zero {
          color: inherit;
        }
      }
    }
  }

  .price-formula-section {
    h4 {
      margin: 0 0 15px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      border-left: 3px solid #409eff;
      padding-left: 10px;
    }

    .formula-container {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 15px;
      background: #f8f9fa;
      border-radius: 6px;
      border: 1px solid #e4e7ed;

      .formula-text {
        font-size: 14px;
        color: #303133;
        font-weight: 500;
      }

      .formula-base {
        font-size: 14px;
        color: #409eff;
        font-weight: 600;
        background: #e8f4ff;
        padding: 2px 8px;
        border-radius: 4px;
      }
    }

    .formula-tip {
      margin-top: 10px;
      padding: 8px 12px;
      background: #f0f9ff;
      border: 1px solid #b3d8ff;
      border-radius: 4px;
    }
  }
}
</style>