@use '@/assets/css/variables.scss' as *;
.account-wrapper {
  margin: 0 auto;
  padding: 20px;

  :deep(.el-form-item--label-top .el-form-item__label) {
    font-size: 14px;
    color: var(--el-text-color-primary);
    letter-spacing: 0;
    font-weight: 700;
  }
    .no-border-r {
      :deep(.el-input__wrapper) {
        box-shadow: -1px 0 0 0 var(--el-input-border-color),
          0 -1px 0 0 var(--el-input-border-color) inset,
          0 1px 0 0 var(--el-input-border-color) inset;
        border-right: 0;
      }

      :deep(.el-input-group__append) {
        font-size: 14px;
        color: var(--el-color-primary);
        background-color: var(--el-color-primary-light-9);
      }
    }



  .save-button {
    margin-top: 20px;
    width: 100%;
    background-color: var(--el-color-primary);
    height: 44px;
    font-size: 16px;
    font-weight: 700;
    box-shadow: inset 0px -1px 2px 0px var(--el-color-primary-dark-2);
    border-radius: 6px;
  }
  .cancel-button {
    margin-top: 20px;
      width: 100%;
      height: 44px;
      font-size: 16px;
      font-weight: 700;
      color: var(--el-text-color-primary);
      cursor: pointer;
      background-image: linear-gradient(180deg, var(--el-color-white) 0%, var(--el-color-primary-light-9) 100%);
      border: 1px solid var(--el-border-color-base);
      border-radius: 6px;
  }

  :deep(.el-form-item__content) {

    .el-input {
      height: 40px;
    }

    .el-input__inner {
      font-size: 14px;
    }
  }

  &-title {
    font-size: 16px;
    color: var(--el-text-color-primary);
    font-weight: 700;
    margin-top: 0;
    margin-bottom: 40px;
  }

  &-inner {
    width: 400px;
    margin: 0 auto;
  }
}