import { getTencentMapKey } from '@/api/tencentMap'

// 腾讯地图SDK加载状态
let mapSdkLoaded = false
let mapSdkLoading = false
let mapSdkLoadPromise: Promise<void> | null = null

/**
 * 动态加载腾讯地图SDK
 * @returns Promise<void>
 */
export const loadTencentMapSDK = async (): Promise<void> => {
  // 如果已经加载完成，直接返回
  if (mapSdkLoaded) {
    return Promise.resolve()
  }

  // 如果正在加载中，返回加载Promise
  if (mapSdkLoading && mapSdkLoadPromise) {
    return mapSdkLoadPromise
  }

  // 开始加载
  mapSdkLoading = true
  mapSdkLoadPromise = new Promise(async (resolve, reject) => {
    try {
      // 获取腾讯地图Key
      const response = await getTencentMapKey()
      
      if (response.code !== 200 || !response.data) {
        throw new Error(response.msg || '获取腾讯地图Key失败')
      }

      const mapKey = response.data

      // 检查是否已经存在地图SDK脚本
      const existingScript = document.querySelector('script[src*="map.qq.com/api/gljs"]')
      if (existingScript) {
        existingScript.remove()
      }

      // 创建script标签动态加载腾讯地图SDK
      const script = document.createElement('script')
      script.type = 'text/javascript'
      script.src = `https://map.qq.com/api/gljs?v=1.exp&key=${mapKey}`
      
      script.onload = () => {
        mapSdkLoaded = true
        mapSdkLoading = false
        console.log('腾讯地图SDK加载成功')
        resolve()
      }

      script.onerror = () => {
        mapSdkLoading = false
        const error = new Error('腾讯地图SDK加载失败')
        console.error(error)
        reject(error)
      }

      // 添加到head标签
      document.head.appendChild(script)

    } catch (error) {
      mapSdkLoading = false
      console.error('动态加载腾讯地图SDK失败:', error)
      reject(error)
    }
  })

  return mapSdkLoadPromise
}

/**
 * 检查腾讯地图SDK是否已加载
 * @returns boolean
 */
export const isTencentMapSDKLoaded = (): boolean => {
  return mapSdkLoaded && typeof window !== 'undefined' && !!(window as any).TMap
}

/**
 * 等待腾讯地图SDK加载完成
 * @param timeout 超时时间（毫秒），默认10秒
 * @returns Promise<void>
 */
export const waitForTencentMapSDK = (timeout: number = 10000): Promise<void> => {
  return new Promise((resolve, reject) => {
    // 如果已经加载完成，直接返回
    if (isTencentMapSDKLoaded()) {
      resolve()
      return
    }

    // 设置超时
    const timeoutId = setTimeout(() => {
      reject(new Error('等待腾讯地图SDK加载超时'))
    }, timeout)

    // 轮询检查SDK是否加载完成
    const checkInterval = setInterval(() => {
      if (isTencentMapSDKLoaded()) {
        clearInterval(checkInterval)
        clearTimeout(timeoutId)
        resolve()
      }
    }, 100)
  })
} 