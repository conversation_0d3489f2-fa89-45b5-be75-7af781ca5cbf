import { computed } from 'vue'
import { useTheme } from '@/composables/useTheme.js'

/**
 * 主题感知混入 - 为组件配置提供主题颜色支持
 */
export function useThemeAware() {
  const { themeConfig, getThemeColors, getWidgetDefaultColors } = useTheme()
  
  // 获取当前主题颜色
  const currentThemeColors = computed(() => {
    return getThemeColors()
  })
  
  // 获取主题颜色重置方法
  const getThemeColorResetMethods = (widgetType) => {
    const themeColors = getWidgetDefaultColors(widgetType)
    
    return {
      // 重置为主题颜色的方法
      resetToThemeColor: (colorKey) => {
        return themeColors[colorKey] || ''
      },
      
      // 批量重置颜色方法
      resetAllColorsToTheme: (localData) => {
        if (!localData.styles) return
        
        Object.keys(themeColors).forEach(colorKey => {
          if (localData.styles.hasOwnProperty(colorKey)) {
            localData.styles[colorKey] = themeColors[colorKey]
          }
        })
      },
      
      // 检查是否为主题颜色
      isThemeColor: (color, colorKey) => {
        return color === themeColors[colorKey]
      },
      
      // 获取颜色重置按钮文本
      getResetButtonText: (colorKey) => {
        const themeColor = themeColors[colorKey]
        return themeColor ? '主题色' : '重置'
      }
    }
  }
  
  return {
    themeConfig,
    currentThemeColors,
    getThemeColorResetMethods,
    getWidgetDefaultColors
  }
} 