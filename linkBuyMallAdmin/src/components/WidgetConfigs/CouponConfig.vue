<template>
    <div class="coupon-config">
        <!-- 内容Tab -->
        <div v-show="activeTab === 'content'" class="content-properties">
            <div class="config-section">
                <h4 class="section-title">风格选择</h4>

                <div class="style-selector-container">
                    <div class="style-preview" @click="openStyleDialog">
                        <div class="preview-content">
                            <img :src="currentStyleImage" :alt="currentStyleName" class="current-style-image" />
                        </div>
                        <div class="style-hover-overlay">
                            <el-button type="primary" size="default">切换风格</el-button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="config-section">
                <h4 class="section-title">添加优惠券</h4>
                <div class="form-group">
                    <label class="form-label">最多可添加{{ maxSelect }}张</label>
                    <el-button type="primary" @click="openCouponSelector" :disabled="selectedCouponCount >= maxSelect">
                        选择优惠券
                    </el-button>
                </div>

                <div class="form-group">
                    <label class="form-label">最大选择数量</label>
                    <el-input-number
                        v-model="localData.content.max_select"
                        :min="1"
                        :max="20"
                        size="small"
                        disabled
                        @change="updateData"
                    />
                </div>

                <!-- 优惠券列表 -->
                <div class="coupon-list" v-if="couponList.length > 0">
                    <div v-for="(coupon, index) in couponList" :key="coupon.id" class="coupon-item-config">
                        <div class="coupon-info">
                            <div class="coupon-name">
                                {{ coupon.name }}
                                <span v-if="coupon.is_preview" class="preview-tag">预览</span>
                            </div>
                            <div class="coupon-desc">{{ coupon.discount_content }}</div>
                        </div>
                        <el-button v-if="!coupon.is_preview" type="danger" text size="small" @click="removeCoupon(index)">
                            删除
                        </el-button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 样式Tab -->
        <div v-show="activeTab === 'style'" class="style-properties">
            <div class="config-section">
                <h4 class="section-title">颜色设置</h4>

                <div class="form-group">
                    <label class="form-label">优惠券背景</label>
                    <div class="color-control">
                        <el-color-picker v-model="localData.styles.bg_color" @change="updateData" size="small" />
                        <el-input v-model="localData.styles.bg_color" placeholder="#FCE7E7" @input="updateData" />
                        <el-button type="primary" text size="small" @click="resetBgColor">重置</el-button>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">文字颜色</label>
                    <div class="color-control">
                        <el-color-picker v-model="localData.styles.text_color" @change="updateData" size="small" />
                        <el-input v-model="localData.styles.text_color" placeholder="#FF2C3C" @input="updateData" />
                        <el-button type="primary" text size="small" @click="resetTextColor">重置</el-button>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">按钮背景</label>
                    <div class="color-control">
                        <el-color-picker v-model="localData.styles.btn_bg_color" @change="updateData" size="small" />
                        <el-input v-model="localData.styles.btn_bg_color" placeholder="#FF2C3C" @input="updateData" />
                        <el-button type="primary" text size="small" @click="resetBtnBgColor">重置</el-button>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">按钮文字</label>
                    <div class="color-control">
                        <el-color-picker v-model="localData.styles.btn_text_color" @change="updateData" size="small" />
                        <el-input v-model="localData.styles.btn_text_color" placeholder="#FFFFFF" @input="updateData" />
                        <el-button type="primary" text size="small" @click="resetBtnTextColor">重置</el-button>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">金额颜色</label>
                    <div class="color-control">
                        <el-color-picker v-model="localData.styles.money_color" @change="updateData" size="small" />
                        <el-input v-model="localData.styles.money_color" placeholder="#FF2C3C" @input="updateData" />
                        <el-button type="primary" text size="small" @click="resetMoneyColor">重置</el-button>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">使用条件颜色</label>
                    <div class="color-control">
                        <el-color-picker v-model="localData.styles.condition_color" @change="updateData" size="small" />
                        <el-input v-model="localData.styles.condition_color" placeholder="#333333" @input="updateData" />
                        <el-button type="primary" text size="small" @click="resetConditionColor">重置</el-button>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">使用场景颜色</label>
                    <div class="color-control">
                        <el-color-picker v-model="localData.styles.scene_color" @change="updateData" size="small" />
                        <el-input v-model="localData.styles.scene_color" placeholder="#999999" @input="updateData" />
                        <el-button type="primary" text size="small" @click="resetSceneColor">重置</el-button>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">标题颜色</label>
                    <div class="color-control">
                        <el-color-picker v-model="localData.styles.title_color" @change="updateData" size="small" />
                        <el-input v-model="localData.styles.title_color" placeholder="#333333" @input="updateData" />
                        <el-button type="primary" text size="small" @click="resetTitleColor">重置</el-button>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">底部背景</label>
                    <div class="color-control">
                        <el-color-picker v-model="localData.styles.root_bg_color" @change="updateData" size="small" />
                        <el-input v-model="localData.styles.root_bg_color" placeholder="#FFFFFF" @input="updateData" />
                        <el-button type="primary" text size="small" @click="resetRootBgColor">重置</el-button>
                    </div>
                </div>
            </div>

            <div class="config-section">
                <h4 class="section-title">边距设置</h4>

                <div class="form-group">
                    <label class="form-label">上边距</label>
                    <div class="slider-control">
                        <el-slider
                            v-model="localData.styles.padding_top"
                            :min="0"
                            :max="50"
                            :show-tooltip="false"
                            @input="updateData"
                        />
                        <el-input-number
                            v-model="localData.styles.padding_top"
                            :min="0"
                            :max="50"
                            size="small"
                            controls-position="right"
                            class="slider-input"
                            @change="updateData"
                        />
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">下边距</label>
                    <div class="slider-control">
                        <el-slider
                            v-model="localData.styles.padding_bottom"
                            :min="0"
                            :max="50"
                            :show-tooltip="false"
                            @input="updateData"
                        />
                        <el-input-number
                            v-model="localData.styles.padding_bottom"
                            :min="0"
                            :max="50"
                            size="small"
                            controls-position="right"
                            class="slider-input"
                            @change="updateData"
                        />
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">左右边距</label>
                    <div class="slider-control">
                        <el-slider
                            v-model="localData.styles.padding_horizontal"
                            :min="0"
                            :max="50"
                            :show-tooltip="false"
                            @input="updateData"
                        />
                        <el-input-number
                            v-model="localData.styles.padding_horizontal"
                            :min="0"
                            :max="50"
                            size="small"
                            controls-position="right"
                            class="slider-input"
                            @change="updateData"
                        />
                    </div>
                </div>
            </div>
        </div>

        <!-- 风格选择弹窗 -->
        <el-dialog v-model="showStyleDialog" title="风格选择" width="600px" :before-close="handleStyleDialogClose">
            <div class="style-dialog-content">
                <div class="style-options">
                    <div v-for="style in couponStyles" :key="style.id" class="style-option-card"
                        :class="{ active: selectedStyleId === style.id }" @click="selectCouponStyle(style)">
                        <div class="style-preview-image">
                            <img :src="style.previewImage" :alt="style.name" />
                        </div>
                        <div class="style-name">{{ style.name }}</div>
                    </div>
                </div>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showStyleDialog = false">取消</el-button>
                    <el-button type="primary" @click="confirmStyleSelection">确定</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 优惠券选择器 -->
        <CouponSelector
            v-model="showCouponSelector"
            :selectedCouponIds="selectedCouponIds"
            :mallId="Number(mallId)"
            :maxSelect="maxSelect"
            @confirm="handleCouponConfirm"
        />
    </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
// 导入优惠券风格预览图片
import couponStyle1 from '@/assets/images/decoration/coupon_style1.png'
import couponStyle2 from '@/assets/images/decoration/coupon_style2.png'
import couponStyle3 from '@/assets/images/decoration/coupon_style3.png'
// 导入优惠券选择器组件
import CouponSelector from '@/components/couponSelector/index.vue'

const props = defineProps({
    data: {
        type: Object,
        required: true
    },
    activeTab: {
        type: String,
        default: 'content'
    },
    mallId: {
        type: Number,
        required: true
    }
})

const emit = defineEmits(['update'])

// 风格选择相关
const showStyleDialog = ref(false)
const selectedStyleId = ref(null)

// 优惠券选择器相关
const showCouponSelector = ref(false)

// 优惠券风格定义
const couponStyles = ref([
    {
        id: 1,
        name: '风格1',
        previewImage: couponStyle1,
        config: {
            layout: 'card',
            style: 'modern'
        }
    },
    {
        id: 2,
        name: '风格2',
        previewImage: couponStyle2,
        config: {
            layout: 'list',
            style: 'classic'
        }
    },
    {
        id: 3,
        name: '风格3',
        previewImage: couponStyle3,
        config: {
            layout: 'grid',
            style: 'compact'
        }
    }
])

// 本地数据副本
const localData = ref({
    title: "优惠券",
    name: "coupon",
    show: 1,
    content: {
        style: 1,
        title: "超值优惠券",
        bg_type: 1,
        data: []
    },
    styles: {
        root_bg_color: "#FFFFFF",
        title_color: "#FFFFFF",
        bg_color: "#FCE7E7",
        bg_image: "",
        text_color: "#FF2C3C",
        btn_bg_color: "#FF2C3C",
        btn_text_color: "#FFFFFF",
        padding_top: 10,
        padding_horizontal: 10,
        padding_bottom: 10,
        money_color: "#FF2C3C",
        condition_color: "#333333",
        scene_color: "#999999"
    },
    ...props.data
})

// 监听props变化，更新本地数据
watch(() => props.data, (newData) => {
    localData.value = { ...localData.value, ...newData }
}, { deep: true })

// 计算属性
const couponList = computed(() => localData.value.content?.data || [])
const selectedCouponList = computed(() => couponList.value.filter(item => !item.is_preview))
const selectedCouponCount = computed(() => selectedCouponList.value.length)
const maxSelect = computed(() => localData.value.content?.max_select || 10)

// 计算当前选中风格的图片和名称
const currentStyleImage = computed(() => {
    const currentStyleId = localData.value.content?.style || 1
    const currentStyle = couponStyles.value.find(style => style.id === currentStyleId)
    return currentStyle ? currentStyle.previewImage : couponStyles.value[0].previewImage
})

const currentStyleName = computed(() => {
    const currentStyleId = localData.value.content?.style || 1
    const currentStyle = couponStyles.value.find(style => style.id === currentStyleId)
    return currentStyle ? currentStyle.name : couponStyles.value[0].name
})

// 更新数据
const updateData = () => {
    emit('update', { ...localData.value })
}

// 重置颜色方法
const resetBgColor = () => {
    localData.value.styles.bg_color = '#FCE7E7'
    updateData()
}

const resetTextColor = () => {
    localData.value.styles.text_color = '#FF2C3C'
    updateData()
}

const resetBtnBgColor = () => {
    localData.value.styles.btn_bg_color = '#FF2C3C'
    updateData()
}

const resetBtnTextColor = () => {
    localData.value.styles.btn_text_color = '#FFFFFF'
    updateData()
}

const resetRootBgColor = () => {
    localData.value.styles.root_bg_color = '#FFFFFF'
    updateData()
}

const resetPaddingHorizontal = () => {
    localData.value.styles.padding_horizontal = 10
    updateData()
}

const resetMoneyColor = () => {
    localData.value.styles.money_color = '#FF2C3C'
    updateData()
}

const resetConditionColor = () => {
    localData.value.styles.condition_color = '#333333'
    updateData()
}

const resetSceneColor = () => {
    localData.value.styles.scene_color = '#999999'
    updateData()
}

const resetTitleColor = () => {
    localData.value.styles.title_color = '#333333'
    updateData()
}

// 打开优惠券选择器
const openCouponSelector = () => {
    showCouponSelector.value = true
}

// 处理优惠券选择确认
const handleCouponConfirm = (selectedCoupons) => {
    if (!localData.value.content.data) {
        localData.value.content.data = []
    }

    // 转换选中的优惠券数据格式
    const newCoupons = selectedCoupons.map(coupon => ({
        id: coupon.id,
        name: coupon.name,
        is_receive: 0,
        is_available: 1,
        use_type: coupon.useScope === 1 ? "全场通用" : "指定商品可用",
        money: coupon.discountValue?.toFixed(2) || "0.00",
        discount_content: getDiscountContent(coupon),
        condition: getConditionText(coupon),
        is_preview: false
    }))

    // 直接替换为新选择的优惠券
    localData.value.content.data = newCoupons
    
    updateData()
}

// 获取优惠内容描述
const getDiscountContent = (coupon) => {
    const discountValue = coupon.discountValue || 0
    const minOrderAmount = coupon.minOrderAmount || 0
    
    if (minOrderAmount <= 0) {
        return `无门槛, 减${discountValue.toFixed(2)}元`
    } else {
        return `订单满${minOrderAmount.toFixed(2)}元, 减${discountValue.toFixed(2)}元`
    }
}

// 获取使用条件文本
const getConditionText = (coupon) => {
    const minOrderAmount = coupon.minOrderAmount || 0
    
    if (minOrderAmount <= 0) {
        return "无金额门槛"
    } else {
        return `满${minOrderAmount.toFixed(2)}使用`
    }
}

// 获取已选择的优惠券ID列表（排除预览数据）
const selectedCouponIds = computed(() => {
    return selectedCouponList.value.map(item => item.id) || []
})

// 删除优惠券
const removeCoupon = (index) => {
    const coupon = localData.value.content.data[index]
    if (!coupon.is_preview) {
        localData.value.content.data.splice(index, 1)
        
        // 如果删除后没有真实优惠券了，恢复预览数据
        const hasRealCoupons = localData.value.content.data.some(item => !item.is_preview)
        if (!hasRealCoupons) {
            // 恢复默认预览数据
            const previewData = [
                {
                    id: 'preview_1',
                    name: "新人专享券",
                    is_receive: 0,
                    is_available: 1,
                    use_type: "全场通用",
                    money: "5.00",
                    discount_content: "订单满100.00元, 减5.00元",
                    condition: "满100.00使用",
                    is_preview: true
                },
                {
                    id: 'preview_2',
                    name: "满减优惠券",
                    is_receive: 0,
                    is_available: 1,
                    use_type: "指定商品可用",
                    money: "10.00",
                    discount_content: "订单满200.00元, 减10.00元",
                    condition: "满200.00使用",
                    is_preview: true
                },
                {
                    id: 'preview_3',
                    name: "限时抢购券",
                    is_receive: 0,
                    is_available: 1,
                    use_type: "全场通用",
                    money: "20.00",
                    discount_content: "订单满300.00元, 减20.00元",
                    condition: "满300.00使用",
                    is_preview: true
                }
            ]
            localData.value.content.data = previewData
        }
        
        updateData()
    }
}

// 风格选择相关方法
const openStyleDialog = () => {
    selectedStyleId.value = localData.value.content?.style || 1
    showStyleDialog.value = true
}

const selectCouponStyle = (style) => {
    selectedStyleId.value = style.id
}

const confirmStyleSelection = () => {
    if (selectedStyleId.value) {
        const style = couponStyles.value.find(s => s.id === selectedStyleId.value)
        if (style) {
            // 确保content对象存在
            if (!localData.value.content) {
                localData.value.content = {}
            }
            if (!localData.value.styles) {
                localData.value.styles = {}
            }

            localData.value.content.style = style.id

            // 根据不同风格设置默认配置，但不清除已选择的优惠券
            if (style.id === 1) {
                // 风格1：现代卡片样式
                localData.value.styles.bg_color = localData.value.styles.bg_color || '#FCE7E7'
                localData.value.styles.text_color = localData.value.styles.text_color || '#FF2C3C'
            } else if (style.id === 2) {
                // 风格2：经典列表样式
                localData.value.styles.bg_color = localData.value.styles.bg_color || '#FFF5F5'
                localData.value.styles.text_color = localData.value.styles.text_color || '#E53E3E'
            } else if (style.id === 3) {
                // 风格3：紧凑网格样式
                localData.value.styles.bg_color = localData.value.styles.bg_color || '#F0F9FF'
                localData.value.styles.text_color = localData.value.styles.text_color || '#0369A1'
            }

            updateData()
        }
    }
    showStyleDialog.value = false
}

const handleStyleDialogClose = () => {
    selectedStyleId.value = localData.value.content?.style || null
    showStyleDialog.value = false
}
</script>

<style scoped>
.coupon-config {
    padding: 16px;
}

.config-section {
    margin-bottom: 24px;
}

.section-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
}

.form-group {
    margin-bottom: 16px;
}

.form-label {
    display: block;
    font-size: 13px;
    color: #666;
    margin-bottom: 8px;
}

.color-control {
    display: flex;
    align-items: center;
    gap: 8px;
}

.color-control .el-input {
    flex: 1;
}

.color-control .el-button {
    flex-shrink: 0;
}

.slider-control {
    display: flex;
    align-items: center;
    gap: 12px;
}

.slider-control .el-slider {
    flex: 1;
}

.slider-control .el-input-number,
.slider-input {
    width: 80px;
    flex-shrink: 0;
}

/* 风格选择样式 */
.style-selector-container {
    margin-bottom: 16px;
}

.style-preview {
    width: 100%;
    height: 120px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f9fafb;
    position: relative;
    overflow: hidden;
}

.style-preview:hover {
    border-color: #3b82f6;
    background: #f0f9ff;
}

.style-preview:hover .style-hover-overlay {
    opacity: 1;
    visibility: visible;
}

.style-preview:hover .preview-content {
    opacity: 0.3;
}

.preview-content {
    text-align: center;
    transition: opacity 0.3s ease;
    z-index: 1;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.current-style-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 6px;
}

.style-hover-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.1);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 2;
}

.coupon-list {
    margin-top: 12px;
}

.coupon-item-config {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    margin-bottom: 8px;
    background: #fafafa;
}

.coupon-info {
    flex: 1;
}

.coupon-name {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.coupon-desc {
    font-size: 12px;
    color: #666;
}

.preview-tag {
    display: inline-block;
    background: #409EFF;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 8px;
}

/* 风格选择弹窗样式 */
.style-dialog-content {
    padding: 20px 0;
}

.style-options {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.style-option-card {
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fff;
    text-align: center;
}

.style-option-card:hover {
    border-color: #3b82f6;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
    transform: translateY(-2px);
}

.style-option-card.active {
    border-color: #3b82f6;
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.25);
}

.style-option-card.active .style-name {
    color: #3b82f6;
}

.style-preview-image {
    margin-bottom: 16px;
    border-radius: 8px;
    overflow: hidden;
    background: #fafafa;
    height: 160px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #e5e7eb;
    position: relative;
}

.style-preview-image img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    display: block;
}

.style-name {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    margin: 0;
    margin-top: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .color-control {
        flex-direction: column;
        align-items: stretch;
    }

    .slider-control {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .slider-control .el-input-number {
        width: 100%;
    }

    .style-options {
        grid-template-columns: 1fr;
    }

    .style-preview-image {
        height: 120px;
    }
}
</style>