<template>
    <div class="image-magic-config">
        <!-- 内容Tab -->
        <div v-show="activeTab === 'content'" class="content-properties">
            <div class="config-section">
                <h4 class="section-title">风格选择</h4>
                
                <div class="form-group">
                    <el-select 
                        v-model="localData.content.style" 
                        placeholder="请选择布局样式"
                        @change="selectLayout"
                        style="width: 100%;"
                    >
                        <el-option
                            v-for="layout in layoutOptions"
                            :key="layout.value"
                            :label="layout.name"
                            :value="layout.value"
                        />
                    </el-select>
                </div>
            </div>

            <div class="config-section">
                <h4 class="section-title">魔方布局</h4>
                
                <!-- 魔方布局预览区域 -->
                <div class="magic-layout-preview">
                    <div 
                        class="layout-container"
                        :class="`layout-style-${localData.content.style}`"
                    >
                        <div 
                            v-for="(image, index) in localData.content.data" 
                            :key="index"
                            class="layout-item"
                            :class="{ 
                                active: selectedImageIndex === index,
                                'has-image': image.url 
                            }"
                            @click="selectLayoutImage(index)"
                        >
                            <img v-if="image.url" :src="image.url" :alt="`图片${index + 1}`" />
                            <div v-else class="placeholder-item">
                                <span class="width-hint">{{ getWidthHint(index) }}</span>
                            </div>
                            <!-- 选中指示器 -->
                            <div v-if="selectedImageIndex === index" class="selected-indicator">
                                <el-icon><Check /></el-icon>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="config-section" v-if="selectedImageIndex >= 0">
                <h4 class="section-title">图片设置</h4>
                
                <div class="image-setting-item">
                    <div class="form-group">
                        <label class="form-label">图片</label>
                        <div class="image-upload-control">
                            <div class="image-preview-small" @click="selectImage">
                                <img 
                                    v-if="currentSelectedImage.url" 
                                    :src="currentSelectedImage.url" 
                                    alt="选中的图片" 
                                />
                                <div v-else class="placeholder-small">
                                    <el-icon><Plus /></el-icon>
                                </div>
                            </div>
                            <div class="upload-actions">
                                <el-button type="primary" size="small" @click="selectImage">
                                    {{ currentSelectedImage.url ? '更换图片' : '选择图片' }}
                                </el-button>
                                <el-button 
                                    v-if="currentSelectedImage.url" 
                                    type="danger" 
                                    size="small" 
                                    @click="removeCurrentImage"
                                >
                                    删除
                                </el-button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">链接</label>
                        <el-input 
                            :model-value="getLinkDisplayText(currentSelectedImage.link)" 
                            placeholder="请选择跳转链接"
                            readonly
                            @click="openLinkSelector"
                        >
                            <template #suffix>
                                <el-icon><ArrowRight /></el-icon>
                            </template>
                        </el-input>
                    </div>
                </div>
            </div>

            <!-- 未选择图片时的提示 -->
            <div class="config-section" v-else>
                <div class="no-selection-tip">
                    <el-icon><InfoFilled /></el-icon>
                    <span>点击上方魔方布局中的图片区域进行设置</span>
                </div>
            </div>
        </div>

        <!-- 样式Tab -->
        <div v-show="activeTab === 'style'" class="style-properties">
            <div class="config-section">
                <h4 class="section-title">颜色设置</h4>

                <div class="form-group">
                    <label class="form-label">底部背景</label>
                    <div class="color-control">
                        <el-color-picker v-model="localData.styles.root_bg_color" @change="updateData" size="small" />
                        <el-input v-model="localData.styles.root_bg_color" placeholder="透明" @input="updateData" />
                        <el-button type="primary" text size="small" @click="resetRootBgColor">重置</el-button>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">背景图片</label>
                    <div class="image-upload">
                        <div class="image-preview" @click="selectBackgroundImage">
                            <img v-if="localData.styles.root_bg_url" :src="localData.styles.root_bg_url" alt="背景图片" />
                            <div v-else class="placeholder">
                                <el-icon><Plus /></el-icon>
                                <span>点击选择背景图片</span>
                            </div>
                            <!-- 删除按钮 -->
                            <div v-if="localData.styles.root_bg_url" 
                                 class="delete-btn" 
                                 @click.stop="removeBackgroundImage"
                                 title="删除背景图片">
                                <el-icon>
                                    <Delete />
                                </el-icon>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="config-section">
                <h4 class="section-title">圆角设置</h4>

                <div class="form-group">
                    <label class="form-label">组件圆角</label>
                    <div class="slider-control">
                        <el-slider 
                            v-model="localData.styles.border_radius" 
                            :min="0" 
                            :max="50"
                            :show-tooltip="false"
                            @input="updateData"
                        />
                        <el-input-number 
                            v-model="localData.styles.border_radius" 
                            :min="0" 
                            :max="50"
                            size="small"
                            controls-position="right"
                            class="slider-input"
                            @change="updateData"
                        />
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">图片圆角</label>
                    <div class="slider-control">
                        <el-slider 
                            v-model="localData.styles.cube_border_radius" 
                            :min="0" 
                            :max="50"
                            :show-tooltip="false"
                            @input="updateData"
                        />
                        <el-input-number 
                            v-model="localData.styles.cube_border_radius" 
                            :min="0" 
                            :max="50"
                            size="small"
                            controls-position="right"
                            class="slider-input"
                            @change="updateData"
                        />
                    </div>
                </div>
            </div>

            <div class="config-section">
                <h4 class="section-title">内边距设置</h4>

                <div class="form-group">
                    <label class="form-label">上边距</label>
                    <div class="slider-control">
                        <el-slider 
                            v-model="localData.styles.padding_top" 
                            :min="0" 
                            :max="50"
                            :show-tooltip="false"
                            @input="updateData"
                        />
                        <el-input-number 
                            v-model="localData.styles.padding_top" 
                            :min="0" 
                            :max="50"
                            size="small"
                            controls-position="right"
                            class="slider-input"
                            @change="updateData"
                        />
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">下边距</label>
                    <div class="slider-control">
                        <el-slider 
                            v-model="localData.styles.padding_bottom" 
                            :min="0" 
                            :max="50"
                            :show-tooltip="false"
                            @input="updateData"
                        />
                        <el-input-number 
                            v-model="localData.styles.padding_bottom" 
                            :min="0" 
                            :max="50"
                            size="small"
                            controls-position="right"
                            class="slider-input"
                            @change="updateData"
                        />
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">左右边距</label>
                    <div class="slider-control">
                        <el-slider 
                            v-model="localData.styles.padding_horizontal" 
                            :min="0" 
                            :max="50"
                            :show-tooltip="false"
                            @input="updateData"
                        />
                        <el-input-number 
                            v-model="localData.styles.padding_horizontal" 
                            :min="0" 
                            :max="50"
                            size="small"
                            controls-position="right"
                            class="slider-input"
                            @change="updateData"
                        />
                    </div>
                </div>
            </div>

            <div class="config-section">
                <h4 class="section-title">外边距设置</h4>

                <div class="form-group">
                    <label class="form-label">上边距</label>
                    <div class="slider-control">
                        <el-slider 
                            v-model="localData.styles.margin_top" 
                            :min="0" 
                            :max="50"
                            :show-tooltip="false"
                            @input="updateData"
                        />
                        <el-input-number 
                            v-model="localData.styles.margin_top" 
                            :min="0" 
                            :max="50"
                            size="small"
                            controls-position="right"
                            class="slider-input"
                            @change="updateData"
                        />
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">下边距</label>
                    <div class="slider-control">
                        <el-slider 
                            v-model="localData.styles.margin_bottom" 
                            :min="0" 
                            :max="50"
                            :show-tooltip="false"
                            @input="updateData"
                        />
                        <el-input-number 
                            v-model="localData.styles.margin_bottom" 
                            :min="0" 
                            :max="50"
                            size="small"
                            controls-position="right"
                            class="slider-input"
                            @change="updateData"
                        />
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">左右边距</label>
                    <div class="slider-control">
                        <el-slider 
                            v-model="localData.styles.margin_horizontal" 
                            :min="0" 
                            :max="50"
                            :show-tooltip="false"
                            @input="updateData"
                        />
                        <el-input-number 
                            v-model="localData.styles.margin_horizontal" 
                            :min="0" 
                            :max="50"
                            size="small"
                            controls-position="right"
                            class="slider-input"
                            @change="updateData"
                        />
                    </div>
                </div>
            </div>
        </div>

        <!-- 图片选择器 -->
        <ImageSelector
            v-model:visible="imageDialogVisible"
            :title="imageSelectorTitle"
            :multiple="false"
            :max-count="1"
            @select="handleImageSelect"
            @cancel="handleImageCancel"
        />

        <!-- 链接选择器 -->
        <el-dialog v-model="linkDialogVisible" title="选择链接" width="600px">
            <div class="link-selector">
                <p>链接选择功能开发中...</p>
            </div>
            <template #footer>
                <el-button @click="linkDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="confirmLinkSelection">确定</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Plus, Delete, ArrowRight, Check, InfoFilled } from '@element-plus/icons-vue'
import ImageSelector from '@/components/ImageSelector/index.vue'

const props = defineProps({
    data: {
        type: Object,
        required: true
    },
    activeTab: {
        type: String,
        default: 'content'
    }
})

const emit = defineEmits(['update'])

// 本地数据副本
const localData = ref({ ...props.data })

// 对话框状态
const imageDialogVisible = ref(false)
const linkDialogVisible = ref(false)
const imageSelectorTitle = ref('选择图片')

// 选中的图片索引
const selectedImageIndex = ref(0)

// 布局选项
const layoutOptions = ref([
    { value: 1, name: '一行一个' },
    { value: 2, name: '一行两个' },
    { value: 3, name: '一行三个' },
    { value: 4, name: '一行四个' },
    { value: 5, name: '一行五个' },
    { value: 6, name: '一行六个' },
    { value: 7, name: '左一右二' },
    { value: 8, name: '左二右二' },
    { value: 9, name: '上一下二' }
])

// 监听props变化，更新本地数据
watch(() => props.data, (newData) => {
    localData.value = { ...newData }
    // 确保图片数量与布局匹配
    ensureCorrectImageCount()
    // 重置选中索引
    if (selectedImageIndex.value >= localData.value.content.data.length) {
        selectedImageIndex.value = 0
    }
}, { deep: true })

// 计算属性
const maxImages = computed(() => {
    const style = localData.value.content.style
    switch (style) {
        case 1: return 1  // 一行一个，固定1个
        case 2: return 2  // 一行两个，固定2个
        case 3: return 3  // 一行三个，固定3个
        case 4: return 4  // 一行四个，固定4个
        case 5: return 5  // 一行五个，固定5个
        case 6: return 6  // 一行六个，固定6个
        case 7: return 3  // 左一右二，固定3个
        case 8: return 4  // 左二右二，固定4个
        case 9: return 3  // 上一下二，固定3个
        default: return 1
    }
})

// 当前选中的图片数据
const currentSelectedImage = computed(() => {
    if (selectedImageIndex.value >= 0 && selectedImageIndex.value < localData.value.content.data.length) {
        return localData.value.content.data[selectedImageIndex.value]
    }
    return createEmptyImage()
})

// 方法
const updateData = () => {
    emit('update', { ...localData.value })
}

// 选择布局
const selectLayout = (layoutValue) => {
    localData.value.content.style = layoutValue
    
    // 根据布局固定图片数量
    const requiredImages = maxImages.value
    const currentImages = localData.value.content.data
    
    if (currentImages.length < requiredImages) {
        // 添加空图片到所需数量
        while (localData.value.content.data.length < requiredImages) {
            localData.value.content.data.push(createEmptyImage())
        }
    } else if (currentImages.length > requiredImages) {
        // 删除多余图片，只保留所需数量
        localData.value.content.data = currentImages.slice(0, requiredImages)
    }
    
    // 重置选中索引，确保不超出范围
    if (selectedImageIndex.value >= localData.value.content.data.length) {
        selectedImageIndex.value = 0
    }
    
    updateData()
}

// 获取布局所需的图片数量（这个函数现在不需要了，直接使用maxImages）
const getRequiredImagesCount = (style) => {
    // 这个函数现在直接返回maxImages的值
    return maxImages.value
}

// 创建空图片对象
const createEmptyImage = () => ({
    url: '',
    link: {
        path: '',
        name: '',
        params: {},
        type: ''
    },
    width: 375,
    height: 133.4375,
    ratio: 512400
})

// 选择布局中的图片
const selectLayoutImage = (index) => {
    selectedImageIndex.value = index
}

// 选择图片
const selectImage = () => {
    imageSelectorTitle.value = `选择图片 ${selectedImageIndex.value + 1}`
    imageDialogVisible.value = true
}

// 删除当前选中的图片
const removeCurrentImage = () => {
    if (selectedImageIndex.value >= 0 && selectedImageIndex.value < localData.value.content.data.length) {
        localData.value.content.data[selectedImageIndex.value].url = ''
        updateData()
    }
}

// 选择背景图片
const selectBackgroundImage = () => {
    imageSelectorTitle.value = '选择背景图片'
    imageDialogVisible.value = true
}

// 删除背景图片
const removeBackgroundImage = () => {
    localData.value.styles.root_bg_url = ''
    updateData()
}

// 处理图片选择
const handleImageSelect = (selectedImage) => {
    console.log('选择的图片:', selectedImage)
    
    if (imageSelectorTitle.value === '选择背景图片') {
        // 背景图片
        localData.value.styles.root_bg_url = selectedImage.url || selectedImage.path
    } else {
        // 魔方图片
        const imageData = localData.value.content.data[selectedImageIndex.value]
        if (imageData) {
            imageData.url = selectedImage.url || selectedImage.path
            // 可以根据需要设置图片的宽高等属性
            if (selectedImage.width && selectedImage.height) {
                imageData.width = selectedImage.width
                imageData.height = selectedImage.height
                imageData.ratio = selectedImage.width * selectedImage.height
            }
        }
    }
    
    updateData()
    imageDialogVisible.value = false
}

// 处理图片选择取消
const handleImageCancel = () => {
    console.log('取消选择图片')
    imageDialogVisible.value = false
}

// 打开链接选择器
const openLinkSelector = () => {
    linkDialogVisible.value = true
}

// 确认链接选择
const confirmLinkSelection = () => {
    // 这里应该处理链接选择结果
    linkDialogVisible.value = false
}

// 获取链接显示文本
const getLinkDisplayText = (link) => {
    if (!link || !link.type) {
        return '请选择跳转链接'
    }
    
    switch (link.type) {
        case 'page':
            return `微页面: ${link.params.name || '未命名页面'}`
        case 'category':
            return `商品分类: ${link.params.name || '未命名分类'}`
        case 'goods':
            return `商品详情: ${link.params.name || '商品ID:' + link.params.id}`
        case 'url':
            return `外部链接: ${link.url || '未设置链接'}`
        default:
            return '未知链接类型'
    }
}

// 重置颜色
const resetRootBgColor = () => {
    localData.value.styles.root_bg_color = ''
    updateData()
}

// 初始化默认数据
if (!localData.value.content) {
    localData.value.content = {
        style: 1,
        data: [createEmptyImage()] // 默认一行一个，只需要1张图片
    }
}

if (!localData.value.styles) {
    localData.value.styles = {
        border_radius: 0,
        root_bg_color: '',
        root_bg_url: '',
        cube_border_radius: 0,
        font_color: '#333',
        padding_top: 18,
        padding_horizontal: 0,
        padding_bottom: 10,
        margin_top: 0,
        margin_bottom: 0,
        margin_horizontal: 0
    }
}

// 确保图片数量与当前布局匹配
const ensureCorrectImageCount = () => {
    const requiredCount = maxImages.value
    const currentCount = localData.value.content.data.length
    
    if (currentCount < requiredCount) {
        // 添加空图片
        while (localData.value.content.data.length < requiredCount) {
            localData.value.content.data.push(createEmptyImage())
        }
    } else if (currentCount > requiredCount) {
        // 删除多余图片
        localData.value.content.data = localData.value.content.data.slice(0, requiredCount)
    }
}

// 初始化时确保图片数量正确
ensureCorrectImageCount()

// 确保有默认选中的图片
if (localData.value.content.data.length > 0) {
    selectedImageIndex.value = 0
}

// 获取宽度提示
const getWidthHint = (index) => {
    const style = localData.value.content.style
    
    // 根据不同布局样式返回相应的宽度提示
    switch (style) {
        case 1: // 一行一个
            return '750x不限高度'
        case 2: // 一行两个
            return '375x不限高度'
        case 3: // 一行三个
            return '250x不限高度'
        case 4: // 一行四个
            return '187.5x不限'
        case 5: // 一行五个
            return '150x不限'
        case 6: // 一行六个
            return '125x不限'
        case 7: // 左一右二
            if (index === 0) {
                return '375x不限高度' // 左侧大图
            } else {
                return '375x不限高度' // 右侧小图
            }
        case 8: // 左二右二
            return '375x不限高度'
        case 9: // 上一下二
            if (index === 0) {
                return '750x不限高度' // 上方大图
            } else {
                return '375x不限高度' // 下方小图
            }
        default:
            return '375x不限高度'
    }
}
</script>

<style scoped>
.image-magic-config {
    padding: 0;
}

.config-section {
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.config-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.section-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin: 0 0 16px 0;
    padding: 0;
}

.form-group {
    margin-bottom: 16px;
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-label {
    display: block;
    font-size: 13px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

/* 魔方布局预览样式 */
.magic-layout-preview {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 4px;
    background: #fafafa;
}

.layout-container {
    display: flex;
    flex-wrap: wrap;
    gap: 1px;
    /* min-height: 120px; */
    align-content: flex-start;
}

.layout-item {
    position: relative;
    border: 2px solid #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.2s;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.layout-item:hover {
    border-color: #409eff;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.layout-item.active {
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.layout-item.has-image {
    border-color: #67c23a;
}

.layout-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.placeholder-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 11px;
    height: 100%;
    width: 100%;
    text-align: center;
    line-height: 1.2;
}

.width-hint {
    font-size: 10px;
    color: #666;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 95%;
    line-height: 1.1;
}

/* 为小框布局特别优化 */
.layout-style-4 .width-hint,
.layout-style-5 .width-hint,
.layout-style-6 .width-hint {
    font-size: 9px;
    white-space: normal;
    word-break: break-all;
    line-height: 1.0;
    max-width: 100%;
}

.selected-indicator {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 20px;
    height: 20px;
    background: #409eff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
}

/* 不同布局样式 */
.layout-style-1 .layout-item {
    width: 100%;
    height: 60px;
    margin-bottom: 4px;
}

.layout-style-2 .layout-item {
    width: calc(50% - 2px);
    height: 60px;
}

.layout-style-3 .layout-item {
    width: calc(33.333% - 0.68px);
    height: 60px;
}

.layout-style-4 .layout-item {
    width: calc(25% - 0.75px);
    height: 60px;
}

.layout-style-5 .layout-item {
    width: calc(20% - 0.8px);
    height: 60px;
}

.layout-style-6 .layout-item {
    width: calc(16.666% - 0.83px);
    height: 60px;
}

/* 左一右二布局 */
.layout-style-7 {
    display: flex !important;
    flex-direction: row !important;
    gap: 4px !important;
    height: 128px !important;
    min-height: 128px !important;
}

.layout-style-7 .layout-item:nth-child(1) {
    width: calc(50% - 2px) !important;
    height: 100% !important;
    flex-shrink: 0 !important;
}

.layout-style-7 .layout-item:nth-child(2),
.layout-style-7 .layout-item:nth-child(3) {
    width: calc(50% - 2px) !important;
    height: calc(50% - 2px) !important;
    flex-shrink: 0 !important;
}

/* 右侧容器 */
.layout-style-7 .layout-item:nth-child(2) {
    margin-bottom: 4px !important;
}

/* 使用CSS Grid来实现更精确的布局 */
.layout-style-7 {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    grid-template-rows: 1fr 1fr !important;
    gap: 4px !important;
    height: 128px !important;
    min-height: 128px !important;
}

.layout-style-7 .layout-item:nth-child(1) {
    grid-row: 1 / 3 !important;
    grid-column: 1 / 2 !important;
    width: 100% !important;
    height: 100% !important;
}

.layout-style-7 .layout-item:nth-child(2) {
    grid-row: 1 / 2 !important;
    grid-column: 2 / 3 !important;
    width: 100% !important;
    height: 100% !important;
}

.layout-style-7 .layout-item:nth-child(3) {
    grid-row: 2 / 3 !important;
    grid-column: 2 / 3 !important;
    width: 100% !important;
    height: 100% !important;
}

/* 左二右二布局 */
.layout-style-8 {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    grid-template-rows: 1fr 1fr !important;
    gap: 4px !important;
    height: 128px !important;
    min-height: 128px !important;
}

/* 按照实际组件的布局逻辑：左侧显示图片1和图片2，右侧显示图片3和图片4 */
.layout-style-8 .layout-item:nth-child(1) {
    grid-row: 1 / 2 !important;
    grid-column: 1 / 2 !important;
    width: 100% !important;
    height: 100% !important;
}

.layout-style-8 .layout-item:nth-child(2) {
    grid-row: 2 / 3 !important;
    grid-column: 1 / 2 !important;
    width: 100% !important;
    height: 100% !important;
}

.layout-style-8 .layout-item:nth-child(3) {
    grid-row: 1 / 2 !important;
    grid-column: 2 / 3 !important;
    width: 100% !important;
    height: 100% !important;
}

.layout-style-8 .layout-item:nth-child(4) {
    grid-row: 2 / 3 !important;
    grid-column: 2 / 3 !important;
    width: 100% !important;
    height: 100% !important;
}

/* 上一下二布局 */
.layout-style-9 {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 4px !important;
    height: 128px !important;
    min-height: 128px !important;
    align-content: flex-start !important;
}

.layout-style-9 .layout-item:nth-child(1) {
    width: 100% !important;
    height: calc(50% - 2px) !important;
    order: 1;
}

.layout-style-9 .layout-item:nth-child(2) {
    width: calc(50% - 2px) !important;
    height: calc(50% - 2px) !important;
    order: 2;
}

.layout-style-9 .layout-item:nth-child(3) {
    width: calc(50% - 2px) !important;
    height: calc(50% - 2px) !important;
    order: 3;
}

/* 图片设置样式 */
.image-setting-item {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 16px;
    background: #fafafa;
}

.image-upload-control {
    display: flex;
    align-items: center;
    gap: 12px;
}

.image-preview-small {
    width: 60px;
    height: 60px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    flex-shrink: 0;
}

.image-preview-small img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.placeholder-small {
    color: #999;
    font-size: 20px;
}

.upload-actions {
    display: flex;
    gap: 8px;
    flex: 1;
}

.no-selection-tip {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #999;
    font-size: 14px;
    background: #fafafa;
    border-radius: 8px;
    border: 1px dashed #e0e0e0;
}

.no-selection-tip .el-icon {
    margin-right: 8px;
    font-size: 16px;
}

/* 其他样式保持不变 */
.color-control {
    display: flex;
    align-items: center;
    gap: 8px;
}

.color-control .el-color-picker {
    flex-shrink: 0;
}

.color-control .el-input {
    flex: 1;
}

.slider-control {
    display: flex;
    align-items: center;
    gap: 12px;
}

.slider-control .el-slider {
    flex: 1;
}

.slider-input {
    width: 80px;
    flex-shrink: 0;
}

.image-upload {
    width: 100%;
}

.image-preview {
    width: 100%;
    height: 120px;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    background: #fafafa;
    transition: border-color 0.3s;
}

.image-preview:hover {
    border-color: #409eff;
}

.image-preview img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #999;
    font-size: 14px;
}

.placeholder .el-icon {
    font-size: 24px;
    margin-bottom: 8px;
}

.delete-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s;
}

.image-preview:hover .delete-btn {
    opacity: 1;
}

.delete-btn:hover {
    background: rgba(255, 0, 0, 0.8);
}
</style> 