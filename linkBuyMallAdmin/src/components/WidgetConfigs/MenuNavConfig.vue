<template>
    <div class="menu-nav-config">
        <!-- 内容Tab -->
        <div v-show="activeTab === 'content'" class="content-properties">
            <div class="config-section">
                <h4 class="section-title">导航样式</h4>
                
                <div class="form-group">
                    <label class="form-label">显示类型</label>
                    <div class="radio-group">
                        <el-radio v-model="localData.styles.nav_style" :label="1" @change="updateData">固定显示</el-radio>
                        <el-radio v-model="localData.styles.nav_style" :label="2" @change="updateData">分页滑动</el-radio>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">行数</label>
                    <div class="slider-control">
                        <el-slider v-model="localData.styles.nav_line" :min="1" :max="5" :show-tooltip="false"
                            @input="updateData" />
                        <el-input-number v-model="localData.styles.nav_line" :min="1" :max="5" size="small"
                            controls-position="right" class="slider-input" @change="updateData" />
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">每行数量</label>
                    <div class="slider-control">
                        <el-slider v-model="localData.styles.nav_line_num" :min="2" :max="6" :show-tooltip="false"
                            @input="updateData" />
                        <el-input-number v-model="localData.styles.nav_line_num" :min="2" :max="6" size="small"
                            controls-position="right" class="slider-input" @change="updateData" />
                    </div>
                </div>

                <div v-if="localData.styles.nav_style === 2" class="config-section">
                    <h4 class="section-title">指示器设置</h4>

                    <div class="form-group">
                        <label class="form-label">指示器样式</label>
                        <el-select 
                            v-model="localData.styles.indicator_style" 
                            placeholder="选择指示器样式"
                            @change="updateData"
                        >
                            <el-option label="圆角" :value="1" />
                            <el-option label="圆形" :value="2" />
                            <el-option label="数字" :value="3" />
                        </el-select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">指示器位置</label>
                        <el-select 
                            v-model="localData.styles.indicator_align" 
                            placeholder="选择指示器位置"
                            @change="updateData"
                        >
                            <el-option label="居左" value="left" />
                            <el-option label="居中" value="center" />
                            <el-option label="居右" value="right" />
                        </el-select>
                    </div>

                    <div v-if="localData.styles.indicator_style !== 3" class="form-group">
                        <label class="form-label">选中颜色</label>
                        <div class="color-control">
                            <el-color-picker v-model="localData.styles.indicator_color" @change="updateData" size="small" />
                            <el-input v-model="localData.styles.indicator_color" placeholder="#FF2C3C" @input="updateData" />
                            <el-button type="primary" text size="small" @click="resetIndicatorColor">重置</el-button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="config-section">
                <h4 class="section-title">菜单导航</h4>
                
                <div class="nav-list">
                    <div v-for="(nav, index) in localData.content.data" :key="index" class="nav-item">
                        <div class="nav-item-header">
                            <span class="nav-item-title">导航 {{ index + 1 }}</span>
                            <div class="nav-item-actions">
                                <el-button 
                                    v-if="localData.content.data.length > 1" 
                                    type="danger" 
                                    text 
                                    size="small" 
                                    @click="removeNavItem(index)"
                                >
                                    删除
                                </el-button>
                            </div>
                        </div>
                        
                        <div class="nav-item-content">
                            <div class="form-group">
                                <label class="form-label">图标</label>
                                <div class="image-upload">
                                    <div class="image-preview" @click="selectImage(index)">
                                        <img v-if="nav.url" :src="nav.url" alt="导航图标" />
                                        <div v-else class="placeholder">
                                            <el-icon><Plus /></el-icon>
                                        </div>
                                        <!-- 删除按钮 -->
                                        <div v-if="nav.url" 
                                             class="delete-btn" 
                                             @click.stop="removeNavIcon(index)"
                                             title="删除图标">
                                            <el-icon>
                                                <Delete />
                                            </el-icon>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">名称</label>
                                <el-input 
                                    v-model="nav.name" 
                                    placeholder="导航名称" 
                                    maxlength="6"
                                    show-word-limit
                                    @input="updateData" 
                                />
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">链接地址</label>
                                <el-input 
                                    :model-value="getLinkDisplayText(nav.link)" 
                                    placeholder="请选择跳转链接"
                                    readonly
                                    @click="openLinkSelector(index)"
                                >
                                    <template #suffix>
                                        <el-icon><ArrowRight /></el-icon>
                                    </template>
                                </el-input>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="nav-actions">
                    <el-button 
                        v-if="localData.content.data.length < 30" 
                        type="primary" 
                        @click="addNavItem"
                        style="width: 100%;"
                    >
                        <el-icon><Plus /></el-icon>
                        添加导航 ({{ localData.content.data.length }}/30)
                    </el-button>
                </div>
            </div>
        </div>

        <!-- 样式Tab -->
        <div v-show="activeTab === 'style'" class="style-properties">
            <div class="config-section">
                <h4 class="section-title">颜色设置</h4>

                <div class="form-group">
                    <label class="form-label">底部背景</label>
                    <div class="color-control">
                        <el-color-picker v-model="localData.styles.root_bg_color" @change="updateData" size="small" />
                        <el-input v-model="localData.styles.root_bg_color" placeholder="透明" @input="updateData" />
                        <el-button type="primary" text size="small" @click="resetRootBgColor">重置</el-button>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">组件背景</label>
                    <div class="color-control">
                        <el-color-picker v-model="localData.styles.bg_color" @change="updateData" size="small" />
                        <el-input v-model="localData.styles.bg_color" placeholder="#FFFFFF" @input="updateData" />
                        <el-button type="primary" text size="small" @click="resetBgColor">重置</el-button>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">文字颜色</label>
                    <div class="color-control">
                        <el-color-picker v-model="localData.styles.color" @change="updateData" size="small" />
                        <el-input v-model="localData.styles.color" placeholder="#333333" @input="updateData" />
                        <el-button type="primary" text size="small" @click="resetTextColor">重置</el-button>
                    </div>
                </div>
            </div>

            <div class="config-section">
                <h4 class="section-title">外边距设置</h4>

                <div class="form-group">
                    <label class="form-label">上边距</label>
                    <div class="slider-control">
                        <el-slider v-model="localData.styles.padding_top" :min="0" :max="50" :show-tooltip="false"
                            @input="updateData" />
                        <el-input-number v-model="localData.styles.padding_top" :min="0" :max="50" size="small"
                            controls-position="right" class="slider-input" @change="updateData" />
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">下边距</label>
                    <div class="slider-control">
                        <el-slider v-model="localData.styles.padding_bottom" :min="0" :max="50" :show-tooltip="false"
                            @input="updateData" />
                        <el-input-number v-model="localData.styles.padding_bottom" :min="0" :max="50" size="small"
                            controls-position="right" class="slider-input" @change="updateData" />
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">左右边距</label>
                    <div class="slider-control">
                        <el-slider v-model="localData.styles.padding_horizontal" :min="0" :max="50" :show-tooltip="false"
                            @input="updateData" />
                        <el-input-number v-model="localData.styles.padding_horizontal" :min="0" :max="50" size="small"
                            controls-position="right" class="slider-input" @change="updateData" />
                    </div>
                </div>
            </div>

            <div class="config-section">
                <h4 class="section-title">内边距设置</h4>

                <div class="form-group">
                    <label class="form-label">上内边距</label>
                    <div class="slider-control">
                        <el-slider v-model="localData.styles.inner_padding_top" :min="0" :max="50" :show-tooltip="false"
                            @input="updateData" />
                        <el-input-number v-model="localData.styles.inner_padding_top" :min="0" :max="50" size="small"
                            controls-position="right" class="slider-input" @change="updateData" />
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">下内边距</label>
                    <div class="slider-control">
                        <el-slider v-model="localData.styles.inner_padding_bottom" :min="0" :max="50" :show-tooltip="false"
                            @input="updateData" />
                        <el-input-number v-model="localData.styles.inner_padding_bottom" :min="0" :max="50" size="small"
                            controls-position="right" class="slider-input" @change="updateData" />
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">左右内边距</label>
                    <div class="slider-control">
                        <el-slider v-model="localData.styles.inner_padding_horizontal" :min="0" :max="50" :show-tooltip="false"
                            @input="updateData" />
                        <el-input-number v-model="localData.styles.inner_padding_horizontal" :min="0" :max="50" size="small"
                            controls-position="right" class="slider-input" @change="updateData" />
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">上下间隔</label>
                    <div class="slider-control">
                        <el-slider v-model="localData.styles.row_gap" :min="0" :max="50" :show-tooltip="false"
                            @input="updateData" />
                        <el-input-number v-model="localData.styles.row_gap" :min="0" :max="50" size="small"
                            controls-position="right" class="slider-input" @change="updateData" />
                    </div>
                </div>
            </div>

            <div class="config-section">
                <h4 class="section-title">图片设置</h4>

                <div class="form-group">
                    <label class="form-label">宽</label>
                    <div class="slider-control">
                        <el-slider v-model="localData.styles.img_width" :min="20" :max="80" :show-tooltip="false"
                            @input="updateData" />
                        <el-input-number v-model="localData.styles.img_width" :min="20" :max="80" size="small"
                            controls-position="right" class="slider-input" @change="updateData" />
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">高</label>
                    <div class="slider-control">
                        <el-slider v-model="localData.styles.img_height" :min="20" :max="80" :show-tooltip="false"
                            @input="updateData" />
                        <el-input-number v-model="localData.styles.img_height" :min="20" :max="80" size="small"
                            controls-position="right" class="slider-input" @change="updateData" />
                    </div>
                </div>
            </div>

            <div class="config-section">
                <h4 class="section-title">圆角设置</h4>

                <div class="form-group">
                    <label class="form-label">上圆角</label>
                    <div class="slider-control">
                        <el-slider v-model="localData.styles.border_radius_top" :min="0" :max="50" :show-tooltip="false"
                            @input="updateData" />
                        <el-input-number v-model="localData.styles.border_radius_top" :min="0" :max="50" size="small"
                            controls-position="right" class="slider-input" @change="updateData" />
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">下圆角</label>
                    <div class="slider-control">
                        <el-slider v-model="localData.styles.border_radius_bottom" :min="0" :max="50" :show-tooltip="false"
                            @input="updateData" />
                        <el-input-number v-model="localData.styles.border_radius_bottom" :min="0" :max="50" size="small"
                            controls-position="right" class="slider-input" @change="updateData" />
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">图片圆角</label>
                    <div class="slider-control">
                        <el-slider v-model="localData.styles.img_border_radius" :min="0" :max="50" :show-tooltip="false"
                            @input="updateData" />
                        <el-input-number v-model="localData.styles.img_border_radius" :min="0" :max="50" size="small"
                            controls-position="right" class="slider-input" @change="updateData" />
                    </div>
                </div>
            </div>
        </div>

        <!-- 图片选择器 -->
        <ImageSelector
            v-model:visible="imageSelector.visible"
            :title="imageSelector.title"
            :multiple="imageSelector.multiple"
            :max-count="imageSelector.maxCount"
            :initial-selected="imageSelector.initialSelected"
            @select="handleImageSelect"
            @cancel="handleImageCancel"
        />
    </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ArrowRight, Plus, Delete } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import ImageSelector from '@/components/ImageSelector/index.vue'

const props = defineProps({
    data: {
        type: Object,
        required: true
    },
    activeTab: {
        type: String,
        default: 'content'
    }
})

const emit = defineEmits(['update'])

// 初始化本地数据
const initializeData = () => {
    const data = { ...props.data }
    // 确保内边距字段存在，如果不存在则设置默认值
    if (!data.styles) {
        data.styles = {}
    }
    if (data.styles.inner_padding_top === undefined) {
        data.styles.inner_padding_top = 0
    }
    if (data.styles.inner_padding_horizontal === undefined) {
        data.styles.inner_padding_horizontal = 0
    }
    if (data.styles.inner_padding_bottom === undefined) {
        data.styles.inner_padding_bottom = 0
    }
    return data
}

// 本地数据副本
const localData = ref(initializeData())

// 图片选择器状态
const imageSelector = ref({
    visible: false,
    title: '选择图标',
    multiple: false,
    maxCount: 1,
    initialSelected: [],
    currentIndex: -1, // 当前编辑的导航项索引
})

// 监听props变化，更新本地数据
watch(() => props.data, (newData) => {
    localData.value = initializeData()
}, { deep: true })

// 更新数据
const updateData = () => {
    emit('update', { ...localData.value })
}

// 添加导航项
const addNavItem = () => {
    if (localData.value.content.data.length < 30) {
        localData.value.content.data.push({
            url: '',
            name: '导航',
            link: {}
        })
        updateData()
    }
}

// 删除导航项
const removeNavItem = (index) => {
    if (localData.value.content.data.length > 1) {
        localData.value.content.data.splice(index, 1)
        updateData()
    }
}

// 选择图片
const selectImage = (index) => {
    imageSelector.value = {
        visible: true,
        title: '选择导航图标',
        multiple: false,
        maxCount: 1,
        initialSelected: localData.value.content.data[index].url ? [{
            id: Date.now(),
            url: localData.value.content.data[index].url,
            name: '当前图标'
        }] : [],
        currentIndex: index
    }
}

// 获取链接显示文本
const getLinkDisplayText = (link) => {
    if (link && link.name) {
        return `已设置：${link.name}`
    }
    return '请选择跳转链接'
}

// 打开链接选择器
const openLinkSelector = (index) => {
    // 这里应该打开链接选择弹窗
    // 暂时用prompt代替
    const linkName = prompt('请输入链接名称:', localData.value.content.data[index].link?.name || '')
    if (linkName !== null) {
        if (!localData.value.content.data[index].link) {
            localData.value.content.data[index].link = {}
        }
        localData.value.content.data[index].link.name = linkName
        updateData()
    }
}

// 颜色重置方法
const resetRootBgColor = () => {
    localData.value.styles.root_bg_color = ''
    updateData()
}

const resetBgColor = () => {
    localData.value.styles.bg_color = '#FFFFFF'
    updateData()
}

const resetTextColor = () => {
    localData.value.styles.color = '#333333'
    updateData()
}

const resetIndicatorColor = () => {
    localData.value.styles.indicator_color = '#FF2C3C'
    updateData()
}

// 删除导航图标
const removeNavIcon = (index) => {
    localData.value.content.data[index].url = ''
    updateData()
    ElMessage.success('图标已删除')
}

// 处理图片选择
const handleImageSelect = (selectedImage) => {
    const index = imageSelector.value.currentIndex
    localData.value.content.data[index].url = selectedImage.url || selectedImage.path
    updateData()
    imageSelector.value.visible = false
    ElMessage.success('图标设置成功')
}

// 处理图片选择取消
const handleImageCancel = () => {
    imageSelector.value.visible = false
}
</script>

<style scoped>
/* 配置面板样式 */
.menu-nav-config {
    height: 100%;
    max-height: calc(100vh - 200px);
    overflow-y: auto;
    overflow-x: hidden;
    padding-right: 20px;
    margin-right: -20px;
}

.menu-nav-config::-webkit-scrollbar {
    width: 8px;
}

.menu-nav-config::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.menu-nav-config::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.menu-nav-config::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.content-properties,
.style-properties {
    padding-bottom: 20px;
}

.config-section {
    margin-bottom: 20px;
    border-bottom: 1px solid #e4e7ed;
    padding-bottom: 16px;
}

.config-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.section-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin: 0 0 12px 0;
}

.form-group {
    margin-bottom: 16px;
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 6px;
}

.color-control {
    display: flex;
    align-items: center;
    gap: 8px;
}

.slider-control {
    display: flex;
    align-items: center;
    gap: 12px;
    padding-left: 12px;
}

.slider-control .el-slider {
    flex: 1;
}

.slider-input {
    width: 80px;
    flex-shrink: 0;
}

/* 导航列表样式 */
.nav-list {
    margin-bottom: 16px;
}

.nav-item {
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    margin-bottom: 12px;
    overflow: hidden;
}

.nav-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #e4e7ed;
}

.nav-item-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.nav-item-content {
    padding: 16px;
}

.nav-actions {
    margin-top: 16px;
}

/* 图片上传样式 */
.image-upload {
    margin-bottom: 8px;
}

.image-preview {
    width: 40px;
    height: 40px;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: border-color 0.3s;
    overflow: hidden;
    position: relative;
}

.image-preview:hover {
    border-color: #409eff;
}

.image-preview:hover .delete-btn {
    opacity: 1;
}

.image-preview img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
    color: #999;
    font-size: 10px;
    text-align: center;
    padding: 2px;
}

.placeholder .el-icon {
    font-size: 14px;
}

/* 删除按钮样式 */
.delete-btn {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 16px;
    height: 16px;
    background: rgba(255, 0, 0, 0.8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 10;
}

.delete-btn:hover {
    background: rgba(255, 0, 0, 1);
    transform: scale(1.1);
}

.delete-btn .el-icon {
    font-size: 10px;
    color: white;
}
</style>
