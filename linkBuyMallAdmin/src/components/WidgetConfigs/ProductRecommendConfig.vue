<template>
    <div class="product-recommend-config">
        <!-- 内容配置 -->
        <div v-if="activeTab === 'content'" class="config-content">
            <!-- 基础设置 -->
            <div class="config-section">
                <h4 class="section-title">基础设置</h4>
                
                <div class="form-group">
                    <label class="form-label">推荐类型</label>
                    <el-select v-model="localData.content.recommend_type" placeholder="请选择推荐类型" @change="handleDataChange">
                        <el-option label="热销商品" value="hot" />
                        <el-option label="新品推荐" value="new" />
                        <el-option label="相似商品" value="similar" />
                        <el-option label="自定义商品" value="custom" />
                    </el-select>
                    <div class="form-tip">
                        根据系统算法，推荐指定数量的商品。优先推荐高销量且排序在前的商品。
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">最大显示数量</label>
                    <el-input-number 
                        v-model="localData.content.max_display" 
                        :min="1" 
                        :max="20" 
                        @input="handleDataChange"
                        @change="handleDataChange"
                    />
                </div>
            </div>



            <!-- 商品显示设置 -->
            <div class="config-section">
                <h4 class="section-title">商品显示设置</h4>
                
                <div class="form-group">
                    <label class="form-label">显示推荐标签</label>
                    <el-switch v-model="localData.content.show_recommend_tag" @change="handleDataChange" />
                </div>

                <div v-if="localData.content.show_recommend_tag" class="form-group">
                    <label class="form-label">推荐标签文字</label>
                    <el-input v-model="localData.content.recommend_tag_text" placeholder="请输入推荐标签文字" @input="handleDataChange" />
                </div>

                <div class="form-group">
                    <label class="form-label">显示原价</label>
                    <el-switch v-model="localData.content.show_original_price" @change="handleDataChange" />
                </div>

                <div class="form-group">
                    <label class="form-label">显示销量</label>
                    <el-switch v-model="localData.content.show_sales" @change="handleDataChange" />
                </div>

                <div class="form-group">
                    <label class="form-label">显示购买按钮</label>
                    <el-switch v-model="localData.content.show_action_btn" @change="handleDataChange" />
                </div>

                <div v-if="localData.content.show_action_btn" class="form-group">
                    <label class="form-label">购买按钮文字</label>
                    <el-input v-model="localData.content.action_btn_text" placeholder="请输入购买按钮文字" @input="handleDataChange" />
                </div>
            </div>


        </div>

        <!-- 样式配置 -->
        <div v-if="activeTab === 'style'" class="config-content">
            <!-- 布局设置 -->
            <div class="config-section">
                <h4 class="section-title">布局设置</h4>
                
                <div class="form-group">
                    <label class="form-label">列数</label>
                    <el-select v-model="localData.styles.columns" @change="handleDataChange">
                        <el-option label="1列" :value="1" />
                        <el-option label="2列" :value="2" />
                    </el-select>
                </div>

                <div class="form-group">
                    <label class="form-label">圆角大小</label>
                    <div class="slider-control">
                        <el-slider v-model="localData.styles.border_radius" :min="0" :max="20" :show-tooltip="false" @input="handleDataChange" />
                        <el-input-number v-model="localData.styles.border_radius" :min="0" :max="20" size="small" controls-position="right" class="slider-input" @change="handleDataChange" />
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">间距大小</label>
                    <div class="slider-control">
                        <el-slider v-model="localData.styles.item_spacing" :min="0" :max="30" :show-tooltip="false" @input="handleDataChange" />
                        <el-input-number v-model="localData.styles.item_spacing" :min="0" :max="30" size="small" controls-position="right" class="slider-input" @change="handleDataChange" />
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">内边距</label>
                    <div class="slider-control">
                        <el-slider v-model="localData.styles.padding" :min="0" :max="30" :show-tooltip="false" @input="handleDataChange" />
                        <el-input-number v-model="localData.styles.padding" :min="0" :max="30" size="small" controls-position="right" class="slider-input" @change="handleDataChange" />
                    </div>
                </div>
            </div>

            <!-- 颜色设置 -->
            <div class="config-section">
                <div class="section-header">
                    <h4 class="section-title">颜色设置</h4>
                    <el-button type="primary" text size="small" @click="applyThemeColors">
                        <el-icon><Brush /></el-icon>
                        应用主题色
                    </el-button>
                </div>
                
                <div class="form-group">
                    <label class="form-label">根背景色</label>
                    <div class="color-control">
                        <el-color-picker v-model="localData.styles.root_bg_color" @active-change="handleDataChange" @change="handleDataChange" size="small" />
                        <el-input v-model="localData.styles.root_bg_color" placeholder="#f5f5f5" @input="handleDataChange" />
                        <el-button type="primary" text size="small" @click="resetRootBgColor">
                            {{ themeColorMethods.getResetButtonText('root_bg_color') }}
                        </el-button>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">内容背景色</label>
                    <div class="color-control">
                        <el-color-picker v-model="localData.styles.bg_color" @active-change="handleDataChange" @change="handleDataChange" size="small" />
                        <el-input v-model="localData.styles.bg_color" placeholder="#ffffff" @input="handleDataChange" />
                        <el-button type="primary" text size="small" @click="resetBgColor">
                            {{ themeColorMethods.getResetButtonText('bg_color') }}
                        </el-button>
                    </div>
                </div>
            </div>



            <!-- 商品样式 -->
            <div class="config-section">
                <h4 class="section-title">商品样式</h4>
                
                <div class="form-group">
                    <label class="form-label">商品名称颜色</label>
                    <div class="color-control">
                        <el-color-picker v-model="localData.styles.name_color" @active-change="handleDataChange" @change="handleDataChange" size="small" />
                        <el-input v-model="localData.styles.name_color" placeholder="#333333" @input="handleDataChange" />
                        <el-button type="primary" text size="small" @click="resetNameColor">重置</el-button>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">商品名称大小</label>
                    <div class="slider-control">
                        <el-slider v-model="localData.styles.name_font_size" :min="12" :max="18" :show-tooltip="false" @input="handleDataChange" />
                        <el-input-number v-model="localData.styles.name_font_size" :min="12" :max="18" size="small" controls-position="right" class="slider-input" @change="handleDataChange" />
                    </div>
                </div>



                <div class="form-group">
                    <label class="form-label">描述大小</label>
                    <div class="slider-control">
                        <el-slider v-model="localData.styles.desc_font_size" :min="10" :max="16" :show-tooltip="false" @input="handleDataChange" />
                        <el-input-number v-model="localData.styles.desc_font_size" :min="10" :max="16" size="small" controls-position="right" class="slider-input" @change="handleDataChange" />
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">现价颜色</label>
                    <div class="color-control">
                        <el-color-picker v-model="localData.styles.price_color" @active-change="handleDataChange" @change="handleDataChange" size="small" />
                        <el-input v-model="localData.styles.price_color" placeholder="#ff4757" @input="handleDataChange" />
                        <el-button type="primary" text size="small" @click="resetPriceColor">重置</el-button>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">原价颜色</label>
                    <div class="color-control">
                        <el-color-picker v-model="localData.styles.scribing_price_color" @active-change="handleDataChange" @change="handleDataChange" size="small" />
                        <el-input v-model="localData.styles.scribing_price_color" placeholder="#999999" @input="handleDataChange" />
                        <el-button type="primary" text size="small" @click="resetScribingPriceColor">重置</el-button>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">销量颜色</label>
                    <div class="color-control">
                        <el-color-picker v-model="localData.styles.sales_color" @active-change="handleDataChange" @change="handleDataChange" size="small" />
                        <el-input v-model="localData.styles.sales_color" placeholder="#999999" @input="handleDataChange" />
                        <el-button type="primary" text size="small" @click="resetSalesColor">重置</el-button>
                    </div>
                </div>
            </div>

            <!-- 标签和按钮样式 -->
            <div class="config-section">
                <h4 class="section-title">标签和按钮样式</h4>
                
                <div class="form-group">
                    <label class="form-label">推荐标签背景色</label>
                    <div class="color-control">
                        <el-color-picker v-model="localData.styles.tag_bg_color" @active-change="handleDataChange" @change="handleDataChange" size="small" />
                        <el-input v-model="localData.styles.tag_bg_color" placeholder="#ff4757" @input="handleDataChange" />
                        <el-button type="primary" text size="small" @click="resetTagBgColor">重置</el-button>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">推荐标签文字颜色</label>
                    <div class="color-control">
                        <el-color-picker v-model="localData.styles.tag_text_color" @active-change="handleDataChange" @change="handleDataChange" size="small" />
                        <el-input v-model="localData.styles.tag_text_color" placeholder="#ffffff" @input="handleDataChange" />
                        <el-button type="primary" text size="small" @click="resetTagTextColor">重置</el-button>
                    </div>
                </div>



                <div class="form-group">
                    <label class="form-label">按钮背景色</label>
                    <div class="color-control">
                        <el-color-picker v-model="localData.styles.btn_bg_color" @active-change="handleDataChange" @change="handleDataChange" size="small" />
                        <el-input v-model="localData.styles.btn_bg_color" placeholder="#409EFF" @input="handleDataChange" />
                        <el-button type="primary" text size="small" @click="resetBtnBgColor">重置</el-button>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">按钮文字颜色</label>
                    <div class="color-control">
                        <el-color-picker v-model="localData.styles.btn_color" @active-change="handleDataChange" @change="handleDataChange" size="small" />
                        <el-input v-model="localData.styles.btn_color" placeholder="#ffffff" @input="handleDataChange" />
                        <el-button type="primary" text size="small" @click="resetBtnColor">重置</el-button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { Brush } from '@element-plus/icons-vue'
import { useThemeAware } from './mixins/themeAware.js'


// Props定义
const props = defineProps({
    data: {
        type: Object,
        required: true
    },
    activeTab: {
        type: String,
        default: 'content'
    }
})

// Emits定义
const emit = defineEmits(['update'])

// 主题感知功能
const { currentThemeColors, getThemeColorResetMethods } = useThemeAware()
const themeColorMethods = getThemeColorResetMethods('product-recommend')

// 本地数据 - 从props初始化
const localData = ref({
    content: {},
    styles: {}
})

// 初始化数据
const initData = () => {
    if (props.data) {
        console.log('ProductRecommendConfig - 接收到的data:', props.data)
        
        // 确保数据结构完整，特别是data字段
        const contentData = props.data.content || {}
        const stylesData = props.data.styles || {}
        
        // 确保data字段存在且是数组
        if (!contentData.data || !Array.isArray(contentData.data)) {
            console.warn('ProductRecommendConfig - data字段缺失或不是数组，使用空数组')
            contentData.data = []
        }
        
        localData.value = {
            content: { ...contentData },
            styles: { ...stylesData }
        }
        console.log('ProductRecommendConfig - 初始化后的localData:', localData.value)
        console.log('ProductRecommendConfig - 商品数据长度:', localData.value.content.data?.length || 0)
    } else {
        console.log('ProductRecommendConfig - 没有接收到data')
        // 如果没有数据，初始化为空结构
        localData.value = {
            content: { data: [] },
            styles: {}
        }
    }
}

// 监听props变化
watch(() => props.data, (newData) => {
    if (newData) {
        initData()
    }
}, { immediate: true, deep: true })

// 数据变化处理
const handleDataChange = () => {
    console.log('ProductRecommendConfig - 数据变化，准备发送更新:', localData.value)
    console.log('ProductRecommendConfig - 发送的商品数据长度:', localData.value.content?.data?.length || 0)
    emit('update', localData.value)
}

// 颜色重置函数 - 使用主题颜色
const resetRootBgColor = () => {
    localData.value.styles.root_bg_color = themeColorMethods.resetToThemeColor('root_bg_color')
    handleDataChange()
}

const resetBgColor = () => {
    localData.value.styles.bg_color = themeColorMethods.resetToThemeColor('bg_color') || '#ffffff'
    handleDataChange()
}

const resetNameColor = () => {
    localData.value.styles.name_color = themeColorMethods.resetToThemeColor('name_color') || '#333333'
    handleDataChange()
}

const resetPriceColor = () => {
    localData.value.styles.price_color = themeColorMethods.resetToThemeColor('price_color') || '#ff4757'
    handleDataChange()
}

const resetScribingPriceColor = () => {
    localData.value.styles.scribing_price_color = themeColorMethods.resetToThemeColor('scribing_price_color') || '#999999'
    handleDataChange()
}

const resetSalesColor = () => {
    localData.value.styles.sales_color = themeColorMethods.resetToThemeColor('sales_color') || '#999999'
    handleDataChange()
}

const resetTagBgColor = () => {
    localData.value.styles.tag_bg_color = themeColorMethods.resetToThemeColor('tag_bg_color') || '#ff4757'
    handleDataChange()
}

const resetTagTextColor = () => {
    localData.value.styles.tag_text_color = themeColorMethods.resetToThemeColor('tag_text_color') || '#ffffff'
    handleDataChange()
}

const resetBtnBgColor = () => {
    localData.value.styles.btn_bg_color = themeColorMethods.resetToThemeColor('btn_bg_color') || '#409EFF'
    handleDataChange()
}

const resetBtnColor = () => {
    localData.value.styles.btn_color = themeColorMethods.resetToThemeColor('btn_color') || '#ffffff'
    handleDataChange()
}

// 批量应用主题颜色
const applyThemeColors = () => {
    themeColorMethods.resetAllColorsToTheme(localData.value)
    handleDataChange()
}

// 初始化
initData()
</script>

<style scoped>
.product-recommend-config {
    height: 100%;
    max-height: calc(100vh - 200px);
    overflow-y: auto;
    overflow-x: hidden;
    padding-right: 20px;
    margin-right: -20px;
}

.product-recommend-config::-webkit-scrollbar {
    width: 8px;
}

.product-recommend-config::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.product-recommend-config::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.product-recommend-config::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.config-content {
    padding: 0;
    padding-bottom: 20px;
}

.config-section {
    margin-bottom: 20px;
    border-bottom: 1px solid #e4e7ed;
    padding-bottom: 16px;
}

.config-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.section-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.form-group {
    margin-bottom: 16px;
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 6px;
}

.color-control {
    display: flex;
    align-items: center;
    gap: 8px;
}

.color-control .el-input {
    flex: 1;
}

.slider-control {
    display: flex;
    align-items: center;
    gap: 12px;
}

.slider-control .el-slider {
    flex: 1;
}

.slider-control .slider-input {
    width: 80px;
    flex-shrink: 0;
}



/* Element Plus 组件样式调整 */
.el-input,
.el-select,
.el-input-number {
    width: 100%;
}

.el-slider {
    padding-left: 12px;
}

.el-radio-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.el-switch {
    --el-switch-on-color: #409EFF;
}

/* 表单提示样式 */
.form-tip {
    margin-top: 6px;
    padding: 8px 12px;
    background-color: #f0f9ff;
    border: 1px solid #e0f2fe;
    border-radius: 4px;
    font-size: 12px;
    color: #0369a1;
    line-height: 1.4;
}
</style> 