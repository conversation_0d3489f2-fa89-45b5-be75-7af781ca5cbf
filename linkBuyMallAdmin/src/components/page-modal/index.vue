<template>
  <div class="page-modal">
    <el-dialog
      :title="modalConfig.title"
      v-model="showModal"
      destroy-on-close
      append-to-body
      :top="top"
      :width="modalConfig.width"
      :close-on-click-modal="false"
      @close="handleModalClose"
    >
      <div class="modal-container">
        <!-- 默认插槽 -->
        <slot></slot>
      </div>
      <template #footer v-if="!modalConfig.hideFooterButton">
        <span class="dialog-footer">
          <el-button v-show="showCancelButton" @click="showModal = false">
            {{ cancelText }}
          </el-button>
          <el-button
            v-show="showConfirmButton"
            type="primary"
            :disabled="modalConfig.isConfirmBtnDisabled"
            @click="handleModalConfirm"
          >
            {{ confirmText }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, ref } from 'vue'
import { IModal } from './types'
import { ElDialog, ElButton } from 'element-plus'
export default defineComponent({
  name: 'page-modal',
  components: {
    ElDialog,
    ElButton
  },
  props: {
    modalConfig: {
      type: Object as PropType<IModal>,
      required: true
    },
    confirmText: {
      type: String,
      default: '确 定'
    },
    cancelText: {
      type: String,
      default: '取 消'
    },
    showCancelButton: {
      type: Boolean,
      default: true
    },
    showConfirmButton: {
      type: Boolean,
      default: true
    },
    top: {
      type: String,
      default: '15vh'
    }
  },
  setup(props, { emit }) {
    // 显示与隐藏
    const showModal = ref(false)

    // 点击确定
    const handleModalConfirm = () => {
      emit('modalConfirm')
    }

    // 关闭弹窗
    const handleModalClose = () => {
      emit('modalClose')
    }

    return {
      showModal,
      // confirm
      handleModalConfirm,
      // 关闭弹窗
      handleModalClose
    }
  }
})
</script>
<style lang="scss">
.el-dialog__body {
  padding-top: 5px !important;
  padding-bottom: 5px !important;
}
</style>
<style lang="scss" scoped>
.page-modal {
  .modal-container {
    max-height: 500px;
    overflow-y: scroll;
    :deep(.mob-form) {
      padding-top: 0;
    }

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(177, 181, 195, 0.4);
      border-radius: 20px;
    }
  }
}
</style>
