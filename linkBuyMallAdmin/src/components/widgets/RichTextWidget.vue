<template>
    <div class="graphic" :style="containerStyle">
        <div class="graphic-scroll-container">
            <div class="graphic-list">
                <div 
                    v-for="(item, index) in currentData" 
                    :key="index"
                    class="graphic-item flex-col"
                    :style="getItemStyle(item)"
                    @click="handleItemClick(item)"
                >
                    <!-- 图片部分 -->
                    <div class="image-container">
                        <img 
                            v-if="item.url"
                            :src="getImageUrl(item.url)" 
                            class="graphic-image"
                            alt="图文图片"
                        />
                        <div 
                            v-else
                            class="image-placeholder"
                        >
                            <span class="placeholder-text">280*280</span>
                        </div>
                    </div>
                    
                    <!-- 信息部分 -->
                    <div class="info" :style="{ backgroundColor: item.bg_color || '#FFFFFF' }">
                        <div 
                            class="title line-1" 
                            :style="{ color: item.title_color || '#333333' }"
                        >
                            {{ item.title || '标题名称' }}
                        </div>
                        <div 
                            class="subtitle line-1" 
                            :style="{ color: item.subtitle_color || '#666666' }"
                        >
                            {{ item.subtitle || '副标题名称' }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
    data: {
        type: Object,
        required: true
    }
})

// 安全获取当前数据
const currentData = computed(() => {
    const data = props.data?.content?.data || []
    // 显示所有项目，包括空的项目（用于占位）
    return data
})

// 安全获取样式配置
const currentStyles = computed(() => {
    return props.data?.styles || {
        root_bg_color: '',
        border_radius_top: 16,
        border_radius_bottom: 19,
        padding_top: 9,
        padding_horizontal: 0,
        padding_bottom: 12
    }
})

// 容器样式
const containerStyle = computed(() => {
    const styles = currentStyles.value
    return {
        backgroundColor: styles.root_bg_color || 'transparent',
        paddingTop: (styles.padding_top || 0) + 'px',
        paddingLeft: (styles.padding_horizontal || 0) + 'px',
        paddingRight: (styles.padding_horizontal || 0) + 'px',
        paddingBottom: (styles.padding_bottom || 0) + 'px'
    }
})

// 获取单个项目的样式
const getItemStyle = (item) => {
    const styles = currentStyles.value
    return {
        borderRadius: `${styles.border_radius_top || 0}px ${styles.border_radius_top || 0}px ${styles.border_radius_bottom || 0}px ${styles.border_radius_bottom || 0}px`
    }
}

// 获取图片URL
const getImageUrl = (url) => {
    if (!url) return ''
    // 如果是相对路径，添加域名前缀
    if (url.startsWith('uploads/')) {
        return `//lfypt4.oss-cn-beijing.aliyuncs.com/${url}`
    }
    return url
}

// 处理项目点击
const handleItemClick = (item) => {
    if (item.link && item.link.length > 0) {
        // 处理链接跳转逻辑
        console.log('点击图文项目:', item)
    }
}
</script>

<style scoped>
.graphic {
    width: 100%;
}

.graphic-scroll-container {
    overflow-x: auto;
    overflow-y: hidden;
    /* 隐藏滚动条但保持滚动功能 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.graphic-scroll-container::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
}

.graphic-list {
    display: flex;
    gap: 12px;
    padding: 0 12px;
    /* 防止flex项目收缩 */
    flex-wrap: nowrap;
}

.graphic-item {
    flex: 0 0 auto; /* 不收缩，不放大，自动宽度 */
    width: 140px; /* 固定宽度 */
    background: #fff;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.2s ease;
}

.graphic-item:hover {
    transform: translateY(-2px);
}

.flex-col {
    display: flex;
    flex-direction: column;
}

.image-container {
    width: 100%;
    height: 140px;
    overflow: hidden;
    position: relative;
}

.graphic-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.image-placeholder {
    width: 100%;
    height: 100%;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 14px;
}

.placeholder-text {
    color: #999;
    font-size: 12px;
}

.info {
    padding: 12px;
    background: #fff;
}

.title {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 4px;
    color: #333;
}

.subtitle {
    font-size: 12px;
    color: #666;
}

.line-1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
}

/* 移动端优化 */
@media (max-width: 480px) {
    .graphic-list {
        gap: 8px;
        padding: 0 8px;
    }
    
    .graphic-item {
        width: 140px; /* 移动端稍微小一点 */
    }
    
    .image-container {
        height: 120px; /* 移动端图片高度稍小 */
    }
}

/* 为了更好的滑动体验，添加平滑滚动 */
.graphic-scroll-container {
    scroll-behavior: smooth;
}

/* 在触摸设备上启用惯性滚动 */
@media (hover: none) and (pointer: coarse) {
    .graphic-scroll-container {
        -webkit-overflow-scrolling: touch;
    }
}
</style> 