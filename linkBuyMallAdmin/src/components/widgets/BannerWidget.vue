<template>
    <div class="banner-widget" :style="widgetRootStyle">
        <div class="banner-content" :style="bannerContentStyle" ref="bannerContainer">
            <!-- 当没有有效轮播图时显示提示 -->
            <div v-if="banners.length === 0" class="no-banner-placeholder" :style="placeholderStyle">
                <el-icon class="placeholder-icon">
                    <Picture />
                </el-icon>
                <span>请添加轮播图片</span>
            </div>

            <!-- 有效轮播图时显示轮播组件 -->
            <el-carousel v-else :height="carouselHeight + 'px'" :autoplay="autoplay" :interval="interval * 1000"
                :arrow="'never'" :indicator-position="'none'" :style="carouselStyle" ref="carousel"
                @change="handleSlideChange">
                <el-carousel-item v-for="(banner, index) in banners" :key="index">
                    <div class="banner-slide" :style="getSlideStyle(banner)" @click="handleBannerClick(banner)">
                        <el-image :src="banner.url" :style="imageStyle" :fit="getImageFit()" :lazy="true"
                            :preview-disabled="true" @load="handleImageLoad">
                            <template #error>
                                <div class="image-error">
                                    <el-icon class="error-icon">
                                        <Picture />
                                    </el-icon>
                                    <span>图片加载失败</span>
                                </div>
                            </template>
                        </el-image>
                    </div>
                </el-carousel-item>
            </el-carousel>

        </div>

        <!-- 自定义指示器 -->
        <div v-if="showIndicator" class="indicator" :style="indicatorStyle">
            <div class="indicator-content" :class="indicatorClass" :style="indicatorContentStyle">
                <!-- 圆角和圆形指示器 -->
                <template v-if="indicatorStyleType === 1 || indicatorStyleType === 2">
                    <span v-for="(banner, index) in banners" :key="index" class="indicator-item" :class="{
                        active: index === currentSlide
                    }" :style="getIndicatorItemStyle(index === currentSlide)" @click="goToSlide(index)"></span>
                </template>

                <!-- 数字指示器 -->
                <template v-else-if="indicatorStyleType === 3">
                    <div class="number-indicator" :style="numberIndicatorStyle">
                        {{ currentSlide + 1 }}/{{ banners.length }}
                    </div>
                </template>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed, ref, nextTick, watch } from 'vue'
import { Picture } from '@element-plus/icons-vue'

const props = defineProps({
    data: {
        type: Object,
        required: true
    }
})

// 当前轮播索引
const currentSlide = ref(0)
const carousel = ref(null)
const bannerContainer = ref(null)
// 自适应模式的动态高度
const adaptiveHeight = ref(200) // 默认高度

// 轮播图数据 - 只显示有图片的项
const banners = computed(() => {
    const data = props.data.content?.data || []
    return data.filter(banner => banner.url && banner.url.trim() !== '')
})

// 样式配置
const styles = computed(() => props.data.styles || {})

// 轮播配置
const autoplay = computed(() => props.data.autoplay !== false)
const interval = computed(() => props.data.interval || 3)

// 指示器样式类型：1-圆角，2-圆形，3-数字
const indicatorStyleType = computed(() => styles.value.indicator_style || 1)

// 指示器对齐方式
const indicatorAlign = computed(() => styles.value.indicator_align || 'left')

// 指示器颜色
const indicatorColor = computed(() => styles.value.indicator_color || '#FF2C3C')

// 边框圆角
const borderRadius = computed(() => styles.value.border_radius || 0)

// 图片比例
const imgRatio = computed(() => {
    const ratio = styles.value.imgRatio
    if (ratio === 1) return 'auto' // 自适应
    if (ratio === 0.75) return 0.75 // 4:3
    if (ratio === 0.67) return 0.67 // 3:2
    if (ratio === 0.56) return 0.56 // 16:9
    return 'auto' // 默认自适应
})

// 轮播图高度计算
const carouselHeight = computed(() => {
    if (imgRatio.value === 'auto') {
        // 自适应模式使用动态计算的高度
        return adaptiveHeight.value
    } else {
        // 固定比例模式，基于375px宽度计算
        return Math.round(375 * imgRatio.value)
    }
})

// 是否显示指示器
const showIndicator = computed(() => {
    return banners.value.length > 1
})

// 组件根容器样式
const widgetRootStyle = computed(() => {
    return {
        paddingTop: (styles.value.padding_top || 0) + 'px',
        paddingBottom: (styles.value.padding_bottom || 0) + 'px',
        paddingLeft: (styles.value.padding_horizontal || 0) + 'px',
        paddingRight: (styles.value.padding_horizontal || 0) + 'px',
        backgroundColor: styles.value.root_bg_color || 'transparent',
        backgroundImage: styles.value.root_bg_url ? `url(${styles.value.root_bg_url})` : 'none',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
    }
})

// 轮播内容样式
const bannerContentStyle = computed(() => {
    return {
        backgroundColor: styles.value.banner_root_bg_color || 'transparent',
        borderRadius: borderRadius.value + 'px',
        overflow: 'hidden',
        position: 'relative'
    }
})

// 轮播组件样式
const carouselStyle = computed(() => {
    return {
        borderRadius: borderRadius.value + 'px'
    }
})

// 占位符样式
const placeholderStyle = computed(() => {
    return {
        height: carouselHeight.value + 'px',
        borderRadius: borderRadius.value + 'px'
    }
})

// 图片样式
const imageStyle = computed(() => {
    return {
        width: '100%',
        height: '100%',
        borderRadius: borderRadius.value + 'px'
    }
})

// 获取单个轮播项样式
const getSlideStyle = (banner) => {
    return {
        backgroundColor: banner.show_item_bg_color ? (banner.item_bg_color || '#fff') : 'transparent',
        cursor: banner.link?.path ? 'pointer' : 'default'
    }
}

// 指示器容器样式
const indicatorStyle = computed(() => {
    const style = {
        position: 'absolute',
        bottom: '10px',
        zIndex: 10
    }

    // 根据对齐方式设置位置
    switch (indicatorAlign.value) {
        case 'left':
            style.left = '10px'
            break
        case 'right':
            style.right = '10px'
            break
        case 'center':
        default:
            style.left = '50%'
            style.transform = 'translateX(-50%)'
            break
    }

    return style
})

// 指示器内容类名
const indicatorClass = computed(() => {
    return {
        'fillet': indicatorStyleType.value === 1,
        'circle': indicatorStyleType.value === 2,
        'number': indicatorStyleType.value === 3
    }
})

// 指示器内容样式
const indicatorContentStyle = computed(() => {
    const style = {}

    // 根据对齐方式设置文本对齐
    switch (indicatorAlign.value) {
        case 'left':
            style.textAlign = 'left'
            break
        case 'right':
            style.textAlign = 'right'
            break
        case 'center':
        default:
            style.textAlign = 'center'
            break
    }

    return style
})

// 获取指示器项样式
const getIndicatorItemStyle = (isActive) => {
    if (indicatorStyleType.value === 1 || indicatorStyleType.value === 2) {
        const baseStyle = {
            backgroundColor: isActive ? indicatorColor.value : 'rgba(0, 0, 0, 0.5)',
            transition: 'all 0.3s ease',
            cursor: 'pointer'
        }

        // 圆角指示器特殊样式
        if (indicatorStyleType.value === 1) {
            baseStyle.width = '16px'
            baseStyle.height = '4px'
            baseStyle.borderRadius = '2px'
        }
        // 圆形指示器特殊样式
        else if (indicatorStyleType.value === 2) {
            baseStyle.width = '8px'
            baseStyle.height = '8px'
            baseStyle.borderRadius = '50%'
            if (isActive) {
                baseStyle.transform = 'scale(1.2)'
            }
        }

        return baseStyle
    }
    return {}
}

// 数字指示器样式
const numberIndicatorStyle = computed(() => {
    return {
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        color: '#fff',
        padding: '4px 8px',
        borderRadius: '12px',
        fontSize: '12px'
    }
})

// 处理轮播切换
const handleSlideChange = (index) => {
    currentSlide.value = index
}

// 跳转到指定轮播
const goToSlide = (index) => {
    if (carousel.value) {
        carousel.value.setActiveItem(index)
    }
}

// 处理轮播图点击
const handleBannerClick = (banner) => {
    if (banner.link?.path) {
        console.log('点击轮播图，跳转链接:', banner.link)
        // 这里可以添加路由跳转逻辑
    }
}

// 获取图片fit属性
const getImageFit = () => {
    if (imgRatio.value === 'auto') {
        return 'contain' // 自适应模式使用contain
    } else {
        return 'cover' // 固定比例模式使用cover
    }
}

// 计算自适应高度
const calculateAdaptiveHeight = (img) => {
    if (imgRatio.value === 'auto' && img && img.naturalWidth && img.naturalHeight) {
        // 获取真实容器宽度
        let containerWidth = 375 // 默认宽度
        if (bannerContainer.value) {
            containerWidth = bannerContainer.value.offsetWidth || 375
        }
        
        // 根据图片原始比例计算高度
        const aspectRatio = img.naturalHeight / img.naturalWidth
        const calculatedHeight = Math.round(containerWidth * aspectRatio)
        
        // 设置合理的高度范围（最小150px，最大500px）
        const minHeight = 150
        const maxHeight = 500
        const finalHeight = Math.max(minHeight, Math.min(maxHeight, calculatedHeight))
        
        adaptiveHeight.value = finalHeight
    }
}

// 处理图片加载完成
const handleImageLoad = (event) => {
    if (imgRatio.value === 'auto') {
        const img = event.target
        calculateAdaptiveHeight(img)
    }
}

// 监听图片比例模式变化，重置自适应高度
watch(() => imgRatio.value, (newRatio) => {
    if (newRatio === 'auto') {
        // 切换到自适应模式时，重置为默认高度，等待图片加载完成后重新计算
        adaptiveHeight.value = 200
    }
}, { immediate: true })

// 监听轮播图数据变化，重新计算高度
watch(() => banners.value, () => {
    if (imgRatio.value === 'auto' && banners.value.length > 0) {
        // 延迟一下，等待DOM更新
        nextTick(() => {
            // 尝试获取当前显示的图片并重新计算高度
            const currentBanner = banners.value[currentSlide.value]
            if (currentBanner?.url) {
                const img = new Image()
                img.onload = () => calculateAdaptiveHeight(img)
                img.src = currentBanner.url
            }
        })
    }
}, { deep: true })
</script>

<style scoped>
.banner-widget {
    position: relative;
    width: 100%;
}

.banner-content {
    position: relative;
    width: 100%;
}

.banner-slide {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: #f5f5f5;
    color: #999;
    font-size: 14px;
}

.error-icon {
    font-size: 32px;
    margin-bottom: 8px;
}

/* 无轮播图时的占位符 */
.no-banner-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
    color: #999;
    font-size: 14px;
    border: 2px dashed #ddd;
}

.no-banner-placeholder .placeholder-icon {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 8px;
}

/* 自定义指示器 */
.indicator {
    position: absolute;
    z-index: 10;
    pointer-events: none;
}

.indicator-content {
    display: flex;
    align-items: center;
    gap: 6px;
    pointer-events: auto;
}

.indicator-content.fillet {
    gap: 4px;
}

.indicator-content.circle {
    gap: 6px;
}

.indicator-content.number {
    justify-content: center;
}

/* 指示器项基础样式 */
.indicator-item {
    display: inline-block;
}

/* 数字指示器 */
.number-indicator {
    display: inline-block;
    white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .banner-widget {
        margin: 0;
    }

    .indicator-content {
        gap: 4px;
    }
}

/* Element Plus 轮播图样式覆盖 */
:deep(.el-carousel__container) {
    position: relative;
}

:deep(.el-carousel__item) {
    display: flex;
    align-items: center;
    justify-content: center;
}

:deep(.el-image) {
    display: block;
    width: 100%;
    height: 100%;
}

:deep(.el-image__inner) {
    width: 100%;
    height: 100%;
}
</style>