<template>
  <div class="coupon-widget" :style="widgetRootStyle">
    <div class="coupon-container" :style="containerStyle">


              <!-- 优惠券列表 -->
        <div class="coupon-list" :class="couponListClass">
          <!-- 样式三的标题 -->
          <div v-if="currentStyle === 3 && title" class="coupon-style3-title" :style="titleStyle">
            {{ title }}
          </div>
          
          <!-- 样式三的优惠券容器 -->
          <div v-if="currentStyle === 3" class="coupon-style3-container">
            <div class="coupon-con flex" v-for="(coupon, index) in couponList" :key="coupon.id || index">
              <!-- 样式3：卡片布局 -->
              <div class="coupon-con-inner flex-col" :class="couponItemClass" :style="couponItemStyle"
                @click="handleCouponClick(coupon)">
                <!-- 金额区域 -->
                <div class="coupon-price flex-col col-center row-center" :style="couponLeftStyle">
                  <div class="coupon-money-wrapper">
                    <span class="currency">¥</span>
                    <span class="money" :style="moneyStyle">{{ formatMoney(coupon.money) }}</span>
                  </div>
                  <div class="coupon-condition xs" :style="conditionStyle">
                    {{ coupon.condition }}
                  </div>
                </div>

                <!-- 信息区域 -->
                <div class="coupon-info flex-col" :style="couponRightStyle">
                  <div class="coupon-scene xs" :style="sceneStyle">
                    {{ coupon.use_type }}
                  </div>
                  <!-- 领取按钮 -->
                  <div class="coupon-btn xs" :style="buttonStyle">
                    {{ getButtonText(coupon) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 样式1和样式2 -->
          <div v-else class="coupon-con flex" v-for="(coupon, index) in couponList" :key="coupon.id || index">
            <!-- 样式2：简化布局 -->
            <div v-if="currentStyle === 2" class="coupon-con-inner flex" :class="couponItemClass" :style="couponItemStyle"
              @click="handleCouponClick(coupon)">
              <!-- 左侧金额区域 -->
              <div class="coupon-left-area">
                <div class="coupon-money-wrapper">
                  <span class="currency">¥</span>
                  <span class="money" :style="moneyStyle">{{ formatMoney(coupon.money) }}</span>
                </div>
                <div class="coupon-condition xs" :style="conditionStyle">
                  {{ coupon.condition }}
                </div>
              </div>

              <!-- 分割线 -->
              <div class="coupon-line" :style="dividerStyle"></div>

              <!-- 右侧文字区域 -->
              <div class="coupon-right-text" :style="{ color: styles.text_color || '#FF2C3C' }">
                立即领取
              </div>
            </div>



            <!-- 样式1：完整布局 -->
            <div v-else class="coupon-con-inner flex" :class="couponItemClass" :style="couponItemStyle"
              @click="handleCouponClick(coupon)">
              <!-- 优惠券左侧 - 金额区域 -->
              <div class="coupon-price flex-col col-center row-center flex-none" :style="couponLeftStyle">
                <div class="coupon-money-wrapper">
                  <span class="currency">¥</span>
                  <span class="money" :style="moneyStyle">{{ formatMoney(coupon.money) }}</span>
                </div>
                <div class="coupon-condition xs" :style="conditionStyle">
                  {{ coupon.condition }}
                </div>
              </div>

              <!-- 分割线 -->
              <div class="coupon-line" :style="dividerStyle"></div>

              <!-- 优惠券右侧 - 信息区域 -->
              <div class="coupon-info flex flex-1" :style="couponRightStyle">
                <div class="flex-1">
                  <div class="coupon-name lg weight-500 line-1" :style="nameStyle">
                    {{ coupon.name }}
                  </div>
                  <div class="coupon-scene xs m-t-5" :style="sceneStyle">
                    {{ coupon.use_type }}
                  </div>
                </div>

                <!-- 领取按钮 -->
                <div class="coupon-btn xs flex-none" :style="buttonStyle">
                  {{ getButtonText(coupon) }}
                </div>
              </div>
            </div>
          </div>
        </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  data: {
    type: Object,
    required: true
  }
})

// 基础数据
const title = computed(() => props.data.content?.title || '')
const couponList = computed(() => props.data.content?.data || [])
const styles = computed(() => props.data.styles || {})
const currentStyle = computed(() => props.data.content?.style || 1)

// 风格相关的类名
const couponListClass = computed(() => {
  return {
    'coupon-list-style1': currentStyle.value === 1,
    'coupon-list-style2': currentStyle.value === 2,
    'coupon-list-style3': currentStyle.value === 3
  }
})

const couponItemClass = computed(() => {
  return {
    'coupon-item-style1': currentStyle.value === 1,
    'coupon-item-style2': currentStyle.value === 2,
    'coupon-item-style3': currentStyle.value === 3
  }
})

// 根容器样式
const widgetRootStyle = computed(() => {
  return {
    backgroundColor: styles.value.root_bg_color || '#FFFFFF',
    paddingTop: `${styles.value.padding_top || 0}px`,
    paddingLeft: `${styles.value.padding_horizontal || 0}px`,
    paddingRight: `${styles.value.padding_horizontal || 0}px`,
    paddingBottom: `${styles.value.padding_bottom || 0}px`,
    '--coupon-bg': styles.value.root_bg_color || '#FFFFFF'
  }
})

// 容器样式
const containerStyle = computed(() => {
  return {
    width: '100%'
  }
})

// 标题样式（仅用于样式三）
const titleStyle = computed(() => {
  return {
    fontSize: '16px',
    fontWeight: 'bold',
    color: styles.value.title_color || '#FFFFFF',
    marginBottom: '12px'
  }
})

// 优惠券项样式
const couponItemStyle = computed(() => {
  return {
    backgroundColor: styles.value.bg_color || '#FCE7E7',
    color: styles.value.text_color || '#FF2C3C',
    borderRadius: '8px',
    // marginBottom: '8px',
    // overflow: 'hidden',
    heigth: '100%',
    border: `1px solid ${styles.value.text_color || '#FF2C3C'}`,
    position: 'relative'
  }
})

// 优惠券左侧样式
const couponLeftStyle = computed(() => {
  return {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    // padding: '5px 8px',
    position: 'relative'
  }
})

// 优惠券右侧样式
const couponRightStyle = computed(() => {
  return {
    flex: '1',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: '12px 8px 12px 8px'
  }
})

// 金额样式
const moneyStyle = computed(() => {
  return {
    fontSize: '24px',
    fontWeight: 'bold',
    color: styles.value.money_color || '#FF2C3C',
    lineHeight: '1'
  }
})

// 条件样式
const conditionStyle = computed(() => {
  return {
    fontSize: '12px',
    color: styles.value.condition_color || '#333333',
    marginTop: '4px',
    textAlign: 'center'
  }
})

// 优惠券名称样式
const nameStyle = computed(() => {
  return {
    fontSize: '16px',
    fontWeight: 'bold',
    color: styles.value.text_color || '#FF2C3C',
    marginBottom: '4px'
  }
})

// 使用场景样式
const sceneStyle = computed(() => {
  return {
    fontSize: '12px',
    color: styles.value.scene_color || '#999999'
  }
})

// 分割线样式
const dividerStyle = computed(() => {
  const color = styles.value.text_color || '#FF2C3C'
  return {
    backgroundImage: `repeating-linear-gradient(${color}, ${color} 4px, transparent 4px, transparent 8px)`,
    width: '2px'
  }
})

// 按钮样式
const buttonStyle = computed(() => {
  return {
    backgroundColor: styles.value.btn_bg_color || '#FF2C3C',
    color: styles.value.btn_text_color || '#FFFFFF',
    border: 'none',
    borderRadius: '14px',
    fontSize: '12px',
    cursor: 'pointer'
  }
})

// 获取按钮类型
const getButtonType = (coupon) => {
  if (coupon.is_receive) return 'info'
  if (!coupon.is_available) return 'info'
  return 'primary'
}

// 获取按钮文本
const getButtonText = (coupon) => {
  if (coupon.is_preview) return '预览模式'
  if (coupon.is_receive) return '已领取'
  if (!coupon.is_available) return '已抢完'
  return '立即领取'
}

// 格式化金额显示
const formatMoney = (money) => {
  if (!money) return '0'
  const num = parseFloat(money)
  if (isNaN(num)) return money
  
  // 如果小数点后两位都是0，显示整数
  if (num % 1 === 0) {
    return num.toString()
  }
  
  // 否则保留两位小数
  return num.toFixed(2)
}

// 处理优惠券点击
const handleCouponClick = (coupon) => {
  if (coupon.is_preview) {
    console.log('预览模式，无法操作')
    return
  }
  if (coupon.is_available && !coupon.is_receive) {
    console.log('领取优惠券:', coupon)
    // 这里可以添加领取优惠券的逻辑
  }
}
</script>

<style scoped>
.coupon-widget {
  width: 100%;
}

.coupon-container {
  width: 100%;
}



/* 基础样式类 */
.flex {
  display: flex;
  align-items: center;
}

.flex > * {
  overflow: visible !important;
}

.flex-col {
  flex-direction: column;
}

.col-center {
  align-items: center;
}

.row-center {
  justify-content: center;
}

.flex-none {
  flex: none;
}

.flex-1 {
  flex: 1;
}

.xs {
  font-size: 12px;
}

.lg {
  font-size: 16px;
}

.weight-500 {
  font-weight: 500;
}

.line-1 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.m-t-5 {
  margin-top: 5px;
}

.coupon-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}



.coupon-con {
  position: relative;
  cursor: pointer;
  transition: transform 0.2s ease;
  border-radius: 8px;
  overflow: hidden;
  min-height: 80px;
  align-items: center;
}
/* 
.coupon-con:hover {
  transform: translateY(-1px);
} */

.coupon-con-inner {
  position: relative;
  cursor: pointer;
  border-radius: 8px;
  width: 100%;
}

/* 优惠券缺口效果 */
.coupon-con-inner::before {
  content: "";
  position: absolute;
  background-color: var(--coupon-bg, #FFFFFF);
  width: 14px;
  height: 14px;
  border-radius: 50%;
  top: 50%;
  left: 0;
  transform: translate(-50%, -50%);
  border: 1px solid currentColor;
  box-sizing: border-box;
  z-index: 3;
}

.coupon-con-inner::after {
  content: "";
  position: absolute;
  background-color: var(--coupon-bg, #FFFFFF);
  width: 14px;
  height: 14px;
  border-radius: 50%;
  top: 50%;
  right: 0;
  transform: translate(50%, -50%);
  border: 1px solid currentColor;
  box-sizing: border-box;
  z-index: 3;
}

/* 优惠券价格区域 */
.coupon-price {
  width: 100px;
  padding: 16px 8px;
  text-align: center;
}

.coupon-money-wrapper {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 4px;
}

.currency {
  font-size: 16px;
  font-weight: 500;
}

.money {
  font-size: 30px;
  font-weight: 500;
  margin-left: 2px;
}

/* 分割线 */
.coupon-line {
  width: 2px;
  height: 56px;
  background-size: 2px 10px;
  background-repeat: repeat-y;
  background-position: 50%;
}

/* 优惠券信息区域 */
.coupon-info {
  padding: 16px;
  align-items: center;
}

.coupon-name {
  max-width: 130px;
  margin-bottom: 4px;
}

/* 按钮 */
.coupon-btn {
  padding: 4px 20px;
  margin-right: 10px;
  border-radius: 20px;
  text-align: center;
  white-space: nowrap;
}


/* 风格1样式 - 现代卡片风格 */
.coupon-list-style1 .coupon-item-style1 {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  /* margin-bottom: 12px; */
}

.coupon-list-style1 .coupon-item-style1::before {
  width: 16px;
  height: 16px;
  top: 50%;
  left: 0;
  transform: translate(-50%, -50%);
}

.coupon-list-style1 .coupon-item-style1::after {
  width: 16px;
  height: 16px;
  top: 50%;
  right: 0;
  transform: translate(50%, -50%);
}

/* 风格2样式 - 横向滑动风格 */
.coupon-list-style2 {
  display: flex !important;
  flex-direction: row !important;
  overflow-x: auto;
  overflow-y: hidden;
  gap: 12px !important;
  /* padding-bottom: 8px; */
  scroll-behavior: smooth;
  flex-wrap: nowrap;
}

.coupon-list-style2::-webkit-scrollbar {
  height: 4px;
}

.coupon-list-style2::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.coupon-list-style2::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.coupon-list-style2::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.coupon-list-style2 .coupon-con {
  flex: none;
  width: 160px;
  margin-bottom: 0;
}

.coupon-list-style2 .coupon-item-style2 {
  border-radius: 8px;
  border-style: solid;
  margin-bottom: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 80px;
  /* min-height: 160px; */
  align-items: center;
}

/* 左侧金额区域 */
.coupon-list-style2 .coupon-left-area {
  width: 110px;
  padding: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: none;
}

.coupon-list-style2 .coupon-money-wrapper {
  margin-bottom: 4px;
  display: flex;
  align-items: baseline;
  justify-content: center;
}

.coupon-list-style2 .currency {
  font-size: 14px;
  font-weight: 500;
}

.coupon-list-style2 .money {
  font-size: 24px !important;
  font-weight: 500;
  margin-left: 2px;
}

.coupon-list-style2 .coupon-condition {
  font-size: 12px !important;
  text-align: center;
}

/* 分割线 */
.coupon-list-style2 .coupon-line {
  height: 50px;
  width: 2px;
  flex: none;
}

/* 右侧文字区域 */
.coupon-list-style2 .coupon-right-text {
  width: 40px;
  padding: 0 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: none;
  writing-mode: vertical-lr;
  text-orientation: upright;
  font-size: 14px;
}

.coupon-list-style2 .text-vertical {
  display: block;
  font-size: 12px;
  font-weight: 500;
  line-height: 1.2;
  margin: 1px 0;
}

.coupon-list-style2 .coupon-item-style2::before {
  width: 12px;
  height: 12px;
  top: -6px;
  left: 112px;
  transform: translateX(-60%);
}

.coupon-list-style2 .coupon-item-style2::after {
  width: 12px;
  height: 12px;
  top: 72px;
  left: 112px;
  transform: translateX(-60%);
}

/* 风格3样式 - 背景图片横向滑动风格 */
.coupon-list-style3 {
  background-image: url('@/assets/images/decoration/coupon_bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 12px;
  padding: 5px 10px 5px 10px;
  display: flex !important;
  flex-direction: column !important;
  overflow-x: auto;
  overflow-y: hidden;
  scroll-behavior: smooth;
  flex-wrap: nowrap;
  min-height: 140px;
}

/* 样式三标题 */
.coupon-style3-title {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 10;
}

/* 样式三优惠券容器 */
.coupon-style3-container {
  display: flex;
  flex-direction: row;
  gap: 10px;
  overflow-x: auto;
  overflow-y: hidden;
  flex-wrap: nowrap;
  padding-top: 40px;
}

.coupon-list-style3 .coupon-con {
  flex: none;
  width: 110px;
  margin-bottom: 0;
}

.coupon-list-style3::-webkit-scrollbar {
  height: 4px;
}

.coupon-list-style3::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.coupon-list-style3::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.6);
  border-radius: 2px;
}

.coupon-list-style3::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.8);
}

.coupon-list-style3 .coupon-item-style3 {
  border-radius: 8px;
  border: none !important;
  margin-bottom: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  min-height: 120px;
  height: 120px;
  background: #fff;
  width: 100%;
  padding: 8px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.coupon-list-style3 .coupon-price {
  width: 100%;
  padding: 0;
  text-align: center;
  flex: none;
}

.coupon-list-style3 .coupon-money-wrapper {
  margin-bottom: 0px;
  justify-content: center;
  display: flex;
  align-items: baseline;
}

.coupon-list-style3 .currency {
  font-size: 20px;
  font-weight: 500;
}

.coupon-list-style3 .money {
  font-size: 28px !important;
  font-weight: bold;
  margin-left: 2px;
}

.coupon-list-style3 .coupon-condition {
  font-size: 11px !important;
  text-align: center;
  margin: 0;
  line-height: 1.2;
}

.coupon-list-style3 .coupon-name {
  font-size: 13px !important;
  font-weight: 500;
  text-align: center;
  margin: 0 0 2px 0;
  line-height: 1.2;
}

.coupon-list-style3 .coupon-scene {
  font-size: 10px !important;
  text-align: center;
  margin: 0 0 10px 0;
  line-height: 1.2;
  color: #999;
}

.coupon-list-style3 .coupon-info {
  padding: 0;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  flex: 1;
  display: flex;
}


.coupon-list-style3 .coupon-line {
  display: none;
}

/* 样式三去掉缺口效果 */
.coupon-list-style3 .coupon-item-style3::before,
.coupon-list-style3 .coupon-item-style3::after {
  display: none;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .coupon-price {
    width: 80px;
    padding: 8px 6px;
  }

  .money {
    font-size: 20px !important;
  }

  .coupon-condition {
    font-size: 10px !important;
  }

  .coupon-name {
    font-size: 14px !important;
  }

  .coupon-info {
    padding: 8px 12px 8px 6px;
  }
}
</style>
