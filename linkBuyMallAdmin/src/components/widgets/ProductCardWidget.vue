<template>
    <div class="product-card-widget" :style="widgetRootStyle">
        <!-- 选项卡头部 -->
        <div class="card-tabs" :style="tabsContainerStyle">
            <div 
                v-for="(tab, index) in safeTabs" 
                :key="index"
                class="tab-item"
                :class="{ active: getCurrentActiveIndex() === index }"
                :style="getTabItemStyle(index)"
                @click="switchTab(index)"
            >
                {{ tab.name }}
                <!-- 选项条指示器 -->
                <div v-if="getCurrentActiveIndex() === index && safeContent.show_line" class="tab-indicator" :style="tabIndicatorStyle"></div>
            </div>
        </div>

        <!-- 选项卡内容区域 -->
        <div class="card-content" :style="contentContainerStyle">
            <div v-if="activeTab" class="tab-content">
                <!-- 大图模式 (style: 1) -->
                <div v-if="activeTab.style === 1" class="product-grid large-mode" :style="getProductContainerStyle(activeTab)">
                    <div 
                        v-for="product in activeTab.data" 
                        :key="product.id"
                        class="product-item large-item"
                        :style="getProductItemStyle(activeTab)"
                    >
                        <div class="product-image large-image">
                            <img v-if="product.image || product.mainImage" :src="product.image || product.mainImage" :alt="product.name" />
                            <div v-else class="product-placeholder">
                                <el-icon class="placeholder-icon">
                                    <Picture />
                                </el-icon>
                            </div>
                        </div>
                        <div class="product-info">
                            <div v-if="activeTab.show_title" class="product-name" :style="{ color: activeTab.title_color }">
                                {{ product.name || '商品名称' }}
                            </div>
                            <div class="product-price-section">
                                <div v-if="activeTab.show_price" class="product-price" :style="{ color: activeTab.price_color }">
                                    <span class="currency">￥</span>
                                    <span v-if="formatPriceDisplay(product).includes('起')" class="price-with-suffix">
                                        <span class="price-amount">{{ formatPriceDisplay(product).replace('起', '') }}</span>
                                        <span class="price-suffix">起</span>
                                    </span>
                                    <span v-else>{{ formatPriceDisplay(product) }}</span>
                                </div>
                                <div v-if="activeTab.show_scribing_price && shouldShowLineationPrice(product)" 
                                      class="lineation-price" 
                                      :style="{ color: activeTab.scribing_price_color }">
                                    ￥{{ getLineationPrice(product) }}
                                </div>
                            </div>
                            <div v-if="activeTab.show_btn" class="buy-btn" :style="getBuyButtonStyle(activeTab)">
                                {{ activeTab.btn_text || '购买' }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 一行两个 (style: 2) -->
                <div v-else-if="activeTab.style === 2" class="product-grid two-columns" :style="getProductContainerStyle(activeTab)">
                    <div 
                        v-for="product in activeTab.data" 
                        :key="product.id"
                        class="product-item two-col"
                        :style="getProductItemStyle(activeTab)"
                    >
                        <div class="product-image">
                            <img v-if="product.image || product.mainImage" :src="product.image || product.mainImage" :alt="product.name" />
                            <div v-else class="product-placeholder">
                                <el-icon class="placeholder-icon">
                                    <Picture />
                                </el-icon>
                            </div>
                        </div>
                        <div class="product-info">
                            <div v-if="activeTab.show_title" class="product-name" :style="{ color: activeTab.title_color }">
                                {{ product.name || '商品名称' }}
                            </div>
                            <div class="product-price-section">
                                <div v-if="activeTab.show_price" class="product-price" :style="{ color: activeTab.price_color }">
                                    <span class="currency">￥</span>
                                    <span v-if="formatPriceDisplay(product).includes('起')" class="price-with-suffix">
                                        <span class="price-amount">{{ formatPriceDisplay(product).replace('起', '') }}</span>
                                        <span class="price-suffix">起</span>
                                    </span>
                                    <span v-else>{{ formatPriceDisplay(product) }}</span>
                                </div>
                                <div v-if="activeTab.show_scribing_price && shouldShowLineationPrice(product)" 
                                      class="lineation-price" 
                                      :style="{ color: activeTab.scribing_price_color }">
                                    ￥{{ getLineationPrice(product) }}
                                </div>
                            </div>
                            <div v-if="activeTab.show_btn" class="buy-btn" :style="getBuyButtonStyle(activeTab)">
                                {{ activeTab.btn_text || '购买' }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 一行三个 (style: 3) -->
                <div v-else-if="activeTab.style === 3" class="product-grid three-columns" :style="getProductContainerStyle(activeTab)">
                    <div 
                        v-for="product in activeTab.data" 
                        :key="product.id"
                        class="product-item three-col"
                        :style="getProductItemStyle(activeTab)"
                    >
                        <div class="product-image">
                            <img v-if="product.image || product.mainImage" :src="product.image || product.mainImage" :alt="product.name" />
                            <div v-else class="product-placeholder">
                                <el-icon class="placeholder-icon">
                                    <Picture />
                                </el-icon>
                            </div>
                        </div>
                        <div class="product-info">
                            <div v-if="activeTab.show_title" class="product-name" :style="{ color: activeTab.title_color }">
                                {{ product.name || '商品名称' }}
                            </div>
                            <div class="product-price-section">
                                <div v-if="activeTab.show_price" class="product-price" :style="{ color: activeTab.price_color }">
                                    <span class="currency">￥</span>
                                    <span v-if="formatPriceDisplay(product).includes('起')" class="price-with-suffix">
                                        <span class="price-amount">{{ formatPriceDisplay(product).replace('起', '') }}</span>
                                        <span class="price-suffix">起</span>
                                    </span>
                                    <span v-else>{{ formatPriceDisplay(product) }}</span>
                                </div>
                                <div v-if="activeTab.show_scribing_price && shouldShowLineationPrice(product)" 
                                      class="lineation-price" 
                                      :style="{ color: activeTab.scribing_price_color }">
                                    ￥{{ getLineationPrice(product) }}
                                </div>
                            </div>
                            <div v-if="activeTab.show_btn" class="buy-btn small" :style="getBuyButtonStyle(activeTab)">
                                {{ activeTab.btn_text || '购买' }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 横向滑动 (style: 4) -->
                <div v-else-if="activeTab.style === 4" class="product-scroll">
                    <div class="scroll-container" :style="getProductContainerStyle(activeTab)">
                        <div 
                            v-for="product in activeTab.data" 
                            :key="product.id"
                            class="product-item scroll"
                            :style="getProductItemStyle(activeTab)"
                        >
                            <div class="product-image">
                                <img v-if="product.image || product.mainImage" :src="product.image || product.mainImage" :alt="product.name" />
                                <div v-else class="product-placeholder">
                                    <el-icon class="placeholder-icon">
                                        <Picture />
                                    </el-icon>
                                </div>
                            </div>
                            <div class="product-info">
                                <div v-if="activeTab.show_title" class="product-name" :style="{ color: activeTab.title_color }">
                                    {{ product.name || '商品名称' }}
                                </div>
                                <div class="product-price-section">
                                    <div v-if="activeTab.show_price" class="product-price" :style="{ color: activeTab.price_color }">
                                        <span class="currency">￥</span>
                                        <span v-if="formatPriceDisplay(product).includes('起')" class="price-with-suffix">
                                            <span class="price-amount">{{ formatPriceDisplay(product).replace('起', '') }}</span>
                                            <span class="price-suffix">起</span>
                                        </span>
                                        <span v-else>{{ formatPriceDisplay(product) }}</span>
                                    </div>
                                    <div v-if="activeTab.show_scribing_price && shouldShowLineationPrice(product)" 
                                          class="lineation-price" 
                                          :style="{ color: activeTab.scribing_price_color }">
                                        ￥{{ getLineationPrice(product) }}
                                    </div>
                                </div>
                                <div v-if="activeTab.show_btn" class="buy-btn" :style="getBuyButtonStyle(activeTab)">
                                    {{ activeTab.btn_text || '购买' }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 列表模式 (style: 5) -->
                <div v-else class="product-list" :style="getProductContainerStyle(activeTab)">
                    <div 
                        v-for="product in activeTab.data" 
                        :key="product.id"
                        class="product-item list"
                        :style="getProductItemStyle(activeTab)"
                    >
                        <div class="product-image">
                            <img v-if="product.image || product.mainImage" :src="product.image || product.mainImage" :alt="product.name" />
                            <div v-else class="product-placeholder">
                                <el-icon class="placeholder-icon">
                                    <Picture />
                                </el-icon>
                            </div>
                        </div>
                        <div class="product-info">
                            <div v-if="activeTab.show_title" class="product-name" :style="{ color: activeTab.title_color }">
                                {{ product.name || '商品名称' }}
                            </div>
                            <div class="product-price-section">
                                <div v-if="activeTab.show_price" class="product-price" :style="{ color: activeTab.price_color }">
                                    <span class="currency">￥</span>
                                    <span v-if="formatPriceDisplay(product).includes('起')" class="price-with-suffix">
                                        <span class="price-amount">{{ formatPriceDisplay(product).replace('起', '') }}</span>
                                        <span class="price-suffix">起</span>
                                    </span>
                                    <span v-else>{{ formatPriceDisplay(product) }}</span>
                                </div>
                                <div v-if="activeTab.show_scribing_price && shouldShowLineationPrice(product)" 
                                      class="lineation-price" 
                                      :style="{ color: activeTab.scribing_price_color }">
                                    ￥{{ getLineationPrice(product) }}
                                </div>
                            </div>
                        </div>
                        <div v-if="activeTab.show_btn" class="buy-btn" :style="getBuyButtonStyle(activeTab)">
                            {{ activeTab.btn_text || '购买' }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { getProductsByIds } from '@/api/goods'
import { Picture } from '@element-plus/icons-vue'
import empty_product from '@/assets/images/decoration/empty_product.avif'

const props = defineProps({
    data: {
        type: Object,
        required: true
    }
})

// 当前激活的选项卡索引
const activeTabIndex = ref(0)

// 商品数据暂存 - 为每个选项卡存储之前选择的商品ID
const productDataStash = ref({})

// 强制刷新计数器，用于触发计算属性重新计算
const forceUpdateCounter = ref(0)

// 默认图片
const defaultImage = empty_product

// 默认商品数据
const defaultProducts = [
    {
        id: 'default_1',
        image: defaultImage,
        name: '商品名称示例',
        sell_price: '99.00',
        lineation_price: '199.00',
        sales_num: '0',
        source: 'DEFAULT'
    },
    {
        id: 'default_2',
        image: defaultImage,
        name: '精选商品推荐',
        sell_price: '159.00',
        lineation_price: '299.00',
        sales_num: '0',
        source: 'DEFAULT'
    },
    {
        id: 'default_3',
        image: defaultImage,
        name: '热销爆款商品',
        sell_price: '79.00',
        lineation_price: '129.00',
        sales_num: '0',
        source: 'DEFAULT'
    },
    {
        id: 'default_4',
        image: defaultImage,
        name: '限时特价商品',
        sell_price: '199.00',
        lineation_price: '399.00',
        sales_num: '0',
        source: 'DEFAULT'
    },
    {
        id: 'default_5',
        image: defaultImage,
        name: '新品上市推荐',
        sell_price: '259.00',
        lineation_price: '459.00',
        sales_num: '0',
        source: 'DEFAULT'
    },
    {
        id: 'default_6',
        image: defaultImage,
        name: '品质优选商品',
        sell_price: '89.00',
        lineation_price: '159.00',
        sales_num: '0',
        source: 'DEFAULT'
    }
]

// 商品详情缓存
const productDetailsCache = ref({})

// 安全访问内容数据
const safeContent = computed(() => {
    return props.data?.content || {
        active: 0,
        show_line: 1,
        has_active_bg: 0,
        max_product_count: 20,
        data: []
    }
})

// 安全访问样式数据
const safeStyles = computed(() => {
    return props.data?.styles || {
        root_bg_color: "",
        bg_color: "#FFFFFF",
        color: "#333333",
        active_color: "#FF2C3C",
        line_color: "#FF2C3C",
        active_bg_color: "",
        // 选项卡样式
        tab_gap: 8,
        tab_padding_top: 12,
        tab_padding_bottom: 12,
        tab_padding_horizontal: 0,
        tab_border_radius_top: 0,
        tab_border_radius_bottom: 0,
        // 商品样式
        content_bg_color: "#FFFFFF",
        margin: 10,
        component_margin: 0,
        content_padding_top: 10,
        content_padding_bottom: 10,
        content_padding_horizontal: 10,
        content_border_radius_top: 0,
        content_border_radius_bottom: 0,
        goods_border_radius: 4
    }
})

// 根据商品ID查询商品详情
const fetchProductDetails = async (productIds) => {
    if (!productIds || productIds.length === 0) return []
    
    try {
        const response = await getProductsByIds(productIds)
        if (response.code === 200) {
            return response.data || []
        }
    } catch (error) {
        console.error('查询商品详情失败:', error)
    }
    return []
}

// 获取选项卡的商品数据（如果没有商品则使用默认商品）
const getTabProducts = async (tab, tabIndex) => {
    // 如果是选择分类模式，检查是否有暂存的商品数据
    if (tab.goods_type === 2) {
        const stashKey = `tab_${tabIndex}`
        const stashedData = productDataStash.value[stashKey]
        
        if (stashedData && stashedData.length > 0) {
            // 如果有暂存数据，查询商品详情
            const cacheKey = stashedData.join(',')
            if (productDetailsCache.value[cacheKey]) {
                return productDetailsCache.value[cacheKey]
            }
            
            const productDetails = await fetchProductDetails(stashedData)
            if (productDetails.length > 0) {
                productDetailsCache.value[cacheKey] = productDetails
                return productDetails
            }
        }
        
        // 选择分类模式且没有暂存数据时，显示默认商品
        return getDefaultProducts(tabIndex)
    }
    
    const tabData = tab.data || []
    
    // 如果有商品数据且是ID数组，则查询详情
    if (tabData.length > 0 && typeof tabData[0] === 'number') {
        // 检查缓存
        const cacheKey = tabData.join(',')
        if (productDetailsCache.value[cacheKey]) {
            return productDetailsCache.value[cacheKey]
        }
        
        // 查询商品详情
        const productDetails = await fetchProductDetails(tabData)
        if (productDetails.length > 0) {
            // 缓存结果
            productDetailsCache.value[cacheKey] = productDetails
            return productDetails
        }
    }
    
    // 如果有商品对象数据，直接返回
    if (tabData.length > 0 && typeof tabData[0] === 'object') {
        return tabData
    }
    
    // 如果没有商品数据，返回默认商品
    return getDefaultProducts(tabIndex)
}

// 获取选项卡的商品数据（同步版本）
const getTabProductsSync = (tab, tabIndex) => {
    // 如果是选择分类模式，始终显示默认商品
    if (tab.goods_type === 2) {
        // 选择分类模式时始终显示默认商品，不使用暂存数据
        return getDefaultProducts(tabIndex)
    }
    
    const tabData = tab.data || []
    
    // 如果有商品数据且是ID数组，则使用缓存的详细信息
    if (tabData.length > 0 && typeof tabData[0] === 'number') {
        // 检查缓存
        const cacheKey = tabData.join(',')
        if (productDetailsCache.value[cacheKey]) {
            return productDetailsCache.value[cacheKey]
        }
        
        // 如果没有缓存，返回默认商品（将在后台异步查询真实数据）
        return getDefaultProducts(tabIndex)
    }
    
    // 如果有商品对象数据，直接返回
    if (tabData.length > 0 && typeof tabData[0] === 'object') {
        return tabData
    }
    
    // 如果没有商品数据，返回默认商品
    return getDefaultProducts(tabIndex)
}

// 获取默认商品
const getDefaultProducts = (tabIndex) => {
    const startIndex = (tabIndex * 2) % defaultProducts.length
    const endIndex = Math.min(startIndex + 4, defaultProducts.length)
    let products = defaultProducts.slice(startIndex, endIndex)
    
    // 如果商品不够4个，从头补充
    if (products.length < 4) {
        const remaining = 4 - products.length
        products = products.concat(defaultProducts.slice(0, remaining))
    }
    
    return products
}

// 处理商品数据暂存和恢复
const handleGoodsTypeChange = (tab, tabIndex, newGoodsType, oldGoodsType) => {
    const stashKey = `tab_${tabIndex}`
    
    if (oldGoodsType === 1 && newGoodsType === 2) {
        // 从选择商品切换到选择分类：暂存当前商品数据
        const currentData = tab.data || []
        if (currentData.length > 0 && typeof currentData[0] === 'number') {
            productDataStash.value[stashKey] = [...currentData]
        }
    } else if (oldGoodsType === 2 && newGoodsType === 1) {
        // 从选择分类切换到选择商品：恢复暂存的商品数据
        const stashedData = productDataStash.value[stashKey]
        if (stashedData && stashedData.length > 0) {
            // 这里不直接修改数据，而是在safeTabs计算属性中处理
        }
    }
}

// 安全访问选项卡数据
const safeTabs = computed(() => {
    // 使用forceUpdateCounter触发重新计算
    forceUpdateCounter.value
    
    const tabs = safeContent.value.data || []
    return tabs.map((tab, index) => {
        let tabData = tab.data || []
        
        // 处理商品数据的暂存和恢复逻辑
        if (tab.goods_type === 1) {
            // 选择商品模式：检查是否需要恢复暂存的数据
            const stashKey = `tab_${index}`
            const stashedData = productDataStash.value[stashKey]
            
            // 如果当前没有商品数据但有暂存数据，则恢复暂存数据
            if ((!tabData || tabData.length === 0) && stashedData && stashedData.length > 0) {
                tabData = stashedData
            }
        } else if (tab.goods_type === 2) {
            // 选择分类模式：清空商品数据，但保留goods_type信息
            tabData = []
        }
        
        // 创建用于传递给getTabProductsSync的tab对象，确保goods_type正确传递
        const tabForSync = {
            ...tab,
            data: tabData,
            goods_type: tab.goods_type || 1
        }
        
        return {
            name: tab.name || '选项卡',
            style: tab.style || 1,
            goods_type: tab.goods_type || 1,
            show_title: tab.show_title !== 0,
            show_price: tab.show_price !== 0,
            show_scribing_price: tab.show_scribing_price !== 0,
            show_btn: tab.show_btn !== 0,
            btn_text: tab.btn_text || '购买',
            show_tag: tab.show_tag || 0,
            tag_type: tab.tag_type || 1,
            custom_tag: tab.custom_tag || "",
            is_custom_tag: tab.is_custom_tag || 0,
            category: tab.category || {
                categoryId: "",
                source: "",
                supplierId: []
            },
            data: getTabProductsSync(tabForSync, index),
            title_color: tab.title_color || '#333333',
            price_color: tab.price_color || '#FF2C3C',
            scribing_price_color: tab.scribing_price_color || '#999999',
            btn_bg_color: tab.btn_bg_color || '#FF2C3C',
            btn_color: tab.btn_color || '#FFFFFF',
            btn_border_radius: tab.btn_border_radius || 0,
            btn_border_color: tab.btn_border_color || "",
            root_bg_color: tab.root_bg_color || "",
            content_bg_color: tab.content_bg_color || "",
            bg_color: tab.bg_color || '#FFFFFF',
            padding: tab.padding || 0,
            margin: tab.margin || 0,
            padding_top: tab.padding_top || 0,
            padding_horizontal: tab.padding_horizontal || 0,
            padding_bottom: tab.padding_bottom || 0,
            goods_border_radius: tab.goods_border_radius || 0,
            tag_width: tab.tag_width || 0,
            border_radius_top: tab.border_radius_top || 0,
            border_radius_bottom: tab.border_radius_bottom || 0
        }
    })
})

// 当前激活的选项卡
const activeTab = computed(() => {
    const index = safeContent.value.active !== undefined ? safeContent.value.active : activeTabIndex.value
    return safeTabs.value[index] || safeTabs.value[0]
})

// 监听数据变化，同步内部状态
watch(() => safeContent.value.active, (newActive) => {
    if (newActive !== undefined && newActive !== activeTabIndex.value) {
        activeTabIndex.value = newActive
    }
}, { immediate: true })

// 监听选项卡数据变化，处理商品类型切换
watch(() => safeContent.value.data, (newData, oldData) => {
    if (!oldData || !newData) return
    
    // 检查每个选项卡的goods_type是否发生变化
    newData.forEach((newTab, index) => {
        const oldTab = oldData[index]
        if (oldTab && newTab.goods_type !== oldTab.goods_type) {
            handleGoodsTypeChange(oldTab, index, newTab.goods_type, oldTab.goods_type)
            
            // 强制刷新计算属性
            forceUpdateCounter.value++
        }
    })
}, { deep: true })

// 组件根样式
const widgetRootStyle = computed(() => {
    const styles = safeStyles.value
    return {
        backgroundColor: styles.root_bg_color || 'transparent',
        margin: `${styles.component_margin || 0}px`
    }
})

// 选项卡容器样式
const tabsContainerStyle = computed(() => {
    const styles = safeStyles.value
    return {
        backgroundColor: styles.bg_color || '#FFFFFF',
        gap: `${styles.tab_gap || 0}px`,
        borderTopLeftRadius: `${styles.tab_border_radius_top || 0}px`,
        borderTopRightRadius: `${styles.tab_border_radius_top || 0}px`,
        borderBottomLeftRadius: `${styles.tab_border_radius_bottom || 0}px`,
        borderBottomRightRadius: `${styles.tab_border_radius_bottom || 0}px`
    }
})

// 内容容器样式
const contentContainerStyle = computed(() => {
    const styles = safeStyles.value
    return {
        backgroundColor: styles.content_bg_color || '#FFFFFF',
        padding: `${styles.content_padding_top || 0}px ${styles.content_padding_horizontal || 0}px ${styles.content_padding_bottom || 0}px`,
        borderTopLeftRadius: `${styles.content_border_radius_top || 0}px`,
        borderTopRightRadius: `${styles.content_border_radius_top || 0}px`,
        borderBottomLeftRadius: `${styles.content_border_radius_bottom || 0}px`,
        borderBottomRightRadius: `${styles.content_border_radius_bottom || 0}px`
    }
})

// 选项条指示器样式
const tabIndicatorStyle = computed(() => {
    const styles = safeStyles.value
    return {
        backgroundColor: styles.line_color || '#FF2C3C'
    }
})

// 获取当前激活的选项卡索引
const getCurrentActiveIndex = () => {
    return safeContent.value.active !== undefined ? safeContent.value.active : activeTabIndex.value
}

// 获取选项卡项样式
const getTabItemStyle = (index) => {
    const styles = safeStyles.value
    const isActive = getCurrentActiveIndex() === index
    
    return {
        color: isActive ? (styles.active_color || '#FF2C3C') : (styles.color || '#333333'),
        backgroundColor: isActive && safeContent.value.has_active_bg ? (styles.active_bg_color || 'transparent') : 'transparent',
        padding: `${styles.tab_padding_top || 0}px ${styles.tab_padding_horizontal || 0}px ${styles.tab_padding_bottom || 0}px`
    }
}

// 获取商品项样式
const getProductItemStyle = (tab) => {
    const styles = safeStyles.value
    return {
        borderRadius: `${styles.goods_border_radius || 0}px`
    }
}

// 获取购买按钮样式
const getBuyButtonStyle = (tab) => {
    return {
        backgroundColor: tab.btn_bg_color || '#FF2C3C',
        color: tab.btn_color || '#FFFFFF',
        borderRadius: `${tab.btn_border_radius || 30}px`,
        border: tab.btn_border_color ? `1px solid ${tab.btn_border_color}` : 'none'
    }
}

// 获取商品容器样式（用于应用商品间距）
const getProductContainerStyle = (tab) => {
    const styles = safeStyles.value
    const margin = styles.margin || 0
    
    // 大图模式使用固定的10px间距，与商品组保持一致
    if (tab.style === 1) {
        return { gap: '10px' }
    } else if (tab.style === 2) {
        // 两列模式使用8px间距
        return { gap: '8px' }
    } else if (tab.style === 3) {
        // 三列模式使用8px间距
        return { gap: '8px' }
    } else if (tab.style === 4) {
        // 横向滑动使用8px间距
        return { gap: '8px' }
    } else if (tab.style === 5) {
        // 列表模式使用8px间距
        return { gap: '8px' }
    }
    
    return {}
}

// 切换选项卡
const switchTab = (index) => {
    activeTabIndex.value = index
}

// 判断是否应该显示划线价格
const shouldShowLineationPrice = (product) => {
    // 兼容不同的字段名
    const sellPrice = parseFloat(product.sell_price || product.salePriceMin || product.salePriceDisplay) || 0
    // 兼容不同的划线价字段名：strikethroughPrice（新）、lineation_price（旧）
    const lineationPrice = parseFloat(product.strikethroughPrice || product.lineation_price) || 0

    // 只有当划线价大于0且大于售价时才显示
    return lineationPrice > 0 && lineationPrice > sellPrice
}

// 初始化商品详情查询
const initProductDetails = async () => {
    const tabs = safeContent.value.data || []
    const productIdsToFetch = new Set()
    
    // 收集所有需要查询的商品ID
    tabs.forEach((tab, tabIndex) => {
        let tabData = tab.data || []
        
        // 如果是选择商品模式，检查实际数据和暂存数据
        if (tab.goods_type === 1) {
            const stashKey = `tab_${tabIndex}`
            const stashedData = productDataStash.value[stashKey]
            
            // 如果当前没有数据但有暂存数据，使用暂存数据
            if ((!tabData || tabData.length === 0) && stashedData && stashedData.length > 0) {
                tabData = stashedData
            }
        }
        
        if (tabData.length > 0 && typeof tabData[0] === 'number') {
            // 检查是否已经有缓存
            const cacheKey = tabData.join(',')
            if (!productDetailsCache.value[cacheKey]) {
                tabData.forEach(id => productIdsToFetch.add(id))
            }
        }
    })
    
    // 查询商品详情
    if (productIdsToFetch.size > 0) {
        const productIds = Array.from(productIdsToFetch)
        const productDetails = await fetchProductDetails(productIds)
        
        if (productDetails.length > 0) {
            // 按ID建立映射
            const productMap = {}
            productDetails.forEach(product => {
                productMap[product.id] = product
            })
            
            // 为每个选项卡设置商品详情缓存
            tabs.forEach((tab, tabIndex) => {
                let tabData = tab.data || []
                
                // 处理暂存数据
                if (tab.goods_type === 1) {
                    const stashKey = `tab_${tabIndex}`
                    const stashedData = productDataStash.value[stashKey]
                    
                    if ((!tabData || tabData.length === 0) && stashedData && stashedData.length > 0) {
                        tabData = stashedData
                    }
                }
                
                if (tabData.length > 0 && typeof tabData[0] === 'number') {
                    const cacheKey = tabData.join(',')
                    if (!productDetailsCache.value[cacheKey]) {
                        const tabProducts = tabData.map(id => productMap[id]).filter(Boolean)
                        if (tabProducts.length > 0) {
                            productDetailsCache.value[cacheKey] = tabProducts
                        }
                    }
                }
            })
        }
    }
}

// 组件挂载时初始化
onMounted(() => {
    initProductDetails()
})

// 监听数据变化，重新初始化
watch(() => safeContent.value.data, () => {
    initProductDetails()
}, { deep: true })

// 格式化价格显示
const formatPriceDisplay = (product) => {
    // 获取最低价和最高价
    const minPrice = parseFloat(product.salePriceMin) || 0
    const maxPrice = parseFloat(product.salePriceMax) || 0
    const sellPrice = parseFloat(product.sell_price) || 0
    
    // 如果有sell_price字段，优先使用（兼容旧数据）
    if (sellPrice > 0) {
        return sellPrice.toString()
    }
    
    // 如果有salePriceDisplay且不是区间格式，直接使用
    if (product.salePriceDisplay && !product.salePriceDisplay.includes('-')) {
        return product.salePriceDisplay
    }
    
    // 如果最低价和最高价相等，显示最低价
    if (minPrice === maxPrice && minPrice > 0) {
        return minPrice.toString()
    }
    
    // 如果最低价和最高价不等，显示"最低价起"
    if (minPrice > 0 && maxPrice > minPrice) {
        return `${minPrice}起`
    }
    
    // 备用显示
    if (minPrice > 0) {
        return minPrice.toString()
    }
    
    // 如果有salePriceDisplay，使用它
    if (product.salePriceDisplay) {
        return product.salePriceDisplay
    }
    
    return '0'
}

// 获取划线价格
const getLineationPrice = (product) => {
    return product.strikethroughPrice || 0
}
</script>

<style scoped>
.product-card-widget {
    overflow: hidden;
}

/* 选项卡头部 */
.card-tabs {
    display: flex;
    position: relative;
    justify-content: flex-start;
    padding: 0 16px;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.card-tabs::-webkit-scrollbar {
    display: none;
}

.tab-item {
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    border-bottom: 2px solid transparent;
    white-space: nowrap;
    flex-shrink: 0;
}

.tab-item:hover {
    opacity: 0.8;
}

.tab-indicator {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 3px;
    border-radius: 2px;
}

/* 内容区域 */
.card-content {
    /* padding由计算属性控制 */
}

/* 大图模式 */
.large-mode {
    display: flex;
    flex-direction: column;
}

.large-item {
    width: 100%;
    background: #ffffff;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.large-image {
    width: 100%;
    height: 120px;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.large-item .product-info {
    padding: 8px;
}

/* 两列模式 */
.two-columns {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
}

.product-item.two-col {
    background: #ffffff;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.product-item.two-col .product-image {
    width: 100%;
    height: 172px;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.product-item.two-col .product-info {
    padding: 8px;
}

/* 三列模式 */
.three-columns {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
}

.product-item.three-col {
    background: #ffffff;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    font-size: 11px;
}

.product-item.three-col .product-image {
    width: 100%;
    height: 100px;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.product-item.three-col .product-info {
    padding: 8px;
}

.product-item.three-col .product-name {
    font-size: 11px;
    -webkit-line-clamp: 1;
}

.product-item.three-col .product-price {
    font-size: 12px;
}

/* 横向滑动 */
.product-scroll {
    display: flex;
    overflow-x: auto;
    padding-bottom: 4px;
    scrollbar-width: none;
    -ms-overflow-style: none;
    -webkit-overflow-scrolling: touch;
}

.product-scroll::-webkit-scrollbar {
    display: none;
}

.scroll-container {
    display: flex;
}

.product-item.scroll {
    flex: 0 0 140px;
    width: 140px;
    background: #ffffff;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.product-item.scroll .product-image {
    width: 100%;
    height: 140px;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.product-item.scroll .product-info {
    padding: 8px;
}

/* 列表模式 */
.product-list {
    display: flex;
    flex-direction: column;
}

.product-item.list {
    display: flex;
    align-items: center;
    padding: 8px;
    gap: 12px;
    background: #ffffff;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.product-item.list .product-image {
    width: 80px;
    height: 80px;
    flex-shrink: 0;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.product-item.list .product-info {
    flex: 1;
    padding: 0;
}

.product-item.list .buy-btn {
    flex-shrink: 0;
    margin-left: auto;
}

/* 通用样式 */
.product-image {
    border-radius: 4px;
    overflow: hidden;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}



/* 通用商品样式 */
.product-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
}

.placeholder-icon {
    font-size: 24px;
    color: #ccc;
}

.product-name {
    font-size: 12px;
    margin-bottom: 6px;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.product-price-section {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 6px;
}

.product-price {
    font-size: 14px;
    font-weight: 500;
}

.currency {
    font-size: 12px;
}

.price-with-suffix {
    display: inline;
}

.price-amount {
    font-size: inherit;
}

.price-suffix {
    font-size: 8px;
    margin-left: 1px;
}

.lineation-price {
    font-size: 10px;
    text-decoration: line-through;
}

.buy-btn {
    border-radius: 30px;
    padding: 4px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid transparent;
    text-align: center;
    white-space: nowrap;
}

.buy-btn:hover {
    opacity: 0.8;
}

.buy-btn.small {
    padding: 2px 8px;
    font-size: 10px;
}

/* 滚动条样式 */
.product-scroll::-webkit-scrollbar {
    height: 4px;
}

.product-scroll::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.product-scroll::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.product-scroll::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style> 