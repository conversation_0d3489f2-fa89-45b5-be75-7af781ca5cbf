<template>
    <div class="blank-space-widget" :style="containerStyle">
        <div class="blank-content" :style="contentStyle">
            <!-- <span class="blank-text">空白间距 {{ data.height }}px</span> -->
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
    data: {
        type: Object,
        required: true
    }
})

// 容器样式（应用底部背景和边距）
const containerStyle = computed(() => {
    return {
        background: props.data.rootBackground || 'transparent',
        paddingTop: `${props.data.marginTop || 0}px`,
        paddingBottom: `${props.data.marginBottom || 0}px`,
        paddingLeft: `${props.data.marginHorizontal || 0}px`,
        paddingRight: `${props.data.marginHorizontal || 0}px`
    }
})

// 内容样式（应用组件背景和高度）
const contentStyle = computed(() => {
    return {
        height: (props.data.height || 20) + 'px',
        background: props.data.background || 'transparent'
    }
})
</script>

<style scoped>
.blank-space-widget {
    /* margin: 0 16px; */
    position: relative;
}

.blank-content {
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px dashed #ccc;
    border-radius: 4px;
}

.blank-text {
    font-size: 12px;
    color: #999;
    background: rgba(255, 255, 255, 0.8);
    padding: 2px 6px;
    border-radius: 2px;
}
</style> 