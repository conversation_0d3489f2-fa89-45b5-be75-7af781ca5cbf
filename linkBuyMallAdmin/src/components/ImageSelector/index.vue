<template>
    <div class="image-selector">
        <el-dialog 
            v-model="dialogVisible" 
            :title="title" 
            width="1200px" 
            :before-close="handleClose" 
            append-to-body
            :fullscreen="false"
            :modal="true"
            :destroy-on-close="false"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :show-close="true"
            class="image-selector-dialog"
            @keydown.esc="handleDialogEsc"
        >
            <div class="image-selector-container">
                <!-- 左侧分组列表 -->
                <div class="group-list">
                    <el-menu :default-active="activeGroup" class="group-menu" @select="handleGroupSelect">
                        <el-menu-item 
                            style="height: 40px; position: relative; padding: 0 0px;" 
                            v-for="group in groupList" 
                            :key="group.id" 
                            :index="group.id + ''"
                        >
                            <div class="group-item-content" :class="{ active: activeGroup === group.id }">
                                <el-icon><Folder /></el-icon>
                                <div class="group-name-actions">
                                    <span class="group-name">{{ group.name }}</span>
                                </div>
                            </div>
                        </el-menu-item>
                    </el-menu>
                </div>

                <!-- 中间图片列表 -->
                <div class="image-list-wrapper">
                    <div class="image-list">
                        <div class="image-list-toolbar">
                            <div class="toolbar-left">
                                <el-input
                                    v-model="filterName"
                                    placeholder="请输入图片名称"
                                    clearable
                                    style="width: 220px; margin-right: 16px;"
                                    @input="handleFilterChange"
                                    @clear="handleSearchClear"
                                />
                                <el-button type="primary" @click="handleUploadImage">上传图片</el-button>
                            </div>
                            <div class="toolbar-right">
                                <div class="image-list-counter" v-if="!singleMode">
                                    已选择 {{ selectedImages.length }} 项
                                    <span v-if="props.maxCount > 0">
                                        / {{ props.maxCount }} 项
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="images" v-loading="loading">
                            <div 
                                v-for="img in images" 
                                :key="img.id" 
                                class="image-item"
                                @click="toggleImageSelect(img)" 
                                @mouseenter="img.hover = true"
                                @mouseleave="img.hover = false"
                            >
                                <div class="image-thumb-wrapper" style="cursor: pointer;" :class="{ 'is-selected': img.selected || (singleMode && selectedImage?.id === img.id) }">
                                    <img :src="img.url" :alt="img.name" class="image-thumb" />
                                    <div v-if="img.selected || (singleMode && selectedImage?.id === img.id)" class="image-mask">
                                        <svg class="check-icon" viewBox="0 0 48 48">
                                            <circle cx="24" cy="24" r="22" fill="none" />
                                            <polyline points="14,26 22,34 34,18" fill="none" stroke="#fff" stroke-width="4"
                                                stroke-linecap="round" stroke-linejoin="round" />
                                        </svg>
                                    </div>
                                    <div class="image-actions" :class="{ show: img.hover }">
                                        <el-tooltip content="查看" placement="top" :enterable="false">
                                            <el-link type="primary" @click.stop="handleView(img)" class="view-button">
                                                <el-icon><View /></el-icon>
                                            </el-link>
                                        </el-tooltip>
                                    </div>
                                </div>
                                <div class="image-name" :title="img.name">
                                    <span class="image-name-text">{{ img.name }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="pagination-bar">
                        <div class="pagination-actions">
                            <template v-if="!singleMode">
                                <el-checkbox v-model="selectAll" @change="handleSelectAll">当页全选</el-checkbox>
                            </template>
                        </div>
                        <div class="pagination">
                            <el-pagination 
                                v-model:current-page="pagination.page" 
                                v-model:page-size="pagination.pageSize"
                                :total="pagination.total" 
                                :pager-count="7" 
                                background 
                                @size-change="handleSizeChange"
                                @current-change="fetchImages" />
                        </div>
                    </div>
                </div>

                <!-- 右侧已选图片 -->
                <div class="selected-preview" v-if="!singleMode">
                    <div class="selected-preview-header">
                        <div class="selected-preview-title">已选择 {{ selectedImages.length }} 项</div>
                        <el-button type="primary" link @click="clearSelectedImages" v-if="selectedImages.length > 0">清空</el-button>
                    </div>
                    <div class="selected-preview-list">
                        <div v-if="selectedImages.length === 0" class="empty-selected">
                            <el-empty description="未选择图片" :image-size="80" />
                        </div>
                        <div 
                            v-for="(img, index) in selectedImages" 
                            :key="img.id" 
                            class="selected-preview-item"
                        >
                            <div class="preview-item-thumb">
                                <img :src="img.url" :alt="img.name" />
                                <div class="preview-item-actions">
                                    <el-button 
                                        type="danger" 
                                        circle 
                                        size="small" 
                                        @click.stop="removeSelectedImage(img, index)"
                                    >
                                        <el-icon><Delete /></el-icon>
                                    </el-button>
                                </div>
                            </div>
                            <div class="preview-item-name" :title="img.name">{{ img.name }}</div>
                        </div>
                    </div>
                </div>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="handleCancel">取消</el-button>
                    <el-button type="primary" @click="handleConfirm">确认</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 添加文件上传输入框 -->
        <input
            type="file"
            ref="uploadInput"
            style="display: none"
            accept="image/*"
            multiple
            @change="onUploadFileChange"
        />
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, nextTick, defineProps, defineEmits, onBeforeUnmount } from 'vue'
import { ElMessage } from 'element-plus'
import { Folder, View, Delete } from '@element-plus/icons-vue'
import { debounce } from 'lodash'
import {
    getGroupList,
    getFileList,
    uploadFile,
    batchUploadFile,
} from '@/api/imageCenter'

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: '选择图片'
    },
    multiple: {
        type: Boolean,
        default: false
    },
    maxCount: {
        type: Number,
        default: 0
    },
    initialSelected: {
        type: Array,
        default: () => []
    }
})

const emit = defineEmits(['update:visible', 'select', 'cancel'])

const dialogVisible = ref(false)
const singleMode = ref(!props.multiple)

// 分组数据
const groupList = ref([])
const activeGroup = ref('all')

// 图片数据
const images = ref([])
const pagination = reactive({
    page: 1,
    pageSize: 21,
    total: 0,
})
const selectAll = ref(false)
const selectedImages = ref([])
const selectedImage = ref(null)
const loading = ref(false)

// 过滤相关
const filterName = ref('')
const debouncedFetchImages = debounce(fetchImages, 600)

// 添加标志位跟踪预览状态
const isPreviewOpen = ref(false)

// 上传相关
const uploadInput = ref(null)

// 监听visible属性
watch(() => props.visible, (val) => {
    console.log('ImageSelector: props.visible changed:', val)
    dialogVisible.value = val
    if (val) {
        // 对话框打开时初始化数据
        init()
    }
})

// 监听dialogVisible变化
watch(dialogVisible, (val) => {
    console.log('ImageSelector: dialogVisible changed:', val)
    emit('update:visible', val)
})

// 组件挂载时
onMounted(() => {
    console.log('ImageSelector mounted, props.visible:', props.visible)
    // 确保初始状态与props.visible保持一致
    dialogVisible.value = props.visible
    if (props.visible) {
        init()
    }
    
    // 添加全局键盘事件监听
    window.addEventListener('keydown', handleGlobalKeydown, true);
})

// 组件卸载时
onBeforeUnmount(() => {
    // 移除全局键盘事件监听
    window.removeEventListener('keydown', handleGlobalKeydown, true);
})

// 全局键盘事件处理
function handleGlobalKeydown(e) {
    // 如果是ESC键且预览正在打开
    if (e.key === 'Escape' && isPreviewOpen.value) {
        // 阻止事件继续传播，防止关闭对话框
        e.stopPropagation();
        e.preventDefault();
    }
}

// 初始化
function init() {
    fetchGroups()
    fetchImages()
    
    // 设置初始选择的图片
    if (props.initialSelected && props.initialSelected.length > 0) {
        if (singleMode.value) {
            selectedImage.value = props.initialSelected[0]
        } else {
            selectedImages.value = [...props.initialSelected]
        }
    } else {
        selectedImages.value = []
        selectedImage.value = null
    }
}

// 获取分组列表
async function fetchGroups() {
    const res = await getGroupList()
    const defaultGroups = [
        { id: 'all', name: '全部', hover: false, moreVisible: false, renameVisible: false },
        { id: 'ungroup', name: '未分组', hover: false, moreVisible: false, renameVisible: false }
    ]
    if (res && res.data) {
        groupList.value = [
            ...defaultGroups,
            ...res.data.map(g => ({
                ...g,
                hover: false,
                moreVisible: false,
                renameVisible: false
            }))
        ]
        // 设置默认选中全部
        activeGroup.value = 'all'
    } else {
        groupList.value = defaultGroups
        activeGroup.value = 'all'
    }
}

// 获取图片列表
async function fetchImages() {
    loading.value = true
    const params = {
        tagId: activeGroup.value,
        name: filterName.value,
        page: pagination.page,
        pageSize: pagination.pageSize
    }
    try {
        const res = await getFileList(params)
        if (res && res.data) {
            const pageData = res.data;
            
            // 处理MyBatis-Plus的IPage返回格式
            if (pageData.records) {
                // 将图片数据转换为我们需要的格式
                images.value = pageData.records.map(img => {
                    // 检查是否在已选中的列表中
                    const isSelected = singleMode.value 
                        ? selectedImage.value && selectedImage.value.id === img.id
                        : selectedImages.value.some(sel => sel.id === img.id);
                    
                    return {
                        ...img,
                        url: img.path, // 兼容图片展示
                        selected: isSelected,
                        hover: false
                    };
                });
                
                // 更新分页信息
                pagination.total = pageData.total;
                
                // 确保分页参数与后端一致
                if (pageData.size && pageData.size !== pagination.pageSize) {
                    pagination.pageSize = pageData.size;
                }
                
                if (pageData.current && pageData.current !== pagination.page) {
                    pagination.page = pageData.current;
                }
            } else {
                images.value = [];
                console.error('返回的数据格式不符合预期');
            }
        }
    } catch (error) {
        console.error('获取图片列表失败:', error);
        ElMessage.error('获取图片列表失败');
        images.value = [];
    } finally {
        loading.value = false;
        
        // 更新全选状态
        updateSelectAllState();
    }
}

// 分组切换
function handleGroupSelect(id) {
    activeGroup.value = id
    pagination.page = 1
    fetchImages()
}

// 全选/取消全选
function handleSelectAll(val) {
    if (singleMode.value) return;
    
    images.value.forEach(img => (img.selected = val))
    updateSelectedImages()
}

// 更新全选状态
function updateSelectAllState() {
    if (images.value.length > 0) {
        selectAll.value = images.value.every(i => i.selected);
    } else {
        selectAll.value = false;
    }
}

// 切换图片选择状态
function toggleImageSelect(img) {
    if (singleMode.value) {
        // 单选模式
        selectedImage.value = img;
        images.value.forEach(i => {
            i.selected = i.id === img.id;
        });
    } else {
        // 多选模式
        img.selected = !img.selected;
        
        // 检查是否超过最大选择数量
        if (props.maxCount > 0) {
            const selectedCount = images.value.filter(i => i.selected).length;
            if (selectedCount > props.maxCount) {
                img.selected = false;
                ElMessage.warning(`最多只能选择${props.maxCount}张图片`);
            }
        }
        
        updateSelectedImages();
        updateSelectAllState();
    }
}

// 更新已选择的图片列表
function updateSelectedImages() {
    selectedImages.value = images.value.filter(i => i.selected);
}

// 查看图片
function handleView(img) {
    console.log('预览图片:', img.url);
    
    // 确保URL是有效的
    if (!img.url) {
        ElMessage.error('图片URL无效');
        return;
    }
    
    // 方法1：使用系统原生能力在新窗口中打开图片
    // window.open(img.url, '_blank');
    
    // 方法2：创建临时全屏预览层
    createTemporaryPreview(img.url);
}

// 创建临时全屏预览
function createTemporaryPreview(imgUrl) {
    // 设置预览状态标志位
    isPreviewOpen.value = true;
    
    // 创建预览容器
    const previewContainer = document.createElement('div');
    previewContainer.style.position = 'fixed';
    previewContainer.style.top = '0';
    previewContainer.style.left = '0';
    previewContainer.style.width = '100vw';
    previewContainer.style.height = '100vh';
    previewContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
    previewContainer.style.zIndex = '99999'; // 设置超高z-index
    previewContainer.style.display = 'flex';
    previewContainer.style.alignItems = 'center';
    previewContainer.style.justifyContent = 'center';
    previewContainer.style.cursor = 'zoom-out';
    
    // 创建图片元素
    const imgElement = document.createElement('img');
    imgElement.src = imgUrl;
    imgElement.style.maxWidth = '90%';
    imgElement.style.maxHeight = '90%';
    imgElement.style.objectFit = 'contain';
    imgElement.style.boxShadow = '0 0 20px rgba(0, 0, 0, 0.5)';
    imgElement.style.transition = 'transform 0.3s';
    imgElement.style.cursor = 'move';
    
    // 添加缩放状态和拖动功能
    let scale = 1;
    let isDragging = false;
    let startX = 0;
    let startY = 0;
    let translateX = 0;
    let translateY = 0;
    
    // 双击图片放大/缩小
    imgElement.addEventListener('dblclick', (e) => {
        e.stopPropagation();
        if (scale === 1) {
            scale = 2;
            imgElement.style.transform = `scale(${scale}) translate(${translateX}px, ${translateY}px)`;
            imgElement.style.cursor = 'move';
        } else {
            scale = 1;
            translateX = 0;
            translateY = 0;
            imgElement.style.transform = 'scale(1) translate(0, 0)';
            imgElement.style.cursor = 'zoom-in';
        }
    });
    
    // 鼠标滚轮缩放
    imgElement.addEventListener('wheel', (e) => {
        e.preventDefault();
        const delta = e.deltaY > 0 ? -0.2 : 0.2;
        scale += delta;
        scale = Math.max(0.5, Math.min(scale, 3));
        imgElement.style.transform = `scale(${scale}) translate(${translateX}px, ${translateY}px)`;
    });
    
    // 拖动功能
    imgElement.addEventListener('mousedown', (e) => {
        if (scale > 1) {
            isDragging = true;
            startX = e.clientX;
            startY = e.clientY;
            imgElement.style.cursor = 'grabbing';
        }
    });
    
    document.addEventListener('mousemove', (e) => {
        if (isDragging) {
            const deltaX = (e.clientX - startX) / scale;
            const deltaY = (e.clientY - startY) / scale;
            translateX += deltaX;
            translateY += deltaY;
            imgElement.style.transform = `scale(${scale}) translate(${translateX}px, ${translateY}px)`;
            startX = e.clientX;
            startY = e.clientY;
        }
    });
    
    document.addEventListener('mouseup', () => {
        if (isDragging) {
            isDragging = false;
            imgElement.style.cursor = 'move';
        }
    });
    
    // 创建关闭按钮
    const closeButton = document.createElement('div');
    closeButton.innerHTML = '×';
    closeButton.style.position = 'absolute';
    closeButton.style.top = '20px';
    closeButton.style.right = '20px';
    closeButton.style.fontSize = '30px';
    closeButton.style.color = 'white';
    closeButton.style.cursor = 'pointer';
    closeButton.style.width = '40px';
    closeButton.style.height = '40px';
    closeButton.style.borderRadius = '50%';
    closeButton.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    closeButton.style.display = 'flex';
    closeButton.style.alignItems = 'center';
    closeButton.style.justifyContent = 'center';
    closeButton.style.zIndex = '100000'; // 确保关闭按钮在最前面
    
    // 添加提示信息
    const tipElement = document.createElement('div');
    tipElement.textContent = '双击图片放大/缩小，拖动移动图片，ESC键关闭';
    tipElement.style.position = 'absolute';
    tipElement.style.bottom = '20px';
    tipElement.style.left = '50%';
    tipElement.style.transform = 'translateX(-50%)';
    tipElement.style.color = 'rgba(255, 255, 255, 0.7)';
    tipElement.style.fontSize = '14px';
    tipElement.style.padding = '8px 16px';
    tipElement.style.borderRadius = '4px';
    tipElement.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    tipElement.style.zIndex = '100000';
    
    // 设置2秒后提示消失
    setTimeout(() => {
        tipElement.style.opacity = '0';
        tipElement.style.transition = 'opacity 1s';
    }, 2000);
    
    // 添加元素到容器
    previewContainer.appendChild(imgElement);
    previewContainer.appendChild(closeButton);
    previewContainer.appendChild(tipElement);
    
    // 添加到body
    document.body.appendChild(previewContainer);
    
    // 清除事件监听的函数
    const cleanup = () => {
        document.body.removeChild(previewContainer);
        document.removeEventListener('keydown', escHandler, true);
        document.removeEventListener('mouseup', mouseupHandler);
        // 重置预览状态标志位
        isPreviewOpen.value = false;
    };
    
    // 点击关闭预览
    previewContainer.addEventListener('click', cleanup);
    
    // 阻止图片点击冒泡
    imgElement.addEventListener('click', (e) => {
        e.stopPropagation();
    });
    
    // 点击关闭按钮关闭预览
    closeButton.addEventListener('click', cleanup);
    
    // ESC键关闭预览 - 使用捕获阶段
    const escHandler = (e) => {
        if (e.key === 'Escape') {
            // 阻止事件传播，防止关闭图片选择弹框
            e.stopPropagation();
            e.preventDefault();
            cleanup();
        }
    };
    
    // 全局鼠标释放处理
    const mouseupHandler = () => {
        if (isDragging) {
            isDragging = false;
            imgElement.style.cursor = 'move';
        }
    };
    
    // 使用捕获阶段，确保我们能先处理事件
    document.addEventListener('keydown', escHandler, true);
    document.addEventListener('mouseup', mouseupHandler);
}

// 过滤变化
function handleFilterChange() {
    debouncedFetchImages();
}

// 清除搜索
function handleSearchClear() {
    filterName.value = '';
    debouncedFetchImages();
}

// 分页大小变化
function handleSizeChange(size) {
    pagination.pageSize = size;
    pagination.page = 1;
    fetchImages();
}

// 确认选择
function handleConfirm() {
    if (singleMode.value) {
        if (!selectedImage.value) {
            ElMessage.warning('请选择一张图片');
            return;
        }
        emit('select', selectedImage.value);
    } else {
        if (selectedImages.value.length === 0) {
            ElMessage.warning('请至少选择一张图片');
            return;
        }
        emit('select', selectedImages.value);
    }
    dialogVisible.value = false;
}

// 取消选择
function handleCancel() {
    console.log('ImageSelector: 取消选择')
    emit('cancel')
    dialogVisible.value = false
}

// 修改对话框的关闭处理
function handleClose() {
    // 如果预览正在打开，不关闭选择器对话框
    if (isPreviewOpen.value) {
        return false;
    }
    
    console.log('ImageSelector: 关闭对话框');
    dialogVisible.value = false;
    return true; // 允许关闭
}

// 清空已选图片
function clearSelectedImages() {
    // 更新图片列表中的选中状态
    images.value.forEach(img => {
        img.selected = false;
    });
    
    // 清空选中图片数组
    selectedImages.value = [];
    
    // 更新全选状态
    selectAll.value = false;
}

// 移除特定选中图片
function removeSelectedImage(img, index) {
    // 从已选择列表中移除
    selectedImages.value.splice(index, 1);
    
    // 更新图片列表中的选中状态
    const imageInList = images.value.find(i => i.id === img.id);
    if (imageInList) {
        imageInList.selected = false;
    }
    
    // 更新全选状态
    updateSelectAllState();
}

// 处理对话框的ESC键关闭
function handleDialogEsc() {
    console.log('ImageSelector: 对话框ESC键关闭');
    handleClose();
}

// 处理上传图片按钮点击
function handleUploadImage() {
    uploadInput.value && uploadInput.value.click()
}

// 处理文件上传
async function onUploadFileChange(e) {
    const files = e.target.files
    if (!files || files.length === 0) return
    
    const fileArray = Array.from(files)
    
    try {
        if (fileArray.length === 1) {
            // 单文件上传
            ElMessage.info('开始上传文件...')
            const file = fileArray[0]
            
            // 使用API上传文件
            const res = await uploadFile(
                file, 
                activeGroup.value !== 'all' && activeGroup.value !== 'ungroup' ? activeGroup.value : undefined,
                file.name
            )
            
            ElMessage.success('上传成功')
            fetchImages()
        } else {
            // 批量上传
            ElMessage.info(`开始批量上传 ${fileArray.length} 个文件...`)
            
            // 使用批量上传API
            const res = await batchUploadFile(
                fileArray,
                activeGroup.value !== 'all' && activeGroup.value !== 'ungroup' ? activeGroup.value : undefined
            )
            
            ElMessage.success(`成功上传 ${res.data.length} 个文件`)
            fetchImages()
        }
    } catch (error) {
        ElMessage.error(error.message || '上传失败')
        console.error('上传错误:', error)
    } finally {
        // 清空文件选择
        e.target.value = ''
    }
}
</script>

<style>
/* 全局样式部分保持不变 */

/* 更新对话框样式 */
:deep(.image-selector-dialog) {
    --el-dialog-margin-top: 5vh;
    --el-dialog-padding-primary: 0;
    display: flex;
    flex-direction: column;
    max-width: 1200px;
    margin: 0 auto !important;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

:deep(.image-selector-dialog .el-dialog__body) {
    flex: 1;
    overflow: hidden;
    padding: 0; /* 移除内边距 */
    max-height: 70vh; /* 限制最大高度 */
}

:deep(.image-selector-dialog .el-dialog__header) {
    margin: 0;
    padding: 16px 20px;
    background-color: var(--el-color-primary-light-9);
    border-bottom: 1px solid var(--el-border-color-light);
}

:deep(.image-selector-dialog .el-dialog__footer) {
    padding: 12px 20px;
    border-top: 1px solid var(--el-border-color-light);
    background-color: var(--el-bg-color);
}
</style>

<style scoped>
.image-selector-container {
    display: flex;
    height: 580px; /* 略微减小高度 */
    max-height: 65vh; /* 最大高度不超过视口的65% */
    background-color: var(--el-bg-color);
    overflow: hidden;
}

.group-list {
    width: 180px;
    height: 100%;
    border-right: 1px solid var(--el-border-color-lighter);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    background-color: var(--el-color-white);
    padding: 10px;
    overflow: hidden;
    flex-shrink: 0;
}

.image-list-wrapper {
    flex: 1;
    padding: 16px;
    display: flex;
    flex-direction: column;
    background-color: var(--el-color-white);
    overflow: hidden;
    height: 100%;
    border-right: 1px solid var(--el-border-color-lighter);
}

/* 右侧已选图片样式 */
.selected-preview {
    width: 220px;
    height: 100%;
    background-color: var(--el-color-white);
    display: flex;
    flex-direction: column;
    padding: 16px 12px;
    flex-shrink: 0;
    border-left: none; /* 移除重复的左边框 */
}

.selected-preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--el-border-color-lighter);
}

.selected-preview-title {
    font-size: 14px;
    color: var(--el-text-color-primary);
    font-weight: 500;
}

.selected-preview-list {
    flex: 1;
    overflow-y: auto;
    padding-right: 4px;
}

.empty-selected {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.selected-preview-item {
    margin-bottom: 16px;
}

.preview-item-thumb {
    position: relative;
    width: 100%;
    height: 120px;
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid var(--el-border-color-lighter);
    margin-bottom: 4px;
}

.preview-item-thumb img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    background-color: #f5f7fa;
}

.preview-item-actions {
    position: absolute;
    top: 5px;
    right: 5px;
    display: none;
}

.preview-item-thumb:hover .preview-item-actions {
    display: block;
}

.preview-item-name {
    font-size: 12px;
    color: var(--el-text-color-regular);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center;
}

/* 增强图片选中样式 */
.image-thumb-wrapper.is-selected {
    border-color: var(--el-color-primary);
    box-shadow: 0 0 0 1px var(--el-color-primary);
}

/* 图片列表工具栏计数器 */
.image-list-toolbar {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    justify-content: space-between;
}

.toolbar-left {
    display: flex;
    align-items: center;
}

.toolbar-right {
    display: flex;
    align-items: center;
}

.image-list-counter {
    font-size: 13px;
    color: var(--el-text-color-secondary);
}

/* 滚动条样式优化 */
.selected-preview-list::-webkit-scrollbar {
    width: 4px;
    background-color: transparent;
}

.selected-preview-list::-webkit-scrollbar-thumb {
    background-color: var(--el-border-color-lighter);
    border-radius: 2px;
}

.selected-preview-list::-webkit-scrollbar-thumb:hover {
    background-color: var(--el-border-color);
}

/* 保留原有样式 */
.group-menu {
    width: 100%;
    border: none;
    background: transparent;
    flex: 1 1 auto;
    overflow-y: auto;
}

.image-list {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.images {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
    overflow-y: auto;
}

/* 其他原有样式 */
.image-item {
    width: 98px;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    margin-bottom: 8px;
    cursor: pointer;
}

.image-thumb-wrapper {
    position: relative;
    border-radius: 6px;
    border: 1px solid var(--el-border-color-base);
    width: 98px;
    height: 98px;
    overflow: hidden;
    background: #f5f7fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-item:hover .image-thumb-wrapper {
    border-color: var(--el-color-primary);
}

.image-thumb {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 4px;
    display: block;
    background: #f5f7fa;
}

.image-mask {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.check-icon {
    width: 40px;
    height: 40px;
    display: block;
}

.image-name {
    font-size: 12px;
    margin-top: 3px;
    text-align: center;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--el-text-color-regular);
    line-height: 1.2;
}

.image-name-text {
    cursor: pointer;
    font-size: 12px;
    color: var(--el-text-color-regular);
    transition: color 0.2s;
    line-height: 1.2;
}

.image-actions {
    position: absolute;
    right: 5px;
    bottom: 5px;
    height: 26px;
    width: 26px;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
    border-radius: 4px;
}

.image-actions.show {
    opacity: 1;
    pointer-events: auto;
}

.image-actions .el-link {
    height: 26px;
    width: 26px;
    color: white !important;
    font-size: 14px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    border-radius: 4px;
    transition: all 0.2s;
}

.image-actions .el-link:hover {
    transform: scale(1.1);
    background-color: rgba(255, 255, 255, 0.2);
}

.image-actions .el-icon {
    font-size: 14px;
}

.image-actions .el-link[type="primary"]:hover {
    color: var(--el-color-primary) !important;
}

.view-button {
    padding: 4px !important;
}

.pagination-bar {
    margin-top: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.pagination-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.selected-count {
    font-size: 13px;
    color: var(--el-color-primary);
}

.group-item-content {
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;
    position: relative;
    padding: 0 10px;
}

.group-item-content.active {
    background: var(--el-color-primary-light-6);
}

.group-name-actions {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
}

.group-name {
    margin-left: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    color: var(--el-text-color-regular);
    transition: color 0.2s;
}

.group-item-content.active .group-name {
    color: var(--el-color-primary);
    font-weight: bold;
}

/* 滚动条样式 */
.images::-webkit-scrollbar,
.group-menu::-webkit-scrollbar {
    width: 4px;
    background-color: transparent;
}

.images::-webkit-scrollbar-thumb,
.group-menu::-webkit-scrollbar-thumb {
    background-color: var(--el-border-color-lighter);
    border-radius: 2px;
}

.images::-webkit-scrollbar-thumb:hover,
.group-menu::-webkit-scrollbar-thumb:hover {
    background-color: var(--el-border-color);
}

.images,
.group-menu {
    scrollbar-width: thin;
    scroll-behavior: smooth;
}
</style> 