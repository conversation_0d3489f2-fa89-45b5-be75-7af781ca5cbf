$normalColor: #8a98ac;
$mobColor: #006C5F;
$mobBorderColor: #006C5F;
$mobBgColor: #F8F9FB;

$pageBg: #f0f2f5;

$gray: #8a98ac;
$red: rgb(254, 59, 48);
$green: #00bfa5;
$orange: #f78922;
$blue: #0d92f2;

// sidebar
$menuText: #333333;
$menuActiveText: #006C5F;
$subMenuActiveText: #006C5F; //https://github.com/ElemeFE/element/issues/12951

$menuBg: #fff;
$menuActiveBg:rgb(230, 244, 243);
$menuHover: #fff;

$subMenuBg: #fff;
$subMenuItemBg: #F7F8FA;
$subMenuHover: #F7F8FA;

$sideBarWidth: 220px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  normalColor: $normalColor;
  mobColor: $mobColor;
  mobBorderColor: $mobBorderColor;
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuActiveBg: $menuActiveBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
  subMenuItemBg: $subMenuItemBg;
}
