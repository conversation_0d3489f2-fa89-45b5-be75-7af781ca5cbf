import{_ as c,aj as i,b as n,o as p,e as u,f as t,k as s,h as o,y as m,t as f}from"./index-5502c85d.js";const g={name:"TradeSettings",components:{Setting:i},computed:{mallId(){return this.$route.query.mallId||this.$route.params.mallId}}},v={class:"trade-settings"},x={class:"content-area"},y={class:"card-header"},I={class:"trade-settings-content"};function S($,e,h,b,k,a){const l=n("Setting"),d=n("el-icon"),_=n("el-button"),r=n("el-card");return p(),u("div",v,[e[3]||(e[3]=t("div",{class:"page-header"},[t("h2",null,"交易设置"),t("p",null,"交易流程和规则设置")],-1)),t("div",x,[s(r,null,{default:o(()=>[t("div",y,[e[1]||(e[1]=t("span",null,"交易规则设置",-1)),s(_,{type:"primary"},{default:o(()=>[s(d,null,{default:o(()=>[s(l)]),_:1}),e[0]||(e[0]=m(" 保存设置 "))]),_:1,__:[0]})]),t("div",I,[e[2]||(e[2]=t("p",null,"交易设置功能正在开发中...",-1)),t("p",null,"当前商城ID: "+f(a.mallId),1)])]),_:1})])])}const N=c(g,[["render",S],["__scopeId","data-v-69621e3c"]]);export{N as default};
