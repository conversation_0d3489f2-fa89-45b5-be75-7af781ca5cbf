import{d as Q,u as J,q as O,r as g,E as o,x as W,b as d,A as X,o as v,e as Y,k as t,f as y,H as Z,h as l,y as u,C as h,ap as ee,B as ae,g as k,ac as H,j as P,t as T,L as te,Q as ne,G as E,_ as le}from"./index-5502c85d.js";import{l as oe}from"./lodash-93e8a199.js";import{g as se,s as re,d as ie}from"./minipage-5327b8a3.js";const de={class:"miniprogram-container"},ue={class:"header"},ce={class:"page-name-cell"},pe={class:"action-buttons"},me={class:"pagination"},_e=Q({__name:"index",setup(ge){const z=J(),D=O(),x=g([]),B=g(0),s=g(1),C=g(10),c=g(""),I=g(!1),V=oe.debounce(()=>{s.value=1,r()},600),p=()=>{const a=D.query.mallId||D.params.mallId;return Array.isArray(a)?a[0]:a},r=async()=>{I.value=!0;try{const a=p();if(!a){o.error("缺少商城ID参数");return}const e={mallId:a,page:s.value,pageSize:C.value};c.value&&(e.keyword=c.value);const i=await se(a,e);x.value=i.data.records.map(m=>({id:m.id,name:m.pageName,updateTime:U(m.updateTime),isHomePage:m.isHomePage===1})),B.value=i.data.total}catch(a){console.error("获取页面列表失败:",a),o.error("获取页面列表失败")}finally{I.value=!1}},S=()=>{V()},L=()=>{c.value="",s.value=1,r()},M=()=>{s.value=1,r()},N=()=>{c.value="",s.value=1,r()},$=a=>{C.value=a,s.value=1,r()},q=()=>{const a=p();if(!a){o.error("缺少商城ID参数");return}z.push({path:"/decoration/miniprogram/editer",query:{mallId:a,mode:"create"}})},A=a=>{const e=p();if(!e){o.error("缺少商城ID参数");return}z.push({path:"/decoration/miniprogram/editer",query:{mallId:e,pageId:a.id,mode:"edit"}})},K=async a=>{try{await E.confirm(`确定要将页面 "${a.name}" 设为首页吗？`,"设为首页",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=p();if(!e){o.error("缺少商城ID参数");return}await re(e,a.id),x.value.forEach(i=>{i.isHomePage=i.id===a.id}),o.success("设置成功")}catch(e){e!=="cancel"&&(console.error("设为首页失败:",e),o.error("设为首页失败"))}},R=async a=>{try{await E.confirm(`确定要删除页面 "${a.name}" 吗？删除后无法恢复。`,"删除页面",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=p();if(!e){o.error("缺少商城ID参数");return}await ie(e,a.id),o.success("删除成功"),r()}catch(e){e!=="cancel"&&(console.error("删除失败:",e),o.error("删除失败"))}},U=a=>a?new Date(a).toLocaleString("zh-CN"):"";return W(()=>{console.log("微页面列表页面已加载，mallId:",p()),r()}),(a,e)=>{const i=d("el-alert"),m=d("el-input"),_=d("el-button"),f=d("el-icon"),b=d("el-table-column"),j=d("el-table"),F=d("el-pagination"),G=X("loading");return v(),Y("div",de,[t(i,{title:"温馨提示：微页面指定为商城首页后不能删除。",type:"info",closable:!1,style:{"margin-bottom":"20px"}}),y("div",ue,[t(m,{modelValue:c.value,"onUpdate:modelValue":e[0]||(e[0]=n=>c.value=n),placeholder:"请输入页面名称",clearable:"",style:{width:"200px","margin-right":"10px"},onKeyup:Z(S,["enter"]),onInput:S,onClear:L},null,8,["modelValue"]),t(_,{type:"primary",onClick:M},{default:l(()=>e[3]||(e[3]=[u("搜索")])),_:1,__:[3]}),t(_,{onClick:N},{default:l(()=>e[4]||(e[4]=[u("重置")])),_:1,__:[4]}),t(_,{type:"primary",onClick:q},{default:l(()=>[t(f,null,{default:l(()=>[t(h(ee))]),_:1}),e[5]||(e[5]=u(" 新建微页面 "))]),_:1,__:[5]})]),ae((v(),k(j,{height:"calc(100vh - 280px)",data:x.value,style:{"margin-top":"16px"}},{default:l(()=>[t(b,{prop:"name",label:"页面名称","min-width":"150"},{default:l(({row:n})=>[y("div",ce,[n.isHomePage?(v(),k(f,{key:0,class:"home-icon"},{default:l(()=>[t(h(H))]),_:1})):P("",!0),y("span",null,T(n.name),1)])]),_:1}),t(b,{prop:"updateTime",label:"更新时间",width:"180"},{default:l(({row:n})=>[u(T(n.updateTime),1)]),_:1}),t(b,{label:"操作",width:"300",align:"right"},{default:l(({row:n})=>[y("div",pe,[n.isHomePage?P("",!0):(v(),k(_,{key:0,link:"",type:"primary",size:"small",onClick:w=>K(n)},{default:l(()=>[t(f,null,{default:l(()=>[t(h(H))]),_:1}),e[6]||(e[6]=u(" 设为首页 "))]),_:2,__:[6]},1032,["onClick"])),t(_,{link:"",type:"primary",size:"small",onClick:w=>A(n)},{default:l(()=>[t(f,null,{default:l(()=>[t(h(te))]),_:1}),e[7]||(e[7]=u(" 编辑界面 "))]),_:2,__:[7]},1032,["onClick"]),n.isHomePage?P("",!0):(v(),k(_,{key:1,link:"",type:"danger",size:"small",onClick:w=>R(n)},{default:l(()=>[t(f,null,{default:l(()=>[t(h(ne))]),_:1}),e[8]||(e[8]=u(" 删除 "))]),_:2,__:[8]},1032,["onClick"]))])]),_:1})]),_:1},8,["data"])),[[G,I.value]]),y("div",me,[t(F,{"current-page":s.value,"onUpdate:currentPage":e[1]||(e[1]=n=>s.value=n),"page-size":C.value,"onUpdate:pageSize":e[2]||(e[2]=n=>C.value=n),total:B.value,background:"","page-sizes":[10,20,50,100],onSizeChange:$,onCurrentChange:r},null,8,["current-page","page-size","total"])])])}}});const he=le(_e,[["__scopeId","data-v-60d84ea8"]]);export{he as default};
