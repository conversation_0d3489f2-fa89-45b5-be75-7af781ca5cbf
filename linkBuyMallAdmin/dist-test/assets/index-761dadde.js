import{$ as X,d as kt,r as C,z as O,x as xt,E as x,c as St,b,A as Vt,o as c,e as m,f as s,k as i,h as a,y as u,j as _,t as n,F as q,m as te,g as S,B as Ct,C as wt,D as Nt,n as I,G as he,_ as Ut}from"./index-5502c85d.js";import{g as $t}from"./category-4961b1f6.js";function Lt(j){return X.get({url:"/company-product-management/list",params:j})}function be(j){return X.get({url:`/company-product-management/${j}`})}function oe(j){return X.post({url:"/company-product-management/batch/status",data:j})}function je(j){return X.post({url:"/company-product-management/batch/price",data:j})}function It(){return X.get({url:"/company-product-management/statistics"})}function Tt(){return X.get({url:"/supplier/list",params:{page:1,size:200}})}const zt={class:"company-product-container"},Mt={class:"page-header"},jt={class:"header-right"},Bt={key:0,class:"batch-actions"},Et={class:"statistics-cards"},Dt={class:"stat-content"},At={class:"stat-number"},Kt={class:"stat-content"},Rt={class:"stat-number"},Ft={class:"stat-content"},Ot={class:"stat-number"},qt={class:"stat-content"},Ht={class:"stat-number"},Gt={class:"stat-content"},Xt={class:"stat-number"},Jt={class:"header"},Qt={class:"product-info"},Wt={class:"product-image"},Yt={class:"image-slot"},Zt={class:"product-details"},es={class:"product-name"},ts={class:"product-code"},ss={class:"product-supplier"},as={class:"price"},ls={key:0,class:"price"},is={class:"stock"},rs={class:"pagination"},ns={key:0},os={key:1},us={key:0,style:{"margin-left":"8px"}},cs={key:1,style:{"margin-left":"8px"}},ds={key:0,class:"error-message"},ps={class:"dialog-footer"},ms={key:0,class:"price-adjust-container"},vs={class:"sku-list-section"},fs={class:"product-info-cell"},_s={class:"product-image"},gs={class:"product-details"},hs={class:"product-name"},bs={class:"product-supplier"},ys={class:"spec-values"},Ps={class:"price-text cost-price"},ks={class:"price-text strike-price"},xs={class:"price-text sale-price"},Ss={class:"price-formula-section"},Vs={class:"formula-container"},Cs={class:"formula-base"},ws={class:"formula-tip"},Ns={key:0,class:"error-message"},Us={class:"dialog-footer"},$s={key:0,class:"product-detail-container"},Ls={class:"product-header"},Is={class:"product-main-info"},Ts={class:"product-image"},zs={class:"product-basic"},Ms={class:"product-title"},js={class:"product-meta"},Bs={class:"product-id"},Es={class:"product-price-info"},Ds={class:"price-item"},As={class:"price-value cost-price"},Ks={class:"price-item"},Rs={class:"price-value sale-price"},Fs={class:"price-item"},Os={class:"price-value strike-price"},qs={class:"price-item"},Hs={class:"price-value purchase-price"},Gs={class:"stats-item"},Xs={class:"stats-value"},Js={class:"stats-item"},Qs={class:"stats-value"},Ws={class:"basic-info-content"},Ys={key:0,class:"physical-attrs"},Zs={class:"shipping-settings"},ea={key:1,class:"product-images-section"},ta={class:"images-gallery"},sa={key:2,class:"product-description"},aa=["innerHTML"],la={class:"spec-stock-content"},ia={key:0},ra={class:"spec-values"},na={class:"price-text cost-price"},oa={class:"price-text sale-price"},ua={class:"price-text purchase-price"},ca={class:"price-text strike-price"},da={class:"stock-info"},pa={key:1,class:"no-image"},ma={class:"spec-summary"},va={key:1,class:"no-data"},fa={class:"preview-only-container"},_a={class:"centered-phone-preview"},ga={class:"phone-container"},ha={class:"phone-screen"},ba={class:"product-info-area"},ya={class:"product-carousel"},Pa={class:"carousel-container"},ka=["src","alt"],xa={key:1,src:"https://via.placeholder.com/375x300/f0f0f0/999?text=商品图片",alt:"默认商品图片",class:"product-main-image"},Sa={key:2,class:"carousel-indicators"},Va={class:"product-info-section"},Ca={class:"product-price"},wa={class:"current-price"},Na={key:0,class:"original-price"},Ua={key:1,class:"original-price"},$a={class:"product-title"},La={class:"product-stock"},Ia={key:0,class:"spec-selection"},Ta={class:"spec-label"},za={class:"spec-options"},Ma=["onClick"],ja={class:"product-detail-content"},Ba={class:"detail-section"},Ea={key:0,class:"detail-html"},Da=["innerHTML"],Aa={key:1,class:"detail-placeholder"},Ka={class:"detail-section"},Ra={class:"param-list"},Fa={key:0,class:"param-item"},Oa={class:"param-value"},qa=kt({__name:"index",setup(j){const ue=C(!1),ye=C([]),V=C([]),Pe=C([]),H=C({}),se=C(!1),r=C(null),ce=C("basic"),T=C([]),Be=C([]),ke=C(new Map),xe=C(null),de=C({}),h=O({productName:"",supplierId:null,status:null,source:null}),N=O({page:1,size:10,total:0}),J=C(!1),ae=C(""),Ee=C("批量操作"),z=O({status:1,reason:"",loading:!1}),w=O({priceUpdateType:"FIXED_AMOUNT",value:0,reason:"",loading:!1}),p=O({priceBase:"costPrice",multiplier:1,adjustment:0,reason:"",loading:!1}),A=C(!1),k=C(null),U=O({reason:""}),K=O({reason:""});let pe=null;xt(()=>{L(),B(),De(),Ze()});const L=async()=>{ue.value=!0;try{const l={page:N.page,size:N.size,...h},e=await Lt(l);ye.value=e.data.records,N.total=e.data.total}catch(l){console.error("获取商品列表失败:",l),x.error("获取商品列表失败")}finally{ue.value=!1}},B=async()=>{try{const l=await It();H.value=l.data}catch(l){console.error("获取统计数据失败:",l)}},De=async()=>{try{const l=await Tt();Pe.value=l.data.records||[]}catch(l){console.error("获取供应商选项失败:",l)}},le=()=>{pe&&clearTimeout(pe),pe=setTimeout(()=>{N.page=1,L()},300)},Ae=()=>{L(),B()},Ke=l=>{V.value=l},Re=l=>{N.size=l,N.page=1,L()},Fe=async l=>{try{const e=await be(l.id);r.value=e.data,r.value.images&&r.value.images.length>0?T.value=r.value.images.map(o=>o.imgUrl):T.value=r.value.mainImageUrl?[r.value.mainImageUrl]:[],ce.value="basic",se.value=!0}catch(e){console.error("获取商品详情失败:",e),x.error("获取商品详情失败")}},Oe=async l=>{const e=l.status===1?0:1,o=e===1?"启用":"禁用";try{await he.confirm(`确定要${o}该商品吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await oe({ids:[l.id],status:e,reason:`${o}商品`}),l.status=e,x.success(`${o}成功`),B()}catch(d){d!=="cancel"&&x.error(`${o}失败`)}},qe=async l=>{try{const e=await be(l.id);k.value=e.data,k.value.skuList&&k.value.skuList.forEach(o=>{o.adjustedSalePrice=Number(o.salePrice)||0}),p.priceBase="costPrice",p.multiplier=1,p.adjustment=0,p.reason="",A.value=!0}catch{x.error("获取商品详情失败")}},He=async()=>{ae.value==="status"?await Ge():ae.value==="price"&&await Xe()},Ge=async()=>{z.loading=!0;try{await oe({ids:V.value.map(l=>l.id),status:z.status,reason:z.reason}),x.success("批量操作成功"),J.value=!1,V.value=[],L(),B()}catch{x.error("批量操作失败")}finally{z.loading=!1}},Xe=async()=>{if(K.reason="",!w.value){x.warning("请输入调价值");return}if(!w.reason||!w.reason.trim()){K.reason="调价原因为必填项，请输入调价原因";return}w.loading=!0;try{(await je({ids:V.value.map(e=>e.id),priceUpdateType:w.priceUpdateType,value:w.value,reason:w.reason})).code===200&&(x.success("批量调价成功"),J.value=!1,V.value=[],L(),B())}catch{x.error("批量调价失败")}finally{w.loading=!1}},Je=()=>{Se()},Se=()=>{h.productName="",h.supplierId=null,h.status=null,h.source=null,N.page=1,L()},Q=l=>{switch(h.productName="",h.supplierId=null,l){case"all":h.status=null,h.source=null;break;case"onSale":h.status=1,h.source=null;break;case"offSale":h.status=0,h.source=null;break;case"platform":h.status=null,h.source=1;break;case"ownSupplier":h.status=null,h.source=2;break}N.page=1,L()},Qe=()=>{se.value=!1,r.value=null,T.value=[]},g=l=>!l||l===0?"0.00":Number(l).toFixed(2),Ve=()=>{if(!r.value||!r.value.skuList)return"¥0.00";const l=r.value.skuList.map(d=>d.costPrice||0).filter(d=>d>0);if(l.length===0)return"¥0.00";const e=Math.min(...l),o=Math.max(...l);return e===o?`¥${g(e)}`:`¥${g(e)}-${g(o)}`},Ce=()=>{if(!r.value)return"¥0.00";if(r.value.skuList&&r.value.skuList.length>0){const l=r.value.skuList.map(d=>d.salePrice||0).filter(d=>d>0);if(l.length===0)return"¥0.00";const e=Math.min(...l),o=Math.max(...l);return e===o?`¥${g(e)}`:`¥${g(e)}-${g(o)}`}return r.value.salePriceMin&&r.value.salePriceMax?r.value.salePriceMin===r.value.salePriceMax?`¥${g(r.value.salePriceMin)}`:`¥${g(r.value.salePriceMin)}-${g(r.value.salePriceMax)}`:"¥0.00"},We=()=>{if(!r.value||!r.value.skuList)return"¥0.00";const l=r.value.skuList.map(d=>d.strikethroughPrice||0).filter(d=>d>0);if(l.length===0)return"¥0.00";const e=Math.min(...l),o=Math.max(...l);return e===o?`¥${g(e)}`:`¥${g(e)}-${g(o)}`},Ye=()=>{if(!r.value||!r.value.skuList)return"¥0.00";const l=r.value.skuList.map(d=>d.purchasePrice||0).filter(d=>d>0);if(l.length===0)return"¥0.00";const e=Math.min(...l),o=Math.max(...l);return e===o?`¥${g(e)}`:`¥${g(e)}-${g(o)}`},Ze=async()=>{try{const l=await $t();l.data&&(Be.value=l.data,we(l.data))}catch(l){console.error("加载分类树失败",l)}},we=(l,e=[])=>{!l||!Array.isArray(l)||l.forEach(o=>{const d=o.categoryName||o.categoryCode||`分类${o.id}`,y=o.id,v=o.children||[];if(y){const E=[...e,d],f=E.join(" / ");ke.value.set(y,f),v&&v.length>0&&we(v,E)}})},Ne=l=>l?ke.value.get(l)||"未知分类":"未分类",et=St(()=>r.value?!!(r.value.length||r.value.wide||r.value.tall||r.value.volume||r.value.weight):!1),tt=l=>l?`模板${l}`:"无",st=l=>{if(!l)return"无";const e={return:"退货",exchange:"换货",refund_only:"仅退款",cancel_order:"整单退款",return_shipping:"退费"};return l.split(",").map(o=>e[o.trim()]||o.trim()).join("、")},ie=l=>{if(!r.value)return 0;if(r.value.skuList&&r.value.skuList.length>1){const o=xe.value||r.value.skuList[0];if(!o)return 0;switch(l){case"cost":return Number(o.costPrice)||0;case"sale":return Number(o.salePrice)||0;case"purchase":return Number(o.purchasePrice)||0;case"strikethrough":return Number(o.strikethroughPrice)||0;default:return 0}}else{const o=r.value.skuList[0];if(!o)return 0;switch(l){case"cost":return Number(o.costPrice)||0;case"sale":return Number(o.salePrice)||0;case"purchase":return Number(o.purchasePrice)||0;case"strikethrough":return Number(o.strikethroughPrice)||0;default:return 0}}},Ue=()=>{if(!r.value||!r.value.skuList)return[];const l=[],e=r.value.skuList;if(r.value.spec1Name){const o=[...new Set(e.map(d=>d.spec1Value).filter(d=>d))];o.length>0&&l.push({name:r.value.spec1Name,values:o})}if(r.value.spec2Name){const o=[...new Set(e.map(d=>d.spec2Value).filter(d=>d))];o.length>0&&l.push({name:r.value.spec2Name,values:o})}if(r.value.spec3Name){const o=[...new Set(e.map(d=>d.spec3Value).filter(d=>d))];o.length>0&&l.push({name:r.value.spec3Name,values:o})}return l},at=(l,e)=>{de.value[l]=e;const o=r.value.skuList.find(d=>{const y=de.value;let v=!0;return y[r.value.spec1Name]&&d.spec1Value!==y[r.value.spec1Name]&&(v=!1),y[r.value.spec2Name]&&d.spec2Value!==y[r.value.spec2Name]&&(v=!1),y[r.value.spec3Name]&&d.spec3Value!==y[r.value.spec3Name]&&(v=!1),v});o&&(xe.value=o)},lt=l=>{console.log("图片加载失败:",l.target.src),l.target.style.display="none"},it=l=>{if(!l||!p.priceBase)return 0;const o=(l[p.priceBase]||0)*p.multiplier+p.adjustment;return Math.max(0,o)},rt=()=>{!k.value||!k.value.skuList||(k.value.skuList.forEach(l=>{l.adjustedSalePrice=it(l)}),U.reason="")},nt=(l,e)=>{l.adjustedSalePrice<0&&(l.adjustedSalePrice=0),U.reason=""},$e=()=>{A.value=!1,k.value=null,p.priceBase="costPrice",p.multiplier=1,p.adjustment=0,p.reason="",U.reason=""},ot=()=>{J.value=!1,K.reason=""},ut=async()=>{if(V.value.length===0){x.warning("请先选择商品");return}try{await he.confirm(`确定要启用选中的 ${V.value.length} 个商品吗？`,"批量启用",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await oe({ids:V.value.map(l=>l.id),status:1,reason:"批量启用商品"}),x.success("批量启用成功"),V.value=[],L(),B()}catch(l){l!=="cancel"&&x.error("批量启用失败")}},ct=async()=>{if(V.value.length===0){x.warning("请先选择商品");return}try{await he.confirm(`确定要禁用选中的 ${V.value.length} 个商品吗？`,"批量禁用",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await oe({ids:V.value.map(l=>l.id),status:0,reason:"批量禁用商品"}),x.success("批量禁用成功"),V.value=[],L(),B()}catch(l){l!=="cancel"&&x.error("批量禁用失败")}},dt=async()=>{var l;if(V.value.length===0){x.warning("请先选择商品");return}try{const e=[];for(const o of V.value){const y=(await be(o.id)).data;y.skuList&&y.skuList.forEach(v=>{v.productId=y.id,v.productName=y.productName,v.supplierName=y.supplierName,v.mainImageUrl=y.mainImageUrl,v.adjustedSalePrice=Number(v.salePrice)||0,e.push(v)})}k.value={id:"batch",productName:`批量调价 (${V.value.length}个商品)`,supplierName:"多个供应商",mainImageUrl:((l=V.value[0])==null?void 0:l.mainImageUrl)||"/default-product.png",skuList:e},p.priceBase="costPrice",p.multiplier=1,p.adjustment=0,p.reason="",A.value=!0}catch{x.error("获取商品详情失败")}},pt=async()=>{if(U.reason="",!p.reason||!p.reason.trim()){U.reason="调价原因为必填项，请输入调价原因";return}if(!k.value.skuList.some(e=>Number(e.adjustedSalePrice)!==Number(e.salePrice))){U.reason="价格没有发生变化，请设置调价参数或手动调整SKU价格后再提交";return}p.loading=!0;try{const e={productId:k.value.id,priceBase:p.priceBase,multiplier:p.multiplier,adjustment:p.adjustment,reason:p.reason,skuList:k.value.skuList.map(v=>({skuId:v.id,skuCode:v.skuCode,oldPrice:v.salePrice,newPrice:v.adjustedSalePrice,basePrice:v[p.priceBase]||0,priceChange:Number(v.adjustedSalePrice)-Number(v.salePrice)}))};console.log("调价数据:",e);const o=k.value.skuList.map(v=>({skuId:v.skuId,newPrice:v.adjustedSalePrice,oldPrice:v.salePrice}));let d=[];k.value.id==="batch"?d=[...new Set(k.value.skuList.map(E=>Number(E.productId)))]:d=[Number(k.value.id)],(await je({ids:d,priceUpdateType:"SET_SKU_PRICE",reason:p.reason,skuPriceUpdates:o})).code===200&&(x.success("调价成功"),A.value=!1,$e(),V.value=[],L(),B())}catch{x.error("调价失败")}finally{p.loading=!1}},W=(l,e)=>{const o=Number(l)||0,d=Number(e)||0;return o-d},Y=(l,e)=>{const o=Number(l)||0,d=Number(e)||0;return o===0?0:(o-d)/o*100},me=l=>`${l.toFixed(2)}%`,Le=l=>{const e=W(l.salePrice,l.costPrice);return e>0?"profit-positive":e<0?"profit-negative":"profit-zero"},Ie=l=>{const e=Y(l.salePrice,l.costPrice);return e>0?"profit-rate-positive":e<0?"profit-rate-negative":"profit-rate-zero"},mt=l=>{const e=W(l.adjustedSalePrice,l.costPrice);return e>0?"profit-positive":e<0?"profit-negative":"profit-zero"},vt=l=>{const e=Y(l.adjustedSalePrice,l.costPrice);return e>0?"profit-rate-positive":e<0?"profit-rate-negative":"profit-rate-zero"};return(l,e)=>{const o=b("el-button"),d=b("el-card"),y=b("el-input"),v=b("el-option"),E=b("el-select"),f=b("el-table-column"),ft=b("el-icon"),Z=b("el-image"),$=b("el-tag"),ve=b("el-table"),_t=b("el-pagination"),R=b("el-radio"),fe=b("el-radio-group"),D=b("el-form-item"),_e=b("el-form"),re=b("el-input-number"),Te=b("el-dialog"),gt=b("el-text"),P=b("el-descriptions-item"),ne=b("el-descriptions"),ge=b("el-tab-pane"),ht=b("el-empty"),bt=b("el-tabs"),yt=b("el-drawer"),Pt=Vt("loading");return c(),m("div",zt,[s("div",Mt,[e[34]||(e[34]=s("div",{class:"header-left"},[s("h2",null,"商品管理"),s("p",null,"管理客户商品的上下架、调价、库存等运营功能")],-1)),s("div",jt,[V.value.length>0?(c(),m("div",Bt,[i(o,{type:"primary",onClick:ut},{default:a(()=>e[30]||(e[30]=[u("启用")])),_:1,__:[30]}),i(o,{type:"danger",onClick:ct},{default:a(()=>e[31]||(e[31]=[u("禁用")])),_:1,__:[31]}),i(o,{type:"warning",onClick:dt},{default:a(()=>e[32]||(e[32]=[u("调价")])),_:1,__:[32]})])):_("",!0),i(o,{onClick:Ae},{default:a(()=>e[33]||(e[33]=[u("刷新")])),_:1,__:[33]})])]),s("div",Et,[i(d,{class:"stat-card",onClick:e[0]||(e[0]=t=>Q("all"))},{default:a(()=>[s("div",Dt,[s("div",At,n(H.value.total),1),e[35]||(e[35]=s("div",{class:"stat-label"},"总商品数",-1))])]),_:1}),i(d,{class:"stat-card",onClick:e[1]||(e[1]=t=>Q("onSale"))},{default:a(()=>[s("div",Kt,[s("div",Rt,n(H.value.onSale),1),e[36]||(e[36]=s("div",{class:"stat-label"},"在售商品",-1))])]),_:1}),i(d,{class:"stat-card",onClick:e[2]||(e[2]=t=>Q("offSale"))},{default:a(()=>[s("div",Ft,[s("div",Ot,n(H.value.offSale),1),e[37]||(e[37]=s("div",{class:"stat-label"},"下架商品",-1))])]),_:1}),i(d,{class:"stat-card",onClick:e[3]||(e[3]=t=>Q("platform"))},{default:a(()=>[s("div",qt,[s("div",Ht,n(H.value.platform),1),e[38]||(e[38]=s("div",{class:"stat-label"},"平台商品",-1))])]),_:1}),i(d,{class:"stat-card",onClick:e[4]||(e[4]=t=>Q("ownSupplier"))},{default:a(()=>[s("div",Gt,[s("div",Xt,n(H.value.ownSupplier),1),e[39]||(e[39]=s("div",{class:"stat-label"},"自有供应商",-1))])]),_:1})]),s("div",Jt,[i(y,{modelValue:h.productName,"onUpdate:modelValue":e[5]||(e[5]=t=>h.productName=t),placeholder:"搜索商品名称",clearable:"",style:{width:"200px","margin-right":"10px"},onInput:le,onClear:Je},null,8,["modelValue"]),i(E,{modelValue:h.supplierId,"onUpdate:modelValue":e[6]||(e[6]=t=>h.supplierId=t),placeholder:"选择供应商",clearable:"",style:{width:"200px","margin-right":"10px"},onChange:le},{default:a(()=>[(c(!0),m(q,null,te(Pe.value,t=>(c(),S(v,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),i(E,{modelValue:h.status,"onUpdate:modelValue":e[7]||(e[7]=t=>h.status=t),placeholder:"商品状态",clearable:"",style:{width:"150px","margin-right":"10px"},onChange:le},{default:a(()=>[i(v,{label:"启用",value:1}),i(v,{label:"禁用",value:0})]),_:1},8,["modelValue"]),i(E,{modelValue:h.source,"onUpdate:modelValue":e[8]||(e[8]=t=>h.source=t),placeholder:"商品来源",clearable:"",style:{width:"150px","margin-right":"10px"},onChange:le},{default:a(()=>[i(v,{label:"平台商品",value:1}),i(v,{label:"自有供应商",value:2})]),_:1},8,["modelValue"]),i(o,{onClick:Se},{default:a(()=>e[40]||(e[40]=[u("重置")])),_:1,__:[40]})]),Ct((c(),S(ve,{height:"calc(100vh - 440px)",data:ye.value,style:{"margin-top":"16px",width:"100%"},onSelectionChange:Ke},{default:a(()=>[i(f,{type:"selection",width:"55"}),i(f,{label:"商品信息",width:"350"},{default:a(({row:t})=>[s("div",Qt,[s("div",Wt,[i(Z,{src:t.mainImageUrl,fit:"cover",style:{width:"60px",height:"60px","border-radius":"4px"}},{error:a(()=>[s("div",Yt,[i(ft,null,{default:a(()=>[i(wt(Nt))]),_:1})])]),_:2},1032,["src"])]),s("div",Zt,[s("div",es,n(t.productName),1),s("div",ts,"编码："+n(t.spuCode),1),s("div",ss," 供应商："+n(t.supplierName),1)])])]),_:1}),i(f,{label:"商品分类",width:"250",align:"center"},{default:a(({row:t})=>[u(n(Ne(t.categoryId)),1)]),_:1}),i(f,{label:"售价范围",width:"150",align:"center"},{default:a(({row:t})=>[s("div",null,[s("div",as,"¥"+n(t.salePriceMin),1),t.salePriceMax!==t.salePriceMin?(c(),m("div",ls,"- ¥"+n(t.salePriceMax),1)):_("",!0)])]),_:1}),i(f,{label:"SKU/库存",width:"100",align:"center"},{default:a(({row:t})=>[s("div",null,[s("div",null,n(t.skuCount)+"个SKU",1),s("div",is,"库存"+n(t.totalStock),1)])]),_:1}),i(f,{prop:"status",label:"状态",width:"100",align:"center"},{default:a(({row:t})=>[i($,{type:t.status===1?"success":"danger"},{default:a(()=>[u(n(t.status===1?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),i(f,{label:"来源",width:"100",align:"center"},{default:a(({row:t})=>[i($,{type:t.source===1?"primary":"warning"},{default:a(()=>[u(n(t.source===1?"平台":"自有"),1)]),_:2},1032,["type"])]),_:1}),i(f,{label:"操作",width:"240",align:"center"},{default:a(({row:t})=>[i(o,{link:"",type:"primary",size:"small",onClick:M=>Fe(t)},{default:a(()=>e[41]||(e[41]=[u("查看")])),_:2,__:[41]},1032,["onClick"]),i(o,{link:"",type:t.status===1?"danger":"success",size:"small",onClick:M=>Oe(t)},{default:a(()=>[u(n(t.status===1?"禁用":"启用"),1)]),_:2},1032,["type","onClick"]),i(o,{link:"",type:"warning",size:"small",onClick:M=>qe(t)},{default:a(()=>e[42]||(e[42]=[u("调价")])),_:2,__:[42]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Pt,ue.value]]),s("div",rs,[i(_t,{"current-page":N.page,"onUpdate:currentPage":e[9]||(e[9]=t=>N.page=t),"page-size":N.size,"onUpdate:pageSize":e[10]||(e[10]=t=>N.size=t),total:N.total,background:"","page-sizes":[10,20,50,100],onSizeChange:Re,onCurrentChange:L},null,8,["current-page","page-size","total"])]),i(Te,{modelValue:J.value,"onUpdate:modelValue":e[17]||(e[17]=t=>J.value=t),title:Ee.value,width:"600px"},{footer:a(()=>[s("div",ps,[i(o,{onClick:ot},{default:a(()=>e[48]||(e[48]=[u("取消")])),_:1,__:[48]}),i(o,{type:"primary",onClick:He},{default:a(()=>e[49]||(e[49]=[u("确定")])),_:1,__:[49]})])]),default:a(()=>[ae.value==="status"?(c(),m("div",ns,[i(_e,{model:z,"label-width":"100px"},{default:a(()=>[i(D,{label:"操作类型"},{default:a(()=>[i(fe,{modelValue:z.status,"onUpdate:modelValue":e[11]||(e[11]=t=>z.status=t)},{default:a(()=>[i(R,{label:1},{default:a(()=>e[43]||(e[43]=[u("启用")])),_:1,__:[43]}),i(R,{label:0},{default:a(()=>e[44]||(e[44]=[u("禁用")])),_:1,__:[44]})]),_:1},8,["modelValue"])]),_:1}),i(D,{label:"操作原因"},{default:a(()=>[i(y,{modelValue:z.reason,"onUpdate:modelValue":e[12]||(e[12]=t=>z.reason=t),type:"textarea",rows:3,placeholder:"请输入操作原因"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])])):_("",!0),ae.value==="price"?(c(),m("div",os,[i(_e,{model:w,"label-width":"100px"},{default:a(()=>[i(D,{label:"调价方式"},{default:a(()=>[i(fe,{modelValue:w.priceUpdateType,"onUpdate:modelValue":e[13]||(e[13]=t=>w.priceUpdateType=t)},{default:a(()=>[i(R,{label:"FIXED_AMOUNT"},{default:a(()=>e[45]||(e[45]=[u("固定金额")])),_:1,__:[45]}),i(R,{label:"PERCENTAGE"},{default:a(()=>e[46]||(e[46]=[u("百分比")])),_:1,__:[46]}),i(R,{label:"SET_PRICE"},{default:a(()=>e[47]||(e[47]=[u("设置价格")])),_:1,__:[47]})]),_:1},8,["modelValue"])]),_:1}),i(D,{label:"调价数值"},{default:a(()=>[i(re,{modelValue:w.value,"onUpdate:modelValue":e[14]||(e[14]=t=>w.value=t),precision:2,step:.01,min:0,style:{width:"200px"}},null,8,["modelValue"]),w.priceUpdateType==="PERCENTAGE"?(c(),m("span",us,"%")):(c(),m("span",cs,"元"))]),_:1}),i(D,{label:"调价原因",required:""},{default:a(()=>[i(y,{modelValue:w.reason,"onUpdate:modelValue":e[15]||(e[15]=t=>w.reason=t),type:"textarea",rows:3,placeholder:"请输入调价原因（必填）",class:I({"is-error":K.reason}),onInput:e[16]||(e[16]=t=>K.reason="")},null,8,["modelValue","class"]),K.reason?(c(),m("div",ds,n(K.reason),1)):_("",!0)]),_:1})]),_:1},8,["model"])])):_("",!0)]),_:1},8,["modelValue","title"]),i(Te,{modelValue:A.value,"onUpdate:modelValue":e[27]||(e[27]=t=>A.value=t),title:"商品调价",width:"800px","before-close":$e},{footer:a(()=>[s("div",Us,[i(o,{onClick:e[26]||(e[26]=t=>A.value=!1)},{default:a(()=>e[61]||(e[61]=[u("取消")])),_:1,__:[61]}),i(o,{type:"primary",onClick:pt,loading:p.loading},{default:a(()=>e[62]||(e[62]=[u("确认调价")])),_:1,__:[62]},8,["loading"])])]),default:a(()=>[k.value?(c(),m("div",ms,[s("div",vs,[e[51]||(e[51]=s("h4",null,"SKU列表",-1)),i(ve,{data:k.value.skuList,border:"",size:"small",height:"400"},{default:a(()=>[i(f,{label:"商品信息",width:"200"},{default:a(({row:t})=>[s("div",fs,[s("div",_s,[i(Z,{src:t.mainImageUrl||k.value.mainImageUrl||"/default-product.png",fit:"cover",style:{width:"50px",height:"50px","border-radius":"4px"}},null,8,["src"])]),s("div",gs,[s("div",hs,n(t.productName||k.value.productName),1),s("div",bs,n(t.supplierName||k.value.supplierName),1)])])]),_:1}),i(f,{label:"规格",width:"100"},{default:a(({row:t})=>[s("div",ys,[t.spec1Value||t.spec2Value||t.spec3Value?(c(),m(q,{key:0},[t.spec1Value?(c(),S($,{key:0,size:"small",class:"spec-tag"},{default:a(()=>[u(n(t.spec1Value),1)]),_:2},1024)):_("",!0),t.spec2Value?(c(),S($,{key:1,size:"small",class:"spec-tag"},{default:a(()=>[u(n(t.spec2Value),1)]),_:2},1024)):_("",!0),t.spec3Value?(c(),S($,{key:2,size:"small",class:"spec-tag"},{default:a(()=>[u(n(t.spec3Value),1)]),_:2},1024)):_("",!0)],64)):(c(),S($,{key:1,size:"small",class:"spec-tag default-spec"},{default:a(()=>e[50]||(e[50]=[u("标准规格")])),_:1,__:[50]}))])]),_:1}),i(f,{label:"成本价",width:"100"},{default:a(({row:t})=>[s("span",Ps,"¥"+n(g(t.costPrice)),1)]),_:1}),i(f,{label:"划线价",width:"100"},{default:a(({row:t})=>[s("span",ks,"¥"+n(g(t.strikethroughPrice)),1)]),_:1}),i(f,{label:"当前销售价",width:"100"},{default:a(({row:t})=>[s("span",xs,"¥"+n(g(t.salePrice)),1)]),_:1}),i(f,{label:"调整后销售价",width:"150"},{default:a(({row:t,$index:M})=>[i(re,{modelValue:t.adjustedSalePrice,"onUpdate:modelValue":ee=>t.adjustedSalePrice=ee,precision:2,step:.01,min:0,size:"small",style:{width:"130px"},onChange:ee=>nt(t,M)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),i(f,{label:"当前毛利",width:"100"},{default:a(({row:t})=>[s("span",{class:I(["price-text profit",Le(t)])}," ¥"+n(g(W(t.salePrice,t.costPrice))),3)]),_:1}),i(f,{label:"调整后毛利",width:"100"},{default:a(({row:t})=>[s("span",{class:I(["price-text profit",mt(t)])}," ¥"+n(g(W(t.adjustedSalePrice,t.costPrice))),3)]),_:1}),i(f,{label:"当前毛利率",width:"100"},{default:a(({row:t})=>[s("span",{class:I(["price-text profit-rate",Ie(t)])},n(me(Y(t.salePrice,t.costPrice))),3)]),_:1}),i(f,{label:"调整后毛利率",width:"110"},{default:a(({row:t})=>[s("span",{class:I(["price-text profit-rate",vt(t)])},n(me(Y(t.adjustedSalePrice,t.costPrice))),3)]),_:1})]),_:1},8,["data"])]),s("div",Ss,[e[60]||(e[60]=s("h4",null,"调价公式",-1)),i(_e,{model:p,"label-width":"100px"},{default:a(()=>[i(D,{label:"价格基准"},{default:a(()=>[i(fe,{modelValue:p.priceBase,"onUpdate:modelValue":e[18]||(e[18]=t=>p.priceBase=t),onChange:e[19]||(e[19]=t=>U.reason="")},{default:a(()=>[i(R,{label:"costPrice"},{default:a(()=>e[52]||(e[52]=[u("成本价")])),_:1,__:[52]}),i(R,{label:"strikethroughPrice"},{default:a(()=>e[53]||(e[53]=[u("划线价")])),_:1,__:[53]})]),_:1},8,["modelValue"])]),_:1}),i(D,{label:"调价公式"},{default:a(()=>[s("div",Vs,[e[55]||(e[55]=s("span",{class:"formula-text"},"调整价格 = ",-1)),s("span",Cs,n(p.priceBase==="costPrice"?"成本价":"划线价"),1),e[56]||(e[56]=s("span",{class:"formula-text"}," × ",-1)),i(re,{modelValue:p.multiplier,"onUpdate:modelValue":e[20]||(e[20]=t=>p.multiplier=t),precision:2,step:.01,min:0,size:"small",style:{width:"120px"},onChange:e[21]||(e[21]=t=>U.reason="")},null,8,["modelValue"]),e[57]||(e[57]=s("span",{class:"formula-text"}," + ",-1)),i(re,{modelValue:p.adjustment,"onUpdate:modelValue":e[22]||(e[22]=t=>p.adjustment=t),precision:2,step:.01,size:"small",style:{width:"120px"},onChange:e[23]||(e[23]=t=>U.reason="")},null,8,["modelValue"]),e[58]||(e[58]=s("span",{class:"formula-text"}," 元",-1)),i(o,{type:"primary",size:"small",onClick:rt,style:{"margin-left":"15px"}},{default:a(()=>e[54]||(e[54]=[u(" 应用到所有SKU ")])),_:1,__:[54]})]),s("div",ws,[i(gt,{type:"info",size:"small"},{default:a(()=>e[59]||(e[59]=[u(' 💡 设置好调价公式后，点击"应用到所有SKU"按钮批量更新价格，或在表格中手动编辑单个SKU的价格 ')])),_:1,__:[59]})])]),_:1}),i(D,{label:"调价原因",required:""},{default:a(()=>[i(y,{modelValue:p.reason,"onUpdate:modelValue":e[24]||(e[24]=t=>p.reason=t),type:"textarea",rows:3,placeholder:"请输入调价原因（必填）",class:I({"is-error":U.reason}),onInput:e[25]||(e[25]=t=>U.reason="")},null,8,["modelValue","class"]),U.reason?(c(),m("div",Ns,n(U.reason),1)):_("",!0)]),_:1})]),_:1},8,["model"])])])):_("",!0)]),_:1},8,["modelValue"]),i(yt,{modelValue:se.value,"onUpdate:modelValue":e[29]||(e[29]=t=>se.value=t),title:"商品详情",size:"80%","before-close":Qe},{default:a(()=>[r.value?(c(),m("div",$s,[s("div",Ls,[s("div",Is,[s("div",Ts,[i(Z,{src:r.value.mainImageUrl||"/default-product.png","preview-src-list":T.value,fit:"cover",style:{width:"120px",height:"120px","border-radius":"8px"}},null,8,["src","preview-src-list"])]),s("div",zs,[s("h2",Ms,n(r.value.productName),1),s("div",js,[s("span",Bs,"商品ID: "+n(r.value.id),1),i($,{type:r.value.status===1?"success":"danger",class:"status-tag"},{default:a(()=>[u(n(r.value.status===1?"启用":"禁用"),1)]),_:1},8,["type"]),i($,{type:r.value.source===1?"primary":"warning",class:"source-tag"},{default:a(()=>[u(n(r.value.source===1?"平台商品":"自有供应商"),1)]),_:1},8,["type"])])])]),s("div",Es,[s("div",Ds,[e[63]||(e[63]=s("span",{class:"price-label"},"成本价",-1)),s("span",As,n(Ve()),1)]),s("div",Ks,[e[64]||(e[64]=s("span",{class:"price-label"},"销售价",-1)),s("span",Rs,n(Ce()),1)]),s("div",Fs,[e[65]||(e[65]=s("span",{class:"price-label"},"划线价",-1)),s("span",Os,n(We()),1)]),s("div",qs,[e[66]||(e[66]=s("span",{class:"price-label"},"采购价",-1)),s("span",Hs,n(Ye()),1)]),s("div",Gs,[e[67]||(e[67]=s("span",{class:"stats-label"},"SKU数量",-1)),s("span",Xs,n(r.value.skuCount||0),1)]),s("div",Js,[e[68]||(e[68]=s("span",{class:"stats-label"},"库存",-1)),s("span",Qs,n(r.value.totalStock||0),1)])])]),i(bt,{modelValue:ce.value,"onUpdate:modelValue":e[28]||(e[28]=t=>ce.value=t),class:"detail-tabs"},{default:a(()=>[i(ge,{label:"基本信息",name:"basic"},{default:a(()=>[s("div",Ws,[i(ne,{column:2,border:""},{default:a(()=>[i(P,{label:"商品编码"},{default:a(()=>[u(n(r.value.spuCode),1)]),_:1}),i(P,{label:"商品分类"},{default:a(()=>[u(n(Ne(r.value.categoryId)),1)]),_:1}),i(P,{label:"条形码"},{default:a(()=>[u(n(r.value.barcode||"无"),1)]),_:1}),i(P,{label:"商品单位"},{default:a(()=>[u(n(r.value.unit||"件"),1)]),_:1}),i(P,{label:"商品类型"},{default:a(()=>[u(n(r.value.type===1?"实物商品":"虚拟商品"),1)]),_:1}),i(P,{label:"供应商"},{default:a(()=>[u(n(r.value.supplierName),1)]),_:1}),i(P,{label:"创建时间"},{default:a(()=>[u(n(r.value.createTime),1)]),_:1}),i(P,{label:"更新时间"},{default:a(()=>[u(n(r.value.updateTime),1)]),_:1})]),_:1}),et.value?(c(),m("div",Ys,[e[69]||(e[69]=s("h3",null,"物理属性",-1)),i(ne,{column:3,border:""},{default:a(()=>[r.value.length?(c(),S(P,{key:0,label:"长度"},{default:a(()=>[u(n(r.value.length)+"cm",1)]),_:1})):_("",!0),r.value.wide?(c(),S(P,{key:1,label:"宽度"},{default:a(()=>[u(n(r.value.wide)+"cm",1)]),_:1})):_("",!0),r.value.tall?(c(),S(P,{key:2,label:"高度"},{default:a(()=>[u(n(r.value.tall)+"cm",1)]),_:1})):_("",!0),r.value.volume?(c(),S(P,{key:3,label:"体积"},{default:a(()=>[u(n(r.value.volume)+"cm³",1)]),_:1})):_("",!0),r.value.weight?(c(),S(P,{key:4,label:"重量"},{default:a(()=>[u(n(r.value.weight)+"kg",1)]),_:1})):_("",!0)]),_:1})])):_("",!0),s("div",Zs,[e[70]||(e[70]=s("h3",null,"物流设置",-1)),i(ne,{column:2,border:""},{default:a(()=>[i(P,{label:"运费类型"},{default:a(()=>[u(n(r.value.shippingType==="unified"?"统一运费":"运费模板"),1)]),_:1}),r.value.shippingType==="unified"?(c(),S(P,{key:0,label:"运费金额"},{default:a(()=>[u(" ¥"+n(r.value.shippingFee||"0.00"),1)]),_:1})):_("",!0),r.value.shippingType==="template"?(c(),S(P,{key:1,label:"运费模板"},{default:a(()=>[u(n(tt(r.value.shippingTemplateId)),1)]),_:1})):_("",!0),i(P,{label:"售后服务"},{default:a(()=>[u(n(r.value.afterSaleType==="no_return"?"不可退换":"支持退换"),1)]),_:1}),r.value.afterSaleType==="support_return"?(c(),S(P,{key:2,label:"支持服务"},{default:a(()=>[u(n(st(r.value.afterSaleServices)),1)]),_:1})):_("",!0)]),_:1})]),r.value.images&&r.value.images.length>0?(c(),m("div",ea,[e[72]||(e[72]=s("h3",null,"商品图片",-1)),s("div",ta,[(c(!0),m(q,null,te(r.value.images,(t,M)=>(c(),m("div",{key:M,class:"image-item"},[i(Z,{src:t.imgUrl,"preview-src-list":T.value,fit:"cover",style:{width:"100px",height:"100px","border-radius":"4px"}},null,8,["src","preview-src-list"]),t.isMain?(c(),S($,{key:0,type:"success",size:"small",class:"main-tag"},{default:a(()=>e[71]||(e[71]=[u("主图")])),_:1,__:[71]})):_("",!0)]))),128))])])):_("",!0),r.value.description?(c(),m("div",sa,[e[73]||(e[73]=s("h3",null,"商品描述",-1)),s("div",{class:"description-content",innerHTML:r.value.description},null,8,aa)])):_("",!0)])]),_:1}),i(ge,{label:"规格库存",name:"spec"},{default:a(()=>[s("div",la,[r.value.skuList&&r.value.skuList.length>0?(c(),m("div",ia,[e[76]||(e[76]=s("h3",null,"SKU列表",-1)),i(ve,{data:r.value.skuList,border:""},{default:a(()=>[i(f,{label:"规格",width:"200"},{default:a(({row:t})=>[s("div",ra,[t.spec1Value||t.spec2Value||t.spec3Value?(c(),m(q,{key:0},[t.spec1Value?(c(),S($,{key:0,size:"small",class:"spec-tag"},{default:a(()=>[u(n(t.spec1Value),1)]),_:2},1024)):_("",!0),t.spec2Value?(c(),S($,{key:1,size:"small",class:"spec-tag"},{default:a(()=>[u(n(t.spec2Value),1)]),_:2},1024)):_("",!0),t.spec3Value?(c(),S($,{key:2,size:"small",class:"spec-tag"},{default:a(()=>[u(n(t.spec3Value),1)]),_:2},1024)):_("",!0)],64)):(c(),S($,{key:1,size:"small",class:"spec-tag default-spec"},{default:a(()=>e[74]||(e[74]=[u("标准规格")])),_:1,__:[74]}))])]),_:1}),i(f,{label:"成本价",width:"100"},{default:a(({row:t})=>[s("span",na,"¥"+n(g(t.costPrice)),1)]),_:1}),i(f,{label:"销售价",width:"100"},{default:a(({row:t})=>[s("span",oa,"¥"+n(g(t.salePrice)),1)]),_:1}),i(f,{label:"采购价",width:"100"},{default:a(({row:t})=>[s("span",ua,"¥"+n(g(t.purchasePrice)),1)]),_:1}),i(f,{label:"划线价",width:"100"},{default:a(({row:t})=>[s("span",ca,"¥"+n(g(t.strikethroughPrice)),1)]),_:1}),i(f,{label:"毛利",width:"100"},{default:a(({row:t})=>[s("span",{class:I(["price-text profit",Le(t)])}," ¥"+n(g(W(t.salePrice,t.costPrice))),3)]),_:1}),i(f,{label:"毛利率",width:"100"},{default:a(({row:t})=>[s("span",{class:I(["price-text profit-rate",Ie(t)])},n(me(Y(t.salePrice,t.costPrice))),3)]),_:1}),i(f,{label:"库存信息",width:"150"},{default:a(({row:t})=>[s("div",da,[s("div",null,"总库存: "+n(t.totalInventory||0),1),s("div",null,"可用: "+n(t.availableInventory||0),1),s("div",null,"锁定: "+n(t.blockedInventory||0),1)])]),_:1}),i(f,{label:"安全库存",width:"100"},{default:a(({row:t})=>[u(n(t.safetyStock||0),1)]),_:1}),i(f,{label:"SKU图片",width:"100"},{default:a(({row:t})=>[t.imgUrl?(c(),S(Z,{key:0,src:t.imgUrl,"preview-src-list":[t.imgUrl],fit:"cover",style:{width:"50px",height:"50px","border-radius":"4px"},"preview-teleported":""},null,8,["src","preview-src-list"])):(c(),m("span",pa,"无图片"))]),_:1})]),_:1},8,["data"]),s("div",ma,[e[75]||(e[75]=s("h4",null,"规格汇总",-1)),i(ne,{column:4,border:""},{default:a(()=>[i(P,{label:"SKU总数"},{default:a(()=>[u(n(r.value.skuList.length),1)]),_:1}),i(P,{label:"成本价区间"},{default:a(()=>[u(n(Ve()),1)]),_:1}),i(P,{label:"销售价区间"},{default:a(()=>[u(n(Ce()),1)]),_:1}),i(P,{label:"库存总计"},{default:a(()=>[u(n(r.value.totalStock||0),1)]),_:1})]),_:1})])])):(c(),m("div",va,[i(ht,{description:"暂无SKU数据"})]))])]),_:1}),i(ge,{label:"小程序预览",name:"preview"},{default:a(()=>{var t,M,ee,ze,Me;return[s("div",fa,[s("div",_a,[e[83]||(e[83]=s("div",{class:"preview-title"},[s("h3",null,"小程序预览效果"),s("p",null,"展示商品在小程序中的显示效果")],-1)),s("div",ga,[e[82]||(e[82]=s("div",{class:"phone-header"},[s("div",{class:"status-bar"},[s("div",{class:"left-status"},[s("span",{class:"signal-bars"},[s("span",{class:"bar"}),s("span",{class:"bar"}),s("span",{class:"bar"}),s("span",{class:"bar"})]),s("span",{class:"carrier"},"Sketch"),s("span",{class:"wifi-icon"},"📶")]),s("div",{class:"center-time"},"1:21 AM"),s("div",{class:"right-status"},[s("span",{class:"battery-percent"},"100%"),s("span",{class:"battery-icon"},"🔋")])])],-1)),s("div",ha,[e[81]||(e[81]=s("div",{class:"search-header"},[s("div",{class:"search-bar"},[s("span",{class:"search-icon"},"🔍"),s("span",{class:"search-placeholder"},"请输入关键字搜索")])],-1)),s("div",ba,[s("div",ya,[s("div",Pa,[T.value.length>0?(c(),m("img",{key:0,src:T.value[0],alt:((t=r.value)==null?void 0:t.productName)||"商品图片",class:"product-main-image",onError:lt},null,40,ka)):(c(),m("img",xa)),T.value.length>1?(c(),m("div",Sa,[(c(!0),m(q,null,te(T.value,(G,F)=>(c(),m("span",{key:F,class:I(["indicator",{active:F===0}])},null,2))),128))])):_("",!0)])]),s("div",Va,[s("div",Ca,[s("span",wa," ¥"+n(g(ie("sale")||99)),1),ie("strikethrough")&&ie("strikethrough")>0?(c(),m("span",Na," ¥"+n(g(ie("strikethrough"))),1)):(c(),m("span",Ua," ¥"+n(g(199)),1))]),s("div",$a,n(((M=r.value)==null?void 0:M.productName)||"商品名称示例"),1),s("div",La," 库存："+n(((ee=r.value)==null?void 0:ee.totalStock)||0)+"件 ",1),Ue().length>0?(c(),m("div",Ia,[(c(!0),m(q,null,te(Ue(),G=>(c(),m("div",{key:G.name,class:"spec-group"},[s("div",Ta,n(G.name),1),s("div",za,[(c(!0),m(q,null,te(G.values,F=>(c(),m("div",{key:String(F),class:I(["spec-option",{active:de.value[G.name]===F}]),onClick:Ha=>at(G.name,String(F))},n(F),11,Ma))),128))])]))),128))])):_("",!0)]),s("div",ja,[s("div",Ba,[e[78]||(e[78]=s("h4",null,"商品详情",-1)),(ze=r.value)!=null&&ze.description?(c(),m("div",Ea,[s("div",{innerHTML:r.value.description,class:"rich-content"},null,8,Da)])):(c(),m("div",Aa,e[77]||(e[77]=[s("p",null,"这里展示商品的详细信息，包括产品特性、材质说明、尺寸规格等详细内容。",-1),s("p",null,"商品采用优质材料制作，工艺精良，品质保证。适用于各种场合，是您的理想选择。",-1),s("p",null,"产品经过严格质量检测，确保每一件商品都符合高标准要求。",-1)])))]),s("div",Ka,[e[80]||(e[80]=s("h4",null,"商品参数",-1)),s("div",Ra,[(Me=r.value)!=null&&Me.unit?(c(),m("div",Fa,[e[79]||(e[79]=s("span",{class:"param-label"},"商品单位：",-1)),s("span",Oa,n(r.value.unit),1)])):_("",!0)])])])])])])])])]}),_:1})]),_:1},8,["modelValue"])])):_("",!0)]),_:1},8,["modelValue"])])}}});const Ja=Ut(qa,[["__scopeId","data-v-a7e366b0"]]);export{Ja as default};
