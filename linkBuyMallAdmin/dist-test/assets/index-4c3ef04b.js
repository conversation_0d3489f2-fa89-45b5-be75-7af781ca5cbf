import{$ as Z,d as Rt,r as M,z as E,c as be,x as Vt,E as D,b as k,A as Ct,o as n,e as c,f as t,k as a,h as l,y as d,t as o,F as T,m as G,g,B as St,C as Mt,D as Ut,j as v,n as j,_ as zt}from"./index-5502c85d.js";import{g as Nt}from"./category-4961b1f6.js";function $t(A){return Z.get({url:"/company-product-review/list",params:A})}function we(A){return Z.get({url:`/company-product-review/${A}`})}function Lt(){return Z.get({url:"/company-product-review/statistics"})}function It(){return Z.get({url:"/supplier/list",params:{page:1,size:200}})}function Bt(A,ee){return Z.post({url:`/company-product-review/review/${A}`,data:ee})}function Dt(A){return Z.post({url:"/company-product-review/batch/review",data:A})}const Tt={class:"product-review-container"},jt={class:"page-header"},Kt={class:"header-right"},Ft={class:"statistics-cards"},Et={class:"stat-content"},At={class:"stat-number"},qt={class:"stat-content"},Ht={class:"stat-number"},Ot={class:"stat-content"},Gt={class:"stat-number"},Jt={class:"stat-content"},Qt={class:"stat-number"},Wt={class:"header"},Xt={class:"product-info"},Yt={class:"product-image"},Zt={class:"image-slot"},es={class:"product-details"},ts={class:"product-name"},ss={class:"product-code"},ls={class:"product-supplier"},as={key:0,class:"price"},is={key:0},rs={key:1,class:"price-pending"},os={key:0,class:"price"},ns={key:0},us={key:1,class:"price-pending"},ds={class:"pagination"},cs={key:0,class:"review-approve-container"},ps={class:"product-info-header"},ms={class:"product-image"},vs={class:"product-details"},_s={class:"product-name"},fs={class:"product-meta"},gs={class:"product-code"},hs={class:"product-supplier"},ks={class:"sku-list-section"},ys={class:"product-info-cell"},bs={class:"product-image"},ws={class:"product-details"},Ps={class:"product-name"},xs={class:"product-supplier"},Rs={class:"spec-values"},Vs={class:"price-text cost-price"},Cs={class:"price-text purchase-price"},Ss={class:"price-text strike-price"},Ms={class:"price-formula-section"},Us={class:"formula-container"},zs={class:"formula-base"},Ns={class:"formula-tip"},$s={key:0,class:"error-message"},Ls={key:1},Is={key:0,class:"error-message"},Bs={class:"dialog-footer"},Ds={key:0},Ts={key:0,class:"error-message"},js={class:"selected-products"},Ks={class:"product-list"},Fs={key:1,class:"batch-review-approve-container"},Es={class:"batch-product-overview"},As={class:"overview-stats"},qs={class:"stat-item"},Hs={class:"stat-item"},Os={class:"sku-list-section"},Gs={class:"product-info-cell"},Js={class:"product-image"},Qs={class:"product-details"},Ws={class:"product-name"},Xs={class:"product-supplier"},Ys={class:"spec-values"},Zs={class:"price-text cost-price"},el={class:"price-text purchase-price"},tl={class:"price-text strike-price"},sl={class:"price-formula-section"},ll={class:"formula-container"},al={class:"formula-base"},il={class:"formula-tip"},rl={key:0,class:"error-message"},ol={key:2},nl={class:"selected-products"},ul={class:"product-list"},dl={class:"dialog-footer"},cl={key:0,class:"product-detail-container"},pl={class:"product-header"},ml={class:"product-main-info"},vl={class:"product-image"},_l={class:"product-basic"},fl={class:"product-title"},gl={class:"product-meta"},hl={class:"product-id"},kl={class:"product-price-info"},yl={class:"price-item"},bl={class:"price-value cost-price"},wl={class:"price-item"},Pl={class:"price-value strike-price"},xl={class:"price-item"},Rl={class:"price-value purchase-price"},Vl={class:"stats-item"},Cl={class:"stats-value"},Sl={class:"stats-item"},Ml={class:"stats-value"},Ul={class:"basic-info-content"},zl={key:0,class:"physical-attrs"},Nl={class:"shipping-settings"},$l={key:1,class:"product-images-section"},Ll={class:"images-gallery"},Il={key:2,class:"product-description"},Bl=["innerHTML"],Dl={key:3,class:"product-features"},Tl={class:"features-content"},jl={key:4,class:"product-instructions"},Kl={class:"instructions-content"},Fl={class:"spec-stock-content"},El={key:0},Al={class:"spec-values"},ql={class:"price-text cost-price"},Hl={class:"price-text purchase-price"},Ol={class:"price-text strike-price"},Gl={class:"stock-info"},Jl={key:1,class:"no-image"},Ql={class:"spec-summary"},Wl={key:1,class:"no-data"},Xl={class:"preview-only-container"},Yl={class:"centered-phone-preview"},Zl={class:"phone-container"},ea={class:"phone-screen"},ta={class:"product-info-area"},sa={class:"product-carousel"},la={class:"carousel-container"},aa=["src","alt"],ia={key:1,src:"https://via.placeholder.com/375x300/f0f0f0/999?text=商品图片",alt:"默认商品图片",class:"product-main-image"},ra={key:2,class:"carousel-indicators"},oa={class:"product-info-section"},na={class:"product-price"},ua={class:"current-price"},da={key:0,class:"original-price"},ca={key:1,class:"original-price"},pa={class:"product-title"},ma={class:"product-stock"},va={key:0,class:"spec-selection"},_a={class:"spec-label"},fa={class:"spec-options"},ga=["onClick"],ha={class:"product-detail-content"},ka={class:"detail-section"},ya={key:0,class:"detail-html"},ba=["innerHTML"],wa={key:1,class:"detail-placeholder"},Pa={class:"detail-section"},xa={class:"param-list"},Ra={key:0,class:"param-item"},Va={class:"param-value"},Ca={class:"review-record-content"},Sa={class:"review-record"},Ma={class:"review-remark"},Ua={key:0,class:"detail-footer"},za=Rt({__name:"index",setup(A){const ee=i=>i?new Date(i).toLocaleString():"",ve=M(!1),Pe=M([]),L=M([]),xe=M([]),K=M({}),S=E({name:"",supplierId:null,status:3});let _e=null;const z=E({page:1,size:10,total:0}),f=E({visible:!1,loading:!1,form:{reviewResult:0,reviewRemark:""}}),Fe=be(()=>f.form.reviewResult===1?"批量审核通过":f.form.reviewResult===4?"批量审核拒绝":"批量审核"),q=M([]),B=M([]),P=E({reviewRemark:""}),R=E({priceBase:"costPrice",multiplier:1,adjustment:0}),r=E({visible:!1,product:null}),fe=M("basic"),J=M(!1),Re=M("商品审核"),ge=M(!1),x=E({id:0,reviewStatus:1,reviewRemark:""}),b=M(null),U=E({priceBase:"costPrice",multiplier:1,adjustment:0}),V=E({reviewRemark:""}),Ee=M([]),Ve=M(new Map);Vt(()=>{F(),re(),Ae(),ht()});const F=async()=>{ve.value=!0;try{const i={page:z.page,size:z.size,...S},e=await $t(i);Pe.value=e.data.records,z.total=e.data.total}catch{D.error("加载商品列表失败")}finally{ve.value=!1}},re=async()=>{try{const i=await Lt();K.value=i.data,K.value.total=K.value.approved+K.value.rejected+K.value.pending}catch(i){console.error("加载统计数据失败",i)}},Ae=async()=>{try{const i=await It();xe.value=i.data.records||[]}catch(i){console.error("加载供应商选项失败",i)}},te=i=>({0:"info",1:"success",2:"warning",3:"primary",4:"danger"})[i]||"info",se=i=>({0:"已下架",1:"销售中",2:"草稿",3:"审核中",4:"审核拒绝"})[i]||"未知",qe=()=>{Ce()},Ce=()=>{S.name="",S.supplierId=null,S.status=3,z.page=1,F()},He=()=>{F(),re()},Oe=i=>{L.value=i},Ge=i=>{z.size=i,z.page=1,F()},Je=async i=>{try{const e=await we(i.id);r.product=e.data,console.log(r.product.shippingType),r.visible=!0}catch{D.error("加载商品详情失败")}},oe=(i,e)=>{x.id=i.id,x.reviewStatus=e,x.reviewRemark="",Re.value=e===1?"审核通过":"审核拒绝",e===1?Qe(i.id):b.value=null,J.value=!0},Qe=async i=>{try{const e=await we(i);b.value=e.data,b.value.skuList&&b.value.skuList.forEach(u=>{u.salePrice=null})}catch{D.error("加载商品详情失败")}},Se=()=>{J.value=!1,b.value=null,x.reviewRemark="",V.reviewRemark="",U.priceBase="costPrice",U.multiplier=1.2,U.adjustment=0},We=(i,e)=>{i.salePrice!==null&&i.salePrice!==void 0&&i.salePrice<0&&(i.salePrice=null),V.reviewRemark=""},Xe=()=>({costPrice:"成本价",purchasePrice:"采购价",strikethroughPrice:"划线价"})[U.priceBase]||"成本价",Ye=()=>{!b.value||!b.value.skuList||(b.value.skuList.forEach(i=>{const u=(i[U.priceBase]||0)*U.multiplier+U.adjustment;i.salePrice=u>0?Number(u.toFixed(2)):null}),V.reviewRemark="")},ne=(i,e)=>{if(i==null||i==="")return 0;const u=Number(i)||0,m=Number(e)||0;return u-m},ue=(i,e)=>{if(i==null||i==="")return 0;const u=Number(i)||0,m=Number(e)||0;return u===0?0:(u-m)/u*100},Me=i=>`${i.toFixed(2)}%`,Ze=i=>{const e=ne(i.salePrice,i.costPrice);return e>0?"profit-positive":e<0?"profit-negative":"profit-zero"},et=i=>{const e=ue(i.salePrice,i.costPrice);return e>0?"profit-rate-positive":e<0?"profit-rate-negative":"profit-rate-zero"},tt=()=>{if(L.value.length===0){D.warning("请先选择要审核的商品");return}if(L.value.some(e=>e.status!==3)){D.warning('只能审核状态为"审核中"的商品');return}f.form.reviewResult=0,f.form.reviewRemark="",P.reviewRemark="",q.value=[],B.value=[],f.visible=!0},st=async i=>{i===1?await lt():(q.value=[],B.value=[])},lt=async()=>{try{q.value=[],B.value=[];for(const i of L.value){const u=(await we(i.id)).data;u.skuList&&u.skuList.forEach(m=>{m.productId=u.id,m.productName=u.name,m.supplierName=u.supplierName,m.mainImageUrl=u.mainImgUrl,m.salePrice=null,B.value.push(m)}),q.value.push(u)}R.priceBase="costPrice",R.multiplier=1,R.adjustment=0}catch{D.error("获取商品详情失败")}},Ue=()=>{f.visible=!1,f.form.reviewResult=0,f.form.reviewRemark="",P.reviewRemark="",q.value=[],B.value=[],R.priceBase="costPrice",R.multiplier=1,R.adjustment=0},at=()=>new Set(q.value.map(e=>e.supplierId)).size,it=(i,e)=>{i.salePrice!==null&&i.salePrice!==void 0&&i.salePrice<0&&(i.salePrice=null),P.reviewRemark=""},rt=()=>({costPrice:"成本价",purchasePrice:"采购价",strikethroughPrice:"划线价"})[R.priceBase]||"成本价",ot=()=>{B.value.length!==0&&(B.value.forEach(i=>{const u=(i[R.priceBase]||0)*R.multiplier+R.adjustment;i.salePrice=u>0?Number(u.toFixed(2)):null}),P.reviewRemark="")},nt=i=>{const e=ne(i.salePrice,i.costPrice);return e>0?"profit-positive":e<0?"profit-negative":"profit-zero"},ut=i=>{const e=ue(i.salePrice,i.costPrice);return e>0?"profit-rate-positive":e<0?"profit-rate-negative":"profit-rate-zero"},dt=async()=>{if(P.reviewRemark="",!f.form.reviewRemark||!f.form.reviewRemark.trim()){P.reviewRemark="审核意见为必填项，请输入审核意见";return}if(f.form.reviewResult===1&&B.value.some(e=>e.salePrice===null||e.salePrice===void 0||e.salePrice===""||e.salePrice<=0)){P.reviewRemark="请为所有SKU设置有效的销售价格（大于0）";return}f.loading=!0;try{const i={ids:L.value.map(u=>u.id),reviewResult:f.form.reviewResult,reviewRemark:f.form.reviewRemark};f.form.reviewResult===1&&(i.skuPriceList=B.value.map(u=>({spuId:u.productId,skuId:u.id,salePrice:Number(u.salePrice)}))),await Dt(i);const e=f.form.reviewResult===1?"通过":"拒绝";D.success(`批量${e}成功`),f.visible=!1,Ue(),L.value=[],F(),re()}catch{D.error("批量审核失败")}finally{f.loading=!1}},ct=async()=>{if(V.reviewRemark="",!x.reviewRemark||!x.reviewRemark.trim()){V.reviewRemark="审核意见为必填项，请输入审核意见";return}if(x.reviewStatus===1&&b.value&&b.value.skuList.some(e=>e.salePrice===null||e.salePrice===void 0||e.salePrice===""||e.salePrice<=0)){V.reviewRemark="请为所有SKU设置有效的销售价格（大于0）";return}ge.value=!0;try{const i={reviewResult:x.reviewStatus,reviewRemark:x.reviewRemark};x.reviewStatus===1&&b.value&&(i.skuPriceList=b.value.skuList.map(e=>({spuId:b.value.id,skuId:e.id,salePrice:Number(e.salePrice)}))),await Bt(x.id,i),D.success("审核成功"),J.value=!1,Se(),F(),re()}catch{D.error("审核失败")}finally{ge.value=!1}},he=()=>{_e&&clearTimeout(_e),_e=setTimeout(()=>{z.page=1,F()},300)},de=i=>{switch(S.name="",S.supplierId=null,i){case"all":S.status=null;break;case"pending":S.status=3;break;case"approved":S.status=1;break;case"rejected":S.status=4;break}z.page=1,F()},pt=()=>{r.visible=!1,r.product=null,fe.value="basic"},h=i=>i?Number(i).toFixed(2):"0.00",Q=be(()=>!r.product||!r.product.images?[]:r.product.images.map(i=>i.imgUrl)),ze=()=>{if(!r.product||!r.product.skuList)return 0;const i=r.product.skuList.map(e=>e.strikethroughPrice||0).filter(e=>e>0);return i.length>0?Math.max(...i):0},Ne=()=>!r.product||!r.product.skuList?0:r.product.skuList.reduce((i,e)=>i+(e.totalInventory||0),0),mt=be(()=>{if(!r.product)return!1;const{length:i,wide:e,tall:u,volume:m,weight:C}=r.product;return i||e||u||m||C}),ce=i=>{if(!r.product)return 0;if(r.product.skuList&&r.product.skuList.length>1){const u=Le.value||r.product.skuList[0];if(!u)return 0;switch(i){case"cost":return Number(u.costPrice)||0;case"purchase":return Number(u.purchasePrice)||0;case"strikethrough":return Number(u.strikethroughPrice)||0;default:return 0}}else{const u=r.product.skuList[0];if(!u)return 0;switch(i){case"cost":return Number(u.costPrice)||0;case"purchase":return Number(u.purchasePrice)||0;case"strikethrough":return Number(u.strikethroughPrice)||0;default:return 0}}},$e=()=>{if(!r.product||!r.product.skuList)return[];const i=[],e=r.product.skuList;if(r.product.spec1Name){const u=[...new Set(e.map(m=>m.spec1Value).filter(m=>m))];u.length>0&&i.push({name:r.product.spec1Name,values:u})}if(r.product.spec2Name){const u=[...new Set(e.map(m=>m.spec2Value).filter(m=>m))];u.length>0&&i.push({name:r.product.spec2Name,values:u})}if(r.product.spec3Name){const u=[...new Set(e.map(m=>m.spec3Value).filter(m=>m))];u.length>0&&i.push({name:r.product.spec3Name,values:u})}return i},vt=(i,e)=>{ke.value[i]=e;const u=r.product.skuList.find(m=>{const C=ke.value;let N=!0;return C[r.product.spec1Name]&&m.spec1Value!==C[r.product.spec1Name]&&(N=!1),C[r.product.spec2Name]&&m.spec2Value!==C[r.product.spec2Name]&&(N=!1),C[r.product.spec3Name]&&m.spec3Value!==C[r.product.spec3Name]&&(N=!1),N});u&&(Le.value=u)},Le=M(null),ke=M({}),_t=i=>{console.log("图片加载失败:",i.target.src),i.target.style.display="none"},ft=i=>i?`模板${i}`:"无",gt=i=>{if(!i)return"无";const e={return:"退货",exchange:"换货",refund_only:"仅退款",cancel_order:"整单退款",return_shipping:"退费"};return i.split(",").filter(m=>m.trim()).map(m=>e[m.trim()]||m).join("、")},Ie=i=>i?Ve.value.get(Number(i))||`分类ID: ${i}`:"未分类",ht=async()=>{try{const i=await Nt();i.code===200&&i.data&&(Ee.value=i.data,Be(i.data))}catch(i){console.error("加载分类树失败",i)}},Be=(i,e=[])=>{!i||!Array.isArray(i)||i.forEach(u=>{const m=u.categoryName||u.categoryCode||`分类${u.id}`,C=u.id,N=u.children||[];if(C){const le=[...e,m],_=le.join(" / ");Ve.value.set(C,_),N&&N.length>0&&Be(N,le)}})};return(i,e)=>{const u=k("el-button"),m=k("el-card"),C=k("el-input"),N=k("el-option"),le=k("el-select"),_=k("el-table-column"),kt=k("el-icon"),H=k("el-image"),y=k("el-tag"),pe=k("el-table"),yt=k("el-pagination"),W=k("el-input-number"),X=k("el-radio"),ye=k("el-radio-group"),I=k("el-form-item"),De=k("el-text"),ae=k("el-form"),Te=k("el-dialog"),w=k("el-descriptions-item"),ie=k("el-descriptions"),me=k("el-tab-pane"),bt=k("el-empty"),wt=k("el-tabs"),Pt=k("el-drawer"),xt=Ct("loading");return n(),c("div",Tt,[t("div",jt,[e[40]||(e[40]=t("div",{class:"header-left"},[t("h2",null,"商品审核"),t("p",null,"审核供应商提报的商品，通过后自动加入商品库")],-1)),t("div",Kt,[a(u,{type:"primary",onClick:tt},{default:l(()=>e[38]||(e[38]=[d("批量审核")])),_:1,__:[38]}),a(u,{onClick:He},{default:l(()=>e[39]||(e[39]=[d("刷新")])),_:1,__:[39]})])]),t("div",Ft,[a(m,{class:"stat-card",onClick:e[0]||(e[0]=s=>de("pending"))},{default:l(()=>[t("div",Et,[t("div",At,o(K.value.pending),1),e[41]||(e[41]=t("div",{class:"stat-label"},"待审核",-1))])]),_:1}),a(m,{class:"stat-card",onClick:e[1]||(e[1]=s=>de("approved"))},{default:l(()=>[t("div",qt,[t("div",Ht,o(K.value.approved),1),e[42]||(e[42]=t("div",{class:"stat-label"},"已通过",-1))])]),_:1}),a(m,{class:"stat-card",onClick:e[2]||(e[2]=s=>de("rejected"))},{default:l(()=>[t("div",Ot,[t("div",Gt,o(K.value.rejected),1),e[43]||(e[43]=t("div",{class:"stat-label"},"已拒绝",-1))])]),_:1}),a(m,{class:"stat-card",onClick:e[3]||(e[3]=s=>de("all"))},{default:l(()=>[t("div",Jt,[t("div",Qt,o(K.value.total),1),e[44]||(e[44]=t("div",{class:"stat-label"},"总数量",-1))])]),_:1})]),t("div",Wt,[a(C,{modelValue:S.name,"onUpdate:modelValue":e[4]||(e[4]=s=>S.name=s),placeholder:"搜索商品名称",clearable:"",style:{width:"200px","margin-right":"10px"},onInput:he,onClear:qe},null,8,["modelValue"]),a(le,{modelValue:S.supplierId,"onUpdate:modelValue":e[5]||(e[5]=s=>S.supplierId=s),placeholder:"选择供应商",clearable:"",style:{width:"200px","margin-right":"10px"},onChange:he},{default:l(()=>[(n(!0),c(T,null,G(xe.value,s=>(n(),g(N,{key:s.id,label:s.name,value:s.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),a(le,{modelValue:S.status,"onUpdate:modelValue":e[6]||(e[6]=s=>S.status=s),placeholder:"审核状态",clearable:"",style:{width:"150px","margin-right":"10px"},onChange:he},{default:l(()=>[a(N,{label:"审核中",value:3}),a(N,{label:"已通过",value:1}),a(N,{label:"已拒绝",value:4})]),_:1},8,["modelValue"]),a(u,{onClick:Ce},{default:l(()=>e[45]||(e[45]=[d("重置")])),_:1,__:[45]})]),St((n(),g(pe,{height:"calc(100vh - 440px)",data:Pe.value,style:{"margin-top":"16px",width:"100%"},onSelectionChange:Oe},{default:l(()=>[a(_,{type:"selection",width:"55"}),a(_,{label:"商品信息","min-width":"300"},{default:l(({row:s})=>[t("div",Xt,[t("div",Yt,[a(H,{src:s.mainImgUrl,fit:"cover",style:{width:"60px",height:"60px","border-radius":"4px"}},{error:l(()=>[t("div",Zt,[a(kt,null,{default:l(()=>[a(Mt(Ut))]),_:1})])]),_:2},1032,["src"])]),t("div",es,[t("div",ts,o(s.name),1),t("div",ss,"编码："+o(s.spuCode),1),t("div",ls," 供应商："+o(s.supplierName),1)])])]),_:1}),a(_,{label:"成本价范围",width:"150",align:"center"},{default:l(({row:s})=>[t("div",null,[s.costPriceMin&&s.costPriceMin>0?(n(),c("div",as,[d(" ¥"+o(s.costPriceMin)+" ",1),s.costPriceMax&&s.costPriceMax!==s.costPriceMin?(n(),c("span",is,"- ¥"+o(s.costPriceMax),1)):v("",!0)])):(n(),c("div",rs,"待定"))])]),_:1}),a(_,{label:"采购价范围",width:"150",align:"center"},{default:l(({row:s})=>[t("div",null,[s.purchasePriceMin&&s.purchasePriceMin>0?(n(),c("div",os,[d(" ¥"+o(s.purchasePriceMin)+" ",1),s.purchasePriceMax&&s.purchasePriceMax!==s.purchasePriceMin?(n(),c("span",ns,"- ¥"+o(s.purchasePriceMax),1)):v("",!0)])):(n(),c("div",us,"待定"))])]),_:1}),a(_,{label:"SKU数量",width:"100",align:"center"},{default:l(({row:s})=>[t("span",null,o(s.skuCount)+"个",1)]),_:1}),a(_,{prop:"status",label:"审核状态",width:"100",align:"center"},{default:l(({row:s})=>[a(y,{type:te(s.status)},{default:l(()=>[d(o(se(s.status)),1)]),_:2},1032,["type"])]),_:1}),a(_,{label:"商品分类",width:"150",align:"center"},{default:l(({row:s})=>[d(o(Ie(s.categoryId)),1)]),_:1}),a(_,{label:"提交时间",width:"180"},{default:l(({row:s})=>[d(o(ee(s.createTime)),1)]),_:1}),a(_,{label:"操作",width:"200",align:"center"},{default:l(({row:s})=>[a(u,{link:"",type:"primary",size:"small",onClick:p=>Je(s)},{default:l(()=>e[46]||(e[46]=[d("查看")])),_:2,__:[46]},1032,["onClick"]),s.status===3?(n(),g(u,{key:0,link:"",type:"success",size:"small",onClick:p=>oe(s,1)},{default:l(()=>e[47]||(e[47]=[d(" 通过 ")])),_:2,__:[47]},1032,["onClick"])):v("",!0),s.status===3?(n(),g(u,{key:1,link:"",type:"danger",size:"small",onClick:p=>oe(s,2)},{default:l(()=>e[48]||(e[48]=[d(" 拒绝 ")])),_:2,__:[48]},1032,["onClick"])):v("",!0)]),_:1})]),_:1},8,["data"])),[[xt,ve.value]]),t("div",ds,[a(yt,{"current-page":z.page,"onUpdate:currentPage":e[7]||(e[7]=s=>z.page=s),"page-size":z.size,"onUpdate:pageSize":e[8]||(e[8]=s=>z.size=s),total:z.total,background:"","page-sizes":[10,20,50,100],onSizeChange:Ge,onCurrentChange:F},null,8,["current-page","page-size","total"])]),a(Te,{modelValue:J.value,"onUpdate:modelValue":e[20]||(e[20]=s=>J.value=s),title:Re.value,width:"800px","before-close":Se},{footer:l(()=>[t("div",Bs,[a(u,{onClick:e[19]||(e[19]=s=>J.value=!1)},{default:l(()=>e[61]||(e[61]=[d("取消")])),_:1,__:[61]}),a(u,{type:"primary",onClick:ct,loading:ge.value},{default:l(()=>e[62]||(e[62]=[d("确定")])),_:1,__:[62]},8,["loading"])])]),default:l(()=>[x.reviewStatus===1&&b.value?(n(),c("div",cs,[t("div",ps,[t("div",ms,[a(H,{src:b.value.mainImgUrl||"/default-product.png",fit:"cover",style:{width:"80px",height:"80px","border-radius":"8px"}},null,8,["src"])]),t("div",vs,[t("div",_s,o(b.value.name),1),t("div",fs,[t("span",gs,"编码："+o(b.value.spuCode),1),t("span",hs,"供应商："+o(b.value.supplierName),1)])])]),t("div",ks,[e[50]||(e[50]=t("h4",null,"SKU列表 - 设置销售价格",-1)),a(pe,{data:b.value.skuList,border:"",size:"small",height:"300"},{default:l(()=>[a(_,{label:"商品信息",width:"200"},{default:l(({row:s})=>[t("div",ys,[t("div",bs,[a(H,{src:s.imgUrl||b.value.mainImgUrl||"/default-product.png",fit:"cover",style:{width:"40px",height:"40px","border-radius":"4px"}},null,8,["src"])]),t("div",ws,[t("div",Ps,o(b.value.name),1),t("div",xs,o(b.value.supplierName),1)])])]),_:1}),a(_,{label:"规格",width:"120"},{default:l(({row:s})=>[t("div",Rs,[s.spec1Value||s.spec2Value||s.spec3Value?(n(),c(T,{key:0},[s.spec1Value?(n(),g(y,{key:0,size:"small",class:"spec-tag"},{default:l(()=>[d(o(s.spec1Value),1)]),_:2},1024)):v("",!0),s.spec2Value?(n(),g(y,{key:1,size:"small",class:"spec-tag"},{default:l(()=>[d(o(s.spec2Value),1)]),_:2},1024)):v("",!0),s.spec3Value?(n(),g(y,{key:2,size:"small",class:"spec-tag"},{default:l(()=>[d(o(s.spec3Value),1)]),_:2},1024)):v("",!0)],64)):(n(),g(y,{key:1,size:"small",class:"spec-tag default-spec"},{default:l(()=>e[49]||(e[49]=[d("标准规格")])),_:1,__:[49]}))])]),_:1}),a(_,{label:"成本价",width:"100"},{default:l(({row:s})=>[t("span",Vs,"¥"+o(h(s.costPrice)),1)]),_:1}),a(_,{label:"采购价",width:"100"},{default:l(({row:s})=>[t("span",Cs,"¥"+o(h(s.purchasePrice)),1)]),_:1}),a(_,{label:"划线价",width:"100"},{default:l(({row:s})=>[t("span",Ss,"¥"+o(h(s.strikethroughPrice)),1)]),_:1}),a(_,{label:"设置销售价",width:"150"},{default:l(({row:s,$index:p})=>[a(W,{modelValue:s.salePrice,"onUpdate:modelValue":$=>s.salePrice=$,precision:2,step:.01,min:0,size:"small",style:{width:"130px"},onChange:$=>We(s,p)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),a(_,{label:"毛利",width:"100"},{default:l(({row:s})=>[t("span",{class:j(["price-text profit",Ze(s)])}," ¥"+o(h(ne(s.salePrice,s.costPrice))),3)]),_:1}),a(_,{label:"毛利率",width:"100"},{default:l(({row:s})=>[t("span",{class:j(["price-text profit-rate",et(s)])},o(Me(ue(s.salePrice,s.costPrice))),3)]),_:1})]),_:1},8,["data"])]),t("div",Ms,[e[59]||(e[59]=t("h4",null,"价格设置公式",-1)),a(ae,{model:U,"label-width":"100px"},{default:l(()=>[a(I,{label:"价格基准"},{default:l(()=>[a(ye,{modelValue:U.priceBase,"onUpdate:modelValue":e[9]||(e[9]=s=>U.priceBase=s),onChange:e[10]||(e[10]=s=>V.reviewRemark="")},{default:l(()=>[a(X,{label:"costPrice"},{default:l(()=>e[51]||(e[51]=[d("成本价")])),_:1,__:[51]}),a(X,{label:"strikethroughPrice"},{default:l(()=>e[52]||(e[52]=[d("划线价")])),_:1,__:[52]})]),_:1},8,["modelValue"])]),_:1}),a(I,{label:"定价公式"},{default:l(()=>[t("div",Us,[e[54]||(e[54]=t("span",{class:"formula-text"},"销售价格 = ",-1)),t("span",zs,o(Xe()),1),e[55]||(e[55]=t("span",{class:"formula-text"}," × ",-1)),a(W,{modelValue:U.multiplier,"onUpdate:modelValue":e[11]||(e[11]=s=>U.multiplier=s),precision:2,step:.01,min:0,size:"small",style:{width:"120px"},onChange:e[12]||(e[12]=s=>V.reviewRemark="")},null,8,["modelValue"]),e[56]||(e[56]=t("span",{class:"formula-text"}," + ",-1)),a(W,{modelValue:U.adjustment,"onUpdate:modelValue":e[13]||(e[13]=s=>U.adjustment=s),precision:2,step:.01,size:"small",style:{width:"120px"},onChange:e[14]||(e[14]=s=>V.reviewRemark="")},null,8,["modelValue"]),e[57]||(e[57]=t("span",{class:"formula-text"}," 元",-1)),a(u,{type:"primary",size:"small",onClick:Ye,style:{"margin-left":"15px"}},{default:l(()=>e[53]||(e[53]=[d(" 应用到所有SKU ")])),_:1,__:[53]})]),t("div",Ns,[a(De,{type:"info",size:"small"},{default:l(()=>e[58]||(e[58]=[d(' 💡 设置好定价公式后，点击"应用到所有SKU"按钮批量设置价格，或在表格中手动编辑单个SKU的价格 ')])),_:1,__:[58]})])]),_:1}),a(I,{label:"审核意见",required:""},{default:l(()=>[a(C,{modelValue:x.reviewRemark,"onUpdate:modelValue":e[15]||(e[15]=s=>x.reviewRemark=s),type:"textarea",rows:3,placeholder:"请输入审核通过的意见（必填）",class:j({"is-error":V.reviewRemark}),onInput:e[16]||(e[16]=s=>V.reviewRemark="")},null,8,["modelValue","class"]),V.reviewRemark?(n(),c("div",$s,o(V.reviewRemark),1)):v("",!0)]),_:1})]),_:1},8,["model"])])])):x.reviewStatus===2?(n(),c("div",Ls,[a(ae,{model:x,"label-width":"100px"},{default:l(()=>[a(I,{label:"审核结果"},{default:l(()=>[a(y,{type:"danger"},{default:l(()=>e[60]||(e[60]=[d("拒绝")])),_:1,__:[60]})]),_:1}),a(I,{label:"审核意见",required:""},{default:l(()=>[a(C,{modelValue:x.reviewRemark,"onUpdate:modelValue":e[17]||(e[17]=s=>x.reviewRemark=s),type:"textarea",rows:4,placeholder:"请输入拒绝原因（必填）",class:j({"is-error":V.reviewRemark}),onInput:e[18]||(e[18]=s=>V.reviewRemark="")},null,8,["modelValue","class"]),V.reviewRemark?(n(),c("div",Is,o(V.reviewRemark),1)):v("",!0)]),_:1})]),_:1},8,["model"])])):v("",!0)]),_:1},8,["modelValue","title"]),a(Te,{modelValue:f.visible,"onUpdate:modelValue":e[33]||(e[33]=s=>f.visible=s),title:Fe.value,width:"800px","before-close":Ue},{footer:l(()=>[t("div",dl,[a(u,{onClick:e[32]||(e[32]=s=>f.visible=!1)},{default:l(()=>e[77]||(e[77]=[d("取消")])),_:1,__:[77]}),f.form.reviewResult===0?(n(),g(u,{key:0,type:"primary",disabled:""},{default:l(()=>e[78]||(e[78]=[d(" 请先选择审核结果 ")])),_:1,__:[78]})):(n(),g(u,{key:1,type:"primary",onClick:dt,loading:f.loading},{default:l(()=>e[79]||(e[79]=[d(" 确认审核 ")])),_:1,__:[79]},8,["loading"]))])]),default:l(()=>[f.form.reviewResult===4?(n(),c("div",Ds,[a(ae,{model:f.form,"label-width":"100px"},{default:l(()=>[a(I,{label:"审核结果"},{default:l(()=>[a(y,{type:"danger"},{default:l(()=>e[63]||(e[63]=[d("拒绝")])),_:1,__:[63]})]),_:1}),a(I,{label:"审核意见",required:""},{default:l(()=>[a(C,{modelValue:f.form.reviewRemark,"onUpdate:modelValue":e[21]||(e[21]=s=>f.form.reviewRemark=s),type:"textarea",rows:4,placeholder:"请输入拒绝原因（必填）",class:j({"is-error":P.reviewRemark}),onInput:e[22]||(e[22]=s=>P.reviewRemark="")},null,8,["modelValue","class"]),P.reviewRemark?(n(),c("div",Ts,o(P.reviewRemark),1)):v("",!0)]),_:1})]),_:1},8,["model"]),t("div",js,[t("p",null,"已选择 "+o(L.value.length)+" 个商品",1),t("div",Ks,[(n(!0),c(T,null,G(L.value,s=>(n(),c("div",{key:s.id,class:"product-item"},[t("span",null,o(s.name),1),t("span",null,o(s.spuCode),1),a(y,{type:te(s.status),size:"small"},{default:l(()=>[d(o(se(s.status)),1)]),_:2},1032,["type"])]))),128))])])])):f.form.reviewResult===1&&q.value.length>0?(n(),c("div",Fs,[t("div",Es,[t("h4",null,"批量审核通过 - "+o(L.value.length)+"个商品",1),t("div",As,[t("span",qs,"总SKU数: "+o(q.value.reduce((s,p)=>{var $;return s+((($=p.skuList)==null?void 0:$.length)||0)},0))+"个",1),t("span",Hs,"涉及供应商: "+o(at())+"个",1)])]),t("div",Os,[e[65]||(e[65]=t("h4",null,"SKU列表 - 设置销售价格",-1)),a(pe,{data:B.value,border:"",size:"small",height:"350"},{default:l(()=>[a(_,{label:"商品信息",width:"200"},{default:l(({row:s})=>[t("div",Gs,[t("div",Js,[a(H,{src:s.mainImageUrl||"/default-product.png",fit:"cover",style:{width:"40px",height:"40px","border-radius":"4px"}},null,8,["src"])]),t("div",Qs,[t("div",Ws,o(s.productName),1),t("div",Xs,o(s.supplierName),1)])])]),_:1}),a(_,{label:"规格",width:"120"},{default:l(({row:s})=>[t("div",Ys,[s.spec1Value||s.spec2Value||s.spec3Value?(n(),c(T,{key:0},[s.spec1Value?(n(),g(y,{key:0,size:"small",class:"spec-tag"},{default:l(()=>[d(o(s.spec1Value),1)]),_:2},1024)):v("",!0),s.spec2Value?(n(),g(y,{key:1,size:"small",class:"spec-tag"},{default:l(()=>[d(o(s.spec2Value),1)]),_:2},1024)):v("",!0),s.spec3Value?(n(),g(y,{key:2,size:"small",class:"spec-tag"},{default:l(()=>[d(o(s.spec3Value),1)]),_:2},1024)):v("",!0)],64)):(n(),g(y,{key:1,size:"small",class:"spec-tag default-spec"},{default:l(()=>e[64]||(e[64]=[d("标准规格")])),_:1,__:[64]}))])]),_:1}),a(_,{label:"成本价",width:"100"},{default:l(({row:s})=>[t("span",Zs,"¥"+o(h(s.costPrice)),1)]),_:1}),a(_,{label:"采购价",width:"100"},{default:l(({row:s})=>[t("span",el,"¥"+o(h(s.purchasePrice)),1)]),_:1}),a(_,{label:"划线价",width:"100"},{default:l(({row:s})=>[t("span",tl,"¥"+o(h(s.strikethroughPrice)),1)]),_:1}),a(_,{label:"设置销售价",width:"150"},{default:l(({row:s,$index:p})=>[a(W,{modelValue:s.salePrice,"onUpdate:modelValue":$=>s.salePrice=$,precision:2,step:.01,min:0,size:"small",style:{width:"130px"},onChange:$=>it(s,p)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),a(_,{label:"毛利",width:"100"},{default:l(({row:s})=>[t("span",{class:j(["price-text profit",nt(s)])}," ¥"+o(h(ne(s.salePrice,s.costPrice))),3)]),_:1}),a(_,{label:"毛利率",width:"100"},{default:l(({row:s})=>[t("span",{class:j(["price-text profit-rate",ut(s)])},o(Me(ue(s.salePrice,s.costPrice))),3)]),_:1})]),_:1},8,["data"])]),t("div",sl,[e[74]||(e[74]=t("h4",null,"价格设置公式",-1)),a(ae,{model:R,"label-width":"100px"},{default:l(()=>[a(I,{label:"价格基准"},{default:l(()=>[a(ye,{modelValue:R.priceBase,"onUpdate:modelValue":e[23]||(e[23]=s=>R.priceBase=s),onChange:e[24]||(e[24]=s=>P.reviewRemark="")},{default:l(()=>[a(X,{label:"costPrice"},{default:l(()=>e[66]||(e[66]=[d("成本价")])),_:1,__:[66]}),a(X,{label:"strikethroughPrice"},{default:l(()=>e[67]||(e[67]=[d("划线价")])),_:1,__:[67]})]),_:1},8,["modelValue"])]),_:1}),a(I,{label:"定价公式"},{default:l(()=>[t("div",ll,[e[69]||(e[69]=t("span",{class:"formula-text"},"销售价格 = ",-1)),t("span",al,o(rt()),1),e[70]||(e[70]=t("span",{class:"formula-text"}," × ",-1)),a(W,{modelValue:R.multiplier,"onUpdate:modelValue":e[25]||(e[25]=s=>R.multiplier=s),precision:2,step:.01,min:0,size:"small",style:{width:"120px"},onChange:e[26]||(e[26]=s=>P.reviewRemark="")},null,8,["modelValue"]),e[71]||(e[71]=t("span",{class:"formula-text"}," + ",-1)),a(W,{modelValue:R.adjustment,"onUpdate:modelValue":e[27]||(e[27]=s=>R.adjustment=s),precision:2,step:.01,size:"small",style:{width:"120px"},onChange:e[28]||(e[28]=s=>P.reviewRemark="")},null,8,["modelValue"]),e[72]||(e[72]=t("span",{class:"formula-text"}," 元",-1)),a(u,{type:"primary",size:"small",onClick:ot,style:{"margin-left":"15px"}},{default:l(()=>e[68]||(e[68]=[d(" 应用到所有SKU ")])),_:1,__:[68]})]),t("div",il,[a(De,{type:"info",size:"small"},{default:l(()=>e[73]||(e[73]=[d(' 💡 设置好定价公式后，点击"应用到所有SKU"按钮批量设置价格，或在表格中手动编辑单个SKU的价格 ')])),_:1,__:[73]})])]),_:1}),a(I,{label:"审核意见",required:""},{default:l(()=>[a(C,{modelValue:f.form.reviewRemark,"onUpdate:modelValue":e[29]||(e[29]=s=>f.form.reviewRemark=s),type:"textarea",rows:3,placeholder:"请输入审核通过的意见（必填）",class:j({"is-error":P.reviewRemark}),onInput:e[30]||(e[30]=s=>P.reviewRemark="")},null,8,["modelValue","class"]),P.reviewRemark?(n(),c("div",rl,o(P.reviewRemark),1)):v("",!0)]),_:1})]),_:1},8,["model"])])])):(n(),c("div",ol,[a(ae,{model:f.form,"label-width":"100px"},{default:l(()=>[a(I,{label:"审核结果"},{default:l(()=>[a(ye,{modelValue:f.form.reviewResult,"onUpdate:modelValue":e[31]||(e[31]=s=>f.form.reviewResult=s),onChange:st},{default:l(()=>[a(X,{label:1},{default:l(()=>e[75]||(e[75]=[d("通过")])),_:1,__:[75]}),a(X,{label:4},{default:l(()=>e[76]||(e[76]=[d("拒绝")])),_:1,__:[76]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"]),t("div",nl,[t("p",null,"已选择 "+o(L.value.length)+" 个商品",1),t("div",ul,[(n(!0),c(T,null,G(L.value,s=>(n(),c("div",{key:s.id,class:"product-item"},[t("span",null,o(s.name),1),t("span",null,o(s.spuCode),1),a(y,{type:te(s.status),size:"small"},{default:l(()=>[d(o(se(s.status)),1)]),_:2},1032,["type"])]))),128))])])]))]),_:1},8,["modelValue","title"]),a(Pt,{modelValue:r.visible,"onUpdate:modelValue":e[37]||(e[37]=s=>r.visible=s),title:"商品详情",size:"80%","before-close":pt},{default:l(()=>{var s;return[r.product?(n(),c("div",cl,[t("div",pl,[t("div",ml,[t("div",vl,[a(H,{src:r.product.mainImgUrl||"/default-product.png","preview-src-list":Q.value,fit:"cover",style:{width:"120px",height:"120px","border-radius":"8px"}},null,8,["src","preview-src-list"])]),t("div",_l,[t("h2",fl,o(r.product.name),1),t("div",gl,[t("span",hl,"商品ID: "+o(r.product.id),1),a(y,{type:te(r.product.status),class:"status-tag"},{default:l(()=>[d(o(se(r.product.status)),1)]),_:1},8,["type"]),r.product.type===1?(n(),g(y,{key:0,type:"info",class:"type-tag"},{default:l(()=>e[80]||(e[80]=[d("实物商品")])),_:1,__:[80]})):(n(),g(y,{key:1,type:"warning",class:"type-tag"},{default:l(()=>e[81]||(e[81]=[d("虚拟商品")])),_:1,__:[81]}))])])]),t("div",kl,[t("div",yl,[e[82]||(e[82]=t("span",{class:"price-label"},"成本价",-1)),t("span",bl,o(r.product.costPriceMin&&r.product.costPriceMax?r.product.costPriceMin===r.product.costPriceMax?`¥${h(r.product.costPriceMin)}`:`¥${h(r.product.costPriceMin)}-${h(r.product.costPriceMax)}`:"¥0.00"),1)]),t("div",wl,[e[83]||(e[83]=t("span",{class:"price-label"},"划线价",-1)),t("span",Pl,o(ze()?`¥${h(ze())}`:"¥0.00"),1)]),t("div",xl,[e[84]||(e[84]=t("span",{class:"price-label"},"采购价",-1)),t("span",Rl,o(r.product.purchasePriceMin&&r.product.purchasePriceMax?r.product.purchasePriceMin===r.product.purchasePriceMax?`¥${h(r.product.purchasePriceMin)}`:`¥${h(r.product.purchasePriceMin)}-${h(r.product.purchasePriceMax)}`:"¥0.00"),1)]),t("div",Vl,[e[85]||(e[85]=t("span",{class:"stats-label"},"销量",-1)),t("span",Cl,o(r.product.salesCount||0),1)]),t("div",Sl,[e[86]||(e[86]=t("span",{class:"stats-label"},"库存",-1)),t("span",Ml,o(Ne()),1)])])]),a(wt,{modelValue:fe.value,"onUpdate:modelValue":e[34]||(e[34]=p=>fe.value=p),class:"detail-tabs"},{default:l(()=>[a(me,{label:"基本信息",name:"basic"},{default:l(()=>[t("div",Ul,[a(ie,{column:2,border:""},{default:l(()=>[a(w,{label:"商品分类"},{default:l(()=>[d(o(Ie(r.product.categoryId)),1)]),_:1}),a(w,{label:"条形码"},{default:l(()=>[d(o(r.product.barcode||"无"),1)]),_:1}),a(w,{label:"商品单位"},{default:l(()=>[d(o(r.product.unit||"件"),1)]),_:1}),a(w,{label:"商品类型"},{default:l(()=>[d(o(r.product.type===1?"实物商品":"虚拟商品"),1)]),_:1}),a(w,{label:"是否推荐"},{default:l(()=>[a(y,{type:r.product.isRecommend?"success":"info"},{default:l(()=>[d(o(r.product.isRecommend?"是":"否"),1)]),_:1},8,["type"])]),_:1}),a(w,{label:"创建时间"},{default:l(()=>[d(o(ee(r.product.createTime)),1)]),_:1})]),_:1}),mt.value?(n(),c("div",zl,[e[87]||(e[87]=t("h3",null,"物理属性",-1)),a(ie,{column:3,border:""},{default:l(()=>[r.product.length?(n(),g(w,{key:0,label:"长度"},{default:l(()=>[d(o(r.product.length)+"cm",1)]),_:1})):v("",!0),r.product.wide?(n(),g(w,{key:1,label:"宽度"},{default:l(()=>[d(o(r.product.wide)+"cm",1)]),_:1})):v("",!0),r.product.tall?(n(),g(w,{key:2,label:"高度"},{default:l(()=>[d(o(r.product.tall)+"cm",1)]),_:1})):v("",!0),r.product.volume?(n(),g(w,{key:3,label:"体积"},{default:l(()=>[d(o(r.product.volume)+"cm³",1)]),_:1})):v("",!0),r.product.weight?(n(),g(w,{key:4,label:"重量"},{default:l(()=>[d(o(r.product.weight)+"kg",1)]),_:1})):v("",!0)]),_:1})])):v("",!0),t("div",Nl,[e[88]||(e[88]=t("h3",null,"物流设置",-1)),a(ie,{column:2,border:""},{default:l(()=>[a(w,{label:"运费类型"},{default:l(()=>[d(o(r.product.shippingType==="unified"?"统一运费":"运费模板"),1)]),_:1}),r.product.shippingType==="unified"?(n(),g(w,{key:0,label:"运费金额"},{default:l(()=>[d(" ¥"+o(r.product.shippingFee||"0.00"),1)]),_:1})):v("",!0),r.product.shippingType==="template"?(n(),g(w,{key:1,label:"运费模板"},{default:l(()=>[d(o(ft(r.product.shippingTemplateId)),1)]),_:1})):v("",!0),a(w,{label:"售后服务"},{default:l(()=>[d(o(r.product.afterSaleType==="no_return"?"不可退换":"支持退换"),1)]),_:1}),r.product.afterSaleType==="support_return"?(n(),g(w,{key:2,label:"支持服务"},{default:l(()=>[d(o(gt(r.product.afterSaleServices)),1)]),_:1})):v("",!0)]),_:1})]),r.product.images&&r.product.images.length>0?(n(),c("div",$l,[e[90]||(e[90]=t("h3",null,"商品图片",-1)),t("div",Ll,[(n(!0),c(T,null,G(r.product.images,(p,$)=>(n(),c("div",{key:$,class:"image-item"},[a(H,{src:p.imgUrl,"preview-src-list":Q.value,fit:"cover",style:{width:"100px",height:"100px","border-radius":"4px"}},null,8,["src","preview-src-list"]),p.isMain?(n(),g(y,{key:0,type:"success",size:"small",class:"main-tag"},{default:l(()=>e[89]||(e[89]=[d("主图")])),_:1,__:[89]})):v("",!0)]))),128))])])):v("",!0),r.product.description?(n(),c("div",Il,[e[91]||(e[91]=t("h3",null,"商品描述",-1)),t("div",{class:"description-content",innerHTML:r.product.description},null,8,Bl)])):v("",!0),r.product.features?(n(),c("div",Dl,[e[92]||(e[92]=t("h3",null,"商品特点",-1)),t("div",Tl,o(r.product.features),1)])):v("",!0),r.product.instructions?(n(),c("div",jl,[e[93]||(e[93]=t("h3",null,"使用说明",-1)),t("div",Kl,o(r.product.instructions),1)])):v("",!0)])]),_:1}),a(me,{label:"规格库存",name:"spec"},{default:l(()=>[t("div",Fl,[r.product.skuList&&r.product.skuList.length>0?(n(),c("div",El,[e[96]||(e[96]=t("h3",null,"SKU列表",-1)),a(pe,{data:r.product.skuList,border:"",style:{position:"relative","z-index":"1"}},{default:l(()=>[a(_,{label:"规格",width:"200"},{default:l(({row:p})=>[t("div",Al,[p.spec1Value||p.spec2Value||p.spec3Value?(n(),c(T,{key:0},[p.spec1Value?(n(),g(y,{key:0,size:"small",class:"spec-tag"},{default:l(()=>[d(o(p.spec1Value),1)]),_:2},1024)):v("",!0),p.spec2Value?(n(),g(y,{key:1,size:"small",class:"spec-tag"},{default:l(()=>[d(o(p.spec2Value),1)]),_:2},1024)):v("",!0),p.spec3Value?(n(),g(y,{key:2,size:"small",class:"spec-tag"},{default:l(()=>[d(o(p.spec3Value),1)]),_:2},1024)):v("",!0)],64)):(n(),g(y,{key:1,size:"small",class:"spec-tag default-spec"},{default:l(()=>e[94]||(e[94]=[d("标准规格")])),_:1,__:[94]}))])]),_:1}),a(_,{label:"成本价",width:"100"},{default:l(({row:p})=>[t("span",ql,"¥"+o(h(p.costPrice)),1)]),_:1}),a(_,{label:"采购价",width:"100"},{default:l(({row:p})=>[t("span",Hl,"¥"+o(h(p.purchasePrice)),1)]),_:1}),a(_,{label:"划线价",width:"100"},{default:l(({row:p})=>[t("span",Ol,"¥"+o(h(p.strikethroughPrice)),1)]),_:1}),a(_,{label:"库存信息",width:"150"},{default:l(({row:p})=>[t("div",Gl,[t("div",null,"总库存: "+o(p.totalInventory||0),1),t("div",null,"可用: "+o(p.availableInventory||0),1),t("div",null,"锁定: "+o(p.blockedInventory||0),1)])]),_:1}),a(_,{label:"安全库存",width:"100"},{default:l(({row:p})=>[d(o(p.safetyStock||0),1)]),_:1}),a(_,{label:"SKU图片",width:"100"},{default:l(({row:p})=>[p.imgUrl?(n(),g(H,{key:0,src:p.imgUrl,"preview-src-list":[p.imgUrl],fit:"cover",style:{width:"50px",height:"50px","border-radius":"4px"},"z-index":9999,"preview-teleported":""},null,8,["src","preview-src-list"])):(n(),c("span",Jl,"无图片"))]),_:1})]),_:1},8,["data"]),t("div",Ql,[e[95]||(e[95]=t("h4",null,"规格汇总",-1)),a(ie,{column:3,border:""},{default:l(()=>[a(w,{label:"SKU总数"},{default:l(()=>[d(o(r.product.skuList.length),1)]),_:1}),a(w,{label:"成本价区间"},{default:l(()=>[d(" ¥"+o(h(r.product.costPriceMin))+" - ¥"+o(h(r.product.costPriceMax)),1)]),_:1}),a(w,{label:"采购价区间"},{default:l(()=>[d(" ¥"+o(h(r.product.purchasePriceMin))+" - ¥"+o(h(r.product.purchasePriceMax)),1)]),_:1})]),_:1})])])):(n(),c("div",Wl,[a(bt,{description:"暂无SKU数据"})]))])]),_:1}),a(me,{label:"小程序预览",name:"detail"},{default:l(()=>{var p,$,je,Ke;return[t("div",Xl,[t("div",Yl,[e[103]||(e[103]=t("div",{class:"preview-title"},[t("h3",null,"小程序预览效果"),t("p",null,"展示商品在小程序中的显示效果")],-1)),t("div",Zl,[e[102]||(e[102]=t("div",{class:"phone-header"},[t("div",{class:"status-bar"},[t("div",{class:"left-status"},[t("span",{class:"signal-bars"},[t("span",{class:"bar"}),t("span",{class:"bar"}),t("span",{class:"bar"}),t("span",{class:"bar"})]),t("span",{class:"carrier"},"Sketch"),t("span",{class:"wifi-icon"},"📶")]),t("div",{class:"center-time"},"1:21 AM"),t("div",{class:"right-status"},[t("span",{class:"battery-percent"},"100%"),t("span",{class:"battery-icon"},"🔋")])])],-1)),t("div",ea,[e[101]||(e[101]=t("div",{class:"search-header"},[t("div",{class:"search-bar"},[t("span",{class:"search-icon"},"🔍"),t("span",{class:"search-placeholder"},"请输入关键字搜索")])],-1)),t("div",ta,[t("div",sa,[t("div",la,[Q.value.length>0?(n(),c("img",{key:0,src:Q.value[0],alt:((p=r.product)==null?void 0:p.name)||"商品图片",class:"product-main-image",onError:_t},null,40,aa)):(n(),c("img",ia)),Q.value.length>1?(n(),c("div",ra,[(n(!0),c(T,null,G(Q.value,(Y,O)=>(n(),c("span",{key:O,class:j(["indicator",{active:O===0}])},null,2))),128))])):v("",!0)])]),t("div",oa,[t("div",na,[t("span",ua," ¥"+o(h(ce("purchase")||99)),1),ce("strikethrough")&&ce("strikethrough")>0?(n(),c("span",da," ¥"+o(h(ce("strikethrough"))),1)):(n(),c("span",ca," ¥"+o(h(199)),1))]),t("div",pa,o((($=r.product)==null?void 0:$.name)||"商品名称示例"),1),t("div",ma," 库存："+o(Ne())+"件 ",1),$e().length>0?(n(),c("div",va,[(n(!0),c(T,null,G($e(),Y=>(n(),c("div",{key:Y.name,class:"spec-group"},[t("div",_a,o(Y.name),1),t("div",fa,[(n(!0),c(T,null,G(Y.values,O=>(n(),c("div",{key:String(O),class:j(["spec-option",{active:ke.value[Y.name]===O}]),onClick:Na=>vt(Y.name,String(O))},o(O),11,ga))),128))])]))),128))])):v("",!0)]),t("div",ha,[t("div",ka,[e[98]||(e[98]=t("h4",null,"商品详情",-1)),(je=r.product)!=null&&je.description?(n(),c("div",ya,[t("div",{innerHTML:r.product.description,class:"rich-content"},null,8,ba)])):(n(),c("div",wa,e[97]||(e[97]=[t("p",null,"这里展示商品的详细信息，包括产品特性、材质说明、尺寸规格等详细内容。",-1),t("p",null,"商品采用优质材料制作，工艺精良，品质保证。适用于各种场合，是您的理想选择。",-1),t("p",null,"产品经过严格质量检测，确保每一件商品都符合高标准要求。",-1)])))]),t("div",Pa,[e[100]||(e[100]=t("h4",null,"商品参数",-1)),t("div",xa,[(Ke=r.product)!=null&&Ke.unit?(n(),c("div",Ra,[e[99]||(e[99]=t("span",{class:"param-label"},"商品单位：",-1)),t("span",Va,o(r.product.unit),1)])):v("",!0)])])])])])])])])]}),_:1}),r.product.reviewDescribe?(n(),g(me,{key:0,label:"审核记录",name:"review"},{default:l(()=>[t("div",Ca,[t("div",Sa,[e[104]||(e[104]=t("h3",null,"审核信息",-1)),a(ie,{column:2,border:""},{default:l(()=>[a(w,{label:"审核状态"},{default:l(()=>[a(y,{type:te(r.product.status)},{default:l(()=>[d(o(se(r.product.status)),1)]),_:1},8,["type"])]),_:1}),a(w,{label:"审核时间"},{default:l(()=>[d(o(ee(r.product.updateTime)),1)]),_:1}),a(w,{label:"审核备注",span:"2"},{default:l(()=>[t("div",Ma,o(r.product.reviewDescribe),1)]),_:1})]),_:1})])])]),_:1})):v("",!0)]),_:1},8,["modelValue"]),((s=r.product)==null?void 0:s.status)===3?(n(),c("div",Ua,[a(u,{type:"success",onClick:e[35]||(e[35]=p=>oe(r.product,1))},{default:l(()=>e[105]||(e[105]=[d(" 通过审核 ")])),_:1,__:[105]}),a(u,{type:"danger",onClick:e[36]||(e[36]=p=>oe(r.product,2))},{default:l(()=>e[106]||(e[106]=[d(" 拒绝审核 ")])),_:1,__:[106]})])):v("",!0)])):v("",!0)]}),_:1},8,["modelValue"])])}}});const Ia=zt(za,[["__scopeId","data-v-4a98f306"]]);export{Ia as default};
