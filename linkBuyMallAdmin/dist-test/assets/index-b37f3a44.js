import{d as Je,q as We,c as _e,r as y,z as Xe,E as f,w as J,x as Ye,b as m,A as Ze,o as p,e as C,k as t,h as l,y as n,f as s,H as et,B as tt,g,t as d,j as v,C as R,aq as lt,aU as at,aJ as it,n as st,aV as ot,ae as nt,G as ie,_ as rt}from"./index-5502c85d.js";import{l as ut}from"./lodash-93e8a199.js";import{g as dt,a as pt,b as ge,u as mt,c as vt,d as yt,s as ct,e as ft}from"./coupon-3db977c5.js";const _t={class:"coupon-container"},gt={class:"header"},Tt={class:"pagination"},bt={key:0,class:"coupon-detail"},Vt={class:"coupon-card-preview"},wt={class:"coupon-card"},xt={class:"coupon-left"},St={class:"discount-info"},Ct={class:"amount"},kt={class:"threshold-info"},Dt={class:"coupon-right"},Ut={class:"coupon-name"},Lt={class:"coupon-validity"},$t={class:"coupon-scope"},zt={class:"coupon-status"},Et={class:"detail-section"},ht={class:"section-title"},qt={class:"coupon-code"},Ot={class:"coupon-name-text"},Qt={class:"detail-section"},Mt={class:"section-title"},At={class:"discount-value"},It={class:"threshold-value"},Bt={class:"discount-desc"},Ft={class:"detail-section"},Nt={class:"section-title"},Rt={class:"quantity-info"},Pt={key:0,class:"unit"},jt={key:0,class:"unit"},Kt={class:"time-range"},Gt={class:"receive-limit-info"},Ht={key:0},Jt={key:1},Wt={class:"detail-section"},Xt={class:"section-title"},Yt={class:"validity-display"},Zt={class:"time-range"},el={class:"validity-days"},tl={key:0,class:"detail-section"},ll={class:"section-title"},al={class:"description-content"},il={class:"form-section"},sl={class:"receive-time-range"},ol={class:"quantity-setting"},nl={key:0,class:"quantity-input"},rl={class:"receive-limit-setting"},ul={class:"radio-option"},dl={key:0,class:"limit-input"},pl={class:"radio-option"},ml={key:0,class:"limit-input"},vl={class:"form-section"},yl={class:"discount-amount"},cl={class:"threshold-setting"},fl={key:0,class:"threshold-input"},_l={class:"form-section"},gl={class:"receive-time-range"},Tl={class:"validity-days"},bl={class:"validity-days"},Vl={class:"form-section"},wl=Je({__name:"index",setup(xl){const se=We(),P=_e(()=>Number(se.query.mallId||se.params.mallId||1)),oe=y([]),M=y({}),ne=y(0),L=y(1),j=y(10),W=y(!1),X=y(!1),K=y("all"),A=y(""),G=y(""),Y=y(!1),E=y(!1),Z=y(!1),h=y(!1),r=y(null),o=Xe({mallId:P.value,name:"",type:1,discountValue:0,minOrderAmount:0,totalQuantity:0,quantityType:1,startTime:"",endTime:"",receiveStartTime:"",receiveEndTime:"",status:0,promotionType:1,useScope:1,validityType:1,validityDays:1,description:""}),q=y("none"),k=y(null),D=y(null),ee=_e({get:()=>o.quantityType===1?"unlimited":"limited",set:a=>{o.quantityType=a==="unlimited"?1:2}}),b=y(),Te={name:[{required:!0,message:"请输入优惠券名称",trigger:"blur"}],promotionType:[{required:!0,message:"请选择推广方式",trigger:"change"}],discountValue:[{required:!0,message:"请输入优惠金额",trigger:"blur"}],perUserTotalLimit:[{validator:(a,e,u)=>{o.receiveLimitType===2&&(!e||e<1)?u(new Error("请输入有效的限制次数")):u()},trigger:"blur"}],perUserDailyLimit:[{validator:(a,e,u)=>{o.receiveLimitType===3&&(!e||e<1)?u(new Error("请输入有效的每日限制次数")):u()},trigger:"blur"}],useScope:[{required:!0,message:"请选择使用范围",trigger:"change"}],validityType:[{required:!0,message:"请选择有效期类型",trigger:"change"}],validityDays:[{required:!0,message:"请输入有效天数",trigger:"blur"},{type:"number",min:1,message:"有效天数至少为1天",trigger:"blur"}],startTime:[{validator:(a,e,u)=>{o.validityType===1&&!k.value?u(new Error("固定日期时必须设置有效时间")):u()},trigger:"change"}],receiveStartTime:[{validator:(a,e,u)=>{o.promotionType===1&&!D.value?u(new Error("买家领取时必须设置领取时间")):u()},trigger:"change"}],totalQuantity:[{validator:(a,e,u)=>{console.log("value",e),o.promotionType===1&&o.quantityType===2&&e===0?u(new Error("买家领取时必须设置发放总量")):u()},trigger:"change"}]},be=ut.debounce(()=>{L.value=1,x()},600),x=async()=>{W.value=!0;try{const a={mallId:P.value,current:L.value,size:j.value};switch(A.value&&(a.couponName=A.value),G.value&&(a.promotionType=G.value),K.value){case"notStarted":a.status=0;break;case"ongoing":a.status=1;break;case"ended":a.status=2;break}const e=await dt(a);e.code===200?(oe.value=e.data.records||[],ne.value=e.data.total||0):f.error(e.msg||"获取优惠券列表失败")}catch(a){console.error("获取优惠券列表失败:",a),f.error("获取优惠券列表失败")}finally{W.value=!1}},I=async()=>{try{const a=await pt(P.value);a.code===200&&(M.value=a.data||{})}catch(a){console.error("获取统计数据失败:",a)}},Ve=a=>{K.value=a,L.value=1,x()},re=()=>{be()},we=()=>{A.value="",L.value=1,x()},xe=a=>{console.log("选择变化:",a)},Se=()=>{L.value=1,x()},Ce=a=>{j.value=a,L.value=1,x()},ue=a=>({1:"买家领取",2:"卖家发放"})[a]||"未知方式",de=a=>({1:"全场通用",2:"部分商品可用",3:"部分商品不可用"})[a||1]||"全场通用",ke=a=>({1:"固定日期",2:"领券当日起",3:"领券次日起"})[a||1]||"固定日期",pe=a=>({0:"未开始",1:"进行中",2:"已结束"})[a]||"未知状态",me=a=>({0:"info",1:"success",2:""})[a]||"info",De=a=>a.status===0,Ue=a=>a.status===0,Le=a=>a.promotionType===1&&a.status===0,$e=a=>a.promotionType===1&&a.status===1,ze=a=>a.promotionType===2,S=a=>a?new Date(a).toLocaleString("zh-CN"):"",Ee=a=>a.promotionType===2?"随时可发放":a.promotionType===1?a.receiveStartTime&&a.receiveEndTime?`${S(a.receiveStartTime)} 至 ${S(a.receiveEndTime)}`:"未设置领取时间":"-",te=a=>a.validityType===1?a.startTime&&a.endTime?`${S(a.startTime)} 至 ${S(a.endTime)}`:"未设置使用期间":a.validityType===2?`领券当日起${a.validityDays||1}天内有效`:a.validityType===3?`领券次日起${a.validityDays||1}天内有效`:"永久有效",he=a=>({1:"success",2:"warning",3:"danger"})[a||1]||"success",qe=a=>({1:"primary",2:"success",3:"warning"})[a||1]||"primary",ve=a=>{const e=a.discountValue||0,u=a.minOrderAmount||0,$=a.useScope||1,V=`减${e.toFixed(2)}元`;if(u<=0){if($===1)return`无门槛, ${V}`;if($===2)return`指定商品, ${V}`;if($===3)return`指定商品, ${V}`}else{const B=`满${u.toFixed(2)}元`;if($===1)return`订单${B}, ${V}`;if($===2)return`指定商品${B}, ${V}`;if($===3)return`部分商品${B}, ${V}`}return`${V}`},Oe=async a=>{try{const e=await ge(a.id);e.code===200?(r.value=e.data,Y.value=!0):f.error(e.msg||"获取优惠券详情失败")}catch(e){console.error("获取优惠券详情失败:",e),f.error("获取优惠券详情失败")}},Qe=()=>{h.value=!1,Ie(),E.value=!0},Me=async a=>{try{const e=await ge(a.id);if(e.code===200){h.value=!0;const u=e.data;Object.assign(o,u),q.value=u.minOrderAmount?"threshold":"none",u.validityType===1&&u.startTime&&u.endTime?k.value=[new Date(u.startTime),new Date(u.endTime)]:k.value=null,u.promotionType===1&&u.receiveStartTime&&u.receiveEndTime?D.value=[new Date(u.receiveStartTime),new Date(u.receiveEndTime)]:D.value=null,u.receiveLimitType||(o.receiveLimitType=1),u.perUserTotalLimit||(o.perUserTotalLimit=1),u.perUserDailyLimit||(o.perUserDailyLimit=1),E.value=!0}else f.error(e.msg||"获取优惠券详情失败")}catch(e){console.error("获取优惠券详情失败:",e),f.error("获取优惠券详情失败")}},Ae=a=>{r.value=a,Z.value=!0},Ie=()=>{Object.assign(o,{mallId:P.value,name:"",type:1,discountValue:0,minOrderAmount:0,totalQuantity:100,quantityType:1,receiveLimitType:1,perUserTotalLimit:1,perUserDailyLimit:1,startTime:"",endTime:"",receiveStartTime:"",receiveEndTime:"",status:0,promotionType:1,useScope:1,validityType:1,validityDays:1,description:""}),q.value="none",k.value=null,D.value=null},Be=async()=>{var a;try{await((a=b.value)==null?void 0:a.validate()),X.value=!0,o.quantityType===1&&(o.totalQuantity=0),q.value==="none"&&(o.minOrderAmount=0),o.receiveLimitType===1?(o.perUserTotalLimit=void 0,o.perUserDailyLimit=void 0):o.receiveLimitType===2?o.perUserDailyLimit=void 0:o.receiveLimitType===3&&(o.perUserTotalLimit=void 0),o.validityType===1?(k.value&&(o.startTime=k.value[0].toISOString(),o.endTime=k.value[1].toISOString()),o.validityDays=void 0):(o.startTime="",o.endTime=""),o.promotionType===1?D.value&&(o.receiveStartTime=D.value[0].toISOString(),o.receiveEndTime=D.value[1].toISOString()):(o.receiveStartTime="",o.receiveEndTime="");let e;h.value?e=await mt(o.id,o):e=await vt(o),e.code===200?(f.success(h.value?"更新成功":"创建成功"),E.value=!1,x(),I()):f.error(e.msg||"操作失败")}catch{}finally{X.value=!1}},Fe=async a=>{try{await ie.confirm("确认删除该优惠券？","提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"});const e=await yt(a.id);e.code===200?(f.success("删除成功"),x(),I()):f.error(e.msg||"删除失败")}catch(e){e!=="cancel"&&(console.error("删除失败:",e),f.error("删除失败"))}},Ne=async a=>{try{await ie.confirm("确认提前开始领取该优惠券？将把领取开始时间改为当前时间。","提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"});const e=await ct(a.id);e.code===200?(f.success("优惠券领取已开始"),x(),I()):f.error(e.msg||"操作失败")}catch(e){e!=="cancel"&&(console.error("操作失败:",e),f.error("操作失败"))}},Re=async a=>{try{await ie.confirm("确认提前结束领取该优惠券？将把领取结束时间改为当前时间。","提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"});const e=await ft(a.id);e.code===200?(f.success("优惠券领取已结束"),x(),I()):f.error(e.msg||"操作失败")}catch(e){e!=="cancel"&&(console.error("操作失败:",e),f.error("操作失败"))}};return J(()=>o.validityType,()=>{b.value&&b.value.validateField("startTime")}),J(()=>o.promotionType,()=>{b.value&&(b.value.validateField("receiveStartTime"),b.value.validateField("totalQuantity"))}),J(()=>o.quantityType,()=>{b.value&&b.value.validateField("totalQuantity")}),J(()=>o.receiveLimitType,()=>{b.value&&(b.value.validateField("perUserTotalLimit"),b.value.validateField("perUserDailyLimit"))}),Ye(()=>{I(),x()}),(a,e)=>{const u=m("el-breadcrumb-item"),$=m("el-breadcrumb"),V=m("el-tab-pane"),B=m("el-tabs"),le=m("el-input"),ye=m("el-option"),Pe=m("el-select"),U=m("el-button"),w=m("el-table-column"),O=m("el-tag"),je=m("el-table"),Ke=m("el-pagination"),F=m("el-icon"),c=m("el-descriptions-item"),H=m("el-descriptions"),ce=m("el-divider"),ae=m("el-drawer"),T=m("el-form-item"),_=m("el-radio"),Q=m("el-radio-group"),fe=m("el-date-picker"),z=m("el-input-number"),Ge=m("el-form"),He=Ze("loading");return p(),C("div",_t,[t($,{separator:"/",style:{"margin-bottom":"10px"}},{default:l(()=>[t(u,null,{default:l(()=>e[26]||(e[26]=[n("营销中心")])),_:1,__:[26]}),t(u,null,{default:l(()=>e[27]||(e[27]=[n("优惠券")])),_:1,__:[27]})]),_:1}),t(B,{modelValue:K.value,"onUpdate:modelValue":e[0]||(e[0]=i=>K.value=i),onTabChange:Ve,class:"coupon-tabs"},{default:l(()=>[t(V,{label:`全部(${M.value.totalCount||0})`,name:"all"},null,8,["label"]),t(V,{label:`未开始(${M.value.notStartedCount||0})`,name:"notStarted"},null,8,["label"]),t(V,{label:`进行中(${M.value.ongoingCount||0})`,name:"ongoing"},null,8,["label"]),t(V,{label:`已结束(${M.value.endedCount||0})`,name:"ended"},null,8,["label"])]),_:1},8,["modelValue"]),s("div",gt,[t(le,{modelValue:A.value,"onUpdate:modelValue":e[1]||(e[1]=i=>A.value=i),placeholder:"搜索优惠券名称",clearable:"",style:{width:"200px","margin-right":"10px"},onKeyup:et(re,["enter"]),onInput:re,onClear:we},null,8,["modelValue"]),t(Pe,{modelValue:G.value,"onUpdate:modelValue":e[2]||(e[2]=i=>G.value=i),placeholder:"推广方式",clearable:"",style:{width:"200px","margin-right":"10px"},onChange:Se},{default:l(()=>[t(ye,{label:"买家领取",value:1}),t(ye,{label:"卖家发放",value:2})]),_:1},8,["modelValue"]),t(U,{type:"primary",onClick:Qe},{default:l(()=>e[28]||(e[28]=[n(" 新建优惠券 ")])),_:1,__:[28]})]),tt((p(),g(je,{height:"calc(100vh - 280px)",data:oe.value,style:{"margin-top":"16px"},onSelectionChange:xe},{default:l(()=>[t(w,{type:"selection",width:"55"}),t(w,{prop:"couponCode",label:"优惠券编码",width:"160"}),t(w,{prop:"name",label:"优惠券名称","min-width":"200","show-overflow-tooltip":""}),t(w,{label:"优惠内容",width:"220"},{default:l(({row:i})=>[n(d(ve(i)),1)]),_:1}),t(w,{label:"推广方式",width:"100"},{default:l(({row:i})=>[t(O,{type:i.promotionType===2?"success":"warning"},{default:l(()=>[n(d(ue(i.promotionType)),1)]),_:2},1032,["type"])]),_:1}),t(w,{label:"领取期间",width:"180"},{default:l(({row:i})=>[n(d(Ee(i)),1)]),_:1}),t(w,{label:"使用期间",width:"180"},{default:l(({row:i})=>[n(d(te(i)),1)]),_:1}),t(w,{prop:"totalQuantity",label:"发放总量",width:"100"},{default:l(({row:i})=>[n(d(i.totalQuantity||"不限制"),1)]),_:1}),t(w,{prop:"remainingQuantity",label:"剩余",width:"80"},{default:l(({row:i})=>[n(d(i.remainingQuantity||"不限制"),1)]),_:1}),t(w,{label:"状态",width:"100"},{default:l(({row:i})=>[t(O,{type:me(i.status)},{default:l(()=>[n(d(pe(i.status)),1)]),_:2},1032,["type"])]),_:1}),t(w,{prop:"createTime",label:"创建时间",width:"160"},{default:l(({row:i})=>[n(d(S(i.createTime)),1)]),_:1}),t(w,{label:"操作",width:"280",fixed:"right"},{default:l(({row:i})=>[t(U,{link:"",type:"primary",size:"small",onClick:N=>Oe(i)},{default:l(()=>e[29]||(e[29]=[n(" 详情 ")])),_:2,__:[29]},1032,["onClick"]),De(i)?(p(),g(U,{key:0,link:"",type:"primary",size:"small",onClick:N=>Me(i)},{default:l(()=>e[30]||(e[30]=[n(" 编辑 ")])),_:2,__:[30]},1032,["onClick"])):v("",!0),Ue(i)?(p(),g(U,{key:1,link:"",type:"danger",size:"small",onClick:N=>Fe(i)},{default:l(()=>e[31]||(e[31]=[n(" 删除 ")])),_:2,__:[31]},1032,["onClick"])):v("",!0),Le(i)?(p(),g(U,{key:2,link:"",type:"success",size:"small",onClick:N=>Ne(i)},{default:l(()=>e[32]||(e[32]=[n(" 提前开始 ")])),_:2,__:[32]},1032,["onClick"])):v("",!0),$e(i)?(p(),g(U,{key:3,link:"",type:"warning",size:"small",onClick:N=>Re(i)},{default:l(()=>e[33]||(e[33]=[n(" 提前结束 ")])),_:2,__:[33]},1032,["onClick"])):v("",!0),ze(i)?(p(),g(U,{key:4,link:"",type:"primary",size:"small",onClick:N=>Ae(i)},{default:l(()=>e[34]||(e[34]=[n(" 手动发放 ")])),_:2,__:[34]},1032,["onClick"])):v("",!0)]),_:1})]),_:1},8,["data"])),[[He,W.value]]),s("div",Tt,[t(Ke,{"current-page":L.value,"onUpdate:currentPage":e[3]||(e[3]=i=>L.value=i),"page-size":j.value,"onUpdate:pageSize":e[4]||(e[4]=i=>j.value=i),total:ne.value,background:"","page-sizes":[10,20,50,100],onSizeChange:Ce,onCurrentChange:x},null,8,["current-page","page-size","total"])]),t(ae,{modelValue:Y.value,"onUpdate:modelValue":e[5]||(e[5]=i=>Y.value=i),title:"优惠券详情",size:"60%"},{default:l(()=>[r.value?(p(),C("div",bt,[s("div",Vt,[s("div",wt,[s("div",xt,[s("div",St,[e[35]||(e[35]=s("span",{class:"currency"},"¥",-1)),s("span",Ct,d(r.value.discountValue),1)]),s("div",kt,d(r.value.minOrderAmount?`满${r.value.minOrderAmount}元可用`:"无门槛使用"),1)]),s("div",Dt,[s("div",Ut,d(r.value.name),1),s("div",Lt,d(te(r.value)),1),s("div",$t,d(de(r.value.useScope)),1),s("div",zt,[t(O,{type:me(r.value.status),size:"small"},{default:l(()=>[n(d(pe(r.value.status)),1)]),_:1},8,["type"])])])])]),s("div",Et,[s("h3",ht,[t(F,null,{default:l(()=>[t(R(lt))]),_:1}),e[36]||(e[36]=n(" 基本信息 "))]),t(H,{column:2,border:"",size:"default"},{default:l(()=>[t(c,{label:"优惠券编码","label-class-name":"detail-label"},{default:l(()=>[s("span",qt,d(r.value.couponCode),1)]),_:1}),t(c,{label:"优惠券ID","label-class-name":"detail-label"},{default:l(()=>[n(d(r.value.id),1)]),_:1}),t(c,{label:"优惠券名称","label-class-name":"detail-label"},{default:l(()=>[s("span",Ot,d(r.value.name),1)]),_:1}),t(c,{label:"推广方式","label-class-name":"detail-label"},{default:l(()=>[t(O,{type:r.value.promotionType===1?"success":"warning",size:"small"},{default:l(()=>[n(d(ue(r.value.promotionType)),1)]),_:1},8,["type"])]),_:1}),t(c,{label:"创建时间","label-class-name":"detail-label"},{default:l(()=>[n(d(S(r.value.createTime)),1)]),_:1}),t(c,{label:"更新时间","label-class-name":"detail-label"},{default:l(()=>[n(d(S(r.value.updateTime)),1)]),_:1})]),_:1})]),s("div",Qt,[s("h3",Mt,[t(F,null,{default:l(()=>[t(R(at))]),_:1}),e[37]||(e[37]=n(" 优惠规则 "))]),t(H,{column:2,border:"",size:"default"},{default:l(()=>[t(c,{label:"优惠金额","label-class-name":"detail-label"},{default:l(()=>[s("span",At,"¥"+d(r.value.discountValue),1)]),_:1}),t(c,{label:"使用门槛","label-class-name":"detail-label"},{default:l(()=>[s("span",It,d(r.value.minOrderAmount?`满¥${r.value.minOrderAmount}`:"无门槛"),1)]),_:1}),t(c,{label:"使用范围","label-class-name":"detail-label"},{default:l(()=>[t(O,{type:he(r.value.useScope),size:"small"},{default:l(()=>[n(d(de(r.value.useScope)),1)]),_:1},8,["type"])]),_:1}),t(c,{label:"优惠内容","label-class-name":"detail-label"},{default:l(()=>[s("span",Bt,d(ve(r.value)),1)]),_:1})]),_:1})]),s("div",Ft,[s("h3",Nt,[t(F,null,{default:l(()=>[t(R(it))]),_:1}),e[38]||(e[38]=n(" 发放设置 "))]),t(H,{column:2,border:"",size:"default"},{default:l(()=>[t(c,{label:"发放总量","label-class-name":"detail-label"},{default:l(()=>[s("span",Rt,[n(d(r.value.totalQuantity||"不限制")+" ",1),r.value.totalQuantity?(p(),C("span",Pt,"张")):v("",!0)])]),_:1}),t(c,{label:"剩余数量","label-class-name":"detail-label"},{default:l(()=>[s("span",{class:st(["remaining-info",{"low-stock":r.value.remainingQuantity&&r.value.remainingQuantity<10}])},[n(d(r.value.remainingQuantity||"不限制")+" ",1),r.value.remainingQuantity?(p(),C("span",jt,"张")):v("",!0)],2)]),_:1}),r.value.promotionType===1?(p(),g(c,{key:0,label:"领取时间","label-class-name":"detail-label",span:2},{default:l(()=>[s("div",Kt,[e[39]||(e[39]=s("span",{class:"time-label"},"开始：",-1)),n(d(S(r.value.receiveStartTime))+" ",1),t(ce,{direction:"vertical"}),e[40]||(e[40]=s("span",{class:"time-label"},"结束：",-1)),n(d(S(r.value.receiveEndTime)),1)])]),_:1})):v("",!0),r.value.promotionType===1&&r.value.receiveLimitType!==1?(p(),g(c,{key:1,label:"领取限制","label-class-name":"detail-label",span:2},{default:l(()=>[s("div",Gt,[r.value.receiveLimitType===2?(p(),C("span",Ht,[e[41]||(e[41]=n(" 每人限领 ")),s("strong",null,d(r.value.perUserTotalLimit),1),e[42]||(e[42]=n(" 次 "))])):r.value.receiveLimitType===3?(p(),C("span",Jt,[e[43]||(e[43]=n(" 每人每天限领 ")),s("strong",null,d(r.value.perUserDailyLimit),1),e[44]||(e[44]=n(" 次 "))])):v("",!0)])]),_:1})):v("",!0)]),_:1})]),s("div",Wt,[s("h3",Xt,[t(F,null,{default:l(()=>[t(R(ot))]),_:1}),e[45]||(e[45]=n(" 有效期设置 "))]),t(H,{column:2,border:"",size:"default"},{default:l(()=>[t(c,{label:"有效期类型","label-class-name":"detail-label"},{default:l(()=>[t(O,{type:qe(r.value.validityType),size:"small"},{default:l(()=>[n(d(ke(r.value.validityType)),1)]),_:1},8,["type"])]),_:1}),t(c,{label:"有效期","label-class-name":"detail-label"},{default:l(()=>[s("span",Yt,d(te(r.value)),1)]),_:1}),r.value.validityType===1?(p(),g(c,{key:0,label:"使用时间","label-class-name":"detail-label",span:2},{default:l(()=>[s("div",Zt,[e[46]||(e[46]=s("span",{class:"time-label"},"开始：",-1)),n(d(S(r.value.startTime))+" ",1),t(ce,{direction:"vertical"}),e[47]||(e[47]=s("span",{class:"time-label"},"结束：",-1)),n(d(S(r.value.endTime)),1)])]),_:1})):v("",!0),r.value.validityType!==1?(p(),g(c,{key:1,label:"有效天数","label-class-name":"detail-label"},{default:l(()=>[s("span",el,d(r.value.validityDays)+" 天",1)]),_:1})):v("",!0)]),_:1})]),r.value.description?(p(),C("div",tl,[s("h3",ll,[t(F,null,{default:l(()=>[t(R(nt))]),_:1}),e[48]||(e[48]=n(" 描述信息 "))]),s("div",al,[s("p",null,d(r.value.description),1)])])):v("",!0)])):v("",!0)]),_:1},8,["modelValue"]),t(ae,{modelValue:E.value,"onUpdate:modelValue":e[24]||(e[24]=i=>E.value=i),title:h.value?"编辑优惠券":"新建优惠券",size:"60%"},{footer:l(()=>[t(U,{onClick:e[23]||(e[23]=i=>E.value=!1)},{default:l(()=>e[76]||(e[76]=[n("取消")])),_:1,__:[76]}),t(U,{type:"primary",onClick:Be,loading:X.value},{default:l(()=>[n(d(h.value?"更新":"创建"),1)]),_:1},8,["loading"])]),default:l(()=>[t(Ge,{model:o,rules:Te,ref_key:"formRef",ref:b,"label-width":"120px"},{default:l(()=>[s("div",il,[e[60]||(e[60]=s("h3",null,"基本信息",-1)),t(T,{label:"优惠券名称",prop:"name"},{default:l(()=>[t(le,{modelValue:o.name,"onUpdate:modelValue":e[6]||(e[6]=i=>o.name=i),placeholder:"请输入优惠券名称",style:{width:"400px"}},null,8,["modelValue"])]),_:1}),t(T,{label:"推广方式",prop:"promotionType"},{default:l(()=>[t(Q,{modelValue:o.promotionType,"onUpdate:modelValue":e[7]||(e[7]=i=>o.promotionType=i)},{default:l(()=>[t(_,{value:1},{default:l(()=>e[49]||(e[49]=[n("买家领取")])),_:1,__:[49]}),t(_,{value:2},{default:l(()=>e[50]||(e[50]=[n("卖家发放")])),_:1,__:[50]})]),_:1},8,["modelValue"])]),_:1}),o.promotionType===1?(p(),g(T,{key:0,label:"领取时间",prop:"receiveStartTime"},{default:l(()=>[s("div",sl,[t(fe,{modelValue:D.value,"onUpdate:modelValue":e[8]||(e[8]=i=>D.value=i),type:"datetimerange","range-separator":"至","start-placeholder":"领取开始时间","end-placeholder":"领取结束时间",style:{width:"400px"}},null,8,["modelValue"])])]),_:1})):v("",!0),t(T,{label:"发放总量",prop:"totalQuantity"},{default:l(()=>[s("div",ol,[t(Q,{modelValue:ee.value,"onUpdate:modelValue":e[9]||(e[9]=i=>ee.value=i)},{default:l(()=>[t(_,{label:"unlimited"},{default:l(()=>e[51]||(e[51]=[n("不限制数量")])),_:1,__:[51]}),t(_,{label:"limited"},{default:l(()=>e[52]||(e[52]=[n("限制数量")])),_:1,__:[52]})]),_:1},8,["modelValue"]),ee.value==="limited"?(p(),C("div",nl,[t(z,{modelValue:o.totalQuantity,"onUpdate:modelValue":e[10]||(e[10]=i=>o.totalQuantity=i),min:1,max:1e7,style:{width:"150px"}},null,8,["modelValue"]),e[53]||(e[53]=s("span",{style:{"margin-left":"5px"}},"张",-1))])):v("",!0)])]),_:1}),o.promotionType===1?(p(),g(T,{key:1,label:"领取次数",prop:"receiveLimitType"},{default:l(()=>[s("div",rl,[t(Q,{modelValue:o.receiveLimitType,"onUpdate:modelValue":e[13]||(e[13]=i=>o.receiveLimitType=i)},{default:l(()=>[t(_,{value:1},{default:l(()=>e[54]||(e[54]=[s("div",{class:"radio-option"},[s("span",{class:"radio-text"},"不限制领取次数")],-1)])),_:1,__:[54]}),t(_,{value:2},{default:l(()=>[s("div",ul,[e[56]||(e[56]=s("span",{class:"radio-text"},"限制领取",-1)),o.receiveLimitType===2?(p(),C("div",dl,[t(z,{modelValue:o.perUserTotalLimit,"onUpdate:modelValue":e[11]||(e[11]=i=>o.perUserTotalLimit=i),min:1,max:100,style:{width:"120px","margin-left":"10px"}},null,8,["modelValue"]),e[55]||(e[55]=s("span",{style:{"margin-left":"5px"}},"次",-1))])):v("",!0)])]),_:1}),t(_,{value:3},{default:l(()=>[s("div",pl,[e[58]||(e[58]=s("span",{class:"radio-text"},"每天限制领取",-1)),o.receiveLimitType===3?(p(),C("div",ml,[t(z,{modelValue:o.perUserDailyLimit,"onUpdate:modelValue":e[12]||(e[12]=i=>o.perUserDailyLimit=i),min:1,max:20,style:{width:"120px","margin-left":"10px"}},null,8,["modelValue"]),e[57]||(e[57]=s("span",{style:{"margin-left":"5px"}},"次",-1))])):v("",!0)])]),_:1})]),_:1},8,["modelValue"]),e[59]||(e[59]=s("div",{class:"receive-limit-tip"},[s("span",{style:{color:"#909399","font-size":"12px"}},"领取次数只对买家领取的场景有效")],-1))])]),_:1})):v("",!0)]),s("div",vl,[e[68]||(e[68]=s("h3",null,"优惠设置",-1)),t(T,{label:"优惠金额",prop:"discountValue"},{default:l(()=>[s("div",yl,[t(z,{modelValue:o.discountValue,"onUpdate:modelValue":e[14]||(e[14]=i=>o.discountValue=i),min:.01,precision:2,style:{width:"150px"}},null,8,["modelValue"]),e[61]||(e[61]=s("span",{style:{"margin-left":"5px"}},"元",-1))])]),_:1}),t(T,{label:"使用门槛",prop:"minOrderAmount"},{default:l(()=>[s("div",cl,[t(Q,{modelValue:q.value,"onUpdate:modelValue":e[15]||(e[15]=i=>q.value=i)},{default:l(()=>[t(_,{label:"none"},{default:l(()=>e[62]||(e[62]=[n("无门槛使用")])),_:1,__:[62]}),t(_,{label:"threshold"},{default:l(()=>e[63]||(e[63]=[n("订单满")])),_:1,__:[63]})]),_:1},8,["modelValue"]),q.value==="threshold"?(p(),C("div",fl,[t(z,{modelValue:o.minOrderAmount,"onUpdate:modelValue":e[16]||(e[16]=i=>o.minOrderAmount=i),min:0,precision:2,style:{width:"150px"}},null,8,["modelValue"]),e[64]||(e[64]=s("span",{style:{"margin-left":"5px"}},"元可使用",-1))])):v("",!0)])]),_:1}),t(T,{label:"使用范围",prop:"useScope"},{default:l(()=>[t(Q,{modelValue:o.useScope,"onUpdate:modelValue":e[17]||(e[17]=i=>o.useScope=i)},{default:l(()=>[t(_,{value:1},{default:l(()=>e[65]||(e[65]=[n("全场通用")])),_:1,__:[65]}),t(_,{value:2},{default:l(()=>e[66]||(e[66]=[n("部分商品可用")])),_:1,__:[66]}),t(_,{value:3},{default:l(()=>e[67]||(e[67]=[n("部分商品不可用")])),_:1,__:[67]})]),_:1},8,["modelValue"])]),_:1})]),s("div",_l,[e[74]||(e[74]=s("h3",null,"有效期设置",-1)),t(T,{label:"有效期类型",prop:"validityType"},{default:l(()=>[t(Q,{modelValue:o.validityType,"onUpdate:modelValue":e[18]||(e[18]=i=>o.validityType=i)},{default:l(()=>[t(_,{value:1},{default:l(()=>e[69]||(e[69]=[n("固定日期")])),_:1,__:[69]}),t(_,{value:2},{default:l(()=>e[70]||(e[70]=[n("领券当日起")])),_:1,__:[70]}),t(_,{value:3},{default:l(()=>e[71]||(e[71]=[n("领券次日起")])),_:1,__:[71]})]),_:1},8,["modelValue"])]),_:1}),o.validityType===1?(p(),g(T,{key:0,label:"有效期",prop:"startTime"},{default:l(()=>[s("div",gl,[t(fe,{modelValue:k.value,"onUpdate:modelValue":e[19]||(e[19]=i=>k.value=i),type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"400px"}},null,8,["modelValue"])])]),_:1})):v("",!0),o.validityType===2?(p(),g(T,{key:1,label:"有效天数",prop:"validityDays"},{default:l(()=>[s("div",Tl,[t(z,{modelValue:o.validityDays,"onUpdate:modelValue":e[20]||(e[20]=i=>o.validityDays=i),min:1,max:365,style:{width:"150px"}},null,8,["modelValue"]),e[72]||(e[72]=s("span",{style:{"margin-left":"10px",color:"#909399"}},"天内可用",-1))])]),_:1})):v("",!0),o.validityType===3?(p(),g(T,{key:2,label:"有效天数",prop:"validityDays"},{default:l(()=>[s("div",bl,[t(z,{modelValue:o.validityDays,"onUpdate:modelValue":e[21]||(e[21]=i=>o.validityDays=i),min:1,max:365,style:{width:"150px"}},null,8,["modelValue"]),e[73]||(e[73]=s("span",{style:{"margin-left":"10px",color:"#909399"}},"天内可用",-1))])]),_:1})):v("",!0)]),s("div",Vl,[e[75]||(e[75]=s("h3",null,"描述信息",-1)),t(T,{label:"优惠券描述",prop:"description"},{default:l(()=>[t(le,{modelValue:o.description,"onUpdate:modelValue":e[22]||(e[22]=i=>o.description=i),type:"textarea",rows:3,placeholder:"请输入优惠券描述",style:{width:"400px"}},null,8,["modelValue"])]),_:1})])]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),t(ae,{modelValue:Z.value,"onUpdate:modelValue":e[25]||(e[25]=i=>Z.value=i),title:"手动发放优惠券",size:"40%"},{default:l(()=>e[77]||(e[77]=[s("div",{class:"send-content"},[s("p",null,"手动发放优惠券功能正在开发中...")],-1)])),_:1,__:[77]},8,["modelValue"])])}}});const Dl=rt(wl,[["__scopeId","data-v-26e7c56a"]]);export{Dl as default};
