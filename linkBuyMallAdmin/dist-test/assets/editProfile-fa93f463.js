import{d as E,a8 as V,a9 as P,aa as g,ab as F,r,v as B,c as h,u as A,x as I,_ as U,b as m,o as w,e as C,f as i,k as l,h as d,n as c}from"./index-5502c85d.js";const $="/assets/accountAvatar-227de53f.svg",k=E({name:"editProfile",components:{ElForm:V,ElFormItem:P,ElInput:g,ElButton:F},setup(){var a,_,v,D;const e=r(),o=r(!0),p=r(!0),f=B(),s=h(()=>f.getUserInfo()),b=A(),t=r({name:(a=s.value)==null?void 0:a.name,email:((_=s.value)==null?void 0:_.email)||"--",mobile:((v=s.value)==null?void 0:v.mobile)||"--",devid:(D=s.value)==null?void 0:D.id}),n=()=>{console.log("Email change requested")},u=()=>{console.log("Phone bind requested")};return I(()=>{}),{formData:t,isEmailDisabled:o,isPhoneDisabled:p,router:b,ruleFormRef:e,changeEmail:n,bindPhone:u,accountAvatar:$}}});const R={class:"account-wrapper account-edit-profile"},q={class:"account-wrapper-inner"},y={class:"profile-picture"},N=["src"];function z(e,o,p,f,s,b){const t=m("el-input"),n=m("el-form-item"),u=m("el-form");return w(),C("div",R,[o[3]||(o[3]=i("p",{class:"account-wrapper-title"},"账号信息",-1)),i("div",q,[i("div",y,[i("img",{src:e.accountAvatar,alt:"Profile Picture"},null,8,N)]),l(u,{model:e.formData,ref:"ruleFormRef"},{default:d(()=>[l(n,{label:"姓名","label-position":"top"},{default:d(()=>[l(t,{modelValue:e.formData.name,"onUpdate:modelValue":o[0]||(o[0]=a=>e.formData.name=a),disabled:"",class:c({"no-border-r":e.isEmailDisabled,"disabled-input":!0})},null,8,["modelValue","class"])]),_:1}),l(n,{label:"邮箱","label-position":"top"},{default:d(()=>[l(t,{modelValue:e.formData.email,"onUpdate:modelValue":o[1]||(o[1]=a=>e.formData.email=a),disabled:"",class:c({"no-border-r":e.isEmailDisabled,"disabled-input":!0})},null,8,["modelValue","class"])]),_:1}),l(n,{label:"手机号","label-position":"top"},{default:d(()=>[l(t,{modelValue:e.formData.mobile,"onUpdate:modelValue":o[2]||(o[2]=a=>e.formData.mobile=a),disabled:e.isPhoneDisabled,class:c({"no-border-r":e.isPhoneDisabled})},null,8,["modelValue","disabled","class"])]),_:1})]),_:1},8,["model"])])])}const S=U(k,[["render",z],["__scopeId","data-v-c879e7c4"]]);export{S as default};
