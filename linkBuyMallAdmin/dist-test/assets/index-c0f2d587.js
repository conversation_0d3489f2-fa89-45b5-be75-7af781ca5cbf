import{$ as X,d as he,c as q,r as m,z as _e,w as xe,b as _,A as ye,o as u,g as U,h as r,f as a,k as n,y as b,e as x,m as J,F as Q,j as B,n as W,B as Pe,t as y,E as I,G as be,_ as Ie}from"./index-5502c85d.js";import{g as ke}from"./supplier-5afd610f.js";function Se(k){return X.get({url:"/company-spu/selector/list",params:k})}function Ve(k){return X.post({url:"/company-spu/selector/batch",data:k})}function we(k){return ke(k||{})}const Ce={class:"product-selector-container"},Re={class:"left-panel"},ze={class:"search-area-fixed"},Ue={class:"search-filters"},Be={class:"filter-row"},Ne={class:"filter-row"},De={class:"custom-checkbox-wrapper"},Le={class:"checkbox-inner"},$e={key:0,class:"check-icon",viewBox:"0 0 1024 1024"},Ae={key:1,class:"indeterminate-icon"},Ee={class:"price-range"},Fe={class:"profit-range"},Oe={class:"table-container"},Te=["onClick"],Me={class:"checkbox-inner"},je={key:0,class:"check-icon",viewBox:"0 0 1024 1024"},He={class:"product-info"},Ge={class:"product-image"},Ke={class:"product-details"},qe=["title"],Je={key:0,class:"product-brand"},Qe={class:"pagination-container"},We={class:"right-panel"},Xe={class:"selected-header"},Ye={class:"selected-count"},Ze={class:"selected-content"},et={key:0,class:"empty-selected"},tt={key:1,class:"selected-list"},lt={class:"selected-product-info"},at={class:"selected-product-image"},ot={class:"selected-product-details"},st=["title"],nt={class:"selected-product-price"},it={class:"selected-product-actions"},dt={class:"selected-actions"},rt={class:"dialog-footer"},ct=he({__name:"index",props:{modelValue:{type:Boolean},selectedProductIds:{default:()=>[]},mallId:{},maxSelect:{default:999999}},emits:["update:modelValue","confirm"],setup(k,{emit:Y}){const c=k,F=Y,R=q({get:()=>c.modelValue,set:t=>F("update:modelValue",t)}),L=m(!1),f=m([]),O=m(0),g=m(1),$=m(10),Z=m(),V=m(!1),s=_e({categoryId:void 0,spuId:"",productName:"",platform:void 0,supplierId:void 0,minPrice:void 0,maxPrice:void 0,minProfitRate:void 0,maxProfitRate:void 0}),N=m(""),D=m(""),w=m([]),i=m([]),d=m([]),T=q(()=>{const t=i.value.filter(e=>f.value.some(o=>o.id===e)).length;return t>0&&t<f.value.length});xe(R,t=>{if(t){if(!c.mallId){I.error("mallId参数缺失，无法获取商品列表"),R.value=!1;return}i.value=[...c.selectedProductIds],d.value=[],g.value=1,M(),s.platform===2&&j(),le(),C()}});const M=()=>{Object.assign(s,{categoryId:void 0,spuId:"",productName:"",platform:void 0,supplierId:void 0,minPrice:void 0,maxPrice:void 0,minProfitRate:void 0,maxProfitRate:void 0}),V.value=!1,N.value="",D.value=""},j=async()=>{try{const t=await we();t.code===200?t.data&&t.data.records?w.value=t.data.records||[]:Array.isArray(t.data)?w.value=t.data:w.value=[]:(console.error("获取供应商列表失败:",t.msg),w.value=[{id:1,name:"京东快递"},{id:2,name:"天猫超市"},{id:3,name:"苏宁易购"},{id:4,name:"客户供应商"}])}catch(t){console.error("获取供应商列表失败:",t),w.value=[{id:1,name:"京东快递"},{id:2,name:"天猫超市"},{id:3,name:"苏宁易购"},{id:4,name:"客户供应商"}]}},C=async()=>{var t,e;try{L.value=!0;const o={page:g.value,size:$.value,categoryId:s.categoryId||void 0,spuId:((t=s.spuId)==null?void 0:t.trim())||void 0,productName:((e=s.productName)==null?void 0:e.trim())||void 0,platform:s.platform||void 0,supplierId:s.supplierId||void 0,minPrice:s.minPrice||void 0,maxPrice:s.maxPrice||void 0,minProfitRate:s.minProfitRate||void 0,maxProfitRate:s.maxProfitRate||void 0,sortField:N.value||void 0,sortOrder:D.value||void 0};Object.keys(o).forEach(h=>{const v=h;(o[v]===void 0||o[v]===null||o[v]==="")&&delete o[v]});const p=await Se(o);if(p.code===200){const h=p.data;f.value=h.records||[],O.value=h.total||0}else I.error(p.msg||"获取商品列表失败");z(),te()}catch(o){console.error("获取商品列表失败:",o),I.error("获取商品列表失败")}finally{L.value=!1}},H=t=>i.value.includes(t),G=t=>i.value.includes(t.id)?!0:i.value.length<c.maxSelect,ee=t=>{if(!G(t)){i.value.length>=c.maxSelect&&I.warning(`最多只能选择${c.maxSelect}个商品`);return}const e=t.id,o=i.value.indexOf(e);if(o>-1){i.value.splice(o,1);const p=d.value.findIndex(h=>h.id===e);p>-1&&d.value.splice(p,1)}else{if(i.value.length>=c.maxSelect){I.warning(`最多只能选择${c.maxSelect}个商品`);return}i.value.push(e),d.value.push(t)}z()},z=()=>{const t=f.value.map(o=>o.id),e=i.value.filter(o=>t.includes(o));V.value=t.length>0&&e.length===t.length},te=()=>{i.value.forEach(t=>{const e=f.value.find(o=>o.id===t);e&&!d.value.some(o=>o.id===t)&&d.value.push(e)})},le=async()=>{if(i.value.length===0){d.value=[];return}try{const t=await Ve(i.value);t.code===200?d.value=t.data||[]:(console.error("获取已选商品详情失败:",t.msg),d.value=[])}catch(t){console.error("获取已选商品详情失败:",t),d.value=[]}},ae=()=>{if(V.value){const t=f.value.map(e=>e.id);i.value=i.value.filter(e=>!t.includes(e)),d.value=d.value.filter(e=>!t.includes(e.id)),V.value=!1}else{const t=c.maxSelect-i.value.length,e=f.value.slice(0,t);e.length<f.value.length&&I.warning(`最多只能选择${c.maxSelect}个商品`),e.forEach(o=>{i.value.includes(o.id)||(i.value.push(o.id),d.value.push(o))}),z()}},oe=t=>{const e=i.value.indexOf(t);if(e>-1){i.value.splice(e,1);const o=d.value.findIndex(p=>p.id===t);o>-1&&d.value.splice(o,1),z()}},se=async()=>{try{await be.confirm("确定要清空所有已选商品吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),i.value=[],d.value=[],z(),I.success("已清空所有选择")}catch{}},ne=({column:t,prop:e,order:o})=>{o?(N.value=e,D.value=o==="ascending"?"asc":"desc"):(N.value="",D.value=""),g.value=1,C()};let A=null;const P=()=>{A&&clearTimeout(A),A=setTimeout(()=>{g.value=1,C()},500)},ie=()=>{s.supplierId=void 0,s.platform===2&&j(),P()},de=()=>{M(),g.value=1,C()},re=t=>{g.value=t,C()},ce=()=>{g.value=1,C()},E=()=>{R.value=!1},ue=()=>{if(d.value.length>c.maxSelect){I.warning(`最多只能选择${c.maxSelect}个商品`);return}F("confirm",d.value),E()};return(t,e)=>{const o=_("el-input"),p=_("el-option"),h=_("el-select"),v=_("el-button"),S=_("el-table-column"),K=_("el-image"),pe=_("el-table"),me=_("el-pagination"),ve=_("el-dialog"),fe=ye("loading");return u(),U(ve,{modelValue:R.value,"onUpdate:modelValue":e[10]||(e[10]=l=>R.value=l),title:"选择商品",width:"1300px",height:"800px","before-close":E,"destroy-on-close":""},{footer:r(()=>[a("div",rt,[n(v,{onClick:E},{default:r(()=>e[25]||(e[25]=[b("取消")])),_:1,__:[25]}),n(v,{type:"primary",onClick:ue},{default:r(()=>e[26]||(e[26]=[b("确认")])),_:1,__:[26]})])]),default:r(()=>[a("div",Ce,[a("div",Re,[a("div",ze,[a("div",Ue,[a("div",Be,[n(o,{modelValue:s.spuId,"onUpdate:modelValue":e[0]||(e[0]=l=>s.spuId=l),placeholder:"请输入商品SPU ID",style:{width:"180px"},clearable:"",onInput:P},null,8,["modelValue"]),n(o,{modelValue:s.productName,"onUpdate:modelValue":e[1]||(e[1]=l=>s.productName=l),placeholder:"请输入商品名称",style:{width:"180px"},clearable:"",onInput:P},null,8,["modelValue"]),n(h,{modelValue:s.platform,"onUpdate:modelValue":e[2]||(e[2]=l=>s.platform=l),placeholder:"请选择平台",style:{width:"120px"},clearable:"",onChange:ie},{default:r(()=>[n(p,{label:"自由供应商",value:2}),n(p,{label:"平台供应商",value:1})]),_:1},8,["modelValue"]),s.platform===2?(u(),U(h,{key:0,modelValue:s.supplierId,"onUpdate:modelValue":e[3]||(e[3]=l=>s.supplierId=l),placeholder:"请选择供应商",style:{width:"150px"},clearable:"",onChange:P},{default:r(()=>[(u(!0),x(Q,null,J(w.value,l=>(u(),U(p,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])):B("",!0)]),a("div",Ne,[a("div",De,[a("div",{class:W(["custom-checkbox",{checked:V.value,indeterminate:T.value}]),onClick:ae},[a("div",Le,[V.value?(u(),x("svg",$e,e[11]||(e[11]=[a("path",{d:"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"},null,-1)]))):T.value?(u(),x("div",Ae)):B("",!0)])],2),e[12]||(e[12]=a("span",{class:"checkbox-label"},"全选",-1))]),a("div",Ee,[e[13]||(e[13]=a("span",null,"¥",-1)),n(o,{modelValue:s.minPrice,"onUpdate:modelValue":e[4]||(e[4]=l=>s.minPrice=l),placeholder:"最低价",style:{width:"80px"},type:"number",onInput:P},null,8,["modelValue"]),e[14]||(e[14]=a("span",{style:{margin:"0 4px"}},"—",-1)),e[15]||(e[15]=a("span",null,"¥",-1)),n(o,{modelValue:s.maxPrice,"onUpdate:modelValue":e[5]||(e[5]=l=>s.maxPrice=l),placeholder:"最高价",style:{width:"80px"},type:"number",onInput:P},null,8,["modelValue"])]),a("div",Fe,[n(o,{modelValue:s.minProfitRate,"onUpdate:modelValue":e[6]||(e[6]=l=>s.minProfitRate=l),placeholder:"最低利润率",style:{width:"80px"},type:"number",onInput:P},null,8,["modelValue"]),e[16]||(e[16]=a("span",{style:{margin:"0 4px"}},"%",-1)),e[17]||(e[17]=a("span",{style:{margin:"0 4px"}},"—",-1)),n(o,{modelValue:s.maxProfitRate,"onUpdate:modelValue":e[7]||(e[7]=l=>s.maxProfitRate=l),placeholder:"最高利润率",style:{width:"80px"},type:"number",onInput:P},null,8,["modelValue"]),e[18]||(e[18]=a("span",null,"%",-1))]),n(v,{onClick:de},{default:r(()=>e[19]||(e[19]=[b("重置")])),_:1,__:[19]})])])]),a("div",Oe,[Pe((u(),U(pe,{ref_key:"tableRef",ref:Z,data:f.value,style:{width:"100%"},height:"440",onSortChange:ne},{default:r(()=>[n(S,{label:"选择",width:"55",align:"center"},{default:r(({row:l})=>[a("div",{class:W(["custom-checkbox",{checked:H(l.id),disabled:!G(l)}]),onClick:ge=>ee(l)},[a("div",Me,[H(l.id)?(u(),x("svg",je,e[20]||(e[20]=[a("path",{d:"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"},null,-1)]))):B("",!0)])],10,Te)]),_:1}),n(S,{label:"商品名",width:"250"},{default:r(({row:l})=>[a("div",He,[a("div",Ge,[n(K,{src:l.mainImage||"/default-product.png","preview-src-list":[l.mainImage||"/default-product.png"],fit:"cover",style:{width:"30px",height:"30px"},"preview-teleported":!0},null,8,["src","preview-src-list"])]),a("div",Ke,[a("div",{class:"product-name",title:l.name},y(l.name),9,qe),l.brand?(u(),x("div",Je,y(l.brand),1)):B("",!0)])])]),_:1}),n(S,{prop:"spuCode",label:"SPU ID",width:"150","show-overflow-tooltip":"",sortable:"custom"}),n(S,{label:"供应商",width:"120",prop:"supplierName"},{default:r(({row:l})=>[b(y(l.supplierName||"京东快递"),1)]),_:1}),n(S,{label:"销售价(元)",width:"150",sortable:"custom",prop:"salePriceMin"},{default:r(({row:l})=>[b(y(l.salePriceDisplay||"0.00"),1)]),_:1}),n(S,{label:"利润率",width:"120",sortable:"custom",prop:"profitRate"},{default:r(({row:l})=>[b(y(l.profitRateDisplay||"0.00")+"% ",1)]),_:1}),n(S,{prop:"stock",label:"库存",width:"100",sortable:"custom"})]),_:1},8,["data"])),[[fe,L.value]])]),a("div",Qe,[n(me,{"current-page":g.value,"onUpdate:currentPage":e[8]||(e[8]=l=>g.value=l),"page-size":$.value,"onUpdate:pageSize":e[9]||(e[9]=l=>$.value=l),total:O.value,background:"",layout:"prev, pager, next, jumper, ->, total","page-sizes":[10,20,50,100],onSizeChange:ce,onCurrentChange:re},null,8,["current-page","page-size","total"])])]),a("div",We,[a("div",Xe,[e[21]||(e[21]=a("h4",null,"已选商品",-1)),a("span",Ye,y(d.value.length)+" / "+y(c.maxSelect===999999?"不限":c.maxSelect),1)]),a("div",Ze,[d.value.length===0?(u(),x("div",et,e[22]||(e[22]=[a("div",{class:"empty-icon"},"📦",-1),a("div",{class:"empty-text"},"暂未选择商品",-1)]))):(u(),x("div",tt,[(u(!0),x(Q,null,J(d.value,l=>(u(),x("div",{key:l.id,class:"selected-item"},[a("div",lt,[a("div",at,[n(K,{src:l.mainImage||"/default-product.png",fit:"cover",style:{width:"40px",height:"40px"}},null,8,["src"])]),a("div",ot,[a("div",{class:"selected-product-name",title:l.name},y(l.name),9,st),a("div",nt," ¥"+y(l.salePriceDisplay||"0.00"),1)])]),a("div",it,[n(v,{type:"danger",size:"small",link:"",onClick:ge=>oe(l.id)},{default:r(()=>e[23]||(e[23]=[b(" 删除 ")])),_:2,__:[23]},1032,["onClick"])])]))),128))]))]),a("div",dt,[d.value.length>0?(u(),U(v,{key:0,type:"danger",plain:"",size:"small",onClick:se},{default:r(()=>e[24]||(e[24]=[b(" 清空所有 ")])),_:1,__:[24]})):B("",!0)])])])]),_:1},8,["modelValue"])}}});const mt=Ie(ct,[["__scopeId","data-v-89ac1f75"]]);export{mt as P,Ve as g};
