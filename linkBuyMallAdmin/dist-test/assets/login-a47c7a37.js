import{d as S,_ as I,o as p,e as A,f as s,u as k,v as F,r as v,x as N,W as Q,s as Z,X as K,p as T,E as h,b as f,P as J,B,Y as U,n as c,j as E,k as y,h as q,Z as x,y as P,t as O}from"./index-5502c85d.js";const _=S({name:"loginHeader",props:{},components:{},setup(){return{}}}),W="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAgGBgcGBQgHBwcJCQgKDBQNDAsLDBkSEw8UHRofHh0aHBwgJC4nICIsIxwcKDcpLDAxNDQ0Hyc5PTgyPC4zNDL/2wBDAQgJCQwLDBgNDRgyIRwhMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjL/wAARCAAyADIDASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwDySxvedjjHqP5//q/EVeVPKlyD+7cYz7dvy/kay/swZ8xsSR/Cww6/h3rStJNy+VJ+Brmmluj18POWkJ9NmWsbLuKTpu+U/n/9etPO0EnsKoGNjER/EhzVu6JVFAHU5rmlrY9Wm+VSYvmZPNPRS7YHU9T/AJ/z2qpESx9qvxuEXFRLTY6Kb5tx/koP43/7+AfpRR5p9T+ZorPU6PcMXw94ck1mwM7XYiXzTDGpjDfNgcnkbRudFyM/erfbwYsepWlguqobidpAVni8vaEXJYEsdwJyoz6H0rD8K+IG0Wzvtl9dRTiMvawo58l5CNp3qOpAOR2yuDXU3Hi7Tp9Wsnlv55LWKeecs0bMY1ZNiRqD9C3YfNXqSSPj6cpC2nhaf+yp724urSOOKMOv70MXz0HB69PzqG90lYjpxluAI7uBZdwQnYCzLjA6/d7Vrp4v0q88PXdnJM7sbQRxRywMA0oCKGB3sBgJ6AZOaqz3OmTaRpLpfr9otLdYWt2ifcSJGJw2MdGrinGzuj2KFaUtJbf8DQwrjS7mO5uYrOGa5W2H76SOBwE9cgjIA56gdKj+yXMccErxMUnjMsZUZ3IGKk/TIP5V2EnibTk1i2miuZooxrct5KVDKHiJTBPrwG49z61Fc6tplx4aj0uGQ28iwt++RSC5812ELf7JDAjHGevtTUS4Sq3Whym8+h/I/wCNFO+z+4orG6PQtM4NH28dfapRuc/MenYdv/r0xVCHapBf+Juy1JkRxbxwTwn9TXqM+NjfqW7a6ZbhI85UHn+tXbe/aSE/WseBtjk/3Vp9tJsXHrWcoJnTTrONjchuw58tz83YnvVlJtpwCVPpXONLu4B+b+H61ds70XKiOThx0PrWM6fVHZRxV3yvc3PNb/Z/I/8AxNFUNsnY5/4ADRWPKd31iXY5tP8Aj3+pGfzqS5/16DtsWiivQ6nzi+H7iJejfjSj7v8An1oopiB/vP8AQ/zNOYlbtypwQxIx9aKKQdfmdCVXceB19KKKK4j3rH//2Q==";const Y={class:"login-header"};function X(o,e,i,d,l,u){return p(),A("div",Y,e[0]||(e[0]=[s("div",{class:"left"},[s("img",{src:W,class:"service-icon"}),s("span",{class:"nav-header-logo"},"邻域业务管理平台")],-1)]))}const G=I(_,[["render",X],["__scopeId","data-v-f730229d"]]),z=S({name:"login",components:{LoginHeader:G},setup(){const o=k(),e=F(),i=v(""),d=v(""),l=v(""),u=v(""),w=v(!1),m=v(!1),n=e.getCompanyName();N(()=>{const r=localStorage.getItem("rememberedLogin");if(r){const{savedPhone:t,savedPassword:a}=JSON.parse(r);i.value=t,l.value=a,w.value=!0}});const g={},R=10,V=5*60*1e3,H=5*60*1e3,L=r=>{const t=Date.now(),a=g[r];if(!a)return g[r]={count:0,timestamp:t},!0;if(a.lockUntil&&t<a.lockUntil){const D=Math.ceil((a.lockUntil-t)/6e4);return u.value="pwdRequired",h.error(`操作过于频繁，请${D}分钟后再试`),!1}return t-a.timestamp>H?(g[r]={count:0,timestamp:t},!0):(a.count++,a.count>=R?(a.lockUntil=t+V,u.value="pwdRequired",h.error("操作过于频繁，请5分钟后再试"),!1):!0)},b=r=>/^1[3-9]\d{9}$/.test(r),j={phoneRequired:"手机号不能为空",invalidPhone:"手机号格式不正确",phoneNotRegistered:"手机号未注册",invalidPassword:"密码格式不正确",pwdRequired:"密码不能为空",pwdLength:"密码长度必须在8-20位之间"},M=async()=>{if(d.value="",!i.value){d.value="phoneRequired";return}if(!b(i.value)){d.value="invalidPhone";return}},C=()=>{if(u.value="",!l.value){u.value="pwdRequired";return}if(l.value.length<6||l.value.length>20){u.value="pwdLength";return}};return{companyName:n,router:o,phone:i,password:l,rememberMe:w,showPassword:m,handleLogin:async()=>{if(C(),M(),u.value||d.value||!L(i.value))return;const r=await Q({mobile:i.value,password:l.value});if(r.code===200){const t=Z();K(r.data.token,t||void 0),w.value?localStorage.setItem("rememberedLogin",JSON.stringify({savedPhone:i.value,savedPassword:l.value})):localStorage.removeItem("rememberedLogin"),e.setUserInfo(r.data,t||void 0),await T().fetchMenu(),t?o.push({path:"/",query:{scode:t}}):o.push("/")}},phoneError:d,passwordError:u,keyForErrorMsg:j,validatePhone:M,validatePassword:C}}});const $={class:"all login-bg"},ee={class:"login-band"},se={class:"login-container"},oe={class:"login-box"},ne={class:"form-group"},te={class:"input-wrapper"},re={key:0,class:c(["error-message","not-registered"])},ae=["innerHTML"],ie={class:"password-header"},le={class:"input-wrapper"},ue=["type"],de={width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},pe={key:0,d:"M12 4.5C7 4.5 2.73 7.61 1 12C2.73 16.39 7 19.5 12 19.5S21.27 16.39 23 12C21.27 7.61 17 4.5 12 4.5ZM12 17C9.24 17 7 14.76 7 12S9.24 7 12 7S17 9.24 17 12S14.76 17 12 17ZM12 9C10.34 9 9 10.34 9 12S10.34 15 12 15S15 13.66 15 12S13.66 9 12 9Z",fill:"#999"},Ae={key:1,d:"M12 7C9.24 7 7 9.24 7 12C7 12.65 7.13 13.26 7.36 13.82L9.13 12.05C9.13 12.03 9.12 12.02 9.12 12C9.12 10.34 10.46 9 12.12 9C12.14 9 12.15 9.01 12.17 9.01L13.94 7.24C13.32 7.09 12.67 7 12 7ZM2.01 3.87L4.69 6.55C3.06 7.83 1.77 9.53 1 12C2.73 16.39 7 19.5 12 19.5C13.52 19.5 14.97 19.13 16.31 18.45L18.42 20.56L19.73 19.25L3.42 2.94L2.01 3.87ZM7.53 9.8L9.08 11.35C9.03 11.56 9 11.78 9 12C9 13.66 10.34 15 12 15C12.22 15 12.44 14.97 12.65 14.92L14.2 16.47C13.53 16.8 12.79 17 12 17C9.24 17 7 14.76 7 12C7 11.21 7.2 10.47 7.53 9.8ZM11.84 9L13 10.16C12.67 10.06 12.34 10 12 10C10.34 10 9 11.34 9 13C9 13.34 9.06 13.67 9.16 14L10.84 12.32C10.84 12.21 10.84 12.11 10.84 12C10.84 10.34 12.18 9 13.84 9C13.95 9 14.05 9 14.16 9L11.84 9Z",fill:"#999"},ve={key:0,class:"error-message invalid-password"},we=["innerHTML"],me={class:"remember-me"},ge={class:"supplier-name"};function ce(o,e,i,d,l,u){const w=f("router-link"),m=f("el-checkbox");return p(),A("div",$,[s("div",ee,[e[15]||(e[15]=s("div",{class:"login-text"},[s("p",null,"商城管理平台"),s("span",{class:"m-t-43"},"化繁为简 · 高效工作")],-1)),s("div",se,[s("div",oe,[e[14]||(e[14]=s("div",null,[s("h2",{class:"login-title"},"欢迎登录系统")],-1)),s("form",{class:"login-form",onSubmit:e[6]||(e[6]=J((...n)=>o.handleLogin&&o.handleLogin(...n),["prevent"]))},[s("div",ne,[s("div",null,[s("div",te,[e[7]||(e[7]=s("div",{class:"input-icon"},[s("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[s("path",{d:"M17 2H7C5.89 2 5 2.9 5 4V20C5 21.1 5.89 22 7 22H17C18.1 22 19 21.1 19 20V4C19 2.9 18.1 2 17 2ZM17 18H7V6H17V18ZM9 7H15V8H9V7ZM9 9H15V10H9V9ZM9 11H13V12H9V11Z",fill:"#999"})])],-1)),B(s("input",{id:"phone","onUpdate:modelValue":e[0]||(e[0]=n=>o.phone=n),type:"tel",class:c(["input-field",{error:o.phoneError}]),placeholder:"请输入管理员手机号",onBlur:e[1]||(e[1]=(...n)=>o.validatePhone&&o.validatePhone(...n))},null,34),[[U,o.phone]])]),o.phoneError?(p(),A("div",re,[s("span",{innerHTML:o.keyForErrorMsg[o.phoneError]},null,8,ae)])):E("",!0)]),s("div",null,[s("div",ie,[e[9]||(e[9]=s("label",{for:"password",class:"input-label"},null,-1)),y(w,{class:"forgot-password",to:"/resetPassword"},{default:q(()=>e[8]||(e[8]=[P("忘记密码？")])),_:1,__:[8]})]),s("div",le,[e[10]||(e[10]=s("div",{class:"input-icon"},[s("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[s("path",{d:"M18 8H17V6C17 3.24 14.76 1 12 1S7 3.24 7 6V8H6C4.9 8 4 8.9 4 10V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V10C20 8.9 19.1 8 18 8ZM12 17C10.9 17 10 16.1 10 15S10.9 13 12 13S14 13.9 14 15S13.1 17 12 17ZM15.1 8H8.9V6C8.9 4.29 10.29 2.9 12 2.9S15.1 4.29 15.1 6V8Z",fill:"#999"})])],-1)),B(s("input",{id:"password","onUpdate:modelValue":e[2]||(e[2]=n=>o.password=n),type:o.showPassword?"text":"password",class:c(["input-field",{error:o.passwordError}]),placeholder:"请输入密码",onBlur:e[3]||(e[3]=(...n)=>o.validatePassword&&o.validatePassword(...n))},null,42,ue),[[x,o.password]]),s("div",{class:"password-toggle",onClick:e[4]||(e[4]=n=>o.showPassword=!o.showPassword)},[(p(),A("svg",de,[o.showPassword?(p(),A("path",Ae)):(p(),A("path",pe))]))])]),o.passwordError?(p(),A("div",ve,[s("span",{innerHTML:o.keyForErrorMsg[o.passwordError]},null,8,we)])):E("",!0)]),s("div",me,[y(m,{modelValue:o.rememberMe,"onUpdate:modelValue":e[5]||(e[5]=n=>o.rememberMe=n),size:"large"},null,8,["modelValue"]),e[11]||(e[11]=P("  ")),e[12]||(e[12]=s("span",null,"记住账号",-1))])]),e[13]||(e[13]=s("button",{type:"submit",class:"login-button"}," 登录 ",-1))],32),s("div",ge,O(o.companyName),1)])])])])}const he=I(z,[["render",ce],["__scopeId","data-v-b395b4e3"]]);export{he as default};
