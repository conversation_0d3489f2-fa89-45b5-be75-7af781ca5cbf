import{d as h,u as S,r as i,z as q,b as u,o as B,e as U,k as o,h as l,f as w,P as k,y as _,t as I,E as g,_ as N}from"./index-5502c85d.js";const R={class:"reset-password-container"},$={class:"verification-code-container"},F=h({__name:"index",setup(M){const y=S(),d=i(),p=i(!1),f=i(!1),m=i(60),c=i("发送验证码"),a=q({email:"",verificationCode:"",newPassword:"",confirmPassword:""}),P={email:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],verificationCode:[{required:!0,message:"请输入验证码",trigger:"blur"},{min:6,max:6,message:"验证码长度应为6位",trigger:"blur"}],newPassword:[{required:!0,validator:(s,e,r)=>{var t;e===""?r(new Error("请输入密码")):(a.confirmPassword!==""&&((t=d.value)==null||t.validateField("confirmPassword")),r())},trigger:"blur"},{min:6,message:"密码长度不能小于6位",trigger:"blur"}],confirmPassword:[{required:!0,validator:(s,e,r)=>{e===""?r(new Error("请再次输入密码")):e!==a.newPassword?r(new Error("两次输入密码不一致!")):r()},trigger:"blur"}]},b=()=>{f.value=!0,m.value=60;const s=setInterval(()=>{m.value--,c.value=`${m.value}秒后重试`,m.value<=0&&(clearInterval(s),f.value=!1,c.value="发送验证码")},1e3)},V=async()=>{var s;try{await((s=d.value)==null?void 0:s.validateField("email")),g.success("验证码已发送到您的邮箱（模拟）"),b()}catch{}},C=async()=>{if(d.value)try{await d.value.validate(),p.value=!0,g.success("密码重置成功，请使用新密码登录（模拟）"),y.push("/login")}catch{}finally{p.value=!1}};return(s,e)=>{const r=u("el-input"),t=u("el-form-item"),v=u("el-button"),x=u("el-form"),E=u("el-card");return B(),U("div",R,[o(E,{class:"reset-password-card"},{header:l(()=>e[5]||(e[5]=[w("h2",null,"重置密码",-1)])),default:l(()=>[o(x,{ref_key:"formRef",ref:d,model:a,rules:P,"label-width":"100px",onSubmit:k(C,["prevent"])},{default:l(()=>[o(t,{label:"邮箱",prop:"email"},{default:l(()=>[o(r,{modelValue:a.email,"onUpdate:modelValue":e[0]||(e[0]=n=>a.email=n),placeholder:"请输入注册邮箱"},null,8,["modelValue"])]),_:1}),o(t,{label:"验证码",prop:"verificationCode"},{default:l(()=>[w("div",$,[o(r,{modelValue:a.verificationCode,"onUpdate:modelValue":e[1]||(e[1]=n=>a.verificationCode=n),placeholder:"请输入验证码"},null,8,["modelValue"]),o(v,{type:"primary",disabled:f.value,onClick:V},{default:l(()=>[_(I(c.value),1)]),_:1},8,["disabled"])])]),_:1}),o(t,{label:"新密码",prop:"newPassword"},{default:l(()=>[o(r,{modelValue:a.newPassword,"onUpdate:modelValue":e[2]||(e[2]=n=>a.newPassword=n),type:"password",placeholder:"请输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),o(t,{label:"确认密码",prop:"confirmPassword"},{default:l(()=>[o(r,{modelValue:a.confirmPassword,"onUpdate:modelValue":e[3]||(e[3]=n=>a.confirmPassword=n),type:"password",placeholder:"请再次输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),o(t,null,{default:l(()=>[o(v,{type:"primary","native-type":"submit",loading:p.value},{default:l(()=>e[6]||(e[6]=[_(" 重置密码 ")])),_:1,__:[6]},8,["loading"]),o(v,{onClick:e[4]||(e[4]=n=>s.$router.push("/login"))},{default:l(()=>e[7]||(e[7]=[_("返回登录")])),_:1,__:[7]})]),_:1})]),_:1},8,["model"])]),_:1})])}}});const j=N(F,[["__scopeId","data-v-c0ce544c"]]);export{j as default};
