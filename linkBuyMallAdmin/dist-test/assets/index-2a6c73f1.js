import{d as w,a1 as M,a2 as U,a3 as A,u as I,r as F,v as C,c as g,x as S,a4 as P,a5 as E,s as $,_ as N,b as u,o as p,e as v,k as c,h as i,f as s,t as h,y,a6 as V,p as D,F as R,m as b,n as q,g as k,i as H,j as O}from"./index-5502c85d.js";const T=w({name:"user-info",components:{User:M,SwitchButton:U,OfficeBuilding:A},setup(){const e=I(),r=F(""),l=async()=>{const t=await P(()=>import("./avatar-75db6e00.js"),[]);r.value=t.default},n=C(),f=g(()=>n.getUserInfo()),m={},d={};return S(async()=>{l()}),{avatar:r,userInfo:f,company:m,selectCompany:d,handleLogout:()=>{E()},goHome:()=>{const t=$();t?e.push({path:"/",query:{scode:t}}):e.push("/")},goAccountSettings:()=>{const t=$();t?e.push({path:"/account/edit-profile",query:{scode:t}}):e.push("/account/edit-profile")}}}});const j={class:"user-info"},z={class:"el-dropdown-link"},J=["src"],W={class:"username"},G={class:"email"},K={class:"operate"},Q={class:"operate"};function X(e,r,l,n,f,m){const d=u("el-divider"),_=u("User"),o=u("el-icon"),a=u("el-dropdown-item"),t=u("SwitchButton"),B=u("el-dropdown-menu"),L=u("el-dropdown");return p(),v("div",j,[c(d,{direction:"vertical"}),c(L,null,{dropdown:i(()=>[c(B,{class:"custom-dropdown-menu",style:{width:"250px!important"}},{default:i(()=>[s("p",W,h(e.userInfo&&e.userInfo.firstName)+" "+h(e.userInfo&&e.userInfo.lastName),1),s("p",G,h(e.userInfo&&e.userInfo.email),1),c(a,{onClick:e.goAccountSettings},{default:i(()=>[s("span",K,[c(o,null,{default:i(()=>[c(_)]),_:1}),r[0]||(r[0]=y(" 账户信息"))])]),_:1},8,["onClick"]),c(a,{onClick:e.handleLogout},{default:i(()=>[s("span",Q,[c(o,null,{default:i(()=>[c(t)]),_:1}),r[1]||(r[1]=y(" 退出登录"))])]),_:1},8,["onClick"])]),_:1})]),default:i(()=>[s("span",z,[s("img",{class:"user-avatar",src:e.avatar,alt:"Profile Picture"},null,8,J)])]),_:1})])}const Y=N(T,[["render",X],["__scopeId","data-v-58ed75e5"]]),Z=C(),x=Z.getCompanyName(),ee=w({name:"nav-header",components:{UserInfo:Y,ElIcon:V},props:{topMenus:{type:Array,required:!0,default:()=>[]}},emits:["foldChange"],setup(e,{emit:r}){const l=I(),n=D(),f=C(),m=g(()=>f.getUserInfo()),d=g(()=>n.isLoaded?e.topMenus:[]),_=o=>{if(o==="0")n.setCurrentRoutePath("/"),l.push({path:"/"});else{const a=e.topMenus.find(t=>t.key===o);a&&(n.setCurrentRoutePath(a.route),a.route.endsWith("noJump")||a.route&&l.push({path:a.route}))}};return S(async()=>{n.isLoaded||await n.fetchMenu()}),{menuStore:n,companyName:x,menuList:d,userInfo:m,handleMenuClick:_}}});const oe={class:"nav-header"},te={class:"content"},se={class:"left"},ne={class:"nav-header-logo"},ae={class:"center-menu"},ce=["onClick"],re={class:"right"};function ue(e,r,l,n,f,m){const d=u("el-icon"),_=u("user-info");return p(),v("div",oe,[s("div",te,[s("div",se,[s("span",ne,h(e.companyName),1)]),s("div",ae,[(p(!0),v(R,null,b(e.menuList,o=>(p(),v("span",{key:o.key,class:q(["menu-item",{active:o.isSelected}]),onClick:a=>e.handleMenuClick(o.key)},[o.icon?(p(),k(d,{key:0,class:"menu-icon"},{default:i(()=>[(p(),k(H(o.icon)))]),_:2},1024)):O("",!0),y(" "+h(o.name),1)],10,ce))),128))]),s("div",re,[c(_)])])])}const de=N(ee,[["render",ue],["__scopeId","data-v-234f3d00"]]);export{de as N};
