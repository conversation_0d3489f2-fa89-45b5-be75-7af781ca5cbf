import{_ as i,aj as c,b as n,o as p,e as u,f as e,k as s,h as o,y as m,t as f}from"./index-5502c85d.js";const g={name:"UserSettings",components:{Setting:c},computed:{mallId(){return this.$route.query.mallId||this.$route.params.mallId}}},v={class:"user-settings"},x={class:"content-area"},y={class:"card-header"},I={class:"user-settings-content"};function S($,t,h,b,k,l){const a=n("Setting"),_=n("el-icon"),d=n("el-button"),r=n("el-card");return p(),u("div",v,[t[3]||(t[3]=e("div",{class:"page-header"},[e("h2",null,"用户设置"),e("p",null,"用户权限和功能设置")],-1)),e("div",x,[s(r,null,{default:o(()=>[e("div",y,[t[1]||(t[1]=e("span",null,"用户权限设置",-1)),s(d,{type:"primary"},{default:o(()=>[s(_,null,{default:o(()=>[s(a)]),_:1}),t[0]||(t[0]=m(" 保存设置 "))]),_:1,__:[0]})]),e("div",I,[t[2]||(t[2]=e("p",null,"用户设置功能正在开发中...",-1)),e("p",null,"当前商城ID: "+f(l.mallId),1)])]),_:1})])])}const N=i(g,[["render",S],["__scopeId","data-v-216e548f"]]);export{N as default};
