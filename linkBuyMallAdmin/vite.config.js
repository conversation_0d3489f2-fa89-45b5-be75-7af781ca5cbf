import { fileURLToPath, URL } from 'node:url'

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import path from 'path'
import commonjs from '@rollup/plugin-commonjs'
import { viteMockServe } from 'vite-plugin-mock'

// https://vitejs.dev/config/
export default ({ mode }) => {
  const env = loadEnv(mode, process.cwd())
  console.log('import.meta.env', env)
  const outputDir = () => {
    return env.VITE_APP_ENV === 'production'
      ? 'dist'
      : 'dist-' + env.VITE_APP_ENV
  }
  return defineConfig({
    plugins: [
      commonjs(),
      vue(),
      createSvgIconsPlugin({
        // Specify the icon folder to be cached
        iconDirs: [path.resolve(process.cwd(), 'src/assets/icons/svg')],
        // Specify symbolId format
        symbolId: 'icon-[name]',
        /**
         * custom insert position
         * @default: body-last
         */
        inject: 'body-last', //body-last|body-first默认body-last
        /**
         * custom dom id
         * @default: __svg__icons__dom__
         */
        customDomId: '__svg__icons__dom__'
      }),
      // viteMockServe({
      //   mockPath: 'mock', // mock 文件目录
      //   localEnabled: true, // 仅本地开发启用
      //   prodEnabled: false, // 生产环境关闭
      //   injectCode: `
      //     import { setupProdMockServer } from '../mock/_createProductionServer';
      //     setupProdMockServer();
      //   `
      // })
    ],
    build: {
      outDir: outputDir()
    },
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    // devServer
    server: {
      port: 3002,
      host: 'dev.mob.com',
      proxy: {
        '/api': {
          target: 'http://127.0.0.1:8082',
          changeOrigin: true,
          rewrite: path => path.replace(/^\/api/, '')
        }
      }
    }
  })
}
