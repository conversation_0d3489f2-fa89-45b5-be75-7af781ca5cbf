// import { Mock<PERSON>ethod } from 'vite-plugin-mock'

// export default [
//   {
//     url: '/api/workbench/createApplication',
//     method: 'post',
//     response: ({ body }) => {
//       return {
//         status: 200,
//         res: true,
//         message: 'success'
//       }
//     }
//   },
//   {
//     url: '/api/workbench/applicationList',
//     method: 'get',
//     response: ({ query }) => {
//       const mockApps = [
//         {
//           id: '1',
//           appName: 'Snapchat',
//           icon: 'https://img.icons8.com/color/48/000000/snapchat.png',
//           appkey: '3ad0aa58e5785',
//           plat: ['ANDROID', 'IOS'],
//           pkg: 'com.snapchat.android',
//           bundle: 'com.snapchat.app',
//           serverNode: 'US',
//           products: [],
//           status: 1
//         },
//         {
//           id: '2',
//           appName: 'Telegram',
//           icon: 'https://img.icons8.com/color/48/000000/telegram-app.png',
//           appkey: '3ad0aa58e5786',
//           plat: ['ANDROID', 'IOS'],
//           pkg: 'org.telegram.messenger',
//           bundle: 'org.telegram.Telegram',
//           serverNode: 'EU',
//           products: ['2dizq0hlx7sgdnmrk', '2dizq0hlx8hm5u4n4'],
//           status: 1
//         },
//         {
//           id: '3',
//           appName: 'TikTok',
//           icon: 'https://img.icons8.com/color/48/000000/tiktok--v1.png',
//           appkey: '3ad0aa58e5787',
//           plat: ['ANDROID', 'IOS'],
//           pkg: 'com.zhiliaoapp.musically',
//           bundle: 'com.zhiliaoapp.musically',
//           serverNode: 'ASIA',
//           products: ['2dizq0hlx7sgdnmrk', '2dizq0hlx8hm5u4n4'],
//           status: 1
//         },
//         {
//           id: '4',
//           appName: 'Flexcil Notes & PDF',
//           icon: 'https://img.icons8.com/color/48/000000/notepad.png',
//           appkey: '3ad0aa58e5788',
//           plat: ['ANDROID', 'IOS'],
//           pkg: 'com.flexcil.flexcilnote',
//           bundle: 'com.flexcil.flexcilnote',
//           serverNode: 'US',
//           products: ['2dizq0hlx7sgdnmrk'],
//           status: 1
//         }
//       ]

//       return {
//         status: 200,
//         res: {
//           list: mockApps,
//           total: mockApps.length,
//           pageNum: Number(query.pageNum) || 1,
//           pageSize: Number(query.pageSize) || 10
//         },
//         message: 'success'
//       }
//     }
//   },
//   {
//     url: '/api/workbench/applicationDetail',
//     method: 'get',
//     response: ({ query }) => {
//       const id = query.id
//       const mockApps = {
//         '1': {
//           id: '1',
//           appName: 'Snapchat',
//           icon: 'https://img.icons8.com/color/48/000000/snapchat.png',
//           appkey: '3ad0aa58e5785',
//           plat: ['ANDROID', 'IOS'],
//           pkg: 'com.snapchat.android',
//           bundle: 'com.snapchat.app',
//           serverNode: 'US',
//           products: [],
//           status: 1
//         },
//         '2': {
//           id: '2',
//           appName: 'Telegram',
//           icon: 'https://img.icons8.com/color/48/000000/telegram-app.png',
//           appkey: '3ad0aa58e5786',
//           plat: ['ANDROID', 'IOS'],
//           pkg: 'org.telegram.messenger',
//           bundle: 'org.telegram.Telegram',
//           serverNode: 'EU',
//           products: ['2dizq0hlx7sgdnmrk', '2dizq0hlx8hm5u4n4'],
//           status: 1
//         },
//         '3': {
//           id: '3',
//           appName: 'TikTok',
//           icon: 'https://img.icons8.com/color/48/000000/tiktok--v1.png',
//           appkey: '3ad0aa58e5787',
//           plat: ['ANDROID', 'IOS'],
//           pkg: 'com.zhiliaoapp.musically',
//           bundle: 'com.zhiliaoapp.musically',
//           serverNode: 'ASIA',
//           products: ['2dizq0hlx7sgdnmrk', '2dizq0hlx8hm5u4n4'],
//           status: 1
//         },
//         '4': {
//           id: '4',
//           appName: 'Flexcil Notes & PDF',
//           icon: 'https://img.icons8.com/color/48/000000/notepad.png',
//           appkey: '3ad0aa58e5788',
//           plat: ['ANDROID', 'IOS'],
//           pkg: 'com.flexcil.flexcilnote',
//           bundle: 'com.flexcil.flexcilnote',
//           serverNode: 'US',
//           products: ['2dizq0hlx7sgdnmrk'],
//           status: 1
//         }
//       }

//       return {
//         status: 200,
//         res: mockApps[id] || null,
//         message: 'success'
//       }
//     }
//   },
//   {
//     url: '/api/workbench/updateApp',
//     method: 'post',
//     response: ({ body }) => {
//       return {
//         status: 200,
//         res: {
//           ...body,
//           status: 1
//         },
//         message: 'success'
//       }
//     }
//   }
// ] as MockMethod[]