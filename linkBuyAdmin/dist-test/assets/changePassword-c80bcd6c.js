import{a as v,r as m,aU as c,z as C,_,b as l,c as g,e as B,k as P,l as a,w as r,ap as b,v as y}from"./views-biz-72c05ff0.js";const V=v({name:"ChangePassword",setup(){const e=m({oldPassword:"",newPassword:"",newPasswordConfirm:""}),o=m({oldPassword:"",newPassword:"",newPasswordConfirm:""}),i={oldPwdRequired:"请输入原密码",valueRequired:"请输入新密码",pwdLength:"密码长度必须在6-20位之间",newPwdSame:"新密码不能与原密码相同",newPwdNotSame:"两次输入的新密码不一致"},w=()=>{e.value.oldPassword?o.value.oldPassword="":o.value.oldPassword="oldPwdRequired"},t=()=>{const s=e.value.newPassword;s?s.length<6||s.length>20?o.value.newPassword="pwdLength":s===e.value.oldPassword?o.value.newPassword="newPwdSame":e.value.newPasswordConfirm!==""&&e.value.newPasswordConfirm!==e.value.newPassword?o.value.newPasswordConfirm="newPwdNotSame":(o.value.newPassword="",o.value.newPasswordConfirm=""):o.value.newPassword="valueRequired"},u=()=>{e.value.newPassword!==e.value.newPasswordConfirm?o.value.newPasswordConfirm="newPwdNotSame":o.value.newPasswordConfirm=""};return{formData:e,errors:o,keyForErrorMsg:i,validateOldPassword:w,validateNewPassword:t,validateNewPasswordConfirm:u,handleOk:async()=>{if(w(),t(),u(),o.value.oldPassword||o.value.newPassword||o.value.newPasswordConfirm)return;(await c({oldPassword:e.value.oldPassword,newPassword:e.value.newPassword})).code===200&&C.success("密码修改成功")}}}});const k={class:"account-wrapper account-change-password"},E={class:"account-wrapper-inner"};function N(e,o,i,w,t,u){const d=l("el-input"),s=l("el-form-item"),p=l("el-button"),f=l("el-form");return g(),B("div",k,[o[4]||(o[4]=P("p",{class:"account-wrapper-title"},"修改密码",-1)),P("div",E,[a(f,{onSubmit:b(e.handleOk,["prevent"])},{default:r(()=>[a(s,{label:"原密码","label-position":"top",error:e.keyForErrorMsg[e.errors.oldPassword]},{default:r(()=>[a(d,{modelValue:e.formData.oldPassword,"onUpdate:modelValue":o[0]||(o[0]=n=>e.formData.oldPassword=n),type:"password"},null,8,["modelValue"])]),_:1},8,["error"]),a(s,{label:"新密码","label-position":"top",error:e.keyForErrorMsg[e.errors.newPassword]},{default:r(()=>[a(d,{modelValue:e.formData.newPassword,"onUpdate:modelValue":o[1]||(o[1]=n=>e.formData.newPassword=n),type:"password",onBlur:e.validateNewPassword},null,8,["modelValue","onBlur"])]),_:1},8,["error"]),a(s,{label:"确认新密码","label-position":"top",error:e.keyForErrorMsg[e.errors.newPasswordConfirm]},{default:r(()=>[a(d,{modelValue:e.formData.newPasswordConfirm,"onUpdate:modelValue":o[2]||(o[2]=n=>e.formData.newPasswordConfirm=n),type:"password",onBlur:e.validateNewPasswordConfirm},null,8,["modelValue","onBlur"])]),_:1},8,["error"]),a(s,null,{default:r(()=>[a(p,{type:"success","native-type":"submit",class:"save-button"},{default:r(()=>o[3]||(o[3]=[y("确定")])),_:1,__:[3]})]),_:1})]),_:1},8,["onSubmit"])])])}const D=_(V,[["render",N],["__scopeId","data-v-4098775e"]]);export{D as default};
