import{$ as B,_ as ee,X as le,r as w,D as te,o as ae,b as d,c as h,e as S,l as t,w as o,a8 as oe,F as re,z as p,k as u,Y as V,a7 as F,v as L,f as N,ao as ne,t as se,ap as O,aq as D,h as ie,ah as ue}from"./views-biz-72c05ff0.js";function ce(v="product"){return B.get({url:`/system/${v}/allCategory`})}function de(v,_="product"){return B.post({url:`/system/${_}/category/save`,data:v})}function pe(v,_="product"){return B.post({url:`/system/${_}/category/structure`,data:v})}function ge(v,_="product"){return B.delete({url:`/system/${_}/category/${v}`})}const me={class:"tree-header"},fe={class:"custom-tree-node"},ve={class:"node-content"},_e={class:"node-actions"},ye={class:"image-upload-container"},he=["src"],ke={key:1,class:"image-placeholder"},be={key:2,class:"image-actions"},Ce={key:1,class:"empty-tip"},xe={__name:"index",setup(v){const _=le(),y=w(String(_.query.categoryType||"product"));console.log("categoryType.value",y.value),te(()=>_.query.categoryType,l=>{l&&l!==y.value&&(y.value=String(l),console.log("categoryType changed to:",y.value),a.value=null,k())},{immediate:!1});const z={children:"children",label:"name"},T=w([]),a=w(null),E=w(null),g=w(null),$=w(!1),A={name:[{required:!0,message:"请输入分类名称",trigger:"blur"},{min:2,max:20,message:"长度在 2 到 20 个字符",trigger:"blur"}],code:[{required:!0,message:"请输入分类编码",trigger:"blur"},{pattern:/^[a-zA-Z0-9_-]+$/,message:"编码只能包含字母、数字、下划线和横线",trigger:"blur"}],image:[{required:!0,message:"请选择分类图片",trigger:"change"},{validator:(l,e,s)=>{!e||e.trim()===""?s(new Error("请选择分类图片")):s()},trigger:"change"}],sortOrder:[{required:!0,message:"请输入排序号",trigger:"blur"}]},k=async()=>{const l=await ce(y.value);l.code===200&&(T.value=l.data)},M=()=>{a.value={name:"",code:"",image:"",description:"",sortOrder:0,parentId:null}},P=l=>{if((m=>{let I=1,f=m;const C=r=>{for(const i of T.value){if(i.id===r)return i;if(i.children){const n=b(i.children,r);if(n)return n}}return null},b=(r,i)=>{for(const n of r){if(n.id===i)return n;if(n.children){const x=b(n.children,i);if(x)return x}}return null};for(;f;){const r=C(f);if(r&&r.parentId)I++,f=r.parentId;else break}return I})(l.id)>=3){p.error("分类最多支持三级");return}a.value={name:"",code:"",image:"",description:"",sortOrder:0,parentId:l.id}},R=l=>{a.value={...l}},G=()=>{var l;console.log("打开图片选择器，当前图片:",(l=a.value)==null?void 0:l.image),$.value=!0},j=l=>{if(console.log("选择的图片:",l),!l){p.warning("未选择任何图片");return}let e="";typeof l=="string"?e=l:typeof l=="object"&&(e=l.url||l.path||l.src||l.link),e?(a.value.image=e,g.value&&g.value.validateField("image")):(p.error("无法获取图片URL，请重新选择"),console.error("图片数据格式异常:",l))},J=()=>{console.log("取消选择图片"),$.value=!1},K=()=>{a.value.image="",p.info("已移除图片"),g.value&&g.value.validateField("image")},X=async()=>{g.value&&await g.value.validate(async l=>{if(l){if(!a.value.image){p.error("请选择分类图片");return}console.log("保存分类:",a.value);const e=await de(a.value,y.value);e.code===200&&(p.success("保存成功"),e.data&&(a.value={...e.data}),k())}else p.error("请完善分类信息")})},Y=()=>{g.value&&g.value.resetFields()},Z=async(l,e,s,m)=>{if(console.log("拖拽节点:",l.data),console.log("目标节点:",e?e.data:"root"),console.log("拖拽类型:",s),s==="inner"&&(r=>{let i=1,n=r;for(;n.parent&&n.parent.key!==void 0;)i++,n=n.parent;return i})(e)>=3){p.error("分类最多支持三级"),k();return}const f=[],C=(r,i=null)=>{r.forEach((n,x)=>{f.push({id:n.id,parentId:i,sortOrder:x}),n.children&&n.children.length>0&&C(n.children,n.id)})};C(T.value),console.log("更新的分类结构:",f),(await pe(f,y.value)).code===200&&p.success("分类结构更新成功"),k()},H=async l=>{var s;await ue.confirm("确定要删除该分类吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),(await ge(l.id,y.value)).code===200&&(p.success("删除成功"),k(),((s=a.value)==null?void 0:s.id)===l.id&&(a.value=null))};return ae(()=>{k()}),(l,e)=>{var U;const s=d("el-icon"),m=d("el-button"),I=d("el-avatar"),f=d("el-tree"),C=d("el-aside"),b=d("el-input"),r=d("el-form-item"),i=d("el-input-number"),n=d("el-form"),x=d("el-main"),Q=d("el-container");return h(),S(re,null,[t(Q,{class:"category-container"},{default:o(()=>[t(C,{width:"30%",class:"category-tree"},{default:o(()=>[u("div",me,[t(m,{type:"primary",onClick:M},{default:o(()=>[t(s,null,{default:o(()=>[t(V(F))]),_:1}),e[5]||(e[5]=L("添加根分类 "))]),_:1,__:[5]})]),t(f,{ref_key:"categoryTree",ref:E,data:T.value,props:z,"node-key":"id",draggable:"",onNodeClick:R,onNodeDrop:Z},{default:o(({node:c,data:q})=>[u("div",fe,[u("span",ve,[q.image?(h(),N(I,{key:0,src:q.image,size:20,class:"category-avatar"},null,8,["src"])):(h(),N(s,{key:1,class:"default-icon"},{default:o(()=>[t(V(ne))]),_:1})),L(" "+se(c.label),1)]),u("span",_e,[t(m,{type:"primary",link:"",onClick:O(W=>P(q),["stop"])},{default:o(()=>[t(s,null,{default:o(()=>[t(V(F))]),_:1})]),_:2},1032,["onClick"]),t(m,{type:"danger",link:"",onClick:O(W=>H(q),["stop"])},{default:o(()=>[t(s,null,{default:o(()=>[t(V(D))]),_:1})]),_:2},1032,["onClick"])])])]),_:1},8,["data"])]),_:1}),t(x,{class:"category-form"},{default:o(()=>[a.value?(h(),N(n,{key:0,ref_key:"categoryForm",ref:g,model:a.value,rules:A,"label-width":"126px","label-position":"left",class:"form-container"},{default:o(()=>[t(r,{label:"分类名称",prop:"name"},{default:o(()=>[t(b,{modelValue:a.value.name,"onUpdate:modelValue":e[0]||(e[0]=c=>a.value.name=c),placeholder:"请输入分类名称"},null,8,["modelValue"])]),_:1}),t(r,{label:"分类编码",prop:"code"},{default:o(()=>[t(b,{modelValue:a.value.code,"onUpdate:modelValue":e[1]||(e[1]=c=>a.value.code=c),placeholder:"请输入分类编码"},null,8,["modelValue"])]),_:1}),t(r,{label:"分类图片",prop:"image",required:""},{default:o(()=>[u("div",ye,[u("div",{class:"image-preview",onClick:G},[a.value.image?(h(),S("img",{key:0,src:a.value.image,class:"uploaded-image"},null,8,he)):(h(),S("div",ke,[t(s,null,{default:o(()=>[t(V(F))]),_:1}),e[6]||(e[6]=u("span",null,"选择图片",-1))])),a.value.image?(h(),S("div",be,[t(m,{type:"danger",circle:"",size:"small",onClick:O(K,["stop"])},{default:o(()=>[t(s,{size:15},{default:o(()=>[t(V(D))]),_:1})]),_:1})])):ie("",!0)]),e[7]||(e[7]=u("div",{class:"image-tips"},[u("p",null,[u("span",{class:"required-mark"},"*"),L(" 分类图片为必填项")]),u("p",null,"建议尺寸：120x120像素"),u("p",null,"支持格式：JPG、PNG、GIF"),u("p",null,"文件大小：不超过10KB")],-1))])]),_:1}),t(r,{label:"分类描述",prop:"description"},{default:o(()=>[t(b,{modelValue:a.value.description,"onUpdate:modelValue":e[2]||(e[2]=c=>a.value.description=c),type:"textarea",rows:3,placeholder:"请输入分类描述"},null,8,["modelValue"])]),_:1}),t(r,{label:"排序号",prop:"sortOrder"},{default:o(()=>[t(i,{modelValue:a.value.sortOrder,"onUpdate:modelValue":e[3]||(e[3]=c=>a.value.sortOrder=c),min:0,max:999},null,8,["modelValue"])]),_:1}),t(r,null,{default:o(()=>[t(m,{type:"primary",onClick:X},{default:o(()=>e[8]||(e[8]=[L("保存")])),_:1,__:[8]}),t(m,{onClick:Y},{default:o(()=>e[9]||(e[9]=[L("重置")])),_:1,__:[9]})]),_:1})]),_:1},8,["model"])):(h(),S("div",Ce," 请选择或创建一个分类 "))]),_:1})]),_:1}),t(oe,{visible:$.value,"onUpdate:visible":e[4]||(e[4]=c=>$.value=c),title:"选择分类图片",multiple:!1,"max-count":1,"initial-selected":(U=a.value)!=null&&U.image?[{id:0,url:a.value.image,name:"当前图片"}]:[],onSelect:j,onCancel:J},null,8,["visible","initial-selected"])],64)}}},Ie=ee(xe,[["__scopeId","data-v-f3d445ea"]]);export{Ie as default};
