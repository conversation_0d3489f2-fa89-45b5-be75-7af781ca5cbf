import{$ as _,a as le,r as u,a4 as te,a3 as ae,o as se,b as d,G as oe,c as C,e as I,k as N,l as t,a6 as ne,w as a,v as r,s as ue,f as K,t as j,F as ie,A as de,z as U,_ as re}from"./views-biz-72c05ff0.js";import{g as pe}from"./role-bd28dc4e.js";function me(i){return _.get({url:"/system/users",params:i})}function ce(i){return _.get({url:`/system/users/${i}`})}function ve(i){return _.post({url:"/system/users",data:i})}function q(i,c){return _.post({url:`/system/users/${i}`,data:c})}function _e(i){return _.get({url:`/system/users/${i}/roles`})}function fe(i,c){return _.post({url:`/system/users/${i}/bindRole`,data:c})}const ge={class:"user-container"},ye={class:"header"},be={class:"pagination"},Ve=le({__name:"index",setup(i){const c=u([]),w=u(0),v=u(1),b=u(10),V=u(""),x=u(!1),M=te.debounce(()=>{v.value=1,p()},600),f=u(!1),R=u("新增账号"),o=ae({id:void 0,name:"",email:"",mobile:"",nickname:""}),D=u(),O={name:[{required:!0,message:"请输入用户名",trigger:"blur"}]},g=u(!1),z=u(null),k=u([]),S=u([]),p=async()=>{x.value=!0;const s=await me({page:v.value,size:b.value,query:V.value});c.value=s.data.records,w.value=s.data.total,x.value=!1},E=()=>{M()},P=()=>{V.value="",v.value=1,p()},T=s=>{b.value=s,v.value=1,p()},F=async s=>{if(s&&s.id){R.value="编辑账号";const e=await ce(s.id);Object.keys(o).forEach(n=>{o[n]=e.data[n]})}else R.value="新增账号",Object.assign(o,{id:void 0,name:"",email:"",mobile:"",nickname:""});f.value=!0},A=async()=>{await D.value.validate(),o.id?(console.log("submitEdit",o),await q(o.id,o),U.success("更新成功")):(await ve(o),U.success("添加成功")),f.value=!1,p()},G=async s=>{await q(s.id,{status:s.status===1?0:1}),U.success(s.status===1?"已禁用":"已启用"),p()},H=async s=>{z.value=s.id;const e=await pe({page:1,pageSize:1e3});S.value=e.data.records;const m=await _e(s.id);k.value=(m.data||[]).map(n=>n.id),g.value=!0},J=async()=>{z.value&&(await fe(z.value,k.value),U.success("角色绑定成功"),g.value=!1,p())};return se(()=>{p()}),(s,e)=>{const m=d("el-input"),n=d("el-button"),y=d("el-table-column"),Q=d("el-tag"),W=d("el-table"),X=d("el-pagination"),$=d("el-form-item"),Y=d("el-form"),L=d("el-dialog"),Z=d("el-option"),h=d("el-select"),ee=oe("loading");return C(),I("div",ge,[N("div",ye,[t(m,{modelValue:V.value,"onUpdate:modelValue":e[0]||(e[0]=l=>V.value=l),placeholder:"搜索用户名/邮箱/手机号",clearable:"",style:{width:"200px","margin-right":"10px"},onKeyup:ne(E,["enter"]),onInput:E,onClear:P},null,8,["modelValue"]),t(n,{type:"primary",onClick:e[1]||(e[1]=l=>F())},{default:a(()=>e[12]||(e[12]=[r("新增账号")])),_:1,__:[12]})]),ue((C(),K(W,{height:"calc(100vh - 230px)",data:c.value,style:{"margin-top":"16px"}},{default:a(()=>[t(y,{prop:"name",label:"用户名"}),t(y,{prop:"email",label:"邮箱"}),t(y,{prop:"mobile",label:"手机号"}),t(y,{prop:"status",label:"状态",width:"100"},{default:a(({row:l})=>[t(Q,{type:l.status===1?"success":"danger"},{default:a(()=>[r(j(l.status===1?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),t(y,{label:"操作",width:"260"},{default:a(({row:l})=>[t(n,{link:"",type:"primary",size:"small",onClick:B=>F(l)},{default:a(()=>e[13]||(e[13]=[r("编辑")])),_:2,__:[13]},1032,["onClick"]),t(n,{link:"",type:"primary",size:"small",onClick:B=>H(l)},{default:a(()=>e[14]||(e[14]=[r("角色绑定")])),_:2,__:[14]},1032,["onClick"]),t(n,{link:"",size:"small",type:l.status===1?"danger":"primary",onClick:B=>G(l)},{default:a(()=>[r(j(l.status===1?"禁用":"启用"),1)]),_:2},1032,["type","onClick"])]),_:1})]),_:1},8,["data"])),[[ee,x.value]]),N("div",be,[t(X,{"current-page":v.value,"onUpdate:currentPage":e[2]||(e[2]=l=>v.value=l),"page-size":b.value,"onUpdate:pageSize":e[3]||(e[3]=l=>b.value=l),total:w.value,background:"","page-sizes":[10,20,50,100],onSizeChange:T,onCurrentChange:p},null,8,["current-page","page-size","total"])]),t(L,{modelValue:f.value,"onUpdate:modelValue":e[8]||(e[8]=l=>f.value=l),title:R.value,width:"400px"},{footer:a(()=>[t(n,{onClick:e[7]||(e[7]=l=>f.value=!1)},{default:a(()=>e[15]||(e[15]=[r("取消")])),_:1,__:[15]}),t(n,{type:"primary",onClick:A},{default:a(()=>e[16]||(e[16]=[r("保存")])),_:1,__:[16]})]),default:a(()=>[t(Y,{model:o,rules:O,ref_key:"editFormRef",ref:D,"label-width":"80px"},{default:a(()=>[t($,{label:"用户名",prop:"name"},{default:a(()=>[t(m,{modelValue:o.name,"onUpdate:modelValue":e[4]||(e[4]=l=>o.name=l)},null,8,["modelValue"])]),_:1}),t($,{label:"邮箱",prop:"email"},{default:a(()=>[t(m,{modelValue:o.email,"onUpdate:modelValue":e[5]||(e[5]=l=>o.email=l)},null,8,["modelValue"])]),_:1}),t($,{label:"手机号",prop:"mobile"},{default:a(()=>[t(m,{modelValue:o.mobile,"onUpdate:modelValue":e[6]||(e[6]=l=>o.mobile=l)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),t(L,{modelValue:g.value,"onUpdate:modelValue":e[11]||(e[11]=l=>g.value=l),title:"账号角色绑定",width:"400px"},{footer:a(()=>[t(n,{onClick:e[10]||(e[10]=l=>g.value=!1)},{default:a(()=>e[17]||(e[17]=[r("取消")])),_:1,__:[17]}),t(n,{type:"primary",onClick:J},{default:a(()=>e[18]||(e[18]=[r("保存")])),_:1,__:[18]})]),default:a(()=>[t(h,{modelValue:k.value,"onUpdate:modelValue":e[9]||(e[9]=l=>k.value=l),multiple:"",filterable:"",placeholder:"请选择角色",style:{width:"100%"}},{default:a(()=>[(C(!0),I(ie,null,de(S.value,l=>(C(),K(Z,{key:l.id,label:l.roleName,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])])}}});const Ue=re(Ve,[["__scopeId","data-v-82fe70c8"]]);export{Ue as default};
