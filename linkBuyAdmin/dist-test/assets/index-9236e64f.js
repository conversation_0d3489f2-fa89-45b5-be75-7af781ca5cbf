import{$ as bn,d as yn,r as F2,a as l,o as Y6,g as An,u as En,p as Sn,n as Bn,_ as k2,b as s2,c,e as J,f as i,h as r0,i as Fn,j as T2,k as w2,t as f2,w as Y,l as a,E as Tn,m as In,q as On,s as s0,v as I2,x as i6,y as Nn,z as Pn,F as u0,A as K6,B as E2,C as Dn,D as a0,G as u6,H as p6,I as X6,J as U2,K as Rn,L as Un,M as Gn,N as Wn,O as Zn,T as qn,P as jn,Q as Yn}from"./views-biz-72c05ff0.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))o(r);new MutationObserver(r=>{for(const s of r)if(s.type==="childList")for(const u of s.addedNodes)u.tagName==="LINK"&&u.rel==="modulepreload"&&o(u)}).observe(document,{childList:!0,subtree:!0});function n(r){const s={};return r.integrity&&(s.integrity=r.integrity),r.referrerPolicy&&(s.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?s.credentials="include":r.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function o(r){if(r.ep)return;r.ep=!0;const s=n(r);fetch(r.href,s)}})();function Kn(){return bn.get({url:"common/dict/all"})}const Xn=yn("dict",()=>{const e=F2({});async function t(){const{data:s,code:u}=await Kn();if(u!==200)return;const h={};s.forEach(d=>{h[d.dictCode]=d.items}),e.value=h}function n(s,u,h){const d=e.value[s];return!d||!u||!h?!1:u.some(m=>{var z;return((z=d.find(x=>x.itemValue.toLowerCase()===m.toLowerCase()))==null?void 0:z.itemName.toLowerCase())===h.toLowerCase()})}function o(s,u){var d;const h=e.value[s];return(d=h==null?void 0:h.find(m=>m.itemValue.toLowerCase()===u.toLowerCase()))==null?void 0:d.itemName}function r(s){return e.value[s]||[]}return{dicts:e,loadDictDataAction:t,isContainName:n,getName:o,getDict:r}},{persist:{enabled:!0,strategies:[{key:"dicts",storage:localStorage,paths:["dicts"]}]}}),Jn=l({name:"App",setup(){const e=F2(!0);return Sn("reload",()=>{e.value=!1,Bn(()=>{e.value=!0})}),Y6(async()=>{await Xn().loadDictDataAction(),An()&&await En().refreshUserInfoAction()}),{isRouterAlive:e}}}),Qn={id:"app"};function eo(e,t,n,o,r,s){const u=s2("router-view");return c(),J("div",Qn,[e.isRouterAlive?(c(),i(u,{key:0})):r0("",!0)])}const to=k2(Jn,[["render",eo]]),_6=(e,t)=>{const n=e.storage||sessionStorage,o=e.key||t.$id;if(e.paths){const r=e.paths.reduce((s,u)=>(s[u]=t.$state[u],s),{});n.setItem(o,JSON.stringify(r))}else n.setItem(o,JSON.stringify(t.$state))};var no=({options:e,store:t})=>{var n,o,r,s;if((n=e.persist)!=null&&n.enabled){const u=[{key:t.$id,storage:sessionStorage}],h=(r=(o=e.persist)==null?void 0:o.strategies)!=null&&r.length?(s=e.persist)==null?void 0:s.strategies:u;h.forEach(d=>{const m=d.storage||sessionStorage,z=d.key||t.$id,x=m.getItem(z);x&&(t.$patch(JSON.parse(x)),_6(d,t))}),t.$subscribe(()=>{h.forEach(d=>{_6(d,t)})})}};const J6=Fn();J6.use(no);const oo=l({name:"page-question",props:{title:{type:String,default:""},tip:{type:String,default:""},width:{type:Number,default:400},placement:{type:String,default:"right"}},components:{},setup(){return{}}});const ro={class:"page-question-panel"},so={class:"title"};function ao(e,t,n,o,r,s){const u=s2("el-popover");return c(),J("div",ro,[T2(e.$slots,"default",{},void 0,!0),w2("span",so,f2(e.title),1),e.tip?(c(),i(u,{key:0,placement:e.placement,width:e.width,trigger:"hover",content:e.tip},{reference:Y(()=>t[0]||(t[0]=[w2("span",{class:"icon-question ml-5"},null,-1)])),_:1},8,["placement","width","content"])):r0("",!0)])}const co=k2(oo,[["render",ao],["__scopeId","data-v-7f38c675"]]),lo=l({name:"page-card",props:{title:{type:String}},components:{},setup(){return{}}});const io={class:"page-card-panel"},uo={class:"card-header"},po={class:"title"};function _o(e,t,n,o,r,s){const u=s2("el-card");return c(),J("div",io,[a(u,{class:"box-card",shadow:"never"},{header:Y(()=>[w2("div",uo,[w2("span",po,f2(e.title),1),T2(e.$slots,"header-operation",{},void 0,!0)])]),default:Y(()=>[T2(e.$slots,"default",{},void 0,!0)]),_:3})])}const ho=k2(lo,[["render",_o],["__scopeId","data-v-7313cb4a"]]),$o=l({name:"page-modal",components:{ElDialog:Tn,ElButton:In},props:{modalConfig:{type:Object,required:!0},confirmText:{type:String,default:"确 定"},cancelText:{type:String,default:"取 消"},showCancelButton:{type:Boolean,default:!0},showConfirmButton:{type:Boolean,default:!0},top:{type:String,default:"15vh"}},setup(e,{emit:t}){return{showModal:F2(!1),handleModalConfirm:()=>{t("modalConfirm")},handleModalClose:()=>{t("modalClose")}}}});const fo={class:"page-modal"},mo={class:"modal-container"},vo={class:"dialog-footer"};function go(e,t,n,o,r,s){const u=s2("el-button"),h=s2("el-dialog");return c(),J("div",fo,[a(h,{title:e.modalConfig.title,modelValue:e.showModal,"onUpdate:modelValue":t[1]||(t[1]=d=>e.showModal=d),"destroy-on-close":"","append-to-body":"",top:e.top,width:e.modalConfig.width,"close-on-click-modal":!1,onClose:e.handleModalClose},On({default:Y(()=>[w2("div",mo,[T2(e.$slots,"default",{},void 0,!0)])]),_:2},[e.modalConfig.hideFooterButton?void 0:{name:"footer",fn:Y(()=>[w2("span",vo,[s0(a(u,{onClick:t[0]||(t[0]=d=>e.showModal=!1)},{default:Y(()=>[I2(f2(e.cancelText),1)]),_:1},512),[[i6,e.showCancelButton]]),s0(a(u,{type:"primary",disabled:e.modalConfig.isConfirmBtnDisabled,onClick:e.handleModalConfirm},{default:Y(()=>[I2(f2(e.confirmText),1)]),_:1},8,["disabled","onClick"]),[[i6,e.showConfirmButton]])])]),key:"0"}]),1032,["title","modelValue","top","width","onClose"])])}const wo=k2($o,[["render",go],["__scopeId","data-v-ca887e02"]]),zo=[co,ho,wo],xo=e=>{for(const t of zo)e.component(t.name,t)};var D0=l({name:"Aim"});const Co={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Mo=a("path",{fill:"currentColor",d:"M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768zm0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896z"},null,-1),Lo=a("path",{fill:"currentColor",d:"M512 96a32 32 0 0 1 32 32v192a32 32 0 0 1-64 0V128a32 32 0 0 1 32-32zm0 576a32 32 0 0 1 32 32v192a32 32 0 1 1-64 0V704a32 32 0 0 1 32-32zM96 512a32 32 0 0 1 32-32h192a32 32 0 0 1 0 64H128a32 32 0 0 1-32-32zm576 0a32 32 0 0 1 32-32h192a32 32 0 1 1 0 64H704a32 32 0 0 1-32-32z"},null,-1);function Ho(e,t,n,o,r,s){return c(),i("svg",Co,[Mo,Lo])}D0.render=Ho;D0.__file="packages/components/Aim.vue";const ko=D0;var R0=l({name:"AddLocation"});const Vo={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},bo=a("path",{fill:"currentColor",d:"M288 896h448q32 0 32 32t-32 32H288q-32 0-32-32t32-32z"},null,-1),yo=a("path",{fill:"currentColor",d:"M800 416a288 288 0 1 0-576 0c0 118.144 94.528 272.128 288 456.576C705.472 688.128 800 534.144 800 416zM512 960C277.312 746.688 160 565.312 160 416a352 352 0 0 1 704 0c0 149.312-117.312 330.688-352 544z"},null,-1),Ao=a("path",{fill:"currentColor",d:"M544 384h96a32 32 0 1 1 0 64h-96v96a32 32 0 0 1-64 0v-96h-96a32 32 0 0 1 0-64h96v-96a32 32 0 0 1 64 0v96z"},null,-1);function Eo(e,t,n,o,r,s){return c(),i("svg",Vo,[bo,yo,Ao])}R0.render=Eo;R0.__file="packages/components/AddLocation.vue";const So=R0;var U0=l({name:"Apple"});const Bo={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Fo=a("path",{fill:"currentColor",d:"M599.872 203.776a189.44 189.44 0 0 1 64.384-4.672l2.624.128c31.168 1.024 51.2 4.096 79.488 16.32 37.632 16.128 74.496 45.056 111.488 89.344 96.384 115.264 82.752 372.8-34.752 521.728-7.68 9.728-32 41.6-30.72 39.936a426.624 426.624 0 0 1-30.08 35.776c-31.232 32.576-65.28 49.216-110.08 50.048-31.36.64-53.568-5.312-84.288-18.752l-6.528-2.88c-20.992-9.216-30.592-11.904-47.296-11.904-18.112 0-28.608 2.88-51.136 12.672l-6.464 2.816c-28.416 12.224-48.32 18.048-76.16 19.2-74.112 2.752-116.928-38.08-180.672-132.16-96.64-142.08-132.608-349.312-55.04-486.4 46.272-81.92 129.92-133.632 220.672-135.04 32.832-.576 60.288 6.848 99.648 22.72 27.136 10.88 34.752 13.76 37.376 14.272 16.256-20.16 27.776-36.992 34.56-50.24 13.568-26.304 27.2-59.968 40.704-100.8a32 32 0 1 1 60.8 20.224c-12.608 37.888-25.408 70.4-38.528 97.664zm-51.52 78.08c-14.528 17.792-31.808 37.376-51.904 58.816a32 32 0 1 1-46.72-43.776l12.288-13.248c-28.032-11.2-61.248-26.688-95.68-26.112-70.4 1.088-135.296 41.6-171.648 105.792C121.6 492.608 176 684.16 247.296 788.992c34.816 51.328 76.352 108.992 130.944 106.944 52.48-2.112 72.32-34.688 135.872-34.688 63.552 0 81.28 34.688 136.96 33.536 56.448-1.088 75.776-39.04 126.848-103.872 107.904-136.768 107.904-362.752 35.776-449.088-72.192-86.272-124.672-84.096-151.68-85.12-41.472-4.288-81.6 12.544-113.664 25.152z"},null,-1);function To(e,t,n,o,r,s){return c(),i("svg",Bo,[Fo])}U0.render=To;U0.__file="packages/components/Apple.vue";const Io=U0;var G0=l({name:"AlarmClock"});const Oo={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},No=a("path",{fill:"currentColor",d:"M512 832a320 320 0 1 0 0-640 320 320 0 0 0 0 640zm0 64a384 384 0 1 1 0-768 384 384 0 0 1 0 768z"},null,-1),Po=a("path",{fill:"currentColor",d:"m292.288 824.576 55.424 32-48 83.136a32 32 0 1 1-55.424-32l48-83.136zm439.424 0-55.424 32 48 83.136a32 32 0 1 0 55.424-32l-48-83.136zM512 512h160a32 32 0 1 1 0 64H480a32 32 0 0 1-32-32V320a32 32 0 0 1 64 0v192zM90.496 312.256A160 160 0 0 1 312.32 90.496l-46.848 46.848a96 96 0 0 0-128 128L90.56 312.256zm835.264 0A160 160 0 0 0 704 90.496l46.848 46.848a96 96 0 0 1 128 128l46.912 46.912z"},null,-1);function Do(e,t,n,o,r,s){return c(),i("svg",Oo,[No,Po])}G0.render=Do;G0.__file="packages/components/AlarmClock.vue";const Ro=G0;var W0=l({name:"ArrowDown"});const Uo={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Go=a("path",{fill:"currentColor",d:"M831.872 340.864 512 652.672 192.128 340.864a30.592 30.592 0 0 0-42.752 0 29.12 29.12 0 0 0 0 41.6L489.664 714.24a32 32 0 0 0 44.672 0l340.288-331.712a29.12 29.12 0 0 0 0-41.728 30.592 30.592 0 0 0-42.752 0z"},null,-1);function Wo(e,t,n,o,r,s){return c(),i("svg",Uo,[Go])}W0.render=Wo;W0.__file="packages/components/ArrowDown.vue";const Zo=W0;var Z0=l({name:"ArrowDownBold"});const qo={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},jo=a("path",{fill:"currentColor",d:"M104.704 338.752a64 64 0 0 1 90.496 0l316.8 316.8 316.8-316.8a64 64 0 0 1 90.496 90.496L557.248 791.296a64 64 0 0 1-90.496 0L104.704 429.248a64 64 0 0 1 0-90.496z"},null,-1);function Yo(e,t,n,o,r,s){return c(),i("svg",qo,[jo])}Z0.render=Yo;Z0.__file="packages/components/ArrowDownBold.vue";const Ko=Z0;var q0=l({name:"ArrowLeft"});const Xo={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Jo=a("path",{fill:"currentColor",d:"M609.408 149.376 277.76 489.6a32 32 0 0 0 0 44.672l331.648 340.352a29.12 29.12 0 0 0 41.728 0 30.592 30.592 0 0 0 0-42.752L339.264 511.936l311.872-319.872a30.592 30.592 0 0 0 0-42.688 29.12 29.12 0 0 0-41.728 0z"},null,-1);function Qo(e,t,n,o,r,s){return c(),i("svg",Xo,[Jo])}q0.render=Qo;q0.__file="packages/components/ArrowLeft.vue";const e8=q0;var j0=l({name:"ArrowLeftBold"});const t8={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},n8=a("path",{fill:"currentColor",d:"M685.248 104.704a64 64 0 0 1 0 90.496L368.448 512l316.8 316.8a64 64 0 0 1-90.496 90.496L232.704 557.248a64 64 0 0 1 0-90.496l362.048-362.048a64 64 0 0 1 90.496 0z"},null,-1);function o8(e,t,n,o,r,s){return c(),i("svg",t8,[n8])}j0.render=o8;j0.__file="packages/components/ArrowLeftBold.vue";const r8=j0;var Y0=l({name:"ArrowRightBold"});const s8={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},a8=a("path",{fill:"currentColor",d:"M338.752 104.704a64 64 0 0 0 0 90.496l316.8 316.8-316.8 316.8a64 64 0 0 0 90.496 90.496l362.048-362.048a64 64 0 0 0 0-90.496L429.248 104.704a64 64 0 0 0-90.496 0z"},null,-1);function c8(e,t,n,o,r,s){return c(),i("svg",s8,[a8])}Y0.render=c8;Y0.__file="packages/components/ArrowRightBold.vue";const l8=Y0;var K0=l({name:"ArrowUp"});const i8={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},u8=a("path",{fill:"currentColor",d:"m488.832 344.32-339.84 356.672a32 32 0 0 0 0 44.16l.384.384a29.44 29.44 0 0 0 42.688 0l320-335.872 319.872 335.872a29.44 29.44 0 0 0 42.688 0l.384-.384a32 32 0 0 0 0-44.16L535.168 344.32a32 32 0 0 0-46.336 0z"},null,-1);function p8(e,t,n,o,r,s){return c(),i("svg",i8,[u8])}K0.render=p8;K0.__file="packages/components/ArrowUp.vue";const _8=K0;var X0=l({name:"Back"});const d8={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},h8=a("path",{fill:"currentColor",d:"M224 480h640a32 32 0 1 1 0 64H224a32 32 0 0 1 0-64z"},null,-1),$8=a("path",{fill:"currentColor",d:"m237.248 512 265.408 265.344a32 32 0 0 1-45.312 45.312l-288-288a32 32 0 0 1 0-45.312l288-288a32 32 0 1 1 45.312 45.312L237.248 512z"},null,-1);function f8(e,t,n,o,r,s){return c(),i("svg",d8,[h8,$8])}X0.render=f8;X0.__file="packages/components/Back.vue";const m8=X0;var J0=l({name:"Bell"});const v8={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},g8=a("path",{fill:"currentColor",d:"M512 64a64 64 0 0 1 64 64v64H448v-64a64 64 0 0 1 64-64z"},null,-1),w8=a("path",{fill:"currentColor",d:"M256 768h512V448a256 256 0 1 0-512 0v320zm256-640a320 320 0 0 1 320 320v384H192V448a320 320 0 0 1 320-320z"},null,-1),z8=a("path",{fill:"currentColor",d:"M96 768h832q32 0 32 32t-32 32H96q-32 0-32-32t32-32zm352 128h128a64 64 0 0 1-128 0z"},null,-1);function x8(e,t,n,o,r,s){return c(),i("svg",v8,[g8,w8,z8])}J0.render=x8;J0.__file="packages/components/Bell.vue";const C8=J0;var Q0=l({name:"Baseball"});const M8={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},L8=a("path",{fill:"currentColor",d:"M195.2 828.8a448 448 0 1 1 633.6-633.6 448 448 0 0 1-633.6 633.6zm45.248-45.248a384 384 0 1 0 543.104-543.104 384 384 0 0 0-543.104 543.104z"},null,-1),H8=a("path",{fill:"currentColor",d:"M497.472 96.896c22.784 4.672 44.416 9.472 64.896 14.528a256.128 256.128 0 0 0 350.208 350.208c5.056 20.48 9.856 42.112 14.528 64.896A320.128 320.128 0 0 1 497.472 96.896zM108.48 491.904a320.128 320.128 0 0 1 423.616 423.68c-23.04-3.648-44.992-7.424-65.728-11.52a256.128 256.128 0 0 0-346.496-346.432 1736.64 1736.64 0 0 1-11.392-65.728z"},null,-1);function k8(e,t,n,o,r,s){return c(),i("svg",M8,[L8,H8])}Q0.render=k8;Q0.__file="packages/components/Baseball.vue";const V8=Q0;var e1=l({name:"Bicycle"});const b8={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},y8=a("path",{fill:"currentColor",d:"M256 832a128 128 0 1 0 0-256 128 128 0 0 0 0 256zm0 64a192 192 0 1 1 0-384 192 192 0 0 1 0 384z"},null,-1),A8=a("path",{fill:"currentColor",d:"M288 672h320q32 0 32 32t-32 32H288q-32 0-32-32t32-32z"},null,-1),E8=a("path",{fill:"currentColor",d:"M768 832a128 128 0 1 0 0-256 128 128 0 0 0 0 256zm0 64a192 192 0 1 1 0-384 192 192 0 0 1 0 384z"},null,-1),S8=a("path",{fill:"currentColor",d:"M480 192a32 32 0 0 1 0-64h160a32 32 0 0 1 31.04 24.256l96 384a32 32 0 0 1-62.08 15.488L615.04 192H480zM96 384a32 32 0 0 1 0-64h128a32 32 0 0 1 30.336 21.888l64 192a32 32 0 1 1-60.672 20.224L200.96 384H96z"},null,-1),B8=a("path",{fill:"currentColor",d:"m373.376 599.808-42.752-47.616 320-288 42.752 47.616z"},null,-1);function F8(e,t,n,o,r,s){return c(),i("svg",b8,[y8,A8,E8,S8,B8])}e1.render=F8;e1.__file="packages/components/Bicycle.vue";const T8=e1;var t1=l({name:"BellFilled"});const I8={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},O8=a("path",{fill:"currentColor",d:"M640 832a128 128 0 0 1-256 0h256zm192-64H134.4a38.4 38.4 0 0 1 0-76.8H192V448c0-154.88 110.08-284.16 256.32-313.6a64 64 0 1 1 127.36 0A320.128 320.128 0 0 1 832 448v243.2h57.6a38.4 38.4 0 0 1 0 76.8H832z"},null,-1);function N8(e,t,n,o,r,s){return c(),i("svg",I8,[O8])}t1.render=N8;t1.__file="packages/components/BellFilled.vue";const P8=t1;var n1=l({name:"Basketball"});const D8={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},R8=a("path",{fill:"currentColor",d:"M778.752 788.224a382.464 382.464 0 0 0 116.032-245.632 256.512 256.512 0 0 0-241.728-13.952 762.88 762.88 0 0 1 125.696 259.584zm-55.04 44.224a699.648 699.648 0 0 0-125.056-269.632 256.128 256.128 0 0 0-56.064 331.968 382.72 382.72 0 0 0 181.12-62.336zm-254.08 61.248A320.128 320.128 0 0 1 557.76 513.6a715.84 715.84 0 0 0-48.192-48.128 320.128 320.128 0 0 1-379.264 88.384 382.4 382.4 0 0 0 110.144 229.696 382.4 382.4 0 0 0 229.184 110.08zM129.28 481.088a256.128 256.128 0 0 0 331.072-56.448 699.648 699.648 0 0 0-268.8-124.352 382.656 382.656 0 0 0-62.272 180.8zm106.56-235.84a762.88 762.88 0 0 1 258.688 125.056 256.512 256.512 0 0 0-13.44-241.088A382.464 382.464 0 0 0 235.84 245.248zm318.08-114.944c40.576 89.536 37.76 193.92-8.448 281.344a779.84 779.84 0 0 1 66.176 66.112 320.832 320.832 0 0 1 282.112-8.128 382.4 382.4 0 0 0-110.144-229.12 382.4 382.4 0 0 0-229.632-110.208zM828.8 828.8a448 448 0 1 1-633.6-633.6 448 448 0 0 1 633.6 633.6z"},null,-1);function U8(e,t,n,o,r,s){return c(),i("svg",D8,[R8])}n1.render=U8;n1.__file="packages/components/Basketball.vue";const G8=n1;var o1=l({name:"Bottom"});const W8={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Z8=a("path",{fill:"currentColor",d:"M544 805.888V168a32 32 0 1 0-64 0v637.888L246.656 557.952a30.72 30.72 0 0 0-45.312 0 35.52 35.52 0 0 0 0 48.064l288 306.048a30.72 30.72 0 0 0 45.312 0l288-306.048a35.52 35.52 0 0 0 0-48 30.72 30.72 0 0 0-45.312 0L544 805.824z"},null,-1);function q8(e,t,n,o,r,s){return c(),i("svg",W8,[Z8])}o1.render=q8;o1.__file="packages/components/Bottom.vue";const j8=o1;var r1=l({name:"Box"});const Y8={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},K8=a("path",{fill:"currentColor",d:"M317.056 128 128 344.064V896h768V344.064L706.944 128H317.056zm-14.528-64h418.944a32 32 0 0 1 24.064 10.88l206.528 236.096A32 32 0 0 1 960 332.032V928a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V332.032a32 32 0 0 1 7.936-21.12L278.4 75.008A32 32 0 0 1 302.528 64z"},null,-1),X8=a("path",{fill:"currentColor",d:"M64 320h896v64H64z"},null,-1),J8=a("path",{fill:"currentColor",d:"M448 327.872V640h128V327.872L526.08 128h-28.16L448 327.872zM448 64h128l64 256v352a32 32 0 0 1-32 32H416a32 32 0 0 1-32-32V320l64-256z"},null,-1);function Q8(e,t,n,o,r,s){return c(),i("svg",Y8,[K8,X8,J8])}r1.render=Q8;r1.__file="packages/components/Box.vue";const er=r1;var s1=l({name:"Briefcase"});const tr={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},nr=a("path",{fill:"currentColor",d:"M320 320V128h384v192h192v192H128V320h192zM128 576h768v320H128V576zm256-256h256.064V192H384v128z"},null,-1);function or(e,t,n,o,r,s){return c(),i("svg",tr,[nr])}s1.render=or;s1.__file="packages/components/Briefcase.vue";const rr=s1;var a1=l({name:"BrushFilled"});const sr={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},ar=a("path",{fill:"currentColor",d:"M608 704v160a96 96 0 0 1-192 0V704h-96a128 128 0 0 1-128-128h640a128 128 0 0 1-128 128h-96zM192 512V128.064h640V512H192z"},null,-1);function cr(e,t,n,o,r,s){return c(),i("svg",sr,[ar])}a1.render=cr;a1.__file="packages/components/BrushFilled.vue";const lr=a1;var c1=l({name:"Bowl"});const ir={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},ur=a("path",{fill:"currentColor",d:"M714.432 704a351.744 351.744 0 0 0 148.16-256H161.408a351.744 351.744 0 0 0 148.16 256h404.864zM288 766.592A415.68 415.68 0 0 1 96 416a32 32 0 0 1 32-32h768a32 32 0 0 1 32 32 415.68 415.68 0 0 1-192 350.592V832a64 64 0 0 1-64 64H352a64 64 0 0 1-64-64v-65.408zM493.248 320h-90.496l254.4-254.4a32 32 0 1 1 45.248 45.248L493.248 320zm187.328 0h-128l269.696-155.712a32 32 0 0 1 32 55.424L680.576 320zM352 768v64h320v-64H352z"},null,-1);function pr(e,t,n,o,r,s){return c(),i("svg",ir,[ur])}c1.render=pr;c1.__file="packages/components/Bowl.vue";const _r=c1;var l1=l({name:"Avatar"});const dr={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},hr=a("path",{fill:"currentColor",d:"M628.736 528.896A416 416 0 0 1 928 928H96a415.872 415.872 0 0 1 299.264-399.104L512 704l116.736-175.104zM720 304a208 208 0 1 1-416 0 208 208 0 0 1 416 0z"},null,-1);function $r(e,t,n,o,r,s){return c(),i("svg",dr,[hr])}l1.render=$r;l1.__file="packages/components/Avatar.vue";const fr=l1;var i1=l({name:"Brush"});const mr={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},vr=a("path",{fill:"currentColor",d:"M896 448H128v192a64 64 0 0 0 64 64h192v192h256V704h192a64 64 0 0 0 64-64V448zm-770.752-64c0-47.552 5.248-90.24 15.552-128 14.72-54.016 42.496-107.392 83.2-160h417.28l-15.36 70.336L736 96h211.2c-24.832 42.88-41.92 96.256-51.2 160a663.872 663.872 0 0 0-6.144 128H960v256a128 128 0 0 1-128 128H704v160a32 32 0 0 1-32 32H352a32 32 0 0 1-32-32V768H192A128 128 0 0 1 64 640V384h61.248zm64 0h636.544c-2.048-45.824.256-91.584 6.848-137.216 4.48-30.848 10.688-59.776 18.688-86.784h-96.64l-221.12 141.248L561.92 160H256.512c-25.856 37.888-43.776 75.456-53.952 112.832-8.768 32.064-13.248 69.12-13.312 111.168z"},null,-1);function gr(e,t,n,o,r,s){return c(),i("svg",mr,[vr])}i1.render=gr;i1.__file="packages/components/Brush.vue";const wr=i1;var u1=l({name:"Burger"});const zr={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},xr=a("path",{fill:"currentColor",d:"M160 512a32 32 0 0 0-32 32v64a32 32 0 0 0 30.08 32H864a32 32 0 0 0 32-32v-64a32 32 0 0 0-32-32H160zm736-58.56A96 96 0 0 1 960 544v64a96 96 0 0 1-51.968 85.312L855.36 833.6a96 96 0 0 1-89.856 62.272H258.496A96 96 0 0 1 168.64 833.6l-52.608-140.224A96 96 0 0 1 64 608v-64a96 96 0 0 1 64-90.56V448a384 384 0 1 1 768 5.44zM832 448a320 320 0 0 0-640 0h640zM512 704H188.352l40.192 107.136a32 32 0 0 0 29.952 20.736h507.008a32 32 0 0 0 29.952-20.736L835.648 704H512z"},null,-1);function Cr(e,t,n,o,r,s){return c(),i("svg",zr,[xr])}u1.render=Cr;u1.__file="packages/components/Burger.vue";const Mr=u1;var p1=l({name:"Camera"});const Lr={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Hr=a("path",{fill:"currentColor",d:"M896 256H128v576h768V256zm-199.424-64-32.064-64h-304.96l-32 64h369.024zM96 192h160l46.336-92.608A64 64 0 0 1 359.552 64h304.96a64 64 0 0 1 57.216 35.328L768.192 192H928a32 32 0 0 1 32 32v640a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V224a32 32 0 0 1 32-32zm416 512a160 160 0 1 0 0-320 160 160 0 0 0 0 320zm0 64a224 224 0 1 1 0-448 224 224 0 0 1 0 448z"},null,-1);function kr(e,t,n,o,r,s){return c(),i("svg",Lr,[Hr])}p1.render=kr;p1.__file="packages/components/Camera.vue";const Vr=p1;var _1=l({name:"BottomLeft"});const br={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},yr=a("path",{fill:"currentColor",d:"M256 768h416a32 32 0 1 1 0 64H224a32 32 0 0 1-32-32V352a32 32 0 0 1 64 0v416z"},null,-1),Ar=a("path",{fill:"currentColor",d:"M246.656 822.656a32 32 0 0 1-45.312-45.312l544-544a32 32 0 0 1 45.312 45.312l-544 544z"},null,-1);function Er(e,t,n,o,r,s){return c(),i("svg",br,[yr,Ar])}_1.render=Er;_1.__file="packages/components/BottomLeft.vue";const Sr=_1;var d1=l({name:"Calendar"});const Br={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Fr=a("path",{fill:"currentColor",d:"M128 384v512h768V192H768v32a32 32 0 1 1-64 0v-32H320v32a32 32 0 0 1-64 0v-32H128v128h768v64H128zm192-256h384V96a32 32 0 1 1 64 0v32h160a32 32 0 0 1 32 32v768a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h160V96a32 32 0 0 1 64 0v32zm-32 384h64a32 32 0 0 1 0 64h-64a32 32 0 0 1 0-64zm0 192h64a32 32 0 1 1 0 64h-64a32 32 0 1 1 0-64zm192-192h64a32 32 0 0 1 0 64h-64a32 32 0 0 1 0-64zm0 192h64a32 32 0 1 1 0 64h-64a32 32 0 1 1 0-64zm192-192h64a32 32 0 1 1 0 64h-64a32 32 0 1 1 0-64zm0 192h64a32 32 0 1 1 0 64h-64a32 32 0 1 1 0-64z"},null,-1);function Tr(e,t,n,o,r,s){return c(),i("svg",Br,[Fr])}d1.render=Tr;d1.__file="packages/components/Calendar.vue";const Ir=d1;var h1=l({name:"CaretBottom"});const Or={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Nr=a("path",{fill:"currentColor",d:"m192 384 320 384 320-384z"},null,-1);function Pr(e,t,n,o,r,s){return c(),i("svg",Or,[Nr])}h1.render=Pr;h1.__file="packages/components/CaretBottom.vue";const Dr=h1;var $1=l({name:"CaretLeft"});const Rr={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Ur=a("path",{fill:"currentColor",d:"M672 192 288 511.936 672 832z"},null,-1);function Gr(e,t,n,o,r,s){return c(),i("svg",Rr,[Ur])}$1.render=Gr;$1.__file="packages/components/CaretLeft.vue";const Wr=$1;var f1=l({name:"CaretRight"});const Zr={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},qr=a("path",{fill:"currentColor",d:"M384 192v640l384-320.064z"},null,-1);function jr(e,t,n,o,r,s){return c(),i("svg",Zr,[qr])}f1.render=jr;f1.__file="packages/components/CaretRight.vue";const Yr=f1;var m1=l({name:"CaretTop"});const Kr={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Xr=a("path",{fill:"currentColor",d:"M512 320 192 704h639.936z"},null,-1);function Jr(e,t,n,o,r,s){return c(),i("svg",Kr,[Xr])}m1.render=Jr;m1.__file="packages/components/CaretTop.vue";const Qr=m1;var v1=l({name:"ChatDotSquare"});const es={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},ts=a("path",{fill:"currentColor",d:"M273.536 736H800a64 64 0 0 0 64-64V256a64 64 0 0 0-64-64H224a64 64 0 0 0-64 64v570.88L273.536 736zM296 800 147.968 918.4A32 32 0 0 1 96 893.44V256a128 128 0 0 1 128-128h576a128 128 0 0 1 128 128v416a128 128 0 0 1-128 128H296z"},null,-1),ns=a("path",{fill:"currentColor",d:"M512 499.2a51.2 51.2 0 1 1 0-102.4 51.2 51.2 0 0 1 0 102.4zm192 0a51.2 51.2 0 1 1 0-102.4 51.2 51.2 0 0 1 0 102.4zm-384 0a51.2 51.2 0 1 1 0-102.4 51.2 51.2 0 0 1 0 102.4z"},null,-1);function os(e,t,n,o,r,s){return c(),i("svg",es,[ts,ns])}v1.render=os;v1.__file="packages/components/ChatDotSquare.vue";const rs=v1;var g1=l({name:"Cellphone"});const ss={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},as=a("path",{fill:"currentColor",d:"M256 128a64 64 0 0 0-64 64v640a64 64 0 0 0 64 64h512a64 64 0 0 0 64-64V192a64 64 0 0 0-64-64H256zm0-64h512a128 128 0 0 1 128 128v640a128 128 0 0 1-128 128H256a128 128 0 0 1-128-128V192A128 128 0 0 1 256 64zm128 128h256a32 32 0 1 1 0 64H384a32 32 0 0 1 0-64zm128 640a64 64 0 1 1 0-128 64 64 0 0 1 0 128z"},null,-1);function cs(e,t,n,o,r,s){return c(),i("svg",ss,[as])}g1.render=cs;g1.__file="packages/components/Cellphone.vue";const ls=g1;var w1=l({name:"ChatDotRound"});const is={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},us=a("path",{fill:"currentColor",d:"m174.72 855.68 135.296-45.12 23.68 11.84C388.096 849.536 448.576 864 512 864c211.84 0 384-166.784 384-352S723.84 160 512 160 128 326.784 128 512c0 69.12 24.96 139.264 70.848 199.232l22.08 28.8-46.272 115.584zm-45.248 82.56A32 32 0 0 1 89.6 896l58.368-145.92C94.72 680.32 64 596.864 64 512 64 299.904 256 96 512 96s448 203.904 448 416-192 416-448 416a461.056 461.056 0 0 1-206.912-48.384l-175.616 58.56z"},null,-1),ps=a("path",{fill:"currentColor",d:"M512 563.2a51.2 51.2 0 1 1 0-102.4 51.2 51.2 0 0 1 0 102.4zm192 0a51.2 51.2 0 1 1 0-102.4 51.2 51.2 0 0 1 0 102.4zm-384 0a51.2 51.2 0 1 1 0-102.4 51.2 51.2 0 0 1 0 102.4z"},null,-1);function _s(e,t,n,o,r,s){return c(),i("svg",is,[us,ps])}w1.render=_s;w1.__file="packages/components/ChatDotRound.vue";const ds=w1;var z1=l({name:"ChatLineSquare"});const hs={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},$s=a("path",{fill:"currentColor",d:"M160 826.88 273.536 736H800a64 64 0 0 0 64-64V256a64 64 0 0 0-64-64H224a64 64 0 0 0-64 64v570.88zM296 800 147.968 918.4A32 32 0 0 1 96 893.44V256a128 128 0 0 1 128-128h576a128 128 0 0 1 128 128v416a128 128 0 0 1-128 128H296z"},null,-1),fs=a("path",{fill:"currentColor",d:"M352 512h320q32 0 32 32t-32 32H352q-32 0-32-32t32-32zm0-192h320q32 0 32 32t-32 32H352q-32 0-32-32t32-32z"},null,-1);function ms(e,t,n,o,r,s){return c(),i("svg",hs,[$s,fs])}z1.render=ms;z1.__file="packages/components/ChatLineSquare.vue";const vs=z1;var x1=l({name:"ChatLineRound"});const gs={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},ws=a("path",{fill:"currentColor",d:"m174.72 855.68 135.296-45.12 23.68 11.84C388.096 849.536 448.576 864 512 864c211.84 0 384-166.784 384-352S723.84 160 512 160 128 326.784 128 512c0 69.12 24.96 139.264 70.848 199.232l22.08 28.8-46.272 115.584zm-45.248 82.56A32 32 0 0 1 89.6 896l58.368-145.92C94.72 680.32 64 596.864 64 512 64 299.904 256 96 512 96s448 203.904 448 416-192 416-448 416a461.056 461.056 0 0 1-206.912-48.384l-175.616 58.56z"},null,-1),zs=a("path",{fill:"currentColor",d:"M352 576h320q32 0 32 32t-32 32H352q-32 0-32-32t32-32zm32-192h256q32 0 32 32t-32 32H384q-32 0-32-32t32-32z"},null,-1);function xs(e,t,n,o,r,s){return c(),i("svg",gs,[ws,zs])}x1.render=xs;x1.__file="packages/components/ChatLineRound.vue";const Cs=x1;var C1=l({name:"ChatRound"});const Ms={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Ls=a("path",{fill:"currentColor",d:"m174.72 855.68 130.048-43.392 23.424 11.392C382.4 849.984 444.352 864 512 864c223.744 0 384-159.872 384-352 0-192.832-159.104-352-384-352S128 319.168 128 512a341.12 341.12 0 0 0 69.248 204.288l21.632 28.8-44.16 110.528zm-45.248 82.56A32 32 0 0 1 89.6 896l56.512-141.248A405.12 405.12 0 0 1 64 512C64 299.904 235.648 96 512 96s448 203.904 448 416-173.44 416-448 416c-79.68 0-150.848-17.152-211.712-46.72l-170.88 56.96z"},null,-1);function Hs(e,t,n,o,r,s){return c(),i("svg",Ms,[Ls])}C1.render=Hs;C1.__file="packages/components/ChatRound.vue";const ks=C1;var M1=l({name:"Check"});const Vs={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},bs=a("path",{fill:"currentColor",d:"M406.656 706.944 195.84 496.256a32 32 0 1 0-45.248 45.248l256 256 512-512a32 32 0 0 0-45.248-45.248L406.592 706.944z"},null,-1);function ys(e,t,n,o,r,s){return c(),i("svg",Vs,[bs])}M1.render=ys;M1.__file="packages/components/Check.vue";const As=M1;var L1=l({name:"ChatSquare"});const Es={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Ss=a("path",{fill:"currentColor",d:"M273.536 736H800a64 64 0 0 0 64-64V256a64 64 0 0 0-64-64H224a64 64 0 0 0-64 64v570.88L273.536 736zM296 800 147.968 918.4A32 32 0 0 1 96 893.44V256a128 128 0 0 1 128-128h576a128 128 0 0 1 128 128v416a128 128 0 0 1-128 128H296z"},null,-1);function Bs(e,t,n,o,r,s){return c(),i("svg",Es,[Ss])}L1.render=Bs;L1.__file="packages/components/ChatSquare.vue";const Fs=L1;var H1=l({name:"Cherry"});const Ts={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Is=a("path",{fill:"currentColor",d:"M261.056 449.6c13.824-69.696 34.88-128.96 63.36-177.728 23.744-40.832 61.12-88.64 112.256-143.872H320a32 32 0 0 1 0-64h384a32 32 0 1 1 0 64H554.752c14.912 39.168 41.344 86.592 79.552 141.76 47.36 68.48 84.8 106.752 106.304 114.304a224 224 0 1 1-84.992 14.784c-22.656-22.912-47.04-53.76-73.92-92.608-38.848-56.128-67.008-105.792-84.352-149.312-55.296 58.24-94.528 107.52-117.76 147.2-23.168 39.744-41.088 88.768-53.568 147.072a224.064 224.064 0 1 1-64.96-1.6zM288 832a160 160 0 1 0 0-320 160 160 0 0 0 0 320zm448-64a160 160 0 1 0 0-320 160 160 0 0 0 0 320z"},null,-1);function Os(e,t,n,o,r,s){return c(),i("svg",Ts,[Is])}H1.render=Os;H1.__file="packages/components/Cherry.vue";const Ns=H1;var k1=l({name:"Chicken"});const Ps={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Ds=a("path",{fill:"currentColor",d:"M349.952 716.992 478.72 588.16a106.688 106.688 0 0 1-26.176-19.072 106.688 106.688 0 0 1-19.072-26.176L304.704 671.744c.768 3.072 1.472 6.144 2.048 9.216l2.048 31.936 31.872 1.984c3.136.64 6.208 1.28 9.28 2.112zm57.344 33.152a128 128 0 1 1-216.32 114.432l-1.92-32-32-1.92a128 128 0 1 1 114.432-216.32L416.64 469.248c-2.432-101.44 58.112-239.104 149.056-330.048 107.328-107.328 231.296-85.504 316.8 0 85.44 85.44 107.328 209.408 0 316.8-91.008 90.88-228.672 151.424-330.112 149.056L407.296 750.08zm90.496-226.304c49.536 49.536 233.344-7.04 339.392-113.088 78.208-78.208 63.232-163.072 0-226.304-63.168-63.232-148.032-78.208-226.24 0C504.896 290.496 448.32 474.368 497.792 523.84zM244.864 708.928a64 64 0 1 0-59.84 59.84l56.32-3.52 3.52-56.32zm8.064 127.68a64 64 0 1 0 59.84-59.84l-56.32 3.52-3.52 56.32z"},null,-1);function Rs(e,t,n,o,r,s){return c(),i("svg",Ps,[Ds])}k1.render=Rs;k1.__file="packages/components/Chicken.vue";const Us=k1;var V1=l({name:"CircleCheckFilled"});const Gs={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Ws=a("path",{fill:"currentColor",d:"M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896zm-55.808 536.384-99.52-99.584a38.4 38.4 0 1 0-54.336 54.336l126.72 126.72a38.272 38.272 0 0 0 54.336 0l262.4-262.464a38.4 38.4 0 1 0-54.272-54.336L456.192 600.384z"},null,-1);function Zs(e,t,n,o,r,s){return c(),i("svg",Gs,[Ws])}V1.render=Zs;V1.__file="packages/components/CircleCheckFilled.vue";const qs=V1;var b1=l({name:"CircleCheck"});const js={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Ys=a("path",{fill:"currentColor",d:"M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768zm0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896z"},null,-1),Ks=a("path",{fill:"currentColor",d:"M745.344 361.344a32 32 0 0 1 45.312 45.312l-288 288a32 32 0 0 1-45.312 0l-160-160a32 32 0 1 1 45.312-45.312L480 626.752l265.344-265.408z"},null,-1);function Xs(e,t,n,o,r,s){return c(),i("svg",js,[Ys,Ks])}b1.render=Xs;b1.__file="packages/components/CircleCheck.vue";const Js=b1;var y1=l({name:"Checked"});const Qs={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},ea=a("path",{fill:"currentColor",d:"M704 192h160v736H160V192h160.064v64H704v-64zM311.616 537.28l-45.312 45.248L447.36 763.52l316.8-316.8-45.312-45.184L447.36 673.024 311.616 537.28zM384 192V96h256v96H384z"},null,-1);function ta(e,t,n,o,r,s){return c(),i("svg",Qs,[ea])}y1.render=ta;y1.__file="packages/components/Checked.vue";const na=y1;var A1=l({name:"CircleCloseFilled"});const oa={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},ra=a("path",{fill:"currentColor",d:"M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896zm0 393.664L407.936 353.6a38.4 38.4 0 1 0-54.336 54.336L457.664 512 353.6 616.064a38.4 38.4 0 1 0 54.336 54.336L512 566.336 616.064 670.4a38.4 38.4 0 1 0 54.336-54.336L566.336 512 670.4 407.936a38.4 38.4 0 1 0-54.336-54.336L512 457.664z"},null,-1);function sa(e,t,n,o,r,s){return c(),i("svg",oa,[ra])}A1.render=sa;A1.__file="packages/components/CircleCloseFilled.vue";const aa=A1;var E1=l({name:"CircleClose"});const ca={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},la=a("path",{fill:"currentColor",d:"m466.752 512-90.496-90.496a32 32 0 0 1 45.248-45.248L512 466.752l90.496-90.496a32 32 0 1 1 45.248 45.248L557.248 512l90.496 90.496a32 32 0 1 1-45.248 45.248L512 557.248l-90.496 90.496a32 32 0 0 1-45.248-45.248L466.752 512z"},null,-1),ia=a("path",{fill:"currentColor",d:"M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768zm0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896z"},null,-1);function ua(e,t,n,o,r,s){return c(),i("svg",ca,[la,ia])}E1.render=ua;E1.__file="packages/components/CircleClose.vue";const pa=E1;var S1=l({name:"ArrowRight"});const _a={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},da=a("path",{fill:"currentColor",d:"M340.864 149.312a30.592 30.592 0 0 0 0 42.752L652.736 512 340.864 831.872a30.592 30.592 0 0 0 0 42.752 29.12 29.12 0 0 0 41.728 0L714.24 534.336a32 32 0 0 0 0-44.672L382.592 149.376a29.12 29.12 0 0 0-41.728 0z"},null,-1);function ha(e,t,n,o,r,s){return c(),i("svg",_a,[da])}S1.render=ha;S1.__file="packages/components/ArrowRight.vue";const $a=S1;var B1=l({name:"CirclePlus"});const fa={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},ma=a("path",{fill:"currentColor",d:"M352 480h320a32 32 0 1 1 0 64H352a32 32 0 0 1 0-64z"},null,-1),va=a("path",{fill:"currentColor",d:"M480 672V352a32 32 0 1 1 64 0v320a32 32 0 0 1-64 0z"},null,-1),ga=a("path",{fill:"currentColor",d:"M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768zm0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896z"},null,-1);function wa(e,t,n,o,r,s){return c(),i("svg",fa,[ma,va,ga])}B1.render=wa;B1.__file="packages/components/CirclePlus.vue";const za=B1;var F1=l({name:"Clock"});const xa={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Ca=a("path",{fill:"currentColor",d:"M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768zm0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896z"},null,-1),Ma=a("path",{fill:"currentColor",d:"M480 256a32 32 0 0 1 32 32v256a32 32 0 0 1-64 0V288a32 32 0 0 1 32-32z"},null,-1),La=a("path",{fill:"currentColor",d:"M480 512h256q32 0 32 32t-32 32H480q-32 0-32-32t32-32z"},null,-1);function Ha(e,t,n,o,r,s){return c(),i("svg",xa,[Ca,Ma,La])}F1.render=Ha;F1.__file="packages/components/Clock.vue";const ka=F1;var T1=l({name:"CloseBold"});const Va={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},ba=a("path",{fill:"currentColor",d:"M195.2 195.2a64 64 0 0 1 90.496 0L512 421.504 738.304 195.2a64 64 0 0 1 90.496 90.496L602.496 512 828.8 738.304a64 64 0 0 1-90.496 90.496L512 602.496 285.696 828.8a64 64 0 0 1-90.496-90.496L421.504 512 195.2 285.696a64 64 0 0 1 0-90.496z"},null,-1);function ya(e,t,n,o,r,s){return c(),i("svg",Va,[ba])}T1.render=ya;T1.__file="packages/components/CloseBold.vue";const Aa=T1;var I1=l({name:"Close"});const Ea={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Sa=a("path",{fill:"currentColor",d:"M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z"},null,-1);function Ba(e,t,n,o,r,s){return c(),i("svg",Ea,[Sa])}I1.render=Ba;I1.__file="packages/components/Close.vue";const Fa=I1;var O1=l({name:"Cloudy"});const Ta={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Ia=a("path",{fill:"currentColor",d:"M598.4 831.872H328.192a256 256 0 0 1-34.496-510.528A352 352 0 1 1 598.4 831.872zm-271.36-64h272.256a288 288 0 1 0-248.512-417.664L335.04 381.44l-34.816 3.584a192 192 0 0 0 26.88 382.848z"},null,-1);function Oa(e,t,n,o,r,s){return c(),i("svg",Ta,[Ia])}O1.render=Oa;O1.__file="packages/components/Cloudy.vue";const Na=O1;var N1=l({name:"CirclePlusFilled"});const Pa={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Da=a("path",{fill:"currentColor",d:"M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896zm-38.4 409.6H326.4a38.4 38.4 0 1 0 0 76.8h147.2v147.2a38.4 38.4 0 0 0 76.8 0V550.4h147.2a38.4 38.4 0 0 0 0-76.8H550.4V326.4a38.4 38.4 0 1 0-76.8 0v147.2z"},null,-1);function Ra(e,t,n,o,r,s){return c(),i("svg",Pa,[Da])}N1.render=Ra;N1.__file="packages/components/CirclePlusFilled.vue";const Ua=N1;var P1=l({name:"CoffeeCup"});const Ga={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Wa=a("path",{fill:"currentColor",d:"M768 192a192 192 0 1 1-8 383.808A256.128 256.128 0 0 1 512 768H320A256 256 0 0 1 64 512V160a32 32 0 0 1 32-32h640a32 32 0 0 1 32 32v32zm0 64v256a128 128 0 1 0 0-256zM96 832h640a32 32 0 1 1 0 64H96a32 32 0 1 1 0-64zm32-640v320a192 192 0 0 0 192 192h192a192 192 0 0 0 192-192V192H128z"},null,-1);function Za(e,t,n,o,r,s){return c(),i("svg",Ga,[Wa])}P1.render=Za;P1.__file="packages/components/CoffeeCup.vue";const qa=P1;var D1=l({name:"ColdDrink"});const ja={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Ya=a("path",{fill:"currentColor",d:"M768 64a192 192 0 1 1-69.952 370.88L480 725.376V896h96a32 32 0 1 1 0 64H320a32 32 0 1 1 0-64h96V725.376L76.8 273.536a64 64 0 0 1-12.8-38.4v-10.688a32 32 0 0 1 32-32h71.808l-65.536-83.84a32 32 0 0 1 50.432-39.424l96.256 123.264h337.728A192.064 192.064 0 0 1 768 64zM656.896 192.448H800a32 32 0 0 1 32 32v10.624a64 64 0 0 1-12.8 38.4l-80.448 107.2a128 128 0 1 0-81.92-188.16v-.064zm-357.888 64 129.472 165.76a32 32 0 0 1-50.432 39.36l-160.256-205.12H144l304 404.928 304-404.928H299.008z"},null,-1);function Ka(e,t,n,o,r,s){return c(),i("svg",ja,[Ya])}D1.render=Ka;D1.__file="packages/components/ColdDrink.vue";const Xa=D1;var R1=l({name:"Coin"});const Ja={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Qa=a("path",{fill:"currentColor",d:"m161.92 580.736 29.888 58.88C171.328 659.776 160 681.728 160 704c0 82.304 155.328 160 352 160s352-77.696 352-160c0-22.272-11.392-44.16-31.808-64.32l30.464-58.432C903.936 615.808 928 657.664 928 704c0 129.728-188.544 224-416 224S96 833.728 96 704c0-46.592 24.32-88.576 65.92-123.264z"},null,-1),ec=a("path",{fill:"currentColor",d:"m161.92 388.736 29.888 58.88C171.328 467.84 160 489.792 160 512c0 82.304 155.328 160 352 160s352-77.696 352-160c0-22.272-11.392-44.16-31.808-64.32l30.464-58.432C903.936 423.808 928 465.664 928 512c0 129.728-188.544 224-416 224S96 641.728 96 512c0-46.592 24.32-88.576 65.92-123.264z"},null,-1),tc=a("path",{fill:"currentColor",d:"M512 544c-227.456 0-416-94.272-416-224S284.544 96 512 96s416 94.272 416 224-188.544 224-416 224zm0-64c196.672 0 352-77.696 352-160S708.672 160 512 160s-352 77.696-352 160 155.328 160 352 160z"},null,-1);function nc(e,t,n,o,r,s){return c(),i("svg",Ja,[Qa,ec,tc])}R1.render=nc;R1.__file="packages/components/Coin.vue";const oc=R1;var U1=l({name:"ArrowUpBold"});const rc={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},sc=a("path",{fill:"currentColor",d:"M104.704 685.248a64 64 0 0 0 90.496 0l316.8-316.8 316.8 316.8a64 64 0 0 0 90.496-90.496L557.248 232.704a64 64 0 0 0-90.496 0L104.704 594.752a64 64 0 0 0 0 90.496z"},null,-1);function ac(e,t,n,o,r,s){return c(),i("svg",rc,[sc])}U1.render=ac;U1.__file="packages/components/ArrowUpBold.vue";const cc=U1;var G1=l({name:"CollectionTag"});const lc={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},ic=a("path",{fill:"currentColor",d:"M256 128v698.88l196.032-156.864a96 96 0 0 1 119.936 0L768 826.816V128H256zm-32-64h576a32 32 0 0 1 32 32v797.44a32 32 0 0 1-51.968 24.96L531.968 720a32 32 0 0 0-39.936 0L243.968 918.4A32 32 0 0 1 192 893.44V96a32 32 0 0 1 32-32z"},null,-1);function uc(e,t,n,o,r,s){return c(),i("svg",lc,[ic])}G1.render=uc;G1.__file="packages/components/CollectionTag.vue";const pc=G1;var W1=l({name:"BottomRight"});const _c={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},dc=a("path",{fill:"currentColor",d:"M352 768a32 32 0 1 0 0 64h448a32 32 0 0 0 32-32V352a32 32 0 0 0-64 0v416H352z"},null,-1),hc=a("path",{fill:"currentColor",d:"M777.344 822.656a32 32 0 0 0 45.312-45.312l-544-544a32 32 0 0 0-45.312 45.312l544 544z"},null,-1);function $c(e,t,n,o,r,s){return c(),i("svg",_c,[dc,hc])}W1.render=$c;W1.__file="packages/components/BottomRight.vue";const fc=W1;var Z1=l({name:"Coffee"});const mc={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},vc=a("path",{fill:"currentColor",d:"M822.592 192h14.272a32 32 0 0 1 31.616 26.752l21.312 128A32 32 0 0 1 858.24 384h-49.344l-39.04 546.304A32 32 0 0 1 737.92 960H285.824a32 32 0 0 1-32-29.696L214.912 384H165.76a32 32 0 0 1-31.552-37.248l21.312-128A32 32 0 0 1 187.136 192h14.016l-6.72-93.696A32 32 0 0 1 226.368 64h571.008a32 32 0 0 1 31.936 34.304L822.592 192zm-64.128 0 4.544-64H260.736l4.544 64h493.184zm-548.16 128H820.48l-10.688-64H214.208l-10.688 64h6.784zm68.736 64 36.544 512H708.16l36.544-512H279.04z"},null,-1);function gc(e,t,n,o,r,s){return c(),i("svg",mc,[vc])}Z1.render=gc;Z1.__file="packages/components/Coffee.vue";const wc=Z1;var q1=l({name:"CameraFilled"});const zc={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},xc=a("path",{fill:"currentColor",d:"M160 224a64 64 0 0 0-64 64v512a64 64 0 0 0 64 64h704a64 64 0 0 0 64-64V288a64 64 0 0 0-64-64H748.416l-46.464-92.672A64 64 0 0 0 644.736 96H379.328a64 64 0 0 0-57.216 35.392L275.776 224H160zm352 435.2a115.2 115.2 0 1 0 0-230.4 115.2 115.2 0 0 0 0 230.4zm0 140.8a256 256 0 1 1 0-512 256 256 0 0 1 0 512z"},null,-1);function Cc(e,t,n,o,r,s){return c(),i("svg",zc,[xc])}q1.render=Cc;q1.__file="packages/components/CameraFilled.vue";const Mc=q1;var j1=l({name:"Collection"});const Lc={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Hc=a("path",{fill:"currentColor",d:"M192 736h640V128H256a64 64 0 0 0-64 64v544zm64-672h608a32 32 0 0 1 32 32v672a32 32 0 0 1-32 32H160l-32 57.536V192A128 128 0 0 1 256 64z"},null,-1),kc=a("path",{fill:"currentColor",d:"M240 800a48 48 0 1 0 0 96h592v-96H240zm0-64h656v160a64 64 0 0 1-64 64H240a112 112 0 0 1 0-224zm144-608v250.88l96-76.8 96 76.8V128H384zm-64-64h320v381.44a32 32 0 0 1-51.968 24.96L480 384l-108.032 86.4A32 32 0 0 1 320 445.44V64z"},null,-1);function Vc(e,t,n,o,r,s){return c(),i("svg",Lc,[Hc,kc])}j1.render=Vc;j1.__file="packages/components/Collection.vue";const bc=j1;var Y1=l({name:"Cpu"});const yc={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Ac=a("path",{fill:"currentColor",d:"M320 256a64 64 0 0 0-64 64v384a64 64 0 0 0 64 64h384a64 64 0 0 0 64-64V320a64 64 0 0 0-64-64H320zm0-64h384a128 128 0 0 1 128 128v384a128 128 0 0 1-128 128H320a128 128 0 0 1-128-128V320a128 128 0 0 1 128-128z"},null,-1),Ec=a("path",{fill:"currentColor",d:"M512 64a32 32 0 0 1 32 32v128h-64V96a32 32 0 0 1 32-32zm160 0a32 32 0 0 1 32 32v128h-64V96a32 32 0 0 1 32-32zm-320 0a32 32 0 0 1 32 32v128h-64V96a32 32 0 0 1 32-32zm160 896a32 32 0 0 1-32-32V800h64v128a32 32 0 0 1-32 32zm160 0a32 32 0 0 1-32-32V800h64v128a32 32 0 0 1-32 32zm-320 0a32 32 0 0 1-32-32V800h64v128a32 32 0 0 1-32 32zM64 512a32 32 0 0 1 32-32h128v64H96a32 32 0 0 1-32-32zm0-160a32 32 0 0 1 32-32h128v64H96a32 32 0 0 1-32-32zm0 320a32 32 0 0 1 32-32h128v64H96a32 32 0 0 1-32-32zm896-160a32 32 0 0 1-32 32H800v-64h128a32 32 0 0 1 32 32zm0-160a32 32 0 0 1-32 32H800v-64h128a32 32 0 0 1 32 32zm0 320a32 32 0 0 1-32 32H800v-64h128a32 32 0 0 1 32 32z"},null,-1);function Sc(e,t,n,o,r,s){return c(),i("svg",yc,[Ac,Ec])}Y1.render=Sc;Y1.__file="packages/components/Cpu.vue";const Bc=Y1;var K1=l({name:"Crop"});const Fc={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Tc=a("path",{fill:"currentColor",d:"M256 768h672a32 32 0 1 1 0 64H224a32 32 0 0 1-32-32V96a32 32 0 0 1 64 0v672z"},null,-1),Ic=a("path",{fill:"currentColor",d:"M832 224v704a32 32 0 1 1-64 0V256H96a32 32 0 0 1 0-64h704a32 32 0 0 1 32 32z"},null,-1);function Oc(e,t,n,o,r,s){return c(),i("svg",Fc,[Tc,Ic])}K1.render=Oc;K1.__file="packages/components/Crop.vue";const Nc=K1;var X1=l({name:"Coordinate"});const Pc={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Dc=a("path",{fill:"currentColor",d:"M480 512h64v320h-64z"},null,-1),Rc=a("path",{fill:"currentColor",d:"M192 896h640a64 64 0 0 0-64-64H256a64 64 0 0 0-64 64zm64-128h512a128 128 0 0 1 128 128v64H128v-64a128 128 0 0 1 128-128zm256-256a192 192 0 1 0 0-384 192 192 0 0 0 0 384zm0 64a256 256 0 1 1 0-512 256 256 0 0 1 0 512z"},null,-1);function Uc(e,t,n,o,r,s){return c(),i("svg",Pc,[Dc,Rc])}X1.render=Uc;X1.__file="packages/components/Coordinate.vue";const Gc=X1;var J1=l({name:"DArrowLeft"});const Wc={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Zc=a("path",{fill:"currentColor",d:"M529.408 149.376a29.12 29.12 0 0 1 41.728 0 30.592 30.592 0 0 1 0 42.688L259.264 511.936l311.872 319.936a30.592 30.592 0 0 1-.512 43.264 29.12 29.12 0 0 1-41.216-.512L197.76 534.272a32 32 0 0 1 0-44.672l331.648-340.224zm256 0a29.12 29.12 0 0 1 41.728 0 30.592 30.592 0 0 1 0 42.688L515.264 511.936l311.872 319.936a30.592 30.592 0 0 1-.512 43.264 29.12 29.12 0 0 1-41.216-.512L453.76 534.272a32 32 0 0 1 0-44.672l331.648-340.224z"},null,-1);function qc(e,t,n,o,r,s){return c(),i("svg",Wc,[Zc])}J1.render=qc;J1.__file="packages/components/DArrowLeft.vue";const jc=J1;var Q1=l({name:"Compass"});const Yc={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Kc=a("path",{fill:"currentColor",d:"M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768zm0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896z"},null,-1),Xc=a("path",{fill:"currentColor",d:"M725.888 315.008C676.48 428.672 624 513.28 568.576 568.64c-55.424 55.424-139.968 107.904-253.568 157.312a12.8 12.8 0 0 1-16.896-16.832c49.536-113.728 102.016-198.272 157.312-253.632 55.36-55.296 139.904-107.776 253.632-157.312a12.8 12.8 0 0 1 16.832 16.832z"},null,-1);function Jc(e,t,n,o,r,s){return c(),i("svg",Yc,[Kc,Xc])}Q1.render=Jc;Q1.__file="packages/components/Compass.vue";const Qc=Q1;var ee=l({name:"Connection"});const el={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},tl=a("path",{fill:"currentColor",d:"M640 384v64H448a128 128 0 0 0-128 128v128a128 128 0 0 0 128 128h320a128 128 0 0 0 128-128V576a128 128 0 0 0-64-110.848V394.88c74.56 26.368 128 97.472 128 181.056v128a192 192 0 0 1-192 192H448a192 192 0 0 1-192-192V576a192 192 0 0 1 192-192h192z"},null,-1),nl=a("path",{fill:"currentColor",d:"M384 640v-64h192a128 128 0 0 0 128-128V320a128 128 0 0 0-128-128H256a128 128 0 0 0-128 128v128a128 128 0 0 0 64 110.848v70.272A192.064 192.064 0 0 1 64 448V320a192 192 0 0 1 192-192h320a192 192 0 0 1 192 192v128a192 192 0 0 1-192 192H384z"},null,-1);function ol(e,t,n,o,r,s){return c(),i("svg",el,[tl,nl])}ee.render=ol;ee.__file="packages/components/Connection.vue";const rl=ee;var te=l({name:"CreditCard"});const sl={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},al=a("path",{fill:"currentColor",d:"M896 324.096c0-42.368-2.496-55.296-9.536-68.48a52.352 52.352 0 0 0-22.144-22.08c-13.12-7.04-26.048-9.536-68.416-9.536H228.096c-42.368 0-55.296 2.496-68.48 9.536a52.352 52.352 0 0 0-22.08 22.144c-7.04 13.12-9.536 26.048-9.536 68.416v375.808c0 42.368 2.496 55.296 9.536 68.48a52.352 52.352 0 0 0 22.144 22.08c13.12 7.04 26.048 9.536 68.416 9.536h567.808c42.368 0 55.296-2.496 68.48-9.536a52.352 52.352 0 0 0 22.08-22.144c7.04-13.12 9.536-26.048 9.536-68.416V324.096zm64 0v375.808c0 57.088-5.952 77.76-17.088 98.56-11.136 20.928-27.52 37.312-48.384 48.448-20.864 11.136-41.6 17.088-98.56 17.088H228.032c-57.088 0-77.76-5.952-98.56-17.088a116.288 116.288 0 0 1-48.448-48.384c-11.136-20.864-17.088-41.6-17.088-98.56V324.032c0-57.088 5.952-77.76 17.088-98.56 11.136-20.928 27.52-37.312 48.384-48.448 20.864-11.136 41.6-17.088 98.56-17.088H795.84c57.088 0 77.76 5.952 98.56 17.088 20.928 11.136 37.312 27.52 48.448 48.384 11.136 20.864 17.088 41.6 17.088 98.56z"},null,-1),cl=a("path",{fill:"currentColor",d:"M64 320h896v64H64v-64zm0 128h896v64H64v-64zm128 192h256v64H192z"},null,-1);function ll(e,t,n,o,r,s){return c(),i("svg",sl,[al,cl])}te.render=ll;te.__file="packages/components/CreditCard.vue";const il=te;var ne=l({name:"DataBoard"});const ul={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},pl=a("path",{fill:"currentColor",d:"M32 128h960v64H32z"},null,-1),_l=a("path",{fill:"currentColor",d:"M192 192v512h640V192H192zm-64-64h768v608a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V128z"},null,-1),dl=a("path",{fill:"currentColor",d:"M322.176 960H248.32l144.64-250.56 55.424 32L322.176 960zm453.888 0h-73.856L576 741.44l55.424-32L776.064 960z"},null,-1);function hl(e,t,n,o,r,s){return c(),i("svg",ul,[pl,_l,dl])}ne.render=hl;ne.__file="packages/components/DataBoard.vue";const $l=ne;var oe=l({name:"DArrowRight"});const fl={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},ml=a("path",{fill:"currentColor",d:"M452.864 149.312a29.12 29.12 0 0 1 41.728.064L826.24 489.664a32 32 0 0 1 0 44.672L494.592 874.624a29.12 29.12 0 0 1-41.728 0 30.592 30.592 0 0 1 0-42.752L764.736 512 452.864 192a30.592 30.592 0 0 1 0-42.688zm-256 0a29.12 29.12 0 0 1 41.728.064L570.24 489.664a32 32 0 0 1 0 44.672L238.592 874.624a29.12 29.12 0 0 1-41.728 0 30.592 30.592 0 0 1 0-42.752L508.736 512 196.864 192a30.592 30.592 0 0 1 0-42.688z"},null,-1);function vl(e,t,n,o,r,s){return c(),i("svg",fl,[ml])}oe.render=vl;oe.__file="packages/components/DArrowRight.vue";const gl=oe;var re=l({name:"Dessert"});const wl={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},zl=a("path",{fill:"currentColor",d:"M128 416v-48a144 144 0 0 1 168.64-141.888 224.128 224.128 0 0 1 430.72 0A144 144 0 0 1 896 368v48a384 384 0 0 1-352 382.72V896h-64v-97.28A384 384 0 0 1 128 416zm287.104-32.064h193.792a143.808 143.808 0 0 1 58.88-132.736 160.064 160.064 0 0 0-311.552 0 143.808 143.808 0 0 1 58.88 132.8zm-72.896 0a72 72 0 1 0-140.48 0h140.48zm339.584 0h140.416a72 72 0 1 0-140.48 0zM512 736a320 320 0 0 0 318.4-288.064H193.6A320 320 0 0 0 512 736zM384 896.064h256a32 32 0 1 1 0 64H384a32 32 0 1 1 0-64z"},null,-1);function xl(e,t,n,o,r,s){return c(),i("svg",wl,[zl])}re.render=xl;re.__file="packages/components/Dessert.vue";const Cl=re;var se=l({name:"DeleteLocation"});const Ml={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Ll=a("path",{fill:"currentColor",d:"M288 896h448q32 0 32 32t-32 32H288q-32 0-32-32t32-32z"},null,-1),Hl=a("path",{fill:"currentColor",d:"M800 416a288 288 0 1 0-576 0c0 118.144 94.528 272.128 288 456.576C705.472 688.128 800 534.144 800 416zM512 960C277.312 746.688 160 565.312 160 416a352 352 0 0 1 704 0c0 149.312-117.312 330.688-352 544z"},null,-1),kl=a("path",{fill:"currentColor",d:"M384 384h256q32 0 32 32t-32 32H384q-32 0-32-32t32-32z"},null,-1);function Vl(e,t,n,o,r,s){return c(),i("svg",Ml,[Ll,Hl,kl])}se.render=Vl;se.__file="packages/components/DeleteLocation.vue";const bl=se;var ae=l({name:"DCaret"});const yl={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Al=a("path",{fill:"currentColor",d:"m512 128 288 320H224l288-320zM224 576h576L512 896 224 576z"},null,-1);function El(e,t,n,o,r,s){return c(),i("svg",yl,[Al])}ae.render=El;ae.__file="packages/components/DCaret.vue";const Sl=ae;var ce=l({name:"Delete"});const Bl={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Fl=a("path",{fill:"currentColor",d:"M160 256H96a32 32 0 0 1 0-64h256V95.936a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32V192h256a32 32 0 1 1 0 64h-64v672a32 32 0 0 1-32 32H192a32 32 0 0 1-32-32V256zm448-64v-64H416v64h192zM224 896h576V256H224v640zm192-128a32 32 0 0 1-32-32V416a32 32 0 0 1 64 0v320a32 32 0 0 1-32 32zm192 0a32 32 0 0 1-32-32V416a32 32 0 0 1 64 0v320a32 32 0 0 1-32 32z"},null,-1);function Tl(e,t,n,o,r,s){return c(),i("svg",Bl,[Fl])}ce.render=Tl;ce.__file="packages/components/Delete.vue";const Il=ce;var le=l({name:"Dish"});const Ol={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Nl=a("path",{fill:"currentColor",d:"M480 257.152V192h-96a32 32 0 0 1 0-64h256a32 32 0 1 1 0 64h-96v65.152A448 448 0 0 1 955.52 768H68.48A448 448 0 0 1 480 257.152zM128 704h768a384 384 0 1 0-768 0zM96 832h832a32 32 0 1 1 0 64H96a32 32 0 1 1 0-64z"},null,-1);function Pl(e,t,n,o,r,s){return c(),i("svg",Ol,[Nl])}le.render=Pl;le.__file="packages/components/Dish.vue";const Dl=le;var ie=l({name:"DishDot"});const Rl={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Ul=a("path",{fill:"currentColor",d:"m384.064 274.56.064-50.688A128 128 0 0 1 512.128 96c70.528 0 127.68 57.152 127.68 127.68v50.752A448.192 448.192 0 0 1 955.392 768H68.544A448.192 448.192 0 0 1 384 274.56zM96 832h832a32 32 0 1 1 0 64H96a32 32 0 1 1 0-64zm32-128h768a384 384 0 1 0-768 0zm447.808-448v-32.32a63.68 63.68 0 0 0-63.68-63.68 64 64 0 0 0-64 63.936V256h127.68z"},null,-1);function Gl(e,t,n,o,r,s){return c(),i("svg",Rl,[Ul])}ie.render=Gl;ie.__file="packages/components/DishDot.vue";const Wl=ie;var ue=l({name:"DocumentCopy"});const Zl={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},ql=a("path",{fill:"currentColor",d:"M128 320v576h576V320H128zm-32-64h640a32 32 0 0 1 32 32v640a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V288a32 32 0 0 1 32-32zM960 96v704a32 32 0 0 1-32 32h-96v-64h64V128H384v64h-64V96a32 32 0 0 1 32-32h576a32 32 0 0 1 32 32zM256 672h320v64H256v-64zm0-192h320v64H256v-64z"},null,-1);function jl(e,t,n,o,r,s){return c(),i("svg",Zl,[ql])}ue.render=jl;ue.__file="packages/components/DocumentCopy.vue";const Yl=ue;var pe=l({name:"Discount"});const Kl={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Xl=a("path",{fill:"currentColor",d:"M224 704h576V318.336L552.512 115.84a64 64 0 0 0-81.024 0L224 318.336V704zm0 64v128h576V768H224zM593.024 66.304l259.2 212.096A32 32 0 0 1 864 303.168V928a32 32 0 0 1-32 32H192a32 32 0 0 1-32-32V303.168a32 32 0 0 1 11.712-24.768l259.2-212.096a128 128 0 0 1 162.112 0z"},null,-1),Jl=a("path",{fill:"currentColor",d:"M512 448a64 64 0 1 0 0-128 64 64 0 0 0 0 128zm0 64a128 128 0 1 1 0-256 128 128 0 0 1 0 256z"},null,-1);function Ql(e,t,n,o,r,s){return c(),i("svg",Kl,[Xl,Jl])}pe.render=Ql;pe.__file="packages/components/Discount.vue";const ei=pe;var _e=l({name:"DocumentChecked"});const ti={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},ni=a("path",{fill:"currentColor",d:"M805.504 320 640 154.496V320h165.504zM832 384H576V128H192v768h640V384zM160 64h480l256 256v608a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32zm318.4 582.144 180.992-180.992L704.64 510.4 478.4 736.64 320 578.304l45.248-45.312L478.4 646.144z"},null,-1);function oi(e,t,n,o,r,s){return c(),i("svg",ti,[ni])}_e.render=oi;_e.__file="packages/components/DocumentChecked.vue";const ri=_e;var de=l({name:"DocumentAdd"});const si={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},ai=a("path",{fill:"currentColor",d:"M832 384H576V128H192v768h640V384zm-26.496-64L640 154.496V320h165.504zM160 64h480l256 256v608a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32zm320 512V448h64v128h128v64H544v128h-64V640H352v-64h128z"},null,-1);function ci(e,t,n,o,r,s){return c(),i("svg",si,[ai])}de.render=ci;de.__file="packages/components/DocumentAdd.vue";const li=de;var he=l({name:"DocumentRemove"});const ii={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},ui=a("path",{fill:"currentColor",d:"M805.504 320 640 154.496V320h165.504zM832 384H576V128H192v768h640V384zM160 64h480l256 256v608a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32zm192 512h320v64H352v-64z"},null,-1);function pi(e,t,n,o,r,s){return c(),i("svg",ii,[ui])}he.render=pi;he.__file="packages/components/DocumentRemove.vue";const _i=he;var $e=l({name:"DataAnalysis"});const di={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},hi=a("path",{fill:"currentColor",d:"m665.216 768 110.848 192h-73.856L591.36 768H433.024L322.176 960H248.32l110.848-192H160a32 32 0 0 1-32-32V192H64a32 32 0 0 1 0-64h896a32 32 0 1 1 0 64h-64v544a32 32 0 0 1-32 32H665.216zM832 192H192v512h640V192zM352 448a32 32 0 0 1 32 32v64a32 32 0 0 1-64 0v-64a32 32 0 0 1 32-32zm160-64a32 32 0 0 1 32 32v128a32 32 0 0 1-64 0V416a32 32 0 0 1 32-32zm160-64a32 32 0 0 1 32 32v192a32 32 0 1 1-64 0V352a32 32 0 0 1 32-32z"},null,-1);function $i(e,t,n,o,r,s){return c(),i("svg",di,[hi])}$e.render=$i;$e.__file="packages/components/DataAnalysis.vue";const fi=$e;var fe=l({name:"DeleteFilled"});const mi={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},vi=a("path",{fill:"currentColor",d:"M352 192V95.936a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32V192h256a32 32 0 1 1 0 64H96a32 32 0 0 1 0-64h256zm64 0h192v-64H416v64zM192 960a32 32 0 0 1-32-32V256h704v672a32 32 0 0 1-32 32H192zm224-192a32 32 0 0 0 32-32V416a32 32 0 0 0-64 0v320a32 32 0 0 0 32 32zm192 0a32 32 0 0 0 32-32V416a32 32 0 0 0-64 0v320a32 32 0 0 0 32 32z"},null,-1);function gi(e,t,n,o,r,s){return c(),i("svg",mi,[vi])}fe.render=gi;fe.__file="packages/components/DeleteFilled.vue";const wi=fe;var me=l({name:"Download"});const zi={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},xi=a("path",{fill:"currentColor",d:"M160 832h704a32 32 0 1 1 0 64H160a32 32 0 1 1 0-64zm384-253.696 236.288-236.352 45.248 45.248L508.8 704 192 387.2l45.248-45.248L480 584.704V128h64v450.304z"},null,-1);function Ci(e,t,n,o,r,s){return c(),i("svg",zi,[xi])}me.render=Ci;me.__file="packages/components/Download.vue";const Mi=me;var ve=l({name:"Drizzling"});const Li={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Hi=a("path",{fill:"currentColor",d:"m739.328 291.328-35.2-6.592-12.8-33.408a192.064 192.064 0 0 0-365.952 23.232l-9.92 40.896-41.472 7.04a176.32 176.32 0 0 0-146.24 173.568c0 97.28 78.72 175.936 175.808 175.936h400a192 192 0 0 0 35.776-380.672zM959.552 480a256 256 0 0 1-256 256h-400A239.808 239.808 0 0 1 63.744 496.192a240.32 240.32 0 0 1 199.488-236.8 256.128 256.128 0 0 1 487.872-30.976A256.064 256.064 0 0 1 959.552 480zM288 800h64v64h-64v-64zm192 0h64v64h-64v-64zm-96 96h64v64h-64v-64zm192 0h64v64h-64v-64zm96-96h64v64h-64v-64z"},null,-1);function ki(e,t,n,o,r,s){return c(),i("svg",Li,[Hi])}ve.render=ki;ve.__file="packages/components/Drizzling.vue";const Vi=ve;var ge=l({name:"Eleme"});const bi={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},yi=a("path",{fill:"currentColor",d:"M300.032 188.8c174.72-113.28 408-63.36 522.24 109.44 5.76 10.56 11.52 20.16 17.28 30.72v.96a22.4 22.4 0 0 1-7.68 26.88l-352.32 228.48c-9.6 6.72-22.08 3.84-28.8-5.76l-18.24-27.84a54.336 54.336 0 0 1 16.32-74.88l225.6-146.88c9.6-6.72 12.48-19.2 5.76-28.8-.96-1.92-1.92-3.84-3.84-4.8a267.84 267.84 0 0 0-315.84-17.28c-123.84 81.6-159.36 247.68-78.72 371.52a268.096 268.096 0 0 0 370.56 78.72 54.336 54.336 0 0 1 74.88 16.32l17.28 26.88c5.76 9.6 3.84 21.12-4.8 27.84-8.64 7.68-18.24 14.4-28.8 21.12a377.92 377.92 0 0 1-522.24-110.4c-113.28-174.72-63.36-408 111.36-522.24zm526.08 305.28a22.336 22.336 0 0 1 28.8 5.76l23.04 35.52a63.232 63.232 0 0 1-18.24 87.36l-35.52 23.04c-9.6 6.72-22.08 3.84-28.8-5.76l-46.08-71.04c-6.72-9.6-3.84-22.08 5.76-28.8l71.04-46.08z"},null,-1);function Ai(e,t,n,o,r,s){return c(),i("svg",bi,[yi])}ge.render=Ai;ge.__file="packages/components/Eleme.vue";const Ei=ge;var we=l({name:"ElemeFilled"});const Si={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Bi=a("path",{fill:"currentColor",d:"M176 64h672c61.824 0 112 50.176 112 112v672a112 112 0 0 1-112 112H176A112 112 0 0 1 64 848V176c0-61.824 50.176-112 112-112zm150.528 173.568c-152.896 99.968-196.544 304.064-97.408 456.96a330.688 330.688 0 0 0 456.96 96.64c9.216-5.888 17.6-11.776 25.152-18.56a18.24 18.24 0 0 0 4.224-24.32L700.352 724.8a47.552 47.552 0 0 0-65.536-14.272A234.56 234.56 0 0 1 310.592 641.6C240 533.248 271.104 387.968 379.456 316.48a234.304 234.304 0 0 1 276.352 15.168c1.664.832 2.56 2.56 3.392 4.224 5.888 8.384 3.328 19.328-5.12 25.216L456.832 489.6a47.552 47.552 0 0 0-14.336 65.472l16 24.384c5.888 8.384 16.768 10.88 25.216 5.056l308.224-199.936a19.584 19.584 0 0 0 6.72-23.488v-.896c-4.992-9.216-10.048-17.6-15.104-26.88-99.968-151.168-304.064-194.88-456.96-95.744zM786.88 504.704l-62.208 40.32c-8.32 5.888-10.88 16.768-4.992 25.216L760 632.32c5.888 8.448 16.768 11.008 25.152 5.12l31.104-20.16a55.36 55.36 0 0 0 16-76.48l-20.224-31.04a19.52 19.52 0 0 0-25.152-5.12z"},null,-1);function Fi(e,t,n,o,r,s){return c(),i("svg",Si,[Bi])}we.render=Fi;we.__file="packages/components/ElemeFilled.vue";const Ti=we;var ze=l({name:"Edit"});const Ii={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Oi=a("path",{fill:"currentColor",d:"M832 512a32 32 0 1 1 64 0v352a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h352a32 32 0 0 1 0 64H192v640h640V512z"},null,-1),Ni=a("path",{fill:"currentColor",d:"m469.952 554.24 52.8-7.552L847.104 222.4a32 32 0 1 0-45.248-45.248L477.44 501.44l-7.552 52.8zm422.4-422.4a96 96 0 0 1 0 135.808l-331.84 331.84a32 32 0 0 1-18.112 9.088L436.8 623.68a32 32 0 0 1-36.224-36.224l15.104-105.6a32 32 0 0 1 9.024-18.112l331.904-331.84a96 96 0 0 1 135.744 0z"},null,-1);function Pi(e,t,n,o,r,s){return c(),i("svg",Ii,[Oi,Ni])}ze.render=Pi;ze.__file="packages/components/Edit.vue";const Di=ze;var xe=l({name:"Failed"});const Ri={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Ui=a("path",{fill:"currentColor",d:"m557.248 608 135.744-135.744-45.248-45.248-135.68 135.744-135.808-135.68-45.248 45.184L466.752 608l-135.68 135.68 45.184 45.312L512 653.248l135.744 135.744 45.248-45.248L557.312 608zM704 192h160v736H160V192h160v64h384v-64zm-320 0V96h256v96H384z"},null,-1);function Gi(e,t,n,o,r,s){return c(),i("svg",Ri,[Ui])}xe.render=Gi;xe.__file="packages/components/Failed.vue";const Wi=xe;var Ce=l({name:"Expand"});const Zi={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},qi=a("path",{fill:"currentColor",d:"M128 192h768v128H128V192zm0 256h512v128H128V448zm0 256h768v128H128V704zm576-352 192 160-192 128V352z"},null,-1);function ji(e,t,n,o,r,s){return c(),i("svg",Zi,[qi])}Ce.render=ji;Ce.__file="packages/components/Expand.vue";const Yi=Ce;var Me=l({name:"Female"});const Ki={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Xi=a("path",{fill:"currentColor",d:"M512 640a256 256 0 1 0 0-512 256 256 0 0 0 0 512zm0 64a320 320 0 1 1 0-640 320 320 0 0 1 0 640z"},null,-1),Ji=a("path",{fill:"currentColor",d:"M512 640q32 0 32 32v256q0 32-32 32t-32-32V672q0-32 32-32z"},null,-1),Qi=a("path",{fill:"currentColor",d:"M352 800h320q32 0 32 32t-32 32H352q-32 0-32-32t32-32z"},null,-1);function e5(e,t,n,o,r,s){return c(),i("svg",Ki,[Xi,Ji,Qi])}Me.render=e5;Me.__file="packages/components/Female.vue";const t5=Me;var Le=l({name:"Document"});const n5={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},o5=a("path",{fill:"currentColor",d:"M832 384H576V128H192v768h640V384zm-26.496-64L640 154.496V320h165.504zM160 64h480l256 256v608a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32zm160 448h384v64H320v-64zm0-192h160v64H320v-64zm0 384h384v64H320v-64z"},null,-1);function r5(e,t,n,o,r,s){return c(),i("svg",n5,[o5])}Le.render=r5;Le.__file="packages/components/Document.vue";const s5=Le;var He=l({name:"Film"});const a5={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},c5=a("path",{fill:"currentColor",d:"M160 160v704h704V160H160zm-32-64h768a32 32 0 0 1 32 32v768a32 32 0 0 1-32 32H128a32 32 0 0 1-32-32V128a32 32 0 0 1 32-32z"},null,-1),l5=a("path",{fill:"currentColor",d:"M320 288V128h64v352h256V128h64v160h160v64H704v128h160v64H704v128h160v64H704v160h-64V544H384v352h-64V736H128v-64h192V544H128v-64h192V352H128v-64h192z"},null,-1);function i5(e,t,n,o,r,s){return c(),i("svg",a5,[c5,l5])}He.render=i5;He.__file="packages/components/Film.vue";const u5=He;var ke=l({name:"Finished"});const p5={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},_5=a("path",{fill:"currentColor",d:"M280.768 753.728 691.456 167.04a32 32 0 1 1 52.416 36.672L314.24 817.472a32 32 0 0 1-45.44 7.296l-230.4-172.8a32 32 0 0 1 38.4-51.2l203.968 152.96zM736 448a32 32 0 1 1 0-64h192a32 32 0 1 1 0 64H736zM608 640a32 32 0 0 1 0-64h319.936a32 32 0 1 1 0 64H608zM480 832a32 32 0 1 1 0-64h447.936a32 32 0 1 1 0 64H480z"},null,-1);function d5(e,t,n,o,r,s){return c(),i("svg",p5,[_5])}ke.render=d5;ke.__file="packages/components/Finished.vue";const h5=ke;var Ve=l({name:"DataLine"});const $5={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},f5=a("path",{fill:"currentColor",d:"M359.168 768H160a32 32 0 0 1-32-32V192H64a32 32 0 0 1 0-64h896a32 32 0 1 1 0 64h-64v544a32 32 0 0 1-32 32H665.216l110.848 192h-73.856L591.36 768H433.024L322.176 960H248.32l110.848-192zM832 192H192v512h640V192zM342.656 534.656a32 32 0 1 1-45.312-45.312L444.992 341.76l125.44 94.08L679.04 300.032a32 32 0 1 1 49.92 39.936L581.632 524.224 451.008 426.24 342.656 534.592z"},null,-1);function m5(e,t,n,o,r,s){return c(),i("svg",$5,[f5])}Ve.render=m5;Ve.__file="packages/components/DataLine.vue";const v5=Ve;var be=l({name:"Filter"});const g5={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},w5=a("path",{fill:"currentColor",d:"M384 523.392V928a32 32 0 0 0 46.336 28.608l192-96A32 32 0 0 0 640 832V523.392l280.768-343.104a32 32 0 1 0-49.536-40.576l-288 352A32 32 0 0 0 576 512v300.224l-128 64V512a32 32 0 0 0-7.232-20.288L195.52 192H704a32 32 0 1 0 0-64H128a32 32 0 0 0-24.768 52.288L384 523.392z"},null,-1);function z5(e,t,n,o,r,s){return c(),i("svg",g5,[w5])}be.render=z5;be.__file="packages/components/Filter.vue";const x5=be;var ye=l({name:"Flag"});const C5={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},M5=a("path",{fill:"currentColor",d:"M288 128h608L736 384l160 256H288v320h-96V64h96v64z"},null,-1);function L5(e,t,n,o,r,s){return c(),i("svg",C5,[M5])}ye.render=L5;ye.__file="packages/components/Flag.vue";const H5=ye;var Ae=l({name:"FolderChecked"});const k5={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},V5=a("path",{fill:"currentColor",d:"M128 192v640h768V320H485.76L357.504 192H128zm-32-64h287.872l128.384 128H928a32 32 0 0 1 32 32v576a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32zm414.08 502.144 180.992-180.992L736.32 494.4 510.08 720.64l-158.4-158.336 45.248-45.312L510.08 630.144z"},null,-1);function b5(e,t,n,o,r,s){return c(),i("svg",k5,[V5])}Ae.render=b5;Ae.__file="packages/components/FolderChecked.vue";const y5=Ae;var Ee=l({name:"FirstAidKit"});const A5={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},E5=a("path",{fill:"currentColor",d:"M192 256a64 64 0 0 0-64 64v448a64 64 0 0 0 64 64h640a64 64 0 0 0 64-64V320a64 64 0 0 0-64-64H192zm0-64h640a128 128 0 0 1 128 128v448a128 128 0 0 1-128 128H192A128 128 0 0 1 64 768V320a128 128 0 0 1 128-128z"},null,-1),S5=a("path",{fill:"currentColor",d:"M544 512h96a32 32 0 0 1 0 64h-96v96a32 32 0 0 1-64 0v-96h-96a32 32 0 0 1 0-64h96v-96a32 32 0 0 1 64 0v96zM352 128v64h320v-64H352zm-32-64h384a32 32 0 0 1 32 32v128a32 32 0 0 1-32 32H320a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32z"},null,-1);function B5(e,t,n,o,r,s){return c(),i("svg",A5,[E5,S5])}Ee.render=B5;Ee.__file="packages/components/FirstAidKit.vue";const F5=Ee;var Se=l({name:"FolderAdd"});const T5={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},I5=a("path",{fill:"currentColor",d:"M128 192v640h768V320H485.76L357.504 192H128zm-32-64h287.872l128.384 128H928a32 32 0 0 1 32 32v576a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32zm384 416V416h64v128h128v64H544v128h-64V608H352v-64h128z"},null,-1);function O5(e,t,n,o,r,s){return c(),i("svg",T5,[I5])}Se.render=O5;Se.__file="packages/components/FolderAdd.vue";const N5=Se;var Be=l({name:"Fold"});const P5={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},D5=a("path",{fill:"currentColor",d:"M896 192H128v128h768V192zm0 256H384v128h512V448zm0 256H128v128h768V704zM320 384 128 512l192 128V384z"},null,-1);function R5(e,t,n,o,r,s){return c(),i("svg",P5,[D5])}Be.render=R5;Be.__file="packages/components/Fold.vue";const U5=Be;var Fe=l({name:"FolderDelete"});const G5={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},W5=a("path",{fill:"currentColor",d:"M128 192v640h768V320H485.76L357.504 192H128zm-32-64h287.872l128.384 128H928a32 32 0 0 1 32 32v576a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32zm370.752 448-90.496-90.496 45.248-45.248L512 530.752l90.496-90.496 45.248 45.248L557.248 576l90.496 90.496-45.248 45.248L512 621.248l-90.496 90.496-45.248-45.248L466.752 576z"},null,-1);function Z5(e,t,n,o,r,s){return c(),i("svg",G5,[W5])}Fe.render=Z5;Fe.__file="packages/components/FolderDelete.vue";const q5=Fe;var Te=l({name:"DocumentDelete"});const j5={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Y5=a("path",{fill:"currentColor",d:"M805.504 320 640 154.496V320h165.504zM832 384H576V128H192v768h640V384zM160 64h480l256 256v608a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32zm308.992 546.304-90.496-90.624 45.248-45.248 90.56 90.496 90.496-90.432 45.248 45.248-90.496 90.56 90.496 90.496-45.248 45.248-90.496-90.496-90.56 90.496-45.248-45.248 90.496-90.496z"},null,-1);function K5(e,t,n,o,r,s){return c(),i("svg",j5,[Y5])}Te.render=K5;Te.__file="packages/components/DocumentDelete.vue";const X5=Te;var Ie=l({name:"Folder"});const J5={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Q5=a("path",{fill:"currentColor",d:"M128 192v640h768V320H485.76L357.504 192H128zm-32-64h287.872l128.384 128H928a32 32 0 0 1 32 32v576a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32z"},null,-1);function e9(e,t,n,o,r,s){return c(),i("svg",J5,[Q5])}Ie.render=e9;Ie.__file="packages/components/Folder.vue";const t9=Ie;var Oe=l({name:"Food"});const n9={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},o9=a("path",{fill:"currentColor",d:"M128 352.576V352a288 288 0 0 1 491.072-204.224 192 192 0 0 1 274.24 204.48 64 64 0 0 1 57.216 74.24C921.6 600.512 850.048 710.656 736 756.992V800a96 96 0 0 1-96 96H384a96 96 0 0 1-96-96v-43.008c-114.048-46.336-185.6-156.48-214.528-330.496A64 64 0 0 1 128 352.64zm64-.576h64a160 160 0 0 1 320 0h64a224 224 0 0 0-448 0zm128 0h192a96 96 0 0 0-192 0zm439.424 0h68.544A128.256 128.256 0 0 0 704 192c-15.36 0-29.952 2.688-43.52 7.616 11.328 18.176 20.672 37.76 27.84 58.304A64.128 64.128 0 0 1 759.424 352zM672 768H352v32a32 32 0 0 0 32 32h256a32 32 0 0 0 32-32v-32zm-342.528-64h365.056c101.504-32.64 165.76-124.928 192.896-288H136.576c27.136 163.072 91.392 255.36 192.896 288z"},null,-1);function r9(e,t,n,o,r,s){return c(),i("svg",n9,[o9])}Oe.render=r9;Oe.__file="packages/components/Food.vue";const s9=Oe;var Ne=l({name:"FolderOpened"});const a9={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},c9=a("path",{fill:"currentColor",d:"M878.08 448H241.92l-96 384h636.16l96-384zM832 384v-64H485.76L357.504 192H128v448l57.92-231.744A32 32 0 0 1 216.96 384H832zm-24.96 512H96a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h287.872l128.384 128H864a32 32 0 0 1 32 32v96h23.04a32 32 0 0 1 31.04 39.744l-112 448A32 32 0 0 1 807.04 896z"},null,-1);function l9(e,t,n,o,r,s){return c(),i("svg",a9,[c9])}Ne.render=l9;Ne.__file="packages/components/FolderOpened.vue";const i9=Ne;var Pe=l({name:"Football"});const u9={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},p9=a("path",{fill:"currentColor",d:"M512 960a448 448 0 1 1 0-896 448 448 0 0 1 0 896zm0-64a384 384 0 1 0 0-768 384 384 0 0 0 0 768z"},null,-1),_9=a("path",{fill:"currentColor",d:"M186.816 268.288c16-16.384 31.616-31.744 46.976-46.08 17.472 30.656 39.808 58.112 65.984 81.28l-32.512 56.448a385.984 385.984 0 0 1-80.448-91.648zm653.696-5.312a385.92 385.92 0 0 1-83.776 96.96l-32.512-56.384a322.923 322.923 0 0 0 68.48-85.76c15.552 14.08 31.488 29.12 47.808 45.184zM465.984 445.248l11.136-63.104a323.584 323.584 0 0 0 69.76 0l11.136 63.104a387.968 387.968 0 0 1-92.032 0zm-62.72-12.8A381.824 381.824 0 0 1 320 396.544l32-55.424a319.885 319.885 0 0 0 62.464 27.712l-11.2 63.488zm300.8-35.84a381.824 381.824 0 0 1-83.328 35.84l-11.2-63.552A319.885 319.885 0 0 0 672 341.184l32 55.424zm-520.768 364.8a385.92 385.92 0 0 1 83.968-97.28l32.512 56.32c-26.88 23.936-49.856 52.352-67.52 84.032-16-13.44-32.32-27.712-48.96-43.072zm657.536.128a1442.759 1442.759 0 0 1-49.024 43.072 321.408 321.408 0 0 0-67.584-84.16l32.512-56.32c33.216 27.456 61.696 60.352 84.096 97.408zM465.92 578.752a387.968 387.968 0 0 1 92.032 0l-11.136 63.104a323.584 323.584 0 0 0-69.76 0l-11.136-63.104zm-62.72 12.8 11.2 63.552a319.885 319.885 0 0 0-62.464 27.712L320 627.392a381.824 381.824 0 0 1 83.264-35.84zm300.8 35.84-32 55.424a318.272 318.272 0 0 0-62.528-27.712l11.2-63.488c29.44 8.64 57.28 20.736 83.264 35.776z"},null,-1);function d9(e,t,n,o,r,s){return c(),i("svg",u9,[p9,_9])}Pe.render=d9;Pe.__file="packages/components/Football.vue";const h9=Pe;var De=l({name:"FolderRemove"});const $9={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},f9=a("path",{fill:"currentColor",d:"M128 192v640h768V320H485.76L357.504 192H128zm-32-64h287.872l128.384 128H928a32 32 0 0 1 32 32v576a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32zm256 416h320v64H352v-64z"},null,-1);function m9(e,t,n,o,r,s){return c(),i("svg",$9,[f9])}De.render=m9;De.__file="packages/components/FolderRemove.vue";const v9=De;var Re=l({name:"Fries"});const g9={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},w9=a("path",{fill:"currentColor",d:"M608 224v-64a32 32 0 0 0-64 0v336h26.88A64 64 0 0 0 608 484.096V224zm101.12 160A64 64 0 0 0 672 395.904V384h64V224a32 32 0 1 0-64 0v160h37.12zm74.88 0a92.928 92.928 0 0 1 91.328 110.08l-60.672 323.584A96 96 0 0 1 720.32 896H303.68a96 96 0 0 1-94.336-78.336L148.672 494.08A92.928 92.928 0 0 1 240 384h-16V224a96 96 0 0 1 188.608-25.28A95.744 95.744 0 0 1 480 197.44V160a96 96 0 0 1 188.608-25.28A96 96 0 0 1 800 224v160h-16zM670.784 512a128 128 0 0 1-99.904 48H453.12a128 128 0 0 1-99.84-48H352v-1.536a128.128 128.128 0 0 1-9.984-14.976L314.88 448H240a28.928 28.928 0 0 0-28.48 34.304L241.088 640h541.824l29.568-157.696A28.928 28.928 0 0 0 784 448h-74.88l-27.136 47.488A132.405 132.405 0 0 1 672 510.464V512h-1.216zM480 288a32 32 0 0 0-64 0v196.096A64 64 0 0 0 453.12 496H480V288zm-128 96V224a32 32 0 0 0-64 0v160h64-37.12A64 64 0 0 1 352 395.904zm-98.88 320 19.072 101.888A32 32 0 0 0 303.68 832h416.64a32 32 0 0 0 31.488-26.112L770.88 704H253.12z"},null,-1);function z9(e,t,n,o,r,s){return c(),i("svg",g9,[w9])}Re.render=z9;Re.__file="packages/components/Fries.vue";const x9=Re;var Ue=l({name:"FullScreen"});const C9={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},M9=a("path",{fill:"currentColor",d:"m160 96.064 192 .192a32 32 0 0 1 0 64l-192-.192V352a32 32 0 0 1-64 0V96h64v.064zm0 831.872V928H96V672a32 32 0 1 1 64 0v191.936l192-.192a32 32 0 1 1 0 64l-192 .192zM864 96.064V96h64v256a32 32 0 1 1-64 0V160.064l-192 .192a32 32 0 1 1 0-64l192-.192zm0 831.872-192-.192a32 32 0 0 1 0-64l192 .192V672a32 32 0 1 1 64 0v256h-64v-.064z"},null,-1);function L9(e,t,n,o,r,s){return c(),i("svg",C9,[M9])}Ue.render=L9;Ue.__file="packages/components/FullScreen.vue";const H9=Ue;var Ge=l({name:"ForkSpoon"});const k9={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},V9=a("path",{fill:"currentColor",d:"M256 410.304V96a32 32 0 0 1 64 0v314.304a96 96 0 0 0 64-90.56V96a32 32 0 0 1 64 0v223.744a160 160 0 0 1-128 156.8V928a32 32 0 1 1-64 0V476.544a160 160 0 0 1-128-156.8V96a32 32 0 0 1 64 0v223.744a96 96 0 0 0 64 90.56zM672 572.48C581.184 552.128 512 446.848 512 320c0-141.44 85.952-256 192-256s192 114.56 192 256c0 126.848-69.184 232.128-160 252.48V928a32 32 0 1 1-64 0V572.48zM704 512c66.048 0 128-82.56 128-192s-61.952-192-128-192-128 82.56-128 192 61.952 192 128 192z"},null,-1);function b9(e,t,n,o,r,s){return c(),i("svg",k9,[V9])}Ge.render=b9;Ge.__file="packages/components/ForkSpoon.vue";const y9=Ge;var We=l({name:"Goblet"});const A9={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},E9=a("path",{fill:"currentColor",d:"M544 638.4V896h96a32 32 0 1 1 0 64H384a32 32 0 1 1 0-64h96V638.4A320 320 0 0 1 192 320c0-85.632 21.312-170.944 64-256h512c42.688 64.32 64 149.632 64 256a320 320 0 0 1-288 318.4zM256 320a256 256 0 1 0 512 0c0-78.592-12.608-142.4-36.928-192h-434.24C269.504 192.384 256 256.256 256 320z"},null,-1);function S9(e,t,n,o,r,s){return c(),i("svg",A9,[E9])}We.render=S9;We.__file="packages/components/Goblet.vue";const B9=We;var Ze=l({name:"GobletFull"});const F9={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},T9=a("path",{fill:"currentColor",d:"M256 320h512c0-78.592-12.608-142.4-36.928-192h-434.24C269.504 192.384 256 256.256 256 320zm503.936 64H264.064a256.128 256.128 0 0 0 495.872 0zM544 638.4V896h96a32 32 0 1 1 0 64H384a32 32 0 1 1 0-64h96V638.4A320 320 0 0 1 192 320c0-85.632 21.312-170.944 64-256h512c42.688 64.32 64 149.632 64 256a320 320 0 0 1-288 318.4z"},null,-1);function I9(e,t,n,o,r,s){return c(),i("svg",F9,[T9])}Ze.render=I9;Ze.__file="packages/components/GobletFull.vue";const O9=Ze;var qe=l({name:"Goods"});const N9={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},P9=a("path",{fill:"currentColor",d:"M320 288v-22.336C320 154.688 405.504 64 512 64s192 90.688 192 201.664v22.4h131.072a32 32 0 0 1 31.808 28.8l57.6 576a32 32 0 0 1-31.808 35.2H131.328a32 32 0 0 1-31.808-35.2l57.6-576a32 32 0 0 1 31.808-28.8H320zm64 0h256v-22.336C640 189.248 582.272 128 512 128c-70.272 0-128 61.248-128 137.664v22.4zm-64 64H217.92l-51.2 512h690.56l-51.264-512H704v96a32 32 0 1 1-64 0v-96H384v96a32 32 0 0 1-64 0v-96z"},null,-1);function D9(e,t,n,o,r,s){return c(),i("svg",N9,[P9])}qe.render=D9;qe.__file="packages/components/Goods.vue";const R9=qe;var je=l({name:"GobletSquareFull"});const U9={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},G9=a("path",{fill:"currentColor",d:"M256 270.912c10.048 6.72 22.464 14.912 28.992 18.624a220.16 220.16 0 0 0 114.752 30.72c30.592 0 49.408-9.472 91.072-41.152l.64-.448c52.928-40.32 82.368-55.04 132.288-54.656 55.552.448 99.584 20.8 142.72 57.408l1.536 1.28V128H256v142.912zm.96 76.288C266.368 482.176 346.88 575.872 512 576c157.44.064 237.952-85.056 253.248-209.984a952.32 952.32 0 0 1-40.192-35.712c-32.704-27.776-63.36-41.92-101.888-42.24-31.552-.256-50.624 9.28-93.12 41.6l-.576.448c-52.096 39.616-81.024 54.208-129.792 54.208-54.784 0-100.48-13.376-142.784-37.056zM480 638.848C250.624 623.424 192 442.496 192 319.68V96a32 32 0 0 1 32-32h576a32 32 0 0 1 32 32v224c0 122.816-58.624 303.68-288 318.912V896h96a32 32 0 1 1 0 64H384a32 32 0 1 1 0-64h96V638.848z"},null,-1);function W9(e,t,n,o,r,s){return c(),i("svg",U9,[G9])}je.render=W9;je.__file="packages/components/GobletSquareFull.vue";const Z9=je;var Ye=l({name:"GoodsFilled"});const q9={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},j9=a("path",{fill:"currentColor",d:"M192 352h640l64 544H128l64-544zm128 224h64V448h-64v128zm320 0h64V448h-64v128zM384 288h-64a192 192 0 1 1 384 0h-64a128 128 0 1 0-256 0z"},null,-1);function Y9(e,t,n,o,r,s){return c(),i("svg",q9,[j9])}Ye.render=Y9;Ye.__file="packages/components/GoodsFilled.vue";const K9=Ye;var Ke=l({name:"Grid"});const X9={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},J9=a("path",{fill:"currentColor",d:"M640 384v256H384V384h256zm64 0h192v256H704V384zm-64 512H384V704h256v192zm64 0V704h192v192H704zm-64-768v192H384V128h256zm64 0h192v192H704V128zM320 384v256H128V384h192zm0 512H128V704h192v192zm0-768v192H128V128h192z"},null,-1);function Q9(e,t,n,o,r,s){return c(),i("svg",X9,[J9])}Ke.render=Q9;Ke.__file="packages/components/Grid.vue";const e7=Ke;var Xe=l({name:"Grape"});const t7={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},n7=a("path",{fill:"currentColor",d:"M544 195.2a160 160 0 0 1 96 60.8 160 160 0 1 1 146.24 254.976 160 160 0 0 1-128 224 160 160 0 1 1-292.48 0 160 160 0 0 1-128-224A160 160 0 1 1 384 256a160 160 0 0 1 96-60.8V128h-64a32 32 0 0 1 0-64h192a32 32 0 0 1 0 64h-64v67.2zM512 448a96 96 0 1 0 0-192 96 96 0 0 0 0 192zm-256 0a96 96 0 1 0 0-192 96 96 0 0 0 0 192zm128 224a96 96 0 1 0 0-192 96 96 0 0 0 0 192zm128 224a96 96 0 1 0 0-192 96 96 0 0 0 0 192zm128-224a96 96 0 1 0 0-192 96 96 0 0 0 0 192zm128-224a96 96 0 1 0 0-192 96 96 0 0 0 0 192z"},null,-1);function o7(e,t,n,o,r,s){return c(),i("svg",t7,[n7])}Xe.render=o7;Xe.__file="packages/components/Grape.vue";const r7=Xe;var Je=l({name:"GobletSquare"});const s7={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},a7=a("path",{fill:"currentColor",d:"M544 638.912V896h96a32 32 0 1 1 0 64H384a32 32 0 1 1 0-64h96V638.848C250.624 623.424 192 442.496 192 319.68V96a32 32 0 0 1 32-32h576a32 32 0 0 1 32 32v224c0 122.816-58.624 303.68-288 318.912zM256 319.68c0 149.568 80 256.192 256 256.256C688.128 576 768 469.568 768 320V128H256v191.68z"},null,-1);function c7(e,t,n,o,r,s){return c(),i("svg",s7,[a7])}Je.render=c7;Je.__file="packages/components/GobletSquare.vue";const l7=Je;var Qe=l({name:"Headset"});const i7={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},u7=a("path",{fill:"currentColor",d:"M896 529.152V512a384 384 0 1 0-768 0v17.152A128 128 0 0 1 320 640v128a128 128 0 1 1-256 0V512a448 448 0 1 1 896 0v256a128 128 0 1 1-256 0V640a128 128 0 0 1 192-110.848zM896 640a64 64 0 0 0-128 0v128a64 64 0 0 0 128 0V640zm-768 0v128a64 64 0 0 0 128 0V640a64 64 0 1 0-128 0z"},null,-1);function p7(e,t,n,o,r,s){return c(),i("svg",i7,[u7])}Qe.render=p7;Qe.__file="packages/components/Headset.vue";const _7=Qe;var e4=l({name:"Comment"});const d7={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},h7=a("path",{fill:"currentColor",d:"M736 504a56 56 0 1 1 0-112 56 56 0 0 1 0 112zm-224 0a56 56 0 1 1 0-112 56 56 0 0 1 0 112zm-224 0a56 56 0 1 1 0-112 56 56 0 0 1 0 112zM128 128v640h192v160l224-160h352V128H128z"},null,-1);function $7(e,t,n,o,r,s){return c(),i("svg",d7,[h7])}e4.render=$7;e4.__file="packages/components/Comment.vue";const f7=e4;var t4=l({name:"HelpFilled"});const m7={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},v7=a("path",{fill:"currentColor",d:"M926.784 480H701.312A192.512 192.512 0 0 0 544 322.688V97.216A416.064 416.064 0 0 1 926.784 480zm0 64A416.064 416.064 0 0 1 544 926.784V701.312A192.512 192.512 0 0 0 701.312 544h225.472zM97.28 544h225.472A192.512 192.512 0 0 0 480 701.312v225.472A416.064 416.064 0 0 1 97.216 544zm0-64A416.064 416.064 0 0 1 480 97.216v225.472A192.512 192.512 0 0 0 322.688 480H97.216z"},null,-1);function g7(e,t,n,o,r,s){return c(),i("svg",m7,[v7])}t4.render=g7;t4.__file="packages/components/HelpFilled.vue";const w7=t4;var n4=l({name:"Histogram"});const z7={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},x7=a("path",{fill:"currentColor",d:"M416 896V128h192v768H416zm-288 0V448h192v448H128zm576 0V320h192v576H704z"},null,-1);function C7(e,t,n,o,r,s){return c(),i("svg",z7,[x7])}n4.render=C7;n4.__file="packages/components/Histogram.vue";const M7=n4;var o4=l({name:"HomeFilled"});const L7={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},H7=a("path",{fill:"currentColor",d:"M512 128 128 447.936V896h255.936V640H640v256h255.936V447.936z"},null,-1);function k7(e,t,n,o,r,s){return c(),i("svg",L7,[H7])}o4.render=k7;o4.__file="packages/components/HomeFilled.vue";const V7=o4;var r4=l({name:"Help"});const b7={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},y7=a("path",{fill:"currentColor",d:"m759.936 805.248-90.944-91.008A254.912 254.912 0 0 1 512 768a254.912 254.912 0 0 1-156.992-53.76l-90.944 91.008A382.464 382.464 0 0 0 512 896c94.528 0 181.12-34.176 247.936-90.752zm45.312-45.312A382.464 382.464 0 0 0 896 512c0-94.528-34.176-181.12-90.752-247.936l-91.008 90.944C747.904 398.4 768 452.864 768 512c0 59.136-20.096 113.6-53.76 156.992l91.008 90.944zm-45.312-541.184A382.464 382.464 0 0 0 512 128c-94.528 0-181.12 34.176-247.936 90.752l90.944 91.008A254.912 254.912 0 0 1 512 256c59.136 0 113.6 20.096 156.992 53.76l90.944-91.008zm-541.184 45.312A382.464 382.464 0 0 0 128 512c0 94.528 34.176 181.12 90.752 247.936l91.008-90.944A254.912 254.912 0 0 1 256 512c0-59.136 20.096-113.6 53.76-156.992l-91.008-90.944zm417.28 394.496a194.56 194.56 0 0 0 22.528-22.528C686.912 602.56 704 559.232 704 512a191.232 191.232 0 0 0-67.968-146.56A191.296 191.296 0 0 0 512 320a191.232 191.232 0 0 0-146.56 67.968C337.088 421.44 320 464.768 320 512a191.232 191.232 0 0 0 67.968 146.56C421.44 686.912 464.768 704 512 704c47.296 0 90.56-17.088 124.032-45.44zM512 960a448 448 0 1 1 0-896 448 448 0 0 1 0 896z"},null,-1);function A7(e,t,n,o,r,s){return c(),i("svg",b7,[y7])}r4.render=A7;r4.__file="packages/components/Help.vue";const E7=r4;var s4=l({name:"House"});const S7={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},B7=a("path",{fill:"currentColor",d:"M192 413.952V896h640V413.952L512 147.328 192 413.952zM139.52 374.4l352-293.312a32 32 0 0 1 40.96 0l352 293.312A32 32 0 0 1 896 398.976V928a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V398.976a32 32 0 0 1 11.52-24.576z"},null,-1);function F7(e,t,n,o,r,s){return c(),i("svg",S7,[B7])}s4.render=F7;s4.__file="packages/components/House.vue";const T7=s4;var a4=l({name:"IceCreamRound"});const I7={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},O7=a("path",{fill:"currentColor",d:"m308.352 489.344 226.304 226.304a32 32 0 0 0 45.248 0L783.552 512A192 192 0 1 0 512 240.448L308.352 444.16a32 32 0 0 0 0 45.248zm135.744 226.304L308.352 851.392a96 96 0 0 1-135.744-135.744l135.744-135.744-45.248-45.248a96 96 0 0 1 0-135.808L466.752 195.2A256 256 0 0 1 828.8 557.248L625.152 760.96a96 96 0 0 1-135.808 0l-45.248-45.248zM398.848 670.4 353.6 625.152 217.856 760.896a32 32 0 0 0 45.248 45.248L398.848 670.4zm248.96-384.64a32 32 0 0 1 0 45.248L466.624 512a32 32 0 1 1-45.184-45.248l180.992-181.056a32 32 0 0 1 45.248 0zm90.496 90.496a32 32 0 0 1 0 45.248L557.248 602.496A32 32 0 1 1 512 557.248l180.992-180.992a32 32 0 0 1 45.312 0z"},null,-1);function N7(e,t,n,o,r,s){return c(),i("svg",I7,[O7])}a4.render=N7;a4.__file="packages/components/IceCreamRound.vue";const P7=a4;var c4=l({name:"HotWater"});const D7={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},R7=a("path",{fill:"currentColor",d:"M273.067 477.867h477.866V409.6H273.067v68.267zm0 68.266v51.2A187.733 187.733 0 0 0 460.8 785.067h102.4a187.733 187.733 0 0 0 187.733-187.734v-51.2H273.067zm-34.134-204.8h546.134a34.133 34.133 0 0 1 34.133 34.134v221.866a256 256 0 0 1-256 256H460.8a256 256 0 0 1-256-256V375.467a34.133 34.133 0 0 1 34.133-34.134zM512 34.133a34.133 34.133 0 0 1 34.133 34.134v170.666a34.133 34.133 0 0 1-68.266 0V68.267A34.133 34.133 0 0 1 512 34.133zM375.467 102.4a34.133 34.133 0 0 1 34.133 34.133v102.4a34.133 34.133 0 0 1-68.267 0v-102.4a34.133 34.133 0 0 1 34.134-34.133zm273.066 0a34.133 34.133 0 0 1 34.134 34.133v102.4a34.133 34.133 0 1 1-68.267 0v-102.4a34.133 34.133 0 0 1 34.133-34.133zM170.667 921.668h682.666a34.133 34.133 0 1 1 0 68.267H170.667a34.133 34.133 0 1 1 0-68.267z"},null,-1);function U7(e,t,n,o,r,s){return c(),i("svg",D7,[R7])}c4.render=U7;c4.__file="packages/components/HotWater.vue";const G7=c4;var l4=l({name:"IceCream"});const W7={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Z7=a("path",{fill:"currentColor",d:"M128.64 448a208 208 0 0 1 193.536-191.552 224 224 0 0 1 445.248 15.488A208.128 208.128 0 0 1 894.784 448H896L548.8 983.68a32 32 0 0 1-53.248.704L128 448h.64zm64.256 0h286.208a144 144 0 0 0-286.208 0zm351.36 0h286.272a144 144 0 0 0-286.272 0zm-294.848 64 271.808 396.608L778.24 512H249.408zM511.68 352.64a207.872 207.872 0 0 1 189.184-96.192 160 160 0 0 0-314.752 5.632c52.608 12.992 97.28 46.08 125.568 90.56z"},null,-1);function q7(e,t,n,o,r,s){return c(),i("svg",W7,[Z7])}l4.render=q7;l4.__file="packages/components/IceCream.vue";const j7=l4;var i4=l({name:"Files"});const Y7={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},K7=a("path",{fill:"currentColor",d:"M128 384v448h768V384H128zm-32-64h832a32 32 0 0 1 32 32v512a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V352a32 32 0 0 1 32-32zm64-128h704v64H160zm96-128h512v64H256z"},null,-1);function X7(e,t,n,o,r,s){return c(),i("svg",Y7,[K7])}i4.render=X7;i4.__file="packages/components/Files.vue";const J7=i4;var u4=l({name:"IceCreamSquare"});const Q7={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},eu=a("path",{fill:"currentColor",d:"M416 640h256a32 32 0 0 0 32-32V160a32 32 0 0 0-32-32H352a32 32 0 0 0-32 32v448a32 32 0 0 0 32 32h64zm192 64v160a96 96 0 0 1-192 0V704h-64a96 96 0 0 1-96-96V160a96 96 0 0 1 96-96h320a96 96 0 0 1 96 96v448a96 96 0 0 1-96 96h-64zm-64 0h-64v160a32 32 0 1 0 64 0V704z"},null,-1);function tu(e,t,n,o,r,s){return c(),i("svg",Q7,[eu])}u4.render=tu;u4.__file="packages/components/IceCreamSquare.vue";const nu=u4;var p4=l({name:"Key"});const ou={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},ru=a("path",{fill:"currentColor",d:"M448 456.064V96a32 32 0 0 1 32-32.064L672 64a32 32 0 0 1 0 64H512v128h160a32 32 0 0 1 0 64H512v128a256 256 0 1 1-64 8.064zM512 896a192 192 0 1 0 0-384 192 192 0 0 0 0 384z"},null,-1);function su(e,t,n,o,r,s){return c(),i("svg",ou,[ru])}p4.render=su;p4.__file="packages/components/Key.vue";const au=p4;var _4=l({name:"IceTea"});const cu={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},lu=a("path",{fill:"currentColor",d:"M197.696 259.648a320.128 320.128 0 0 1 628.608 0A96 96 0 0 1 896 352v64a96 96 0 0 1-71.616 92.864l-49.408 395.072A64 64 0 0 1 711.488 960H312.512a64 64 0 0 1-63.488-56.064l-49.408-395.072A96 96 0 0 1 128 416v-64a96 96 0 0 1 69.696-92.352zM264.064 256h495.872a256.128 256.128 0 0 0-495.872 0zm495.424 256H264.512l48 384h398.976l48-384zM224 448h576a32 32 0 0 0 32-32v-64a32 32 0 0 0-32-32H224a32 32 0 0 0-32 32v64a32 32 0 0 0 32 32zm160 192h64v64h-64v-64zm192 64h64v64h-64v-64zm-128 64h64v64h-64v-64zm64-192h64v64h-64v-64z"},null,-1);function iu(e,t,n,o,r,s){return c(),i("svg",cu,[lu])}_4.render=iu;_4.__file="packages/components/IceTea.vue";const uu=_4;var d4=l({name:"KnifeFork"});const pu={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},_u=a("path",{fill:"currentColor",d:"M256 410.56V96a32 32 0 0 1 64 0v314.56A96 96 0 0 0 384 320V96a32 32 0 0 1 64 0v224a160 160 0 0 1-128 156.8V928a32 32 0 1 1-64 0V476.8A160 160 0 0 1 128 320V96a32 32 0 0 1 64 0v224a96 96 0 0 0 64 90.56zm384-250.24V544h126.72c-3.328-78.72-12.928-147.968-28.608-207.744-14.336-54.528-46.848-113.344-98.112-175.872zM640 608v320a32 32 0 1 1-64 0V64h64c85.312 89.472 138.688 174.848 160 256 21.312 81.152 32 177.152 32 288H640z"},null,-1);function du(e,t,n,o,r,s){return c(),i("svg",pu,[_u])}d4.render=du;d4.__file="packages/components/KnifeFork.vue";const hu=d4;var h4=l({name:"Iphone"});const $u={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},fu=a("path",{fill:"currentColor",d:"M224 768v96.064a64 64 0 0 0 64 64h448a64 64 0 0 0 64-64V768H224zm0-64h576V160a64 64 0 0 0-64-64H288a64 64 0 0 0-64 64v544zm32 288a96 96 0 0 1-96-96V128a96 96 0 0 1 96-96h512a96 96 0 0 1 96 96v768a96 96 0 0 1-96 96H256zm304-144a48 48 0 1 1-96 0 48 48 0 0 1 96 0z"},null,-1);function mu(e,t,n,o,r,s){return c(),i("svg",$u,[fu])}h4.render=mu;h4.__file="packages/components/Iphone.vue";const vu=h4;var $4=l({name:"InfoFilled"});const gu={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},wu=a("path",{fill:"currentColor",d:"M512 64a448 448 0 1 1 0 896.064A448 448 0 0 1 512 64zm67.2 275.072c33.28 0 60.288-23.104 60.288-57.344s-27.072-57.344-60.288-57.344c-33.28 0-60.16 23.104-60.16 57.344s26.88 57.344 60.16 57.344zM590.912 699.2c0-6.848 2.368-24.64 1.024-34.752l-52.608 60.544c-10.88 11.456-24.512 19.392-30.912 17.28a12.992 12.992 0 0 1-8.256-14.72l87.68-276.992c7.168-35.136-12.544-67.2-54.336-71.296-44.096 0-108.992 44.736-148.48 101.504 0 6.784-1.28 23.68.064 33.792l52.544-60.608c10.88-11.328 23.552-19.328 29.952-17.152a12.8 12.8 0 0 1 7.808 16.128L388.48 728.576c-10.048 32.256 8.96 63.872 55.04 71.04 67.84 0 107.904-43.648 147.456-100.416z"},null,-1);function zu(e,t,n,o,r,s){return c(),i("svg",gu,[wu])}$4.render=zu;$4.__file="packages/components/InfoFilled.vue";const xu=$4;var f4=l({name:"Link"});const Cu={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Mu=a("path",{fill:"currentColor",d:"M715.648 625.152 670.4 579.904l90.496-90.56c75.008-74.944 85.12-186.368 22.656-248.896-62.528-62.464-173.952-52.352-248.96 22.656L444.16 353.6l-45.248-45.248 90.496-90.496c100.032-99.968 251.968-110.08 339.456-22.656 87.488 87.488 77.312 239.424-22.656 339.456l-90.496 90.496zm-90.496 90.496-90.496 90.496C434.624 906.112 282.688 916.224 195.2 828.8c-87.488-87.488-77.312-239.424 22.656-339.456l90.496-90.496 45.248 45.248-90.496 90.56c-75.008 74.944-85.12 186.368-22.656 248.896 62.528 62.464 173.952 52.352 248.96-22.656l90.496-90.496 45.248 45.248zm0-362.048 45.248 45.248L398.848 670.4 353.6 625.152 625.152 353.6z"},null,-1);function Lu(e,t,n,o,r,s){return c(),i("svg",Cu,[Mu])}f4.render=Lu;f4.__file="packages/components/Link.vue";const Hu=f4;var m4=l({name:"IceDrink"});const ku={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Vu=a("path",{fill:"currentColor",d:"M512 448v128h239.68l16.064-128H512zm-64 0H256.256l16.064 128H448V448zm64-255.36V384h247.744A256.128 256.128 0 0 0 512 192.64zm-64 8.064A256.448 256.448 0 0 0 264.256 384H448V200.704zm64-72.064A320.128 320.128 0 0 1 825.472 384H896a32 32 0 1 1 0 64h-64v1.92l-56.96 454.016A64 64 0 0 1 711.552 960H312.448a64 64 0 0 1-63.488-56.064L192 449.92V448h-64a32 32 0 0 1 0-64h70.528A320.384 320.384 0 0 1 448 135.04V96a96 96 0 0 1 96-96h128a32 32 0 1 1 0 64H544a32 32 0 0 0-32 32v32.64zM743.68 640H280.32l32.128 256h399.104l32.128-256z"},null,-1);function bu(e,t,n,o,r,s){return c(),i("svg",ku,[Vu])}m4.render=bu;m4.__file="packages/components/IceDrink.vue";const yu=m4;var v4=l({name:"Lightning"});const Au={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Eu=a("path",{fill:"currentColor",d:"M288 671.36v64.128A239.808 239.808 0 0 1 63.744 496.192a240.32 240.32 0 0 1 199.488-236.8 256.128 256.128 0 0 1 487.872-30.976A256.064 256.064 0 0 1 736 734.016v-64.768a192 192 0 0 0 3.328-377.92l-35.2-6.592-12.8-33.408a192.064 192.064 0 0 0-365.952 23.232l-9.92 40.896-41.472 7.04a176.32 176.32 0 0 0-146.24 173.568c0 91.968 70.464 167.36 160.256 175.232z"},null,-1),Su=a("path",{fill:"currentColor",d:"M416 736a32 32 0 0 1-27.776-47.872l128-224a32 32 0 1 1 55.552 31.744L471.168 672H608a32 32 0 0 1 27.776 47.872l-128 224a32 32 0 1 1-55.68-31.744L552.96 736H416z"},null,-1);function Bu(e,t,n,o,r,s){return c(),i("svg",Au,[Eu,Su])}v4.render=Bu;v4.__file="packages/components/Lightning.vue";const Fu=v4;var g4=l({name:"Loading"});const Tu={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Iu=a("path",{fill:"currentColor",d:"M512 64a32 32 0 0 1 32 32v192a32 32 0 0 1-64 0V96a32 32 0 0 1 32-32zm0 640a32 32 0 0 1 32 32v192a32 32 0 1 1-64 0V736a32 32 0 0 1 32-32zm448-192a32 32 0 0 1-32 32H736a32 32 0 1 1 0-64h192a32 32 0 0 1 32 32zm-640 0a32 32 0 0 1-32 32H96a32 32 0 0 1 0-64h192a32 32 0 0 1 32 32zM195.2 195.2a32 32 0 0 1 45.248 0L376.32 331.008a32 32 0 0 1-45.248 45.248L195.2 240.448a32 32 0 0 1 0-45.248zm452.544 452.544a32 32 0 0 1 45.248 0L828.8 783.552a32 32 0 0 1-45.248 45.248L647.744 692.992a32 32 0 0 1 0-45.248zM828.8 195.264a32 32 0 0 1 0 45.184L692.992 376.32a32 32 0 0 1-45.248-45.248l135.808-135.808a32 32 0 0 1 45.248 0zm-452.544 452.48a32 32 0 0 1 0 45.248L240.448 828.8a32 32 0 0 1-45.248-45.248l135.808-135.808a32 32 0 0 1 45.248 0z"},null,-1);function Ou(e,t,n,o,r,s){return c(),i("svg",Tu,[Iu])}g4.render=Ou;g4.__file="packages/components/Loading.vue";const Nu=g4;var w4=l({name:"Lollipop"});const Pu={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Du=a("path",{fill:"currentColor",d:"M513.28 448a64 64 0 1 1 76.544 49.728A96 96 0 0 0 768 448h64a160 160 0 0 1-320 0h1.28zm-126.976-29.696a256 256 0 1 0 43.52-180.48A256 256 0 0 1 832 448h-64a192 192 0 0 0-381.696-29.696zm105.664 249.472L285.696 874.048a96 96 0 0 1-135.68-135.744l206.208-206.272a320 320 0 1 1 135.744 135.744zm-54.464-36.032a321.92 321.92 0 0 1-45.248-45.248L195.2 783.552a32 32 0 1 0 45.248 45.248l197.056-197.12z"},null,-1);function Ru(e,t,n,o,r,s){return c(),i("svg",Pu,[Du])}w4.render=Ru;w4.__file="packages/components/Lollipop.vue";const Uu=w4;var z4=l({name:"LocationInformation"});const Gu={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Wu=a("path",{fill:"currentColor",d:"M288 896h448q32 0 32 32t-32 32H288q-32 0-32-32t32-32z"},null,-1),Zu=a("path",{fill:"currentColor",d:"M800 416a288 288 0 1 0-576 0c0 118.144 94.528 272.128 288 456.576C705.472 688.128 800 534.144 800 416zM512 960C277.312 746.688 160 565.312 160 416a352 352 0 0 1 704 0c0 149.312-117.312 330.688-352 544z"},null,-1),qu=a("path",{fill:"currentColor",d:"M512 512a96 96 0 1 0 0-192 96 96 0 0 0 0 192zm0 64a160 160 0 1 1 0-320 160 160 0 0 1 0 320z"},null,-1);function ju(e,t,n,o,r,s){return c(),i("svg",Gu,[Wu,Zu,qu])}z4.render=ju;z4.__file="packages/components/LocationInformation.vue";const Yu=z4;var x4=l({name:"Lock"});const Ku={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Xu=a("path",{fill:"currentColor",d:"M224 448a32 32 0 0 0-32 32v384a32 32 0 0 0 32 32h576a32 32 0 0 0 32-32V480a32 32 0 0 0-32-32H224zm0-64h576a96 96 0 0 1 96 96v384a96 96 0 0 1-96 96H224a96 96 0 0 1-96-96V480a96 96 0 0 1 96-96z"},null,-1),Ju=a("path",{fill:"currentColor",d:"M512 544a32 32 0 0 1 32 32v192a32 32 0 1 1-64 0V576a32 32 0 0 1 32-32zm192-160v-64a192 192 0 1 0-384 0v64h384zM512 64a256 256 0 0 1 256 256v128H256V320A256 256 0 0 1 512 64z"},null,-1);function Qu(e,t,n,o,r,s){return c(),i("svg",Ku,[Xu,Ju])}x4.render=Qu;x4.__file="packages/components/Lock.vue";const ep=x4;var C4=l({name:"LocationFilled"});const tp={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},np=a("path",{fill:"currentColor",d:"M512 928c23.936 0 117.504-68.352 192.064-153.152C803.456 661.888 864 535.808 864 416c0-189.632-155.84-320-352-320S160 226.368 160 416c0 120.32 60.544 246.4 159.936 359.232C394.432 859.84 488 928 512 928zm0-435.2a64 64 0 1 0 0-128 64 64 0 0 0 0 128zm0 140.8a204.8 204.8 0 1 1 0-409.6 204.8 204.8 0 0 1 0 409.6z"},null,-1);function op(e,t,n,o,r,s){return c(),i("svg",tp,[np])}C4.render=op;C4.__file="packages/components/LocationFilled.vue";const rp=C4;var M4=l({name:"Magnet"});const sp={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},ap=a("path",{fill:"currentColor",d:"M832 320V192H704v320a192 192 0 1 1-384 0V192H192v128h128v64H192v128a320 320 0 0 0 640 0V384H704v-64h128zM640 512V128h256v384a384 384 0 1 1-768 0V128h256v384a128 128 0 1 0 256 0z"},null,-1);function cp(e,t,n,o,r,s){return c(),i("svg",sp,[ap])}M4.render=cp;M4.__file="packages/components/Magnet.vue";const lp=M4;var L4=l({name:"Male"});const ip={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},up=a("path",{fill:"currentColor",d:"M399.5 849.5a225 225 0 1 0 0-450 225 225 0 0 0 0 450zm0 56.25a281.25 281.25 0 1 1 0-562.5 281.25 281.25 0 0 1 0 562.5zm253.125-787.5h225q28.125 0 28.125 28.125T877.625 174.5h-225q-28.125 0-28.125-28.125t28.125-28.125z"},null,-1),pp=a("path",{fill:"currentColor",d:"M877.625 118.25q28.125 0 28.125 28.125v225q0 28.125-28.125 28.125T849.5 371.375v-225q0-28.125 28.125-28.125z"},null,-1),_p=a("path",{fill:"currentColor",d:"M604.813 458.9 565.1 419.131l292.613-292.668 39.825 39.824z"},null,-1);function dp(e,t,n,o,r,s){return c(),i("svg",ip,[up,pp,_p])}L4.render=dp;L4.__file="packages/components/Male.vue";const hp=L4;var H4=l({name:"Location"});const $p={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},fp=a("path",{fill:"currentColor",d:"M800 416a288 288 0 1 0-576 0c0 118.144 94.528 272.128 288 456.576C705.472 688.128 800 534.144 800 416zM512 960C277.312 746.688 160 565.312 160 416a352 352 0 0 1 704 0c0 149.312-117.312 330.688-352 544z"},null,-1),mp=a("path",{fill:"currentColor",d:"M512 512a96 96 0 1 0 0-192 96 96 0 0 0 0 192zm0 64a160 160 0 1 1 0-320 160 160 0 0 1 0 320z"},null,-1);function vp(e,t,n,o,r,s){return c(),i("svg",$p,[fp,mp])}H4.render=vp;H4.__file="packages/components/Location.vue";const gp=H4;var k4=l({name:"Menu"});const wp={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},zp=a("path",{fill:"currentColor",d:"M160 448a32 32 0 0 1-32-32V160.064a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32V416a32 32 0 0 1-32 32H160zm448 0a32 32 0 0 1-32-32V160.064a32 32 0 0 1 32-32h255.936a32 32 0 0 1 32 32V416a32 32 0 0 1-32 32H608zM160 896a32 32 0 0 1-32-32V608a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32v256a32 32 0 0 1-32 32H160zm448 0a32 32 0 0 1-32-32V608a32 32 0 0 1 32-32h255.936a32 32 0 0 1 32 32v256a32 32 0 0 1-32 32H608z"},null,-1);function xp(e,t,n,o,r,s){return c(),i("svg",wp,[zp])}k4.render=xp;k4.__file="packages/components/Menu.vue";const Cp=k4;var V4=l({name:"MagicStick"});const Mp={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Lp=a("path",{fill:"currentColor",d:"M512 64h64v192h-64V64zm0 576h64v192h-64V640zM160 480v-64h192v64H160zm576 0v-64h192v64H736zM249.856 199.04l45.248-45.184L430.848 289.6 385.6 334.848 249.856 199.104zM657.152 606.4l45.248-45.248 135.744 135.744-45.248 45.248L657.152 606.4zM114.048 923.2 68.8 877.952l316.8-316.8 45.248 45.248-316.8 316.8zM702.4 334.848 657.152 289.6l135.744-135.744 45.248 45.248L702.4 334.848z"},null,-1);function Hp(e,t,n,o,r,s){return c(),i("svg",Mp,[Lp])}V4.render=Hp;V4.__file="packages/components/MagicStick.vue";const kp=V4;var b4=l({name:"MessageBox"});const Vp={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},bp=a("path",{fill:"currentColor",d:"M288 384h448v64H288v-64zm96-128h256v64H384v-64zM131.456 512H384v128h256V512h252.544L721.856 192H302.144L131.456 512zM896 576H704v128H320V576H128v256h768V576zM275.776 128h472.448a32 32 0 0 1 28.608 17.664l179.84 359.552A32 32 0 0 1 960 519.552V864a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V519.552a32 32 0 0 1 3.392-14.336l179.776-359.552A32 32 0 0 1 275.776 128z"},null,-1);function yp(e,t,n,o,r,s){return c(),i("svg",Vp,[bp])}b4.render=yp;b4.__file="packages/components/MessageBox.vue";const Ap=b4;var y4=l({name:"MapLocation"});const Ep={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Sp=a("path",{fill:"currentColor",d:"M800 416a288 288 0 1 0-576 0c0 118.144 94.528 272.128 288 456.576C705.472 688.128 800 534.144 800 416zM512 960C277.312 746.688 160 565.312 160 416a352 352 0 0 1 704 0c0 149.312-117.312 330.688-352 544z"},null,-1),Bp=a("path",{fill:"currentColor",d:"M512 448a64 64 0 1 0 0-128 64 64 0 0 0 0 128zm0 64a128 128 0 1 1 0-256 128 128 0 0 1 0 256zm345.6 192L960 960H672v-64H352v64H64l102.4-256h691.2zm-68.928 0H235.328l-76.8 192h706.944l-76.8-192z"},null,-1);function Fp(e,t,n,o,r,s){return c(),i("svg",Ep,[Sp,Bp])}y4.render=Fp;y4.__file="packages/components/MapLocation.vue";const Tp=y4;var A4=l({name:"Mic"});const Ip={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Op=a("path",{fill:"currentColor",d:"M480 704h160a64 64 0 0 0 64-64v-32h-96a32 32 0 0 1 0-64h96v-96h-96a32 32 0 0 1 0-64h96v-96h-96a32 32 0 0 1 0-64h96v-32a64 64 0 0 0-64-64H384a64 64 0 0 0-64 64v32h96a32 32 0 0 1 0 64h-96v96h96a32 32 0 0 1 0 64h-96v96h96a32 32 0 0 1 0 64h-96v32a64 64 0 0 0 64 64h96zm64 64v128h192a32 32 0 1 1 0 64H288a32 32 0 1 1 0-64h192V768h-96a128 128 0 0 1-128-128V192A128 128 0 0 1 384 64h256a128 128 0 0 1 128 128v448a128 128 0 0 1-128 128h-96z"},null,-1);function Np(e,t,n,o,r,s){return c(),i("svg",Ip,[Op])}A4.render=Np;A4.__file="packages/components/Mic.vue";const Pp=A4;var E4=l({name:"Message"});const Dp={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Rp=a("path",{fill:"currentColor",d:"M128 224v512a64 64 0 0 0 64 64h640a64 64 0 0 0 64-64V224H128zm0-64h768a64 64 0 0 1 64 64v512a128 128 0 0 1-128 128H192A128 128 0 0 1 64 736V224a64 64 0 0 1 64-64z"},null,-1),Up=a("path",{fill:"currentColor",d:"M904 224 656.512 506.88a192 192 0 0 1-289.024 0L120 224h784zm-698.944 0 210.56 240.704a128 128 0 0 0 192.704 0L818.944 224H205.056z"},null,-1);function Gp(e,t,n,o,r,s){return c(),i("svg",Dp,[Rp,Up])}E4.render=Gp;E4.__file="packages/components/Message.vue";const Wp=E4;var S4=l({name:"Medal"});const Zp={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},qp=a("path",{fill:"currentColor",d:"M512 896a256 256 0 1 0 0-512 256 256 0 0 0 0 512zm0 64a320 320 0 1 1 0-640 320 320 0 0 1 0 640z"},null,-1),jp=a("path",{fill:"currentColor",d:"M576 128H448v200a286.72 286.72 0 0 1 64-8c19.52 0 40.832 2.688 64 8V128zm64 0v219.648c24.448 9.088 50.56 20.416 78.4 33.92L757.44 128H640zm-256 0H266.624l39.04 253.568c27.84-13.504 53.888-24.832 78.336-33.92V128zM229.312 64h565.376a32 32 0 0 1 31.616 36.864L768 480c-113.792-64-199.104-96-256-96-56.896 0-142.208 32-256 96l-58.304-379.136A32 32 0 0 1 229.312 64z"},null,-1);function Yp(e,t,n,o,r,s){return c(),i("svg",Zp,[qp,jp])}S4.render=Yp;S4.__file="packages/components/Medal.vue";const Kp=S4;var B4=l({name:"MilkTea"});const Xp={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Jp=a("path",{fill:"currentColor",d:"M416 128V96a96 96 0 0 1 96-96h128a32 32 0 1 1 0 64H512a32 32 0 0 0-32 32v32h320a96 96 0 0 1 11.712 191.296l-39.68 581.056A64 64 0 0 1 708.224 960H315.776a64 64 0 0 1-63.872-59.648l-39.616-581.056A96 96 0 0 1 224 128h192zM276.48 320l39.296 576h392.448l4.8-70.784a224.064 224.064 0 0 1 30.016-439.808L747.52 320H276.48zM224 256h576a32 32 0 1 0 0-64H224a32 32 0 0 0 0 64zm493.44 503.872 21.12-309.12a160 160 0 0 0-21.12 309.12z"},null,-1);function Qp(e,t,n,o,r,s){return c(),i("svg",Xp,[Jp])}B4.render=Qp;B4.__file="packages/components/MilkTea.vue";const e_=B4;var F4=l({name:"Microphone"});const t_={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},n_=a("path",{fill:"currentColor",d:"M512 128a128 128 0 0 0-128 128v256a128 128 0 1 0 256 0V256a128 128 0 0 0-128-128zm0-64a192 192 0 0 1 192 192v256a192 192 0 1 1-384 0V256A192 192 0 0 1 512 64zm-32 832v-64a288 288 0 0 1-288-288v-32a32 32 0 0 1 64 0v32a224 224 0 0 0 224 224h64a224 224 0 0 0 224-224v-32a32 32 0 1 1 64 0v32a288 288 0 0 1-288 288v64h64a32 32 0 1 1 0 64H416a32 32 0 1 1 0-64h64z"},null,-1);function o_(e,t,n,o,r,s){return c(),i("svg",t_,[n_])}F4.render=o_;F4.__file="packages/components/Microphone.vue";const r_=F4;var T4=l({name:"Minus"});const s_={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},a_=a("path",{fill:"currentColor",d:"M128 544h768a32 32 0 1 0 0-64H128a32 32 0 0 0 0 64z"},null,-1);function c_(e,t,n,o,r,s){return c(),i("svg",s_,[a_])}T4.render=c_;T4.__file="packages/components/Minus.vue";const l_=T4;var I4=l({name:"Money"});const i_={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},u_=a("path",{fill:"currentColor",d:"M256 640v192h640V384H768v-64h150.976c14.272 0 19.456 1.472 24.64 4.288a29.056 29.056 0 0 1 12.16 12.096c2.752 5.184 4.224 10.368 4.224 24.64v493.952c0 14.272-1.472 19.456-4.288 24.64a29.056 29.056 0 0 1-12.096 12.16c-5.184 2.752-10.368 4.224-24.64 4.224H233.024c-14.272 0-19.456-1.472-24.64-4.288a29.056 29.056 0 0 1-12.16-12.096c-2.688-5.184-4.224-10.368-4.224-24.576V640h64z"},null,-1),p_=a("path",{fill:"currentColor",d:"M768 192H128v448h640V192zm64-22.976v493.952c0 14.272-1.472 19.456-4.288 24.64a29.056 29.056 0 0 1-12.096 12.16c-5.184 2.752-10.368 4.224-24.64 4.224H105.024c-14.272 0-19.456-1.472-24.64-4.288a29.056 29.056 0 0 1-12.16-12.096C65.536 682.432 64 677.248 64 663.04V169.024c0-14.272 1.472-19.456 4.288-24.64a29.056 29.056 0 0 1 12.096-12.16C85.568 129.536 90.752 128 104.96 128h685.952c14.272 0 19.456 1.472 24.64 4.288a29.056 29.056 0 0 1 12.16 12.096c2.752 5.184 4.224 10.368 4.224 24.64z"},null,-1),__=a("path",{fill:"currentColor",d:"M448 576a160 160 0 1 1 0-320 160 160 0 0 1 0 320zm0-64a96 96 0 1 0 0-192 96 96 0 0 0 0 192z"},null,-1);function d_(e,t,n,o,r,s){return c(),i("svg",i_,[u_,p_,__])}I4.render=d_;I4.__file="packages/components/Money.vue";const h_=I4;var O4=l({name:"MoonNight"});const $_={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},f_=a("path",{fill:"currentColor",d:"M384 512a448 448 0 0 1 215.872-383.296A384 384 0 0 0 213.76 640h188.8A448.256 448.256 0 0 1 384 512zM171.136 704a448 448 0 0 1 636.992-575.296A384 384 0 0 0 499.328 704h-328.32z"},null,-1),m_=a("path",{fill:"currentColor",d:"M32 640h960q32 0 32 32t-32 32H32q-32 0-32-32t32-32zm128 128h384a32 32 0 1 1 0 64H160a32 32 0 1 1 0-64zm160 127.68 224 .256a32 32 0 0 1 32 32V928a32 32 0 0 1-32 32l-224-.384a32 32 0 0 1-32-32v-.064a32 32 0 0 1 32-32z"},null,-1);function v_(e,t,n,o,r,s){return c(),i("svg",$_,[f_,m_])}O4.render=v_;O4.__file="packages/components/MoonNight.vue";const g_=O4;var N4=l({name:"Monitor"});const w_={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},z_=a("path",{fill:"currentColor",d:"M544 768v128h192a32 32 0 1 1 0 64H288a32 32 0 1 1 0-64h192V768H192A128 128 0 0 1 64 640V256a128 128 0 0 1 128-128h640a128 128 0 0 1 128 128v384a128 128 0 0 1-128 128H544zM192 192a64 64 0 0 0-64 64v384a64 64 0 0 0 64 64h640a64 64 0 0 0 64-64V256a64 64 0 0 0-64-64H192z"},null,-1);function x_(e,t,n,o,r,s){return c(),i("svg",w_,[z_])}N4.render=x_;N4.__file="packages/components/Monitor.vue";const C_=N4;var P4=l({name:"Moon"});const M_={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},L_=a("path",{fill:"currentColor",d:"M240.448 240.448a384 384 0 1 0 559.424 525.696 448 448 0 0 1-542.016-542.08 390.592 390.592 0 0 0-17.408 16.384zm181.056 362.048a384 384 0 0 0 525.632 16.384A448 448 0 1 1 405.056 76.8a384 384 0 0 0 16.448 525.696z"},null,-1);function H_(e,t,n,o,r,s){return c(),i("svg",M_,[L_])}P4.render=H_;P4.__file="packages/components/Moon.vue";const k_=P4;var D4=l({name:"More"});const V_={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},b_=a("path",{fill:"currentColor",d:"M176 416a112 112 0 1 0 0 224 112 112 0 0 0 0-224m0 64a48 48 0 1 1 0 96 48 48 0 0 1 0-96zm336-64a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm0 64a48 48 0 1 0 0 96 48 48 0 0 0 0-96zm336-64a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm0 64a48 48 0 1 0 0 96 48 48 0 0 0 0-96z"},null,-1);function y_(e,t,n,o,r,s){return c(),i("svg",V_,[b_])}D4.render=y_;D4.__file="packages/components/More.vue";const A_=D4;var R4=l({name:"MostlyCloudy"});const E_={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},S_=a("path",{fill:"currentColor",d:"M737.216 357.952 704 349.824l-11.776-32a192.064 192.064 0 0 0-367.424 23.04l-8.96 39.04-39.04 8.96A192.064 192.064 0 0 0 320 768h368a207.808 207.808 0 0 0 207.808-208 208.32 208.32 0 0 0-158.592-202.048zm15.168-62.208A272.32 272.32 0 0 1 959.744 560a271.808 271.808 0 0 1-271.552 272H320a256 256 0 0 1-57.536-505.536 256.128 256.128 0 0 1 489.92-30.72z"},null,-1);function B_(e,t,n,o,r,s){return c(),i("svg",E_,[S_])}R4.render=B_;R4.__file="packages/components/MostlyCloudy.vue";const F_=R4;var U4=l({name:"MoreFilled"});const T_={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},I_=a("path",{fill:"currentColor",d:"M176 416a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224z"},null,-1);function O_(e,t,n,o,r,s){return c(),i("svg",T_,[I_])}U4.render=O_;U4.__file="packages/components/MoreFilled.vue";const N_=U4;var G4=l({name:"Mouse"});const P_={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},D_=a("path",{fill:"currentColor",d:"M438.144 256c-68.352 0-92.736 4.672-117.76 18.112-20.096 10.752-35.52 26.176-46.272 46.272C260.672 345.408 256 369.792 256 438.144v275.712c0 68.352 4.672 92.736 18.112 117.76 10.752 20.096 26.176 35.52 46.272 46.272C345.408 891.328 369.792 896 438.144 896h147.712c68.352 0 92.736-4.672 117.76-18.112 20.096-10.752 35.52-26.176 46.272-46.272C763.328 806.592 768 782.208 768 713.856V438.144c0-68.352-4.672-92.736-18.112-117.76a110.464 110.464 0 0 0-46.272-46.272C678.592 260.672 654.208 256 585.856 256H438.144zm0-64h147.712c85.568 0 116.608 8.96 147.904 25.6 31.36 16.768 55.872 41.344 72.576 72.64C823.104 321.536 832 352.576 832 438.08v275.84c0 85.504-8.96 116.544-25.6 147.84a174.464 174.464 0 0 1-72.64 72.576C702.464 951.104 671.424 960 585.92 960H438.08c-85.504 0-116.544-8.96-147.84-25.6a174.464 174.464 0 0 1-72.64-72.704c-16.768-31.296-25.664-62.336-25.664-147.84v-275.84c0-85.504 8.96-116.544 25.6-147.84a174.464 174.464 0 0 1 72.768-72.576c31.232-16.704 62.272-25.6 147.776-25.6z"},null,-1),R_=a("path",{fill:"currentColor",d:"M512 320q32 0 32 32v128q0 32-32 32t-32-32V352q0-32 32-32zm32-96a32 32 0 0 1-64 0v-64a32 32 0 0 0-32-32h-96a32 32 0 0 1 0-64h96a96 96 0 0 1 96 96v64z"},null,-1);function U_(e,t,n,o,r,s){return c(),i("svg",P_,[D_,R_])}G4.render=U_;G4.__file="packages/components/Mouse.vue";const G_=G4;var W4=l({name:"Mug"});const W_={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Z_=a("path",{fill:"currentColor",d:"M736 800V160H160v640a64 64 0 0 0 64 64h448a64 64 0 0 0 64-64zm64-544h63.552a96 96 0 0 1 96 96v224a96 96 0 0 1-96 96H800v128a128 128 0 0 1-128 128H224A128 128 0 0 1 96 800V128a32 32 0 0 1 32-32h640a32 32 0 0 1 32 32v128zm0 64v288h63.552a32 32 0 0 0 32-32V352a32 32 0 0 0-32-32H800z"},null,-1);function q_(e,t,n,o,r,s){return c(),i("svg",W_,[Z_])}W4.render=q_;W4.__file="packages/components/Mug.vue";const j_=W4;var Z4=l({name:"Mute"});const Y_={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},K_=a("path",{fill:"currentColor",d:"m412.16 592.128-45.44 45.44A191.232 191.232 0 0 1 320 512V256a192 192 0 1 1 384 0v44.352l-64 64V256a128 128 0 1 0-256 0v256c0 30.336 10.56 58.24 28.16 80.128zm51.968 38.592A128 128 0 0 0 640 512v-57.152l64-64V512a192 192 0 0 1-287.68 166.528l47.808-47.808zM314.88 779.968l46.144-46.08A222.976 222.976 0 0 0 480 768h64a224 224 0 0 0 224-224v-32a32 32 0 1 1 64 0v32a288 288 0 0 1-288 288v64h64a32 32 0 1 1 0 64H416a32 32 0 1 1 0-64h64v-64c-61.44 0-118.4-19.2-165.12-52.032zM266.752 737.6A286.976 286.976 0 0 1 192 544v-32a32 32 0 0 1 64 0v32c0 56.832 21.184 108.8 56.064 148.288L266.752 737.6z"},null,-1),X_=a("path",{fill:"currentColor",d:"M150.72 859.072a32 32 0 0 1-45.44-45.056l704-708.544a32 32 0 0 1 45.44 45.056l-704 708.544z"},null,-1);function J_(e,t,n,o,r,s){return c(),i("svg",Y_,[K_,X_])}Z4.render=J_;Z4.__file="packages/components/Mute.vue";const Q_=Z4;var q4=l({name:"NoSmoking"});const ed={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},td=a("path",{fill:"currentColor",d:"M440.256 576H256v128h56.256l-64 64H224a32 32 0 0 1-32-32V544a32 32 0 0 1 32-32h280.256l-64 64zm143.488 128H704V583.744L775.744 512H928a32 32 0 0 1 32 32v192a32 32 0 0 1-32 32H519.744l64-64zM768 576v128h128V576H768zm-29.696-207.552 45.248 45.248-497.856 497.856-45.248-45.248zM256 64h64v320h-64zM128 192h64v192h-64zM64 512h64v256H64z"},null,-1);function nd(e,t,n,o,r,s){return c(),i("svg",ed,[td])}q4.render=nd;q4.__file="packages/components/NoSmoking.vue";const od=q4;var j4=l({name:"MuteNotification"});const rd={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},sd=a("path",{fill:"currentColor",d:"m241.216 832 63.616-64H768V448c0-42.368-10.24-82.304-28.48-117.504l46.912-47.232C815.36 331.392 832 387.84 832 448v320h96a32 32 0 1 1 0 64H241.216zm-90.24 0H96a32 32 0 1 1 0-64h96V448a320.128 320.128 0 0 1 256-313.6V128a64 64 0 1 1 128 0v6.4a319.552 319.552 0 0 1 171.648 97.088l-45.184 45.44A256 256 0 0 0 256 448v278.336L151.04 832zM448 896h128a64 64 0 0 1-128 0z"},null,-1),ad=a("path",{fill:"currentColor",d:"M150.72 859.072a32 32 0 0 1-45.44-45.056l704-708.544a32 32 0 0 1 45.44 45.056l-704 708.544z"},null,-1);function cd(e,t,n,o,r,s){return c(),i("svg",rd,[sd,ad])}j4.render=cd;j4.__file="packages/components/MuteNotification.vue";const ld=j4;var Y4=l({name:"Notification"});const id={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},ud=a("path",{fill:"currentColor",d:"M512 128v64H256a64 64 0 0 0-64 64v512a64 64 0 0 0 64 64h512a64 64 0 0 0 64-64V512h64v256a128 128 0 0 1-128 128H256a128 128 0 0 1-128-128V256a128 128 0 0 1 128-128h256z"},null,-1),pd=a("path",{fill:"currentColor",d:"M768 384a128 128 0 1 0 0-256 128 128 0 0 0 0 256zm0 64a192 192 0 1 1 0-384 192 192 0 0 1 0 384z"},null,-1);function _d(e,t,n,o,r,s){return c(),i("svg",id,[ud,pd])}Y4.render=_d;Y4.__file="packages/components/Notification.vue";const dd=Y4;var K4=l({name:"Notebook"});const hd={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},$d=a("path",{fill:"currentColor",d:"M192 128v768h640V128H192zm-32-64h704a32 32 0 0 1 32 32v832a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32z"},null,-1),fd=a("path",{fill:"currentColor",d:"M672 128h64v768h-64zM96 192h128q32 0 32 32t-32 32H96q-32 0-32-32t32-32zm0 192h128q32 0 32 32t-32 32H96q-32 0-32-32t32-32zm0 192h128q32 0 32 32t-32 32H96q-32 0-32-32t32-32zm0 192h128q32 0 32 32t-32 32H96q-32 0-32-32t32-32z"},null,-1);function md(e,t,n,o,r,s){return c(),i("svg",hd,[$d,fd])}K4.render=md;K4.__file="packages/components/Notebook.vue";const vd=K4;var X4=l({name:"Odometer"});const gd={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},wd=a("path",{fill:"currentColor",d:"M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768zm0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896z"},null,-1),zd=a("path",{fill:"currentColor",d:"M192 512a320 320 0 1 1 640 0 32 32 0 1 1-64 0 256 256 0 1 0-512 0 32 32 0 0 1-64 0z"},null,-1),xd=a("path",{fill:"currentColor",d:"M570.432 627.84A96 96 0 1 1 509.568 608l60.992-187.776A32 32 0 1 1 631.424 440l-60.992 187.776zM502.08 734.464a32 32 0 1 0 19.84-60.928 32 32 0 0 0-19.84 60.928z"},null,-1);function Cd(e,t,n,o,r,s){return c(),i("svg",gd,[wd,zd,xd])}X4.render=Cd;X4.__file="packages/components/Odometer.vue";const Md=X4;var J4=l({name:"OfficeBuilding"});const Ld={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Hd=a("path",{fill:"currentColor",d:"M192 128v704h384V128H192zm-32-64h448a32 32 0 0 1 32 32v768a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32z"},null,-1),kd=a("path",{fill:"currentColor",d:"M256 256h256v64H256v-64zm0 192h256v64H256v-64zm0 192h256v64H256v-64zm384-128h128v64H640v-64zm0 128h128v64H640v-64zM64 832h896v64H64v-64z"},null,-1),Vd=a("path",{fill:"currentColor",d:"M640 384v448h192V384H640zm-32-64h256a32 32 0 0 1 32 32v512a32 32 0 0 1-32 32H608a32 32 0 0 1-32-32V352a32 32 0 0 1 32-32z"},null,-1);function bd(e,t,n,o,r,s){return c(),i("svg",Ld,[Hd,kd,Vd])}J4.render=bd;J4.__file="packages/components/OfficeBuilding.vue";const yd=J4;var Q4=l({name:"Operation"});const Ad={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Ed=a("path",{fill:"currentColor",d:"M389.44 768a96.064 96.064 0 0 1 181.12 0H896v64H570.56a96.064 96.064 0 0 1-181.12 0H128v-64h261.44zm192-288a96.064 96.064 0 0 1 181.12 0H896v64H762.56a96.064 96.064 0 0 1-181.12 0H128v-64h453.44zm-320-288a96.064 96.064 0 0 1 181.12 0H896v64H442.56a96.064 96.064 0 0 1-181.12 0H128v-64h133.44z"},null,-1);function Sd(e,t,n,o,r,s){return c(),i("svg",Ad,[Ed])}Q4.render=Sd;Q4.__file="packages/components/Operation.vue";const Bd=Q4;var et=l({name:"Opportunity"});const Fd={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Td=a("path",{fill:"currentColor",d:"M384 960v-64h192.064v64H384zm448-544a350.656 350.656 0 0 1-128.32 271.424C665.344 719.04 640 763.776 640 813.504V832H320v-14.336c0-48-19.392-95.36-57.216-124.992a351.552 351.552 0 0 1-128.448-344.256c25.344-136.448 133.888-248.128 269.76-276.48A352.384 352.384 0 0 1 832 416zm-544 32c0-132.288 75.904-224 192-224v-64c-154.432 0-256 122.752-256 288h64z"},null,-1);function Id(e,t,n,o,r,s){return c(),i("svg",Fd,[Td])}et.render=Id;et.__file="packages/components/Opportunity.vue";const Od=et;var tt=l({name:"Orange"});const Nd={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Pd=a("path",{fill:"currentColor",d:"M544 894.72a382.336 382.336 0 0 0 215.936-89.472L577.024 622.272c-10.24 6.016-21.248 10.688-33.024 13.696v258.688zm261.248-134.784A382.336 382.336 0 0 0 894.656 544H635.968c-3.008 11.776-7.68 22.848-13.696 33.024l182.976 182.912zM894.656 480a382.336 382.336 0 0 0-89.408-215.936L622.272 446.976c6.016 10.24 10.688 21.248 13.696 33.024h258.688zm-134.72-261.248A382.336 382.336 0 0 0 544 129.344v258.688c11.776 3.008 22.848 7.68 33.024 13.696l182.912-182.976zM480 129.344a382.336 382.336 0 0 0-215.936 89.408l182.912 182.976c10.24-6.016 21.248-10.688 33.024-13.696V129.344zm-261.248 134.72A382.336 382.336 0 0 0 129.344 480h258.688c3.008-11.776 7.68-22.848 13.696-33.024L218.752 264.064zM129.344 544a382.336 382.336 0 0 0 89.408 215.936l182.976-182.912A127.232 127.232 0 0 1 388.032 544H129.344zm134.72 261.248A382.336 382.336 0 0 0 480 894.656V635.968a127.232 127.232 0 0 1-33.024-13.696L264.064 805.248zM512 960a448 448 0 1 1 0-896 448 448 0 0 1 0 896zm0-384a64 64 0 1 0 0-128 64 64 0 0 0 0 128z"},null,-1);function Dd(e,t,n,o,r,s){return c(),i("svg",Nd,[Pd])}tt.render=Dd;tt.__file="packages/components/Orange.vue";const Rd=tt;var nt=l({name:"Open"});const Ud={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Gd=a("path",{fill:"currentColor",d:"M329.956 257.138a254.862 254.862 0 0 0 0 509.724h364.088a254.862 254.862 0 0 0 0-509.724H329.956zm0-72.818h364.088a327.68 327.68 0 1 1 0 655.36H329.956a327.68 327.68 0 1 1 0-655.36z"},null,-1),Wd=a("path",{fill:"currentColor",d:"M694.044 621.227a109.227 109.227 0 1 0 0-218.454 109.227 109.227 0 0 0 0 218.454zm0 72.817a182.044 182.044 0 1 1 0-364.088 182.044 182.044 0 0 1 0 364.088z"},null,-1);function Zd(e,t,n,o,r,s){return c(),i("svg",Ud,[Gd,Wd])}nt.render=Zd;nt.__file="packages/components/Open.vue";const qd=nt;var ot=l({name:"Paperclip"});const jd={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Yd=a("path",{fill:"currentColor",d:"M602.496 240.448A192 192 0 1 1 874.048 512l-316.8 316.8A256 256 0 0 1 195.2 466.752L602.496 59.456l45.248 45.248L240.448 512A192 192 0 0 0 512 783.552l316.8-316.8a128 128 0 1 0-181.056-181.056L353.6 579.904a32 32 0 1 0 45.248 45.248l294.144-294.144 45.312 45.248L444.096 670.4a96 96 0 1 1-135.744-135.744l294.144-294.208z"},null,-1);function Kd(e,t,n,o,r,s){return c(),i("svg",jd,[Yd])}ot.render=Kd;ot.__file="packages/components/Paperclip.vue";const Xd=ot;var rt=l({name:"Pear"});const Jd={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Qd=a("path",{fill:"currentColor",d:"M542.336 258.816a443.255 443.255 0 0 0-9.024 25.088 32 32 0 1 1-60.8-20.032l1.088-3.328a162.688 162.688 0 0 0-122.048 131.392l-17.088 102.72-20.736 15.36C256.192 552.704 224 610.88 224 672c0 120.576 126.4 224 288 224s288-103.424 288-224c0-61.12-32.192-119.296-89.728-161.92l-20.736-15.424-17.088-102.72a162.688 162.688 0 0 0-130.112-133.12zm-40.128-66.56c7.936-15.552 16.576-30.08 25.92-43.776 23.296-33.92 49.408-59.776 78.528-77.12a32 32 0 1 1 32.704 55.04c-20.544 12.224-40.064 31.552-58.432 58.304a316.608 316.608 0 0 0-9.792 15.104 226.688 226.688 0 0 1 164.48 181.568l12.8 77.248C819.456 511.36 864 587.392 864 672c0 159.04-157.568 288-352 288S160 831.04 160 672c0-84.608 44.608-160.64 115.584-213.376l12.8-77.248a226.624 226.624 0 0 1 213.76-189.184z"},null,-1);function eh(e,t,n,o,r,s){return c(),i("svg",Jd,[Qd])}rt.render=eh;rt.__file="packages/components/Pear.vue";const th=rt;var st=l({name:"PartlyCloudy"});const nh={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},oh=a("path",{fill:"currentColor",d:"M598.4 895.872H328.192a256 256 0 0 1-34.496-510.528A352 352 0 1 1 598.4 895.872zm-271.36-64h272.256a288 288 0 1 0-248.512-417.664L335.04 445.44l-34.816 3.584a192 192 0 0 0 26.88 382.848z"},null,-1),rh=a("path",{fill:"currentColor",d:"M139.84 501.888a256 256 0 1 1 417.856-277.12c-17.728 2.176-38.208 8.448-61.504 18.816A192 192 0 1 0 189.12 460.48a6003.84 6003.84 0 0 0-49.28 41.408z"},null,-1);function sh(e,t,n,o,r,s){return c(),i("svg",nh,[oh,rh])}st.render=sh;st.__file="packages/components/PartlyCloudy.vue";const ah=st;var at=l({name:"Phone"});const ch={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},lh=a("path",{fill:"currentColor",d:"M79.36 432.256 591.744 944.64a32 32 0 0 0 35.2 6.784l253.44-108.544a32 32 0 0 0 9.984-52.032l-153.856-153.92a32 32 0 0 0-36.928-6.016l-69.888 34.944L358.08 394.24l35.008-69.888a32 32 0 0 0-5.952-36.928L233.152 133.568a32 32 0 0 0-52.032 10.048L72.512 397.056a32 32 0 0 0 6.784 35.2zm60.48-29.952 81.536-190.08L325.568 316.48l-24.64 49.216-20.608 41.216 32.576 32.64 271.552 271.552 32.64 32.64 41.216-20.672 49.28-24.576 104.192 104.128-190.08 81.472L139.84 402.304zM512 320v-64a256 256 0 0 1 256 256h-64a192 192 0 0 0-192-192zm0-192V64a448 448 0 0 1 448 448h-64a384 384 0 0 0-384-384z"},null,-1);function ih(e,t,n,o,r,s){return c(),i("svg",ch,[lh])}at.render=ih;at.__file="packages/components/Phone.vue";const uh=at;var ct=l({name:"PictureFilled"});const ph={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},_h=a("path",{fill:"currentColor",d:"M96 896a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h832a32 32 0 0 1 32 32v704a32 32 0 0 1-32 32H96zm315.52-228.48-68.928-68.928a32 32 0 0 0-45.248 0L128 768.064h778.688l-242.112-290.56a32 32 0 0 0-49.216 0L458.752 665.408a32 32 0 0 1-47.232 2.112zM256 384a96 96 0 1 0 192.064-.064A96 96 0 0 0 256 384z"},null,-1);function dh(e,t,n,o,r,s){return c(),i("svg",ph,[_h])}ct.render=dh;ct.__file="packages/components/PictureFilled.vue";const hh=ct;var lt=l({name:"PhoneFilled"});const $h={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},fh=a("path",{fill:"currentColor",d:"M199.232 125.568 90.624 379.008a32 32 0 0 0 6.784 35.2l512.384 512.384a32 32 0 0 0 35.2 6.784l253.44-108.608a32 32 0 0 0 10.048-52.032L769.6 633.92a32 32 0 0 0-36.928-5.952l-130.176 65.088-271.488-271.552 65.024-130.176a32 32 0 0 0-5.952-36.928L251.2 115.52a32 32 0 0 0-51.968 10.048z"},null,-1);function mh(e,t,n,o,r,s){return c(),i("svg",$h,[fh])}lt.render=mh;lt.__file="packages/components/PhoneFilled.vue";const vh=lt;var it=l({name:"PictureRounded"});const gh={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},wh=a("path",{fill:"currentColor",d:"M512 128a384 384 0 1 0 0 768 384 384 0 0 0 0-768zm0-64a448 448 0 1 1 0 896 448 448 0 0 1 0-896z"},null,-1),zh=a("path",{fill:"currentColor",d:"M640 288q64 0 64 64t-64 64q-64 0-64-64t64-64zM214.656 790.656l-45.312-45.312 185.664-185.6a96 96 0 0 1 123.712-10.24l138.24 98.688a32 32 0 0 0 39.872-2.176L906.688 422.4l42.624 47.744L699.52 693.696a96 96 0 0 1-119.808 6.592l-138.24-98.752a32 32 0 0 0-41.152 3.456l-185.664 185.6z"},null,-1);function xh(e,t,n,o,r,s){return c(),i("svg",gh,[wh,zh])}it.render=xh;it.__file="packages/components/PictureRounded.vue";const Ch=it;var ut=l({name:"Guide"});const Mh={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Lh=a("path",{fill:"currentColor",d:"M640 608h-64V416h64v192zm0 160v160a32 32 0 0 1-32 32H416a32 32 0 0 1-32-32V768h64v128h128V768h64zM384 608V416h64v192h-64zm256-352h-64V128H448v128h-64V96a32 32 0 0 1 32-32h192a32 32 0 0 1 32 32v160z"},null,-1),Hh=a("path",{fill:"currentColor",d:"m220.8 256-71.232 80 71.168 80H768V256H220.8zm-14.4-64H800a32 32 0 0 1 32 32v224a32 32 0 0 1-32 32H206.4a32 32 0 0 1-23.936-10.752l-99.584-112a32 32 0 0 1 0-42.496l99.584-112A32 32 0 0 1 206.4 192zm678.784 496-71.104 80H266.816V608h547.2l71.168 80zm-56.768-144H234.88a32 32 0 0 0-32 32v224a32 32 0 0 0 32 32h593.6a32 32 0 0 0 23.936-10.752l99.584-112a32 32 0 0 0 0-42.496l-99.584-112A32 32 0 0 0 828.48 544z"},null,-1);function kh(e,t,n,o,r,s){return c(),i("svg",Mh,[Lh,Hh])}ut.render=kh;ut.__file="packages/components/Guide.vue";const Vh=ut;var pt=l({name:"Place"});const bh={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},yh=a("path",{fill:"currentColor",d:"M512 512a192 192 0 1 0 0-384 192 192 0 0 0 0 384zm0 64a256 256 0 1 1 0-512 256 256 0 0 1 0 512z"},null,-1),Ah=a("path",{fill:"currentColor",d:"M512 512a32 32 0 0 1 32 32v256a32 32 0 1 1-64 0V544a32 32 0 0 1 32-32z"},null,-1),Eh=a("path",{fill:"currentColor",d:"M384 649.088v64.96C269.76 732.352 192 771.904 192 800c0 37.696 139.904 96 320 96s320-58.304 320-96c0-28.16-77.76-67.648-192-85.952v-64.96C789.12 671.04 896 730.368 896 800c0 88.32-171.904 160-384 160s-384-71.68-384-160c0-69.696 106.88-128.96 256-150.912z"},null,-1);function Sh(e,t,n,o,r,s){return c(),i("svg",bh,[yh,Ah,Eh])}pt.render=Sh;pt.__file="packages/components/Place.vue";const Bh=pt;var _t=l({name:"Platform"});const Fh={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Th=a("path",{fill:"currentColor",d:"M448 832v-64h128v64h192v64H256v-64h192zM128 704V128h768v576H128z"},null,-1);function Ih(e,t,n,o,r,s){return c(),i("svg",Fh,[Th])}_t.render=Ih;_t.__file="packages/components/Platform.vue";const Oh=_t;var dt=l({name:"PieChart"});const Nh={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Ph=a("path",{fill:"currentColor",d:"M448 68.48v64.832A384.128 384.128 0 0 0 512 896a384.128 384.128 0 0 0 378.688-320h64.768A448.128 448.128 0 0 1 64 512 448.128 448.128 0 0 1 448 68.48z"},null,-1),Dh=a("path",{fill:"currentColor",d:"M576 97.28V448h350.72A384.064 384.064 0 0 0 576 97.28zM512 64V33.152A448 448 0 0 1 990.848 512H512V64z"},null,-1);function Rh(e,t,n,o,r,s){return c(),i("svg",Nh,[Ph,Dh])}dt.render=Rh;dt.__file="packages/components/PieChart.vue";const Uh=dt;var ht=l({name:"Pointer"});const Gh={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Wh=a("path",{fill:"currentColor",d:"M511.552 128c-35.584 0-64.384 28.8-64.384 64.448v516.48L274.048 570.88a94.272 94.272 0 0 0-112.896-3.456 44.416 44.416 0 0 0-8.96 62.208L332.8 870.4A64 64 0 0 0 384 896h512V575.232a64 64 0 0 0-45.632-61.312l-205.952-61.76A96 96 0 0 1 576 360.192V192.448C576 156.8 547.2 128 511.552 128zM359.04 556.8l24.128 19.2V192.448a128.448 128.448 0 1 1 256.832 0v167.744a32 32 0 0 0 22.784 30.656l206.016 61.76A128 128 0 0 1 960 575.232V896a64 64 0 0 1-64 64H384a128 128 0 0 1-102.4-51.2L101.056 668.032A108.416 108.416 0 0 1 128 512.512a158.272 158.272 0 0 1 185.984 8.32L359.04 556.8z"},null,-1);function Zh(e,t,n,o,r,s){return c(),i("svg",Gh,[Wh])}ht.render=Zh;ht.__file="packages/components/Pointer.vue";const qh=ht;var $t=l({name:"Plus"});const jh={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Yh=a("path",{fill:"currentColor",d:"M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64h352z"},null,-1);function Kh(e,t,n,o,r,s){return c(),i("svg",jh,[Yh])}$t.render=Kh;$t.__file="packages/components/Plus.vue";const Q6=$t;var ft=l({name:"Position"});const Xh={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Jh=a("path",{fill:"currentColor",d:"m249.6 417.088 319.744 43.072 39.168 310.272L845.12 178.88 249.6 417.088zm-129.024 47.168a32 32 0 0 1-7.68-61.44l777.792-311.04a32 32 0 0 1 41.6 41.6l-310.336 775.68a32 32 0 0 1-61.44-7.808L512 516.992l-391.424-52.736z"},null,-1);function Qh(e,t,n,o,r,s){return c(),i("svg",Xh,[Jh])}ft.render=Qh;ft.__file="packages/components/Position.vue";const e$=ft;var mt=l({name:"Postcard"});const t$={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},n$=a("path",{fill:"currentColor",d:"M160 224a32 32 0 0 0-32 32v512a32 32 0 0 0 32 32h704a32 32 0 0 0 32-32V256a32 32 0 0 0-32-32H160zm0-64h704a96 96 0 0 1 96 96v512a96 96 0 0 1-96 96H160a96 96 0 0 1-96-96V256a96 96 0 0 1 96-96z"},null,-1),o$=a("path",{fill:"currentColor",d:"M704 320a64 64 0 1 1 0 128 64 64 0 0 1 0-128zM288 448h256q32 0 32 32t-32 32H288q-32 0-32-32t32-32zm0 128h256q32 0 32 32t-32 32H288q-32 0-32-32t32-32z"},null,-1);function r$(e,t,n,o,r,s){return c(),i("svg",t$,[n$,o$])}mt.render=r$;mt.__file="packages/components/Postcard.vue";const s$=mt;var vt=l({name:"Present"});const a$={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},c$=a("path",{fill:"currentColor",d:"M480 896V640H192v-64h288V320H192v576h288zm64 0h288V320H544v256h288v64H544v256zM128 256h768v672a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V256z"},null,-1),l$=a("path",{fill:"currentColor",d:"M96 256h832q32 0 32 32t-32 32H96q-32 0-32-32t32-32z"},null,-1),i$=a("path",{fill:"currentColor",d:"M416 256a64 64 0 1 0 0-128 64 64 0 0 0 0 128zm0 64a128 128 0 1 1 0-256 128 128 0 0 1 0 256z"},null,-1),u$=a("path",{fill:"currentColor",d:"M608 256a64 64 0 1 0 0-128 64 64 0 0 0 0 128zm0 64a128 128 0 1 1 0-256 128 128 0 0 1 0 256z"},null,-1);function p$(e,t,n,o,r,s){return c(),i("svg",a$,[c$,l$,i$,u$])}vt.render=p$;vt.__file="packages/components/Present.vue";const _$=vt;var gt=l({name:"PriceTag"});const d$={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},h$=a("path",{fill:"currentColor",d:"M224 318.336V896h576V318.336L552.512 115.84a64 64 0 0 0-81.024 0L224 318.336zM593.024 66.304l259.2 212.096A32 32 0 0 1 864 303.168V928a32 32 0 0 1-32 32H192a32 32 0 0 1-32-32V303.168a32 32 0 0 1 11.712-24.768l259.2-212.096a128 128 0 0 1 162.112 0z"},null,-1),$$=a("path",{fill:"currentColor",d:"M512 448a64 64 0 1 0 0-128 64 64 0 0 0 0 128zm0 64a128 128 0 1 1 0-256 128 128 0 0 1 0 256z"},null,-1);function f$(e,t,n,o,r,s){return c(),i("svg",d$,[h$,$$])}gt.render=f$;gt.__file="packages/components/PriceTag.vue";const m$=gt;var wt=l({name:"Promotion"});const v$={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},g$=a("path",{fill:"currentColor",d:"m64 448 832-320-128 704-446.08-243.328L832 192 242.816 545.472 64 448zm256 512V657.024L512 768 320 960z"},null,-1);function w$(e,t,n,o,r,s){return c(),i("svg",v$,[g$])}wt.render=w$;wt.__file="packages/components/Promotion.vue";const z$=wt;var zt=l({name:"Pouring"});const x$={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},C$=a("path",{fill:"currentColor",d:"m739.328 291.328-35.2-6.592-12.8-33.408a192.064 192.064 0 0 0-365.952 23.232l-9.92 40.896-41.472 7.04a176.32 176.32 0 0 0-146.24 173.568c0 97.28 78.72 175.936 175.808 175.936h400a192 192 0 0 0 35.776-380.672zM959.552 480a256 256 0 0 1-256 256h-400A239.808 239.808 0 0 1 63.744 496.192a240.32 240.32 0 0 1 199.488-236.8 256.128 256.128 0 0 1 487.872-30.976A256.064 256.064 0 0 1 959.552 480zM224 800a32 32 0 0 1 32 32v96a32 32 0 1 1-64 0v-96a32 32 0 0 1 32-32zm192 0a32 32 0 0 1 32 32v96a32 32 0 1 1-64 0v-96a32 32 0 0 1 32-32zm192 0a32 32 0 0 1 32 32v96a32 32 0 1 1-64 0v-96a32 32 0 0 1 32-32zm192 0a32 32 0 0 1 32 32v96a32 32 0 1 1-64 0v-96a32 32 0 0 1 32-32z"},null,-1);function M$(e,t,n,o,r,s){return c(),i("svg",x$,[C$])}zt.render=M$;zt.__file="packages/components/Pouring.vue";const L$=zt;var xt=l({name:"ReadingLamp"});const H$={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},k$=a("path",{fill:"currentColor",d:"M352 896h320q32 0 32 32t-32 32H352q-32 0-32-32t32-32zm-44.672-768-99.52 448h608.384l-99.52-448H307.328zm-25.6-64h460.608a32 32 0 0 1 31.232 25.088l113.792 512A32 32 0 0 1 856.128 640H167.872a32 32 0 0 1-31.232-38.912l113.792-512A32 32 0 0 1 281.664 64z"},null,-1),V$=a("path",{fill:"currentColor",d:"M672 576q32 0 32 32v128q0 32-32 32t-32-32V608q0-32 32-32zm-192-.064h64V960h-64z"},null,-1);function b$(e,t,n,o,r,s){return c(),i("svg",H$,[k$,V$])}xt.render=b$;xt.__file="packages/components/ReadingLamp.vue";const y$=xt;var Ct=l({name:"QuestionFilled"});const A$={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},E$=a("path",{fill:"currentColor",d:"M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896zm23.744 191.488c-52.096 0-92.928 14.784-123.2 44.352-30.976 29.568-45.76 70.4-45.76 122.496h80.256c0-29.568 5.632-52.8 17.6-68.992 13.376-19.712 35.2-28.864 66.176-28.864 23.936 0 42.944 6.336 56.32 19.712 12.672 13.376 19.712 31.68 19.712 54.912 0 17.6-6.336 34.496-19.008 49.984l-8.448 9.856c-45.76 40.832-73.216 70.4-82.368 89.408-9.856 19.008-14.08 42.24-14.08 68.992v9.856h80.96v-9.856c0-16.896 3.52-31.68 10.56-45.76 6.336-12.672 15.488-24.64 28.16-35.2 33.792-29.568 54.208-48.576 60.544-55.616 16.896-22.528 26.048-51.392 26.048-86.592 0-42.944-14.08-76.736-42.24-101.376-28.16-25.344-65.472-37.312-111.232-37.312zm-12.672 406.208a54.272 54.272 0 0 0-38.72 14.784 49.408 49.408 0 0 0-15.488 38.016c0 15.488 4.928 28.16 15.488 38.016A54.848 54.848 0 0 0 523.072 768c15.488 0 28.16-4.928 38.72-14.784a51.52 51.52 0 0 0 16.192-38.72 51.968 51.968 0 0 0-15.488-38.016 55.936 55.936 0 0 0-39.424-14.784z"},null,-1);function S$(e,t,n,o,r,s){return c(),i("svg",A$,[E$])}Ct.render=S$;Ct.__file="packages/components/QuestionFilled.vue";const B$=Ct;var Mt=l({name:"Printer"});const F$={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},T$=a("path",{fill:"currentColor",d:"M256 768H105.024c-14.272 0-19.456-1.472-24.64-4.288a29.056 29.056 0 0 1-12.16-12.096C65.536 746.432 64 741.248 64 727.04V379.072c0-42.816 4.48-58.304 12.8-73.984 8.384-15.616 20.672-27.904 36.288-36.288 15.68-8.32 31.168-12.8 73.984-12.8H256V64h512v192h68.928c42.816 0 58.304 4.48 73.984 12.8 15.616 8.384 27.904 20.672 36.288 36.288 8.32 15.68 12.8 31.168 12.8 73.984v347.904c0 14.272-1.472 19.456-4.288 24.64a29.056 29.056 0 0 1-12.096 12.16c-5.184 2.752-10.368 4.224-24.64 4.224H768v192H256V768zm64-192v320h384V576H320zm-64 128V512h512v192h128V379.072c0-29.376-1.408-36.48-5.248-43.776a23.296 23.296 0 0 0-10.048-10.048c-7.232-3.84-14.4-5.248-43.776-5.248H187.072c-29.376 0-36.48 1.408-43.776 5.248a23.296 23.296 0 0 0-10.048 10.048c-3.84 7.232-5.248 14.4-5.248 43.776V704h128zm64-448h384V128H320v128zm-64 128h64v64h-64v-64zm128 0h64v64h-64v-64z"},null,-1);function I$(e,t,n,o,r,s){return c(),i("svg",F$,[T$])}Mt.render=I$;Mt.__file="packages/components/Printer.vue";const O$=Mt;var Lt=l({name:"Picture"});const N$={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},P$=a("path",{fill:"currentColor",d:"M160 160v704h704V160H160zm-32-64h768a32 32 0 0 1 32 32v768a32 32 0 0 1-32 32H128a32 32 0 0 1-32-32V128a32 32 0 0 1 32-32z"},null,-1),D$=a("path",{fill:"currentColor",d:"M384 288q64 0 64 64t-64 64q-64 0-64-64t64-64zM185.408 876.992l-50.816-38.912L350.72 556.032a96 96 0 0 1 134.592-17.856l1.856 1.472 122.88 99.136a32 32 0 0 0 44.992-4.864l216-269.888 49.92 39.936-215.808 269.824-.256.32a96 96 0 0 1-135.04 14.464l-122.88-99.072-.64-.512a32 32 0 0 0-44.8 5.952L185.408 876.992z"},null,-1);function R$(e,t,n,o,r,s){return c(),i("svg",N$,[P$,D$])}Lt.render=R$;Lt.__file="packages/components/Picture.vue";const U$=Lt;var Ht=l({name:"RefreshRight"});const G$={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},W$=a("path",{fill:"currentColor",d:"M784.512 230.272v-50.56a32 32 0 1 1 64 0v149.056a32 32 0 0 1-32 32H667.52a32 32 0 1 1 0-64h92.992A320 320 0 1 0 524.8 833.152a320 320 0 0 0 320-320h64a384 384 0 0 1-384 384 384 384 0 0 1-384-384 384 384 0 0 1 643.712-282.88z"},null,-1);function Z$(e,t,n,o,r,s){return c(),i("svg",G$,[W$])}Ht.render=Z$;Ht.__file="packages/components/RefreshRight.vue";const q$=Ht;var kt=l({name:"Reading"});const j$={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Y$=a("path",{fill:"currentColor",d:"m512 863.36 384-54.848v-638.72L525.568 222.72a96 96 0 0 1-27.136 0L128 169.792v638.72l384 54.848zM137.024 106.432l370.432 52.928a32 32 0 0 0 9.088 0l370.432-52.928A64 64 0 0 1 960 169.792v638.72a64 64 0 0 1-54.976 63.36l-388.48 55.488a32 32 0 0 1-9.088 0l-388.48-55.488A64 64 0 0 1 64 808.512v-638.72a64 64 0 0 1 73.024-63.36z"},null,-1),K$=a("path",{fill:"currentColor",d:"M480 192h64v704h-64z"},null,-1);function X$(e,t,n,o,r,s){return c(),i("svg",j$,[Y$,K$])}kt.render=X$;kt.__file="packages/components/Reading.vue";const J$=kt;var Vt=l({name:"RefreshLeft"});const Q$={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},ef=a("path",{fill:"currentColor",d:"M289.088 296.704h92.992a32 32 0 0 1 0 64H232.96a32 32 0 0 1-32-32V179.712a32 32 0 0 1 64 0v50.56a384 384 0 0 1 643.84 282.88 384 384 0 0 1-383.936 384 384 384 0 0 1-384-384h64a320 320 0 1 0 640 0 320 320 0 0 0-555.712-216.448z"},null,-1);function tf(e,t,n,o,r,s){return c(),i("svg",Q$,[ef])}Vt.render=tf;Vt.__file="packages/components/RefreshLeft.vue";const nf=Vt;var bt=l({name:"Refresh"});const of={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},rf=a("path",{fill:"currentColor",d:"M771.776 794.88A384 384 0 0 1 128 512h64a320 320 0 0 0 555.712 216.448H654.72a32 32 0 1 1 0-64h149.056a32 32 0 0 1 32 32v148.928a32 32 0 1 1-64 0v-50.56zM276.288 295.616h92.992a32 32 0 0 1 0 64H220.16a32 32 0 0 1-32-32V178.56a32 32 0 0 1 64 0v50.56A384 384 0 0 1 896.128 512h-64a320 320 0 0 0-555.776-216.384z"},null,-1);function sf(e,t,n,o,r,s){return c(),i("svg",of,[rf])}bt.render=sf;bt.__file="packages/components/Refresh.vue";const af=bt;var yt=l({name:"Refrigerator"});const cf={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},lf=a("path",{fill:"currentColor",d:"M256 448h512V160a32 32 0 0 0-32-32H288a32 32 0 0 0-32 32v288zm0 64v352a32 32 0 0 0 32 32h448a32 32 0 0 0 32-32V512H256zm32-448h448a96 96 0 0 1 96 96v704a96 96 0 0 1-96 96H288a96 96 0 0 1-96-96V160a96 96 0 0 1 96-96zm32 224h64v96h-64v-96zm0 288h64v96h-64v-96z"},null,-1);function uf(e,t,n,o,r,s){return c(),i("svg",cf,[lf])}yt.render=uf;yt.__file="packages/components/Refrigerator.vue";const pf=yt;var At=l({name:"RemoveFilled"});const _f={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},df=a("path",{fill:"currentColor",d:"M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896zM288 512a38.4 38.4 0 0 0 38.4 38.4h371.2a38.4 38.4 0 0 0 0-76.8H326.4A38.4 38.4 0 0 0 288 512z"},null,-1);function hf(e,t,n,o,r,s){return c(),i("svg",_f,[df])}At.render=hf;At.__file="packages/components/RemoveFilled.vue";const $f=At;var Et=l({name:"Right"});const ff={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},mf=a("path",{fill:"currentColor",d:"M754.752 480H160a32 32 0 1 0 0 64h594.752L521.344 777.344a32 32 0 0 0 45.312 45.312l288-288a32 32 0 0 0 0-45.312l-288-288a32 32 0 1 0-45.312 45.312L754.752 480z"},null,-1);function vf(e,t,n,o,r,s){return c(),i("svg",ff,[mf])}Et.render=vf;Et.__file="packages/components/Right.vue";const gf=Et;var St=l({name:"ScaleToOriginal"});const wf={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},zf=a("path",{fill:"currentColor",d:"M813.176 180.706a60.235 60.235 0 0 1 60.236 60.235v481.883a60.235 60.235 0 0 1-60.236 60.235H210.824a60.235 60.235 0 0 1-60.236-60.235V240.94a60.235 60.235 0 0 1 60.236-60.235h602.352zm0-60.235H210.824A120.47 120.47 0 0 0 90.353 240.94v481.883a120.47 120.47 0 0 0 120.47 120.47h602.353a120.47 120.47 0 0 0 120.471-120.47V240.94a120.47 120.47 0 0 0-120.47-120.47zm-120.47 180.705a30.118 30.118 0 0 0-30.118 30.118v301.177a30.118 30.118 0 0 0 60.236 0V331.294a30.118 30.118 0 0 0-30.118-30.118zm-361.412 0a30.118 30.118 0 0 0-30.118 30.118v301.177a30.118 30.118 0 1 0 60.236 0V331.294a30.118 30.118 0 0 0-30.118-30.118zM512 361.412a30.118 30.118 0 0 0-30.118 30.117v30.118a30.118 30.118 0 0 0 60.236 0V391.53A30.118 30.118 0 0 0 512 361.412zM512 512a30.118 30.118 0 0 0-30.118 30.118v30.117a30.118 30.118 0 0 0 60.236 0v-30.117A30.118 30.118 0 0 0 512 512z"},null,-1);function xf(e,t,n,o,r,s){return c(),i("svg",wf,[zf])}St.render=xf;St.__file="packages/components/ScaleToOriginal.vue";const Cf=St;var Bt=l({name:"School"});const Mf={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Lf=a("path",{fill:"currentColor",d:"M224 128v704h576V128H224zm-32-64h640a32 32 0 0 1 32 32v768a32 32 0 0 1-32 32H192a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32z"},null,-1),Hf=a("path",{fill:"currentColor",d:"M64 832h896v64H64zm256-640h128v96H320z"},null,-1),kf=a("path",{fill:"currentColor",d:"M384 832h256v-64a128 128 0 1 0-256 0v64zm128-256a192 192 0 0 1 192 192v128H320V768a192 192 0 0 1 192-192zM320 384h128v96H320zm256-192h128v96H576zm0 192h128v96H576z"},null,-1);function Vf(e,t,n,o,r,s){return c(),i("svg",Mf,[Lf,Hf,kf])}Bt.render=Vf;Bt.__file="packages/components/School.vue";const bf=Bt;var Ft=l({name:"Remove"});const yf={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Af=a("path",{fill:"currentColor",d:"M352 480h320a32 32 0 1 1 0 64H352a32 32 0 0 1 0-64z"},null,-1),Ef=a("path",{fill:"currentColor",d:"M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768zm0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896z"},null,-1);function Sf(e,t,n,o,r,s){return c(),i("svg",yf,[Af,Ef])}Ft.render=Sf;Ft.__file="packages/components/Remove.vue";const Bf=Ft;var Tt=l({name:"Scissor"});const Ff={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Tf=a("path",{fill:"currentColor",d:"m512.064 578.368-106.88 152.768a160 160 0 1 1-23.36-78.208L472.96 522.56 196.864 128.256a32 32 0 1 1 52.48-36.736l393.024 561.344a160 160 0 1 1-23.36 78.208l-106.88-152.704zm54.4-189.248 208.384-297.6a32 32 0 0 1 52.48 36.736l-221.76 316.672-39.04-55.808zm-376.32 425.856a96 96 0 1 0 110.144-157.248 96 96 0 0 0-110.08 157.248zm643.84 0a96 96 0 1 0-110.08-157.248 96 96 0 0 0 110.08 157.248z"},null,-1);function If(e,t,n,o,r,s){return c(),i("svg",Ff,[Tf])}Tt.render=If;Tt.__file="packages/components/Scissor.vue";const Of=Tt;var It=l({name:"Select"});const Nf={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Pf=a("path",{fill:"currentColor",d:"M77.248 415.04a64 64 0 0 1 90.496 0l226.304 226.304L846.528 188.8a64 64 0 1 1 90.56 90.496l-543.04 543.04-316.8-316.8a64 64 0 0 1 0-90.496z"},null,-1);function Df(e,t,n,o,r,s){return c(),i("svg",Nf,[Pf])}It.render=Df;It.__file="packages/components/Select.vue";const Rf=It;var Ot=l({name:"Management"});const Uf={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Gf=a("path",{fill:"currentColor",d:"M576 128v288l96-96 96 96V128h128v768H320V128h256zm-448 0h128v768H128V128z"},null,-1);function Wf(e,t,n,o,r,s){return c(),i("svg",Uf,[Gf])}Ot.render=Wf;Ot.__file="packages/components/Management.vue";const Zf=Ot;var Nt=l({name:"Search"});const qf={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},jf=a("path",{fill:"currentColor",d:"m795.904 750.72 124.992 124.928a32 32 0 0 1-45.248 45.248L750.656 795.904a416 416 0 1 1 45.248-45.248zM480 832a352 352 0 1 0 0-704 352 352 0 0 0 0 704z"},null,-1);function Yf(e,t,n,o,r,s){return c(),i("svg",qf,[jf])}Nt.render=Yf;Nt.__file="packages/components/Search.vue";const Kf=Nt;var Pt=l({name:"Sell"});const Xf={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Jf=a("path",{fill:"currentColor",d:"M704 288h131.072a32 32 0 0 1 31.808 28.8L886.4 512h-64.384l-16-160H704v96a32 32 0 1 1-64 0v-96H384v96a32 32 0 0 1-64 0v-96H217.92l-51.2 512H512v64H131.328a32 32 0 0 1-31.808-35.2l57.6-576a32 32 0 0 1 31.808-28.8H320v-22.336C320 154.688 405.504 64 512 64s192 90.688 192 201.664v22.4zm-64 0v-22.336C640 189.248 582.272 128 512 128c-70.272 0-128 61.248-128 137.664v22.4h256zm201.408 483.84L768 698.496V928a32 32 0 1 1-64 0V698.496l-73.344 73.344a32 32 0 1 1-45.248-45.248l128-128a32 32 0 0 1 45.248 0l128 128a32 32 0 1 1-45.248 45.248z"},null,-1);function Qf(e,t,n,o,r,s){return c(),i("svg",Xf,[Jf])}Pt.render=Qf;Pt.__file="packages/components/Sell.vue";const em=Pt;var Dt=l({name:"SemiSelect"});const tm={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},nm=a("path",{fill:"currentColor",d:"M128 448h768q64 0 64 64t-64 64H128q-64 0-64-64t64-64z"},null,-1);function om(e,t,n,o,r,s){return c(),i("svg",tm,[nm])}Dt.render=om;Dt.__file="packages/components/SemiSelect.vue";const rm=Dt;var Rt=l({name:"Share"});const sm={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},am=a("path",{fill:"currentColor",d:"m679.872 348.8-301.76 188.608a127.808 127.808 0 0 1 5.12 52.16l279.936 104.96a128 128 0 1 1-22.464 59.904l-279.872-104.96a128 128 0 1 1-16.64-166.272l301.696-188.608a128 128 0 1 1 33.92 54.272z"},null,-1);function cm(e,t,n,o,r,s){return c(),i("svg",sm,[am])}Rt.render=cm;Rt.__file="packages/components/Share.vue";const lm=Rt;var Ut=l({name:"Setting"});const im={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},um=a("path",{fill:"currentColor",d:"M600.704 64a32 32 0 0 1 30.464 22.208l35.2 109.376c14.784 7.232 28.928 15.36 42.432 24.512l112.384-24.192a32 32 0 0 1 34.432 15.36L944.32 364.8a32 32 0 0 1-4.032 37.504l-77.12 85.12a357.12 357.12 0 0 1 0 49.024l77.12 85.248a32 32 0 0 1 4.032 37.504l-88.704 153.6a32 32 0 0 1-34.432 15.296L708.8 803.904c-13.44 9.088-27.648 17.28-42.368 24.512l-35.264 109.376A32 32 0 0 1 600.704 960H423.296a32 32 0 0 1-30.464-22.208L357.696 828.48a351.616 351.616 0 0 1-42.56-24.64l-112.32 24.256a32 32 0 0 1-34.432-15.36L79.68 659.2a32 32 0 0 1 4.032-37.504l77.12-85.248a357.12 357.12 0 0 1 0-48.896l-77.12-85.248A32 32 0 0 1 79.68 364.8l88.704-153.6a32 32 0 0 1 34.432-15.296l112.32 24.256c13.568-9.152 27.776-17.408 42.56-24.64l35.2-109.312A32 32 0 0 1 423.232 64H600.64zm-23.424 64H446.72l-36.352 113.088-24.512 11.968a294.113 294.113 0 0 0-34.816 20.096l-22.656 15.36-116.224-25.088-65.28 113.152 79.68 88.192-1.92 27.136a293.12 293.12 0 0 0 0 40.192l1.92 27.136-79.808 88.192 65.344 113.152 116.224-25.024 22.656 15.296a294.113 294.113 0 0 0 34.816 20.096l24.512 11.968L446.72 896h130.688l36.48-113.152 24.448-11.904a288.282 288.282 0 0 0 34.752-20.096l22.592-15.296 116.288 25.024 65.28-113.152-79.744-88.192 1.92-27.136a293.12 293.12 0 0 0 0-40.256l-1.92-27.136 79.808-88.128-65.344-113.152-116.288 24.96-22.592-15.232a287.616 287.616 0 0 0-34.752-20.096l-24.448-11.904L577.344 128zM512 320a192 192 0 1 1 0 384 192 192 0 0 1 0-384zm0 64a128 128 0 1 0 0 256 128 128 0 0 0 0-256z"},null,-1);function pm(e,t,n,o,r,s){return c(),i("svg",im,[um])}Ut.render=pm;Ut.__file="packages/components/Setting.vue";const _m=Ut;var Gt=l({name:"Service"});const dm={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},hm=a("path",{fill:"currentColor",d:"M864 409.6a192 192 0 0 1-37.888 349.44A256.064 256.064 0 0 1 576 960h-96a32 32 0 1 1 0-64h96a192.064 192.064 0 0 0 181.12-128H736a32 32 0 0 1-32-32V416a32 32 0 0 1 32-32h32c10.368 0 20.544.832 30.528 2.432a288 288 0 0 0-573.056 0A193.235 193.235 0 0 1 256 384h32a32 32 0 0 1 32 32v320a32 32 0 0 1-32 32h-32a192 192 0 0 1-96-358.4 352 352 0 0 1 704 0zM256 448a128 128 0 1 0 0 256V448zm640 128a128 128 0 0 0-128-128v256a128 128 0 0 0 128-128z"},null,-1);function $m(e,t,n,o,r,s){return c(),i("svg",dm,[hm])}Gt.render=$m;Gt.__file="packages/components/Service.vue";const fm=Gt;var Wt=l({name:"Ship"});const mm={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},vm=a("path",{fill:"currentColor",d:"M512 386.88V448h405.568a32 32 0 0 1 30.72 40.768l-76.48 267.968A192 192 0 0 1 687.168 896H336.832a192 192 0 0 1-184.64-139.264L75.648 488.768A32 32 0 0 1 106.368 448H448V117.888a32 32 0 0 1 47.36-28.096l13.888 7.616L512 96v2.88l231.68 126.4a32 32 0 0 1-2.048 57.216L512 386.88zm0-70.272 144.768-65.792L512 171.84v144.768zM512 512H148.864l18.24 64H856.96l18.24-64H512zM185.408 640l28.352 99.2A128 128 0 0 0 336.832 832h350.336a128 128 0 0 0 123.072-92.8l28.352-99.2H185.408z"},null,-1);function gm(e,t,n,o,r,s){return c(),i("svg",mm,[vm])}Wt.render=gm;Wt.__file="packages/components/Ship.vue";const wm=Wt;var Zt=l({name:"SetUp"});const zm={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},xm=a("path",{fill:"currentColor",d:"M224 160a64 64 0 0 0-64 64v576a64 64 0 0 0 64 64h576a64 64 0 0 0 64-64V224a64 64 0 0 0-64-64H224zm0-64h576a128 128 0 0 1 128 128v576a128 128 0 0 1-128 128H224A128 128 0 0 1 96 800V224A128 128 0 0 1 224 96z"},null,-1),Cm=a("path",{fill:"currentColor",d:"M384 416a64 64 0 1 0 0-128 64 64 0 0 0 0 128zm0 64a128 128 0 1 1 0-256 128 128 0 0 1 0 256z"},null,-1),Mm=a("path",{fill:"currentColor",d:"M480 320h256q32 0 32 32t-32 32H480q-32 0-32-32t32-32zm160 416a64 64 0 1 0 0-128 64 64 0 0 0 0 128zm0 64a128 128 0 1 1 0-256 128 128 0 0 1 0 256z"},null,-1),Lm=a("path",{fill:"currentColor",d:"M288 640h256q32 0 32 32t-32 32H288q-32 0-32-32t32-32z"},null,-1);function Hm(e,t,n,o,r,s){return c(),i("svg",zm,[xm,Cm,Mm,Lm])}Zt.render=Hm;Zt.__file="packages/components/SetUp.vue";const km=Zt;var qt=l({name:"ShoppingBag"});const Vm={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},bm=a("path",{fill:"currentColor",d:"M704 320v96a32 32 0 0 1-32 32h-32V320H384v128h-32a32 32 0 0 1-32-32v-96H192v576h640V320H704zm-384-64a192 192 0 1 1 384 0h160a32 32 0 0 1 32 32v640a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V288a32 32 0 0 1 32-32h160zm64 0h256a128 128 0 1 0-256 0z"},null,-1),ym=a("path",{fill:"currentColor",d:"M192 704h640v64H192z"},null,-1);function Am(e,t,n,o,r,s){return c(),i("svg",Vm,[bm,ym])}qt.render=Am;qt.__file="packages/components/ShoppingBag.vue";const Em=qt;var jt=l({name:"Shop"});const Sm={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Bm=a("path",{fill:"currentColor",d:"M704 704h64v192H256V704h64v64h384v-64zm188.544-152.192C894.528 559.616 896 567.616 896 576a96 96 0 1 1-192 0 96 96 0 1 1-192 0 96 96 0 1 1-192 0 96 96 0 1 1-192 0c0-8.384 1.408-16.384 3.392-24.192L192 128h640l60.544 423.808z"},null,-1);function Fm(e,t,n,o,r,s){return c(),i("svg",Sm,[Bm])}jt.render=Fm;jt.__file="packages/components/Shop.vue";const Tm=jt;var Yt=l({name:"ShoppingCart"});const Im={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Om=a("path",{fill:"currentColor",d:"M432 928a48 48 0 1 1 0-96 48 48 0 0 1 0 96zm320 0a48 48 0 1 1 0-96 48 48 0 0 1 0 96zM96 128a32 32 0 0 1 0-64h160a32 32 0 0 1 31.36 25.728L320.64 256H928a32 32 0 0 1 31.296 38.72l-96 448A32 32 0 0 1 832 768H384a32 32 0 0 1-31.36-25.728L229.76 128H96zm314.24 576h395.904l82.304-384H333.44l76.8 384z"},null,-1);function Nm(e,t,n,o,r,s){return c(),i("svg",Im,[Om])}Yt.render=Nm;Yt.__file="packages/components/ShoppingCart.vue";const Pm=Yt;var Kt=l({name:"ShoppingCartFull"});const Dm={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Rm=a("path",{fill:"currentColor",d:"M432 928a48 48 0 1 1 0-96 48 48 0 0 1 0 96zm320 0a48 48 0 1 1 0-96 48 48 0 0 1 0 96zM96 128a32 32 0 0 1 0-64h160a32 32 0 0 1 31.36 25.728L320.64 256H928a32 32 0 0 1 31.296 38.72l-96 448A32 32 0 0 1 832 768H384a32 32 0 0 1-31.36-25.728L229.76 128H96zm314.24 576h395.904l82.304-384H333.44l76.8 384z"},null,-1),Um=a("path",{fill:"currentColor",d:"M699.648 256 608 145.984 516.352 256h183.296zm-140.8-151.04a64 64 0 0 1 98.304 0L836.352 320H379.648l179.2-215.04z"},null,-1);function Gm(e,t,n,o,r,s){return c(),i("svg",Dm,[Rm,Um])}Kt.render=Gm;Kt.__file="packages/components/ShoppingCartFull.vue";const Wm=Kt;var Xt=l({name:"Soccer"});const Zm={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},qm=a("path",{fill:"currentColor",d:"M418.496 871.04 152.256 604.8c-16.512 94.016-2.368 178.624 42.944 224 44.928 44.928 129.344 58.752 223.296 42.24zm72.32-18.176a573.056 573.056 0 0 0 224.832-137.216 573.12 573.12 0 0 0 137.216-224.832L533.888 171.84a578.56 578.56 0 0 0-227.52 138.496A567.68 567.68 0 0 0 170.432 532.48l320.384 320.384zM871.04 418.496c16.512-93.952 2.688-178.368-42.24-223.296-44.544-44.544-128.704-58.048-222.592-41.536L871.04 418.496zM149.952 874.048c-112.96-112.96-88.832-408.96 111.168-608.96C461.056 65.152 760.96 36.928 874.048 149.952c113.024 113.024 86.784 411.008-113.152 610.944-199.936 199.936-497.92 226.112-610.944 113.152zm452.544-497.792 22.656-22.656a32 32 0 0 1 45.248 45.248l-22.656 22.656 45.248 45.248A32 32 0 1 1 647.744 512l-45.248-45.248L557.248 512l45.248 45.248a32 32 0 1 1-45.248 45.248L512 557.248l-45.248 45.248L512 647.744a32 32 0 1 1-45.248 45.248l-45.248-45.248-22.656 22.656a32 32 0 1 1-45.248-45.248l22.656-22.656-45.248-45.248A32 32 0 1 1 376.256 512l45.248 45.248L466.752 512l-45.248-45.248a32 32 0 1 1 45.248-45.248L512 466.752l45.248-45.248L512 376.256a32 32 0 0 1 45.248-45.248l45.248 45.248z"},null,-1);function jm(e,t,n,o,r,s){return c(),i("svg",Zm,[qm])}Xt.render=jm;Xt.__file="packages/components/Soccer.vue";const Ym=Xt;var Jt=l({name:"SoldOut"});const Km={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Xm=a("path",{fill:"currentColor",d:"M704 288h131.072a32 32 0 0 1 31.808 28.8L886.4 512h-64.384l-16-160H704v96a32 32 0 1 1-64 0v-96H384v96a32 32 0 0 1-64 0v-96H217.92l-51.2 512H512v64H131.328a32 32 0 0 1-31.808-35.2l57.6-576a32 32 0 0 1 31.808-28.8H320v-22.336C320 154.688 405.504 64 512 64s192 90.688 192 201.664v22.4zm-64 0v-22.336C640 189.248 582.272 128 512 128c-70.272 0-128 61.248-128 137.664v22.4h256zm201.408 476.16a32 32 0 1 1 45.248 45.184l-128 128a32 32 0 0 1-45.248 0l-128-128a32 32 0 1 1 45.248-45.248L704 837.504V608a32 32 0 1 1 64 0v229.504l73.408-73.408z"},null,-1);function Jm(e,t,n,o,r,s){return c(),i("svg",Km,[Xm])}Jt.render=Jm;Jt.__file="packages/components/SoldOut.vue";const Qm=Jt;var Qt=l({name:"Smoking"});const ev={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},tv=a("path",{fill:"currentColor",d:"M256 576v128h640V576H256zm-32-64h704a32 32 0 0 1 32 32v192a32 32 0 0 1-32 32H224a32 32 0 0 1-32-32V544a32 32 0 0 1 32-32z"},null,-1),nv=a("path",{fill:"currentColor",d:"M704 576h64v128h-64zM256 64h64v320h-64zM128 192h64v192h-64zM64 512h64v256H64z"},null,-1);function ov(e,t,n,o,r,s){return c(),i("svg",ev,[tv,nv])}Qt.render=ov;Qt.__file="packages/components/Smoking.vue";const rv=Qt;var e3=l({name:"SortDown"});const sv={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},av=a("path",{fill:"currentColor",d:"M576 96v709.568L333.312 562.816A32 32 0 1 0 288 608l297.408 297.344A32 32 0 0 0 640 882.688V96a32 32 0 0 0-64 0z"},null,-1);function cv(e,t,n,o,r,s){return c(),i("svg",sv,[av])}e3.render=cv;e3.__file="packages/components/SortDown.vue";const lv=e3;var t3=l({name:"Sort"});const iv={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},uv=a("path",{fill:"currentColor",d:"M384 96a32 32 0 0 1 64 0v786.752a32 32 0 0 1-54.592 22.656L95.936 608a32 32 0 0 1 0-45.312h.128a32 32 0 0 1 45.184 0L384 805.632V96zm192 45.248a32 32 0 0 1 54.592-22.592L928.064 416a32 32 0 0 1 0 45.312h-.128a32 32 0 0 1-45.184 0L640 218.496V928a32 32 0 1 1-64 0V141.248z"},null,-1);function pv(e,t,n,o,r,s){return c(),i("svg",iv,[uv])}t3.render=pv;t3.__file="packages/components/Sort.vue";const _v=t3;var n3=l({name:"SortUp"});const dv={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},hv=a("path",{fill:"currentColor",d:"M384 141.248V928a32 32 0 1 0 64 0V218.56l242.688 242.688A32 32 0 1 0 736 416L438.592 118.656A32 32 0 0 0 384 141.248z"},null,-1);function $v(e,t,n,o,r,s){return c(),i("svg",dv,[hv])}n3.render=$v;n3.__file="packages/components/SortUp.vue";const fv=n3;var o3=l({name:"Star"});const mv={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},vv=a("path",{fill:"currentColor",d:"m512 747.84 228.16 119.936a6.4 6.4 0 0 0 9.28-6.72l-43.52-254.08 184.512-179.904a6.4 6.4 0 0 0-3.52-10.88l-255.104-37.12L517.76 147.904a6.4 6.4 0 0 0-11.52 0L392.192 379.072l-255.104 37.12a6.4 6.4 0 0 0-3.52 10.88L318.08 606.976l-43.584 254.08a6.4 6.4 0 0 0 9.28 6.72L512 747.84zM313.6 924.48a70.4 70.4 0 0 1-102.144-74.24l37.888-220.928L88.96 472.96A70.4 70.4 0 0 1 128 352.896l221.76-32.256 99.2-200.96a70.4 70.4 0 0 1 126.208 0l99.2 200.96 221.824 32.256a70.4 70.4 0 0 1 39.04 120.064L774.72 629.376l37.888 220.928a70.4 70.4 0 0 1-102.144 74.24L512 820.096l-198.4 104.32z"},null,-1);function gv(e,t,n,o,r,s){return c(),i("svg",mv,[vv])}o3.render=gv;o3.__file="packages/components/Star.vue";const wv=o3;var r3=l({name:"Stamp"});const zv={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},xv=a("path",{fill:"currentColor",d:"M624 475.968V640h144a128 128 0 0 1 128 128H128a128 128 0 0 1 128-128h144V475.968a192 192 0 1 1 224 0zM128 896v-64h768v64H128z"},null,-1);function Cv(e,t,n,o,r,s){return c(),i("svg",zv,[xv])}r3.render=Cv;r3.__file="packages/components/Stamp.vue";const Mv=r3;var s3=l({name:"StarFilled"});const Lv={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Hv=a("path",{fill:"currentColor",d:"M283.84 867.84 512 747.776l228.16 119.936a6.4 6.4 0 0 0 9.28-6.72l-43.52-254.08 184.512-179.904a6.4 6.4 0 0 0-3.52-10.88l-255.104-37.12L517.76 147.904a6.4 6.4 0 0 0-11.52 0L392.192 379.072l-255.104 37.12a6.4 6.4 0 0 0-3.52 10.88L318.08 606.976l-43.584 254.08a6.4 6.4 0 0 0 9.28 6.72z"},null,-1);function kv(e,t,n,o,r,s){return c(),i("svg",Lv,[Hv])}s3.render=kv;s3.__file="packages/components/StarFilled.vue";const Vv=s3;var a3=l({name:"Stopwatch"});const bv={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},yv=a("path",{fill:"currentColor",d:"M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768zm0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896z"},null,-1),Av=a("path",{fill:"currentColor",d:"M672 234.88c-39.168 174.464-80 298.624-122.688 372.48-64 110.848-202.624 30.848-138.624-80C453.376 453.44 540.48 355.968 672 234.816z"},null,-1);function Ev(e,t,n,o,r,s){return c(),i("svg",bv,[yv,Av])}a3.render=Ev;a3.__file="packages/components/Stopwatch.vue";const Sv=a3;var c3=l({name:"SuccessFilled"});const Bv={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Fv=a("path",{fill:"currentColor",d:"M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896zm-55.808 536.384-99.52-99.584a38.4 38.4 0 1 0-54.336 54.336l126.72 126.72a38.272 38.272 0 0 0 54.336 0l262.4-262.464a38.4 38.4 0 1 0-54.272-54.336L456.192 600.384z"},null,-1);function Tv(e,t,n,o,r,s){return c(),i("svg",Bv,[Fv])}c3.render=Tv;c3.__file="packages/components/SuccessFilled.vue";const Iv=c3;var l3=l({name:"Suitcase"});const Ov={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Nv=a("path",{fill:"currentColor",d:"M128 384h768v-64a64 64 0 0 0-64-64H192a64 64 0 0 0-64 64v64zm0 64v320a64 64 0 0 0 64 64h640a64 64 0 0 0 64-64V448H128zm64-256h640a128 128 0 0 1 128 128v448a128 128 0 0 1-128 128H192A128 128 0 0 1 64 768V320a128 128 0 0 1 128-128z"},null,-1),Pv=a("path",{fill:"currentColor",d:"M384 128v64h256v-64H384zm0-64h256a64 64 0 0 1 64 64v64a64 64 0 0 1-64 64H384a64 64 0 0 1-64-64v-64a64 64 0 0 1 64-64z"},null,-1);function Dv(e,t,n,o,r,s){return c(),i("svg",Ov,[Nv,Pv])}l3.render=Dv;l3.__file="packages/components/Suitcase.vue";const Rv=l3;var i3=l({name:"Sugar"});const Uv={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Gv=a("path",{fill:"currentColor",d:"m801.728 349.184 4.48 4.48a128 128 0 0 1 0 180.992L534.656 806.144a128 128 0 0 1-181.056 0l-4.48-4.48-19.392 109.696a64 64 0 0 1-108.288 34.176L78.464 802.56a64 64 0 0 1 34.176-108.288l109.76-19.328-4.544-4.544a128 128 0 0 1 0-181.056l271.488-271.488a128 128 0 0 1 181.056 0l4.48 4.48 19.392-109.504a64 64 0 0 1 108.352-34.048l142.592 143.04a64 64 0 0 1-34.24 108.16l-109.248 19.2zm-548.8 198.72h447.168v2.24l60.8-60.8a63.808 63.808 0 0 0 18.752-44.416h-426.88l-89.664 89.728a64.064 64.064 0 0 0-10.24 13.248zm0 64c2.752 4.736 6.144 9.152 10.176 13.248l135.744 135.744a64 64 0 0 0 90.496 0L638.4 611.904H252.928zm490.048-230.976L625.152 263.104a64 64 0 0 0-90.496 0L416.768 380.928h326.208zM123.712 757.312l142.976 142.976 24.32-137.6a25.6 25.6 0 0 0-29.696-29.632l-137.6 24.256zm633.6-633.344-24.32 137.472a25.6 25.6 0 0 0 29.632 29.632l137.28-24.064-142.656-143.04z"},null,-1);function Wv(e,t,n,o,r,s){return c(),i("svg",Uv,[Gv])}i3.render=Wv;i3.__file="packages/components/Sugar.vue";const Zv=i3;var u3=l({name:"Sunny"});const qv={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},jv=a("path",{fill:"currentColor",d:"M512 704a192 192 0 1 0 0-384 192 192 0 0 0 0 384zm0 64a256 256 0 1 1 0-512 256 256 0 0 1 0 512zm0-704a32 32 0 0 1 32 32v64a32 32 0 0 1-64 0V96a32 32 0 0 1 32-32zm0 768a32 32 0 0 1 32 32v64a32 32 0 1 1-64 0v-64a32 32 0 0 1 32-32zM195.2 195.2a32 32 0 0 1 45.248 0l45.248 45.248a32 32 0 1 1-45.248 45.248L195.2 240.448a32 32 0 0 1 0-45.248zm543.104 543.104a32 32 0 0 1 45.248 0l45.248 45.248a32 32 0 0 1-45.248 45.248l-45.248-45.248a32 32 0 0 1 0-45.248zM64 512a32 32 0 0 1 32-32h64a32 32 0 0 1 0 64H96a32 32 0 0 1-32-32zm768 0a32 32 0 0 1 32-32h64a32 32 0 1 1 0 64h-64a32 32 0 0 1-32-32zM195.2 828.8a32 32 0 0 1 0-45.248l45.248-45.248a32 32 0 0 1 45.248 45.248L240.448 828.8a32 32 0 0 1-45.248 0zm543.104-543.104a32 32 0 0 1 0-45.248l45.248-45.248a32 32 0 0 1 45.248 45.248l-45.248 45.248a32 32 0 0 1-45.248 0z"},null,-1);function Yv(e,t,n,o,r,s){return c(),i("svg",qv,[jv])}u3.render=Yv;u3.__file="packages/components/Sunny.vue";const Kv=u3;var p3=l({name:"Sunrise"});const Xv={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Jv=a("path",{fill:"currentColor",d:"M32 768h960a32 32 0 1 1 0 64H32a32 32 0 1 1 0-64zm129.408-96a352 352 0 0 1 701.184 0h-64.32a288 288 0 0 0-572.544 0h-64.32zM512 128a32 32 0 0 1 32 32v96a32 32 0 0 1-64 0v-96a32 32 0 0 1 32-32zm407.296 168.704a32 32 0 0 1 0 45.248l-67.84 67.84a32 32 0 1 1-45.248-45.248l67.84-67.84a32 32 0 0 1 45.248 0zm-814.592 0a32 32 0 0 1 45.248 0l67.84 67.84a32 32 0 1 1-45.248 45.248l-67.84-67.84a32 32 0 0 1 0-45.248z"},null,-1);function Qv(e,t,n,o,r,s){return c(),i("svg",Xv,[Jv])}p3.render=Qv;p3.__file="packages/components/Sunrise.vue";const eg=p3;var _3=l({name:"Switch"});const tg={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},ng=a("path",{fill:"currentColor",d:"M118.656 438.656a32 32 0 0 1 0-45.248L416 96l4.48-3.776A32 32 0 0 1 461.248 96l3.712 4.48a32.064 32.064 0 0 1-3.712 40.832L218.56 384H928a32 32 0 1 1 0 64H141.248a32 32 0 0 1-22.592-9.344zM64 608a32 32 0 0 1 32-32h786.752a32 32 0 0 1 22.656 54.592L608 928l-4.48 3.776a32.064 32.064 0 0 1-40.832-49.024L805.632 640H96a32 32 0 0 1-32-32z"},null,-1);function og(e,t,n,o,r,s){return c(),i("svg",tg,[ng])}_3.render=og;_3.__file="packages/components/Switch.vue";const rg=_3;var d3=l({name:"Ticket"});const sg={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},ag=a("path",{fill:"currentColor",d:"M640 832H64V640a128 128 0 1 0 0-256V192h576v160h64V192h256v192a128 128 0 1 0 0 256v192H704V672h-64v160zm0-416v192h64V416h-64z"},null,-1);function cg(e,t,n,o,r,s){return c(),i("svg",sg,[ag])}d3.render=cg;d3.__file="packages/components/Ticket.vue";const lg=d3;var h3=l({name:"Sunset"});const ig={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},ug=a("path",{fill:"currentColor",d:"M82.56 640a448 448 0 1 1 858.88 0h-67.2a384 384 0 1 0-724.288 0H82.56zM32 704h960q32 0 32 32t-32 32H32q-32 0-32-32t32-32zm256 128h448q32 0 32 32t-32 32H288q-32 0-32-32t32-32z"},null,-1);function pg(e,t,n,o,r,s){return c(),i("svg",ig,[ug])}h3.render=pg;h3.__file="packages/components/Sunset.vue";const _g=h3;var $3=l({name:"Tickets"});const dg={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},hg=a("path",{fill:"currentColor",d:"M192 128v768h640V128H192zm-32-64h704a32 32 0 0 1 32 32v832a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32zm160 448h384v64H320v-64zm0-192h192v64H320v-64zm0 384h384v64H320v-64z"},null,-1);function $g(e,t,n,o,r,s){return c(),i("svg",dg,[hg])}$3.render=$g;$3.__file="packages/components/Tickets.vue";const fg=$3;var f3=l({name:"SwitchButton"});const mg={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},vg=a("path",{fill:"currentColor",d:"M352 159.872V230.4a352 352 0 1 0 320 0v-70.528A416.128 416.128 0 0 1 512 960a416 416 0 0 1-160-800.128z"},null,-1),gg=a("path",{fill:"currentColor",d:"M512 64q32 0 32 32v320q0 32-32 32t-32-32V96q0-32 32-32z"},null,-1);function wg(e,t,n,o,r,s){return c(),i("svg",mg,[vg,gg])}f3.render=wg;f3.__file="packages/components/SwitchButton.vue";const zg=f3;var m3=l({name:"TakeawayBox"});const xg={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Cg=a("path",{fill:"currentColor",d:"M832 384H192v448h640V384zM96 320h832V128H96v192zm800 64v480a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V384H64a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32h896a32 32 0 0 1 32 32v256a32 32 0 0 1-32 32h-64zM416 512h192a32 32 0 0 1 0 64H416a32 32 0 0 1 0-64z"},null,-1);function Mg(e,t,n,o,r,s){return c(),i("svg",xg,[Cg])}m3.render=Mg;m3.__file="packages/components/TakeawayBox.vue";const Lg=m3;var v3=l({name:"ToiletPaper"});const Hg={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},kg=a("path",{fill:"currentColor",d:"M595.2 128H320a192 192 0 0 0-192 192v576h384V352c0-90.496 32.448-171.2 83.2-224zM736 64c123.712 0 224 128.96 224 288S859.712 640 736 640H576v320H64V320A256 256 0 0 1 320 64h416zM576 352v224h160c84.352 0 160-97.28 160-224s-75.648-224-160-224-160 97.28-160 224z"},null,-1),Vg=a("path",{fill:"currentColor",d:"M736 448c-35.328 0-64-43.008-64-96s28.672-96 64-96 64 43.008 64 96-28.672 96-64 96z"},null,-1);function bg(e,t,n,o,r,s){return c(),i("svg",Hg,[kg,Vg])}v3.render=bg;v3.__file="packages/components/ToiletPaper.vue";const yg=v3;var g3=l({name:"Timer"});const Ag={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Eg=a("path",{fill:"currentColor",d:"M512 896a320 320 0 1 0 0-640 320 320 0 0 0 0 640zm0 64a384 384 0 1 1 0-768 384 384 0 0 1 0 768z"},null,-1),Sg=a("path",{fill:"currentColor",d:"M512 320a32 32 0 0 1 32 32l-.512 224a32 32 0 1 1-64 0L480 352a32 32 0 0 1 32-32z"},null,-1),Bg=a("path",{fill:"currentColor",d:"M448 576a64 64 0 1 0 128 0 64 64 0 1 0-128 0zm96-448v128h-64V128h-96a32 32 0 0 1 0-64h256a32 32 0 1 1 0 64h-96z"},null,-1);function Fg(e,t,n,o,r,s){return c(),i("svg",Ag,[Eg,Sg,Bg])}g3.render=Fg;g3.__file="packages/components/Timer.vue";const Tg=g3;var w3=l({name:"Tools"});const Ig={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Og=a("path",{fill:"currentColor",d:"M764.416 254.72a351.68 351.68 0 0 1 86.336 149.184H960v192.064H850.752a351.68 351.68 0 0 1-86.336 149.312l54.72 94.72-166.272 96-54.592-94.72a352.64 352.64 0 0 1-172.48 0L371.136 936l-166.272-96 54.72-94.72a351.68 351.68 0 0 1-86.336-149.312H64v-192h109.248a351.68 351.68 0 0 1 86.336-149.312L204.8 160l166.208-96h.192l54.656 94.592a352.64 352.64 0 0 1 172.48 0L652.8 64h.128L819.2 160l-54.72 94.72zM704 499.968a192 192 0 1 0-384 0 192 192 0 0 0 384 0z"},null,-1);function Ng(e,t,n,o,r,s){return c(),i("svg",Ig,[Og])}w3.render=Ng;w3.__file="packages/components/Tools.vue";const Pg=w3;var z3=l({name:"TopLeft"});const Dg={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Rg=a("path",{fill:"currentColor",d:"M256 256h416a32 32 0 1 0 0-64H224a32 32 0 0 0-32 32v448a32 32 0 0 0 64 0V256z"},null,-1),Ug=a("path",{fill:"currentColor",d:"M246.656 201.344a32 32 0 0 0-45.312 45.312l544 544a32 32 0 0 0 45.312-45.312l-544-544z"},null,-1);function Gg(e,t,n,o,r,s){return c(),i("svg",Dg,[Rg,Ug])}z3.render=Gg;z3.__file="packages/components/TopLeft.vue";const Wg=z3;var x3=l({name:"Top"});const Zg={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},qg=a("path",{fill:"currentColor",d:"M572.235 205.282v600.365a30.118 30.118 0 1 1-60.235 0V205.282L292.382 438.633a28.913 28.913 0 0 1-42.646 0 33.43 33.43 0 0 1 0-45.236l271.058-288.045a28.913 28.913 0 0 1 42.647 0L834.5 393.397a33.43 33.43 0 0 1 0 45.176 28.913 28.913 0 0 1-42.647 0l-219.618-233.23z"},null,-1);function jg(e,t,n,o,r,s){return c(),i("svg",Zg,[qg])}x3.render=jg;x3.__file="packages/components/Top.vue";const Yg=x3;var C3=l({name:"TopRight"});const Kg={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Xg=a("path",{fill:"currentColor",d:"M768 256H353.6a32 32 0 1 1 0-64H800a32 32 0 0 1 32 32v448a32 32 0 0 1-64 0V256z"},null,-1),Jg=a("path",{fill:"currentColor",d:"M777.344 201.344a32 32 0 0 1 45.312 45.312l-544 544a32 32 0 0 1-45.312-45.312l544-544z"},null,-1);function Qg(e,t,n,o,r,s){return c(),i("svg",Kg,[Xg,Jg])}C3.render=Qg;C3.__file="packages/components/TopRight.vue";const ew=C3;var M3=l({name:"TrendCharts"});const tw={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},nw=a("path",{fill:"currentColor",d:"M128 896V128h768v768H128zm291.712-327.296 128 102.4 180.16-201.792-47.744-42.624-139.84 156.608-128-102.4-180.16 201.792 47.744 42.624 139.84-156.608zM816 352a48 48 0 1 0-96 0 48 48 0 0 0 96 0z"},null,-1);function ow(e,t,n,o,r,s){return c(),i("svg",tw,[nw])}M3.render=ow;M3.__file="packages/components/TrendCharts.vue";const rw=M3;var L3=l({name:"TurnOff"});const sw={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},aw=a("path",{fill:"currentColor",d:"M329.956 257.138a254.862 254.862 0 0 0 0 509.724h364.088a254.862 254.862 0 0 0 0-509.724H329.956zm0-72.818h364.088a327.68 327.68 0 1 1 0 655.36H329.956a327.68 327.68 0 1 1 0-655.36z"},null,-1),cw=a("path",{fill:"currentColor",d:"M329.956 621.227a109.227 109.227 0 1 0 0-218.454 109.227 109.227 0 0 0 0 218.454zm0 72.817a182.044 182.044 0 1 1 0-364.088 182.044 182.044 0 0 1 0 364.088z"},null,-1);function lw(e,t,n,o,r,s){return c(),i("svg",sw,[aw,cw])}L3.render=lw;L3.__file="packages/components/TurnOff.vue";const iw=L3;var H3=l({name:"Unlock"});const uw={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},pw=a("path",{fill:"currentColor",d:"M224 448a32 32 0 0 0-32 32v384a32 32 0 0 0 32 32h576a32 32 0 0 0 32-32V480a32 32 0 0 0-32-32H224zm0-64h576a96 96 0 0 1 96 96v384a96 96 0 0 1-96 96H224a96 96 0 0 1-96-96V480a96 96 0 0 1 96-96z"},null,-1),_w=a("path",{fill:"currentColor",d:"M512 544a32 32 0 0 1 32 32v192a32 32 0 1 1-64 0V576a32 32 0 0 1 32-32zm178.304-295.296A192.064 192.064 0 0 0 320 320v64h352l96 38.4V448H256V320a256 256 0 0 1 493.76-95.104l-59.456 23.808z"},null,-1);function dw(e,t,n,o,r,s){return c(),i("svg",uw,[pw,_w])}H3.render=dw;H3.__file="packages/components/Unlock.vue";const hw=H3;var k3=l({name:"Trophy"});const $w={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},fw=a("path",{fill:"currentColor",d:"M480 896V702.08A256.256 256.256 0 0 1 264.064 512h-32.64a96 96 0 0 1-91.968-68.416L93.632 290.88a76.8 76.8 0 0 1 73.6-98.88H256V96a32 32 0 0 1 32-32h448a32 32 0 0 1 32 32v96h88.768a76.8 76.8 0 0 1 73.6 98.88L884.48 443.52A96 96 0 0 1 792.576 512h-32.64A256.256 256.256 0 0 1 544 702.08V896h128a32 32 0 1 1 0 64H352a32 32 0 1 1 0-64h128zm224-448V128H320v320a192 192 0 1 0 384 0zm64 0h24.576a32 32 0 0 0 30.656-22.784l45.824-152.768A12.8 12.8 0 0 0 856.768 256H768v192zm-512 0V256h-88.768a12.8 12.8 0 0 0-12.288 16.448l45.824 152.768A32 32 0 0 0 231.424 448H256z"},null,-1);function mw(e,t,n,o,r,s){return c(),i("svg",$w,[fw])}k3.render=mw;k3.__file="packages/components/Trophy.vue";const vw=k3;var V3=l({name:"Umbrella"});const gw={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},ww=a("path",{fill:"currentColor",d:"M320 768a32 32 0 1 1 64 0 64 64 0 0 0 128 0V512H64a448 448 0 1 1 896 0H576v256a128 128 0 1 1-256 0zm570.688-320a384.128 384.128 0 0 0-757.376 0h757.376z"},null,-1);function zw(e,t,n,o,r,s){return c(),i("svg",gw,[ww])}V3.render=zw;V3.__file="packages/components/Umbrella.vue";const xw=V3;var b3=l({name:"UploadFilled"});const Cw={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Mw=a("path",{fill:"currentColor",d:"M544 864V672h128L512 480 352 672h128v192H320v-1.6c-5.376.32-10.496 1.6-16 1.6A240 240 0 0 1 64 624c0-123.136 93.12-223.488 212.608-237.248A239.808 239.808 0 0 1 512 192a239.872 239.872 0 0 1 235.456 194.752c119.488 13.76 212.48 114.112 212.48 237.248a240 240 0 0 1-240 240c-5.376 0-10.56-1.28-16-1.6v1.6H544z"},null,-1);function Lw(e,t,n,o,r,s){return c(),i("svg",Cw,[Mw])}b3.render=Lw;b3.__file="packages/components/UploadFilled.vue";const Hw=b3;var y3=l({name:"UserFilled"});const kw={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Vw=a("path",{fill:"currentColor",d:"M288 320a224 224 0 1 0 448 0 224 224 0 1 0-448 0zm544 608H160a32 32 0 0 1-32-32v-96a160 160 0 0 1 160-160h448a160 160 0 0 1 160 160v96a32 32 0 0 1-32 32z"},null,-1);function bw(e,t,n,o,r,s){return c(),i("svg",kw,[Vw])}y3.render=bw;y3.__file="packages/components/UserFilled.vue";const yw=y3;var A3=l({name:"Upload"});const Aw={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Ew=a("path",{fill:"currentColor",d:"M160 832h704a32 32 0 1 1 0 64H160a32 32 0 1 1 0-64zm384-578.304V704h-64V247.296L237.248 490.048 192 444.8 508.8 128l316.8 316.8-45.312 45.248L544 253.696z"},null,-1);function Sw(e,t,n,o,r,s){return c(),i("svg",Aw,[Ew])}A3.render=Sw;A3.__file="packages/components/Upload.vue";const Bw=A3;var E3=l({name:"User"});const Fw={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Tw=a("path",{fill:"currentColor",d:"M512 512a192 192 0 1 0 0-384 192 192 0 0 0 0 384zm0 64a256 256 0 1 1 0-512 256 256 0 0 1 0 512zm320 320v-96a96 96 0 0 0-96-96H288a96 96 0 0 0-96 96v96a32 32 0 1 1-64 0v-96a160 160 0 0 1 160-160h448a160 160 0 0 1 160 160v96a32 32 0 1 1-64 0z"},null,-1);function Iw(e,t,n,o,r,s){return c(),i("svg",Fw,[Tw])}E3.render=Iw;E3.__file="packages/components/User.vue";const Ow=E3;var S3=l({name:"Van"});const Nw={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Pw=a("path",{fill:"currentColor",d:"M128.896 736H96a32 32 0 0 1-32-32V224a32 32 0 0 1 32-32h576a32 32 0 0 1 32 32v96h164.544a32 32 0 0 1 31.616 27.136l54.144 352A32 32 0 0 1 922.688 736h-91.52a144 144 0 1 1-286.272 0H415.104a144 144 0 1 1-286.272 0zm23.36-64a143.872 143.872 0 0 1 239.488 0H568.32c17.088-25.6 42.24-45.376 71.744-55.808V256H128v416h24.256zm655.488 0h77.632l-19.648-128H704v64.896A144 144 0 0 1 807.744 672zm48.128-192-14.72-96H704v96h151.872zM688 832a80 80 0 1 0 0-160 80 80 0 0 0 0 160zm-416 0a80 80 0 1 0 0-160 80 80 0 0 0 0 160z"},null,-1);function Dw(e,t,n,o,r,s){return c(),i("svg",Nw,[Pw])}S3.render=Dw;S3.__file="packages/components/Van.vue";const Rw=S3;var B3=l({name:"CopyDocument"});const Uw={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Gw=a("path",{fill:"currentColor",d:"M768 832a128 128 0 0 1-128 128H192A128 128 0 0 1 64 832V384a128 128 0 0 1 128-128v64a64 64 0 0 0-64 64v448a64 64 0 0 0 64 64h448a64 64 0 0 0 64-64h64z"},null,-1),Ww=a("path",{fill:"currentColor",d:"M384 128a64 64 0 0 0-64 64v448a64 64 0 0 0 64 64h448a64 64 0 0 0 64-64V192a64 64 0 0 0-64-64H384zm0-64h448a128 128 0 0 1 128 128v448a128 128 0 0 1-128 128H384a128 128 0 0 1-128-128V192A128 128 0 0 1 384 64z"},null,-1);function Zw(e,t,n,o,r,s){return c(),i("svg",Uw,[Gw,Ww])}B3.render=Zw;B3.__file="packages/components/CopyDocument.vue";const qw=B3;var F3=l({name:"VideoPause"});const jw={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Yw=a("path",{fill:"currentColor",d:"M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896zm0 832a384 384 0 0 0 0-768 384 384 0 0 0 0 768zm-96-544q32 0 32 32v256q0 32-32 32t-32-32V384q0-32 32-32zm192 0q32 0 32 32v256q0 32-32 32t-32-32V384q0-32 32-32z"},null,-1);function Kw(e,t,n,o,r,s){return c(),i("svg",jw,[Yw])}F3.render=Kw;F3.__file="packages/components/VideoPause.vue";const Xw=F3;var T3=l({name:"VideoCameraFilled"});const Jw={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Qw=a("path",{fill:"currentColor",d:"m768 576 192-64v320l-192-64v96a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V480a32 32 0 0 1 32-32h640a32 32 0 0 1 32 32v96zM192 768v64h384v-64H192zm192-480a160 160 0 0 1 320 0 160 160 0 0 1-320 0zm64 0a96 96 0 1 0 192.064-.064A96 96 0 0 0 448 288zm-320 32a128 128 0 1 1 256.064.064A128 128 0 0 1 128 320zm64 0a64 64 0 1 0 128 0 64 64 0 0 0-128 0z"},null,-1);function ez(e,t,n,o,r,s){return c(),i("svg",Jw,[Qw])}T3.render=ez;T3.__file="packages/components/VideoCameraFilled.vue";const tz=T3;var I3=l({name:"View"});const nz={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},oz=a("path",{fill:"currentColor",d:"M512 160c320 0 512 352 512 352S832 864 512 864 0 512 0 512s192-352 512-352zm0 64c-225.28 0-384.128 208.064-436.8 288 52.608 79.872 211.456 288 436.8 288 225.28 0 384.128-208.064 436.8-288-52.608-79.872-211.456-288-436.8-288zm0 64a224 224 0 1 1 0 448 224 224 0 0 1 0-448zm0 64a160.192 160.192 0 0 0-160 160c0 88.192 71.744 160 160 160s160-71.808 160-160-71.744-160-160-160z"},null,-1);function rz(e,t,n,o,r,s){return c(),i("svg",nz,[oz])}I3.render=rz;I3.__file="packages/components/View.vue";const sz=I3;var O3=l({name:"Wallet"});const az={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},cz=a("path",{fill:"currentColor",d:"M640 288h-64V128H128v704h384v32a32 32 0 0 0 32 32H96a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32h512a32 32 0 0 1 32 32v192z"},null,-1),lz=a("path",{fill:"currentColor",d:"M128 320v512h768V320H128zm-32-64h832a32 32 0 0 1 32 32v576a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V288a32 32 0 0 1 32-32z"},null,-1),iz=a("path",{fill:"currentColor",d:"M704 640a64 64 0 1 1 0-128 64 64 0 0 1 0 128z"},null,-1);function uz(e,t,n,o,r,s){return c(),i("svg",az,[cz,lz,iz])}O3.render=uz;O3.__file="packages/components/Wallet.vue";const pz=O3;var N3=l({name:"WarningFilled"});const _z={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},dz=a("path",{fill:"currentColor",d:"M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896zm0 192a58.432 58.432 0 0 0-58.24 63.744l23.36 256.384a35.072 35.072 0 0 0 69.76 0l23.296-256.384A58.432 58.432 0 0 0 512 256zm0 512a51.2 51.2 0 1 0 0-102.4 51.2 51.2 0 0 0 0 102.4z"},null,-1);function hz(e,t,n,o,r,s){return c(),i("svg",_z,[dz])}N3.render=hz;N3.__file="packages/components/WarningFilled.vue";const $z=N3;var P3=l({name:"Watch"});const fz={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},mz=a("path",{fill:"currentColor",d:"M512 768a256 256 0 1 0 0-512 256 256 0 0 0 0 512zm0 64a320 320 0 1 1 0-640 320 320 0 0 1 0 640z"},null,-1),vz=a("path",{fill:"currentColor",d:"M480 352a32 32 0 0 1 32 32v160a32 32 0 0 1-64 0V384a32 32 0 0 1 32-32z"},null,-1),gz=a("path",{fill:"currentColor",d:"M480 512h128q32 0 32 32t-32 32H480q-32 0-32-32t32-32zm128-256V128H416v128h-64V64h320v192h-64zM416 768v128h192V768h64v192H352V768h64z"},null,-1);function wz(e,t,n,o,r,s){return c(),i("svg",fz,[mz,vz,gz])}P3.render=wz;P3.__file="packages/components/Watch.vue";const zz=P3;var D3=l({name:"VideoPlay"});const xz={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Cz=a("path",{fill:"currentColor",d:"M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896zm0 832a384 384 0 0 0 0-768 384 384 0 0 0 0 768zm-48-247.616L668.608 512 464 375.616v272.768zm10.624-342.656 249.472 166.336a48 48 0 0 1 0 79.872L474.624 718.272A48 48 0 0 1 400 678.336V345.6a48 48 0 0 1 74.624-39.936z"},null,-1);function Mz(e,t,n,o,r,s){return c(),i("svg",xz,[Cz])}D3.render=Mz;D3.__file="packages/components/VideoPlay.vue";const Lz=D3;var R3=l({name:"Watermelon"});const Hz={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},kz=a("path",{fill:"currentColor",d:"m683.072 600.32-43.648 162.816-61.824-16.512 53.248-198.528L576 493.248l-158.4 158.4-45.248-45.248 158.4-158.4-55.616-55.616-198.528 53.248-16.512-61.824 162.816-43.648L282.752 200A384 384 0 0 0 824 741.248L683.072 600.32zm231.552 141.056a448 448 0 1 1-632-632l632 632z"},null,-1);function Vz(e,t,n,o,r,s){return c(),i("svg",Hz,[kz])}R3.render=Vz;R3.__file="packages/components/Watermelon.vue";const bz=R3;var U3=l({name:"VideoCamera"});const yz={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Az=a("path",{fill:"currentColor",d:"M704 768V256H128v512h576zm64-416 192-96v512l-192-96v128a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V224a32 32 0 0 1 32-32h640a32 32 0 0 1 32 32v128zm0 71.552v176.896l128 64V359.552l-128 64zM192 320h192v64H192v-64z"},null,-1);function Ez(e,t,n,o,r,s){return c(),i("svg",yz,[Az])}U3.render=Ez;U3.__file="packages/components/VideoCamera.vue";const Sz=U3;var G3=l({name:"WalletFilled"});const Bz={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Fz=a("path",{fill:"currentColor",d:"M688 512a112 112 0 1 0 0 224h208v160H128V352h768v160H688zm32 160h-32a48 48 0 0 1 0-96h32a48 48 0 0 1 0 96zm-80-544 128 160H384l256-160z"},null,-1);function Tz(e,t,n,o,r,s){return c(),i("svg",Bz,[Fz])}G3.render=Tz;G3.__file="packages/components/WalletFilled.vue";const Iz=G3;var W3=l({name:"Warning"});const Oz={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Nz=a("path",{fill:"currentColor",d:"M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896zm0 832a384 384 0 0 0 0-768 384 384 0 0 0 0 768zm48-176a48 48 0 1 1-96 0 48 48 0 0 1 96 0zm-48-464a32 32 0 0 1 32 32v288a32 32 0 0 1-64 0V288a32 32 0 0 1 32-32z"},null,-1);function Pz(e,t,n,o,r,s){return c(),i("svg",Oz,[Nz])}W3.render=Pz;W3.__file="packages/components/Warning.vue";const Dz=W3;var Z3=l({name:"List"});const Rz={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Uz=a("path",{fill:"currentColor",d:"M704 192h160v736H160V192h160v64h384v-64zM288 512h448v-64H288v64zm0 256h448v-64H288v64zm96-576V96h256v96H384z"},null,-1);function Gz(e,t,n,o,r,s){return c(),i("svg",Rz,[Uz])}Z3.render=Gz;Z3.__file="packages/components/List.vue";const Wz=Z3;var q3=l({name:"ZoomIn"});const Zz={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},qz=a("path",{fill:"currentColor",d:"m795.904 750.72 124.992 124.928a32 32 0 0 1-45.248 45.248L750.656 795.904a416 416 0 1 1 45.248-45.248zM480 832a352 352 0 1 0 0-704 352 352 0 0 0 0 704zm-32-384v-96a32 32 0 0 1 64 0v96h96a32 32 0 0 1 0 64h-96v96a32 32 0 0 1-64 0v-96h-96a32 32 0 0 1 0-64h96z"},null,-1);function jz(e,t,n,o,r,s){return c(),i("svg",Zz,[qz])}q3.render=jz;q3.__file="packages/components/ZoomIn.vue";const Yz=q3;var j3=l({name:"ZoomOut"});const Kz={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},Xz=a("path",{fill:"currentColor",d:"m795.904 750.72 124.992 124.928a32 32 0 0 1-45.248 45.248L750.656 795.904a416 416 0 1 1 45.248-45.248zM480 832a352 352 0 1 0 0-704 352 352 0 0 0 0 704zM352 448h256a32 32 0 0 1 0 64H352a32 32 0 0 1 0-64z"},null,-1);function Jz(e,t,n,o,r,s){return c(),i("svg",Kz,[Xz])}j3.render=Jz;j3.__file="packages/components/ZoomOut.vue";const Qz=j3;var Y3=l({name:"Rank"});const ex={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},tx=a("path",{fill:"currentColor",d:"m186.496 544 41.408 41.344a32 32 0 1 1-45.248 45.312l-96-96a32 32 0 0 1 0-45.312l96-96a32 32 0 1 1 45.248 45.312L186.496 480h290.816V186.432l-41.472 41.472a32 32 0 1 1-45.248-45.184l96-96.128a32 32 0 0 1 45.312 0l96 96.064a32 32 0 0 1-45.248 45.184l-41.344-41.28V480H832l-41.344-41.344a32 32 0 0 1 45.248-45.312l96 96a32 32 0 0 1 0 45.312l-96 96a32 32 0 0 1-45.248-45.312L832 544H541.312v293.44l41.344-41.28a32 32 0 1 1 45.248 45.248l-96 96a32 32 0 0 1-45.312 0l-96-96a32 32 0 1 1 45.312-45.248l41.408 41.408V544H186.496z"},null,-1);function nx(e,t,n,o,r,s){return c(),i("svg",ex,[tx])}Y3.render=nx;Y3.__file="packages/components/Rank.vue";const ox=Y3;var K3=l({name:"WindPower"});const rx={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},sx=a("path",{fill:"currentColor",d:"M160 64q32 0 32 32v832q0 32-32 32t-32-32V96q0-32 32-32zm416 354.624 128-11.584V168.96l-128-11.52v261.12zm-64 5.824V151.552L320 134.08V160h-64V64l616.704 56.064A96 96 0 0 1 960 215.68v144.64a96 96 0 0 1-87.296 95.616L256 512V224h64v217.92l192-17.472zm256-23.232 98.88-8.96A32 32 0 0 0 896 360.32V215.68a32 32 0 0 0-29.12-31.872l-98.88-8.96v226.368z"},null,-1);function ax(e,t,n,o,r,s){return c(),i("svg",rx,[sx])}K3.render=ax;K3.__file="packages/components/WindPower.vue";const cx=K3,d6=Object.freeze(Object.defineProperty({__proto__:null,AddLocation:So,Aim:ko,AlarmClock:Ro,Apple:Io,ArrowDown:Zo,ArrowDownBold:Ko,ArrowLeft:e8,ArrowLeftBold:r8,ArrowRight:$a,ArrowRightBold:l8,ArrowUp:_8,ArrowUpBold:cc,Avatar:fr,Back:m8,Baseball:V8,Basketball:G8,Bell:C8,BellFilled:P8,Bicycle:T8,Bottom:j8,BottomLeft:Sr,BottomRight:fc,Bowl:_r,Box:er,Briefcase:rr,Brush:wr,BrushFilled:lr,Burger:Mr,Calendar:Ir,Camera:Vr,CameraFilled:Mc,CaretBottom:Dr,CaretLeft:Wr,CaretRight:Yr,CaretTop:Qr,Cellphone:ls,ChatDotRound:ds,ChatDotSquare:rs,ChatLineRound:Cs,ChatLineSquare:vs,ChatRound:ks,ChatSquare:Fs,Check:As,Checked:na,Cherry:Ns,Chicken:Us,CircleCheck:Js,CircleCheckFilled:qs,CircleClose:pa,CircleCloseFilled:aa,CirclePlus:za,CirclePlusFilled:Ua,Clock:ka,Close:Fa,CloseBold:Aa,Cloudy:Na,Coffee:wc,CoffeeCup:qa,Coin:oc,ColdDrink:Xa,Collection:bc,CollectionTag:pc,Comment:f7,Compass:Qc,Connection:rl,Coordinate:Gc,CopyDocument:qw,Cpu:Bc,CreditCard:il,Crop:Nc,DArrowLeft:jc,DArrowRight:gl,DCaret:Sl,DataAnalysis:fi,DataBoard:$l,DataLine:v5,Delete:Il,DeleteFilled:wi,DeleteLocation:bl,Dessert:Cl,Discount:ei,Dish:Dl,DishDot:Wl,Document:s5,DocumentAdd:li,DocumentChecked:ri,DocumentCopy:Yl,DocumentDelete:X5,DocumentRemove:_i,Download:Mi,Drizzling:Vi,Edit:Di,Eleme:Ei,ElemeFilled:Ti,Expand:Yi,Failed:Wi,Female:t5,Files:J7,Film:u5,Filter:x5,Finished:h5,FirstAidKit:F5,Flag:H5,Fold:U5,Folder:t9,FolderAdd:N5,FolderChecked:y5,FolderDelete:q5,FolderOpened:i9,FolderRemove:v9,Food:s9,Football:h9,ForkSpoon:y9,Fries:x9,FullScreen:H9,Goblet:B9,GobletFull:O9,GobletSquare:l7,GobletSquareFull:Z9,Goods:R9,GoodsFilled:K9,Grape:r7,Grid:e7,Guide:Vh,Headset:_7,Help:E7,HelpFilled:w7,Histogram:M7,HomeFilled:V7,HotWater:G7,House:T7,IceCream:j7,IceCreamRound:P7,IceCreamSquare:nu,IceDrink:yu,IceTea:uu,InfoFilled:xu,Iphone:vu,Key:au,KnifeFork:hu,Lightning:Fu,Link:Hu,List:Wz,Loading:Nu,Location:gp,LocationFilled:rp,LocationInformation:Yu,Lock:ep,Lollipop:Uu,MagicStick:kp,Magnet:lp,Male:hp,Management:Zf,MapLocation:Tp,Medal:Kp,Menu:Cp,Message:Wp,MessageBox:Ap,Mic:Pp,Microphone:r_,MilkTea:e_,Minus:l_,Money:h_,Monitor:C_,Moon:k_,MoonNight:g_,More:A_,MoreFilled:N_,MostlyCloudy:F_,Mouse:G_,Mug:j_,Mute:Q_,MuteNotification:ld,NoSmoking:od,Notebook:vd,Notification:dd,Odometer:Md,OfficeBuilding:yd,Open:qd,Operation:Bd,Opportunity:Od,Orange:Rd,Paperclip:Xd,PartlyCloudy:ah,Pear:th,Phone:uh,PhoneFilled:vh,Picture:U$,PictureFilled:hh,PictureRounded:Ch,PieChart:Uh,Place:Bh,Platform:Oh,Plus:Q6,Pointer:qh,Position:e$,Postcard:s$,Pouring:L$,Present:_$,PriceTag:m$,Printer:O$,Promotion:z$,QuestionFilled:B$,Rank:ox,Reading:J$,ReadingLamp:y$,Refresh:af,RefreshLeft:nf,RefreshRight:q$,Refrigerator:pf,Remove:Bf,RemoveFilled:$f,Right:gf,ScaleToOriginal:Cf,School:bf,Scissor:Of,Search:Kf,Select:Rf,Sell:em,SemiSelect:rm,Service:fm,SetUp:km,Setting:_m,Share:lm,Ship:wm,Shop:Tm,ShoppingBag:Em,ShoppingCart:Pm,ShoppingCartFull:Wm,Smoking:rv,Soccer:Ym,SoldOut:Qm,Sort:_v,SortDown:lv,SortUp:fv,Stamp:Mv,Star:wv,StarFilled:Vv,Stopwatch:Sv,SuccessFilled:Iv,Sugar:Zv,Suitcase:Rv,Sunny:Kv,Sunrise:eg,Sunset:_g,Switch:rg,SwitchButton:zg,TakeawayBox:Lg,Ticket:lg,Tickets:fg,Timer:Tg,ToiletPaper:yg,Tools:Pg,Top:Yg,TopLeft:Wg,TopRight:ew,TrendCharts:rw,Trophy:vw,TurnOff:iw,Umbrella:xw,Unlock:hw,Upload:Bw,UploadFilled:Hw,User:Ow,UserFilled:yw,Van:Rw,VideoCamera:Sz,VideoCameraFilled:tz,VideoPause:Xw,VideoPlay:Lz,View:sz,Wallet:pz,WalletFilled:Iz,Warning:Dz,WarningFilled:$z,Watch:zz,Watermelon:bz,WindPower:cx,ZoomIn:Yz,ZoomOut:Qz},Symbol.toStringTag,{value:"Module"}));const lx=e=>{e.use(Nn);for(const t in d6)e.component(t,d6[t])},ix={name:"trim",updated(e,t){var o,r;const n=e.children[0];try{if(!n)return!1;const s=n.selectionStart,u=(o=n.value)==null?void 0:o.length,h=t.value?t.value.replace(/\s/g,""):"";t.value!==h&&(n.value=h,((r=n.value)==null?void 0:r.length)!==u&&(n.selectionStart=s-1,n.selectionEnd=s-1),n.dispatchEvent(new Event(t.modifiers.lazy?"change":"input")))}catch(s){console.log(s)}}},ux={name:"copy",beforeMount(e,t){e.$value=t.value,e.handler=()=>{if(!e.$value)return;const n=document.createElement("textarea");n.readOnly=!0,n.style.position="absolute",n.style.left="-9999px",n.value=e.$value,document.body.appendChild(n),n.select(),document.execCommand("Copy")&&Pn.success("复制成功"),document.body.removeChild(n)},e.addEventListener("click",e.handler)},updated(e,t){e.$value=t.value},unmounted(e){e.removeEventListener("click",e.$handle)}},px={name:"focus",mounted(e){e.querySelector("input").focus()}},_x=[ix,ux,px],dx=e=>{for(const t of _x)e.directive(t.name,t)},hx=l({name:"breadcrumb",props:{breadcrumbs:{type:Array,default:()=>[]}},components:{},setup(){return{}}});const $x={class:"breadcrumb"};function fx(e,t,n,o,r,s){const u=s2("el-breadcrumb-item"),h=s2("el-breadcrumb");return c(),J("div",$x,[a(h,{separator:">"},{default:Y(()=>[a(u,{to:"/"},{default:Y(()=>t[0]||(t[0]=[I2(" 首页 ")])),_:1,__:[0]}),(c(!0),J(u0,null,K6(e.breadcrumbs,d=>(c(),i(u,{key:d.name,to:d.url?{path:d.url}:""},{default:Y(()=>[I2(f2(d.name),1)]),_:2},1032,["to"]))),128))]),_:1})])}const mx=k2(hx,[["render",fx],["__scopeId","data-v-2748abed"]]);const vx=l({name:"MobSvgIcon",props:{prefix:{type:String,default:"icon"},name:{type:String,required:!0}},setup(e){return{symbolId:E2(()=>`#${e.prefix}-${e.name}`)}}}),gx={key:1,class:"mob-svg-icon","aria-hidden":"true"},wx=["xlink:href"];function zx(e,t,n,o,r,s){return e.name.indexOf("el-icon-")===0?(c(),J("i",{key:0,class:Dn(e.name)},null,2)):(c(),J("svg",gx,[w2("use",{"xlink:href":`#icon-${e.name}`},null,8,wx)]))}const xx=k2(vx,[["render",zx],["__scopeId","data-v-e38a631b"]]),Cx=l({name:"mob-table",props:{border:{type:Boolean,default:!0},listData:{type:Array,default:()=>[]},listCount:{type:Number,default:0},loading:{type:Boolean,default:!1},page:{type:Object,default:()=>({})},propList:{type:Array,default:()=>[]},childrenProps:{type:Object,default:()=>({})},pageSizes:{type:Array,default:()=>[10,20,50]},pageLayout:{type:String,default:"total,sizes,prev,pager,next,jumper"},isHiddenTooltip:{type:Boolean,default:!1},showAddItemBtn:{type:Boolean,default:!1},addItemBtnText:{type:String,default:"添加"},spanMethod:{type:Function},lazy:{type:Boolean},loadMethod:{type:Function},treeProps:{type:Object,default:()=>({children:"children",hasChildren:"hasChildren"})},rowKey:{type:String,default:"id"}},components:{},emits:["update:page","getPageDataByFilter","sortChange","addItem","selectChange"],setup(e,{emit:t}){const n=F2();let o=F2();return a0(()=>e.page,z=>{o.value=z},{immediate:!0,deep:!0}),{Plus:Q6,tableRef:n,pageInfo:o,handleSizeChange:z=>{t("update:page",{pageNum:1,pageSize:z})},handleCurrentChange:z=>{t("update:page",{...e.page,pageNum:z})},handleSortChange:z=>{t("sortChange",z)},handleClearSort:()=>{n.value.clearSort()},handleAddItem:()=>{t("addItem",e.listData)},handleSelectionChange:z=>{t("selectChange",z)}}}});const Mx={class:"mob-table"},Lx={key:1},Hx={class:"copy-text"},kx={key:1},Vx={key:0,class:"add-btn"},bx={class:"footer flex flex-right"};function yx(e,t,n,o,r,s){const u=s2("page-question"),h=s2("el-tooltip"),d=s2("el-table-column"),m=s2("el-table"),z=s2("el-button"),x=s2("el-pagination"),L=u6("copy"),y=u6("loading");return c(),J("div",Mx,[s0((c(),i(m,p6({class:"mt-10",ref:"tableRef",data:e.listData,border:e.border,style:{width:"100%"}},e.childrenProps,{"row-key":e.rowKey,"header-row-class-name":"table-header",onSelectionChange:e.handleSelectionChange,onSortChange:e.handleSortChange,"span-method":e.spanMethod,lazy:e.lazy,load:e.loadMethod,"tree-props":e.treeProps}),{default:Y(()=>[(c(!0),J(u0,null,K6(e.propList,V=>(c(),i(d,p6({key:V.label,ref_for:!0},V,{type:V.type||void 0,align:V.align?V.align:"center","show-overflow-tooltip":!e.isHiddenTooltip,fixed:V.fixed||void 0}),{header:Y(()=>[V.labelTip?(c(),i(u,{key:0,class:"header-question",title:V.label,tip:V.labelTip,width:V.labelTipWidth||200,placement:"top"},null,8,["title","tip","width"])):(c(),J("span",Lx,f2(V.label),1))]),default:Y(A=>[!V.type||V.type!="selection"?T2(e.$slots,V.slotName,{key:0,row:A.row},()=>[V.isCopy?(c(),i(h,{key:0,effect:"dark",content:"点击文本即可复制",placement:"right"},{default:Y(()=>[s0((c(),J("span",Hx,[I2(f2(A.row[V.prop]?A.row[V.prop]:"-"),1)])),[[L,A.row[V.prop]]])]),_:2},1024)):(c(),J("span",kx,f2(A.row[V.prop]?A.row[V.prop]:"-"),1))],!0):r0("",!0)]),_:2},1040,["type","align","show-overflow-tooltip","fixed"]))),128))]),_:3},16,["data","border","row-key","onSelectionChange","onSortChange","span-method","lazy","load","tree-props"])),[[y,e.loading]]),e.showAddItemBtn?(c(),J("div",Vx,[a(z,{type:"primary",plain:"",icon:e.Plus,onClick:e.handleAddItem},{default:Y(()=>[I2(f2(e.addItemBtnText),1)]),_:1},8,["icon","onClick"])])):r0("",!0),w2("div",bx,[T2(e.$slots,"footer",{},()=>[a(x,{onSizeChange:e.handleSizeChange,onCurrentChange:e.handleCurrentChange,"current-page":e.page.pageNum,"page-size":e.page.pageSize,"page-sizes":e.pageSizes,layout:e.pageLayout,total:e.listCount,"pager-count":5},null,8,["onSizeChange","onCurrentChange","current-page","page-size","page-sizes","layout","total"])],!0)])])}const Ax=k2(Cx,[["render",yx],["__scopeId","data-v-b91d4e0e"]]),Ex=[mx,xx,Ax],Sx=e=>{for(const t of Ex)e.component(t.name,t)},Bx=e=>{e.use(lx),e.use(xo),e.use(dx),e.use(Sx)};if(typeof window<"u"){let e=function(){var t=document.body,n=document.getElementById("__svg__icons__dom__");n||(n=document.createElementNS("http://www.w3.org/2000/svg","svg"),n.style.position="absolute",n.style.width="0",n.style.height="0",n.id="__svg__icons__dom__",n.setAttribute("xmlns","http://www.w3.org/2000/svg"),n.setAttribute("xmlns:link","http://www.w3.org/1999/xlink")),n.innerHTML=`<symbol  viewBox="0 0 18 18" id="icon-am_nor"><path d="M9 0a9 9 0 1 1-.001 18.001A9 9 0 0 1 9 0ZM3.566 11.827a.15.15 0 0 0-.074.012.143.143 0 0 0-.053.044c-.012.018-.014.044-.006.074s.03.064.065.103c.144.168.325.351.538.544.215.193.498.41.852.653.351.241.723.458 1.107.647.385.189.831.347 1.342.476.51.128 1.018.193 1.528.193.505 0 .997-.05 1.473-.153.478-.102.88-.223 1.207-.362.326-.138.63-.29.904-.46.276-.168.48-.309.613-.423.133-.115.227-.205.283-.272.065-.078.097-.148.097-.205 0-.06-.024-.096-.07-.108a.28.28 0 0 0-.167.018c-.86.394-1.696.673-2.51.84-1.753.357-3.463.17-5.128-.56-.462-.204-1.105-.547-1.927-1.033a.14.14 0 0 0-.074-.028Zm10.48-.09a2.915 2.915 0 0 0-.693.005 2.268 2.268 0 0 0-.66.187c-.228.1-.328.183-.302.25l.01.017.012.01.02.004h.065l.044-.004.054-.006.07-.006c.028-.002.057-.006.083-.01l.144-.012.2-.018a4.282 4.282 0 0 1 .427-.012c.054.004.119.008.189.016s.129.02.173.038c.046.02.078.042.1.073.072.09.058.295-.042.612-.1.32-.195.57-.285.758-.038.078-.038.128 0 .148.038.02.094.002.166-.056.217-.169.4-.428.545-.771.084-.205.146-.416.183-.64.036-.22.032-.365-.014-.435-.06-.075-.223-.125-.489-.149ZM9.12 3.414c-.438 0-.844.05-1.224.153a3.541 3.541 0 0 0-.972.408c-.27.168-.5.365-.693.59a2.386 2.386 0 0 0-.43.703 2.09 2.09 0 0 0-.14.742l1.888.168c.076-.229.178-.427.307-.598a1.76 1.76 0 0 1 .362-.37c.11-.076.233-.136.36-.183a1.43 1.43 0 0 1 .258-.074c.046-.004.08-.006.107-.006.373 0 .633.11.777.33.089.132.135.333.135.604v.792c-.213.008-.426.022-.639.04a7.382 7.382 0 0 0-.767.114c-.3.059-.577.13-.834.215-.257.087-.52.205-.788.356-.267.15-.494.323-.683.516a2.248 2.248 0 0 0-.462.725c-.118.29-.179.61-.179.959 0 .38.067.715.2 1.012.132.297.309.53.532.703.223.173.476.308.755.408.28.1.575.143.882.13.307-.011.607-.06.898-.142.29-.082.562-.215.818-.396.253-.18.462-.393.624-.639.06.09.113.16.161.205l.123.125.117.117.254.247c.167.16.336.315.507.47l1.456-1.395-.082-.068a1.776 1.776 0 0 1-.213-.199 3.497 3.497 0 0 1-.241-.287 1.732 1.732 0 0 1-.205-.362A1.009 1.009 0 0 1 12 9.131V5.464c0-.142-.026-.293-.08-.458a2.031 2.031 0 0 0-.29-.536 2.35 2.35 0 0 0-.52-.516c-.207-.153-.486-.28-.831-.384a4.082 4.082 0 0 0-1.161-.155Zm.735 4.408V8.83c0 .233-.03.442-.09.629-.13.406-.372.661-.732.765a1.034 1.034 0 0 1-.892-.136.932.932 0 0 1-.45-.828c0-.28.07-.516.21-.713a1.27 1.27 0 0 1 .558-.442c.233-.096.46-.167.683-.209.223-.042.46-.066.713-.074Z" fill="#333" fill-rule="nonzero" /></symbol><symbol  viewBox="0 0 18 18" id="icon-fcm_nor"><path d="M9 0a9 9 0 1 1 0 18.001A9 9 0 0 1 9 0Zm-.816 7.28c-.684 0-1.206.221-1.566.677-.318.396-.474.918-.474 1.554 0 .648.15 1.164.456 1.548.348.444.882.672 1.596.672.462 0 .858-.132 1.188-.396.354-.282.576-.672.672-1.176h-.684c-.084.324-.228.57-.432.732-.192.144-.444.222-.75.222-.468 0-.816-.15-1.038-.438-.204-.27-.306-.66-.306-1.164 0-.492.102-.876.312-1.152.228-.312.564-.462 1.014-.462.3 0 .546.06.738.192.198.132.33.336.402.618h.684c-.066-.432-.252-.78-.564-1.032-.324-.264-.744-.396-1.248-.396Zm-2.43.083H2.832v4.284h.702V9.751h2.094v-.6H3.534V7.963h2.22v-.6Zm5.784 0h-.822v4.284h.702V8.695h.024l1.26 2.952h.606l1.26-2.952h.024v2.952h.702V7.363h-.822l-1.452 3.36h-.024l-1.458-3.36Z" fill="#333" fill-rule="evenodd" /></symbol><symbol class="icon" viewBox="0 0 1024 1024"  id="icon-home"><path d="M946.5 505 560.1 118.8l-25.9-25.9c-12.3-12.2-32.1-12.2-44.4 0L77.5 505c-12.3 12.3-18.9 28.6-18.8 46 .4 35.2 29.7 63.3 64.9 63.3h42.5V940h691.8V614.3h43.4c17.1 0 33.2-6.7 45.3-18.8 12.1-12.1 18.7-28.2 18.7-45.3 0-17-6.7-33.1-18.8-45.2zM568 868H456V664h112v204zm217.9-325.7V868H632V640c0-22.1-17.9-40-40-40H432c-22.1 0-40 17.9-40 40v228H238.1V542.3h-96l370-369.7 23.1 23.1L882 542.3h-96.1z" /></symbol><symbol  viewBox="0 0 18.001 18.001" id="icon-hw_nor"><path d="M9 0a9 9 0 1 1 0 18.001A9 9 0 0 1 9 0Zm1.121 11.422c.005 0 .482.364.743.562.274.194 1.287.837 1.935.55.653-.289.995-1.013.995-1.013l-3.673-.1Zm-2.245 0-3.668.099c.004 0 .35.724.994 1.012.653.288 1.656-.36 1.936-.549.256-.202.738-.562.738-.562Zm6.894-2.916c-.054-.014-1.404.738-2.061 1.066-.662.329-2.615 1.512-2.633 1.557-.018.05 2.475.077 2.903.018.373-.063 1.197-.27 1.62-.976.423-.716.216-1.652.171-1.665Zm-11.534 0c-.054.013-.252.954.166 1.66.419.707 1.247.927 1.62.977.148.022.546.032 1.002.035h.35c.768-.003 1.561-.025 1.551-.053-.009-.036-1.971-1.224-2.628-1.553-.662-.328-2.007-1.084-2.061-1.066Zm1.683-2.872c-.108.014-.878.779-.945 1.526-.068.747.247 1.247 1.143 1.845.9.644 3.038 1.814 3.074 1.715.04-.1-.828-1.643-1.54-2.768-.706-1.13-1.629-2.331-1.732-2.318Zm8.159 0c-.108-.013-1.03 1.189-1.737 2.314-.155.244-.317.508-.475.773l-.188.317c-.493.839-.904 1.605-.876 1.677.04.1 2.178-1.07 3.073-1.714.9-.599 1.225-1.094 1.148-1.841-.072-.747-.832-1.512-.945-1.526ZM9.923 4.046h-.072c-.063.09-.459 1.16-.594 2.007-.135.842.032 4.262.036 4.307.005.045.072.072.122.018.054-.054 1.548-2.44 1.831-3.042.284-.612.734-1.792.135-2.566-.495-.645-1.227-.718-1.458-.724Zm-1.768 0h-.033c-.171 0-.97.044-1.498.724-.594.774-.148 1.949.135 2.557.28.607 1.774 2.992 1.832 3.042.054.054.117.031.122-.018.004-.045.17-3.465.036-4.303-.135-.837-.54-1.912-.594-2.002Z" fill="#333" fill-rule="evenodd" /></symbol><symbol  viewBox="0 0 18 18" id="icon-icon-android"><path d="M9 0a9 9 0 1 1 0 18A9 9 0 0 1 9 0Zm3.214 7.286H5.785v3.929c0 .592.48 1.071 1.072 1.071v1.43c0 .392.321.713.714.713a.716.716 0 0 0 .714-.714v-1.429h1.429v1.43c0 .392.321.713.714.713a.716.716 0 0 0 .715-.714v-1.429c.591 0 1.071-.48 1.071-1.071V7.286Zm1.072 0A.716.716 0 0 0 12.57 8v2.858c0 .393.322.714.715.714a.716.716 0 0 0 .714-.714V8a.716.716 0 0 0-.714-.714Zm-8.573 0A.716.716 0 0 0 4 8v2.858c0 .393.322.714.714.714a.716.716 0 0 0 .715-.714V8a.716.716 0 0 0-.715-.714Zm5.753-4.089-.359.718-.093-.037a3.208 3.208 0 0 0-2.029 0l-.093.037-.359-.718a.357.357 0 0 0-.639.32l.358.715a3.213 3.213 0 0 0-1.447 2.34v.357h6.41v-.357h-.02a3.212 3.212 0 0 0-1.448-2.34l.358-.715a.357.357 0 0 0-.64-.32ZM7.929 5.143a.357.357 0 1 1-.002 0Zm2.143 0a.357.357 0 1 1-.002 0Z" fill="#333" fill-rule="evenodd" /></symbol><symbol  viewBox="0 0 30 30" id="icon-icon-avatar"><defs><linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="icon-icon-avatar_a"><stop stop-color="#FFF" offset="0%" /><stop stop-color="#FFF" stop-opacity=".6" offset="100%" /></linearGradient><linearGradient x1="67.021%" y1="44.391%" x2="28.269%" y2="66.237%" id="icon-icon-avatar_b"><stop stop-color="#FFF" stop-opacity="0" offset="0%" /><stop stop-color="#FFF" offset="100%" /></linearGradient><linearGradient x1="67.021%" y1="42.782%" x2="28.269%" y2="70.894%" id="icon-icon-avatar_c"><stop stop-color="#FFF" stop-opacity="0" offset="0%" /><stop stop-color="#FFF" offset="100%" /></linearGradient></defs><g fill="none" fill-rule="evenodd"><rect stroke="currentColor" stroke-width=".833" fill-opacity=".364" fill="#00BEA5" x=".417" y=".417" width="29.167" height="29.167" rx="14.583" /><path d="M10.662 10.767c3.58 0 6.48 2.872 6.48 6.418v.413c0 1.397-2.901 1.45-6.48 1.45H6.481C2.9 19.048 0 19.046 0 17.598v-.413c0-3.546 2.901-6.418 6.48-6.418h4.182ZM8.362 0c2.771 0 5.017 2.225 5.017 4.97 0 2.743-2.246 4.968-5.017 4.968-2.771 0-5.017-2.225-5.017-4.969C3.345 2.224 5.592 0 8.362 0Z" fill="url(#icon-icon-avatar_a)" transform="translate(6.466 5.51)" /><path d="M11.022 0c1.18 5.672-1.119 8.627-6.897 8.867-5.777.24-4.394.75 4.15 1.53l7.4-.459 2.317-5.95L11.022 0Z" fill="url(#icon-icon-avatar_b)" transform="translate(6.466 5.51)" /><path d="M13.408 8.99c1.18 6.435-1.119 9.788-6.896 10.06-5.778.272-4.395.85 4.15 1.734l7.4-.52 2.316-6.749-6.97-4.524Z" fill="url(#icon-icon-avatar_c)" transform="translate(6.466 5.51)" /></g></symbol><symbol class="icon" viewBox="0 0 1024 1024"  id="icon-icon-delete"><path d="M572.075 337.135a57.276 57.276 0 1 0-114.176-6.008h-.171l12.937 272.896v.444a44.544 44.544 0 1 0 89.02-1.194l12.424-266.138zM375.125 145.92c76.152-130.048 199.817-129.775 275.798 0L991.3 727.006c76.118 130.048 15.77 235.417-135.236 235.417h-686.08c-150.835 0-211.217-105.676-135.202-235.417L375.125 145.92zm140.05 687.582a57.276 57.276 0 1 0 0-114.517 57.276 57.276 0 0 0 0 114.517z" fill="#FB6547" /></symbol><symbol class="icon" viewBox="0 0 1024 1024"  id="icon-icon-email"><path d="M951.45 870.595c-2.381 32.43-74.512 20.47-95.232 20.47H127.432c-11.9 0-27.551 2.503-35.707-8.305-7.905-10.481-3.927-33.531-3.927-45.44V306.657c0-13.593-4.178-37.724 8.304-47.145 8.223-6.205 23.409-3.927 33.05-3.927h730.122c19.364 0 81.772-10.588 91.136 13.973 4.245 11.14 1.034 28.365 1.034 39.777v93.015l.006 468.245c0 33.013 51.2 33.013 51.2 0V301.604c0-19.262 1.914-40.417-7.783-57.892-25.559-46.1-74.148-39.327-118.38-39.327H108.934c-39.997 0-72.335 32.963-72.335 72.776v576.753c0 26.347 3.333 49.54 23.342 69.55 16.21 16.21 38.41 18.8 59.94 18.8h810.43c41.282 0 69.442-32.389 72.334-71.67 2.427-32.957-48.793-32.726-51.194 0z" fill="#2c2c2c" /><path d="m77.998 277.617 415.447 355.978c11.305 9.692 24.883 9.523 36.204 0L971.023 262.44c25.303-21.279-11.11-57.303-36.198-36.204a33642133.146 33642133.146 0 0 1-441.38 371.16h36.204L114.207 241.412c-24.924-21.36-61.317 14.69-36.209 36.204zM408.11 539.09 77.655 900.265" fill="#2c2c2c" /><path d="M390.01 520.99 59.55 882.167c-22.235 24.3 13.87 60.61 36.204 36.198l330.46-361.175c22.237-24.3-13.87-60.61-36.203-36.198zM603.336 538.081 964.29 900.265M585.236 556.18l360.955 362.184c23.327 23.409 59.525-12.8 36.199-36.198L621.435 519.982c-23.327-23.414-59.525 12.8-36.199 36.198z" fill="#2c2c2c" /></symbol><symbol  viewBox="0 0 18 18" id="icon-icon-ios"><path d="M9 0a9 9 0 1 1 0 18A9 9 0 0 1 9 0Zm1.905 5.786c-.836-.079-1.632.458-2.057.458-.423 0-1.078-.447-1.772-.435a2.662 2.662 0 0 0-2.223 1.254c-.947 1.53-.242 3.797.681 5.039.452.607.99 1.29 1.696 1.265.68-.025.937-.41 1.76-.41s1.054.41 1.774.397c.731-.012 1.195-.619 1.643-1.228.518-.705.731-1.388.744-1.423-.016-.007-1.426-.51-1.44-2.022-.015-1.267 1.11-1.875 1.159-1.905-.631-.859-1.615-.977-1.965-.99Zm.011-2.419c-.54.02-1.194.335-1.582.757-.348.376-.652.973-.57 1.548.603.043 1.218-.286 1.593-.708.375-.423.628-1.011.56-1.597Z" fill="#333" fill-rule="evenodd" /></symbol><symbol  viewBox="0 0 16 16" id="icon-icon-khgl-nor"><path d="M8 7.891c1.572 0 2.847-1.318 2.847-2.945C10.847 3.319 9.572 2 8 2S5.153 3.319 5.153 4.946C5.153 6.573 6.428 7.89 8 7.89Zm1.5.677h-3c-2.485 0-4.5 2.085-4.5 4.656 0 .429.336.776.75.776h10.5c.414 0 .75-.347.75-.776 0-2.571-2.015-4.656-4.5-4.656Z" fill="#B0BEC6" fill-rule="nonzero" /></symbol><symbol  viewBox="0 0 16 16" id="icon-icon-khgl-sel"><path d="M8 7.891c1.572 0 2.847-1.318 2.847-2.945C10.847 3.319 9.572 2 8 2S5.153 3.319 5.153 4.946C5.153 6.573 6.428 7.89 8 7.89Zm1.5.677h-3c-2.485 0-4.5 2.085-4.5 4.656 0 .429.336.776.75.776h10.5c.414 0 .75-.347.75-.776 0-2.571-2.015-4.656-4.5-4.656Z" fill="#009588" fill-rule="nonzero" /></symbol><symbol  viewBox="0 0 14 14" id="icon-icon-language"><path d="M7 0c1.857 0 3.637.727 4.95 2.021A6.85 6.85 0 0 1 14 6.9v.2c0 3.81-3.134 6.9-7 6.9s-7-3.09-7-6.9v-.2C0 3.09 3.134 0 7 0Zm5.972 7.5H9.93a9.777 9.777 0 0 1-1.985 5.427c2.754-.434 4.839-2.685 5.028-5.427Zm-8.9 0H1.027c.19 2.742 2.274 4.993 5.028 5.427A9.777 9.777 0 0 1 4.07 7.5Zm4.841 0H5.086a8.79 8.79 0 0 0 1.913 4.997A8.79 8.79 0 0 0 8.913 7.5Zm-.969-6.427A9.777 9.777 0 0 1 9.93 6.5h3.043c-.19-2.742-2.274-4.993-5.028-5.427ZM7 1.503A8.79 8.79 0 0 0 5.087 6.5h3.826A8.79 8.79 0 0 0 7 1.503Zm-.943-.43c-2.754.434-4.84 2.684-5.03 5.427h3.044a9.777 9.777 0 0 1 1.985-5.427Z" fill="#1C1C1C" fill-rule="nonzero" /></symbol><symbol   viewBox="0 0 134 50" id="icon-icon-logo"><defs><linearGradient x1="1.069%" y1="1.023%" x2="100%" y2="95.721%" id="icon-icon-logo_c"><stop stop-color="#F4FFFF" offset="0%" /><stop stop-color="#B0FFF5" offset="100%" /></linearGradient><linearGradient x1="46.1%" y1="30.738%" x2="35.927%" y2="96.999%" id="icon-icon-logo_d"><stop stop-color="#61E2EE" offset="0%" /><stop stop-color="#FFFDFC" stop-opacity=".018" offset="100%" /></linearGradient><linearGradient x1="50%" y1="100%" x2="62.371%" y2="1.023%" id="icon-icon-logo_g"><stop stop-color="#ECFFFD" offset="0%" /><stop stop-color="#9AFFE7" offset="100%" /></linearGradient><linearGradient x1="47.284%" y1="26.262%" x2="35.927%" y2="96.999%" id="icon-icon-logo_h"><stop stop-color="#61E2EE" offset="0%" /><stop stop-color="#FFFDFC" stop-opacity=".018" offset="100%" /></linearGradient><linearGradient x1="43.983%" y1="13.099%" x2="78.431%" y2="88.443%" id="icon-icon-logo_j"><stop stop-color="#B3FFED" offset="0%" /><stop stop-color="#44D7ED" offset="100%" /></linearGradient><path d="M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0Zm0 3.634a4.366 4.366 0 1 0 0 8.732 4.366 4.366 0 0 0 0-8.732Z" id="icon-icon-logo_b" /><path d="M5.88 0a5.88 5.88 0 1 1 0 11.76A5.88 5.88 0 0 1 5.88 0Zm0 2.019a3.861 3.861 0 1 0 0 7.722 3.861 3.861 0 0 0 0-7.722Z" id="icon-icon-logo_f" /><filter x="-143.8%" y="-143.8%" width="387.5%" height="387.5%" filterUnits="objectBoundingBox" id="icon-icon-logo_a"><feOffset dy="6" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset><feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur><feColorMatrix values="0 0 0 0 0.0823529412 0 0 0 0 0.647058824 0 0 0 0 0.874509804 0 0 0 1 0" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix><feMerge><feMergeNode in="shadowMatrixOuter1"></feMergeNode><feMergeNode in="SourceGraphic"></feMergeNode></feMerge></filter></defs><g fill="none" fill-rule="evenodd"><text transform="translate(37.887 14.427)" fill="#FFF" font-family="PingFangSC-Semibold, PingFang SC" font-size="16" font-weight="500">
                        <tspan x="0" y="17">秒验管理平台</tspan>
                    </text><g filter="url(#icon-icon-logo_a)" transform="matrix(1 0 0 -1 17 33)"><mask id="icon-icon-logo_e" fill="#fff"><use xlink:href="#icon-icon-logo_b" /></mask><use fill="url(#icon-icon-logo_c)" xlink:href="#icon-icon-logo_b" /><path d="M13.727 2.346c.32.343.547.605.681.786.295.394.542.842.703 1.116.451.772 1.14 2.925.795 4.948-.344 2.016-1.306 3.606-1.906 4.137-3.053 1.014-4.716.17-4.991-2.533.686-.404 1.129-.736 1.329-.998.643-.84.646-1.63.63-1.952-.023-.471-.187-.96-.49-1.465l3.249-4.039Z" fill="url(#icon-icon-logo_d)" mask="url(#icon-icon-logo_e)" /><g transform="translate(2.12 2.12)"><mask id="icon-icon-logo_i" fill="#fff"><use xlink:href="#icon-icon-logo_f" /></mask><use fill="url(#icon-icon-logo_g)" xlink:href="#icon-icon-logo_f" /><path d="M11.607.226c.32.343.547.605.682.786.294.394.541.842.702 1.116.451.772 1.141 2.925.796 4.948-.345 2.016-1.307 3.606-1.907 4.137-3.053 1.014-4.716.17-4.991-2.533.686-.404 1.129-.736 1.33-.998.642-.839.645-1.63.63-1.952-.024-.47-.187-.96-.49-1.464l3.248-4.04Z" fill="url(#icon-icon-logo_h)" mask="url(#icon-icon-logo_i)" /></g><path d="M14.118 1.882 9.699 7.995c-.221.289-.569.476-.959.476-.669 0-1.21-.548-1.21-1.224 0-.393.182-.742.466-.966l-.005-.005 6.127-4.394Z" fill="url(#icon-icon-logo_j)" /></g></g></symbol><symbol   viewBox="0 0 134 50" id="icon-icon-logo1"><defs><linearGradient x1="1.069%" y1="1.023%" x2="100%" y2="95.721%" id="icon-icon-logo1_c"><stop stop-color="#F4FFFF" offset="0%" /><stop stop-color="#B0FFF5" offset="100%" /></linearGradient><linearGradient x1="46.1%" y1="30.738%" x2="35.927%" y2="96.999%" id="icon-icon-logo1_d"><stop stop-color="#61E2EE" offset="0%" /><stop stop-color="#FFFDFC" stop-opacity=".018" offset="100%" /></linearGradient><linearGradient x1="50%" y1="100%" x2="62.371%" y2="1.023%" id="icon-icon-logo1_g"><stop stop-color="#ECFFFD" offset="0%" /><stop stop-color="#9AFFE7" offset="100%" /></linearGradient><linearGradient x1="47.284%" y1="26.262%" x2="35.927%" y2="96.999%" id="icon-icon-logo1_h"><stop stop-color="#61E2EE" offset="0%" /><stop stop-color="#FFFDFC" stop-opacity=".018" offset="100%" /></linearGradient><linearGradient x1="43.983%" y1="13.099%" x2="78.431%" y2="88.443%" id="icon-icon-logo1_j"><stop stop-color="#B3FFED" offset="0%" /><stop stop-color="#44D7ED" offset="100%" /></linearGradient><path d="M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0Zm0 3.634a4.366 4.366 0 1 0 0 8.732 4.366 4.366 0 0 0 0-8.732Z" id="icon-icon-logo1_b" /><path d="M5.88 0a5.88 5.88 0 1 1 0 11.76A5.88 5.88 0 0 1 5.88 0Zm0 2.019a3.861 3.861 0 1 0 0 7.722 3.861 3.861 0 0 0 0-7.722Z" id="icon-icon-logo1_f" /><filter x="-143.8%" y="-143.8%" width="387.5%" height="387.5%" filterUnits="objectBoundingBox" id="icon-icon-logo1_a"><feOffset dy="6" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset><feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur><feColorMatrix values="0 0 0 0 0.0823529412 0 0 0 0 0.647058824 0 0 0 0 0.874509804 0 0 0 1 0" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix><feMerge><feMergeNode in="shadowMatrixOuter1"></feMergeNode><feMergeNode in="SourceGraphic"></feMergeNode></feMerge></filter></defs><g fill="none" fill-rule="evenodd"><text transform="translate(37.887 14.427)" fill="#FFF" font-family="PingFangSC-Semibold, PingFang SC" font-size="16" font-weight="500">
                        <tspan x="0" y="17">秒验管理平台</tspan>
                    </text><g filter="url(#icon-icon-logo1_a)" transform="matrix(1 0 0 -1 17 33)"><mask id="icon-icon-logo1_e" fill="#fff"><use xlink:href="#icon-icon-logo1_b" /></mask><use fill="url(#icon-icon-logo1_c)" xlink:href="#icon-icon-logo1_b" /><path d="M13.727 2.346c.32.343.547.605.681.786.295.394.542.842.703 1.116.451.772 1.14 2.925.795 4.948-.344 2.016-1.306 3.606-1.906 4.137-3.053 1.014-4.716.17-4.991-2.533.686-.404 1.129-.736 1.329-.998.643-.84.646-1.63.63-1.952-.023-.471-.187-.96-.49-1.465l3.249-4.039Z" fill="url(#icon-icon-logo1_d)" mask="url(#icon-icon-logo1_e)" /><g transform="translate(2.12 2.12)"><mask id="icon-icon-logo1_i" fill="#fff"><use xlink:href="#icon-icon-logo1_f" /></mask><use fill="url(#icon-icon-logo1_g)" xlink:href="#icon-icon-logo1_f" /><path d="M11.607.226c.32.343.547.605.682.786.294.394.541.842.702 1.116.451.772 1.141 2.925.796 4.948-.345 2.016-1.307 3.606-1.907 4.137-3.053 1.014-4.716.17-4.991-2.533.686-.404 1.129-.736 1.33-.998.642-.839.645-1.63.63-1.952-.024-.47-.187-.96-.49-1.464l3.248-4.04Z" fill="url(#icon-icon-logo1_h)" mask="url(#icon-icon-logo1_i)" /></g><path d="M14.118 1.882 9.699 7.995c-.221.289-.569.476-.959.476-.669 0-1.21-.548-1.21-1.224 0-.393.182-.742.466-.966l-.005-.005 6.127-4.394Z" fill="url(#icon-icon-logo1_j)" /></g></g></symbol><symbol   viewBox="0 0 134 50" id="icon-icon-logo2"><defs><linearGradient x1="1.069%" y1="1.023%" x2="100%" y2="95.721%" id="icon-icon-logo2_c"><stop stop-color="#F4FFFF" offset="0%" /><stop stop-color="#B0FFF5" offset="100%" /></linearGradient><linearGradient x1="46.1%" y1="30.738%" x2="35.927%" y2="96.999%" id="icon-icon-logo2_d"><stop stop-color="#61E2EE" offset="0%" /><stop stop-color="#FFFDFC" stop-opacity=".018" offset="100%" /></linearGradient><linearGradient x1="50%" y1="100%" x2="62.371%" y2="1.023%" id="icon-icon-logo2_g"><stop stop-color="#ECFFFD" offset="0%" /><stop stop-color="#9AFFE7" offset="100%" /></linearGradient><linearGradient x1="47.284%" y1="26.262%" x2="35.927%" y2="96.999%" id="icon-icon-logo2_h"><stop stop-color="#61E2EE" offset="0%" /><stop stop-color="#FFFDFC" stop-opacity=".018" offset="100%" /></linearGradient><linearGradient x1="43.983%" y1="13.099%" x2="78.431%" y2="88.443%" id="icon-icon-logo2_j"><stop stop-color="#B3FFED" offset="0%" /><stop stop-color="#44D7ED" offset="100%" /></linearGradient><path d="M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0Zm0 3.634a4.366 4.366 0 1 0 0 8.732 4.366 4.366 0 0 0 0-8.732Z" id="icon-icon-logo2_b" /><path d="M5.88 0a5.88 5.88 0 1 1 0 11.76A5.88 5.88 0 0 1 5.88 0Zm0 2.019a3.861 3.861 0 1 0 0 7.722 3.861 3.861 0 0 0 0-7.722Z" id="icon-icon-logo2_f" /><filter x="-143.8%" y="-143.8%" width="387.5%" height="387.5%" filterUnits="objectBoundingBox" id="icon-icon-logo2_a"><feOffset dy="6" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset><feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur><feColorMatrix values="0 0 0 0 0.0823529412 0 0 0 0 0.647058824 0 0 0 0 0.874509804 0 0 0 1 0" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix><feMerge><feMergeNode in="shadowMatrixOuter1"></feMergeNode><feMergeNode in="SourceGraphic"></feMergeNode></feMerge></filter></defs><g fill="none" fill-rule="evenodd"><text transform="translate(37.887 14.427)" fill="#FFF" font-family="PingFangSC-Semibold, PingFang SC" font-size="16" font-weight="500">
                        <tspan x="0" y="17">秒验管理平台</tspan>
                    </text><g filter="url(#icon-icon-logo2_a)" transform="matrix(1 0 0 -1 17 33)"><mask id="icon-icon-logo2_e" fill="#fff"><use xlink:href="#icon-icon-logo2_b" /></mask><use fill="url(#icon-icon-logo2_c)" xlink:href="#icon-icon-logo2_b" /><path d="M13.727 2.346c.32.343.547.605.681.786.295.394.542.842.703 1.116.451.772 1.14 2.925.795 4.948-.344 2.016-1.306 3.606-1.906 4.137-3.053 1.014-4.716.17-4.991-2.533.686-.404 1.129-.736 1.329-.998.643-.84.646-1.63.63-1.952-.023-.471-.187-.96-.49-1.465l3.249-4.039Z" fill="url(#icon-icon-logo2_d)" mask="url(#icon-icon-logo2_e)" /><g transform="translate(2.12 2.12)"><mask id="icon-icon-logo2_i" fill="#fff"><use xlink:href="#icon-icon-logo2_f" /></mask><use fill="url(#icon-icon-logo2_g)" xlink:href="#icon-icon-logo2_f" /><path d="M11.607.226c.32.343.547.605.682.786.294.394.541.842.702 1.116.451.772 1.141 2.925.796 4.948-.345 2.016-1.307 3.606-1.907 4.137-3.053 1.014-4.716.17-4.991-2.533.686-.404 1.129-.736 1.33-.998.642-.839.645-1.63.63-1.952-.024-.47-.187-.96-.49-1.464l3.248-4.04Z" fill="url(#icon-icon-logo2_h)" mask="url(#icon-icon-logo2_i)" /></g><path d="M14.118 1.882 9.699 7.995c-.221.289-.569.476-.959.476-.669 0-1.21-.548-1.21-1.224 0-.393.182-.742.466-.966l-.005-.005 6.127-4.394Z" fill="url(#icon-icon-logo2_j)" /></g></g></symbol><symbol  viewBox="0 0 14 14" id="icon-icon-organize"><path d="M9.818 1.273C7.91 1.273 6 .001 4.091.001A7.72 7.72 0 0 0 .91.637v12.727a.636.636 0 0 0 1.272 0V9.125a8.523 8.523 0 0 1 1.91-.216c1.908 0 3.817 1.273 5.726 1.273A7.72 7.72 0 0 0 13 9.546V.637a7.72 7.72 0 0 1-3.182.636Zm1.91 7.382a7.142 7.142 0 0 1-1.91.254 8.845 8.845 0 0 1-2.708-.653 9.685 9.685 0 0 0-3.019-.62c-.64 0-1.28.063-1.909.188V1.528a7.142 7.142 0 0 1 1.91-.255 8.845 8.845 0 0 1 2.707.654 9.685 9.685 0 0 0 3.02.62c.64 0 1.28-.063 1.908-.188v6.296Z" fill="#1C1C1C" fill-rule="nonzero" /></symbol><symbol  viewBox="0 0 70 70" id="icon-icon-pass"><defs><linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="icon-icon-pass_a"><stop stop-color="#13E6C5" offset="0%" /><stop stop-color="#03A790" offset="100%" /></linearGradient></defs><g fill="none" fill-rule="evenodd"><circle fill="url(#icon-icon-pass_a)" cx="35" cy="35" r="35" /><path d="M54.65 24.384s-6.344 2.083-13.745 10.414c-6.873 7.55-8.459 10.673-11.102 14.838-.265-.26-4.23-7.81-14.803-13.276l5.551-5.207s5.022 3.384 8.459 9.632c0 0 8.723-13.277 25.64-19.785v3.384Z" fill="#FFF" fill-rule="nonzero" /></g></symbol><symbol  viewBox="0 0 16 16" id="icon-icon-sjbb-nor"><path d="M12.286 2C13.232 2 14 2.768 14 3.714v8.572c0 .946-.768 1.714-1.714 1.714H3.714A1.714 1.714 0 0 1 2 12.286V3.714C2 2.768 2.768 2 3.714 2h8.572Zm-1.863 3.429a.429.429 0 0 0-.428.428V11a.429.429 0 1 0 .857 0V5.857a.429.429 0 0 0-.429-.428ZM5.281 8.857a.429.429 0 0 0-.429.429V11a.429.429 0 1 0 .857 0V9.286a.429.429 0 0 0-.428-.429Zm2.571-1.714a.429.429 0 0 0-.429.428V11a.429.429 0 1 0 .858 0V7.571a.429.429 0 0 0-.429-.428Z" fill="#B0BEC6" fill-rule="nonzero" /></symbol><symbol  viewBox="0 0 16 16" id="icon-icon-sjbb-sel"><path d="M12.286 2C13.232 2 14 2.768 14 3.714v8.572c0 .946-.768 1.714-1.714 1.714H3.714A1.714 1.714 0 0 1 2 12.286V3.714C2 2.768 2.768 2 3.714 2h8.572Zm-1.863 3.429a.429.429 0 0 0-.428.428V11a.429.429 0 1 0 .857 0V5.857a.429.429 0 0 0-.429-.428ZM5.281 8.857a.429.429 0 0 0-.429.429V11a.429.429 0 1 0 .857 0V9.286a.429.429 0 0 0-.428-.429Zm2.571-1.714a.429.429 0 0 0-.429.428V11a.429.429 0 1 0 .858 0V7.571a.429.429 0 0 0-.429-.428Z" fill="#009588" fill-rule="nonzero" /></symbol><symbol  viewBox="0 0 16 16" id="icon-icon-xtgl-nor"><path d="M11.467 14c.202 0 .39-.114.49-.298l2.967-5.404a.622.622 0 0 0 0-.596l-2.967-5.404a.562.562 0 0 0-.49-.298H5.533a.562.562 0 0 0-.49.298L2.076 7.702a.622.622 0 0 0 0 .596l2.967 5.404a.562.562 0 0 0 .49.298h5.934Zm-2.852-4a2 2 0 1 1 0-4 2 2 0 0 1 0 4Z" fill="#B0BEC6" fill-rule="evenodd" /></symbol><symbol  viewBox="0 0 16 16" id="icon-icon-xtgl-sel"><path d="M11.467 14c.202 0 .39-.114.49-.298l2.967-5.404a.622.622 0 0 0 0-.596l-2.967-5.404a.562.562 0 0 0-.49-.298H5.533a.562.562 0 0 0-.49.298L2.076 7.702a.622.622 0 0 0 0 .596l2.967 5.404a.562.562 0 0 0 .49.298h5.934Zm-2.852-4a2 2 0 1 1 0-4 2 2 0 0 1 0 4Z" fill="#009588" fill-rule="evenodd" /></symbol><symbol  viewBox="0 0 16 16" id="icon-icon-yygl-nor"><path d="M12.5 14H9.86c-.84 0-1.5-.66-1.5-1.5V8.36h4.14c.84 0 1.5.66 1.5 1.5v2.64c0 .84-.66 1.5-1.5 1.5Zm0-6.42H8.36V3.5c0-.84.66-1.5 1.5-1.5h2.64c.84 0 1.5.66 1.5 1.5v2.58c0 .84-.66 1.5-1.5 1.5ZM6.08 14H3.5c-.84 0-1.5-.66-1.5-1.5V9.86c0-.84.66-1.5 1.5-1.5h4.08v4.14c0 .84-.66 1.5-1.5 1.5ZM2 6.08V3.5C2 2.66 2.66 2 3.5 2h2.58c.84 0 1.5.66 1.5 1.5v4.08H3.5c-.84 0-1.5-.66-1.5-1.5Z" fill="#B0BEC6" fill-rule="evenodd" /></symbol><symbol  viewBox="0 0 16 16" id="icon-icon-yygl-sel"><path d="M12.5 14H9.86c-.84 0-1.5-.66-1.5-1.5V8.36h4.14c.84 0 1.5.66 1.5 1.5v2.64c0 .84-.66 1.5-1.5 1.5Zm0-6.42H8.36V3.5c0-.84.66-1.5 1.5-1.5h2.64c.84 0 1.5.66 1.5 1.5v2.58c0 .84-.66 1.5-1.5 1.5ZM6.08 14H3.5c-.84 0-1.5-.66-1.5-1.5V9.86c0-.84.66-1.5 1.5-1.5h4.08v4.14c0 .84-.66 1.5-1.5 1.5ZM2 6.08V3.5C2 2.66 2.66 2 3.5 2h2.58c.84 0 1.5.66 1.5 1.5v4.08H3.5c-.84 0-1.5-.66-1.5-1.5Z" fill="#009588" fill-rule="evenodd" /></symbol><symbol  viewBox="0 0 18 18" id="icon-mz_nor"><path d="M9 0a9 9 0 1 1 0 18A9 9 0 0 1 9 0Zm.705 7.622H9.23v2.747h.474V7.622Zm-5.021 0H1.936c-.284 0-.473.19-.473.473v2.274h.473V8.095H2.98v2.274h.473V8.095h1.232v2.274h.473V8.095c-.094-.284-.284-.473-.473-.473Zm4.073 0H6.484c-.284 0-.474.19-.474.473v1.8c0 .095.095.284.095.284.081.163.162.186.303.19h2.35v-.474H6.483v-.568h2.273v-.474H6.484v-.758h2.273v-.473Zm4.169 0h-2.747v.473h1.989l-1.895 1.8h-.094v.38h2.747v-.38h-1.99l1.895-1.8h.095v-.473Zm.947 0H13.4v2.273c0 .284.19.38.473.38h1.8c.19 0 .284-.096.38-.19h.094V7.622h-.474v2.273h-1.8V7.622Z" fill="#333" fill-rule="nonzero" /></symbol><symbol  viewBox="0 0 18.002 18.002" id="icon-oppo_nor"><path d="M9 0a9 9 0 1 1 0 18.001A9 9 0 0 1 9 0Zm.57 7.523-.065.001a.815.815 0 0 1-.17 0c-.192-.02-.25.078-.246.275.008.396.002.792.002 1.188v1.163c.003.34.031.333.406.333.18 0 .263-.066.255-.27-.01-.25-.004-.497-.002-.746.002-.251.008-.247.225-.155.89.382 1.78.392 2.658-.04.277-.137.539-.34.533-.717-.004-.368-.264-.55-.533-.695-.663-.356-1.36-.36-2.067-.235-.078.012-.22.014-.2.118.021.113.146.03.222.02.52-.072 1.029-.048 1.485.273.416.294.416.746.012 1.06-.527.407-1.606.417-2.142.021a.424.424 0 0 1-.195-.383c.008-.312-.004-.623.004-.933.004-.195-.058-.295-.247-.277ZM5.18 7.52H5.1c-.198-.003-.266.08-.262.288.008.396.002.792.002 1.188 0 .41.002.821-.002 1.233-.002.177.064.262.229.25a.964.964 0 0 1 .17 0c.178.016.244-.073.238-.262-.008-.263.002-.528-.002-.791-.002-.173.042-.22.2-.14l.207.092c.69.28 1.398.272 2.114.06.25-.075.484-.191.681-.378.304-.287.31-.669.016-.968a1.507 1.507 0 0 0-.55-.348c-.587-.227-1.188-.235-1.794-.133-.087.015-.241.017-.223.119.02.12.156.032.239.022.5-.058.986-.036 1.426.265.45.308.462.808.004 1.091-.699.432-1.416.414-2.121-.002-.139-.082-.203-.205-.2-.381.009-.264.003-.529.003-.792 0-.418 0-.41-.376-.414Zm-1.03.36c-.62-.355-1.283-.373-1.958-.287-.336.043-.655.151-.942.352-.529.372-.529.882.004 1.248.456.313.972.371 1.47.405.513-.024.99-.086 1.429-.345.257-.153.5-.342.494-.7-.008-.345-.243-.528-.496-.672ZM15.5 7.555h-.18c-.608-.002-1.046.087-1.404.29-.277.158-.549.337-.547.733.002.386.268.574.545.705.894.424 1.798.424 2.692-.004.281-.135.539-.334.543-.71.002-.377-.266-.564-.539-.712a2.53 2.53 0 0 0-1.29-.302Zm-1.52.434c.655-.371 1.336-.374 1.993-.004.53.298.527.838.008 1.167-.31.197-.657.242-1.012.268-.348-.026-.693-.073-1.003-.276-.502-.33-.506-.861.014-1.155ZM2.481 7.731c.372-.01.726.068 1.043.283.448.304.45.78.01 1.1-.534.389-1.57.391-2.106.007-.462-.331-.452-.813.027-1.123.315-.205.663-.275 1.026-.267Z" fill="#333" fill-rule="evenodd" /></symbol><symbol  viewBox="0 0 18.001 18.001" id="icon-ry_nor"><path d="M9 0a9 9 0 1 1 0 18.001A9 9 0 0 1 9 0ZM5.76 7.376c-.812 0-1.47.729-1.47 1.627 0 .9.658 1.628 1.47 1.628.812 0 1.47-.729 1.47-1.628 0-.898-.658-1.627-1.47-1.627Zm6.408-.005c-.812 0-1.47.728-1.47 1.627s.658 1.627 1.47 1.627c.812 0 1.47-.728 1.47-1.627s-.658-1.627-1.47-1.627Zm-10.69.036v3.182h.508v-1.32h1.132l.001 1.318h.506V7.41H3.1v1.297H1.985V7.409l-.509-.002Zm13.996.001h-1.162v3.18h.493l.003-1.395 1.057 1.394h.66s-.782-1.043-.855-1.167c.122-.04.634-.287.634-.976 0-.781-.752-1.036-.83-1.036Zm-7.178 0h-.39l-.002 3.157h.488l.005-1.973 1.247 1.974h.39V7.408l-.507.001v1.95l-1.231-1.95Zm-2.523.523c.526 0 .952.472.952 1.054 0 .583-.426 1.055-.952 1.055s-.952-.472-.952-1.055c0-.582.426-1.054.952-1.054Zm6.408-.005c.526 0 .952.472.952 1.054 0 .582-.426 1.054-.952 1.054s-.953-.472-.953-1.054c0-.582.427-1.054.953-1.054Zm3.283.05c.127 0 .33.188.33.468 0 .269-.23.48-.334.48l-.648.001v-.949h.652Z" fill="#333" fill-rule="evenodd" /></symbol><symbol  viewBox="0 0 18.002 18.002" id="icon-vivo_nor"><path d="M9 0a9 9 0 1 1 0 18.001A9 9 0 0 1 9 0ZM6.68 7.954a.328.328 0 0 0-.321.335v2.622a.33.33 0 0 0 .321.336c.177 0 .32-.15.32-.336V8.29a.33.33 0 0 0-.32-.335Zm4.997-.002a.32.32 0 0 0-.263.147l-1.449 2.266c-.088.143-.185.203-.28.203-.112-.002-.192-.065-.282-.203L7.95 8.103a.31.31 0 0 0-.263-.147.31.31 0 0 0-.181.06.357.357 0 0 0-.082.474l1.454 2.259c.201.315.494.498.808.498.321.002.572-.15.802-.498l1.452-2.263a.361.361 0 0 0 .056-.193.347.347 0 0 0-.138-.28.31.31 0 0 0-.181-.061ZM1.676 7.95a.3.3 0 0 0-.181.06.358.358 0 0 0-.082.474l1.456 2.265c.2.315.496.498.808.498.321.002.572-.15.803-.498l1.453-2.263a.36.36 0 0 0 .056-.193.35.35 0 0 0-.138-.28.31.31 0 0 0-.181-.061.32.32 0 0 0-.264.147L3.954 10.36c-.088.142-.185.203-.28.203-.112-.002-.192-.065-.283-.203L1.94 8.099a.312.312 0 0 0-.263-.149Zm13.438-.05h-1.61c-.843 0-1.528.73-1.528 1.623v.084c0 .896.685 1.624 1.527 1.624h1.611c.842 0 1.527-.73 1.527-1.624v-.084C16.64 8.63 15.956 7.9 15.114 7.9Zm0 .633c.51.002.925.444.925.99v.084c-.002.547-.416.989-.925.991h-1.61c-.511-.002-.925-.444-.925-.99v-.085c0-.546.416-.988.924-.99h1.611ZM6.682 6.755a.124.124 0 0 0-.094.04l-.336.355a.142.142 0 0 0 0 .193l.336.356c.024.026.058.04.092.04a.122.122 0 0 0 .09-.04l.336-.356a.142.142 0 0 0 0-.193l-.335-.355a.126.126 0 0 0-.089-.04Z" fill="#333" fill-rule="evenodd" /></symbol><symbol  viewBox="0 0 18.001 18.001" id="icon-xm_nor"><path d="M9 0a9 9 0 1 1 0 18.001A9 9 0 0 1 9 0Zm.347 5.427H3.992a.079.079 0 0 0-.077.077v6.633c0 .04.032.077.077.077h1.43c.041 0 .077-.036.077-.077V6.93c0-.04.032-.076.077-.076h3.078c.576 0 1.049.482 1.049 1.076v4.207c0 .04.031.077.076.077h1.409c.04 0 .076-.036.076-.077V7.385c-.009-1.08-.864-1.958-1.917-1.958Zm4.663 0h-1.432a.079.079 0 0 0-.076.077v6.633c0 .04.031.077.076.077h1.432c.04 0 .076-.036.076-.077V5.504a.083.083 0 0 0-.022-.054.075.075 0 0 0-.054-.023ZM8.29 8.07H6.858a.079.079 0 0 0-.076.077v3.987c0 .04.031.076.076.076H8.29c.018 0 .04-.009.054-.022a.083.083 0 0 0 .022-.054V8.146a.083.083 0 0 0-.022-.054.083.083 0 0 0-.054-.023Z" fill="#333" fill-rule="evenodd" /></symbol>`,t.insertBefore(n,t.lastChild)};document.readyState==="loading"?document.addEventListener("DOMContentLoaded",e):e()}/*!
  * shared v11.1.3
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */const Fx=(e,t,n)=>Tx({l:e,k:t,s:n}),Tx=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),K=e=>typeof e=="number"&&isFinite(e),Ix=e=>J3(e)==="[object Date]",h6=e=>J3(e)==="[object RegExp]",X3=e=>N(e)&&Object.keys(e).length===0,z2=Object.assign,Ox=Object.create,X=(e=null)=>Ox(e);let $6;const f6=()=>$6||($6=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:X());function m6(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const Nx=Object.prototype.hasOwnProperty;function H2(e,t){return Nx.call(e,t)}const _2=Array.isArray,G=e=>typeof e=="function",B=e=>typeof e=="string",e2=e=>typeof e=="boolean",q=e=>e!==null&&typeof e=="object",Px=e=>q(e)&&G(e.then)&&G(e.catch),en=Object.prototype.toString,J3=e=>en.call(e),N=e=>J3(e)==="[object Object]",Dx=e=>e==null?"":_2(e)||N(e)&&e.toString===en?JSON.stringify(e,null,2):String(e);function Q3(e,t=""){return e.reduce((n,o,r)=>r===0?n+o:n+t+o,"")}function Rx(e,t){typeof console<"u"&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}/*!
  * message-compiler v11.1.3
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */function Ux(e,t,n){return{line:e,column:t,offset:n}}function A0(e,t,n){const o={start:e,end:t};return n!=null&&(o.source=n),o}const T={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16},Gx=17;function p0(e,t,n={}){const{domain:o,messages:r,args:s}=n,u=e,h=new SyntaxError(String(u));return h.code=e,t&&(h.location=t),h.domain=o,h}function Wx(e){throw e}const $2=" ",Zx="\r",j=`
`,qx=String.fromCharCode(8232),jx=String.fromCharCode(8233);function Yx(e){const t=e;let n=0,o=1,r=1,s=0;const u=M=>t[M]===Zx&&t[M+1]===j,h=M=>t[M]===j,d=M=>t[M]===jx,m=M=>t[M]===qx,z=M=>u(M)||h(M)||d(M)||m(M),x=()=>n,L=()=>o,y=()=>r,V=()=>s,A=M=>u(M)||d(M)||m(M)?j:t[M],E=()=>A(n),$=()=>A(n+s);function g(){return s=0,z(n)&&(o++,r=0),u(n)&&n++,n++,r++,t[n]}function H(){return u(n+s)&&s++,s++,t[n+s]}function v(){n=0,o=1,r=1,s=0}function k(M=0){s=M}function b(){const M=n+s;for(;M!==n;)g();s=0}return{index:x,line:L,column:y,peekOffset:V,charAt:A,currentChar:E,currentPeek:$,next:g,peek:H,reset:v,resetPeek:k,skipToPeek:b}}const g2=void 0,Kx=".",v6="'",Xx="tokenizer";function Jx(e,t={}){const n=t.location!==!1,o=Yx(e),r=()=>o.index(),s=()=>Ux(o.line(),o.column(),o.index()),u=s(),h=r(),d={currentType:13,offset:h,startLoc:u,endLoc:u,lastType:13,lastOffset:h,lastStartLoc:u,lastEndLoc:u,braceNest:0,inLinked:!1,text:""},m=()=>d,{onError:z}=t;function x(p,_,w,...S){const R=m();if(_.column+=w,_.offset+=w,z){const F=n?A0(R.startLoc,_):null,f=p0(p,F,{domain:Xx,args:S});z(f)}}function L(p,_,w){p.endLoc=s(),p.currentType=_;const S={type:_};return n&&(S.loc=A0(p.startLoc,p.endLoc)),w!=null&&(S.value=w),S}const y=p=>L(p,13);function V(p,_){return p.currentChar()===_?(p.next(),_):(x(T.EXPECTED_TOKEN,s(),0,_),"")}function A(p){let _="";for(;p.currentPeek()===$2||p.currentPeek()===j;)_+=p.currentPeek(),p.peek();return _}function E(p){const _=A(p);return p.skipToPeek(),_}function $(p){if(p===g2)return!1;const _=p.charCodeAt(0);return _>=97&&_<=122||_>=65&&_<=90||_===95}function g(p){if(p===g2)return!1;const _=p.charCodeAt(0);return _>=48&&_<=57}function H(p,_){const{currentType:w}=_;if(w!==2)return!1;A(p);const S=$(p.currentPeek());return p.resetPeek(),S}function v(p,_){const{currentType:w}=_;if(w!==2)return!1;A(p);const S=p.currentPeek()==="-"?p.peek():p.currentPeek(),R=g(S);return p.resetPeek(),R}function k(p,_){const{currentType:w}=_;if(w!==2)return!1;A(p);const S=p.currentPeek()===v6;return p.resetPeek(),S}function b(p,_){const{currentType:w}=_;if(w!==7)return!1;A(p);const S=p.currentPeek()===".";return p.resetPeek(),S}function M(p,_){const{currentType:w}=_;if(w!==8)return!1;A(p);const S=$(p.currentPeek());return p.resetPeek(),S}function O(p,_){const{currentType:w}=_;if(!(w===7||w===11))return!1;A(p);const S=p.currentPeek()===":";return p.resetPeek(),S}function P(p,_){const{currentType:w}=_;if(w!==9)return!1;const S=()=>{const F=p.currentPeek();return F==="{"?$(p.peek()):F==="@"||F==="|"||F===":"||F==="."||F===$2||!F?!1:F===j?(p.peek(),S()):l2(p,!1)},R=S();return p.resetPeek(),R}function r2(p){A(p);const _=p.currentPeek()==="|";return p.resetPeek(),_}function l2(p,_=!0){const w=(R=!1,F="")=>{const f=p.currentPeek();return f==="{"||f==="@"||!f?R:f==="|"?!(F===$2||F===j):f===$2?(p.peek(),w(!0,$2)):f===j?(p.peek(),w(!0,j)):!0},S=w();return _&&p.resetPeek(),S}function W(p,_){const w=p.currentChar();return w===g2?g2:_(w)?(p.next(),w):null}function V2(p){const _=p.charCodeAt(0);return _>=97&&_<=122||_>=65&&_<=90||_>=48&&_<=57||_===95||_===36}function d0(p){return W(p,V2)}function h0(p){const _=p.charCodeAt(0);return _>=97&&_<=122||_>=65&&_<=90||_>=48&&_<=57||_===95||_===36||_===45}function $0(p){return W(p,h0)}function f0(p){const _=p.charCodeAt(0);return _>=48&&_<=57}function m0(p){return W(p,f0)}function h2(p){const _=p.charCodeAt(0);return _>=48&&_<=57||_>=65&&_<=70||_>=97&&_<=102}function K2(p){return W(p,h2)}function X2(p){let _="",w="";for(;_=m0(p);)w+=_;return w}function v0(p){let _="";for(;;){const w=p.currentChar();if(w==="{"||w==="}"||w==="@"||w==="|"||!w)break;if(w===$2||w===j)if(l2(p))_+=w,p.next();else{if(r2(p))break;_+=w,p.next()}else _+=w,p.next()}return _}function g0(p){E(p);let _="",w="";for(;_=$0(p);)w+=_;return p.currentChar()===g2&&x(T.UNTERMINATED_CLOSING_BRACE,s(),0),w}function w0(p){E(p);let _="";return p.currentChar()==="-"?(p.next(),_+=`-${X2(p)}`):_+=X2(p),p.currentChar()===g2&&x(T.UNTERMINATED_CLOSING_BRACE,s(),0),_}function l6(p){return p!==v6&&p!==j}function z0(p){E(p),V(p,"'");let _="",w="";for(;_=W(p,l6);)_==="\\"?w+=x0(p):w+=_;const S=p.currentChar();return S===j||S===g2?(x(T.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,s(),0),S===j&&(p.next(),V(p,"'")),w):(V(p,"'"),w)}function x0(p){const _=p.currentChar();switch(_){case"\\":case"'":return p.next(),`\\${_}`;case"u":return J2(p,_,4);case"U":return J2(p,_,6);default:return x(T.UNKNOWN_ESCAPE_SEQUENCE,s(),0,_),""}}function J2(p,_,w){V(p,_);let S="";for(let R=0;R<w;R++){const F=K2(p);if(!F){x(T.INVALID_UNICODE_ESCAPE_SEQUENCE,s(),0,`\\${_}${S}${p.currentChar()}`);break}S+=F}return`\\${_}${S}`}function C0(p){return p!=="{"&&p!=="}"&&p!==$2&&p!==j}function M0(p){E(p);let _="",w="";for(;_=W(p,C0);)w+=_;return w}function L0(p){let _="",w="";for(;_=d0(p);)w+=_;return w}function H0(p){const _=w=>{const S=p.currentChar();return S==="{"||S==="@"||S==="|"||S==="("||S===")"||!S||S===$2?w:(w+=S,p.next(),_(w))};return _("")}function N2(p){E(p);const _=V(p,"|");return E(p),_}function b2(p,_){let w=null;switch(p.currentChar()){case"{":return _.braceNest>=1&&x(T.NOT_ALLOW_NEST_PLACEHOLDER,s(),0),p.next(),w=L(_,2,"{"),E(p),_.braceNest++,w;case"}":return _.braceNest>0&&_.currentType===2&&x(T.EMPTY_PLACEHOLDER,s(),0),p.next(),w=L(_,3,"}"),_.braceNest--,_.braceNest>0&&E(p),_.inLinked&&_.braceNest===0&&(_.inLinked=!1),w;case"@":return _.braceNest>0&&x(T.UNTERMINATED_CLOSING_BRACE,s(),0),w=y2(p,_)||y(_),_.braceNest=0,w;default:{let R=!0,F=!0,f=!0;if(r2(p))return _.braceNest>0&&x(T.UNTERMINATED_CLOSING_BRACE,s(),0),w=L(_,1,N2(p)),_.braceNest=0,_.inLinked=!1,w;if(_.braceNest>0&&(_.currentType===4||_.currentType===5||_.currentType===6))return x(T.UNTERMINATED_CLOSING_BRACE,s(),0),_.braceNest=0,P2(p,_);if(R=H(p,_))return w=L(_,4,g0(p)),E(p),w;if(F=v(p,_))return w=L(_,5,w0(p)),E(p),w;if(f=k(p,_))return w=L(_,6,z0(p)),E(p),w;if(!R&&!F&&!f)return w=L(_,12,M0(p)),x(T.INVALID_TOKEN_IN_PLACEHOLDER,s(),0,w.value),E(p),w;break}}return w}function y2(p,_){const{currentType:w}=_;let S=null;const R=p.currentChar();switch((w===7||w===8||w===11||w===9)&&(R===j||R===$2)&&x(T.INVALID_LINKED_FORMAT,s(),0),R){case"@":return p.next(),S=L(_,7,"@"),_.inLinked=!0,S;case".":return E(p),p.next(),L(_,8,".");case":":return E(p),p.next(),L(_,9,":");default:return r2(p)?(S=L(_,1,N2(p)),_.braceNest=0,_.inLinked=!1,S):b(p,_)||O(p,_)?(E(p),y2(p,_)):M(p,_)?(E(p),L(_,11,L0(p))):P(p,_)?(E(p),R==="{"?b2(p,_)||S:L(_,10,H0(p))):(w===7&&x(T.INVALID_LINKED_FORMAT,s(),0),_.braceNest=0,_.inLinked=!1,P2(p,_))}}function P2(p,_){let w={type:13};if(_.braceNest>0)return b2(p,_)||y(_);if(_.inLinked)return y2(p,_)||y(_);switch(p.currentChar()){case"{":return b2(p,_)||y(_);case"}":return x(T.UNBALANCED_CLOSING_BRACE,s(),0),p.next(),L(_,3,"}");case"@":return y2(p,_)||y(_);default:{if(r2(p))return w=L(_,1,N2(p)),_.braceNest=0,_.inLinked=!1,w;if(l2(p))return L(_,0,v0(p));break}}return w}function k0(){const{currentType:p,offset:_,startLoc:w,endLoc:S}=d;return d.lastType=p,d.lastOffset=_,d.lastStartLoc=w,d.lastEndLoc=S,d.offset=r(),d.startLoc=s(),o.currentChar()===g2?L(d,13):P2(o,d)}return{nextToken:k0,currentOffset:r,currentPosition:s,context:m}}const Qx="parser",eC=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function tC(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const o=parseInt(t||n,16);return o<=55295||o>=57344?String.fromCodePoint(o):"�"}}}function nC(e={}){const t=e.location!==!1,{onError:n}=e;function o($,g,H,v,...k){const b=$.currentPosition();if(b.offset+=v,b.column+=v,n){const M=t?A0(H,b):null,O=p0(g,M,{domain:Qx,args:k});n(O)}}function r($,g,H){const v={type:$};return t&&(v.start=g,v.end=g,v.loc={start:H,end:H}),v}function s($,g,H,v){t&&($.end=g,$.loc&&($.loc.end=H))}function u($,g){const H=$.context(),v=r(3,H.offset,H.startLoc);return v.value=g,s(v,$.currentOffset(),$.currentPosition()),v}function h($,g){const H=$.context(),{lastOffset:v,lastStartLoc:k}=H,b=r(5,v,k);return b.index=parseInt(g,10),$.nextToken(),s(b,$.currentOffset(),$.currentPosition()),b}function d($,g){const H=$.context(),{lastOffset:v,lastStartLoc:k}=H,b=r(4,v,k);return b.key=g,$.nextToken(),s(b,$.currentOffset(),$.currentPosition()),b}function m($,g){const H=$.context(),{lastOffset:v,lastStartLoc:k}=H,b=r(9,v,k);return b.value=g.replace(eC,tC),$.nextToken(),s(b,$.currentOffset(),$.currentPosition()),b}function z($){const g=$.nextToken(),H=$.context(),{lastOffset:v,lastStartLoc:k}=H,b=r(8,v,k);return g.type!==11?(o($,T.UNEXPECTED_EMPTY_LINKED_MODIFIER,H.lastStartLoc,0),b.value="",s(b,v,k),{nextConsumeToken:g,node:b}):(g.value==null&&o($,T.UNEXPECTED_LEXICAL_ANALYSIS,H.lastStartLoc,0,u2(g)),b.value=g.value||"",s(b,$.currentOffset(),$.currentPosition()),{node:b})}function x($,g){const H=$.context(),v=r(7,H.offset,H.startLoc);return v.value=g,s(v,$.currentOffset(),$.currentPosition()),v}function L($){const g=$.context(),H=r(6,g.offset,g.startLoc);let v=$.nextToken();if(v.type===8){const k=z($);H.modifier=k.node,v=k.nextConsumeToken||$.nextToken()}switch(v.type!==9&&o($,T.UNEXPECTED_LEXICAL_ANALYSIS,g.lastStartLoc,0,u2(v)),v=$.nextToken(),v.type===2&&(v=$.nextToken()),v.type){case 10:v.value==null&&o($,T.UNEXPECTED_LEXICAL_ANALYSIS,g.lastStartLoc,0,u2(v)),H.key=x($,v.value||"");break;case 4:v.value==null&&o($,T.UNEXPECTED_LEXICAL_ANALYSIS,g.lastStartLoc,0,u2(v)),H.key=d($,v.value||"");break;case 5:v.value==null&&o($,T.UNEXPECTED_LEXICAL_ANALYSIS,g.lastStartLoc,0,u2(v)),H.key=h($,v.value||"");break;case 6:v.value==null&&o($,T.UNEXPECTED_LEXICAL_ANALYSIS,g.lastStartLoc,0,u2(v)),H.key=m($,v.value||"");break;default:{o($,T.UNEXPECTED_EMPTY_LINKED_KEY,g.lastStartLoc,0);const k=$.context(),b=r(7,k.offset,k.startLoc);return b.value="",s(b,k.offset,k.startLoc),H.key=b,s(H,k.offset,k.startLoc),{nextConsumeToken:v,node:H}}}return s(H,$.currentOffset(),$.currentPosition()),{node:H}}function y($){const g=$.context(),H=g.currentType===1?$.currentOffset():g.offset,v=g.currentType===1?g.endLoc:g.startLoc,k=r(2,H,v);k.items=[];let b=null;do{const P=b||$.nextToken();switch(b=null,P.type){case 0:P.value==null&&o($,T.UNEXPECTED_LEXICAL_ANALYSIS,g.lastStartLoc,0,u2(P)),k.items.push(u($,P.value||""));break;case 5:P.value==null&&o($,T.UNEXPECTED_LEXICAL_ANALYSIS,g.lastStartLoc,0,u2(P)),k.items.push(h($,P.value||""));break;case 4:P.value==null&&o($,T.UNEXPECTED_LEXICAL_ANALYSIS,g.lastStartLoc,0,u2(P)),k.items.push(d($,P.value||""));break;case 6:P.value==null&&o($,T.UNEXPECTED_LEXICAL_ANALYSIS,g.lastStartLoc,0,u2(P)),k.items.push(m($,P.value||""));break;case 7:{const r2=L($);k.items.push(r2.node),b=r2.nextConsumeToken||null;break}}}while(g.currentType!==13&&g.currentType!==1);const M=g.currentType===1?g.lastOffset:$.currentOffset(),O=g.currentType===1?g.lastEndLoc:$.currentPosition();return s(k,M,O),k}function V($,g,H,v){const k=$.context();let b=v.items.length===0;const M=r(1,g,H);M.cases=[],M.cases.push(v);do{const O=y($);b||(b=O.items.length===0),M.cases.push(O)}while(k.currentType!==13);return b&&o($,T.MUST_HAVE_MESSAGES_IN_PLURAL,H,0),s(M,$.currentOffset(),$.currentPosition()),M}function A($){const g=$.context(),{offset:H,startLoc:v}=g,k=y($);return g.currentType===13?k:V($,H,v,k)}function E($){const g=Jx($,z2({},e)),H=g.context(),v=r(0,H.offset,H.startLoc);return t&&v.loc&&(v.loc.source=$),v.body=A(g),e.onCacheKey&&(v.cacheKey=e.onCacheKey($)),H.currentType!==13&&o(g,T.UNEXPECTED_LEXICAL_ANALYSIS,H.lastStartLoc,0,$[H.offset]||""),s(v,g.currentOffset(),g.currentPosition()),v}return{parse:E}}function u2(e){if(e.type===13)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function oC(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:s=>(n.helpers.add(s),s)}}function g6(e,t){for(let n=0;n<e.length;n++)e6(e[n],t)}function e6(e,t){switch(e.type){case 1:g6(e.cases,t),t.helper("plural");break;case 2:g6(e.items,t);break;case 6:{e6(e.key,t),t.helper("linked"),t.helper("type");break}case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named");break}}function rC(e,t={}){const n=oC(e);n.helper("normalize"),e.body&&e6(e.body,n);const o=n.context();e.helpers=Array.from(o.helpers)}function sC(e){const t=e.body;return t.type===2?w6(t):t.cases.forEach(n=>w6(n)),e}function w6(e){if(e.items.length===1){const t=e.items[0];(t.type===3||t.type===9)&&(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const o=e.items[n];if(!(o.type===3||o.type===9)||o.value==null)break;t.push(o.value)}if(t.length===e.items.length){e.static=Q3(t);for(let n=0;n<e.items.length;n++){const o=e.items[n];(o.type===3||o.type===9)&&delete o.value}}}}function S2(e){switch(e.t=e.type,e.type){case 0:{const t=e;S2(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let o=0;o<n.length;o++)S2(n[o]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let o=0;o<n.length;o++)S2(n[o]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;S2(t.key),t.k=t.key,delete t.key,t.modifier&&(S2(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}}delete e.type}function aC(e,t){const{sourceMap:n,filename:o,breakLineCode:r,needIndent:s}=t,u=t.location!==!1,h={filename:o,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:r,needIndent:s,indentLevel:0};u&&e.loc&&(h.source=e.loc.source);const d=()=>h;function m(E,$){h.code+=E}function z(E,$=!0){const g=$?r:"";m(s?g+"  ".repeat(E):g)}function x(E=!0){const $=++h.indentLevel;E&&z($)}function L(E=!0){const $=--h.indentLevel;E&&z($)}function y(){z(h.indentLevel)}return{context:d,push:m,indent:x,deindent:L,newline:y,helper:E=>`_${E}`,needIndent:()=>h.needIndent}}function cC(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),O2(e,t.key),t.modifier?(e.push(", "),O2(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}function lC(e,t){const{helper:n,needIndent:o}=e;e.push(`${n("normalize")}([`),e.indent(o());const r=t.items.length;for(let s=0;s<r&&(O2(e,t.items[s]),s!==r-1);s++)e.push(", ");e.deindent(o()),e.push("])")}function iC(e,t){const{helper:n,needIndent:o}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(o());const r=t.cases.length;for(let s=0;s<r&&(O2(e,t.cases[s]),s!==r-1);s++)e.push(", ");e.deindent(o()),e.push("])")}}function uC(e,t){t.body?O2(e,t.body):e.push("null")}function O2(e,t){const{helper:n}=e;switch(t.type){case 0:uC(e,t);break;case 1:iC(e,t);break;case 2:lC(e,t);break;case 6:cC(e,t);break;case 8:e.push(JSON.stringify(t.value),t);break;case 7:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t);break;case 9:e.push(JSON.stringify(t.value),t);break;case 3:e.push(JSON.stringify(t.value),t);break}}const pC=(e,t={})=>{const n=B(t.mode)?t.mode:"normal",o=B(t.filename)?t.filename:"message.intl",r=!!t.sourceMap,s=t.breakLineCode!=null?t.breakLineCode:n==="arrow"?";":`
`,u=t.needIndent?t.needIndent:n!=="arrow",h=e.helpers||[],d=aC(e,{mode:n,filename:o,sourceMap:r,breakLineCode:s,needIndent:u});d.push(n==="normal"?"function __msg__ (ctx) {":"(ctx) => {"),d.indent(u),h.length>0&&(d.push(`const { ${Q3(h.map(x=>`${x}: _${x}`),", ")} } = ctx`),d.newline()),d.push("return "),O2(d,e),d.deindent(u),d.push("}"),delete e.helpers;const{code:m,map:z}=d.context();return{ast:e,code:m,map:z?z.toJSON():void 0}};function _C(e,t={}){const n=z2({},t),o=!!n.jit,r=!!n.minify,s=n.optimize==null?!0:n.optimize,h=nC(n).parse(e);return o?(s&&sC(h),r&&S2(h),{ast:h,code:""}):(rC(h,n),pC(h,n))}/*!
  * core-base v11.1.3
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */function dC(){typeof __INTLIFY_PROD_DEVTOOLS__!="boolean"&&(f6().__INTLIFY_PROD_DEVTOOLS__=!1),typeof __INTLIFY_DROP_MESSAGE_COMPILER__!="boolean"&&(f6().__INTLIFY_DROP_MESSAGE_COMPILER__=!1)}function d2(e){return q(e)&&t6(e)===0&&(H2(e,"b")||H2(e,"body"))}const tn=["b","body"];function hC(e){return x2(e,tn)}const nn=["c","cases"];function $C(e){return x2(e,nn,[])}const on=["s","static"];function fC(e){return x2(e,on)}const rn=["i","items"];function mC(e){return x2(e,rn,[])}const sn=["t","type"];function t6(e){return x2(e,sn)}const an=["v","value"];function e0(e,t){const n=x2(e,an);if(n!=null)return n;throw G2(t)}const cn=["m","modifier"];function vC(e){return x2(e,cn)}const ln=["k","key"];function gC(e){const t=x2(e,ln);if(t)return t;throw G2(6)}function x2(e,t,n){for(let o=0;o<t.length;o++){const r=t[o];if(H2(e,r)&&e[r]!=null)return e[r]}return n}const un=[...tn,...nn,...on,...rn,...ln,...cn,...an,...sn];function G2(e){return new Error(`unhandled node type: ${e}`)}function V0(e){return n=>wC(n,e)}function wC(e,t){const n=hC(t);if(n==null)throw G2(0);if(t6(n)===1){const s=$C(n);return e.plural(s.reduce((u,h)=>[...u,z6(e,h)],[]))}else return z6(e,n)}function z6(e,t){const n=fC(t);if(n!=null)return e.type==="text"?n:e.normalize([n]);{const o=mC(t).reduce((r,s)=>[...r,E0(e,s)],[]);return e.normalize(o)}}function E0(e,t){const n=t6(t);switch(n){case 3:return e0(t,n);case 9:return e0(t,n);case 4:{const o=t;if(H2(o,"k")&&o.k)return e.interpolate(e.named(o.k));if(H2(o,"key")&&o.key)return e.interpolate(e.named(o.key));throw G2(n)}case 5:{const o=t;if(H2(o,"i")&&K(o.i))return e.interpolate(e.list(o.i));if(H2(o,"index")&&K(o.index))return e.interpolate(e.list(o.index));throw G2(n)}case 6:{const o=t,r=vC(o),s=gC(o);return e.linked(E0(e,s),r?E0(e,r):void 0,e.type)}case 7:return e0(t,n);case 8:return e0(t,n);default:throw new Error(`unhandled node on format message part: ${n}`)}}const zC=e=>e;let t0=X();function xC(e,t={}){let n=!1;const o=t.onError||Wx;return t.onError=r=>{n=!0,o(r)},{..._C(e,t),detectError:n}}function CC(e,t){if(!__INTLIFY_DROP_MESSAGE_COMPILER__&&B(e)){e2(t.warnHtmlMessage)&&t.warnHtmlMessage;const o=(t.onCacheKey||zC)(e),r=t0[o];if(r)return r;const{ast:s,detectError:u}=xC(e,{...t,location:!1,jit:!0}),h=V0(s);return u?h:t0[o]=h}else{const n=e.cacheKey;if(n){const o=t0[n];return o||(t0[n]=V0(e))}else return V0(e)}}let W2=null;function MC(e){W2=e}function LC(e,t,n){W2&&W2.emit("i18n:init",{timestamp:Date.now(),i18n:e,version:t,meta:n})}const HC=kC("function:translate");function kC(e){return t=>W2&&W2.emit(e,t)}const m2={INVALID_ARGUMENT:Gx,INVALID_DATE_ARGUMENT:18,INVALID_ISO_DATE_ARGUMENT:19,NOT_SUPPORT_NON_STRING_MESSAGE:20,NOT_SUPPORT_LOCALE_PROMISE_VALUE:21,NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:22,NOT_SUPPORT_LOCALE_TYPE:23},VC=24;function v2(e){return p0(e,null,void 0)}function n6(e,t){return t.locale!=null?x6(t.locale):x6(e.locale)}let b0;function x6(e){if(B(e))return e;if(G(e)){if(e.resolvedOnce&&b0!=null)return b0;if(e.constructor.name==="Function"){const t=e();if(Px(t))throw v2(m2.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return b0=t}else throw v2(m2.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}else throw v2(m2.NOT_SUPPORT_LOCALE_TYPE)}function bC(e,t,n){return[...new Set([n,..._2(t)?t:q(t)?Object.keys(t):B(t)?[t]:[n]])]}function pn(e,t,n){const o=B(n)?n:Z2,r=e;r.__localeChainCache||(r.__localeChainCache=new Map);let s=r.__localeChainCache.get(o);if(!s){s=[];let u=[n];for(;_2(u);)u=C6(s,u,t);const h=_2(t)||!N(t)?t:t.default?t.default:null;u=B(h)?[h]:h,_2(u)&&C6(s,u,!1),r.__localeChainCache.set(o,s)}return s}function C6(e,t,n){let o=!0;for(let r=0;r<t.length&&e2(o);r++){const s=t[r];B(s)&&(o=yC(e,t[r],n))}return o}function yC(e,t,n){let o;const r=t.split("-");do{const s=r.join("-");o=AC(e,s,n),r.splice(-1,1)}while(r.length&&o===!0);return o}function AC(e,t,n){let o=!1;if(!e.includes(t)&&(o=!0,t)){o=t[t.length-1]!=="!";const r=t.replace(/!/g,"");e.push(r),(_2(n)||N(n))&&n[r]&&(o=n[r])}return o}const C2=[];C2[0]={w:[0],i:[3,0],"[":[4],o:[7]};C2[1]={w:[1],".":[2],"[":[4],o:[7]};C2[2]={w:[2],i:[3,0],0:[3,0]};C2[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]};C2[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]};C2[5]={"'":[4,0],o:8,l:[5,0]};C2[6]={'"':[4,0],o:8,l:[6,0]};const EC=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function SC(e){return EC.test(e)}function BC(e){const t=e.charCodeAt(0),n=e.charCodeAt(e.length-1);return t===n&&(t===34||t===39)?e.slice(1,-1):e}function FC(e){if(e==null)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function TC(e){const t=e.trim();return e.charAt(0)==="0"&&isNaN(parseInt(e))?!1:SC(t)?BC(t):"*"+t}function IC(e){const t=[];let n=-1,o=0,r=0,s,u,h,d,m,z,x;const L=[];L[0]=()=>{u===void 0?u=h:u+=h},L[1]=()=>{u!==void 0&&(t.push(u),u=void 0)},L[2]=()=>{L[0](),r++},L[3]=()=>{if(r>0)r--,o=4,L[0]();else{if(r=0,u===void 0||(u=TC(u),u===!1))return!1;L[1]()}};function y(){const V=e[n+1];if(o===5&&V==="'"||o===6&&V==='"')return n++,h="\\"+V,L[0](),!0}for(;o!==null;)if(n++,s=e[n],!(s==="\\"&&y())){if(d=FC(s),x=C2[o],m=x[d]||x.l||8,m===8||(o=m[0],m[1]!==void 0&&(z=L[m[1]],z&&(h=s,z()===!1))))return;if(o===7)return t}}const M6=new Map;function OC(e,t){return q(e)?e[t]:null}function NC(e,t){if(!q(e))return null;let n=M6.get(t);if(n||(n=IC(t),n&&M6.set(t,n)),!n)return null;const o=n.length;let r=e,s=0;for(;s<o;){const u=n[s];if(un.includes(u)&&d2(r))return null;const h=r[u];if(h===void 0||G(r))return null;r=h,s++}return r}const PC="11.1.3",_0=-1,Z2="en-US",L6="",H6=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;function DC(){return{upper:(e,t)=>t==="text"&&B(e)?e.toUpperCase():t==="vnode"&&q(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>t==="text"&&B(e)?e.toLowerCase():t==="vnode"&&q(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>t==="text"&&B(e)?H6(e):t==="vnode"&&q(e)&&"__v_isVNode"in e?H6(e.children):e}}let _n;function RC(e){_n=e}let dn;function UC(e){dn=e}let hn;function GC(e){hn=e}let $n=null;const WC=e=>{$n=e},ZC=()=>$n;let fn=null;const k6=e=>{fn=e},qC=()=>fn;let V6=0;function jC(e={}){const t=G(e.onWarn)?e.onWarn:Rx,n=B(e.version)?e.version:PC,o=B(e.locale)||G(e.locale)?e.locale:Z2,r=G(o)?Z2:o,s=_2(e.fallbackLocale)||N(e.fallbackLocale)||B(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:r,u=N(e.messages)?e.messages:y0(r),h=N(e.datetimeFormats)?e.datetimeFormats:y0(r),d=N(e.numberFormats)?e.numberFormats:y0(r),m=z2(X(),e.modifiers,DC()),z=e.pluralRules||X(),x=G(e.missing)?e.missing:null,L=e2(e.missingWarn)||h6(e.missingWarn)?e.missingWarn:!0,y=e2(e.fallbackWarn)||h6(e.fallbackWarn)?e.fallbackWarn:!0,V=!!e.fallbackFormat,A=!!e.unresolving,E=G(e.postTranslation)?e.postTranslation:null,$=N(e.processor)?e.processor:null,g=e2(e.warnHtmlMessage)?e.warnHtmlMessage:!0,H=!!e.escapeParameter,v=G(e.messageCompiler)?e.messageCompiler:_n,k=G(e.messageResolver)?e.messageResolver:dn||OC,b=G(e.localeFallbacker)?e.localeFallbacker:hn||bC,M=q(e.fallbackContext)?e.fallbackContext:void 0,O=e,P=q(O.__datetimeFormatters)?O.__datetimeFormatters:new Map,r2=q(O.__numberFormatters)?O.__numberFormatters:new Map,l2=q(O.__meta)?O.__meta:{};V6++;const W={version:n,cid:V6,locale:o,fallbackLocale:s,messages:u,modifiers:m,pluralRules:z,missing:x,missingWarn:L,fallbackWarn:y,fallbackFormat:V,unresolving:A,postTranslation:E,processor:$,warnHtmlMessage:g,escapeParameter:H,messageCompiler:v,messageResolver:k,localeFallbacker:b,fallbackContext:M,onWarn:t,__meta:l2};return W.datetimeFormats=h,W.numberFormats=d,W.__datetimeFormatters=P,W.__numberFormatters=r2,__INTLIFY_PROD_DEVTOOLS__&&LC(W,n,l2),W}const y0=e=>({[e]:X()});function o6(e,t,n,o,r){const{missing:s,onWarn:u}=e;if(s!==null){const h=s(e,n,t,r);return B(h)?h:t}else return t}function D2(e,t,n){const o=e;o.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}function YC(e,t){return e===t?!1:e.split("-")[0]===t.split("-")[0]}function KC(e,t){const n=t.indexOf(e);if(n===-1)return!1;for(let o=n+1;o<t.length;o++)if(YC(e,t[o]))return!0;return!1}function b6(e,...t){const{datetimeFormats:n,unresolving:o,fallbackLocale:r,onWarn:s,localeFallbacker:u}=e,{__datetimeFormatters:h}=e,[d,m,z,x]=S0(...t),L=e2(z.missingWarn)?z.missingWarn:e.missingWarn;e2(z.fallbackWarn)?z.fallbackWarn:e.fallbackWarn;const y=!!z.part,V=n6(e,z),A=u(e,r,V);if(!B(d)||d==="")return new Intl.DateTimeFormat(V,x).format(m);let E={},$,g=null;const H="datetime format";for(let b=0;b<A.length&&($=A[b],E=n[$]||{},g=E[d],!N(g));b++)o6(e,d,$,L,H);if(!N(g)||!B($))return o?_0:d;let v=`${$}__${d}`;X3(x)||(v=`${v}__${JSON.stringify(x)}`);let k=h.get(v);return k||(k=new Intl.DateTimeFormat($,z2({},g,x)),h.set(v,k)),y?k.formatToParts(m):k.format(m)}const mn=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function S0(...e){const[t,n,o,r]=e,s=X();let u=X(),h;if(B(t)){const d=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!d)throw v2(m2.INVALID_ISO_DATE_ARGUMENT);const m=d[3]?d[3].trim().startsWith("T")?`${d[1].trim()}${d[3].trim()}`:`${d[1].trim()}T${d[3].trim()}`:d[1].trim();h=new Date(m);try{h.toISOString()}catch{throw v2(m2.INVALID_ISO_DATE_ARGUMENT)}}else if(Ix(t)){if(isNaN(t.getTime()))throw v2(m2.INVALID_DATE_ARGUMENT);h=t}else if(K(t))h=t;else throw v2(m2.INVALID_ARGUMENT);return B(n)?s.key=n:N(n)&&Object.keys(n).forEach(d=>{mn.includes(d)?u[d]=n[d]:s[d]=n[d]}),B(o)?s.locale=o:N(o)&&(u=o),N(r)&&(u=r),[s.key||"",h,s,u]}function y6(e,t,n){const o=e;for(const r in n){const s=`${t}__${r}`;o.__datetimeFormatters.has(s)&&o.__datetimeFormatters.delete(s)}}function A6(e,...t){const{numberFormats:n,unresolving:o,fallbackLocale:r,onWarn:s,localeFallbacker:u}=e,{__numberFormatters:h}=e,[d,m,z,x]=B0(...t),L=e2(z.missingWarn)?z.missingWarn:e.missingWarn;e2(z.fallbackWarn)?z.fallbackWarn:e.fallbackWarn;const y=!!z.part,V=n6(e,z),A=u(e,r,V);if(!B(d)||d==="")return new Intl.NumberFormat(V,x).format(m);let E={},$,g=null;const H="number format";for(let b=0;b<A.length&&($=A[b],E=n[$]||{},g=E[d],!N(g));b++)o6(e,d,$,L,H);if(!N(g)||!B($))return o?_0:d;let v=`${$}__${d}`;X3(x)||(v=`${v}__${JSON.stringify(x)}`);let k=h.get(v);return k||(k=new Intl.NumberFormat($,z2({},g,x)),h.set(v,k)),y?k.formatToParts(m):k.format(m)}const vn=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function B0(...e){const[t,n,o,r]=e,s=X();let u=X();if(!K(t))throw v2(m2.INVALID_ARGUMENT);const h=t;return B(n)?s.key=n:N(n)&&Object.keys(n).forEach(d=>{vn.includes(d)?u[d]=n[d]:s[d]=n[d]}),B(o)?s.locale=o:N(o)&&(u=o),N(r)&&(u=r),[s.key||"",h,s,u]}function E6(e,t,n){const o=e;for(const r in n){const s=`${t}__${r}`;o.__numberFormatters.has(s)&&o.__numberFormatters.delete(s)}}const XC=e=>e,JC=e=>"",QC="text",eM=e=>e.length===0?"":Q3(e),tM=Dx;function S6(e,t){return e=Math.abs(e),t===2?e?e>1?1:0:1:e?Math.min(e,2):0}function nM(e){const t=K(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(K(e.named.count)||K(e.named.n))?K(e.named.count)?e.named.count:K(e.named.n)?e.named.n:t:t}function oM(e,t){t.count||(t.count=e),t.n||(t.n=e)}function rM(e={}){const t=e.locale,n=nM(e),o=q(e.pluralRules)&&B(t)&&G(e.pluralRules[t])?e.pluralRules[t]:S6,r=q(e.pluralRules)&&B(t)&&G(e.pluralRules[t])?S6:void 0,s=$=>$[o(n,$.length,r)],u=e.list||[],h=$=>u[$],d=e.named||X();K(e.pluralIndex)&&oM(n,d);const m=$=>d[$];function z($,g){const H=G(e.messages)?e.messages($,!!g):q(e.messages)?e.messages[$]:!1;return H||(e.parent?e.parent.message($):JC)}const x=$=>e.modifiers?e.modifiers[$]:XC,L=N(e.processor)&&G(e.processor.normalize)?e.processor.normalize:eM,y=N(e.processor)&&G(e.processor.interpolate)?e.processor.interpolate:tM,V=N(e.processor)&&B(e.processor.type)?e.processor.type:QC,E={list:h,named:m,plural:s,linked:($,...g)=>{const[H,v]=g;let k="text",b="";g.length===1?q(H)?(b=H.modifier||b,k=H.type||k):B(H)&&(b=H||b):g.length===2&&(B(H)&&(b=H||b),B(v)&&(k=v||k));const M=z($,!0)(E),O=k==="vnode"&&_2(M)&&b?M[0]:M;return b?x(b)(O,k):O},message:z,type:V,interpolate:y,normalize:L,values:z2(X(),u,d)};return E}const B6=()=>"",c2=e=>G(e);function F6(e,...t){const{fallbackFormat:n,postTranslation:o,unresolving:r,messageCompiler:s,fallbackLocale:u,messages:h}=e,[d,m]=F0(...t),z=e2(m.missingWarn)?m.missingWarn:e.missingWarn,x=e2(m.fallbackWarn)?m.fallbackWarn:e.fallbackWarn,L=e2(m.escapeParameter)?m.escapeParameter:e.escapeParameter,y=!!m.resolvedMessage,V=B(m.default)||e2(m.default)?e2(m.default)?s?d:()=>d:m.default:n?s?d:()=>d:null,A=n||V!=null&&(B(V)||G(V)),E=n6(e,m);L&&sM(m);let[$,g,H]=y?[d,E,h[E]||X()]:gn(e,d,E,u,x,z),v=$,k=d;if(!y&&!(B(v)||d2(v)||c2(v))&&A&&(v=V,k=v),!y&&(!(B(v)||d2(v)||c2(v))||!B(g)))return r?_0:d;let b=!1;const M=()=>{b=!0},O=c2(v)?v:wn(e,d,g,v,k,M);if(b)return v;const P=lM(e,g,H,m),r2=rM(P),l2=aM(e,O,r2),W=o?o(l2,d):l2;if(__INTLIFY_PROD_DEVTOOLS__){const V2={timestamp:Date.now(),key:B(d)?d:c2(v)?v.key:"",locale:g||(c2(v)?v.locale:""),format:B(v)?v:c2(v)?v.source:"",message:W};V2.meta=z2({},e.__meta,ZC()||{}),HC(V2)}return W}function sM(e){_2(e.list)?e.list=e.list.map(t=>B(t)?m6(t):t):q(e.named)&&Object.keys(e.named).forEach(t=>{B(e.named[t])&&(e.named[t]=m6(e.named[t]))})}function gn(e,t,n,o,r,s){const{messages:u,onWarn:h,messageResolver:d,localeFallbacker:m}=e,z=m(e,o,n);let x=X(),L,y=null;const V="translate";for(let A=0;A<z.length&&(L=z[A],x=u[L]||X(),(y=d(x,t))===null&&(y=x[t]),!(B(y)||d2(y)||c2(y)));A++)if(!KC(L,z)){const E=o6(e,t,L,s,V);E!==t&&(y=E)}return[y,L,x]}function wn(e,t,n,o,r,s){const{messageCompiler:u,warnHtmlMessage:h}=e;if(c2(o)){const m=o;return m.locale=m.locale||n,m.key=m.key||t,m}if(u==null){const m=()=>o;return m.locale=n,m.key=t,m}const d=u(o,cM(e,n,r,o,h,s));return d.locale=n,d.key=t,d.source=o,d}function aM(e,t,n){return t(n)}function F0(...e){const[t,n,o]=e,r=X();if(!B(t)&&!K(t)&&!c2(t)&&!d2(t))throw v2(m2.INVALID_ARGUMENT);const s=K(t)?String(t):(c2(t),t);return K(n)?r.plural=n:B(n)?r.default=n:N(n)&&!X3(n)?r.named=n:_2(n)&&(r.list=n),K(o)?r.plural=o:B(o)?r.default=o:N(o)&&z2(r,o),[s,r]}function cM(e,t,n,o,r,s){return{locale:t,key:n,warnHtmlMessage:r,onError:u=>{throw s&&s(u),u},onCacheKey:u=>Fx(t,n,u)}}function lM(e,t,n,o){const{modifiers:r,pluralRules:s,messageResolver:u,fallbackLocale:h,fallbackWarn:d,missingWarn:m,fallbackContext:z}=e,L={locale:t,modifiers:r,pluralRules:s,messages:(y,V)=>{let A=u(n,y);if(A==null&&(z||V)){const[,,E]=gn(z||e,y,t,h,d,m);A=u(E,y)}if(B(A)||d2(A)){let E=!1;const g=wn(e,y,t,A,y,()=>{E=!0});return E?B6:g}else return c2(A)?A:B6}};return e.processor&&(L.processor=e.processor),o.list&&(L.list=o.list),o.named&&(L.named=o.named),K(o.plural)&&(L.pluralIndex=o.plural),L}dC();/*!
  * shared v11.1.3
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */const c0=typeof window<"u",M2=(e,t=!1)=>t?Symbol.for(e):Symbol(e),q2=e=>typeof e=="number"&&isFinite(e),l0=e=>zn(e)==="[object RegExp]",iM=e=>Q(e)&&Object.keys(e).length===0,i2=Object.assign,uM=Object.create,t2=(e=null)=>uM(e);let T6;const R2=()=>T6||(T6=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:t2()),pM=Object.prototype.hasOwnProperty;function i0(e,t){return pM.call(e,t)}const p2=Array.isArray,B2=e=>typeof e=="function",D=e=>typeof e=="string",Z=e=>typeof e=="boolean",n2=e=>e!==null&&typeof e=="object",_M=Object.prototype.toString,zn=e=>_M.call(e),Q=e=>zn(e)==="[object Object]",n0=e=>!n2(e)||p2(e);function o0(e,t){if(n0(e)||n0(t))throw new Error("Invalid value");const n=[{src:e,des:t}];for(;n.length;){const{src:o,des:r}=n.pop();Object.keys(o).forEach(s=>{s!=="__proto__"&&(n2(o[s])&&!n2(r[s])&&(r[s]=Array.isArray(o[s])?[]:t2()),n0(r[s])||n0(o[s])?r[s]=o[s]:n.push({src:o[s],des:r[s]}))})}}/*!
  * vue-i18n v11.1.3
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */const dM="11.1.3";function hM(){typeof __VUE_I18N_FULL_INSTALL__!="boolean"&&(R2().__VUE_I18N_FULL_INSTALL__=!0),typeof __VUE_I18N_LEGACY_API__!="boolean"&&(R2().__VUE_I18N_LEGACY_API__=!0),typeof __INTLIFY_DROP_MESSAGE_COMPILER__!="boolean"&&(R2().__INTLIFY_DROP_MESSAGE_COMPILER__=!1),typeof __INTLIFY_PROD_DEVTOOLS__!="boolean"&&(R2().__INTLIFY_PROD_DEVTOOLS__=!1)}const o2={UNEXPECTED_RETURN_TYPE:VC,INVALID_ARGUMENT:25,MUST_BE_CALL_SETUP_TOP:26,NOT_INSTALLED:27,REQUIRED_VALUE:28,INVALID_VALUE:29,CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:30,NOT_INSTALLED_WITH_PROVIDE:31,UNEXPECTED_ERROR:32,NOT_COMPATIBLE_LEGACY_VUE_I18N:33,NOT_AVAILABLE_COMPOSITION_IN_LEGACY:34};function a2(e,...t){return p0(e,null,void 0)}const T0=M2("__translateVNode"),I0=M2("__datetimeParts"),O0=M2("__numberParts"),xn=M2("__setPluralRules"),Cn=M2("__injectWithOption"),N0=M2("__dispose");function j2(e){if(!n2(e)||d2(e))return e;for(const t in e)if(i0(e,t))if(!t.includes("."))n2(e[t])&&j2(e[t]);else{const n=t.split("."),o=n.length-1;let r=e,s=!1;for(let u=0;u<o;u++){if(n[u]==="__proto__")throw new Error(`unsafe key: ${n[u]}`);if(n[u]in r||(r[n[u]]=t2()),!n2(r[n[u]])){s=!0;break}r=r[n[u]]}if(s||(d2(r)?un.includes(n[o])||delete e[t]:(r[n[o]]=e[t],delete e[t])),!d2(r)){const u=r[n[o]];n2(u)&&j2(u)}}return e}function r6(e,t){const{messages:n,__i18n:o,messageResolver:r,flatJson:s}=t,u=Q(n)?n:p2(o)?t2():{[e]:t2()};if(p2(o)&&o.forEach(h=>{if("locale"in h&&"resource"in h){const{locale:d,resource:m}=h;d?(u[d]=u[d]||t2(),o0(m,u[d])):o0(m,u)}else D(h)&&o0(JSON.parse(h),u)}),r==null&&s)for(const h in u)i0(u,h)&&j2(u[h]);return u}function Mn(e){return e.type}function Ln(e,t,n){let o=n2(t.messages)?t.messages:t2();"__i18nGlobal"in n&&(o=r6(e.locale.value,{messages:o,__i18n:n.__i18nGlobal}));const r=Object.keys(o);r.length&&r.forEach(s=>{e.mergeLocaleMessage(s,o[s])});{if(n2(t.datetimeFormats)){const s=Object.keys(t.datetimeFormats);s.length&&s.forEach(u=>{e.mergeDateTimeFormat(u,t.datetimeFormats[u])})}if(n2(t.numberFormats)){const s=Object.keys(t.numberFormats);s.length&&s.forEach(u=>{e.mergeNumberFormat(u,t.numberFormats[u])})}}}function I6(e){return a(qn,null,e,0)}const O6="__INTLIFY_META__",N6=()=>[],$M=()=>!1;let P6=0;function D6(e){return(t,n,o,r)=>e(n,o,U2()||void 0,r)}const fM=()=>{const e=U2();let t=null;return e&&(t=Mn(e)[O6])?{[O6]:t}:null};function s6(e={}){const{__root:t,__injectWithOption:n}=e,o=t===void 0,r=e.flatJson,s=c0?F2:Zn;let u=Z(e.inheritLocale)?e.inheritLocale:!0;const h=s(t&&u?t.locale.value:D(e.locale)?e.locale:Z2),d=s(t&&u?t.fallbackLocale.value:D(e.fallbackLocale)||p2(e.fallbackLocale)||Q(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:h.value),m=s(r6(h.value,e)),z=s(Q(e.datetimeFormats)?e.datetimeFormats:{[h.value]:{}}),x=s(Q(e.numberFormats)?e.numberFormats:{[h.value]:{}});let L=t?t.missingWarn:Z(e.missingWarn)||l0(e.missingWarn)?e.missingWarn:!0,y=t?t.fallbackWarn:Z(e.fallbackWarn)||l0(e.fallbackWarn)?e.fallbackWarn:!0,V=t?t.fallbackRoot:Z(e.fallbackRoot)?e.fallbackRoot:!0,A=!!e.fallbackFormat,E=B2(e.missing)?e.missing:null,$=B2(e.missing)?D6(e.missing):null,g=B2(e.postTranslation)?e.postTranslation:null,H=t?t.warnHtmlMessage:Z(e.warnHtmlMessage)?e.warnHtmlMessage:!0,v=!!e.escapeParameter;const k=t?t.modifiers:Q(e.modifiers)?e.modifiers:{};let b=e.pluralRules||t&&t.pluralRules,M;M=(()=>{o&&k6(null);const f={version:dM,locale:h.value,fallbackLocale:d.value,messages:m.value,modifiers:k,pluralRules:b,missing:$===null?void 0:$,missingWarn:L,fallbackWarn:y,fallbackFormat:A,unresolving:!0,postTranslation:g===null?void 0:g,warnHtmlMessage:H,escapeParameter:v,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};f.datetimeFormats=z.value,f.numberFormats=x.value,f.__datetimeFormatters=Q(M)?M.__datetimeFormatters:void 0,f.__numberFormatters=Q(M)?M.__numberFormatters:void 0;const C=jC(f);return o&&k6(C),C})(),D2(M,h.value,d.value);function P(){return[h.value,d.value,m.value,z.value,x.value]}const r2=E2({get:()=>h.value,set:f=>{M.locale=f,h.value=f}}),l2=E2({get:()=>d.value,set:f=>{M.fallbackLocale=f,d.value=f,D2(M,h.value,f)}}),W=E2(()=>m.value),V2=E2(()=>z.value),d0=E2(()=>x.value);function h0(){return B2(g)?g:null}function $0(f){g=f,M.postTranslation=f}function f0(){return E}function m0(f){f!==null&&($=D6(f)),E=f,M.missing=$}const h2=(f,C,I,U,L2,Q2)=>{P();let A2;try{__INTLIFY_PROD_DEVTOOLS__,o||(M.fallbackContext=t?qC():void 0),A2=f(M)}finally{__INTLIFY_PROD_DEVTOOLS__,o||(M.fallbackContext=void 0)}if(I!=="translate exists"&&q2(A2)&&A2===_0||I==="translate exists"&&!A2){const[Vn,NM]=C();return t&&V?U(t):L2(Vn)}else{if(Q2(A2))return A2;throw a2(o2.UNEXPECTED_RETURN_TYPE)}};function K2(...f){return h2(C=>Reflect.apply(F6,null,[C,...f]),()=>F0(...f),"translate",C=>Reflect.apply(C.t,C,[...f]),C=>C,C=>D(C))}function X2(...f){const[C,I,U]=f;if(U&&!n2(U))throw a2(o2.INVALID_ARGUMENT);return K2(C,I,i2({resolvedMessage:!0},U||{}))}function v0(...f){return h2(C=>Reflect.apply(b6,null,[C,...f]),()=>S0(...f),"datetime format",C=>Reflect.apply(C.d,C,[...f]),()=>L6,C=>D(C))}function g0(...f){return h2(C=>Reflect.apply(A6,null,[C,...f]),()=>B0(...f),"number format",C=>Reflect.apply(C.n,C,[...f]),()=>L6,C=>D(C))}function w0(f){return f.map(C=>D(C)||q2(C)||Z(C)?I6(String(C)):C)}const z0={normalize:w0,interpolate:f=>f,type:"vnode"};function x0(...f){return h2(C=>{let I;const U=C;try{U.processor=z0,I=Reflect.apply(F6,null,[U,...f])}finally{U.processor=null}return I},()=>F0(...f),"translate",C=>C[T0](...f),C=>[I6(C)],C=>p2(C))}function J2(...f){return h2(C=>Reflect.apply(A6,null,[C,...f]),()=>B0(...f),"number format",C=>C[O0](...f),N6,C=>D(C)||p2(C))}function C0(...f){return h2(C=>Reflect.apply(b6,null,[C,...f]),()=>S0(...f),"datetime format",C=>C[I0](...f),N6,C=>D(C)||p2(C))}function M0(f){b=f,M.pluralRules=b}function L0(f,C){return h2(()=>{if(!f)return!1;const I=D(C)?C:h.value,U=b2(I),L2=M.messageResolver(U,f);return d2(L2)||c2(L2)||D(L2)},()=>[f],"translate exists",I=>Reflect.apply(I.te,I,[f,C]),$M,I=>Z(I))}function H0(f){let C=null;const I=pn(M,d.value,h.value);for(let U=0;U<I.length;U++){const L2=m.value[I[U]]||{},Q2=M.messageResolver(L2,f);if(Q2!=null){C=Q2;break}}return C}function N2(f){const C=H0(f);return C??(t?t.tm(f)||{}:{})}function b2(f){return m.value[f]||{}}function y2(f,C){if(r){const I={[f]:C};for(const U in I)i0(I,U)&&j2(I[U]);C=I[f]}m.value[f]=C,M.messages=m.value}function P2(f,C){m.value[f]=m.value[f]||{};const I={[f]:C};if(r)for(const U in I)i0(I,U)&&j2(I[U]);C=I[f],o0(C,m.value[f]),M.messages=m.value}function k0(f){return z.value[f]||{}}function p(f,C){z.value[f]=C,M.datetimeFormats=z.value,y6(M,f,C)}function _(f,C){z.value[f]=i2(z.value[f]||{},C),M.datetimeFormats=z.value,y6(M,f,C)}function w(f){return x.value[f]||{}}function S(f,C){x.value[f]=C,M.numberFormats=x.value,E6(M,f,C)}function R(f,C){x.value[f]=i2(x.value[f]||{},C),M.numberFormats=x.value,E6(M,f,C)}P6++,t&&c0&&(a0(t.locale,f=>{u&&(h.value=f,M.locale=f,D2(M,h.value,d.value))}),a0(t.fallbackLocale,f=>{u&&(d.value=f,M.fallbackLocale=f,D2(M,h.value,d.value))}));const F={id:P6,locale:r2,fallbackLocale:l2,get inheritLocale(){return u},set inheritLocale(f){u=f,f&&t&&(h.value=t.locale.value,d.value=t.fallbackLocale.value,D2(M,h.value,d.value))},get availableLocales(){return Object.keys(m.value).sort()},messages:W,get modifiers(){return k},get pluralRules(){return b||{}},get isGlobal(){return o},get missingWarn(){return L},set missingWarn(f){L=f,M.missingWarn=L},get fallbackWarn(){return y},set fallbackWarn(f){y=f,M.fallbackWarn=y},get fallbackRoot(){return V},set fallbackRoot(f){V=f},get fallbackFormat(){return A},set fallbackFormat(f){A=f,M.fallbackFormat=A},get warnHtmlMessage(){return H},set warnHtmlMessage(f){H=f,M.warnHtmlMessage=f},get escapeParameter(){return v},set escapeParameter(f){v=f,M.escapeParameter=f},t:K2,getLocaleMessage:b2,setLocaleMessage:y2,mergeLocaleMessage:P2,getPostTranslationHandler:h0,setPostTranslationHandler:$0,getMissingHandler:f0,setMissingHandler:m0,[xn]:M0};return F.datetimeFormats=V2,F.numberFormats=d0,F.rt=X2,F.te=L0,F.tm=N2,F.d=v0,F.n=g0,F.getDateTimeFormat=k0,F.setDateTimeFormat=p,F.mergeDateTimeFormat=_,F.getNumberFormat=w,F.setNumberFormat=S,F.mergeNumberFormat=R,F[Cn]=n,F[T0]=x0,F[I0]=C0,F[O0]=J2,F}function mM(e){const t=D(e.locale)?e.locale:Z2,n=D(e.fallbackLocale)||p2(e.fallbackLocale)||Q(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:t,o=B2(e.missing)?e.missing:void 0,r=Z(e.silentTranslationWarn)||l0(e.silentTranslationWarn)?!e.silentTranslationWarn:!0,s=Z(e.silentFallbackWarn)||l0(e.silentFallbackWarn)?!e.silentFallbackWarn:!0,u=Z(e.fallbackRoot)?e.fallbackRoot:!0,h=!!e.formatFallbackMessages,d=Q(e.modifiers)?e.modifiers:{},m=e.pluralizationRules,z=B2(e.postTranslation)?e.postTranslation:void 0,x=D(e.warnHtmlInMessage)?e.warnHtmlInMessage!=="off":!0,L=!!e.escapeParameterHtml,y=Z(e.sync)?e.sync:!0;let V=e.messages;if(Q(e.sharedMessages)){const k=e.sharedMessages;V=Object.keys(k).reduce((M,O)=>{const P=M[O]||(M[O]={});return i2(P,k[O]),M},V||{})}const{__i18n:A,__root:E,__injectWithOption:$}=e,g=e.datetimeFormats,H=e.numberFormats,v=e.flatJson;return{locale:t,fallbackLocale:n,messages:V,flatJson:v,datetimeFormats:g,numberFormats:H,missing:o,missingWarn:r,fallbackWarn:s,fallbackRoot:u,fallbackFormat:h,modifiers:d,pluralRules:m,postTranslation:z,warnHtmlMessage:x,escapeParameter:L,messageResolver:e.messageResolver,inheritLocale:y,__i18n:A,__root:E,__injectWithOption:$}}function P0(e={}){const t=s6(mM(e)),{__extender:n}=e,o={id:t.id,get locale(){return t.locale.value},set locale(r){t.locale.value=r},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(r){t.fallbackLocale.value=r},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get missing(){return t.getMissingHandler()},set missing(r){t.setMissingHandler(r)},get silentTranslationWarn(){return Z(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(r){t.missingWarn=Z(r)?!r:r},get silentFallbackWarn(){return Z(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(r){t.fallbackWarn=Z(r)?!r:r},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(r){t.fallbackFormat=r},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(r){t.setPostTranslationHandler(r)},get sync(){return t.inheritLocale},set sync(r){t.inheritLocale=r},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(r){t.warnHtmlMessage=r!=="off"},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(r){t.escapeParameter=r},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t(...r){return Reflect.apply(t.t,t,[...r])},rt(...r){return Reflect.apply(t.rt,t,[...r])},te(r,s){return t.te(r,s)},tm(r){return t.tm(r)},getLocaleMessage(r){return t.getLocaleMessage(r)},setLocaleMessage(r,s){t.setLocaleMessage(r,s)},mergeLocaleMessage(r,s){t.mergeLocaleMessage(r,s)},d(...r){return Reflect.apply(t.d,t,[...r])},getDateTimeFormat(r){return t.getDateTimeFormat(r)},setDateTimeFormat(r,s){t.setDateTimeFormat(r,s)},mergeDateTimeFormat(r,s){t.mergeDateTimeFormat(r,s)},n(...r){return Reflect.apply(t.n,t,[...r])},getNumberFormat(r){return t.getNumberFormat(r)},setNumberFormat(r,s){t.setNumberFormat(r,s)},mergeNumberFormat(r,s){t.mergeNumberFormat(r,s)}};return o.__extender=n,o}function vM(e,t,n){return{beforeCreate(){const o=U2();if(!o)throw a2(o2.UNEXPECTED_ERROR);const r=this.$options;if(r.i18n){const s=r.i18n;if(r.__i18n&&(s.__i18n=r.__i18n),s.__root=t,this===this.$root)this.$i18n=R6(e,s);else{s.__injectWithOption=!0,s.__extender=n.__vueI18nExtend,this.$i18n=P0(s);const u=this.$i18n;u.__extender&&(u.__disposer=u.__extender(this.$i18n))}}else if(r.__i18n)if(this===this.$root)this.$i18n=R6(e,r);else{this.$i18n=P0({__i18n:r.__i18n,__injectWithOption:!0,__extender:n.__vueI18nExtend,__root:t});const s=this.$i18n;s.__extender&&(s.__disposer=s.__extender(this.$i18n))}else this.$i18n=e;r.__i18nGlobal&&Ln(t,r,r),this.$t=(...s)=>this.$i18n.t(...s),this.$rt=(...s)=>this.$i18n.rt(...s),this.$te=(s,u)=>this.$i18n.te(s,u),this.$d=(...s)=>this.$i18n.d(...s),this.$n=(...s)=>this.$i18n.n(...s),this.$tm=s=>this.$i18n.tm(s),n.__setInstance(o,this.$i18n)},mounted(){},unmounted(){const o=U2();if(!o)throw a2(o2.UNEXPECTED_ERROR);const r=this.$i18n;delete this.$t,delete this.$rt,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,r.__disposer&&(r.__disposer(),delete r.__disposer,delete r.__extender),n.__deleteInstance(o),delete this.$i18n}}}function R6(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[xn](t.pluralizationRules||e.pluralizationRules);const n=r6(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(n).forEach(o=>e.mergeLocaleMessage(o,n[o])),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach(o=>e.mergeDateTimeFormat(o,t.datetimeFormats[o])),t.numberFormats&&Object.keys(t.numberFormats).forEach(o=>e.mergeNumberFormat(o,t.numberFormats[o])),e}const a6={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>e==="parent"||e==="global",default:"parent"},i18n:{type:Object}};function gM({slots:e},t){return t.length===1&&t[0]==="default"?(e.default?e.default():[]).reduce((o,r)=>[...o,...r.type===u0?r.children:[r]],[]):t.reduce((n,o)=>{const r=e[o];return r&&(n[o]=r()),n},t2())}function Hn(){return u0}const wM=l({name:"i18n-t",props:i2({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>q2(e)||!isNaN(e)}},a6),setup(e,t){const{slots:n,attrs:o}=t,r=e.i18n||c6({useScope:e.scope,__useComponent:!0});return()=>{const s=Object.keys(n).filter(x=>x!=="_"),u=t2();e.locale&&(u.locale=e.locale),e.plural!==void 0&&(u.plural=D(e.plural)?+e.plural:e.plural);const h=gM(t,s),d=r[T0](e.keypath,h,u),m=i2(t2(),o),z=D(e.tag)||n2(e.tag)?e.tag:Hn();return X6(z,m,d)}}}),U6=wM;function zM(e){return p2(e)&&!D(e[0])}function kn(e,t,n,o){const{slots:r,attrs:s}=t;return()=>{const u={part:!0};let h=t2();e.locale&&(u.locale=e.locale),D(e.format)?u.key=e.format:n2(e.format)&&(D(e.format.key)&&(u.key=e.format.key),h=Object.keys(e.format).reduce((L,y)=>n.includes(y)?i2(t2(),L,{[y]:e.format[y]}):L,t2()));const d=o(e.value,u,h);let m=[u.key];p2(d)?m=d.map((L,y)=>{const V=r[L.type],A=V?V({[L.type]:L.value,index:y,parts:d}):[L.value];return zM(A)&&(A[0].key=`${L.type}-${y}`),A}):D(d)&&(m=[d]);const z=i2(t2(),s),x=D(e.tag)||n2(e.tag)?e.tag:Hn();return X6(x,z,m)}}const xM=l({name:"i18n-n",props:i2({value:{type:Number,required:!0},format:{type:[String,Object]}},a6),setup(e,t){const n=e.i18n||c6({useScope:e.scope,__useComponent:!0});return kn(e,t,vn,(...o)=>n[O0](...o))}}),G6=xM;function CM(e,t){const n=e;if(e.mode==="composition")return n.__getInstance(t)||e.global;{const o=n.__getInstance(t);return o!=null?o.__composer:e.global.__composer}}function MM(e){const t=u=>{const{instance:h,value:d}=u;if(!h||!h.$)throw a2(o2.UNEXPECTED_ERROR);const m=CM(e,h.$),z=W6(d);return[Reflect.apply(m.t,m,[...Z6(z)]),m]};return{created:(u,h)=>{const[d,m]=t(h);c0&&e.global===m&&(u.__i18nWatcher=a0(m.locale,()=>{h.instance&&h.instance.$forceUpdate()})),u.__composer=m,u.textContent=d},unmounted:u=>{c0&&u.__i18nWatcher&&(u.__i18nWatcher(),u.__i18nWatcher=void 0,delete u.__i18nWatcher),u.__composer&&(u.__composer=void 0,delete u.__composer)},beforeUpdate:(u,{value:h})=>{if(u.__composer){const d=u.__composer,m=W6(h);u.textContent=Reflect.apply(d.t,d,[...Z6(m)])}},getSSRProps:u=>{const[h]=t(u);return{textContent:h}}}}function W6(e){if(D(e))return{path:e};if(Q(e)){if(!("path"in e))throw a2(o2.REQUIRED_VALUE,"path");return e}else throw a2(o2.INVALID_VALUE)}function Z6(e){const{path:t,locale:n,args:o,choice:r,plural:s}=e,u={},h=o||{};return D(n)&&(u.locale=n),q2(r)&&(u.plural=r),q2(s)&&(u.plural=s),[t,h,u]}function LM(e,t,...n){const o=Q(n[0])?n[0]:{};(Z(o.globalInstall)?o.globalInstall:!0)&&([U6.name,"I18nT"].forEach(s=>e.component(s,U6)),[G6.name,"I18nN"].forEach(s=>e.component(s,G6)),[j6.name,"I18nD"].forEach(s=>e.component(s,j6))),e.directive("t",MM(t))}const HM=M2("global-vue-i18n");function kM(e={}){const t=__VUE_I18N_LEGACY_API__&&Z(e.legacy)?e.legacy:__VUE_I18N_LEGACY_API__,n=Z(e.globalInjection)?e.globalInjection:!0,o=new Map,[r,s]=VM(e,t),u=M2("");function h(x){return o.get(x)||null}function d(x,L){o.set(x,L)}function m(x){o.delete(x)}const z={get mode(){return __VUE_I18N_LEGACY_API__&&t?"legacy":"composition"},async install(x,...L){if(x.__VUE_I18N_SYMBOL__=u,x.provide(x.__VUE_I18N_SYMBOL__,z),Q(L[0])){const A=L[0];z.__composerExtend=A.__composerExtend,z.__vueI18nExtend=A.__vueI18nExtend}let y=null;!t&&n&&(y=TM(x,z.global)),__VUE_I18N_FULL_INSTALL__&&LM(x,z,...L),__VUE_I18N_LEGACY_API__&&t&&x.mixin(vM(s,s.__composer,z));const V=x.unmount;x.unmount=()=>{y&&y(),z.dispose(),V()}},get global(){return s},dispose(){r.stop()},__instances:o,__getInstance:h,__setInstance:d,__deleteInstance:m};return z}function c6(e={}){const t=U2();if(t==null)throw a2(o2.MUST_BE_CALL_SETUP_TOP);if(!t.isCE&&t.appContext.app!=null&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw a2(o2.NOT_INSTALLED);const n=bM(t),o=AM(n),r=Mn(t),s=yM(e,r);if(s==="global")return Ln(o,e,r),o;if(s==="parent"){let d=EM(n,t,e.__useComponent);return d==null&&(d=o),d}const u=n;let h=u.__getInstance(t);if(h==null){const d=i2({},e);"__i18n"in r&&(d.__i18n=r.__i18n),o&&(d.__root=o),h=s6(d),u.__composerExtend&&(h[N0]=u.__composerExtend(h)),BM(u,t,h),u.__setInstance(t,h)}return h}function VM(e,t){const n=Rn(),o=__VUE_I18N_LEGACY_API__&&t?n.run(()=>P0(e)):n.run(()=>s6(e));if(o==null)throw a2(o2.UNEXPECTED_ERROR);return[n,o]}function bM(e){const t=Un(e.isCE?HM:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw a2(e.isCE?o2.NOT_INSTALLED_WITH_PROVIDE:o2.UNEXPECTED_ERROR);return t}function yM(e,t){return iM(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}function AM(e){return e.mode==="composition"?e.global:e.global.__composer}function EM(e,t,n=!1){let o=null;const r=t.root;let s=SM(t,n);for(;s!=null;){const u=e;if(e.mode==="composition")o=u.__getInstance(s);else if(__VUE_I18N_LEGACY_API__){const h=u.__getInstance(s);h!=null&&(o=h.__composer,n&&o&&!o[Cn]&&(o=null))}if(o!=null||r===s)break;s=s.parent}return o}function SM(e,t=!1){return e==null?null:t&&e.vnode.ctx||e.parent}function BM(e,t,n){Y6(()=>{},t),Gn(()=>{const o=n;e.__deleteInstance(t);const r=o[N0];r&&(r(),delete o[N0])},t)}const FM=["locale","fallbackLocale","availableLocales"],q6=["t","rt","d","n","tm","te"];function TM(e,t){const n=Object.create(null);return FM.forEach(r=>{const s=Object.getOwnPropertyDescriptor(t,r);if(!s)throw a2(o2.UNEXPECTED_ERROR);const u=Wn(s.value)?{get(){return s.value.value},set(h){s.value.value=h}}:{get(){return s.get&&s.get()}};Object.defineProperty(n,r,u)}),e.config.globalProperties.$i18n=n,q6.forEach(r=>{const s=Object.getOwnPropertyDescriptor(t,r);if(!s||!s.value)throw a2(o2.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${r}`,s)}),()=>{delete e.config.globalProperties.$i18n,q6.forEach(r=>{delete e.config.globalProperties[`$${r}`]})}}const IM=l({name:"i18n-d",props:i2({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},a6),setup(e,t){const n=e.i18n||c6({useScope:e.scope,__useComponent:!0});return kn(e,t,mn,(...o)=>n[I0](...o))}}),j6=IM;hM();RC(CC);UC(NC);GC(pn);if(__INTLIFY_PROD_DEVTOOLS__){const e=R2();e.__INTLIFY__=!0,MC(e.__INTLIFY_DEVTOOLS_GLOBAL_HOOK__)}const OM=kM({locale:localStorage.getItem("language")||"zh",fallbackLocale:"zh",legacy:!1,globalInjection:!0}),Y2=jn(to);Y2.use(Bx);Y2.use(J6);Y2.use(OM);Y2.use(Yn);Y2.mount("#app");export{mx as M};
