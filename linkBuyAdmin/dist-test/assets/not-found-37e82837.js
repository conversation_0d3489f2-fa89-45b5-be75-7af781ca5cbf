import{a,_ as i,c as n,e as c,k as s,aX as l,t as d}from"./views-biz-72c05ff0.js";const r=a({name:"not-found",props:{},components:{},setup(){return{message:"The webmaster said that you can not enter this page..."}}}),_=""+new URL("404-538aa4d7.png",import.meta.url).href,e=""+new URL("404_cloud-98e7ac66.png",import.meta.url).href;const p={class:"wscn-http404-container"},u={class:"wscn-http404"},m={class:"bullshit"},b={class:"bullshit__headline"};function h(o,t,v,f,g,k){return n(),c("div",p,[s("div",u,[t[4]||(t[4]=l('<div class="pic-404" data-v-42bd8b3c><img class="pic-404__parent" src="'+_+'" alt="404" data-v-42bd8b3c><img class="pic-404__child left" src="'+e+'" alt="404" data-v-42bd8b3c><img class="pic-404__child mid" src="'+e+'" alt="404" data-v-42bd8b3c><img class="pic-404__child right" src="'+e+'" alt="404" data-v-42bd8b3c></div>',1)),s("div",m,[t[0]||(t[0]=s("div",{class:"bullshit__oops"},"OOPS!",-1)),t[1]||(t[1]=s("div",{class:"bullshit__info"},null,-1)),s("div",b,d(o.message),1),t[2]||(t[2]=s("div",{class:"bullshit__info"}," Please check that the URL you entered is correct, or click the button below to return to the homepage. ",-1)),t[3]||(t[3]=s("a",{href:"#",class:"bullshit__return-home"},"Back to home",-1))])])])}const y=i(r,[["render",h],["__scopeId","data-v-42bd8b3c"]]);export{y as default};
