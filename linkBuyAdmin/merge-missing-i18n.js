const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

// 1. 调用 vue-i18n-extract missing 并拿到表格输出
const raw = execSync('npx vue-i18n-extract missing', { encoding: 'utf-8' })

// 2. 从第 3 行开始解析，提取出每行第二列（'Login'）的 key
const lines = raw
  .split('\n')
  .slice(3)
  .filter((l) => l.trim() && !/^─/.test(l))
const keys = lines
  .map((line) => {
    const m = line.match(/'([^']+)'/)
    return m ? m[1] : null
  })
  .filter(Boolean)

// 3. 读现有 ch.json（如果空或不存在则初始化为 {}）
const langFile = path.resolve(__dirname, 'src/locales/ch.json')
let data = {}
if (fs.existsSync(langFile)) {
  const txt = fs.readFileSync(langFile, 'utf-8').trim()
  if (txt) data = JSON.parse(txt)
}

// 4. 合并缺失 key，默认值设为 key 本身
let added = 0
keys.forEach((k) => {
  if (!(k in data)) {
    data[k] = k
    added++
  }
})

// 5. 写回文件
fs.writeFileSync(langFile, JSON.stringify(data, null, 2), 'utf-8')
console.log(`✅ 已向 ch.json 添加 ${added} 项：`, keys)
