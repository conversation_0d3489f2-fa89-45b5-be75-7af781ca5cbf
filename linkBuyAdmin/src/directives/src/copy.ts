import { DirectiveBinding } from 'vue'
import { ElMessage } from 'element-plus'

import { IDirective } from '../types'

const copy: IDirective = {
  name: 'copy',
  beforeMount(el, binding: DirectiveBinding) {
    el.$value = binding.value
    el.handler = () => {
      if (!el.$value) {
        return
      }
      // 动态创建 textarea 标签
      const textarea = document.createElement('textarea')
      textarea.readOnly = true
      textarea.style.position = 'absolute'
      textarea.style.left = '-9999px'
      // 将要 copy 的值赋给 textarea 标签的 value 属性
      textarea.value = el.$value
      // 将 textarea 插入到 body 中
      document.body.appendChild(textarea)
      // 选中值并复制
      textarea.select()
      const result = document.execCommand('Copy')
      if (result) {
        ElMessage.success('复制成功')
      }
      document.body.removeChild(textarea)
    }
    // 绑定点击事件
    el.addEventListener('click', el.handler)
  },
  updated(el, binding: DirectiveBinding) {
    el.$value = binding.value
  },
  unmounted(el) {
    //移除点击事件
    el.removeEventListener('click', el.$handle)
  }
}

export default copy
