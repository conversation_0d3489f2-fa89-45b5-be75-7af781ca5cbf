import { DirectiveBinding } from 'vue'
import { IDirective } from '../types'

const trim: IDirective = {
  name: 'trim',
  updated(el: HTMLElement, bindings: DirectiveBinding) {
    // 获取真实的inputDOM元素
    const input: any = el.children[0]
    try {
      // 如果不是el输入框 那么直接返回
      if (!input) {
        return false
      }
      // 获取当前光标所在位置
      const inputSelection = input.selectionStart
      // 获取加上空格后的字符串长度
      const valueLength = input.value?.length
      const newValue = bindings.value ? bindings.value.replace(/\s/g, '') : ''
      if (bindings.value !== newValue) {
        input.value = newValue
        if (input.value?.length !== valueLength) {
          input.selectionStart = inputSelection - 1
          input.selectionEnd = inputSelection - 1
        }
        input.dispatchEvent(
          new Event(bindings.modifiers.lazy ? 'change' : 'input')
        )
      }
    } catch (e) {
      console.log(e)
    }
  }
}

export default trim
