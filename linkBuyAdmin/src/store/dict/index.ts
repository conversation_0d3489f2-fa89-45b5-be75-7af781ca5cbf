import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getAll, IDictItem } from '@/api/dict.ts'

interface DictMap {
    [dictCode: string]: IDictItem[]
}

export const useDict = defineStore('dict', () => {
    // dicts: key 为 dictCode，value 为 IDictItem[] 的 map
    const dicts = ref<DictMap>({})

    async function loadDictDataAction() {
        const { data, code } = await getAll()
        if (code !== 200) return
        const map: DictMap = {}
        data.forEach((dict: { dictCode: string; items: IDictItem[] }) => {
            map[dict.dictCode] = dict.items
        })
        dicts.value = map
    }
    async function getItems(dictCode: string) {
        return dicts.value[dictCode] || []
    }

    function isContainName(dictCode: string, value: string[], name: string) {
        const dict = dicts.value[dictCode]
        if (!dict || !value || !name) return false
        return value.some(v =>
            dict.find(item => item.itemValue.toLowerCase() === v.toLowerCase())?.itemName.toLowerCase() === name.toLowerCase()
        )
    }

    function getName(dictCode: string, value: string) {
        const dict = dicts.value[dictCode]
        return dict?.find(item => item.itemValue.toLowerCase() === value.toLowerCase())?.itemName
    }

    function getDict(dictCode: string) {
        return dicts.value[dictCode] || []
    }

    return {
        dicts,
        loadDictDataAction,
        isContainName,
        getName,
        getDict
    }
}, {
    persist: {
        enabled: true,
        strategies: [
            {
                key: 'dicts',
                storage: localStorage,
                paths: ['dicts']
            }
        ]
    }
})
