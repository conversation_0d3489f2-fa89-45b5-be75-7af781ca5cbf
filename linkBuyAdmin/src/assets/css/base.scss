// @import '~normalize.css';

body {
  padding: 0;
  margin: 0;
}

html,
body,
#app {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  font-family: 'Microsoft YaHei', PingFangSC-Regular;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

@for $v from 0 through 150 {
  .pd-#{$v} {
    padding: #{$v}px;
  }
  .pt-#{$v} {
    padding-top: #{$v}px;
  }
  .pb-#{$v} {
    padding-bottom: #{$v}px;
  }
  .pl-#{$v} {
    padding-left: #{$v}px;
  }
  .pr-#{$v} {
    padding-right: #{$v}px;
  }
  .mt-#{$v} {
    margin-top: #{$v}px;
  }
  .mb-#{$v} {
    margin-bottom: #{$v}px;
  }
  .ml-#{$v} {
    margin-left: #{$v}px;
  }
  .mr-#{$v} {
    margin-right: #{$v}px;
  }
}

.keyword-match {
  color: #f56c6c;
}


// 取消input的上下箭头, 可以隐藏上下箭头
input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
          appearance: none !important;

}

input::-webkit-outer-spin-button{
  -webkit-appearance: none !important;
          appearance: none !important;

}

input[type="number"]{
  -webkit-appearance: textfield;
    -moz-appearance: textfield;
          appearance: textfield;
}
