<template>
  <div class="mall-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>商城管理 / 商城列表</h2>
    </div>

    <!-- 操作按钮区 -->
    <div class="action-buttons">
      <el-button type="primary" icon="Plus" @click="openAddMallDialog">新增商城应用</el-button>
      <el-button type="primary" @click="generateMallLink">生成商城链接</el-button>
    </div>

    <!-- 商城列表 -->
    <div class="mall-list" v-loading="loading">
      <div v-for="mall in mallList" :key="mall.id" class="mall-card">
        <div class="mall-info">
          <div class="mall-icon">
            <el-image :src="mall.iconUrl" fit="cover">
              <template #error>
                <div class="image-slot">
                  <el-icon><Picture /></el-icon>
                </div>
              </template>
            </el-image>
          </div>
          <div class="mall-name">{{ mall.name }}</div>
          <div class="mall-status" :class="{ 'status-active': mall.status === 1 }">
            <el-tag :type="mall.status === 1 ? 'success' : 'info'">{{ mall.status === 1 ? '使用正常' : '已停用' }}</el-tag>
          </div>
        </div>

        <div class="mall-description">
          <div class="description-label">简介：</div>
          <div class="description-content">{{ mall.description || '暂无简介' }}</div>
        </div>

        <div class="mall-links">
          <div class="link-item">
            <el-icon><Link /></el-icon>
            <span>管理后台地址</span>
            <el-button link type="primary" @click="copyText(mall.adminUrl)">复制</el-button>
          </div>
          <div class="link-item">
            <el-icon><Link /></el-icon>
            <span>移动商城地址</span>
            <el-button link type="primary" @click="copyText(mall.mobileUrl)">复制</el-button>
          </div>
        </div>

        <div class="mall-actions">
          <el-button link type="primary" @click="editMall(mall)">编辑</el-button>
          <el-button link type="primary" @click="modifyAccount(mall)">修改账号</el-button>
          <el-button link type="danger" @click="toggleMallStatus(mall)">
            {{ mall.status === 1 ? '停用' : '启用' }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- 新增/编辑商城弹窗 -->
    <el-dialog v-model="mallDialogVisible" :title="isEdit ? '编辑商城' : '新增商城'" width="600px">
      <el-form :model="mallForm" label-width="100px" :rules="mallRules" ref="mallFormRef">
        <el-form-item label="商城名称" prop="name">
          <el-input v-model="mallForm.name" placeholder="请输入商城名称" />
        </el-form-item>
        <el-form-item label="商城图标">
          <div class="upload-icon">
            <el-image v-if="mallForm.iconUrl" :src="mallForm.iconUrl" class="preview-icon" fit="cover">
              <template #error>
                <div class="image-slot">
                  <el-icon><Picture /></el-icon>
                </div>
              </template>
            </el-image>
            <el-button v-if="!mallForm.iconUrl" type="primary" @click="openImageSelector">上传图标</el-button>
            <el-button v-else type="danger" @click="removeIcon">移除</el-button>
          </div>
        </el-form-item>
        <el-form-item label="商城简介" prop="description">
          <el-input v-model="mallForm.description" type="textarea" rows="3" placeholder="请输入商城简介" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="mallDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitMallForm">确定</el-button>
      </template>
    </el-dialog>

    <!-- 修改账号弹窗 -->
    <el-dialog v-model="accountDialogVisible" title="修改账号" width="500px">
      <el-form :model="accountForm" label-width="100px" :rules="accountRules" ref="accountFormRef">
        <el-form-item label="账号" prop="username">
          <el-input v-model="accountForm.username" placeholder="请输入账号" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="accountForm.password" type="password" placeholder="请输入密码" show-password />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="accountDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitAccountForm">确定</el-button>
      </template>
    </el-dialog>

    <!-- 生成链接弹窗 -->
    <el-dialog v-model="linkDialogVisible" title="生成商城链接" width="500px">
      <el-form :model="linkForm" label-width="100px" ref="linkFormRef">
        <el-form-item label="选择商城" prop="mallId">
          <el-select v-model="linkForm.mallId" placeholder="请选择商城" style="width: 100%">
            <el-option v-for="mall in mallList" :key="mall.id" :label="mall.name" :value="mall.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="linkDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitGenerateLink">生成链接</el-button>
      </template>
    </el-dialog>

    <!-- 图片选择器组件 -->
    <ImageSelector v-model:visible="imageSelectorVisible" title="选择商城图标" :multiple="false" @select="handleImageSelect"
      @cancel="imageSelectorVisible = false" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Picture, Link } from '@element-plus/icons-vue'
import ImageSelector from '@/components/ImageSelector/index.vue'
import { 
  type IMall, 
  type IMallForm, 
  type IMallAccountForm, 
  type IMallLinkForm,
  getMallList,
  addMall,
  updateMall,
  updateMallStatus,
  updateMallAccount,
  generateMallLink as generateMallLinkApi
} from '@/api/biz/mall'

// 页面状态
const loading = ref(false)

// 商城列表数据
const mallList = ref<IMall[]>([])

// 新增/编辑商城相关
const mallDialogVisible = ref(false)
const isEdit = ref(false)
const mallFormRef = ref()
const mallForm = reactive<IMallForm>({
  id: undefined,
  name: '',
  iconUrl: '',
  description: ''
})
const mallRules = {
  name: [{ required: true, message: '请输入商城名称', trigger: 'blur' }]
}

// 修改账号相关
const accountDialogVisible = ref(false)
const currentMall = ref<IMall | null>(null)
const accountFormRef = ref()
const accountForm = reactive<IMallAccountForm>({
  username: '',
  password: ''
})
const accountRules = {
  username: [{ required: true, message: '请输入账号', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
}

// 生成链接相关
const linkDialogVisible = ref(false)
const linkFormRef = ref()
const linkForm = reactive<IMallLinkForm>({
  mallId: undefined
})

// 图片选择器
const imageSelectorVisible = ref(false)

// 获取商城列表
const fetchMallList = async () => {
  loading.value = true
  try {
    const res = await getMallList()
    mallList.value = res.data?.records || []
  } catch (error) {
    console.error('获取商城列表失败:', error)
    ElMessage.error('获取商城列表失败')
  } finally {
    loading.value = false
  }
}

// 打开新增商城弹窗
const openAddMallDialog = () => {
  isEdit.value = false
  mallForm.id = undefined
  mallForm.name = ''
  mallForm.iconUrl = ''
  mallForm.description = ''
  mallDialogVisible.value = true
}

// 编辑商城
const editMall = (mall: IMall) => {
  isEdit.value = true
  mallForm.id = mall.id
  mallForm.name = mall.name
  mallForm.iconUrl = mall.iconUrl
  mallForm.description = mall.description
  mallDialogVisible.value = true
}

// 提交商城表单
const submitMallForm = async () => {
  try {
    await mallFormRef.value.validate()
    
    if (isEdit.value && mallForm.id) {
      await updateMall(mallForm.id, mallForm)
      ElMessage.success('编辑商城成功')
    } else {
      await addMall(mallForm)
      ElMessage.success('新增商城成功')
    }
    
    mallDialogVisible.value = false
    fetchMallList()
  } catch (error) {
    console.error('保存商城失败:', error)
    ElMessage.error('保存商城失败')
  }
}

// 修改账号
const modifyAccount = (mall: IMall) => {
  currentMall.value = mall
  accountForm.username = ''
  accountForm.password = ''
  accountDialogVisible.value = true
}

// 提交账号表单
const submitAccountForm = async () => {
  try {
    await accountFormRef.value.validate()
    if (currentMall.value?.id) {
      await updateMallAccount(currentMall.value.id, accountForm)
      ElMessage.success('账号修改成功')
      accountDialogVisible.value = false
    }
  } catch (error) {
    console.error('修改账号失败:', error)
    ElMessage.error('修改账号失败')
  }
}

// 切换商城状态
const toggleMallStatus = async (mall: IMall) => {
  try {
    const newStatus = mall.status === 1 ? 0 : 1
    await updateMallStatus(mall.id, newStatus)
    mall.status = newStatus
    ElMessage.success(mall.status === 1 ? '商城已启用' : '商城已停用')
  } catch (error) {
    console.error('切换状态失败:', error)
    ElMessage.error('切换状态失败')
  }
}

// 生成商城链接
const generateMallLink = () => {
  linkForm.mallId = undefined
  linkDialogVisible.value = true
}

// 提交生成链接
const submitGenerateLink = async () => {
  if (!linkForm.mallId) {
    ElMessage.warning('请选择商城')
    return
  }
  
  try {
    const res = await generateMallLinkApi(linkForm.mallId)
    ElMessage.success('链接生成成功')
    linkDialogVisible.value = false
    // 可以在这里处理返回的链接数据
    console.log('生成的链接:', res.data)
  } catch (error) {
    console.error('生成链接失败:', error)
    ElMessage.error('生成链接失败')
  }
}

// 复制文本
const copyText = (text: string) => {
  navigator.clipboard.writeText(text).then(() => {
    ElMessage.success('复制成功')
  }).catch(() => {
    ElMessage.error('复制失败')
  })
}

// 打开图片选择器
const openImageSelector = () => {
  imageSelectorVisible.value = true
}

// 处理图片选择
const handleImageSelect = (image: { id: number, url: string, name: string }) => {
  if (image && image.url) {
    mallForm.iconUrl = image.url
  }
  imageSelectorVisible.value = false
}

// 移除图标
const removeIcon = () => {
  mallForm.iconUrl = ''
}

onMounted(() => {
  fetchMallList()
})
</script>

<style scoped lang="scss">
.mall-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
  
  h2 {
    font-size: 18px;
    font-weight: 500;
    color: #333;
  }
}

.action-buttons {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.mall-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.mall-card {
  width: calc(50% - 10px);
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.mall-info {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.mall-icon {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  margin-right: 15px;
  
  .el-image {
    width: 100%;
    height: 100%;
  }
}

.mall-name {
  font-size: 18px;
  font-weight: bold;
  flex: 1;
}

.mall-status {
  margin-left: auto;
}

.mall-description {
  margin-bottom: 15px;
  display: flex;
  
  .description-label {
    color: #606266;
    margin-right: 5px;
  }
  
  .description-content {
    color: #333;
    flex: 1;
  }
}

.mall-links {
  margin-bottom: 20px;
  
  .link-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    
    .el-icon {
      margin-right: 8px;
      color: #409EFF;
    }
    
    span {
      flex: 1;
      color: #606266;
    }
  }
}

.mall-actions {
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #eee;
  padding-top: 15px;
}

.upload-icon {
  display: flex;
  align-items: center;
  
  .preview-icon {
    width: 60px;
    height: 60px;
    border-radius: 4px;
    margin-right: 10px;
  }
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 20px;
}
</style> 