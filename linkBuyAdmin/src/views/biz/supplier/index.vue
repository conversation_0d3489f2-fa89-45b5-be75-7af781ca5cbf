<template>
  <div class="supplier-container">
    <!-- 搜索栏 -->
    <div class="header">
      <el-input v-model="searchKeyword" placeholder="搜索供应商名称/联系人/手机号" clearable style="width: 200px; margin-right: 10px"
        @keyup.enter="fetchSupplierList" @input="handleSearchChange" @clear="handleSearchClear" />
      <el-button type="primary" @click="openEditDialog()">新增供应商</el-button>
    </div>

    <!-- 供应商列表 -->
    <el-table height="calc(100vh - 230px)" :data="supplierList" v-loading="loading"
      style="margin-top: 16px; width: 100%">
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="name" label="供应商" min-width="200">
        <template #default="{ row }">
          <div>
            <div style="font-weight: bold;">名称：{{ row.name }}</div>
            <div style="font-size: 12px; color: #666;">编码：{{ row.code || '暂无' }}</div>
            <div style="font-size: 12px; color: #666;">统一社会代码：{{ row.usciCode || '暂无' }}</div>
            <div style="font-size: 12px; color: #666;">地址：{{ row.address || '暂无' }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="法人/联系人" min-width="180">
        <template #default="{ row }">
          <div>
            <div>法人：{{ row.legalPerson || '暂无' }}</div>
            <div style="font-size: 12px; color: #666;">联系人：{{ row.contactName || '暂无' }}</div>
            <div style="font-size: 12px; color: #666;">手机号：{{ row.contactMobile || '暂无' }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="银行信息" min-width="180">
        <template #default="{ row }">
          <div>
            <div>银行：{{ row.bankName || '暂无' }}</div>
            <div style="font-size: 12px; color: #666;">支行：{{ row.subbranchName || '暂无' }}</div>
            <div style="font-size: 12px; color: #666;">账号：{{ row.bankAccount || '暂无' }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100" align="center">
        <template #default="{ row }">
          <el-switch v-model="row.status" :active-value="1" :inactive-value="0" @change="toggleStatus(row)" />
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="180" />
      <el-table-column label="操作" width="200" align="center">
        <template #default="{ row }">
          <el-button link type="primary" size="small" @click="openEditDialog(row)">编辑</el-button>
          <el-button link type="primary" size="small" @click="openLicenseDialog(row)">资质</el-button>
          <el-button link type="primary" size="small" @click="openUserDialog(row)">用户</el-button>
          <el-button link type="primary" size="small" @click="handleSuperLogin(row)" :disabled="row.status !== 1">登录</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total" background
        :page-sizes="[10, 20, 50, 100]" @size-change="handleSizeChange" @current-change="fetchSupplierList" />
    </div>

    <!-- 编辑供应商抽屉 -->
    <el-drawer v-model="editDialogVisible" :title="editDialogTitle" size="600px" direction="rtl">
      <el-form :model="editForm" :rules="editRules" ref="editFormRef" label-width="120px" style="padding: 0 20px">

        <!-- 基本信息区域 -->
        <el-divider content-position="left">
          <span class="section-title">基本信息</span>
        </el-divider>

        <el-form-item label="供应商名称" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入供应商名称" />
        </el-form-item>

        <el-form-item label="统一‌社会代码" prop="usciCode">
          <el-input v-model="editForm.usciCode" placeholder="请输入统一社会信用代码" />
        </el-form-item>

        <el-form-item label="法人代表" prop="legalPerson">
          <el-input v-model="editForm.legalPerson" placeholder="请输入法人代表" />
        </el-form-item>

        <el-form-item label="详细地址" prop="address">
          <el-input v-model="editForm.address" type="textarea" :rows="2" placeholder="请输入详细地址" />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="editForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>

        <!-- 联系信息区域 -->
        <el-divider content-position="left">
          <span class="section-title">联系信息</span>
        </el-divider>

        <!-- 提示信息 -->
        <div class="contact-tip">
          <el-alert title="联系人手机号将作为供应商管理员账号，请确保手机号正确" type="info" :closable="false" show-icon />
        </div>
        <el-form-item label="联系人姓名" prop="contactName">
          <el-input v-model="editForm.contactName" placeholder="请输入联系人姓名" />
        </el-form-item>

        <el-form-item label="联系人手机" prop="contactMobile">
          <el-input v-model="editForm.contactMobile" placeholder="请输入联系人手机" />
        </el-form-item>

        <el-form-item label="联系人邮箱" prop="contactEmail">
          <el-input v-model="editForm.contactEmail" placeholder="请输入联系人邮箱" />
        </el-form-item>


        <!-- 账户信息区域 -->
        <el-divider content-position="left">
          <span class="section-title">账户信息</span>
        </el-divider>

        <el-form-item label="银行名称" prop="bankName">
          <el-input v-model="editForm.bankName" placeholder="请输入银行名称" />
        </el-form-item>

        <el-form-item label="支行名称" prop="subbranchName">
          <el-input v-model="editForm.subbranchName" placeholder="请输入支行名称" />
        </el-form-item>

        <el-form-item label="银行账号" prop="bankAccount">
          <el-input v-model="editForm.bankAccount" placeholder="请输入银行账号" />
        </el-form-item>

        <el-form-item label="发票抬头" prop="invoiceTitle">
          <el-input v-model="editForm.invoiceTitle" placeholder="请输入发票抬头" />
        </el-form-item>

        <el-form-item label="发票税号" prop="invoiceTaxNo">
          <el-input v-model="editForm.invoiceTaxNo" placeholder="请输入发票税号" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div style="padding: 20px; text-align: right; border-top: 1px solid #e4e7ed;">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitEdit">保存</el-button>
        </div>
      </template>
    </el-drawer>

    <!-- 资质管理抽屉 -->
    <el-drawer v-model="licenseDialogVisible" title="资质管理" size="800px" direction="rtl">
      <div style="padding: 0 20px">
        <div style="margin-bottom: 20px;">
          <el-button type="primary" @click="openLicenseForm">
            <el-icon>
              <Plus />
            </el-icon>
            添加资质
          </el-button>
        </div>

        <div v-if="licenseList.length === 0" style="text-align: center; color: #999; padding: 40px 0;">
          暂无资质文件
        </div>

        <div v-else>
          <el-table :data="licenseList" style="width: 100%">
            <el-table-column prop="licenseName" label="资质名称" min-width="120" />
            <el-table-column prop="licenseType" label="资质类型" width="100" />
            <el-table-column prop="licenseNumber" label="证书编号" min-width="120" />
            <el-table-column label="资质文件" width="100" align="center">
              <template #default="{ row }">
                <el-image :src="row.filePath" style="width: 60px; height: 45px; border-radius: 4px;" fit="cover"
                  :preview-src-list="[row.filePath]" :preview-teleported="true" />
              </template>
            </el-table-column>
            <el-table-column prop="expireDate" label="有效期至" width="100" />
            <el-table-column prop="status" label="状态" width="80" align="center">
              <template #default="{ row }">
                <el-tag :type="getLicenseStatusType(row.status)">
                  {{ getLicenseStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" align="center">
              <template #default="{ row }">
                <el-button link type="primary" size="small" @click="editLicense(row)">编辑</el-button>
                <el-button link type="danger" size="small" @click="deleteLicense(row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-drawer>

    <!-- 资质编辑弹窗 -->
    <el-dialog v-model="licenseFormVisible" :title="licenseFormTitle" width="600px">
      <el-form :model="licenseForm" :rules="licenseRules" ref="licenseFormRef" label-width="120px">
        <el-form-item label="资质名称" prop="licenseName">
          <el-input v-model="licenseForm.licenseName" placeholder="请输入资质名称" />
        </el-form-item>

        <el-form-item label="资质类型" prop="licenseType">
          <el-select v-model="licenseForm.licenseType" placeholder="请选择资质类型" style="width: 100%">
            <el-option label="营业执照" value="营业执照" />
            <el-option label="税务登记证" value="税务登记证" />
            <el-option label="组织机构代码证" value="组织机构代码证" />
            <el-option label="开户许可证" value="开户许可证" />
            <el-option label="质量管理体系认证" value="质量管理体系认证" />
            <el-option label="环境管理体系认证" value="环境管理体系认证" />
            <el-option label="职业健康安全管理体系认证" value="职业健康安全管理体系认证" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>

        <el-form-item label="证书编号" prop="licenseNumber">
          <el-input v-model="licenseForm.licenseNumber" placeholder="请输入证书编号" />
        </el-form-item>

        <el-form-item label="颁发机构" prop="issuingAuthority">
          <el-input v-model="licenseForm.issuingAuthority" placeholder="请输入颁发机构" />
        </el-form-item>

        <el-form-item label="颁发日期" prop="issueDate">
          <el-date-picker v-model="licenseForm.issueDate" type="date" placeholder="请选择颁发日期" style="width: 100%" />
        </el-form-item>

        <el-form-item label="有效期至" prop="expireDate">
          <el-date-picker v-model="licenseForm.expireDate" type="date" placeholder="请选择有效期至" style="width: 100%" />
        </el-form-item>

        <el-form-item label="资质文件" prop="filePath">
          <div style="display: flex; align-items: center; gap: 10px;">
            <el-button @click="selectLicenseFile">选择文件</el-button>
            <el-image v-if="licenseForm.filePath" :src="licenseForm.filePath"
              style="width: 60px; height: 45px; border-radius: 4px;" fit="cover" />
            <span v-else style="color: #999;">未选择文件</span>
          </div>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="licenseForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="licenseFormVisible = false">取消</el-button>
          <el-button type="primary" @click="submitLicenseForm">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 图片选择器 -->
    <ImageSelector v-model:visible="imageSelectorVisible" title="选择资质文件" :multiple="false" @select="handleImageSelect"
      @cancel="imageSelectorVisible = false" />

    <!-- 用户管理抽屉 -->
    <el-drawer v-model="userDialogVisible" title="用户管理" size="1000px" direction="rtl">
      <div style="padding: 0 20px">
        <div v-if="userList.length === 0" style="text-align: center; color: #999; padding: 40px 0;">
          暂无用户账号
        </div>

        <div v-else>
          <el-table :data="userList" style="width: 100%">
            <el-table-column prop="username" label="用户名" min-width="120" />
            <el-table-column prop="mobile" label="手机号" width="120" />
            <el-table-column prop="email" label="邮箱" min-width="150">
              <template #default="{ row }">
                <span>{{ row.email || '暂无' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="80" align="center">
              <template #default="{ row }">
                <el-tag :type="row.status === 1 ? 'success' : 'danger'">
                  {{ row.status === 1 ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="180" />
            <el-table-column label="操作" width="120" align="center">
              <template #default="{ row }">
                <el-button link type="primary" size="small" @click="resetPassword(row)">重置密码</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { debounce } from 'lodash'
import { Plus } from '@element-plus/icons-vue'
import ImageSelector from '@/components/ImageSelector/index.vue'
import { mobileRule, emailRule } from '@/utils/validation'
import { SUPPLIER_LOGIN_URL } from '@/service/request/config'
import {
  getSupplierList,
  getSupplierDetail,
  addSupplier,
  updateSupplier,
  updateSupplierStatus,
  getSupplierLicenseList,
  addSupplierLicense,
  deleteSupplierLicense,
  getSupplierUsers,
  resetSupplierUserPassword,
  supplierSuperLogin,
  type ISupplier,
  type ISupplierLicense,
  type ISupplierUser
} from '@/api/biz/supplier'

const supplierList = ref<ISupplier[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const searchKeyword = ref('')
const loading = ref(false)

// 编辑弹窗
const editDialogVisible = ref(false)
const editDialogTitle = ref('新增供应商')
const editForm = reactive<Partial<ISupplier>>({
  id: undefined,
  name: '',
  usciCode: '',
  companyId: undefined,
  legalPerson: '',
  contactName: '',
  contactMobile: '',
  contactEmail: '',
  address: '',
  bankName: '',
  subbranchName: '',
  bankAccount: '',
  invoiceTitle: '',
  invoiceTaxNo: '',
  remark: '',
  status: 1
})
const editFormRef = ref()
const editRules = {
  name: [{ required: true, message: '请输入供应商名称', trigger: 'blur' }],
  usciCode: [{ required: true, message: '请输入统一社会信用代码', trigger: 'change' }],
  contactName: [{ required: true, message: '请输入联系人姓名', trigger: 'blur' }],
  contactMobile: [
    { required: true, message: '请输入联系人手机', trigger: 'blur' },
    mobileRule
  ],
  contactEmail: [emailRule]
}

// 资质管理
const licenseDialogVisible = ref(false)
const currentSupplier = ref<ISupplier | null>(null)
const licenseList = ref<ISupplierLicense[]>([])

// 资质表单
const licenseFormVisible = ref(false)
const licenseFormTitle = ref('新增资质')
const licenseForm = reactive<Partial<ISupplierLicense>>({
  id: undefined,
  supplierId: 0,
  licenseName: '',
  licenseType: '',
  licenseNumber: '',
  filePath: '',
  issueDate: '',
  expireDate: '',
  issuingAuthority: '',
  status: 1,
  remark: ''
})
const licenseFormRef = ref()
const licenseRules = {
  licenseName: [{ required: true, message: '请输入资质名称', trigger: 'blur' }],
  licenseType: [{ required: true, message: '请选择资质类型', trigger: 'change' }],
  filePath: [{ required: true, message: '请选择资质文件', trigger: 'blur' }]
}

// 图片选择器
const imageSelectorVisible = ref(false)

// 用户管理
const userDialogVisible = ref(false)
const currentSupplierForUser = ref<ISupplier | null>(null)
const userList = ref<ISupplierUser[]>([])

// 获取文件名
const getFileName = (filePath: string) => {
  return filePath.split('/').pop() || '未知文件'
}

// 获取资质状态类型
const getLicenseStatusType = (status: number) => {
  switch (status) {
    case 1: return 'success'
    case 2: return 'warning'
    case 3: return 'danger'
    default: return 'info'
  }
}

// 获取资质状态文本
const getLicenseStatusText = (status: number) => {
  switch (status) {
    case 1: return '有效'
    case 2: return '即将过期'
    case 3: return '已过期'
    default: return '未知'
  }
}

// 打开图片选择器
const openImageSelector = () => {
  imageSelectorVisible.value = true
}

// 处理图片选择
const handleImageSelect = async (image: { id: number, url: string, name: string }) => {
  if (image && image.url) {
    // 如果是在资质表单中选择文件，则设置到表单中
    if (licenseFormVisible.value) {
      licenseForm.filePath = image.url
      imageSelectorVisible.value = false
      return
    }

    // 如果是直接添加资质文件（保留原有逻辑，但现在需要完整信息）
    if (currentSupplier.value) {
      try {
        await addSupplierLicense({
          supplierId: currentSupplier.value.id!,
          licenseName: '资质文件',
          licenseType: '其他',
          filePath: image.url,
          status: 1
        } as ISupplierLicense)
        ElMessage.success('资质文件添加成功')
        fetchLicenseList()
      } catch (error) {
        console.error('添加资质文件失败:', error)
        ElMessage.error('添加资质文件失败')
      }
    }
  }
  imageSelectorVisible.value = false
}

// 创建防抖搜索函数
const debouncedFetchSupplierList = debounce(() => {
  currentPage.value = 1 // 搜索时重置到第一页
  fetchSupplierList()
}, 600)

const fetchSupplierList = async () => {
  loading.value = true
  try {
    const res = await getSupplierList({
      page: currentPage.value,
      size: pageSize.value,
      query: searchKeyword.value
    })
    supplierList.value = res.data.records
    total.value = res.data.total
  } catch (error) {
    console.error('获取供应商列表失败:', error)
    ElMessage.error('获取供应商列表失败')
  } finally {
    loading.value = false
  }
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchSupplierList()
}

// 打开编辑弹窗
const openEditDialog = async (row?: ISupplier) => {
  if (row && row.id) {
    editDialogTitle.value = '编辑供应商'
    try {
      const res = await getSupplierDetail(row.id)
      // 直接赋值整个对象
      Object.assign(editForm, res.data)
    } catch (error) {
      console.error('获取供应商详情失败:', error)
      ElMessage.error('获取供应商详情失败')
    }
  } else {
    editDialogTitle.value = '新增供应商'
    Object.assign(editForm, {
      id: undefined,
      name: '',
      usciCode: '',
      companyId: undefined,
      legalPerson: '',
      contactName: '',
      contactMobile: '',
      contactEmail: '',
      address: '',
      bankName: '',
      subbranchName: '',
      bankAccount: '',
      invoiceTitle: '',
      invoiceTaxNo: '',
      remark: '',
      status: 1
    })
  }
  editDialogVisible.value = true
}

// 保存供应商
const submitEdit = async () => {
  await editFormRef.value.validate()
  let res
  if (editForm.id) {
    res = await updateSupplier(editForm.id, editForm)
  } else {
    res = await addSupplier(editForm as ISupplier)
  }
  if (res.code === 200) {
    if (editForm.id) {
      ElMessage.success('更新成功')
    } else {
      ElMessage.success('添加成功')
    }
    editDialogVisible.value = false
    fetchSupplierList()
  }
}

// 切换状态
const toggleStatus = async (row: ISupplier) => {
  try {
    await updateSupplierStatus(row.id!, row.status)
    ElMessage.success(row.status === 1 ? '已启用' : '已禁用')
  } catch (error) {
    console.error('更新状态失败:', error)
    ElMessage.error('更新状态失败')
    // 恢复原状态
    row.status = row.status === 1 ? 0 : 1
  }
}

// 打开资质管理弹窗
const openLicenseDialog = (row: ISupplier) => {
  currentSupplier.value = row
  licenseDialogVisible.value = true
  fetchLicenseList()
}

// 获取资质列表
const fetchLicenseList = async () => {
  if (!currentSupplier.value?.id) return
  try {
    const res = await getSupplierLicenseList(currentSupplier.value.id)
    licenseList.value = res.data
  } catch (error) {
    console.error('获取资质列表失败:', error)
    ElMessage.error('获取资质列表失败')
  }
}

// 删除资质
const deleteLicense = async (license: ISupplierLicense) => {
  try {
    await ElMessageBox.confirm('确定要删除这个资质文件吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await deleteSupplierLicense(license.id!)
    ElMessage.success('删除成功')
    fetchLicenseList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除资质失败:', error)
      ElMessage.error('删除资质失败')
    }
  }
}

// 处理搜索输入变化
const handleSearchChange = () => {
  debouncedFetchSupplierList()
}

// 处理搜索清空
const handleSearchClear = () => {
  searchKeyword.value = ''
  currentPage.value = 1
  fetchSupplierList()
}

// 打开资质表单
const openLicenseForm = () => {
  licenseFormTitle.value = '新增资质'
  Object.assign(licenseForm, {
    id: undefined,
    supplierId: currentSupplier.value?.id || 0,
    licenseName: '',
    licenseType: '',
    licenseNumber: '',
    filePath: '',
    issueDate: '',
    expireDate: '',
    issuingAuthority: '',
    status: 1,
    remark: ''
  })
  licenseFormVisible.value = true
}

// 编辑资质
const editLicense = (row: ISupplierLicense) => {
  licenseFormTitle.value = '编辑资质'
  Object.assign(licenseForm, row)
  licenseFormVisible.value = true
}

// 选择资质文件
const selectLicenseFile = () => {
  imageSelectorVisible.value = true
}

// 提交资质表单
const submitLicenseForm = async () => {
  await licenseFormRef.value.validate()
  try {
    if (licenseForm.id) {
      // 更新资质（需要添加更新API）
      ElMessage.success('更新成功')
    } else {
      await addSupplierLicense(licenseForm as ISupplierLicense)
      ElMessage.success('添加成功')
    }
    licenseFormVisible.value = false
    fetchLicenseList()
  } catch (error) {
    console.error('保存资质失败:', error)
    ElMessage.error('保存资质失败')
  }
}

// 打开用户对话框
const openUserDialog = (row: ISupplier) => {
  currentSupplierForUser.value = row
  userDialogVisible.value = true
  fetchUserList()
}

// 获取用户列表
const fetchUserList = async () => {
  if (!currentSupplierForUser.value?.id) return
  try {
    const res = await getSupplierUsers(currentSupplierForUser.value.id)
    userList.value = res.data
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  }
}

// 重置密码
const resetPassword = async (user: ISupplierUser) => {
  await ElMessageBox.confirm('确定要重置该用户的密码吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
  
  const res = await resetSupplierUserPassword(user.id!)
  
  // 显示新密码
  await ElMessageBox.alert(
    `密码重置成功！`,
    '密码重置成功',
    {
      confirmButtonText: '确定',
      type: 'success'
    }
  )
  
  ElMessage.success('密码重置成功')
}

// 处理超级登录
const handleSuperLogin = async (supplier: ISupplier) => {
  // 验证供应商信息
  if (!supplier.id || !supplier.code) {
    ElMessage.error('供应商信息不完整，无法登录')
    return
  }

  if (supplier.status !== 1) {
    ElMessage.error('供应商已禁用，无法登录')
    return
  }

  try {
    // 显示加载状态
    const loadingMessage = ElMessage({
      message: '正在生成登录链接...',
      type: 'info',
      duration: 0
    })

    const res = await supplierSuperLogin(supplier.id)
    const loginCode = res.data
    
    // 关闭加载消息
    loadingMessage.close()
    
    if (!loginCode) {
      ElMessage.error('获取登录凭证失败')
      return
    }
    
    // 构建登录URL - 确保scode参数正确传递到多租户供应商系统
    const loginUrl = `${SUPPLIER_LOGIN_URL}/#?scode=${supplier.code}&loginCode=${loginCode}`
    console.log("构建的登录URL:", loginUrl)
    
    // 在新窗口中打开
    const newWindow = window.open(loginUrl, '_blank')
    
    // 检查是否成功打开新窗口
    if (newWindow) {
      ElMessage.success(`正在为供应商 "${supplier.name}" 打开登录窗口...`)
    } else {
      ElMessage.warning('无法打开新窗口，请检查浏览器弹窗设置')
      // 提供备用方案：复制链接到剪贴板
      if (navigator.clipboard) {
        try {
          await navigator.clipboard.writeText(loginUrl)
          ElMessage.info('登录链接已复制到剪贴板，请手动打开')
        } catch (clipboardError) {
          console.error('复制到剪贴板失败:', clipboardError)
          ElMessage.error('请手动复制以下链接：' + loginUrl)
        }
      }
    }
    
  } catch (error) {
    console.error('超级登录失败:', error)
    ElMessage.error('登录失败，请稍后重试')
  }
}

onMounted(() => {
  fetchSupplierList()
})
</script>

<style scoped lang="scss">
.supplier-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  justify-content: flex-end;
  display: flex;
}

.section-title {
  font-weight: bold;
  font-size: 18px;
}

.contact-tip {
  margin-bottom: 20px;

  :deep(.el-alert) {
    border-radius: 6px;

    .el-alert__title {
      font-size: 13px;
      line-height: 1.4;
    }
  }
}

.license-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  margin-bottom: 12px;
  gap: 12px;

  .license-info {
    flex: 1;

    .license-name {
      font-weight: 500;
      margin-bottom: 4px;
    }

    .license-time {
      font-size: 12px;
      color: #999;
    }
  }
}
</style>