<template>
  <div class="account-wrapper account-change-password">
    <p class="account-wrapper-title">修改密码</p>
    <div class="account-wrapper-inner">
      <el-form @submit.prevent="handleOk">
        <el-form-item
          label="原密码"
          label-position="top"
          :error="keyForErrorMsg[errors['oldPassword']]"
        >
          <el-input v-model="formData.oldPassword" type="password"></el-input>
        </el-form-item>

        <el-form-item
          label="新密码"
          label-position="top"
          :error="keyForErrorMsg[errors['newPassword']]"
        >
          <el-input v-model="formData.newPassword" type="password" @blur="validateNewPassword"></el-input>
        </el-form-item>

        <el-form-item
          label="确认新密码"
          label-position="top"
          :error="keyForErrorMsg[errors['newPasswordConfirm']]"
        >
          <el-input v-model="formData.newPasswordConfirm" type="password" @blur="validateNewPasswordConfirm"></el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="success" native-type="submit" class="save-button">确定</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'
import { ElMessage } from 'element-plus'
import { changePasswordApi } from '@/api/auth.ts'

export default defineComponent({
  name: 'ChangePassword',
  setup() {
    const formData = ref({
      oldPassword: '',
      newPassword: '',
      newPasswordConfirm: ''
    })

    const errors = ref({
      oldPassword: '',
      newPassword: '',
      newPasswordConfirm: ''
    })

    const keyForErrorMsg: Record<string, string> = {
      oldPwdRequired: '请输入原密码',
      valueRequired: '请输入新密码',
      pwdLength: '密码长度必须在6-20位之间',
      newPwdSame: '新密码不能与原密码相同',
      newPwdNotSame: '两次输入的新密码不一致'
    }

    const validateOldPassword = () => {
      if (!formData.value.oldPassword) {
        errors.value.oldPassword = 'oldPwdRequired'
      } else {
        errors.value.oldPassword = ''
      }
    }

    const validateNewPassword = () => {
      const password = formData.value.newPassword
      if (!password) {
        errors.value.newPassword = 'valueRequired'
      } else if (password.length < 6 || password.length > 20) {
        errors.value.newPassword = 'pwdLength'
      } else if (password === formData.value.oldPassword) {
        errors.value.newPassword = 'newPwdSame'
      } else if (
        formData.value.newPasswordConfirm !== '' &&
        formData.value.newPasswordConfirm !== formData.value.newPassword
      ) {
        errors.value.newPasswordConfirm = 'newPwdNotSame'
      } else {
        errors.value.newPassword = ''
        errors.value.newPasswordConfirm = ''
      }
    }

    const validateNewPasswordConfirm = () => {
      if (formData.value.newPassword !== formData.value.newPasswordConfirm) {
        errors.value.newPasswordConfirm = 'newPwdNotSame'
      } else {
        errors.value.newPasswordConfirm = ''
      }
    }

    const handleOk = async() => {
      validateOldPassword()
      validateNewPassword()
      validateNewPasswordConfirm()

      if (errors.value.oldPassword || errors.value.newPassword || errors.value.newPasswordConfirm) {
        return
      }
      // 调用接口
      const res = await changePasswordApi({
        oldPassword: formData.value.oldPassword,
        newPassword: formData.value.newPassword
      })
      if(res.code === 200){
        ElMessage.success('密码修改成功')
      }
    }

    return {
      formData,
      errors,
      keyForErrorMsg,
      validateOldPassword,
      validateNewPassword,
      validateNewPasswordConfirm,
      handleOk
    }
  }
})
</script>

<style lang="scss" scoped>
@import './common.scss';
.account-change-password {
  .save-button {
    width: 100%;
    background-color: var(--el-color-primary);
    color: var(--el-color-white);
  }
}
</style>