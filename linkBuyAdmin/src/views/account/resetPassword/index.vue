<template>
  <div class="reset-password-container">
    <el-card class="reset-password-card">
      <template #header>
        <h2>重置密码</h2>
      </template>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        @submit.prevent="handleSubmit"
      >
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入注册邮箱" />
        </el-form-item>
        <el-form-item label="验证码" prop="verificationCode">
          <div class="verification-code-container">
            <el-input v-model="form.verificationCode" placeholder="请输入验证码" />
            <el-button
              type="primary"
              :disabled="isCodeSending"
              @click="handleSendCode"
            >
              {{ codeButtonText }}
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="form.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="form.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" native-type="submit" :loading="loading">
            重置密码
          </el-button>
          <el-button @click="$router.push('/login')">返回登录</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { useRouter } from 'vue-router'

const router = useRouter()
const formRef = ref<FormInstance>()
const loading = ref(false)
const isCodeSending = ref(false)
const countdown = ref(60)
const codeButtonText = ref('发送验证码')

const form = reactive({
  email: '',
  verificationCode: '',
  newPassword: '',
  confirmPassword: ''
})

const validatePass = (rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请输入密码'))
  } else {
    if (form.confirmPassword !== '') {
      formRef.value?.validateField('confirmPassword')
    }
    callback()
  }
}

const validatePass2 = (rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== form.newPassword) {
    callback(new Error('两次输入密码不一致!'))
  } else {
    callback()
  }
}

const rules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  verificationCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { min: 6, max: 6, message: '验证码长度应为6位', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, validator: validatePass, trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, validator: validatePass2, trigger: 'blur' }
  ]
}

const startCountdown = () => {
  isCodeSending.value = true
  countdown.value = 60
  const timer = setInterval(() => {
    countdown.value--
    codeButtonText.value = `${countdown.value}秒后重试`
    if (countdown.value <= 0) {
      clearInterval(timer)
      isCodeSending.value = false
      codeButtonText.value = '发送验证码'
    }
  }, 1000)
}

const handleSendCode = async () => {
  try {
    await formRef.value?.validateField('email')
    // TODO: 调用发送验证码API
    // const res = await sendVerificationCode(form.email)
    // if (res.code === 200) {
    //   ElMessage.success('验证码已发送到您的邮箱')
    //   startCountdown()
    // } else {
    //   ElMessage.error(res.message || '发送验证码失败')
    // }
    ElMessage.success('验证码已发送到您的邮箱（模拟）')
    startCountdown()
  } catch (error) {
    // 邮箱验证失败
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    // TODO: 调用重置密码API
    // const res = await resetPassword(form)
    // if (res.code === 200) {
    //   ElMessage.success('密码重置成功，请使用新密码登录')
    //   router.push('/login')
    // } else {
    //   ElMessage.error(res.message || '重置密码失败')
    // }
    ElMessage.success('密码重置成功，请使用新密码登录（模拟）')
    router.push('/login')
  } catch (error) {
    // 表单验证失败
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
.reset-password-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f7fa;
  
  .reset-password-card {
    width: 500px;
    
    :deep(.el-card__header) {
      text-align: center;
      
      h2 {
        margin: 0;
        font-size: 24px;
        color: #303133;
      }
    }
    
    .verification-code-container {
      display: flex;
      gap: 10px;
      
      .el-input {
        flex: 1;
      }
      
      .el-button {
        width: 120px;
      }
    }
  }
}
</style> 