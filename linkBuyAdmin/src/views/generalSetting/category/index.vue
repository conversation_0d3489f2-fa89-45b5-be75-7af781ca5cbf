<template>
    <el-container class="category-container">
        <!-- 左侧分类树 -->
        <el-aside width="30%" class="category-tree">
        <div class="tree-header">
          <el-button type="primary" @click="handleAddRoot">
            <el-icon>
              <Plus />
                    </el-icon>添加根分类
          </el-button>
        </div>
            <el-tree ref="categoryTree" :data="categoryList" :props="defaultProps" node-key="id" draggable
          @node-click="handleNodeClick" @node-drop="handleNodeDrop">
          <template #default="{ node, data }">
            <div class="custom-tree-node">
                        <span class="node-content">
                            <el-avatar v-if="data.image" :src="data.image" :size="20" class="category-avatar" />
                            <el-icon v-else class="default-icon">
                                <Folder />
                </el-icon>
                {{ node.label }}
              </span>
              <span class="node-actions">
                <el-button type="primary" link @click.stop="handleAddChild(data)">
                  <el-icon>
                    <Plus />
                  </el-icon>
                </el-button>
                <el-button type="danger" link @click.stop="handleDelete(data)">
                  <el-icon>
                    <Delete />
                  </el-icon>
                </el-button>
              </span>
            </div>
          </template>
        </el-tree>
      </el-aside>
  
      <!-- 右侧属性表单 -->
        <el-main class="category-form">
            <template v-if="currentCategory">
                <el-form ref="categoryForm" :model="currentCategory" :rules="rules" label-width="126px"
                    label-position="left" class="form-container">
                    <el-form-item label="分类名称" prop="name">
                        <el-input v-model="currentCategory.name" placeholder="请输入分类名称" />
            </el-form-item>
  
                    <el-form-item label="分类编码" prop="code">
                        <el-input v-model="currentCategory.code" placeholder="请输入分类编码" />
            </el-form-item>
  
                    <el-form-item label="分类图片" prop="image" required>
                        <div class="image-upload-container">
                            <div class="image-preview" @click="openImageSelector">
                                <img v-if="currentCategory.image" :src="currentCategory.image" class="uploaded-image" />
                                <div v-else class="image-placeholder">
                                    <el-icon>
                                        <Plus />
                                    </el-icon>
                                    <span>选择图片</span>
                                </div>
                                <div v-if="currentCategory.image" class="image-actions">
                                    <el-button type="danger" circle size="small" @click.stop="removeImage">
                                        <el-icon :size="15">
                                            <Delete />
                                        </el-icon>
                                    </el-button>
                                </div>
                            </div>
                            <div class="image-tips">
                                <p><span class="required-mark">*</span> 分类图片为必填项</p>
                                <p>建议尺寸：120x120像素</p>
                                <p>支持格式：JPG、PNG、GIF</p>
                                <p>文件大小：不超过10KB</p>
                            </div>
                        </div>
            </el-form-item>
  
                    <el-form-item label="分类描述" prop="description">
                        <el-input v-model="currentCategory.description" type="textarea" :rows="3"
                            placeholder="请输入分类描述" />
            </el-form-item>
  
            <el-form-item label="排序号" prop="sortOrder">
                        <el-input-number v-model="currentCategory.sortOrder" :min="0" :max="999" />
            </el-form-item>
  
            <el-form-item>
              <el-button type="primary" @click="handleSave">保存</el-button>
                        <el-button @click="handleReset">重置</el-button>
            </el-form-item>
          </el-form>
        </template>
        <div v-else class="empty-tip">
                请选择或创建一个分类
        </div>
      </el-main>
    </el-container>

    <!-- 图片选择器 -->
    <ImageSelector v-model:visible="imageSelectorVisible" title="选择分类图片" :multiple="false" :max-count="1"
        :initial-selected="currentCategory?.image ? [{ id: 0, url: currentCategory.image, name: '当前图片' }] : []"
        @select="handleImageSelect" @cancel="handleImageCancel" />
  </template>
  
  <script setup>
  import { ref, reactive, onMounted, watch } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Delete, Folder } from '@element-plus/icons-vue'
  import { useRoute } from 'vue-router'
import ImageSelector from '@/components/ImageSelector/index.vue'
import { getAllCategory, saveCategory, getCategoryDetail, updateCategoryStructure, deleteCategory } from '@/api/category'
  
  // 获取路由参数
  const route = useRoute()
const categoryType = ref(String(route.query.categoryType || 'product'))
console.log("categoryType.value", categoryType.value)
  
  // 监听路由参数变化
watch(() => route.query.categoryType, (newCategoryType) => {
    if (newCategoryType && newCategoryType !== categoryType.value) {
        categoryType.value = String(newCategoryType)
        console.log("categoryType changed to:", categoryType.value)
        // 清空当前选中的分类
        currentCategory.value = null
        // 刷新分类树
        getCategoryTree()
    }
  }, { immediate: false })
  
// 分类树配置
  const defaultProps = {
    children: 'children',
    label: 'name'
  }
  
// 分类列表数据
const categoryList = ref([])
const currentCategory = ref(null)
const categoryTree = ref(null)
const categoryForm = ref(null)

// 图片选择器
const imageSelectorVisible = ref(false)

// 自定义图片验证器
const validateImage = (rule, value, callback) => {
    if (!value || value.trim() === '') {
        callback(new Error('请选择分类图片'))
    } else {
        callback()
    }
}
  
  // 表单验证规则
  const rules = {
    name: [
        { required: true, message: '请输入分类名称', trigger: 'blur' },
      { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
    ],
    code: [
        { required: true, message: '请输入分类编码', trigger: 'blur' },
        { pattern: /^[a-zA-Z0-9_-]+$/, message: '编码只能包含字母、数字、下划线和横线', trigger: 'blur' }
    ],
    image: [
        { required: true, message: '请选择分类图片', trigger: 'change' },
        { validator: validateImage, trigger: 'change' }
    ],
    sortOrder: [
      { required: true, message: '请输入排序号', trigger: 'blur' }
    ]
  }
  
// 获取分类树数据
const getCategoryTree = async () => {
    const res = await getAllCategory(categoryType.value)
    if (res.code === 200) {
        categoryList.value = res.data
    }
  }
  
// 添加根分类
  const handleAddRoot = () => {
    currentCategory.value = {
      name: '',
      code: '',
        image: '',
        description: '',
      sortOrder: 0,
      parentId: null
    }
  }
  
// 添加子分类
  const handleAddChild = (parent) => {
    // 检查父分类层级
    const getCategoryLevel = (categoryId) => {
      let level = 1
        let currentId = categoryId
      const findParent = (id) => {
            for (const category of categoryList.value) {
                if (category.id === id) {
                    return category
          }
                if (category.children) {
                    const found = findInChildren(category.children, id)
            if (found) return found
          }
        }
        return null
      }
      const findInChildren = (children, id) => {
        for (const child of children) {
          if (child.id === id) {
            return child
          }
          if (child.children) {
            const found = findInChildren(child.children, id)
            if (found) return found
          }
        }
        return null
      }
  
      while (currentId) {
            const category = findParent(currentId)
            if (category && category.parentId) {
          level++
                currentId = category.parentId
        } else {
          break
        }
      }
      return level
    }
  
    const parentLevel = getCategoryLevel(parent.id)
    if (parentLevel >= 3) {
        ElMessage.error('分类最多支持三级')
      return
    }
  
    currentCategory.value = {
      name: '',
      code: '',
        image: '',
        description: '',
      sortOrder: 0,
      parentId: parent.id
    }
  }
  
// 点击分类节点
  const handleNodeClick = (data) => {
    currentCategory.value = { ...data }
}

// 打开图片选择器
const openImageSelector = () => {
    console.log('打开图片选择器，当前图片:', currentCategory.value?.image)
    imageSelectorVisible.value = true
}

// 处理图片选择
const handleImageSelect = (image) => {
    console.log('选择的图片:', image)

    if (!image) {
        ElMessage.warning('未选择任何图片')
        return
    }

    // 兼容不同的返回格式
    let imageUrl = ''

    if (typeof image === 'string') {
        // 如果直接返回URL字符串
        imageUrl = image
    } else if (typeof image === 'object') {
        // 如果返回对象，尝试获取URL
        imageUrl = image.url || image.path || image.src || image.link
    }

    if (imageUrl) {
        currentCategory.value.image = imageUrl
        // ElMessage.success('图片选择成功')
        // console.log('设置图片URL:', imageUrl)

        // 触发表单验证
        if (categoryForm.value) {
            categoryForm.value.validateField('image')
        }
    } else {
        ElMessage.error('无法获取图片URL，请重新选择')
        console.error('图片数据格式异常:', image)
    }
}

// 处理图片选择器取消
const handleImageCancel = () => {
    console.log('取消选择图片')
    imageSelectorVisible.value = false
}

// 移除图片
const removeImage = () => {
    currentCategory.value.image = ''
    ElMessage.info('已移除图片')

    // 触发表单验证
    if (categoryForm.value) {
        categoryForm.value.validateField('image')
    }
  }
  
// 保存分类
  const handleSave = async () => {
    if (!categoryForm.value) return
  
    await categoryForm.value.validate(async (valid) => {
      if (valid) {
            // 额外检查图片是否已选择
            if (!currentCategory.value.image) {
                ElMessage.error('请选择分类图片')
                return
            }
            
            console.log('保存分类:', currentCategory.value)
            const res = await saveCategory(currentCategory.value, categoryType.value)
            if (res.code === 200) {
        ElMessage.success('保存成功')
                // 更新当前分类数据，包括服务器返回的ID等信息
                if (res.data) {
                    currentCategory.value = { ...res.data }
                }
                getCategoryTree() // 刷新分类树
      }
        } else {
            ElMessage.error('请完善分类信息')
        }
    })
}

// 重置表单
const handleReset = () => {
    if (categoryForm.value) {
        categoryForm.value.resetFields()
    }
  }
  
  // 处理拖拽排序
  const handleNodeDrop = async (draggingNode, dropNode, dropType, ev) => {
      console.log('拖拽节点:', draggingNode.data)
      console.log('目标节点:', dropNode ? dropNode.data : 'root')
      console.log('拖拽类型:', dropType)
  
      // 检查目标位置的层级
      const getTargetLevel = (node) => {
        let level = 1
        let current = node
        while (current.parent && current.parent.key !== undefined) {
          level++
          current = current.parent
        }
        return level
      }
  
      // 检查拖拽后的层级
      if (dropType === 'inner') {
        const targetLevel = getTargetLevel(dropNode)
        if (targetLevel >= 3) {
            ElMessage.error('分类最多支持三级')
          // 恢复拖拽前的状态
            getCategoryTree()
          return
        }
      }
  
      // 构建需要更新的数据
    const categoryStructures = []
  
    // 从更新后的分类树构建数据
      const buildStructureData = (nodes, parentId = null) => {
        nodes.forEach((node, index) => {
            categoryStructures.push({
            id: node.id,
            parentId: parentId,
            sortOrder: index
          })
  
          if (node.children && node.children.length > 0) {
            buildStructureData(node.children, node.id)
          }
        })
      }
  
    // 从当前的分类树构建更新数据
    buildStructureData(categoryList.value)
  
    console.log('更新的分类结构:', categoryStructures)
  
    // 批量更新分类结构和排序
    const res = await updateCategoryStructure(categoryStructures, categoryType.value)
    if (res.code === 200) {
        ElMessage.success('分类结构更新成功')
        getCategoryTree() // 刷新分类树
    } else {
        // 失败时恢复状态
        getCategoryTree()
    }
  }
  
// 删除分类
  const handleDelete = async (node) => {
    await ElMessageBox.confirm('确定要删除该分类吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
  
    // 调用API删除分类
    const res = await deleteCategory(node.id, categoryType.value)
    if (res.code === 200) {
      ElMessage.success('删除成功')
        getCategoryTree() // 刷新分类树
        if (currentCategory.value?.id === node.id) {
            currentCategory.value = null
      }
    }
  }
  
  // 初始化
  onMounted(() => {
    getCategoryTree()
  })
  </script>
  
  <style lang="scss" scoped>
.category-container {
    padding: 20px;
    background-color: var(--el-bg-color-base);
    height: calc(100vh - 60px);
    overflow: hidden;
  }
  
.category-header {
    background-color: var(--el-color-white);
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 20px;
  }
  
  .header-title {
    font-size: 18px;
    color: var(--el-text-color-primary);
    margin-bottom: 20px;
  }
  
  .search-form {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
  }
  
.category-table {
    background-color: var(--el-color-white);
    padding: 20px;
    border-radius: 4px;
  }
  
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .table-title {
    font-size: 16px;
    color: var(--el-text-color-primary);
  }
  
  .table-actions {
    display: flex;
    gap: 10px;
  }
  
  .action-button {
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
  
    &.primary {
      background-color: var(--el-color-primary);
      color: var(--el-color-white);
  
      &:hover {
        background-color: var(--el-color-primary-light-1);
      }
    }
  
    &.default {
      background-color: var(--el-color-white);
      border: 1px solid var(--el-border-color-base);
      color: var(--el-text-color-primary);
  
      &:hover {
        border-color: var(--el-color-primary);
        color: var(--el-color-primary);
      }
    }
  }
  
  .status-tag {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
  
    &.success {
      background-color: var(--el-color-success-light-9);
      color: var(--el-color-success);
    }
  
    &.danger {
      background-color: var(--el-color-danger-light-9);
      color: var(--el-color-danger);
    }
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
  }
  
.category-tree {
    background-color: #fff;
    border-right: 1px solid #eee;
    padding: 20px;
    height: calc(100vh - 120px);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
  
    .tree-header {
      margin-bottom: 20px;
      flex-shrink: 0;
    }
    
    .el-tree {
      flex: 1;
      overflow-y: auto;
    }
  }
  
.category-form {
    padding: 20px;
    height: calc(100vh - 120px);
    overflow-y: auto;
  
    .form-container {
      width: 600px;
      margin: 0 auto;
      background-color: #fff;
      padding: 20px;
      border-radius: 16px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
  
    .empty-tip {
      text-align: center;
      color: #909399;
      font-size: 14px;
      margin-top: 100px;
    }
  }
  
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
    width: 100%;

    .node-content {
        display: flex;
        align-items: center;
        gap: 8px;

        .category-avatar {
            border-radius: 4px;
        }

        .default-icon {
            color: var(--el-color-primary);
        }
    }
  
    .node-actions {
      display: none;
      margin-left: auto;
  
      .el-button {
        padding: 2px 4px;
        margin-left: 4px;
      }
    }
  
    &:hover .node-actions {
      display: inline-block;
    }
  }

.image-upload-container {
    display: flex;
    gap: 20px;
    align-items: flex-start;

    .image-preview {
        position: relative;
        width: 120px;
        height: 120px;
        border: 1px dashed var(--el-border-color);
        border-radius: 6px;
        cursor: pointer;
        overflow: hidden;
        transition: var(--el-transition-duration-fast);

        &:hover {
            border-color: var(--el-color-primary);

            .image-actions {
                opacity: 1;
            }
        }

        .image-placeholder {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 8px;
            color: var(--el-text-color-secondary);

            .el-icon {
                font-size: 28px;
            }

            span {
                font-size: 12px;
            }
        }

        .image-actions {
            position: absolute;
            top: 4px;
            right: 4px;
            opacity: 0;
            transition: opacity 0.3s;

            .el-button {
                background: rgba(255, 255, 255, 0.9);
                border: none;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                padding: 4px;

                &:hover {
                    background: rgba(255, 255, 255, 1);
                }
            }
        }

        .uploaded-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 6px;
        }
    }

    .image-tips {
        color: var(--el-text-color-secondary);
        font-size: 12px;
        line-height: 1.5;

        p {
            margin: 0 0 4px 0;
        }

        .required-mark {
            color: var(--el-color-danger);
            font-weight: bold;
            margin-right: 2px;
        }
    }
}
  
  :deep(.el-tree-node__content) {
    height: 40px;
  
    &:hover {
      background-color: #f5f7fa;
    }
  }
  
  :deep(.el-form-item__label) {
    font-weight: bold;
  }
  
  :deep(.el-input__wrapper) {
    width: 100%;
  }
  
:deep(.el-textarea__inner) {
    resize: vertical;
}
  </style>