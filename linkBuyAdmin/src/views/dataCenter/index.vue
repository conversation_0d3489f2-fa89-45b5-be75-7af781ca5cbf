<template>
  <div class="data-center-container">
    <div class="header">
      <h2>运营数据</h2>
    </div>
    <div class="content">
      <el-card>
        <div class="data-placeholder">
          <el-icon size="64" color="#ccc">
            <Tickets />
          </el-icon>
          <p>运营数据功能开发中...</p>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Tickets } from '@element-plus/icons-vue'
</script>

<style scoped lang="scss">
.data-center-container {
  padding: 20px;
  
  .header {
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      color: #303133;
    }
  }
  
  .content {
    .data-placeholder {
      text-align: center;
      padding: 60px 20px;
      color: #909399;
      
      p {
        margin-top: 16px;
        font-size: 16px;
      }
    }
  }
}
</style>
