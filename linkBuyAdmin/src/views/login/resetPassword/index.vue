<template>
  <div class="all">
    <loginHeader />
    <div class="nav-header">
      <div class="nav-right">
        <router-link class="nav-link" to="/login">{{ $t('Login') }}</router-link>
      </div>
    </div>
    <div class="login-container">
      <div class="login-box">
        <div>
          <h2 v-if="step == 1" class="login-title">{{ $t('resetPassword.confirmEmail') }}</h2>
          <h2 v-else-if="step == 2" class="login-title">{{ $t('resetPassword.resetPassword') }}</h2>
        </div>
        <form class="login-form" @submit.prevent="handleLogin">
          <div v-if="step==1" class="form-group">
            <div>
              <label for="email" class="input-label">{{ $t('resetPassword.email') }} *</label>
              <input id="email" v-model="email" type="email" required class="input-field" :class="{'error':emailError}"
                 @blur="validateEmail" />
              <div v-if="emailError"
                :class="['error-message','not-registered']">
                  <span v-html="keyForErrorMsg[emailError].value"></span>
              </div>
            </div>
            <div class="password-container">
              <VerificationCodeInput v-model="password" :label="`${$t('resetPassword.verificationCode')} *`"
                 :email="email" :sendType="2" :disabled="!email"
                :inputClass="{ 'no-border-r': true, 'disabled-append': isCounting || !email }" :error="passwordError" />
                <div v-if="passwordError" class="error-message-code">{{ passwordError }}</div>
            </div>
          </div>
          <div v-else-if="step == 2" class="form-group">
            <div>
              <label for="New Password" class="input-label">{{ $t('resetPassword.newPassword') }} *</label>
              <div style="position: relative; width: 400px; ">
                <input
                  :type="showPassword ? 'text' : 'password'"
                  v-model="formData.newPassword"
                  required
                  class="input-field"
                  :class="{ 'error': errors.newPassword }"
                  @blur="validateNewPassword"
                />
                <span
                  @click="showPassword = !showPassword"
                  style="position: absolute; right: -30px; top: 35px; transform: translateY(-50%); cursor: pointer; z-index: 2;"
                >
                  <svg v-if="showPassword" width="16" height="16" viewBox="0 0 26 26" fill="none">
                    <path d="M1 10C3.5 5.5 7.5 3 12.5 3C17.5 3 21.5 5.5 24 10C21.5 14.5 17.5 17 12.5 17C7.5 17 3.5 14.5 1 10Z" stroke="#888" stroke-width="2"/>
                    <ellipse cx="12.5" cy="10" rx="3.5" ry="3" stroke="#888" stroke-width="2"/>
                    <circle cx="12.5" cy="10" r="1.5" fill="#888"/>
                  </svg>
                  <svg v-else width="16" height="16" viewBox="0 0 26 26" fill="none">
                    <path d="M2 2L18 18" stroke="#888" stroke-width="2" stroke-linecap="round"/>
                    <path d="M1 10C2.73 6.11 6.61 3.5 11 3.5C13.13 3.5 15.13 4.13 16.82 5.18M19 10C17.27 13.89 13.39 16.5 9 16.5C6.87 16.5 4.87 15.87 3.18 14.82" stroke="#888" stroke-width="2"/>
                    <circle cx="11" cy="10" r="3" stroke="#888" stroke-width="2"/>
                  </svg>
                </span>
              </div>
              <div v-if="errors.newPassword"
                :class="['error-message', errors.newPassword === 'newPasswordRequired' ? 'invalid-email' : 'not-registered']">
                <template v-if="errors.newPassword === 'newPasswordRequired'">
                  <span>{{ keyForErrorMsg[errors.newPassword].value }}</span>
                </template>
                <template v-else>
                  {{ keyForErrorMsg[errors.newPassword].value }}
                </template>
              </div>
            </div>
            <div>
              <label for="Password Again" class="input-label">{{ $t('resetPassword.passwordAgain') }} *</label>
              <div style="position: relative; width: 400px;">
                <input
                  :type="showPasswordConfirm ? 'text' : 'password'"
                  v-model="formData.newPasswordConfirm"
                  required
                  class="input-field"
                  :class="{ 'error': errors.newPasswordConfirm }"
                  @blur="validateNewPasswordConfirm"
                />
                <span
                  @click="showPasswordConfirm = !showPasswordConfirm"
                  style="position: absolute; right: -30px; top: 35px; transform: translateY(-50%); cursor: pointer; z-index: 2;"
                >
                <svg v-if="showPasswordConfirm" width="16" height="16" viewBox="0 0 26 26" fill="none">
                    <path d="M1 10C3.5 5.5 7.5 3 12.5 3C17.5 3 21.5 5.5 24 10C21.5 14.5 17.5 17 12.5 17C7.5 17 3.5 14.5 1 10Z" stroke="#888" stroke-width="2"/>
                    <ellipse cx="12.5" cy="10" rx="3.5" ry="3" stroke="#888" stroke-width="2"/>
                    <circle cx="12.5" cy="10" r="1.5" fill="#888"/>
                  </svg>
                  <svg v-else width="16" height="16" viewBox="0 0 26 26" fill="none">
                    <path d="M2 2L18 18" stroke="#888" stroke-width="2" stroke-linecap="round"/>
                    <path d="M1 10C2.73 6.11 6.61 3.5 11 3.5C13.13 3.5 15.13 4.13 16.82 5.18M19 10C17.27 13.89 13.39 16.5 9 16.5C6.87 16.5 4.87 15.87 3.18 14.82" stroke="#888" stroke-width="2"/>
                    <circle cx="11" cy="10" r="3" stroke="#888" stroke-width="2"/>
                  </svg>
                </span>
              </div>
              <div v-if="errors.newPasswordConfirm"
                :class="['error-message', errors.newPasswordConfirm === 'newPasswordRequired' ? 'invalid-email' : 'not-registered']">
                <template v-if="errors.newPasswordConfirm === 'newPasswordRequired'">
                  <span>{{ keyForErrorMsg[errors.newPasswordConfirm].value }}</span>
                </template>
                <template v-else>
                  {{ keyForErrorMsg[errors.newPasswordConfirm].value }}
                </template>
              </div>
            </div>
          </div>
          <div v-else-if="step == 3" style="text-align: center">
            <mob-svg-icon class="pass-icon" name="icon-pass"></mob-svg-icon>
            <h2 class="login-title">{{ $t('resetPassword.passwordUpdated') }}</h2>
            <button class="login-button" style="margin-top: 80px" @click="goLogin">
              {{ $t('resetPassword.goAndLogin') }}
            </button>
          </div>

          <button v-if="step !== 3" type="submit" class="login-button">
            {{ $t('resetPassword.continue') }}
          </button>
          <div v-if="step !== 3" class="signup-prompt">
            <p>
              {{ $t('resetPassword.goBackTo') }}
              <router-link to="/login" class="signup-link">{{ $t('Login') }}</router-link>
              {{ $t('resetPassword.page') }}
            </p>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent ,ref,onMounted, computed} from 'vue'
import { useRouter } from 'vue-router'
import LoginHeader from '../components/header.vue'
import { checkEmailExistApi, sendCodeApi, checkEmailCodeApi, resetPasswordApi } from '@/api/auth'
import CryptoJS from 'crypto-js'
import { ElMessage } from 'element-plus'
import VerificationCodeInput from '@/views/account/components/codeInput.vue'
import LanguageSwitch from '@/components/language-switch/index.vue'
import { useI18n } from 'vue-i18n'

export default defineComponent({
  name: 'login',
  props: {},
  components: { LoginHeader, VerificationCodeInput, LanguageSwitch },
  setup() {
    const { t } = useI18n()
    const router = useRouter()
    const email = ref('')
    const emailError = ref('')
    const password = ref('')
    const passwordError = ref('')
    const rememberMe = ref(false)
    const showPassword = ref(false)
    const showPasswordConfirm = ref(false)
    const resendCountdown = ref(0)
    const verifyError=ref('')
    const step=ref(1)
    const formData=ref({
      newPassword: '',
      newPasswordConfirm: ''
    })
    const errors = ref({
      newPassword: '',
      newPasswordConfirm: ''
    })

    const keyForErrorMsg = {
      valueRequired: computed(() => t('resetPassword.valueRequired')),
      emailValid: computed(() => t('resetPassword.emailValid'),),
      emailNotRegistered: computed(() => t('resetPassword.emailNotRegistered', { signupLink: `<a href="/#/register" class="signup-link">${t('resetPassword.signupLink')}</a>` })),
      codeError: computed(() => t('resetPassword.codeError')),
      newPwdValid: computed(() => t('resetPassword.newPwdValid')),
      newPwdLength: computed(() => t('resetPassword.newPwdLength')),
      newPwdNotMatch: computed(() => t('resetPassword.newPwdNotMatch'))
    }


    const startResendCountdown = () => {
      resendCountdown.value = 60
      const timer = setInterval(() => {
        if (resendCountdown.value > 0) {
          resendCountdown.value--
        } else {
          clearInterval(timer)
        }
      }, 1000)
    }


   const validateEmailFormat = (email: string) => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return emailRegex.test(email)
    }

    const checkEmailRegistration = async (email: string) => {
      try {
        const res = await checkEmailExistApi({ email });
        // status === 200 并且 res.res === true 代表已注册
        return res.status === 200 && res.res === true;
      } catch (error) {
        // 网络或接口异常，按未注册处理
        return false;
      }
    }

    const validateEmail = async () => {
      emailError.value = ''

      if (!email.value) {
        emailError.value = 'valueRequired'
        return
      }

      if (!validateEmailFormat(email.value)) {
        emailError.value = 'emailValid'
        return
      }

      const isRegistered = await checkEmailRegistration(email.value)
      if (!isRegistered) {
        emailError.value = 'emailNotRegistered'
      }
    }

    const validatePassword = () => {
      passwordError.value = ''

      if (!password.value){
        passwordError.value = 'valueRequired'
        return
      }
    }


    const resendCode = async () => {
      try {
        // 调用发送验证码的 API
        await sendCodeApi({
          email: email.value,
          type: 2
        })
        startResendCountdown()
      } catch (error) {
        verifyError.value = '验证码发送失败，请稍后重试'
      }
    }

    const handleLogin = async () => {
      if (step.value === 1) {
        // 重置错误信息
        emailError.value = ''
        passwordError.value = ''

        // 检查必填项
        if (!email.value) {
          emailError.value = 'valueRequired'
          return
        }

        if (!password.value) {
          passwordError.value = 'valueRequired'
          return
        }

        // 验证邮箱格式
        if (!validateEmailFormat(email.value)) {
          emailError.value = 'emailValid'
          return
        }
        // 检查邮箱是否注册
        const isRegistered = await checkEmailRegistration(email.value)
        if (!isRegistered) {
          emailError.value = 'emailNotRegistered'
          return
        }

        // 验证邮箱验证码是否正确
        const res = await checkEmailCodeApi({
          email: email.value,
          code: password.value,
          type: 2
        })
        if (res.status !== 200) {
          // passwordError.value = res.message || '验证码错误'
          ElMessage.error(res.message)
          return
        } else {
          // passwordError.value = ''
          step.value = 2
        }
      } else {
        handleReset()
      }

    }
    const validateNewPassword = () => {
      const password = formData.value.newPassword
      if (!password) {
        errors.value.newPassword = 'valueRequired'
      } else if (password.length<8 || password.length>16) {
        errors.value.newPassword = 'newPwdLength'
      } else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) {
        errors.value.newPassword = 'newPwdValid'
      } else if (formData.value.newPasswordConfirm !== '' && (formData.value.newPasswordConfirm !== formData.value.newPassword)) {
        errors.value.newPassword = 'newPwdNotMatch'
      } else {
        errors.value.newPassword = ''
      }
    }

    const validateNewPasswordConfirm = () => {
      if (formData.value.newPassword !== formData.value.newPasswordConfirm) {
        errors.value.newPasswordConfirm = 'newPwdNotMatch'
      } else {
        errors.value.newPasswordConfirm = ''
      }
    }
    const handleReset = async () => {
      validateNewPassword()
      validateNewPasswordConfirm()

      const encryptedPasswordNew = CryptoJS.MD5(formData.value.newPassword).toString();
      if (!errors.value.newPassword && !errors.value.newPasswordConfirm) {
        // 调用接口
        const res = await resetPasswordApi({
          newPassword: encryptedPasswordNew,
          code: password.value,
          email: email.value,
          resetPasswordType: 1
        })
        if (res.status === 200) {
          step.value = 3
        } else {
          ElMessage.error(res.message)
        }
      } else {
        ElMessage.error('Please fix the errors before submitting')
      }
    }
    const goLogin = () => {
      router.push('/login')
    }

   return{
    router,
    email,
    password,
    rememberMe,
    showPassword,
    showPasswordConfirm,
    handleLogin,
    emailError,
    passwordError,
    step,
    validateEmail,
    validatePassword,
    resendCountdown,
    resendCode,
    formData,
    handleReset,
    errors,
    keyForErrorMsg,
    validateNewPassword,
    validateNewPasswordConfirm,
    goLogin
   }
  }
})

</script>

<style lang="scss" scoped>
.all{
  min-height: 100vh;
  width: 100%;
  background-color: var(--el-bg-color-base);
}

.nav-header{
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  height: 60px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0 40px;
}

.nav-right{
  display: flex;
  align-items: center;
  gap: 24px;
}

.nav-link{
  font-family: Helvetica;
  font-size: 14px;
  color: #1C1C1C;
  letter-spacing: 0;
  font-weight: 400;
  text-decoration: none;
  &:hover {
    color: #006C5F;
  }
}

.login-container{
  // background-image: linear-gradient(180deg, #FDFDFD 0%, #EBF1EF 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: calc(100vh - 60px);
}

.login-box{
background: #FDFDFD;
border: 1px solid rgba(255,255,255,1);
box-shadow: 0px 4px 20px 0px rgba(0,0,0,0.1);
border-radius: 20px;
width: 480px;
height: 445px;
}

h2{
font-family: Helvetica-Bold;
font-size: 24px;
color: #1C1C1C;
letter-spacing: 0;
font-weight: 700;
text-align: center;
}

.login-form {
  margin-top: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.input-label {
  display: block;
  font-size: 16px;
  color: #1C1C1C;
  letter-spacing: 0;
  font-weight: 400;
  margin-left: 40px;
  font-family: Helvetica;
}

.input-field {
  width: 400px;
  height: 44px;
  margin-left: 40px;
  margin-top: 10px;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  background: #FFFFFF;
  border: 1px solid rgba(205,210,217,1);
  &:focus {
    outline: none;
    border-color: #006C5F;
    background: rgba(0,108,95,0.06);
    border: 1px solid rgba(0,108,95,1);
    box-shadow: 0 0 0 2px rgba(0, 108, 95, 0.1);
  }
}

.password-container{
  position: relative;
  width: 400px;
  margin: 0 auto;
  margin-bottom: 0 !important;
  .disabled-append {
      :deep(.el-input-group__append) {
        cursor: not-allowed;
        color: gray;
      }
    }
}

.password-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.resend-btn{
  position: absolute;
  top: 70%;
  right: 40px;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  font-family: Helvetica;
  font-size: 14px;
  color: #006C5F;
  letter-spacing: 0;
  text-align: right;
  font-weight: 400;
}


.login-button {
  width: 400px;
  height: 44px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: rgb(13, 148, 136);
  color: white;
  font-size: 16px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  background: #006C5F;
  box-shadow: inset 0px -1px 2px 0px rgba(0,73,64,1);
  box-shadow: inset 0px 1px 1px 0px rgba(28,141,127,1);
  border-radius: 6px;
  margin-left: 40px;

  &:hover{
    background-color: rgb(15, 118, 110);
  }

}


.signup-prompt {
  text-align: center;
  font-family: Helvetica;
  font-size: 14px;
  color: #1C1C1C;
  letter-spacing: 0;
  line-height: 18px;
  font-weight: 400;
}



.input-field.error {
  border-color: #FF4D4F;
  background: rgba(255, 77, 79, 0.06);
}

.error-message {
  margin-left: 40px;
  margin-top: 8px;
  font-family: Helvetica;
  font-size: 13px;
  line-height: 18px;
  position: absolute;
  width: 400px;

  &.invalid-email,
  &.invalid-password
  {
    color: #FF4D4F;
  }
  &.not-registered{
    color: #FF4D4F;
    display: flex;
    align-items: center;
    gap: 4px;

    span{
      display: inline;
    }
  }
}
  :deep(.signup-link) {
    color: #006C5F;
    text-decoration: none;
    margin-left: 4px;
    font-family: Helvetica;
    font-size: 14px;
    line-height: 18px;
    // white-space: nowrap;
    margin-left: 4px;
    display: inline-block;
    &:hover {
      text-decoration: underline;
      color: darken(#006C5F, 10%);
    }
  }

  .pass-icon{
    font-size: 70px;
    display: inline-block;
    margin-top: 30px;
  }


.form-group > div {
  position: relative;
  margin-bottom: 24px;
}

  .error-message-code{
    color: #FF4D4F;
    font-size: 13px;
    margin-top: 4px;
    margin-left: 2px;
    transition: min-height 0.2s;
    // display: flex;
    align-items: center;
  }
</style>
