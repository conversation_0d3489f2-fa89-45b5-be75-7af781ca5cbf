<template>
    <el-drawer v-model="visible" :title="title" size="600px" :before-close="handleClose">
        <div class="permission-container">
            <!-- 搜索区域 -->
            <div class="filter-container">
                <el-input v-model="searchKeyword" placeholder="搜索菜单名称" clearable class="search-input"
                    @input="handleSearch">
                    <template #prefix>
                        <el-icon>
                            <Search />
                        </el-icon>
                    </template>
                </el-input>
            </div>

            <!-- 菜单树 -->
            <div class="tree-container">
                <el-tree ref="menuTreeRef" :data="filteredMenuList" :props="defaultProps" show-checkbox node-key="id"
                    :default-checked-keys="checkedKeys" :default-expanded-keys="expandedKeys"
                    :filter-node-method="filterNode" highlight-current :check-strictly="true">
                    <template #default="{ node, data }">
                        <span class="custom-tree-node">
                            <span class="node-content">
                                <el-icon v-if="data.icon" class="menu-icon">
                                    <component :is="data.icon" />
                                </el-icon>
                                <span class="menu-name">{{ node.label }}</span>
                            </span>
                            <span class="menu-path">{{ data.path }}</span>
                        </span>
                    </template>
                </el-tree>
            </div>

            <!-- 已选菜单统计 -->
            <div class="selected-stats">
                <el-tag type="info">
                    已选择 {{ selectedCount }} 个菜单
                </el-tag>
                <div class="action-buttons">
                    <el-button link type="primary" @click="handleExpandAll">
                        {{ isAllExpanded ? '收起全部' : '展开全部' }}
                    </el-button>
                    <el-button link type="primary" @click="handleSelectAll">
                        全选
                    </el-button>
                    <el-button link type="primary" @click="handleUnselectAll">
                        取消全选
                    </el-button>
                </div>
            </div>
        </div>

        <template #footer>
            <div class="drawer-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="handleSave" :loading="loading">
                    保存 ({{ selectedCount }})
                </el-button>
            </div>
        </template>
    </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { TreeInstance } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { getRolePermissions, updateRolePermissions } from '@/api/role'
import { getAllMenu } from '@/api/common'
import type { MenuDto } from '@/types/menu'

interface Menu extends MenuDto {
    children?: Menu[]
}

const props = defineProps<{
    modelValue: boolean
    roleId?: number
    roleName?: string
}>()

const emit = defineEmits<{
    (e: 'update:modelValue', value: boolean): void
    (e: 'success'): void
}>()

const visible = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val)
})

const title = computed(() => `菜单授权 - ${props.roleName || ''}`)

const menuTreeRef = ref<TreeInstance>()
const menuList = ref<Menu[]>([])
const checkedKeys = ref<string[]>([])
const expandedKeys = ref<string[]>([])
const loading = ref(false)
const searchKeyword = ref('')
const isAllExpanded = ref(true)

const defaultProps = {
    children: 'children',
    label: 'name'
}

// 过滤后的菜单列表
const filteredMenuList = computed(() => {
    if (!searchKeyword.value) return menuList.value

    const keyword = searchKeyword.value.toLowerCase()
    return menuList.value.filter(item =>
        item.name.toLowerCase().includes(keyword) ||
        item.code.toLowerCase().includes(keyword)
    )
})

// 已选菜单数量
const selectedCount = computed(() => {
    return menuTreeRef.value?.getCheckedKeys().length || 0
})

// 获取所有菜单列表
const fetchAllMenus = async () => {
    const res = await getAllMenu()
    if (res.code === 200) {
        menuList.value = res.data
        // 展开所有节点
        expandedKeys.value = menuList.value.map(item => item.id)
    }
}

// 获取角色已分配菜单
const fetchRoleMenus = async () => {
    if (!props.roleId) return

    const res = await getRolePermissions(props.roleId)
    if (res.code === 200) {
        checkedKeys.value = res.data
        menuTreeRef.value?.setCheckedKeys(res.data)
    } else {
        ElMessage.error(res.msg || '获取角色菜单失败')
    }
}

// 处理搜索
const handleSearch = () => {
    if (menuTreeRef.value) {
        menuTreeRef.value.filter(searchKeyword.value)
    }
}

// 过滤节点
const filterNode = (value: string, data: Menu) => {
    if (!value) return true
    const keyword = value.toLowerCase()
    return (
        data.name.toLowerCase().includes(keyword) ||
        data.code.toLowerCase().includes(keyword)
    )
}

// 展开/收起所有节点
const handleExpandAll = () => {
    if (isAllExpanded.value) {
        expandedKeys.value = []
    } else {
        expandedKeys.value = menuList.value.map(item => item.id)
    }
    isAllExpanded.value = !isAllExpanded.value
}

// 全选
const handleSelectAll = () => {
    menuTreeRef.value?.setCheckedKeys(menuList.value.map(item => item.id))
}

// 取消全选
const handleUnselectAll = () => {
    menuTreeRef.value?.setCheckedKeys([])
}

// 监听角色ID变化
watch(() => props.roleId, (newVal) => {
    if (newVal) {
        fetchRoleMenus()
    }
})

// 监听抽屉显示状态
watch(() => props.modelValue, (newVal) => {
    if (newVal) {
        fetchAllMenus()
        console.log(props.roleId)
        if (props.roleId) {
            fetchRoleMenus()
        }
    } else {
        // 重置状态
        searchKeyword.value = ''
        isAllExpanded.value = true
    }
})

// 处理关闭
const handleClose = () => {
    visible.value = false
}

// 处理保存
const handleSave = async () => {
    if (!props.roleId) return

    loading.value = true
    const checkedNodes = menuTreeRef.value?.getCheckedKeys() as string[]
    const res = await updateRolePermissions(props.roleId, checkedNodes)
    if (res.code === 200) {
        ElMessage.success('保存成功')
        emit('success')
        handleClose()
    }
    loading.value = false
}
</script>

<style scoped lang="scss">
.permission-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.filter-container {
    padding: 16px;
    border-bottom: 1px solid #dcdfe6;

    .search-input {
        width: 100%;
    }
}

.tree-container {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
}

.custom-tree-node {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-right: 8px;

    .node-content {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .menu-icon {
        font-size: 16px;
        color: #909399;
    }

    .menu-name {
        margin-right: 8px;
    }

    .menu-path {
        color: #909399;
        font-size: 12px;
    }
}

.selected-stats {
    padding: 12px 16px;
    border-top: 1px solid #dcdfe6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f5f7fa;

    .action-buttons {
        display: flex;
        gap: 8px;
    }
}

.drawer-footer {
    padding: 16px;
    text-align: right;
    border-top: 1px solid #dcdfe6;
}
</style>
