<template>
  <div class="map-setting">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="腾讯地图" name="tencent">
        <div class="map-setting-card">
          <div class="map-setting-title">地图设置</div>
          <el-form :model="form" label-width="126px" label-position="left" class="map-form">
            <el-form-item label="地图服务商">
              <span class="map-type">腾讯地图</span>
            </el-form-item>
            <el-form-item label="Key" required>
              <el-input v-model="form.key" placeholder="请输入腾讯地图Key" />
              <div class="key-tip">
                腾讯地图开发者密钥，用于调用地图API服务
              </div>
            </el-form-item>
            <el-form-item label="Secret Key" required>
              <el-input v-model="form.secretKey" placeholder="请输入腾讯地图Secret Key"  type="password" show-password />
              <div class="key-tip">
                腾讯地图密钥，用于API签名验证，请妥善保管
              </div>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSave" :loading="loading">保存</el-button>
              <el-button @click="handleTest" :loading="testLoading">测试连接</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>
      <!-- 预留其他地图服务商tab -->
      <!--
      <el-tab-pane label="高德地图" name="amap">
        <div class="map-setting-card">
          高德地图配置表单...
        </div>
      </el-tab-pane>
      <el-tab-pane label="百度地图" name="baidu">
        <div class="map-setting-card">
          百度地图配置表单...
        </div>
      </el-tab-pane>
      -->
    </el-tabs>
    
    <!-- 测试连接对话框 -->
    <el-dialog v-model="testDialogVisible" title="测试地图连接" width="400px">
      <el-form label-width="80px">
        <el-form-item label="测试地址">
          <el-input v-model="testAddress" placeholder="请输入测试地址" />
          <div class="dialog-tip">
            默认使用"上海市 上海市 静安寺"进行测试
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="testDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmTest" :loading="testLoading">开始测试</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getMapConfig, saveMapConfig, testMapConnection, TencentMapConfigDto } from '@/api/config'

const activeTab = ref('tencent')
const loading = ref(false)
const testLoading = ref(false)
const testDialogVisible = ref(false)
const testAddress = ref('上海市 上海市 静安寺')
const form = ref<TencentMapConfigDto>({
  key: '',
  secretKey: ''
})

// 获取地图配置
const fetchConfig = async () => {
  try {
    const res = await getMapConfig()
    if (res.code === 200 && res.data) {
      form.value = { ...res.data }
    }
  } catch (error) {
    console.error('获取地图配置失败:', error)
    ElMessage.error('获取地图配置失败')
  }
}

// 保存配置
const handleSave = async () => {
  if (!form.value.key.trim()) {
    ElMessage.error('请输入腾讯地图Key')
    return
  }
  if (!form.value.secretKey.trim()) {
    ElMessage.error('请输入腾讯地图Secret Key')
    return
  }

  loading.value = true
  try {
    await saveMapConfig(form.value)
    ElMessage.success('保存成功')
  } catch (error) {
    console.error('保存地图配置失败:', error)
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

// 测试连接
const handleTest = () => {
  // 检查配置是否完整
  if (!form.value.key.trim() || !form.value.secretKey.trim()) {
    ElMessage.warning('请先保存完整的地图配置后再进行测试')
    return
  }
  
  testDialogVisible.value = true
}

// 确认测试
const confirmTest = async () => {
  if (!testAddress.value.trim()) {
    ElMessage.error('请输入测试地址')
    return
  }

  testLoading.value = true
  try {
    // 先保存配置，确保后端使用最新配置进行测试
    await saveMapConfig(form.value)
    
    // 调用腾讯地图地址解析接口测试连接
    const res = await testMapConnection(testAddress.value)
    
    if (res.code === 200 && res.data) {
      const mapResult = res.data
      if (mapResult.status === 0 && mapResult.result && mapResult.result.location) {
        const { lat, lng } = mapResult.result.location
        ElMessage.success(`连接测试成功！地址解析正常，坐标：${lat}, ${lng}`)
      } else {
        ElMessage.error(`连接测试失败：${mapResult.message || '地址解析失败'}`)
      }
    } else {
      ElMessage.error('连接测试失败：请求失败')
    }
    
    testDialogVisible.value = false
  } catch (error: any) {
    console.error('测试地图连接失败:', error)
    const errorMsg = error?.response?.data?.message || error?.message || '连接测试失败'
    ElMessage.error(`连接测试失败：${errorMsg}`)
  } finally {
    testLoading.value = false
  }
}

// 页面加载时获取配置
onMounted(() => {
  fetchConfig()
})
</script>

<style scoped lang="scss">
.map-setting {
  background: #fff;
  min-height: 100%;
  padding: 24px;
  .map-setting-card {
    width: 800px;
    margin: 20px 40px 0;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 2px 8px #f0f1f2;
    padding: 32px 40px;
    border: 1px solid #e4e7ed;
    .map-setting-title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 24px;
      border-left: 4px solid #409eff;
      padding-left: 12px;
    }
    .map-form {
      .el-form-item {
        margin-bottom: 24px;
      }
      .el-button {
        width: 120px;
        margin-right: 12px;
      }
      .map-type {
        font-size: 16px;
        color: #333;
        font-weight: 500;
      }
      .key-tip {
        color: #909399;
        font-size: 12px;
        margin-top: 4px;
        line-height: 1.5;
      }
    }
  }
}

// 对话框样式
:deep(.el-dialog) {
  .dialog-tip {
    color: #909399;
    font-size: 12px;
    margin-top: 4px;
    line-height: 1.5;
  }
}
</style>
