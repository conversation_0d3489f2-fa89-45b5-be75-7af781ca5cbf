import type { IDataType, IPageData } from '@/service/request/types'
import $http from '@/service'

// login
export function loginApi(data: any) {
  return $http.post<IDataType<IUser>>({
    url: '/user/login',
    data
  })
}
// logout
export function logOutApi() {
  return $http.post<IDataType<IUser>>({
    url: '/user/logout',
  })
}
// 重置密码
export function resetPasswordApi(data: any) {
  return $http.post<IDataType<IUser>>({
    url: '/user/resetPassword',
    data
  })
}
// 更改密码
export function changePasswordApi(data: any) {
  return $http.post<IDataType<IUser>>({
    url: '/user/setPassword',
    data
  })
}
// 获取用户信息
export function getUserInfoApi() {
  return $http.get<IDataType<IUser>>({
    url: '/user/info',
    showLoading: true
  })
}
// 刷新token
export function refreshTokenApi() {
  return $http.post<IDataType<{ token: string }>>({
    url: '/api/auth/refresh'
  })
}

export interface IUser {
  userId: string,
  name: string,
  email: string,
  mobile: string,
  avatar: string,
  token: string,
  permissions: string[]
}
