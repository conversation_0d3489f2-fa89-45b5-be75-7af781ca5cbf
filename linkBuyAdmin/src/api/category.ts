import type { IDataType } from '@/service/request/types'
import $http from '@/service'

// 分类数据类型定义
export interface CategoryDto {
  id?: number
  name: string
  code: string
  image: string
  description?: string
  sortOrder: number
  parentId?: number
  level?: number
  children?: CategoryDto[]
}

// 分类结构更新数据类型
export interface CategoryStructureDto {
  id: number
  parentId?: number
  sortOrder: number
}

/**
 * 获取所有分类树
 */
export function getAllCategory(categoryType: string = 'product') {
  return $http.get<IDataType<CategoryDto[]>>({
    url: `/system/${categoryType}/allCategory`,
  })
}

/**
 * 保存分类
 */
export function saveCategory(data: CategoryDto, categoryType: string = 'product') {
  return $http.post<IDataType<CategoryDto>>({
    url: `/system/${categoryType}/category/save`,
    data
  })
}

/**
 * 获取分类详情
 */
export function getCategoryDetail(id: number, categoryType: string = 'product') {
  return $http.get<IDataType<CategoryDto>>({
    url: `/system/${categoryType}/category/${id}`,
  })
}

/**
 * 批量更新分类结构
 */
export function updateCategoryStructure(data: CategoryStructureDto[], categoryType: string = 'product') {
  return $http.post<IDataType<boolean>>({
    url: `/system/${categoryType}/category/structure`,
    data
  })
}

/**
 * 删除分类
 */
export function deleteCategory(id: number, categoryType: string = 'product') {
  return $http.delete<IDataType<boolean>>({
    url: `/system/${categoryType}/category/${id}`,
  })
} 