import type { IDataType } from '@/service/request/types'
import $http from '@/service'

// 存储设置 DTO
export interface StorageConfigDto {
  domain: string
  bucket: string
  accessKey: string
  secretKey: string
}

// 物流设置 DTO
export interface LogisticsConfigDto {
  customer: string
  appKey: string
}

// 邮件设置 DTO
export interface EmailConfigDto {
  smtpHost: string
  smtpPort: string
  smtpUsername: string
  smtpPassword: string
  fromAddress: string
  fromName: string
  smtpAuth: string
  starttlsEnable: string
}

// 短信设置 DTO
export interface SmsConfigDto {
  accessKey: string
  accessSecret: string
  signName: string
  templateCode: string
}

// 腾讯地图配置接口类型
export interface TencentMapConfigDto {
  key: string
  secretKey: string
}

// 获取存储设置
export function getStorageConfig() {
  return $http.get<IDataType<StorageConfigDto>>({
    url: '/system/config/storage'
  })
}

// 保存存储设置
export function saveStorageConfig(data: StorageConfigDto) {
  return $http.post<IDataType<boolean>>({
    url: '/system/config/storage',
    data
  })
}

// 获取物流设置
export function getLogisticsConfig() {
  return $http.get<IDataType<LogisticsConfigDto>>({
    url: '/system/config/logistics'
  })
}

// 保存物流设置
export function saveLogisticsConfig(data: LogisticsConfigDto) {
  return $http.post<IDataType<boolean>>({
    url: '/system/config/logistics',
    data
  })
}

// 获取邮件设置
export function getEmailConfig() {
  return $http.get<IDataType<EmailConfigDto>>({
    url: '/system/config/email'
  })
}

// 保存邮件设置
export function saveEmailConfig(data: EmailConfigDto) {
  return $http.post<IDataType<boolean>>({
    url: '/system/config/email',
    data
  })
}

// 发送测试邮件
export function sendTestEmail(testEmail: string) {
  return $http.post<IDataType<boolean>>({
    url: '/system/config/email/test',
    params: { testEmail }
  })
}

// 获取短信设置
export function getSmsConfig() {
  return $http.get<IDataType<SmsConfigDto>>({
    url: '/system/config/sms'
  })
}

// 保存短信设置
export function saveSmsConfig(data: SmsConfigDto) {
  return $http.post<IDataType<boolean>>({
    url: '/system/config/sms',
    data
  })
}

// 发送测试短信
export function sendTestSms(phoneNumber: string) {
  return $http.post<IDataType<boolean>>({
    url: '/system/config/sms/test',
    params: { phoneNumber }
  })
}

/**
 * 保存腾讯地图配置
 */
export function saveMapConfig(data: TencentMapConfigDto) {
  return $http.post<IDataType<boolean>>({
    url: '/system/config/map',
    data
  })
}

/**
 * 获取腾讯地图配置
 */
export function getMapConfig() {
  return $http.get<IDataType<TencentMapConfigDto>>({
    url: '/system/config/map'
  })
}

/**
 * 测试腾讯地图连接
 */
export function testMapConnection(address: string = '上海市 上海市 静安寺') {
  return $http.get<IDataType<any>>({
    url: '/common/tencentMap/geocoder',
    params: { address }
  })
}
