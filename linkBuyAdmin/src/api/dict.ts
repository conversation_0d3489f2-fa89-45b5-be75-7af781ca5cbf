import type { IDataType } from '@/service/request/types'
import $http from '@/service'

export function getDict(dictId: string) {
    return $http.get<IDataType<IDictData[]>>({
        url: `common/dict/${dictId}`,
    })
}
export function getAll() {
    return $http.get<IDataType<IDictData[]>>({
        url: `common/dict/all`,
    })
}

export interface IDictData {
    dictCode: string
    dictName: string
    items: IDictItem[]
}
export interface IDictItem {
    itemValue: string
    itemName: string
}