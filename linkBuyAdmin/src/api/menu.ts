import type { MenuDto } from '@/types/menu'
import type { IDataType } from '@/service/request/types'
import $http from '@/service'


export function getAllMenu(menuType: string) {
  return $http.get<IDataType<MenuDto>>({
    url: `system/${menuType}/allMenu`
  })
}

export function saveMenu(data: MenuDto, menuType: string) {
  return $http.post<IDataType<MenuDto>>({
    url: `system/${menuType}/menu/save`,
    data
  })
}

export function updateMenuSort(menuIds: number[], menuType: string) {
  return $http.post<IDataType<boolean>>({
    url: `system/${menuType}/menu/sort`,
    data: menuIds
  })
}

export function deleteMenu(id: number, menuType: string) {
  return $http.delete<IDataType<boolean>>({
    url: `system/${menuType}/menu/${id}`
  })
}

/**
 * 更新菜单结构（包括层级和排序）
 */
export interface MenuStructureDto {
  id: number
  parentId: number | null
  sortOrder: number
}

export function updateMenuStructure(data: MenuStructureDto[], menuType: string) {
  return $http.post<IDataType<boolean>>({
    url: `system/${menuType}/menu/structure`,
    data
  })
}