import type { IDataType, IPageData } from '@/service/request/types'
import $http from '@/service'

// 客户接口类型定义
export interface ICustomer {
  id?: number
  code: string
  name: string
  usciCode?: string
  contactName: string
  contactMobile: string
  contactEmail?: string
  invoiceTitle?: string
  invoiceTaxNo?: string
  address?: string
  bizLicensePicPath?: string
  status: number
  remark?: string
  createTime?: string
  updateTime?: string
}

// 获取客户列表
export function getCustomerList(params: {
  page: number
  size: number
  query?: string
}) {
  return $http.get<IDataType<IPageData<ICustomer>>>({
    url: '/biz/customer/list',
    params
  })
}

// 获取客户详情
export function getCustomerDetail(id: number) {
  return $http.get<IDataType<ICustomer>>({
    url: `/biz/customer/${id}`
  })
}

// 新增客户
export function addCustomer(data: ICustomer) {
  return $http.post<IDataType<ICustomer>>({
    url: '/biz/customer',
    data
  })
}

// 更新客户信息
export function updateCustomer(id: number, data: Partial<ICustomer>) {
  return $http.post<IDataType<ICustomer>>({
    url: `/biz/customer/${id}`,
    data
  })
}

// 更新客户状态
export function updateCustomerStatus(id: number, status: number) {
  return $http.post<IDataType<boolean>>({
    url: `/biz/customer/${id}/status`,
    data: { status }
  })
}

// 客户用户接口类型定义
export interface ICompanyUser {
  id?: number
  companyId: number
  username: string
  email?: string
  mobile: string
  status: number
  createTime?: string
  updateTime?: string
}

// 获取客户用户列表
export function getCompanyUsers(companyId: number) {
  return $http.get<IDataType<ICompanyUser[]>>({
    url: `/biz/customer/${companyId}/users`
  })
}

// 重置客户用户密码
export function resetCompanyUserPassword(userId: number) {
  return $http.post<IDataType<{ newPassword: string }>>({
    url: `/biz/customer/user/${userId}/reset-password`
  })
}

// 客户超级登录
export function companySuperLogin(id: number) {
  return $http.get<IDataType<string>>({
    url: `/biz/customer/${id}/superLogin`
  })
}
