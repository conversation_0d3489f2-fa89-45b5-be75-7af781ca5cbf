import type { IDataType, IPageData } from '@/service/request/types'
import $http from '@/service'

// 用户类型定义
export interface IUser {
  id: number
  username: string
  email: string
  mobile: string
  status: number
  createTime: string
  updateTime: string
}

// 获取用户列表（分页+搜索）
export function getUserList(params: { page?: number; size?: number; query?: string }) {
  return $http.get<IDataType<IPageData<IUser>>>({ url: '/system/users', params })
}

// 获取用户详情
export function getUserDetail(id: number) {
  return $http.get<IDataType<IUser>>({ url: `/system/users/${id}` })
}

// 添加用户
export function addUser(data: any) {
  return $http.post<IDataType<IUser>>({ url: '/system/users', data })
}

// 更新用户
export function updateUser(id: number, data: any) {
  return $http.post<IDataType<IUser>>({ url: `/system/users/${id}`, data })
}

// 删除用户
export function deleteUser(id: number) {
  return $http.delete<IDataType<boolean>>({ url: `/system/users/${id}` })
}

// 获取用户角色列表
export function getUserRoles(id: number) {
  return $http.get<IDataType<any[]>>({ url: `/system/users/${id}/roles` })
}

// 绑定用户角色（全量覆盖）
export function bindUserRoles(id: number, roleIds: number[]) {
  return $http.post<IDataType<boolean>>({ url: `/system/users/${id}/bindRole`, data: roleIds })
}
