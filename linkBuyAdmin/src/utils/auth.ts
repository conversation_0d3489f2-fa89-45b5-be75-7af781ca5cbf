import localCache from './local-cache'
import { logOutApi, refreshTokenApi } from '@/api/auth'
import $http from '@/service'

const TokenKey = 'token'

export function getToken() {
  return localCache.getCache(TokenKey)
}

export function setToken(token: string) {
  localCache.setCache(TokenKey, token)
}

export function removeToken() {
  localCache.removeCache(TokenKey)
}

export function logout(href = window.location.href) {
  logOutApi().then(() => {
    removeToken()
  })

  location.href = window.origin + '/#/login'
}

// 检查token是否即将过期
const isTokenExpiringSoon = (token: string): boolean => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    const expirationTime = payload.exp * 1000 // 转换为毫秒
    const currentTime = Date.now()
    // 如果token将在5分钟内过期，则刷新
    return expirationTime - currentTime < 5 * 60 * 1000
  } catch {
    return false
  }
}

// 刷新token
const refreshToken = async () => {
  try {
    const res = await refreshTokenApi()
    if (res.data) {
      setToken(res.data.token)
    }
  } catch (error) {
    console.error('刷新token失败:', error)
    logout()
  }
}

// 刷新token并重试请求
const refreshTokenAndRetry = async (config: any) => {
  try {
    await refreshToken()
    // 使用新token重试请求
    return $http.request(config)
  } catch (error) {
    logout()
    return Promise.reject(error)
  }
}

// 检查token是否有效
export const isTokenValid = (token: string): boolean => {
  if (!token) return false
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    return payload.exp * 1000 > Date.now()
  } catch {
    return false
  }
}

// 获取token中的用户信息
export const getTokenPayload = (token: string): any => {
  try {
    return JSON.parse(atob(token.split('.')[1]))
  } catch {
    return null
  }
}
