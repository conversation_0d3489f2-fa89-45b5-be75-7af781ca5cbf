{"name": "mob-admin-vue3", "version": "0.0.0", "private": true, "scripts": {"serve": "vite", "build": "vite build", "build:test": "vite build --mode test", "build:pre": "vite build --mode pre", "preview": "vite preview", "prettier": "prettier --write ."}, "dependencies": {"@antv/x6": "^2.15.6", "@antv/x6-common": "^2.0.16", "@antv/x6-geometry": "^2.0.5", "@antv/x6-plugin-clipboard": "^2.1.6", "@antv/x6-plugin-dnd": "^2.1.1", "@antv/x6-plugin-export": "^2.1.6", "@antv/x6-plugin-history": "^2.2.4", "@antv/x6-plugin-keyboard": "^2.2.1", "@antv/x6-plugin-scroller": "^2.0.10", "@antv/x6-plugin-selection": "^2.2.1", "@antv/x6-plugin-snapline": "^2.1.7", "@antv/x6-plugin-stencil": "^2.1.4", "@antv/x6-plugin-transform": "^2.1.8", "@antv/x6-vue-shape": "^2.1.1", "@element-plus/icons": "^0.0.11", "@rollup/plugin-commonjs": "^25.0.7", "@types/crypto-js": "^4.2.2", "@types/event-source-polyfill": "^1.0.1", "@types/js-cookie": "^3.0.1", "@vueup/vue-quill": "^1.2.0", "axios": "^0.24.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "^5.4.2", "element-plus": "^2.9.4", "esbuild": "^0.25.3", "event-source-polyfill": "^1.0.31", "i18n": "^0.15.1", "i18next": "^25.0.2", "js-cookie": "^3.0.1", "lodash": "^4.17.21", "mockjs": "^1.1.0", "monaco-editor": "^0.29.1", "normalize.css": "^8.0.1", "pinia": "^2.0.33", "pinia-plugin-persist": "^1.0.0", "quill": "^2.0.3", "sql-formatter": "^14.0.0", "vite-plugin-monaco-editor": "1.0.10", "vue": "^3.2.47", "vue-i18n": "^11.1.3", "vue-quill-editor": "^3.0.6", "vue-router": "^4.5.1", "vue3-json-viewer": "^2.2.2"}, "devDependencies": {"@intlify/cli": "^0.13.1", "@types/vue-i18n": "^6.1.3", "@types/vue-router": "^2.0.0", "@vitejs/plugin-vue": "^4.0.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "fast-glob": "^3.3.2", "prettier": "^2.8.5", "sass": "^1.26.5", "sass-loader": "^8.0.2", "typescript": ">=4.4.4", "vite": "^4.1.4", "vite-plugin-mock": "^3.0.2", "vite-plugin-require-transform": "^1.0.21", "vite-plugin-svg-icons": "^2.0.1", "vite-svg-loader": "^5.1.0", "vue-i18n-extract": "^2.0.7"}}