/// <reference types='@dcloudio/types' />

declare module '*.vue' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module '@vue/runtime-core' {
  type Hooks = App.AppInstance & Page.PageInstance

  interface ComponentCustomOptions extends Hooks {}
}

// 全局类型声明
declare global {
  const uni: any
  const wx: any
  const plus: any
  const weex: any
}
