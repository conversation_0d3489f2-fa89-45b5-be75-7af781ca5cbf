// 用户相关类型
export interface User {
  id: number
  username: string
  nickname: string
  avatar: string
  phone: string
  email: string
  gender: number
  birthday: string
  createTime: string
}

export interface LoginParams {
  username: string
  password: string
  code?: string
}

export interface RegisterParams {
  username: string
  password: string
  confirmPassword: string
  phone: string
  code: string
}

// 地址相关类型
export interface Address {
  id: number
  userId: number
  name: string
  phone: string
  province: string
  city: string
  district: string
  detail: string
  isDefault: boolean
  createTime: string
}

// 商品相关类型
export interface Product {
  id: number
  name: string
  description: string
  price: number
  originalPrice: number
  stock: number
  sales: number
  images: string[]
  categoryId: number
  categoryName: string
  specs: ProductSpec[]
  createTime: string
}

export interface ProductSpec {
  id: number
  name: string
  options: string[]
}

export interface Category {
  id: number
  name: string
  icon: string
  parentId: number
  children?: Category[]
}

// 购物车相关类型
export interface CartItem {
  id: number
  productId: number
  productName: string
  productImage: string
  price: number
  quantity: number
  specs: string
  selected: boolean
}

// 订单相关类型
export interface Order {
  id: number
  orderNo: string
  userId: number
  status: OrderStatus
  totalAmount: number
  payAmount: number
  payType: PayType
  payTime: string
  deliveryTime: string
  finishTime: string
  address: Address
  items: OrderItem[]
  createTime: string
}

export interface OrderItem {
  id: number
  orderId: number
  productId: number
  productName: string
  productImage: string
  price: number
  quantity: number
  specs: string
}

export enum OrderStatus {
  PENDING = 1,    // 待付款
  PAID = 2,       // 已付款
  SHIPPED = 3,    // 已发货
  DELIVERED = 4,  // 已送达
  FINISHED = 5,   // 已完成
  CANCELLED = 6   // 已取消
}

export enum PayType {
  WECHAT = 1,     // 微信支付
  ALIPAY = 2,     // 支付宝
  BALANCE = 3     // 余额支付
}

// 主题相关类型
export interface ThemeConfig {
  name: string
  primary: string
  primaryLight: string
  primaryDark: string
  secondary: string
  accent: string
  background: string
  surface: string
  text: string
  textSecondary: string
  border: string
  gradient: string
}

// 底部导航栏类型
export interface NavBar {
  name: string
  icon: string
  iconActive: string
  path: string
}

// 导航配置类型
export interface NavConfig {
  enabled: boolean
  displayType: string
  navHeight: number
  fontSize: number
  fontWeight: number
  textMarginTop: number
  iconSize: number
  backgroundColor: string
}

// 微页面相关类型
export interface MiniPageInfo {
  id: number
  pageCode: string
  pageName: string
  pageConfig: any
  isHomePage: boolean
  status: number
}

// 页面组件类型
export interface PageWidget {
  id: number
  pageId: number
  widgetType: string
  widgetData: any
  sortOrder: number
  isHidden: boolean
}

// 开屏广告配置类型
export interface SplashConfig {
  id?: number
  mallId?: number
  enabled: boolean
  autoClose: boolean
  duration: number
  buttonPosition: string
  indicatorStyle: string
}

// 开屏广告类型
export interface SplashAds {
  id?: number
  mallId?: number
  title: string
  imageUrl: string
  linkUrl: string
  linkType: string
  sortOrder: number
  status?: number
}

// 分类页装修配置类型
export interface CategoryDecoration {
  categoryType: string  // 分类类型：custom等
  categoryLevel: number  // 分类层级
  styleType: number  // 样式类型
  showSearch: boolean  // 是否显示搜索
  searchConfig: string  // 搜索配置JSON字符串
  searchWidgetData: string  // 搜索组件数据JSON字符串
}

// 搜索配置类型
export interface SearchConfig {
  locationText: string
  placeholder: string
}

// 搜索组件样式类型
export interface SearchWidgetStyles {
  text_align: string
  position: string
  address_text_color: string
  address_icon_color: string
  border_radius: number
  root_bg_color: string
  root_bg_url: string
  bg_color: string
  icon_color: string
  color: string
  padding_top: number
  padding_horizontal: number
  padding_bottom: number
  bg_image: string
}

// 搜索组件数据类型
export interface SearchWidgetData {
  title: string
  name: string
  show: number
  content: {
    text: string
    show_address: number
    data: Array<{
      url: string
      link: any[]
    }>
  }
  styles: SearchWidgetStyles
}

// 商城全局配置类型 - 用于mallInfo接口
export interface MallInfo {
  mallId: number
  mallName: string
  mallLogo: string
  mallDesc: string
  themes: string  // 主题名称
  navBars: NavBar[]  // 底部导航配置
  navConfig: NavConfig  // 导航样式配置
  splashConfig: SplashConfig  // 开屏广告配置
  splashAds: SplashAds[]  // 开屏广告列表
  categoryDecoration: CategoryDecoration  // 分类页装修配置
}

// 页面配置类型 - 用于homePage和landingPage接口
export interface PageInfo {
  miniPage?: MiniPageInfo | null  // 微页面信息
  pageWidgets?: PageWidget[]  // 页面组件列表
}

// API响应类型 - 更新为匹配新的API结构
export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
}

export interface PageData<T = any> {
  list: T[]
  total: number
  page: number
  pageSize: number
  hasMore: boolean
}

// 搜索相关类型
export interface SearchParams {
  keyword?: string
  categoryId?: number
  minPrice?: number
  maxPrice?: number
  sortBy?: 'default' | 'price' | 'sales' | 'createTime'
  sortOrder?: 'asc' | 'desc'
  page?: number
  pageSize?: number
}

// 轮播图类型
export interface Banner {
  id: number
  title: string
  image: string
  link: string
  sort: number
  status: number
}

// 公告类型
export interface Notice {
  id: number
  title: string
  content: string
  type: number
  status: number
} 