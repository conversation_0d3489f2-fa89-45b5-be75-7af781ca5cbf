<template>
  <view class="product-card-widget" :style="rootStyle">
    <!-- 已初始化：显示完整的选项卡组件 -->
    <template v-if="isVisible">
      <!-- 选项卡头部 -->
      <view class="card-tabs" :style="tabsContainerStyle">
        <view 
          v-for="(tab, index) in safeTabs" 
          :key="index"
          class="tab-item"
          :class="{ active: currentActiveIndex === index }"
          :style="getTabItemStyle(index)"
          @click="switchTab(index)"
        >
          {{ tab.name }}
          <!-- 选项条指示器 -->
          <view 
            v-if="currentActiveIndex === index && (safeContent.show_line === 1 || safeContent.show_line === true)" 
            class="tab-indicator" 
            :style="tabIndicatorStyle"
          ></view>
        </view>
      </view>

      <!-- 选项卡内容区域 -->
      <view class="card-content" :style="contentContainerStyle">
        <!-- 使用 ProductGroup 组件展示当前选项卡的商品 -->
        <ProductGroup 
          v-if="activeTab"
          :key="`tab-${currentActiveIndex}`"
          :config="activeTabConfig" 
          :styles="activeTabStyles"
        />
      </view>
    </template>
    
    <!-- 未初始化：显示占位内容 -->
    <view v-else class="placeholder-content">
      <view class="placeholder-tabs">
        <view 
          v-for="(tab, index) in safeTabs" 
          :key="index"
          class="placeholder-tab"
          :class="{ active: currentActiveIndex === index }"
        >
          {{ tab.name }}
        </view>
      </view>
      <view class="placeholder-loading">
        <text class="loading-text">加载中...</text>
      </view>
    </view>
  </view>
</template>

<script>
import ProductGroup from '@/components/ProductGroup/ProductGroup.vue'

export default {
  name: 'ProductCard',
  components: {
    ProductGroup
  },
  props: {
    config: {
      type: Object,
      default: () => ({})
    },
    styles: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      activeTabIndex: 0,
      isVisible: false,
      hasInitialized: false,
      intersectionObserver: null
    }
  },

  computed: {
    // 安全访问内容数据
    safeContent() {
      return {
        active: 0,
        show_line: 1,
        has_active_bg: 0,
        max_product_count: 20,
        data: [],
        ...this.config
      }
    },

    // 安全访问样式数据
    safeStyles() {
      return {
        root_bg_color: '',
        bg_color: '#FFFFFF',
        color: '#333333',
        active_color: '#FF2C3C',
        line_color: '#FF2C3C',
        active_bg_color: '',
        // 选项卡样式
        tab_gap: 8,
        tab_padding_top: 12,
        tab_padding_bottom: 12,
        tab_padding_horizontal: 16,
        tab_border_radius_top: 0,
        tab_border_radius_bottom: 0,
        // 商品样式
        content_bg_color: '#FFFFFF',
        margin: 10,
        component_margin: 0,
        content_padding_top: 10,
        content_padding_bottom: 10,
        content_padding_horizontal: 10,
        content_border_radius_top: 0,
        content_border_radius_bottom: 0,
        goods_border_radius: 4,
        ...this.styles
      }
    },

    // 处理选项卡数据，确保每个选项卡都有完整的配置
    safeTabs() {
      const tabs = this.safeContent.data || []
      
      // 如果没有选项卡数据，创建一个默认选项卡
      if (!Array.isArray(tabs) || tabs.length === 0) {
        return [{
          name: '选项卡1',
          style: 1,
          show_title: 1,
          show_price: 1,
          show_scribing_price: 1,
          show_btn: 1,
          btn_text: '购买',
          show_tag: 0,
          data: [],
          goods_type: 1,
          category: {},
          title_color: this.safeStyles.color || '#333333',
          price_color: this.safeStyles.active_color || '#FF2C3C',
          scribing_price_color: '#999999',
          btn_bg_color: this.safeStyles.active_color || '#FF2C3C',
          btn_color: '#FFFFFF',
          btn_border_radius: 30,
          btn_border_color: ''
        }]
      }
      
      // 处理选项卡数组，确保每个选项卡都有完整的配置
      return tabs.map(tab => ({
        name: tab.name || '选项卡',
        style: tab.style || 1,
        show_title: tab.show_title !== undefined ? tab.show_title : 1,
        show_price: tab.show_price !== undefined ? tab.show_price : 1,
        show_scribing_price: tab.show_scribing_price !== undefined ? tab.show_scribing_price : 1,
        show_btn: tab.show_btn !== undefined ? tab.show_btn : 1,
        btn_text: tab.btn_text || '购买',
        show_tag: tab.show_tag !== undefined ? tab.show_tag : 0,
        data: tab.data || [],
        goods_type: tab.goods_type || 1,
        category: tab.category || {},
        title_color: tab.title_color || '#333333',
        price_color: tab.price_color || '#FF2C3C',
        scribing_price_color: tab.scribing_price_color || '#999999',
        btn_bg_color: tab.btn_bg_color || '#FF2C3C',
        btn_color: tab.btn_color || '#FFFFFF',
        btn_border_radius: tab.btn_border_radius || 30,
        btn_border_color: tab.btn_border_color || ''
      }))
    },

    // 当前激活的选项卡索引
    currentActiveIndex() {
      return this.activeTabIndex
    },

    // 当前激活的选项卡
    activeTab() {
      return this.safeTabs[this.currentActiveIndex] || this.safeTabs[0]
    },

    // 当前选项卡的配置，转换为 ProductGroup 组件需要的格式
    activeTabConfig() {
      const tab = this.activeTab
      if (!tab) return {}
      
      return {
        style: tab.style,
        show_title: tab.show_title,
        show_price: tab.show_price,
        show_scribing_price: tab.show_scribing_price,
        show_btn: tab.show_btn,
        btn_text: tab.btn_text,
        show_tag: tab.show_tag,
        data: tab.data,
        goods_type: tab.goods_type,
        category: tab.category,
        show_description: false, // 选项卡模式通常不显示描述
        show_sales: false // 选项卡模式通常不显示销量
      }
    },

    // 当前选项卡的样式，转换为 ProductGroup 组件需要的格式
    activeTabStyles() {
      const tab = this.activeTab
      const styles = this.safeStyles
      
      if (!tab) return {}
      
      return {
        // 商品项样式 - 优先使用选项卡自己的样式，然后是全局样式
        title_color: tab.title_color || styles.color || '#333333',
        price_color: tab.price_color || styles.active_color || '#FF2C3C',
        scribing_price_color: tab.scribing_price_color || '#999999',
        btn_bg_color: tab.btn_bg_color || styles.active_color || '#FF2C3C',
        btn_color: tab.btn_color || '#FFFFFF',
        btn_border_radius: tab.btn_border_radius || 30,
        btn_border_color: tab.btn_border_color || '',
        
        // 容器样式
        root_bg_color: 'transparent', // 选项卡内容区域不需要背景色
        bg_color: tab.content_bg_color || styles.content_bg_color || '#FFFFFF',
        content_bg_color: tab.content_bg_color || styles.content_bg_color || '#FFFFFF',
        
        // 间距样式
        margin: tab.margin || styles.margin || 10,
        padding: tab.padding || 0,
        padding_top: tab.padding_top || 0,
        padding_bottom: tab.padding_bottom || 0,
        padding_horizontal: tab.padding_horizontal || 0,
        
        // 圆角样式
        border_radius_top: tab.border_radius_top || 0,
        border_radius_bottom: tab.border_radius_bottom || 0,
        goods_border_radius: tab.goods_border_radius || styles.goods_border_radius || 4,
        
        // 商品间距
        item_spacing: 8,
        
        // 标签样式
        tag_bg_color: '#ff4757',
        tag_text_color: '#ffffff'
      }
    },

    // 组件根样式
    rootStyle() {
      const styles = this.safeStyles
      return {
        backgroundColor: styles.root_bg_color || 'transparent',
        margin: `${styles.component_margin || 0}px`,
        width: '100%',
        boxSizing: 'border-box'
      }
    },

    // 选项卡容器样式
    tabsContainerStyle() {
      const styles = this.safeStyles
      return {
        backgroundColor: styles.bg_color || '#FFFFFF',
        gap: `${styles.tab_gap || 8}px`,
        borderTopLeftRadius: `${styles.tab_border_radius_top || 0}px`,
        borderTopRightRadius: `${styles.tab_border_radius_top || 0}px`,
        borderBottomLeftRadius: `${styles.tab_border_radius_bottom || 0}px`,
        borderBottomRightRadius: `${styles.tab_border_radius_bottom || 0}px`,
        padding: '0 16px'
      }
    },

    // 内容容器样式
    contentContainerStyle() {
      const styles = this.safeStyles
      return {
        backgroundColor: styles.content_bg_color || '#FFFFFF',
        padding: `${styles.content_padding_top || 0}px ${styles.content_padding_horizontal || 0}px ${styles.content_padding_bottom || 0}px`,
        borderTopLeftRadius: `${styles.content_border_radius_top || 0}px`,
        borderTopRightRadius: `${styles.content_border_radius_top || 0}px`,
        borderBottomLeftRadius: `${styles.content_border_radius_bottom || 0}px`,
        borderBottomRightRadius: `${styles.content_border_radius_bottom || 0}px`
      }
    },

    // 选项条指示器样式
    tabIndicatorStyle() {
      const styles = this.safeStyles
      return {
        backgroundColor: styles.line_color || styles.active_color || '#FF2C3C'
      }
    }
  },

  watch: {
    // 监听配置中的active变化
    'safeContent.active'(newActive) {
      if (newActive !== undefined && newActive !== this.activeTabIndex) {
        this.activeTabIndex = newActive
      }
    }
  },

  mounted() {
    // 初始化激活的选项卡索引 - 默认激活第一个选项卡
    if (this.safeContent.active !== undefined) {
      this.activeTabIndex = this.safeContent.active
    } else {
      this.activeTabIndex = 0
    }
    
    console.log('ProductCard组件挂载:', {
      config: this.config,
      styles: this.styles,
      safeContent: this.safeContent,
      safeTabs: this.safeTabs,
      activeTab: this.activeTab,
      currentActiveIndex: this.currentActiveIndex,
      tabsLength: this.safeTabs.length,
      isVisible: this.isVisible,
      hasInitialized: this.hasInitialized
    })
    
    // 设置交叉观察器，实现懒加载
    this.$nextTick(() => {
      this.setupIntersectionObserver()
    })
  },

  beforeUnmount() {
    // 销毁交叉观察器
    this.destroyIntersectionObserver()
  },

  methods: {
    // 获取选项卡项样式
    getTabItemStyle(index) {
      const styles = this.safeStyles
      const isActive = this.currentActiveIndex === index
      
      return {
        color: isActive ? (styles.active_color || '#FF2C3C') : (styles.color || '#333333'),
        backgroundColor: isActive && (this.safeContent.has_active_bg === 1 || this.safeContent.has_active_bg === true) ? (styles.active_bg_color || 'transparent') : 'transparent',
        padding: `${styles.tab_padding_top || 0}px ${styles.tab_padding_horizontal || 0}px ${styles.tab_padding_bottom || 0}px`,
        fontWeight: isActive ? '600' : '400'
      }
    },

    // 切换选项卡
    switchTab(index) {
      if (this.activeTabIndex === index) {
        return // 如果点击的是当前激活的选项卡，不需要切换
      }
      
      this.activeTabIndex = index
      console.log('切换到选项卡:', index, this.safeTabs[index], {
        activeTab: this.activeTab,
        activeTabConfig: this.activeTabConfig
      })
      
      // 只有组件已经初始化后才触发数据加载
      if (this.hasInitialized) {
        this.loadTabData(index)
      }
    },

    // 设置交叉观察器
    setupIntersectionObserver() {
      // #ifdef H5
      if (typeof IntersectionObserver !== 'undefined') {
        this.intersectionObserver = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting && !this.hasInitialized) {
              console.log('ProductCard组件进入视口，开始初始化')
              this.initializeComponent()
            }
          })
        }, {
          rootMargin: '100px' // 提前100px开始加载
        })
        
        this.intersectionObserver.observe(this.$el)
      } else {
        // 如果不支持IntersectionObserver，直接初始化
        this.initializeComponent()
      }
      // #endif
      
      // #ifndef H5
      // 小程序环境使用uni.createIntersectionObserver
      this.setupMiniProgramObserver()
      // #endif
    },

    // 设置小程序交叉观察器
    setupMiniProgramObserver() {
      // #ifndef H5
      this.intersectionObserver = uni.createIntersectionObserver(this, {
        rootMargin: '100px 0px 100px 0px' // 提前100px开始加载
      })
      
      this.intersectionObserver.relativeToViewport().observe('.product-card-widget', (res) => {
        if (res.intersectionRatio > 0 && !this.hasInitialized) {
          console.log('ProductCard小程序组件进入视口，开始初始化')
          this.initializeComponent()
        }
      })
      // #endif
    },

    // 销毁交叉观察器
    destroyIntersectionObserver() {
      if (this.intersectionObserver) {
        // #ifdef H5
        this.intersectionObserver.disconnect()
        // #endif
        
        // #ifndef H5
        this.intersectionObserver.disconnect()
        // #endif
        
        this.intersectionObserver = null
      }
    },

    // 初始化组件
    initializeComponent() {
      if (this.hasInitialized) return
      
      this.isVisible = true
      this.hasInitialized = true
      
      console.log('ProductCard组件初始化完成，加载第一个选项卡数据')
      
      // 初始化时加载第一个选项卡的数据
      this.loadTabData(this.activeTabIndex)
    },

    // 加载选项卡数据
    loadTabData(index) {
      const tab = this.safeTabs[index]
      if (!tab) return
      
      console.log('加载选项卡数据:', {
        tabIndex: index,
        tabName: tab.name,
        goodsType: tab.goods_type,
        data: tab.data,
        category: tab.category
      })
      
      // 这里可以根据选项卡的数据类型调用不同的API
      // 如果是商品ID列表，ProductGroup组件会自动处理
      // 如果是分类数据，可以在这里调用分类商品接口
      if (tab.goods_type === 2 && tab.category && tab.category.id) {
        // 分类商品类型，可以调用分类商品接口
        console.log('需要加载分类商品:', tab.category)
      }
    }
  }
}
</script>

<style scoped>
.product-card-widget {
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 选项卡头部 */
.card-tabs {
  display: flex;
  position: relative;
  justify-content: flex-start;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
  box-sizing: border-box;
}

.card-tabs::-webkit-scrollbar {
  display: none;
}

.tab-item {
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  border-bottom: 2px solid transparent;
  white-space: nowrap;
  flex-shrink: 0;
  box-sizing: border-box;
}

.tab-item:hover {
  opacity: 0.8;
}

.tab-item.active {
  font-weight: 600;
}

/* 选项条指示器 */
.tab-indicator {
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  border-radius: 1px;
}

/* 内容区域 */
.card-content {
  width: 100%;
  box-sizing: border-box;
}

/* 占位内容样式 */
.placeholder-content {
  width: 100%;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
}

.placeholder-tabs {
  display: flex;
  gap: 8px;
  padding: 0 16px;
  background-color: #ffffff;
  border-bottom: 1px solid #f0f0f0;
}

.placeholder-tab {
  padding: 12px 16px;
  font-size: 14px;
  color: #999;
  position: relative;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
  margin: 8px 0;
}

.placeholder-tab.active {
  color: #666;
  background: linear-gradient(90deg, #e0e0e0 25%, #d0d0d0 50%, #e0e0e0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.placeholder-tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background: linear-gradient(90deg, #c0c0c0 25%, #b0b0b0 50%, #c0c0c0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 1px;
}

.placeholder-loading {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background-color: #ffffff;
}

.loading-text {
  color: #999;
  font-size: 14px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  padding: 8px 16px;
  border-radius: 4px;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 确保容器不会超出屏幕 */
.product-card-widget,
.card-tabs,
.card-content {
  max-width: 100%;
  box-sizing: border-box;
}
</style>
