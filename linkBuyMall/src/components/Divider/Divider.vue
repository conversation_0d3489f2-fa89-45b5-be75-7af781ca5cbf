<template>
  <view class="divider-widget" :style="widgetStyle">
    <view class="divider-line" :style="lineStyle" :class="lineClass"></view>
  </view>
</template>

<script>
export default {
  name: 'Divider',
  props: {
    config: {
      type: Object,
      default: () => ({})
    },
    styles: {
      type: Object,
      default: () => ({})
    }
  },

  computed: {
    // 安全访问配置数据
    safeConfig() {
      return {
        style: 'solid',
        color: '#e5e5e5',
        thickness: 1,
        backgroundColor: 'transparent',
        marginTop: 10,
        marginBottom: 10,
        marginHorizontal: 0,
        ...this.config
      }
    },

    // 安全访问样式数据
    safeStyles() {
      return {
        style: 'solid',
        color: '#e5e5e5',
        thickness: 1,
        backgroundColor: 'transparent',
        marginTop: 10,
        marginBottom: 10,
        marginHorizontal: 0,
        ...this.styles
      }
    },

    // 合并配置和样式数据
    mergedData() {
      return {
        ...this.safeConfig,
        ...this.safeStyles
      }
    },

    // 计算组件整体样式
    widgetStyle() {
      const data = this.mergedData
      return {
        backgroundColor: data.backgroundColor || 'transparent',
        paddingLeft: `${Math.max(0, Number(data.marginHorizontal) || 0)}px`,
        paddingRight: `${Math.max(0, Number(data.marginHorizontal) || 0)}px`,
        paddingTop: `${Math.max(0, Number(data.marginTop) || 0)}px`,
        paddingBottom: `${Math.max(0, Number(data.marginBottom) || 0)}px`,
        width: '100%',
        boxSizing: 'border-box'
      }
    },

    // 计算分割线样式类
    lineClass() {
      const data = this.mergedData
      const style = data.style || 'solid'
      return `line-${style}`
    },

    // 计算分割线样式
    lineStyle() {
      const data = this.mergedData
      const color = data.color || '#e5e5e5'
      const thickness = Math.max(1, Number(data.thickness) || 1)
      
      return {
        borderColor: color,
        borderTopWidth: `${thickness}px`,
        borderTopStyle: data.style || 'solid',
        margin: '0',
        width: '100%',
        height: '0'
      }
    }
  },

  mounted() {
    console.log('Divider组件初始化:', {
      config: this.config,
      styles: this.styles,
      mergedData: this.mergedData,
      widgetStyle: this.widgetStyle,
      lineStyle: this.lineStyle
    })
  }
}
</script>

<style scoped>
.divider-widget {
  width: 100%;
  box-sizing: border-box;
}

.divider-line {
  width: 100%;
  height: 0;
  border-top-style: solid;
  border-left: none;
  border-right: none;
  border-bottom: none;
  box-sizing: border-box;
}

/* 实线样式 */
.line-solid {
  border-top-style: solid;
}

/* 虚线样式 */
.line-dashed {
  border-top-style: dashed;
}

/* 点状样式 */
.line-dotted {
  border-top-style: dotted;
}

/* 确保容器不会超出屏幕 */
.divider-widget,
.divider-line {
  max-width: 100%;
  box-sizing: border-box;
}
</style>
