<template>
  <view class="notice-bar" :style="containerStyle" v-if="config.content">
    <view class="notice-content">
      <text class="notice-icon" v-if="config.icon">📢</text>
      <view class="notice-text-container">
        <text class="notice-text" :class="{ 'scrollable': config.scrollable }">
          {{ config.content }}
        </text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'NoticeBar',
  props: {
    config: {
      type: Object,
      default: () => ({})
    },
    styles: {
      type: Object,
      default: () => ({})
    }
  },

  computed: {
    containerStyle() {
      const styles = this.styles || {}
      return {
        backgroundColor: styles.bg_color || '#FFF7E6',
        color: styles.text_color || '#FF8C00',
        padding: `${styles.padding_top || 8}rpx ${styles.padding_horizontal || 20}rpx ${styles.padding_bottom || 8}rpx`,
        borderRadius: `${styles.border_radius || 0}rpx`
      }
    }
  }
}
</script>

<style scoped>
.notice-bar {
  display: flex;
  align-items: center;
  overflow: hidden;
}

.notice-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.notice-icon {
  margin-right: 16rpx;
  font-size: 28rpx;
}

.notice-text-container {
  flex: 1;
  overflow: hidden;
}

.notice-text {
  font-size: 28rpx;
  line-height: 1.4;
}

.notice-text.scrollable {
  white-space: nowrap;
  animation: scroll 10s linear infinite;
}

@keyframes scroll {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}
</style> 