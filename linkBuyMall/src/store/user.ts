import { defineStore } from 'pinia'
import type { User, Address, LoginParams, RegisterParams } from '@/types'
import { login as loginApi, register as registerApi, wechatLogin, sendSmsCode } from '@/api'
import { getPlatformType } from '@/utils/platform'

export type UserType = 'guest' | 'registered'

interface UserState {
  // 用户信息
  userInfo: User | null
  // 登录状态
  isLoggedIn: boolean
  // 用户token
  token: string
  // 用户类型
  userType: UserType
  // 平台类型
  platform: string
  // 购物车数量
  cartCount: number
  // 地址列表
  addressList: Address[]
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    userInfo: null,
    isLoggedIn: false,
    token: '',
    userType: 'guest',
    platform: getPlatformType(),
    cartCount: 0,
    addressList: []
  }),

  getters: {
    // 获取用户昵称
    nickname: (state): string => state.userInfo?.nickname || '未登录',

    // 获取用户头像
    avatar: (state): string => state.userInfo?.avatar || '/static/default-avatar.png',

    // 获取用户类型
    getUserType: (state): UserType => state.userType,

    // 获取平台类型
    getPlatform: (state): string => state.platform,

    // 是否为游客
    isGuest: (state): boolean => state.userType === 'guest',

    // 是否为注册用户
    isRegistered: (state): boolean => state.userType === 'registered',

    // 获取默认地址
    defaultAddress: (state): Address | null => {
      return state.addressList.find((addr: Address) => addr.isDefault) || null
    },

    // 是否有地址
    hasAddress: (state): boolean => state.addressList.length > 0
  },

  actions: {
    // 设置用户信息
    setUserInfo(userInfo: User): void {
      this.userInfo = userInfo
      this.isLoggedIn = true
      this.userType = 'registered'
      // 保存到本地存储
      uni.setStorageSync('userInfo', userInfo)
    },

    // 设置token
    setToken(token: string): void {
      this.token = token
      this.isLoggedIn = !!token
      // 保存到本地存储
      uni.setStorageSync('token', token)
    },

    // 统一登录方法
    async login(params: LoginParams): Promise<{ success: boolean; message?: string }> {
      try {
        // 添加平台信息
        const loginData = {
          ...params,
          platform: this.platform
        }

        const response = await loginApi(loginData)

        if (response.code === 200) {
          this.setUserInfo(response.data.user)
          this.setToken(response.data.token)

          // 记住密码功能
          if (params.rememberMe && params.loginType === 'password') {
            uni.setStorageSync('rememberedLogin', {
              mobile: params.mobile,
              password: params.password
            })
          } else {
            uni.removeStorageSync('rememberedLogin')
          }

          console.log('用户登录成功:', response.data.user.nickname)
          return { success: true }
        } else {
          return { success: false, message: response.msg || '登录失败' }
        }
      } catch (error: any) {
        console.error('登录失败:', error)
        return { success: false, message: error.message || '网络错误' }
      }
    },

    // 微信登录
    async wechatLogin(code: string): Promise<{ success: boolean; message?: string }> {
      try {
        const response = await wechatLogin({
          code,
          platform: this.platform as 'wechat-h5' | 'mp-weixin'
        })

        if (response.code === 200) {
          this.setUserInfo(response.data.user)
          this.setToken(response.data.token)
          console.log('微信登录成功:', response.data.user.nickname)
          return { success: true }
        } else {
          return { success: false, message: response.msg || '微信登录失败' }
        }
      } catch (error: any) {
        console.error('微信登录失败:', error)
        return { success: false, message: error.message || '微信登录失败' }
      }
    },

    // 用户注册
    async register(params: RegisterParams): Promise<{ success: boolean; message?: string }> {
      try {
        const registerData = {
          ...params,
          platform: this.platform
        }

        const response = await registerApi(registerData)

        if (response.code === 200) {
          // 注册成功后自动登录
          const loginResult = await this.login({
            loginType: 'password',
            platform: this.platform,
            mobile: params.mobile,
            password: params.password
          })

          return loginResult
        } else {
          return { success: false, message: response.msg || '注册失败' }
        }
      } catch (error: any) {
        console.error('注册失败:', error)
        return { success: false, message: error.message || '注册失败' }
      }
    },

    // 发送验证码
    async sendSmsCode(mobile: string, type: 'login' | 'register' | 'reset'): Promise<{ success: boolean; message?: string }> {
      try {
        const response = await sendSmsCode({ mobile, type })

        if (response.code === 200) {
          return { success: true, message: '验证码发送成功' }
        } else {
          return { success: false, message: response.msg || '验证码发送失败' }
        }
      } catch (error: any) {
        console.error('发送验证码失败:', error)
        return { success: false, message: error.message || '发送验证码失败' }
      }
    },

    // 退出登录
    logout(): void {
      this.userInfo = null
      this.isLoggedIn = false
      this.token = ''
      this.userType = 'guest'
      this.cartCount = 0
      this.addressList = []

      // 清除本地存储
      uni.removeStorageSync('userInfo')
      uni.removeStorageSync('token')

      console.log('用户已退出登录')
    },

    // 更新用户信息
    updateUserInfo(userInfo: Partial<User>): void {
      if (this.userInfo) {
        this.userInfo = { ...this.userInfo, ...userInfo }
        // 更新本地存储
        uni.setStorageSync('userInfo', this.userInfo)
      }
    },

    // 恢复用户信息（从本地存储）
    restoreUserInfo(): void {
      try {
        const userInfo = uni.getStorageSync('userInfo') as User | null
        const token = uni.getStorageSync('token') as string
        
        if (userInfo && token) {
          this.userInfo = userInfo
          this.token = token
          this.isLoggedIn = true
          console.log('用户信息恢复成功:', userInfo.nickname)
        }
      } catch (error) {
        console.error('恢复用户信息失败:', error)
        this.logout()
      }
    },

    // 设置购物车数量
    setCartCount(count: number): void {
      this.cartCount = count
    },

    // 增加购物车数量
    addCartCount(count: number = 1): void {
      this.cartCount += count
    },

    // 减少购物车数量
    reduceCartCount(count: number = 1): void {
      this.cartCount = Math.max(0, this.cartCount - count)
    },

    // 设置地址列表
    setAddressList(addressList: Address[]): void {
      this.addressList = addressList
    },

    // 添加地址
    addAddress(address: Address): void {
      // 如果是默认地址，先取消其他地址的默认状态
      if (address.isDefault) {
        this.addressList.forEach((addr: Address) => {
          addr.isDefault = false
        })
      }
      this.addressList.push(address)
    },

    // 更新地址
    updateAddress(addressId: number, addressData: Partial<Address>): void {
      const index = this.addressList.findIndex((addr: Address) => addr.id === addressId)
      if (index !== -1) {
        // 如果设置为默认地址，先取消其他地址的默认状态
        if (addressData.isDefault) {
          this.addressList.forEach((addr: Address) => {
            addr.isDefault = false
          })
        }
        this.addressList[index] = { ...this.addressList[index], ...addressData }
      }
    },

    // 删除地址
    removeAddress(addressId: number): void {
      const index = this.addressList.findIndex((addr: Address) => addr.id === addressId)
      if (index !== -1) {
        this.addressList.splice(index, 1)
      }
    },

    // 设置默认地址
    setDefaultAddress(addressId: number): void {
      this.addressList.forEach((addr: Address) => {
        addr.isDefault = addr.id === addressId
      })
    }
  }
}) 