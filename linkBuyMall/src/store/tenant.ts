import { defineStore } from 'pinia'
import { getMpScode, validateScodeFormat, extractScodeFromUrl } from '@/utils/tenant'
import { validateScode as validateScodeApi, getTenantInfo, getTenantConfig } from '@/api/tenant'

interface TenantState {
  // 商城代码，用于标识不同的租户
  scode: string
  // 租户信息
  tenantInfo: {
    id?: number
    name?: string
    logo?: string
    theme?: string
  } | null
  // 是否已初始化
  initialized: boolean
}

export const useTenantStore = defineStore('tenant', {
  state: (): TenantState => ({
    scode: '',
    tenantInfo: null,
    initialized: false
  }),

  getters: {
    // 获取商城代码
    getStoreCode: (state): string => state.scode,
    
    // 获取租户名称
    getTenantName: (state): string => state.tenantInfo?.name || '商城',
    
    // 获取租户Logo
    getTenantLogo: (state): string => state.tenantInfo?.logo || '/static/logo.png',
    
    // 是否有有效的scode
    hasValidScode: (state): boolean => !!state.scode && state.scode.length > 0
  },

  actions: {
    // 设置商城代码
    setScode(scode: string): void {
      this.scode = scode
      this.initialized = true
      // 保存到本地存储
      uni.setStorageSync('scode', scode)
      console.log('设置商城代码:', scode)
    },

    // 设置租户信息
    setTenantInfo(tenantInfo: any): void {
      this.tenantInfo = tenantInfo
      // 保存到本地存储
      uni.setStorageSync('tenantInfo', tenantInfo)
      console.log('设置租户信息:', tenantInfo)
    },

    // 从本地存储恢复scode
    restoreScode(): void {
      try {
        const scode = uni.getStorageSync('scode') as string
        const tenantInfo = uni.getStorageSync('tenantInfo')
        
        if (scode) {
          this.scode = scode
          this.tenantInfo = tenantInfo
          this.initialized = true
          console.log('恢复商城代码:', scode)
        }
      } catch (error) {
        console.error('恢复商城代码失败:', error)
      }
    },

    // 从URL参数获取scode (H5环境)
    getScodeFromUrl(): string | null {
      // #ifdef H5
      try {
        const scode = extractScodeFromUrl(window.location.href)
        if (scode) {
          console.log('从URL获取到scode:', scode)
          return scode
        }
      } catch (error) {
        console.error('从URL获取scode失败:', error)
      }
      // #endif
      return null
    },

    // 从小程序获取scode（使用appid）
    getScodeFromConfig(): string | null {
      // #ifdef MP-WEIXIN
      try {
        const scode = getMpScode() // 获取小程序appid作为scode
        if (scode && validateScodeFormat(scode)) {
          console.log('从小程序获取到scode(appid):', scode)
          return scode
        }
      } catch (error) {
        console.error('从小程序获取scode失败:', error)
      }
      // #endif
      return null
    },

    // 初始化scode
    async initScode(): Promise<boolean> {
      // 防止重复初始化
      if (this.initialized && this.hasValidScode) {
        console.log('scode已初始化，跳过重复初始化')
        return true
      }

      // 先尝试从本地存储恢复
      this.restoreScode()
      if (this.hasValidScode) {
        this.initialized = true
        return true
      }

      let scode: string | null = null

      try {
        // H5环境：从URL参数获取
        // #ifdef H5
        scode = this.getScodeFromUrl()
        if (scode) {
          this.setScode(scode)
          return true
        }
        // #endif

        // 小程序环境：从配置获取
        // #ifdef MP-WEIXIN
        scode = this.getScodeFromConfig()
        if (scode) {
          this.setScode(scode)
          return true
        }
        // #endif

        // 如果都没有获取到，返回false
        console.warn('未能获取到有效的scode')
        this.initialized = true // 标记为已初始化，避免重复尝试
        return false
      } catch (error) {
        console.error('初始化scode时发生错误:', error)
        this.initialized = true // 标记为已初始化，避免重复尝试
        return false
      }
    },

    // 清除租户信息
    clearTenant(): void {
      this.scode = ''
      this.tenantInfo = null
      this.initialized = false
      
      // 清除本地存储
      uni.removeStorageSync('scode')
      uni.removeStorageSync('tenantInfo')
      
      console.log('已清除租户信息')
    },

    // 验证scode是否有效（可以调用API验证）
    async validateScode(): Promise<boolean> {
      if (!this.hasValidScode) {
        return false
      }

      try {
        // 这里可以调用API验证scode是否有效
        // const response = await validateScodeApi(this.scode)
        // return response.code === 200
        
        // 暂时返回true，实际项目中应该调用API验证
        return true
      } catch (error) {
        console.error('验证scode失败:', error)
        return false
      }
    }
  }
}) 