import { defineStore } from 'pinia'
import { getLocationWithPermission, getLocationSmart, getLocationSimple, wgs84ToGcj02, gcj02ToBd09, type LocationInfo, type LocationError } from '@/utils/location'

interface LocationState {
  locationInfo: LocationInfo | null
  isLocationLoading: boolean
  locationError: LocationError | null
  lastUpdateTime: number
}

export const useLocationStore = defineStore('location', {
  state: (): LocationState => ({
    locationInfo: null,
    isLocationLoading: false,
    locationError: null,
    lastUpdateTime: 0
  }),

  getters: {
    hasLocation: (state): boolean => state.locationInfo !== null,
    
    locationText: (state): string => {
      if (!state.locationInfo) return '未知位置'
      
      // 优先显示地址信息
      if (state.locationInfo.address) {
        return state.locationInfo.address
      }
      
      // 如果有城市信息，显示城市
      if (state.locationInfo.city) {
        const province = state.locationInfo.province || ''
        return `${province}${state.locationInfo.city}`
      }
      
      // 否则显示经纬度
      return `${state.locationInfo.latitude.toFixed(4)}, ${state.locationInfo.longitude.toFixed(4)}`
    },
    
    hasBd09Location: (state): boolean => !!state.locationInfo?.bd09,
    
    bd09Coords: (state) => {
      return state.locationInfo?.bd09 || null
    },
    
    bd09Text: (state): string => {
      if (!state.locationInfo?.bd09) return '百度坐标未知'
      const { latitude, longitude } = state.locationInfo.bd09
      return `BD-09: ${latitude.toFixed(6)}, ${longitude.toFixed(6)}`
    },
    
    gcj02Text: (state): string => {
      if (!state.locationInfo) return 'GCJ02坐标未知'
      return `GCJ02: ${state.locationInfo.latitude.toFixed(6)}, ${state.locationInfo.longitude.toFixed(6)}`
    }
  },

  actions: {
    /**
     * 直接获取WGS84坐标并转换为GCJ02保存
     */
    async getLocationWgs84AndConvert(options: {
      altitude?: boolean
      timeout?: number
      showLoading?: boolean
      loadingText?: string
    } = {}, forceRefresh = false): Promise<LocationInfo> {
      // 如果不强制刷新且有缓存数据且缓存时间小于5分钟，直接返回缓存
      const now = Date.now()
      if (!forceRefresh && this.locationInfo && (now - this.lastUpdateTime) < 5 * 60 * 1000) {
        console.log('使用缓存的位置信息')
        return this.locationInfo
      }
      
      try {
        this.isLocationLoading = true
        this.locationError = null
        
        console.log('开始获取WGS84位置信息并转换为GCJ02...')
        
        // 直接使用简单获取方法获取WGS84坐标
        const wgs84Location = await getLocationSimple({
          ...options,
          showLoading: false // 这里不显示加载，由外层控制
        })
        
        console.log('WGS84位置获取成功:', wgs84Location)
        
        // 转换为GCJ02坐标
        const gcj02Coords = wgs84ToGcj02(wgs84Location.latitude, wgs84Location.longitude)
        
        // 转换为BD-09坐标
        const bd09Coords = gcj02ToBd09(gcj02Coords.latitude, gcj02Coords.longitude)
        
        console.log('坐标转换结果:', {
          wgs84: { lat: wgs84Location.latitude, lng: wgs84Location.longitude },
          gcj02: { lat: gcj02Coords.latitude, lng: gcj02Coords.longitude },
          bd09: { lat: bd09Coords.latitude, lng: bd09Coords.longitude }
        })
        
        // 创建转换后的位置信息（包含GCJ02和BD-09坐标）
        const convertedLocation: LocationInfo = {
          ...wgs84Location,
          latitude: gcj02Coords.latitude, // 主坐标使用GCJ02
          longitude: gcj02Coords.longitude, // 主坐标使用GCJ02
          bd09: { // 同时保存BD-09坐标
            latitude: bd09Coords.latitude,
            longitude: bd09Coords.longitude
          }
        }
        
        this.locationInfo = convertedLocation
        this.lastUpdateTime = now
        
        console.log('位置信息获取并转换成功:', convertedLocation)
        return convertedLocation
        
      } catch (error) {
        console.error('获取WGS84位置并转换失败:', error)
        this.locationError = error as LocationError
        
        // 显示错误提示
        uni.showToast({
          title: (error as LocationError).message || '获取位置失败',
          icon: 'none',
          duration: 2000
        })
        
        throw error
      } finally {
        this.isLocationLoading = false
      }
    },

    /**
     * 获取位置信息（兼容旧版本）
     */
    async getLocation(options: {
      type?: 'wgs84' | 'gcj02'
      altitude?: boolean
      timeout?: number
      showLoading?: boolean
      loadingText?: string
    } = {}, forceRefresh = false): Promise<LocationInfo> {
      // 如果不强制刷新且有缓存数据且缓存时间小于5分钟，直接返回缓存
      const now = Date.now()
      if (!forceRefresh && this.locationInfo && (now - this.lastUpdateTime) < 5 * 60 * 1000) {
        console.log('使用缓存的位置信息')
        return this.locationInfo
      }
      
      try {
        this.isLocationLoading = true
        this.locationError = null
        
        console.log('开始获取位置信息...')
        
        // 直接使用WGS84转换方法
        const location = await this.getLocationWgs84AndConvert(options, forceRefresh)
        
        console.log('位置信息获取成功:', location)
        return location
        
      } catch (error) {
        console.error('获取位置信息失败:', error)
        this.locationError = error as LocationError
        
        // 显示错误提示
        uni.showToast({
          title: (error as LocationError).message || '获取位置失败',
          icon: 'none',
          duration: 2000
        })
        
        throw error
      } finally {
        this.isLocationLoading = false
      }
    },
    
    /**
     * 清除位置信息
     */
    clearLocation(): void {
      this.locationInfo = null
      this.locationError = null
      this.lastUpdateTime = 0
    },
    
    /**
     * 重新获取位置信息
     */
    async refreshLocation(options = {}): Promise<LocationInfo> {
      return await this.getLocationWgs84AndConvert(options, true)
    },
    
    /**
     * 初始化位置信息（应用启动时调用）
     */
    async initLocation(options: {
      silent?: boolean // 是否静默获取（不显示加载提示）
      timeout?: number
    } = {}): Promise<LocationInfo | null> {
      const { silent = false, timeout = 8000 } = options
      
      try {
        console.log('初始化位置信息...')
        
        const locationOptions = {
          timeout,
          showLoading: !silent,
          loadingText: '正在获取位置信息...'
        }
        
        const location = await this.getLocationWgs84AndConvert(locationOptions, false)
        console.log('位置信息初始化成功')
        return location
        
      } catch (error) {
        console.error('位置信息初始化失败:', error)
        
        if (!silent) {
          uni.showToast({
            title: '获取位置信息失败',
            icon: 'none',
            duration: 2000
          })
        }
        
        return null
      }
    }
  }
}) 