/**
 * 微信相关工具函数
 */

import { getPlatformType } from './platform'

/**
 * 获取微信登录授权码
 */
export function getWechatLoginCode(): Promise<string> {
  return new Promise((resolve, reject) => {
    const platform = getPlatformType()
    
    if (platform === 'mp-weixin') {
      // 微信小程序登录
      // #ifdef MP-WEIXIN
      uni.login({
        provider: 'weixin',
        success: (res) => {
          if (res.code) {
            resolve(res.code)
          } else {
            reject(new Error('获取微信登录code失败'))
          }
        },
        fail: (error) => {
          reject(new Error('微信登录失败: ' + error.errMsg))
        }
      })
      // #endif
    } else if (platform === 'wechat-h5') {
      // 微信公众号H5登录
      // #ifdef H5
      // 这里需要根据实际的微信公众号配置来实现
      // 通常需要跳转到微信授权页面
      reject(new Error('微信公众号H5登录功能待实现'))
      // #endif
    } else {
      reject(new Error('当前平台不支持微信登录'))
    }
  })
}

/**
 * 获取微信用户信息
 */
export function getWechatUserInfo(): Promise<any> {
  return new Promise((resolve, reject) => {
    const platform = getPlatformType()
    
    if (platform === 'mp-weixin') {
      // #ifdef MP-WEIXIN
      uni.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          resolve(res.userInfo)
        },
        fail: (error) => {
          reject(new Error('获取微信用户信息失败: ' + error.errMsg))
        }
      })
      // #endif
    } else {
      reject(new Error('当前平台不支持获取微信用户信息'))
    }
  })
}

/**
 * 检查微信登录状态
 */
export function checkWechatLoginStatus(): Promise<boolean> {
  return new Promise((resolve) => {
    const platform = getPlatformType()
    
    if (platform === 'mp-weixin') {
      // #ifdef MP-WEIXIN
      uni.checkSession({
        success: () => {
          resolve(true)
        },
        fail: () => {
          resolve(false)
        }
      })
      // #endif
    } else {
      resolve(false)
    }
  })
}

/**
 * 微信支付
 */
export function wechatPay(paymentData: any): Promise<any> {
  return new Promise((resolve, reject) => {
    const platform = getPlatformType()
    
    if (platform === 'mp-weixin') {
      // #ifdef MP-WEIXIN
      uni.requestPayment({
        ...paymentData,
        success: (res) => {
          resolve(res)
        },
        fail: (error) => {
          reject(new Error('微信支付失败: ' + error.errMsg))
        }
      })
      // #endif
    } else if (platform === 'wechat-h5') {
      // #ifdef H5
      // H5微信支付
      // 这里需要根据实际的微信支付配置来实现
      reject(new Error('H5微信支付功能待实现'))
      // #endif
    } else {
      reject(new Error('当前平台不支持微信支付'))
    }
  })
}

/**
 * 微信分享
 */
export function wechatShare(shareData: {
  title: string
  desc: string
  link: string
  imgUrl: string
}): Promise<void> {
  return new Promise((resolve, reject) => {
    const platform = getPlatformType()
    
    if (platform === 'mp-weixin') {
      // #ifdef MP-WEIXIN
      uni.share({
        provider: 'weixin',
        scene: 'WXSceneSession',
        type: 0,
        href: shareData.link,
        title: shareData.title,
        summary: shareData.desc,
        imageUrl: shareData.imgUrl,
        success: () => {
          resolve()
        },
        fail: (error) => {
          reject(new Error('微信分享失败: ' + error.errMsg))
        }
      })
      // #endif
    } else {
      reject(new Error('当前平台不支持微信分享'))
    }
  })
}

/**
 * 设置微信小程序导航栏标题
 */
export function setWechatNavigationTitle(title: string): void {
  const platform = getPlatformType()
  
  if (platform === 'mp-weixin') {
    // #ifdef MP-WEIXIN
    uni.setNavigationBarTitle({
      title: title
    })
    // #endif
  }
}

/**
 * 微信小程序跳转到其他小程序
 */
export function navigateToMiniProgram(options: {
  appId: string
  path?: string
  extraData?: any
}): Promise<void> {
  return new Promise((resolve, reject) => {
    const platform = getPlatformType()
    
    if (platform === 'mp-weixin') {
      // #ifdef MP-WEIXIN
      uni.navigateToMiniProgram({
        appId: options.appId,
        path: options.path,
        extraData: options.extraData,
        success: () => {
          resolve()
        },
        fail: (error) => {
          reject(new Error('跳转小程序失败: ' + error.errMsg))
        }
      })
      // #endif
    } else {
      reject(new Error('当前平台不支持跳转小程序'))
    }
  })
}
