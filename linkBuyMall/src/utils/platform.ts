/**
 * 平台检测工具
 * 根据PRD文档要求，检测当前运行平台
 */

export type PlatformType = 'h5' | 'wechat-h5' | 'mp-weixin'

/**
 * 获取当前平台类型
 */
export function getPlatformType(): PlatformType {
  // #ifdef H5
  const ua = navigator.userAgent
  if (ua.includes('MicroMessenger')) {
    return 'wechat-h5'  // 微信公众号H5
  }
  return 'h5'  // 纯H5
  // #endif
  
  // #ifdef MP-WEIXIN
  return 'mp-weixin'  // 微信小程序
  // #endif
  
  // 默认返回H5
  return 'h5'
}

/**
 * 判断是否为微信环境
 */
export function isWechatEnv(): boolean {
  const platform = getPlatformType()
  return platform === 'wechat-h5' || platform === 'mp-weixin'
}

/**
 * 判断是否为小程序环境
 */
export function isMiniProgram(): boolean {
  return getPlatformType() === 'mp-weixin'
}

/**
 * 判断是否为H5环境
 */
export function isH5(): boolean {
  const platform = getPlatformType()
  return platform === 'h5' || platform === 'wechat-h5'
}

/**
 * 获取平台显示名称
 */
export function getPlatformName(): string {
  const platform = getPlatformType()
  const names = {
    'h5': '浏览器',
    'wechat-h5': '微信公众号',
    'mp-weixin': '微信小程序'
  }
  return names[platform] || '未知平台'
}

/**
 * 获取平台支持的登录方式
 */
export function getSupportedLoginMethods() {
  const platform = getPlatformType()
  
  switch (platform) {
    case 'h5':
      return {
        password: true,      // 手机号+密码
        sms: true,          // 手机号+验证码
        wechat: false,      // 微信一键登录
        guest: true         // 游客模式
      }
    case 'wechat-h5':
      return {
        password: true,      // 手机号+密码（备选）
        sms: true,          // 手机号+验证码（备选）
        wechat: true,       // 微信一键登录（主要）
        guest: true         // 游客模式
      }
    case 'mp-weixin':
      return {
        password: true,      // 手机号+密码（备选）
        sms: true,          // 手机号+验证码（备选）
        wechat: true,       // 微信一键登录（主要）
        guest: true         // 游客模式
      }
    default:
      return {
        password: true,
        sms: false,
        wechat: false,
        guest: true
      }
  }
}
