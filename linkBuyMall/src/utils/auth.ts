/**
 * 权限检查工具
 */

import { useUserStore } from '@/store/user'

/**
 * 检查用户是否已登录
 */
export function checkLogin(): boolean {
  const userStore = useUserStore()
  return userStore.isLoggedIn
}

/**
 * 检查用户是否为注册用户
 */
export function checkRegistered(): boolean {
  const userStore = useUserStore()
  return userStore.isRegistered
}

/**
 * 检查用户是否为游客
 */
export function checkGuest(): boolean {
  const userStore = useUserStore()
  return userStore.isGuest
}

/**
 * 需要登录的页面权限检查
 */
export function requireLogin(showModal: boolean = true): boolean {
  if (!checkLogin()) {
    if (showModal) {
      uni.showModal({
        title: '提示',
        content: '请先登录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            uni.navigateTo({
              url: '/pages/login/login'
            })
          }
        }
      })
    }
    return false
  }
  return true
}

/**
 * 需要注册用户权限的功能检查
 */
export function requireRegistered(showModal: boolean = true): boolean {
  if (!checkLogin()) {
    return requireLogin(showModal)
  }
  
  if (!checkRegistered()) {
    if (showModal) {
      uni.showModal({
        title: '提示',
        content: '该功能需要完成注册',
        confirmText: '去注册',
        success: (res) => {
          if (res.confirm) {
            uni.navigateTo({
              url: '/pages/register/register'
            })
          }
        }
      })
    }
    return false
  }
  return true
}

/**
 * 购物车权限检查
 */
export function checkCartPermission(): boolean {
  if (checkGuest()) {
    uni.showModal({
      title: '提示',
      content: '游客无法使用购物车功能，请先登录',
      confirmText: '去登录',
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: '/pages/login/login'
          })
        }
      }
    })
    return false
  }
  return true
}

/**
 * 下单权限检查
 */
export function checkOrderPermission(): boolean {
  if (checkGuest()) {
    uni.showModal({
      title: '提示',
      content: '游客无法下单，请先登录',
      confirmText: '去登录',
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: '/pages/login/login'
          })
        }
      }
    })
    return false
  }
  
  return requireRegistered()
}

/**
 * 个人中心权限检查
 */
export function checkProfilePermission(): boolean {
  return requireLogin()
}

/**
 * 地址管理权限检查
 */
export function checkAddressPermission(): boolean {
  return requireRegistered()
}

/**
 * 收藏权限检查
 */
export function checkFavoritePermission(): boolean {
  return requireLogin()
}

/**
 * 评价权限检查
 */
export function checkReviewPermission(): boolean {
  return requireRegistered()
}

/**
 * 自动登录检查
 */
export function autoLogin(): void {
  try {
    const token = uni.getStorageSync('token')
    const userInfo = uni.getStorageSync('userInfo')
    
    if (token && userInfo) {
      const userStore = useUserStore()
      userStore.setToken(token)
      userStore.setUserInfo(userInfo)
      console.log('自动登录成功')
    }
  } catch (error) {
    console.error('自动登录失败:', error)
  }
}

/**
 * 页面权限拦截器
 */
export function pageAuthInterceptor(url: string): boolean {
  // 需要登录的页面列表
  const loginRequiredPages = [
    '/pages/user/user',
    '/pages/cart/cart',
    '/pages/order/order',
    '/pages/address/address'
  ]
  
  // 需要注册用户的页面列表
  const registeredRequiredPages = [
    '/pages/order/order',
    '/pages/address/address'
  ]
  
  // 检查是否需要登录
  if (loginRequiredPages.some(page => url.includes(page))) {
    return requireLogin()
  }
  
  // 检查是否需要注册用户权限
  if (registeredRequiredPages.some(page => url.includes(page))) {
    return requireRegistered()
  }
  
  return true
}

/**
 * API请求权限拦截器
 */
export function apiAuthInterceptor(apiUrl: string): boolean {
  // 需要登录的API列表
  const loginRequiredApis = [
    '/user/info',
    '/cart/',
    '/order/',
    '/address/',
    '/favorite/'
  ]
  
  // 需要注册用户的API列表
  const registeredRequiredApis = [
    '/order/',
    '/address/',
    '/review/'
  ]
  
  // 检查是否需要登录
  if (loginRequiredApis.some(api => apiUrl.includes(api))) {
    return checkLogin()
  }
  
  // 检查是否需要注册用户权限
  if (registeredRequiredApis.some(api => apiUrl.includes(api))) {
    return checkRegistered()
  }
  
  return true
}

/**
 * 初始化权限系统
 */
export function initAuth(): void {
  // 自动登录
  autoLogin()
  
  // 设置页面拦截器
  const originalNavigateTo = uni.navigateTo
  uni.navigateTo = function(options: any) {
    if (pageAuthInterceptor(options.url)) {
      return originalNavigateTo.call(this, options)
    }
  }
  
  const originalSwitchTab = uni.switchTab
  uni.switchTab = function(options: any) {
    if (pageAuthInterceptor(options.url)) {
      return originalSwitchTab.call(this, options)
    }
  }
  
  console.log('权限系统初始化完成')
}
