/**
 * 租户相关工具函数
 */

// 小程序scode配置
// 小程序环境下优先使用appid作为scode，这里的配置作为备用
export const MP_SCODE_CONFIG = {
  // 默认scode，当无法获取appid时使用
  default: 'MALL_1748873951003',
  // 开发环境备用scode
  development: 'MALL_1748873951003',
  // 生产环境备用scode
  production: ''
}

/**
 * 获取小程序的scode配置
 * @returns {string} scode
 */
export function getMpScode(): string {
  // #ifdef MP-WEIXIN
  // 小程序环境下使用appid作为scode
  try {
    const accountInfo = uni.getAccountInfoSync()
    const appId = accountInfo?.miniProgram?.appId
    console.log('accountInfo', accountInfo)
    if (appId) {
      console.log('获取到小程序appId作为scode:', appId)
      return appId
    }
  } catch (error) {
    console.error('获取小程序appId失败:', error)
  }
  
  // 如果获取appId失败，使用配置的默认值
  console.log('使用默认scode配置')
  return MP_SCODE_CONFIG.development || MP_SCODE_CONFIG.default
  // #endif
  
  // 非小程序环境返回空字符串
  return ''
}

/**
 * 验证scode格式是否正确
 * @param {string} scode 
 * @returns {boolean}
 */
export function validateScodeFormat(scode: string): boolean {
  if (!scode || typeof scode !== 'string') {
    return false
  }
  
  // 检查是否是微信小程序appid格式 (wx开头，18位字符)
  const wechatAppIdRegex = /^wx[a-zA-Z0-9]{16}$/
  if (wechatAppIdRegex.test(scode)) {
    return true
  }
  
  // 普通scode格式：字母数字组合，长度在3-30之间，支持下划线
  const scodeRegex = /^[a-zA-Z0-9_]{3,30}$/
  return scodeRegex.test(scode)
}

/**
 * 从URL中提取scode参数
 * @param {string} url 
 * @returns {string | null}
 */
export function extractScodeFromUrl(url: string): string | null {
  try {
    console.log('正在解析URL:', url)
    
    // 先尝试从普通URL参数中获取
    const urlObj = new URL(url)
    let scode = urlObj.searchParams.get('scode')
    
    if (scode && validateScodeFormat(scode)) {
      console.log('从URL参数获取到scode:', scode)
      return scode
    }
    
    // 如果普通参数中没有，尝试从hash部分获取
    const hash = urlObj.hash
    if (hash) {
      console.log('解析hash部分:', hash)
      
      // 处理 #/?scode=xxx 格式
      if (hash.includes('?')) {
        const hashParams = hash.split('?')[1]
        const params = new URLSearchParams(hashParams)
        scode = params.get('scode')
        
        if (scode && validateScodeFormat(scode)) {
          console.log('从hash参数获取到scode:', scode)
          return scode
        }
      }
      
      // 处理 #/path?scode=xxx 格式
      const hashParts = hash.split('?')
      if (hashParts.length > 1) {
        const params = new URLSearchParams(hashParts[1])
        scode = params.get('scode')
        
        if (scode && validateScodeFormat(scode)) {
          console.log('从hash路径参数获取到scode:', scode)
          return scode
        }
      }
    }
    
    console.log('未能从URL中解析到有效的scode')
    return null
  } catch (error) {
    console.error('解析URL失败:', error)
    return null
  }
}

/**
 * 构建带有scode参数的URL
 * @param {string} baseUrl 基础URL
 * @param {string} scode 商城代码
 * @returns {string}
 */
export function buildUrlWithScode(baseUrl: string, scode: string): string {
  if (!scode || !validateScodeFormat(scode)) {
    return baseUrl
  }
  
  try {
    const url = new URL(baseUrl)
    url.searchParams.set('scode', scode)
    return url.toString()
  } catch (error) {
    // 如果不是完整URL，直接拼接参数
    const separator = baseUrl.includes('?') ? '&' : '?'
    return `${baseUrl}${separator}scode=${encodeURIComponent(scode)}`
  }
}

/**
 * 获取当前环境的租户配置
 * @returns {object}
 */
export function getTenantConfig() {
  return {
    // H5环境配置
    h5: {
      // H5环境下必须从URL参数获取scode
      requireScodeInUrl: true,
      // 默认跳转页面（当没有scode时）
      defaultRedirectPage: '/pages/error/no-scode'
    },
    
    // 小程序环境配置
    mp: {
      // 小程序的scode配置（使用appid作为scode）
      scode: getMpScode(),
      // 是否允许动态切换租户（小程序不允许，因为appid是固定的）
      allowDynamicSwitch: false
    }
  }
} 