/// <reference types="vite/client" />

/**
 * 环境工具函数
 */

// 获取当前环境
export function getCurrentEnv(): 'development' | 'test' | 'production' {
  try {
    return import.meta.env.MODE as 'development' | 'test' | 'production'
  } catch (error) {
    console.warn('获取环境模式失败，使用默认值:', error)
    return 'development'
  }
}

// 获取API基础URL
export function getApiBaseUrl(): string {
  try {
    return import.meta.env.VITE_API_BASE_URL || 'http://localhost:8083'
  } catch (error) {
    console.warn('获取API基础URL失败，使用默认值:', error)
    return 'http://localhost:8083'
  }
}

// 获取应用标题
export function getAppTitle(): string {
  try {
    return import.meta.env.VITE_APP_TITLE || 'LinkBuy商城'
  } catch (error) {
    console.warn('获取应用标题失败，使用默认值:', error)
    return 'LinkBuy商城'
  }
}

// 是否为调试模式
export function isDebugMode(): boolean {
  try {
    return import.meta.env.VITE_DEBUG_MODE === 'true'
  } catch (error) {
    console.warn('获取调试模式失败，使用默认值:', error)
    return false
  }
}

// 是否为开发环境
export function isDevelopment(): boolean {
  return getCurrentEnv() === 'development'
}

// 是否为测试环境
export function isTest(): boolean {
  return getCurrentEnv() === 'test'
}

// 是否为生产环境
export function isProduction(): boolean {
  return getCurrentEnv() === 'production'
}

// 打印环境信息
export function printEnvInfo(): void {
  try {
    console.log('=== 环境信息 ===')
    console.log('当前环境:', getCurrentEnv())
    console.log('API地址:', getApiBaseUrl())
    console.log('应用标题:', getAppTitle())
    console.log('调试模式:', isDebugMode())
    console.log('===============')
  } catch (error) {
    console.error('打印环境信息失败:', error)
  }
} 