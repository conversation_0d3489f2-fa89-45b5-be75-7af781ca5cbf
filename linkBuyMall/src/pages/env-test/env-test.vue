<template>
  <view class="env-test">
    <view class="header">
      <text class="title">环境配置测试</text>
    </view>
    
    <view class="info-section">
      <view class="info-item">
        <text class="label">当前环境：</text>
        <text class="value">{{ currentEnv }}</text>
      </view>
      
      <view class="info-item">
        <text class="label">API地址：</text>
        <text class="value">{{ apiUrl }}</text>
      </view>
      
      <view class="info-item">
        <text class="label">应用标题：</text>
        <text class="value">{{ appTitle }}</text>
      </view>
      
      <view class="info-item">
        <text class="label">调试模式：</text>
        <text class="value">{{ debugMode ? '开启' : '关闭' }}</text>
      </view>
    </view>
    
    <view class="test-section">
      <button class="test-btn" @click="testApiRequest">测试API请求</button>
      <button class="test-btn" @click="showEnvInfo">显示环境信息</button>
    </view>
    
    <view class="log-section" v-if="logs.length > 0">
      <text class="log-title">日志信息：</text>
      <view class="log-item" v-for="(log, index) in logs" :key="index">
        <text class="log-text">{{ log }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { 
  getCurrentEnv, 
  getApiBaseUrl, 
  getAppTitle, 
  isDebugMode,
  printEnvInfo 
} from '@/utils/env'
import { AppConfig } from '@/utils/config'
import { get } from '@/utils/request'

// 响应式数据
const currentEnv = ref('')
const apiUrl = ref('')
const appTitle = ref('')
const debugMode = ref(false)
const logs = ref<string[]>([])

// 添加日志
const addLog = (message: string) => {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.unshift(`[${timestamp}] ${message}`)
  if (logs.value.length > 10) {
    logs.value = logs.value.slice(0, 10)
  }
}

// 测试API请求
const testApiRequest = async () => {
  try {
    addLog('开始测试API请求...')
    addLog(`请求地址: ${apiUrl.value}/api/test`)
    
    // 这里可以调用一个实际的API接口
    // const result = await get('/api/test')
    // addLog(`请求成功: ${JSON.stringify(result)}`)
    
    // 模拟请求
    setTimeout(() => {
      addLog('API请求测试完成（模拟）')
    }, 1000)
    
  } catch (error) {
    addLog(`API请求失败: ${error}`)
  }
}

// 显示环境信息
const showEnvInfo = () => {
  addLog('=== 环境信息 ===')
  addLog(`当前环境: ${currentEnv.value}`)
  addLog(`API地址: ${apiUrl.value}`)
  addLog(`应用标题: ${appTitle.value}`)
  addLog(`调试模式: ${debugMode.value ? '开启' : '关闭'}`)
  addLog(`MODE: ${getCurrentEnv()}`)
  addLog('================')
  
  // 在控制台打印详细信息
  printEnvInfo()
  console.log('AppConfig:', AppConfig)
}

// 初始化
onMounted(() => {
  currentEnv.value = getCurrentEnv()
  apiUrl.value = getApiBaseUrl()
  appTitle.value = getAppTitle()
  debugMode.value = isDebugMode()
  
  addLog('页面初始化完成')
  addLog(`环境: ${currentEnv.value}`)
})
</script>

<style scoped>
.env-test {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
}

.info-section {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  font-size: 32rpx;
  color: #666;
  font-weight: 500;
}

.value {
  font-size: 32rpx;
  color: #333;
  word-break: break-all;
  text-align: right;
  flex: 1;
  margin-left: 20rpx;
}

.test-section {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.test-btn {
  background-color: #007aff;
  color: #fff;
  border: none;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.test-btn:active {
  background-color: #0056cc;
}

.log-section {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.log-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.log-item {
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.log-item:last-child {
  border-bottom: none;
}

.log-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}
</style> 