<template>
  <view class="user">
    <!-- 用户信息头部 -->
    <view class="user-header">
      <view class="user-info" @click="handleLogin">
        <image :src="userStore.avatar" class="user-avatar"></image>
        <view class="user-details">
          <text class="user-name">{{ userStore.nickname }}</text>
          <text class="user-desc" v-if="!userStore.isLoggedIn">点击登录</text>
          <text class="user-desc" v-else-if="userStore.isGuest">游客模式</text>
          <text class="user-desc" v-else-if="userStore.isRegistered">{{ platformName }}用户</text>
        </view>
        <view class="user-type-badge" v-if="userStore.isLoggedIn">
          <text class="badge-text" v-if="userStore.isGuest">游客</text>
          <text class="badge-text registered" v-else-if="userStore.isRegistered">会员</text>
        </view>
      </view>
      <view class="user-stats" v-if="userStore.isLoggedIn">
        <view class="stat-item">
          <text class="stat-number">0</text>
          <text class="stat-label">收藏</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">0</text>
          <text class="stat-label">关注</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">0</text>
          <text class="stat-label">粉丝</text>
        </view>
      </view>
    </view>

    <!-- 订单管理 -->
    <view class="order-section">
      <view class="section-title" @click="goToOrderList">
        <text class="title-text">我的订单</text>
        <text class="more-text">查看全部 ></text>
      </view>
      <view class="order-types">
        <view class="order-type" @click="goToOrderList('pending')">
          <u-icon name="clock" size="24" color="#ff9500"></u-icon>
          <text class="type-text">待付款</text>
        </view>
        <view class="order-type" @click="goToOrderList('paid')">
          <u-icon name="car" size="24" color="#007aff"></u-icon>
          <text class="type-text">待发货</text>
        </view>
        <view class="order-type" @click="goToOrderList('shipped')">
          <u-icon name="checkmark-circle" size="24" color="#4cd964"></u-icon>
          <text class="type-text">待收货</text>
        </view>
        <view class="order-type" @click="goToOrderList('completed')">
          <u-icon name="star" size="24" color="#ffcc00"></u-icon>
          <text class="type-text">待评价</text>
        </view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-item" @click="goToAddressList">
        <u-icon name="map" size="20" color="#666"></u-icon>
        <text class="menu-text">收货地址</text>
        <u-icon name="arrow-right" size="16" color="#ccc"></u-icon>
      </view>
      <view class="menu-item" @click="goToFavorites">
        <u-icon name="heart" size="20" color="#666"></u-icon>
        <text class="menu-text">我的收藏</text>
        <u-icon name="arrow-right" size="16" color="#ccc"></u-icon>
      </view>
      <view class="menu-item" @click="goToCustomerService">
        <u-icon name="chat" size="20" color="#666"></u-icon>
        <text class="menu-text">客服中心</text>
        <u-icon name="arrow-right" size="16" color="#ccc"></u-icon>
      </view>
      <view class="menu-item" @click="goToSettings">
        <u-icon name="setting" size="20" color="#666"></u-icon>
        <text class="menu-text">设置</text>
        <u-icon name="arrow-right" size="16" color="#ccc"></u-icon>
      </view>
    </view>

    <!-- 退出登录 -->
    <view class="logout-section" v-if="userStore.hasLogin">
      <u-button type="error" @click="handleLogout" plain>退出登录</u-button>
    </view>
    
    <!-- 自定义TabBar -->
    <CustomTabBar />
  </view>
</template>

<script>
import { useUserStore } from '@/store/user'
import CustomTabBar from '@/components/CustomTabBar/CustomTabBar.vue'
import { getPlatformName } from '@/utils/platform'
import { checkOrderPermission, checkAddressPermission, checkFavoritePermission } from '@/utils/auth'

export default {
  components: {
    CustomTabBar
  },

  data() {
    return {
      platformName: getPlatformName()
    }
  },

  setup() {
    const userStore = useUserStore()
    return {
      userStore
    }
  },

  onShow() {
    // 每次显示时刷新用户信息
    this.userStore.restoreUserInfo()
    // 触发tabBar更新
    uni.$emit('tabBarChange')
  },

  methods: {
    // 处理登录
    handleLogin() {
      if (!this.userStore.isLoggedIn) {
        uni.navigateTo({
          url: '/pages/login/login'
        })
      } else if (this.userStore.isGuest) {
        // 游客用户点击头像，提示升级为注册用户
        uni.showModal({
          title: '提示',
          content: '您当前是游客模式，是否要注册成为正式用户？',
          confirmText: '去注册',
          success: (res) => {
            if (res.confirm) {
              uni.navigateTo({
                url: '/pages/register/register'
              })
            }
          }
        })
      }
    },

    // 退出登录
    async handleLogout() {
      try {
        const res = await uni.showModal({
          title: '提示',
          content: '确定要退出登录吗？'
        })
        
        if (res.confirm) {
          this.userStore.logout()
        }
      } catch (error) {
        console.error('退出登录失败:', error)
      }
    },

    // 跳转到订单列表
    goToOrderList(status = '') {
      if (!checkOrderPermission()) {
        return
      }

      uni.navigateTo({
        url: `/pages/order/list${status ? '?status=' + status : ''}`
      })
    },

    // 跳转到地址列表
    goToAddressList() {
      if (!checkAddressPermission()) {
        return
      }

      uni.navigateTo({
        url: '/pages/user/address'
      })
    },

    // 跳转到收藏列表
    goToFavorites() {
      if (!checkFavoritePermission()) {
        return
      }

      uni.navigateTo({
        url: '/pages/user/favorites'
      })
    },

    // 跳转到客服中心
    goToCustomerService() {
      uni.navigateTo({
        url: '/pages/user/service'
      })
    },

    // 跳转到设置页面
    goToSettings() {
      uni.navigateTo({
        url: '/pages/user/settings'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.user {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx; /* 为自定义tabBar留出空间 */
}

.user-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx;
  color: white;

  .user-info {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;

    .user-avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 60rpx;
      margin-right: 30rpx;
    }

    .user-details {
      flex: 1;

      .user-name {
        font-size: 36rpx;
        font-weight: bold;
        margin-bottom: 10rpx;
        display: block;
      }

      .user-desc {
        font-size: 24rpx;
        opacity: 0.8;
      }
    }

    .user-type-badge {
      .badge-text {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        font-size: 20rpx;
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        border: 1rpx solid rgba(255, 255, 255, 0.3);

        &.registered {
          background: linear-gradient(45deg, #ffd700, #ffed4e);
          color: #333;
          border-color: #ffd700;
          font-weight: bold;
        }
      }
    }
  }

  .user-stats {
    display: flex;
    justify-content: space-around;

    .stat-item {
      text-align: center;

      .stat-number {
        font-size: 32rpx;
        font-weight: bold;
        display: block;
        margin-bottom: 10rpx;
      }

      .stat-label {
        font-size: 24rpx;
        opacity: 0.8;
      }
    }
  }
}

.order-section {
  background: white;
  margin: 20rpx;
  border-radius: 15rpx;
  padding: 30rpx;

  .section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;

    .title-text {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .more-text {
      font-size: 24rpx;
      color: #999;
    }
  }

  .order-types {
    display: flex;
    justify-content: space-around;

    .order-type {
      display: flex;
      flex-direction: column;
      align-items: center;

      .type-text {
        font-size: 24rpx;
        color: #666;
        margin-top: 10rpx;
      }
    }
  }
}

.menu-section {
  background: white;
  margin: 20rpx;
  border-radius: 15rpx;
  overflow: hidden;

  .menu-item {
    display: flex;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    .menu-text {
      flex: 1;
      font-size: 28rpx;
      color: #333;
      margin-left: 20rpx;
    }
  }
}

.logout-section {
  margin: 40rpx 20rpx;
}
</style> 