<template>
  <view class="home">
    <view class="header" :style="headerStyle">
      <text class="title">{{ pageTitle }}</text>
      <text class="subtitle">{{ pageDescription }}</text>
      
      <!-- 位置信息显示 -->
      <view class="location-info" @click="handleLocationClick">
        <text class="location-icon">📍</text>
        <text class="location-text">{{ locationText }}</text>
        <text class="location-refresh" v-if="!isLocationLoading">🔄</text>
        <text class="location-loading" v-if="isLocationLoading">⏳</text>
      </view>
      
      <!-- 坐标系信息显示 -->
      <view class="coords-info" v-if="hasLocation">
        <view class="coord-item">
          <text class="coord-label">GCJ02:</text>
          <text class="coord-value">{{ locationStore.gcj02Text }}</text>
        </view>
        <view class="coord-item" v-if="locationStore.hasBd09Location">
          <text class="coord-label">BD-09:</text>
          <text class="coord-value">{{ locationStore.bd09Text }}</text>
        </view>
      </view>
    </view>
    
    <view class="content">
      <!-- API配置信息展示 -->
      <view class="api-info" v-if="pageInfo">
        <view class="info-title">
          <text>API配置信息</text>
        </view>
        <view class="info-content">
          <view class="info-item">
            <text class="label">主题：</text>
            <text class="value">{{ pageInfo.themes }}</text>
          </view>
          <view class="info-item">
            <text class="label">底部导航数量：</text>
            <text class="value">{{ pageInfo.navBars?.length || 0 }}个</text>
          </view>
        </view>
      </view>
      
      <view class="welcome" :style="welcomeStyle">
        <text class="welcome-text">欢迎来到{{ pageTitle }}！</text>
        <text class="theme-name">当前主题：{{ themeName }}</text>
      </view>
      
      <view class="features">
        <view 
          class="feature-item" 
          v-for="(feature, index) in features" 
          :key="index"
          :style="featureItemStyle"
        >
          <text>{{ feature }}</text>
        </view>
      </view>
      
      <!-- 快捷操作 -->
      <view class="quick-actions">
        <view class="action-title">
          <text>快捷操作</text>
        </view>
        <view class="action-buttons">
          <view class="action-btn" :style="actionBtnStyle" @click="goToThemeDemo">
            <text class="btn-icon">🎨</text>
            <text class="btn-text">主题演示</text>
          </view>
          <view class="action-btn" :style="actionBtnStyle" @click="testAPI">
            <text class="btn-icon">🔧</text>
            <text class="btn-text">刷新配置</text>
          </view>
          <view class="action-btn" :style="actionBtnStyle" @click="debugTabBar">
            <text class="btn-icon">🐛</text>
            <text class="btn-text">调试TabBar</text>
          </view>
          <view class="action-btn" :style="actionBtnStyle" @click="testSimpleLocation">
            <text class="btn-icon">🌍</text>
            <text class="btn-text">测试坐标转换</text>
          </view>
          <view class="action-btn" :style="actionBtnStyle" @click="testPrivacyAndLocation">
            <text class="btn-icon">🔐</text>
            <text class="btn-text">测试隐私协议</text>
          </view>
        </view>
      </view>
      
      <!-- 主题切换器 -->
      <view class="theme-switcher">
        <view class="switcher-title">
          <text>主题切换</text>
        </view>
        <view class="theme-list">
          <view 
            class="theme-item" 
            v-for="theme in availableThemes" 
            :key="theme.key"
            :class="{ active: theme.key === currentTheme }"
            :style="getThemeItemStyle(theme)"
            @click="switchTheme(theme.key)"
          >
            <view class="theme-color" :style="{ backgroundColor: theme.primary }"></view>
            <text class="theme-name">{{ theme.name }}</text>
          </view>
        </view>
      </view>
      
      <!-- 底部导航配置展示 -->
      <view class="navbar-config" v-if="navBars.length > 0">
        <view class="config-title">
          <text>底部导航配置</text>
        </view>
        <view class="navbar-list">
          <view 
            class="navbar-item" 
            v-for="(nav, index) in navBars" 
            :key="index"
            :style="navItemStyle"
          >
            <image class="nav-icon" :src="nav.icon" mode="aspectFit" />
            <text class="nav-name">{{ nav.name }}</text>
            <text class="nav-path">{{ nav.path }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 自定义TabBar -->
    <CustomTabBar />
  </view>
</template>

<script>
import { useThemeStore } from '@/store/theme'
import { useLocationStore } from '@/store/location'
import CustomTabBar from '@/components/CustomTabBar/CustomTabBar.vue'

export default {
  name: 'Home',
  components: {
    CustomTabBar
  },
  data() {
    return {
      
    }
  },
  
  computed: {
    themeStore() {
      return useThemeStore()
    },
    
    locationStore() {
      return useLocationStore()
    },
    
    // 页面配置信息
    pageInfo() {
      return this.themeStore.pageInfo
    },
    
    // 底部导航配置
    navBars() {
      return this.themeStore.tabBarList
    },
    
    // 页面标题
    pageTitle() {
      return 'LinkBuy商城'
    },
    
    // 页面描述
    pageDescription() {
      return '精品购物，品质生活'
    },
    
    // 功能特性
    features() {
      return ['✨ 精选商品', '🚀 快速配送', '💯 品质保证', '🎨 动态主题', '📱 自定义导航']
    },
    
    // 当前主题名称
    themeName() {
      return this.themeStore.themeName
    },
    
    // 当前主题key
    currentTheme() {
      return this.themeStore.currentTheme
    },
    
    // 可用主题列表
    availableThemes() {
      return this.themeStore.getAllThemes()
    },
    
    // 位置信息相关
    locationText() {
      return this.locationStore.locationText
    },
    
    isLocationLoading() {
      return this.locationStore.isLocationLoading
    },
    
    hasLocation() {
      return this.locationStore.hasLocation
    },
    
    // 头部样式
    headerStyle() {
      return {
        background: this.themeStore.gradientColor
      }
    },
    
    // 欢迎区域样式
    welcomeStyle() {
      return {
        background: this.themeStore.gradientColor,
        borderColor: this.themeStore.primaryColor
      }
    },
    
    // 功能项样式
    featureItemStyle() {
      return {
        borderLeftColor: this.themeStore.primaryColor,
        backgroundColor: this.themeStore.surfaceColor
      }
    },
    
    // 操作按钮样式
    actionBtnStyle() {
      return {
        backgroundColor: this.themeStore.primaryColor,
        borderColor: this.themeStore.primaryDarkColor
      }
    },
    
    // 导航项样式
    navItemStyle() {
      return {
        borderColor: this.themeStore.borderColor,
        backgroundColor: this.themeStore.surfaceColor
      }
    }
  },
  
  onLoad() {
    console.log('首页加载完成')
  },
  
  onShow() {
    // 触发tabBar更新
    uni.$emit('tabBarChange')
  },
  
  methods: {
    // 切换主题
    switchTheme(themeKey) {
      this.themeStore.changeTheme(themeKey)
      
      uni.showToast({
        title: `已切换到${this.themeStore.themeName}`,
        icon: 'success'
      })
    },
    
    // 获取主题项样式
    getThemeItemStyle(theme) {
      const isActive = theme.key === this.currentTheme
      return {
        borderColor: isActive ? this.themeStore.primaryColor : '#E2E8F0',
        backgroundColor: isActive ? this.themeStore.secondaryColor : '#FFFFFF'
      }
    },
    
    // 跳转到主题演示页面
    goToThemeDemo() {
      uni.navigateTo({
        url: '/pages/theme/demo'
      })
    },
    
    // 刷新API配置
    async testAPI() {
      uni.showLoading({ title: '刷新中...' })
      
      try {
        // 重新加载页面配置
        await this.themeStore.loadPageInfo()
        
        uni.hideLoading()
        uni.showToast({
          title: '配置已刷新',
          icon: 'success'
        })
      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: '刷新失败',
          icon: 'error'
        })
      }
    },
    
    // 调试TabBar
    debugTabBar() {
      console.log('=== TabBar调试信息 ===')
      console.log('当前主题:', this.themeStore.currentTheme)
      console.log('主题配置:', this.themeStore.themeConfig)
      console.log('页面配置:', this.themeStore.pageInfo)
      console.log('导航配置:', this.themeStore.tabBarList)
      
      // 手动触发TabBar更新
      uni.$emit('tabBarChange')
      
      // 显示当前路径信息
      // #ifdef H5
      if (typeof window !== 'undefined' && window.location) {
        console.log('当前URL:', window.location.href)
        console.log('当前Hash:', window.location.hash)
      }
      // #endif
      
      uni.showToast({
        title: '调试信息已输出到控制台',
        icon: 'none'
      })
    },
    
    // 处理位置信息点击
    handleLocationClick() {
      if (this.isLocationLoading) {
        return // 如果正在加载，不处理点击
      }
      
      uni.showModal({
        title: '位置信息',
        content: this.hasLocation ? 
          `当前位置：${this.locationText}\n\n是否重新获取位置信息？` : 
          '当前未获取到位置信息，是否立即获取？',
        confirmText: this.hasLocation ? '重新获取' : '获取位置',
        success: (res) => {
          if (res.confirm) {
            this.refreshLocation()
          }
        }
      })
    },
    
    // 刷新位置信息
    async refreshLocation() {
      try {
        await this.locationStore.refreshLocation({
          showLoading: true,
          loadingText: '正在获取位置信息...'
        })
        
        uni.showToast({
          title: '位置信息已更新',
          icon: 'success'
        })
      } catch (error) {
        console.error('刷新位置失败:', error)
        // 错误提示已在store中处理
      }
    },
    
    // 测试WGS84转GCJ02位置获取方法
    async testSimpleLocation() {
      try {
        const location = await this.locationStore.getLocationWgs84AndConvert({
          showLoading: true,
          loadingText: '测试WGS84转GCJ02...'
        })
        
        console.log('WGS84转GCJ02+BD09获取位置成功:', location)
        
        let content = `GCJ02坐标:\n纬度: ${location.latitude.toFixed(6)}\n经度: ${location.longitude.toFixed(6)}\n精度: ${location.accuracy}m`
        
        if (location.bd09) {
          content += `\n\nBD-09坐标:\n纬度: ${location.bd09.latitude.toFixed(6)}\n经度: ${location.bd09.longitude.toFixed(6)}`
        }
        
        uni.showModal({
          title: '多坐标系位置获取成功',
          content: content,
          showCancel: false
        })
      } catch (error) {
        console.error('WGS84转GCJ02获取位置失败:', error)
        uni.showModal({
          title: '位置获取失败',
          content: JSON.stringify(error),
          showCancel: false
        })
      }
    },
    
    // 测试隐私协议和位置获取
    async testPrivacyAndLocation() {
      try {
        uni.showLoading({
          title: '测试隐私协议...',
          mask: true
        })
        
        // 导入隐私协议处理函数
        const { handlePrivacyAgreement, getCurrentLocation } = await import('@/utils/location')
        
        // 先测试隐私协议
        const privacyAgreed = await handlePrivacyAgreement()
        console.log('隐私协议结果:', privacyAgreed)
        
        if (!privacyAgreed) {
          uni.hideLoading()
          uni.showModal({
            title: '隐私协议测试',
            content: '用户未同意隐私协议',
            showCancel: false
          })
          return
        }
        
        // 隐私协议通过后，测试位置获取
        uni.showLoading({
          title: '测试位置获取...',
          mask: true
        })
        
        const location = await getCurrentLocation({
          type: 'wgs84',
          timeout: 8000
        })
        
        uni.hideLoading()
        
        uni.showModal({
          title: '隐私协议和位置测试成功',
          content: `隐私协议: 已同意\n位置获取: 成功\n纬度: ${location.latitude.toFixed(6)}\n经度: ${location.longitude.toFixed(6)}`,
          showCancel: false
        })
        
      } catch (error) {
        uni.hideLoading()
        console.error('隐私协议或位置获取失败:', error)
        
        let errorMsg = '未知错误'
        if (error.errMsg) {
          if (error.errMsg.includes('requiredPrivateInfos')) {
            errorMsg = 'manifest.json中缺少requiredPrivateInfos配置'
          } else if (error.errMsg.includes('privacy agreement')) {
            errorMsg = '用户未同意隐私协议'
          } else {
            errorMsg = error.errMsg
          }
        } else if (error.message) {
          errorMsg = error.message
        }
        
        uni.showModal({
          title: '测试失败',
          content: `错误信息: ${errorMsg}`,
          showCancel: false
        })
      }
    }
  }
}
</script>

<style scoped>
.home {
  padding: 0;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx; /* 为自定义tabBar留出空间 */
}

.header {
  text-align: center;
  padding: 60rpx 40rpx 40rpx;
  background: linear-gradient(135deg, #E53E3E 0%, #C53030 100%);
  color: white;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 位置信息样式 */
.location-info {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30rpx;
  padding: 20rpx 30rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.location-info:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.2);
}

.location-icon {
  font-size: 28rpx;
  margin-right: 10rpx;
}

.location-text {
  color: white;
  font-size: 26rpx;
  max-width: 400rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 10rpx;
}

.location-refresh {
  font-size: 24rpx;
  opacity: 0.8;
  animation: rotate 2s linear infinite;
  animation-play-state: paused;
}

.location-info:hover .location-refresh {
  animation-play-state: running;
}

.location-loading {
  font-size: 24rpx;
  opacity: 0.8;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
}

/* 坐标系信息样式 */
.coords-info {
  margin-top: 20rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.coord-item {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.coord-item:last-child {
  margin-bottom: 0;
}

.coord-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 22rpx;
  font-weight: bold;
  margin-right: 10rpx;
  min-width: 80rpx;
}

.coord-value {
  color: white;
  font-size: 20rpx;
  font-family: 'Monaco', 'Consolas', monospace;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.content {
  padding: 40rpx 20rpx;
}

.api-info {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.info-title {
  text-align: center;
  margin-bottom: 30rpx;
}

.info-title text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.info-item {
  display: flex;
  align-items: center;
}

.label {
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
}

.value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.welcome {
  text-align: center;
  padding: 60rpx 40rpx;
  background: linear-gradient(135deg, #E53E3E 0%, #C53030 100%);
  border-radius: 20rpx;
  margin-bottom: 40rpx;
  border: 2rpx solid transparent;
}

.welcome-text {
  color: white;
  font-size: 36rpx;
  display: block;
  margin-bottom: 20rpx;
}

.theme-name {
  color: white;
  font-size: 24rpx;
  opacity: 0.8;
}

.features {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.feature-item {
  padding: 30rpx;
  background: white;
  border-radius: 15rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
  border-left: 6rpx solid #E53E3E;
}

.feature-item text {
  font-size: 32rpx;
  color: #333;
}

.quick-actions {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.action-title {
  text-align: center;
  margin-bottom: 30rpx;
}

.action-title text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  background-color: #E53E3E;
  border-radius: 15rpx;
  border: 2rpx solid #C53030;
  gap: 10rpx;
}

.btn-icon {
  font-size: 48rpx;
}

.btn-text {
  color: white;
  font-size: 28rpx;
  font-weight: bold;
}

.theme-switcher {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.switcher-title {
  text-align: center;
  margin-bottom: 30rpx;
}

.switcher-title text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.theme-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.theme-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border: 2rpx solid #E2E8F0;
  border-radius: 15rpx;
  background: white;
  transition: all 0.3s ease;
}

.theme-item.active {
  transform: scale(1.02);
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.15);
}

.theme-color {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.theme-name {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.navbar-config {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.config-title {
  text-align: center;
  margin-bottom: 30rpx;
}

.config-title text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.navbar-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.navbar-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border: 1rpx solid #E2E8F0;
  border-radius: 15rpx;
  background: #F7FAFC;
}

.nav-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.nav-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-right: 20rpx;
}

.nav-path {
  font-size: 24rpx;
  color: #666;
  flex: 1;
}

/* H5环境下使用CSS变量 */
/* #ifdef H5 */
.header {
  background: var(--theme-gradient, linear-gradient(135deg, #E53E3E 0%, #C53030 100%)) !important;
}

.welcome {
  background: var(--theme-gradient, linear-gradient(135deg, #E53E3E 0%, #C53030 100%)) !important;
  border-color: var(--theme-primary, #E53E3E) !important;
}

.feature-item {
  border-left-color: var(--theme-primary, #E53E3E) !important;
  background-color: var(--theme-surface, #F7FAFC) !important;
}

.action-btn {
  background-color: var(--theme-primary, #E53E3E) !important;
  border-color: var(--theme-primary-dark, #C53030) !important;
}
/* #endif */
</style> 