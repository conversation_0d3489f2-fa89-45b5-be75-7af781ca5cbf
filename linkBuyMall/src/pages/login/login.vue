<template>
  <view class="login">
    <view class="login-container">
      <!-- Logo -->
      <view class="logo-section">
        <image src="/static/logo.png" class="logo"></image>
        <text class="app-name">LinkBuy商城</text>
        <text class="platform-info">当前平台：{{ platformName }}</text>
      </view>

      <!-- 登录方式切换 -->
      <view class="login-type-tabs" v-if="supportedMethods.password || supportedMethods.sms">
        <view
          class="tab-item"
          :class="{ active: currentLoginType === 'password' }"
          @click="switchLoginType('password')"
          v-if="supportedMethods.password"
        >
          密码登录
        </view>
        <view
          class="tab-item"
          :class="{ active: currentLoginType === 'sms' }"
          @click="switchLoginType('sms')"
          v-if="supportedMethods.sms"
        >
          验证码登录
        </view>
      </view>

      <!-- 微信一键登录（主要方式） -->
      <view class="wechat-login-section" v-if="supportedMethods.wechat && isRecommendedWechat">
        <u-button
          type="primary"
          :loading="wechatLoading"
          @click="goToWechatAuth"
          class="wechat-login-btn"
          color="#1aad19"
        >
          <u-icon name="weixin" size="20" color="#fff" style="margin-right: 8px;"></u-icon>
          {{ wechatLoading ? '微信登录中...' : '微信一键登录' }}
        </u-button>

        <view class="divider">
          <text class="divider-text">其他登录方式</text>
        </view>
      </view>

      <!-- 登录表单 -->
      <view class="form-section">
        <u-form :model="form" ref="form" :rules="rules">
          <!-- 手机号输入 -->
          <u-form-item prop="mobile">
            <u-input
              v-model="form.mobile"
              placeholder="请输入手机号"
              prefix-icon="phone"
              clearable
              type="number"
              maxlength="11"
            ></u-input>
          </u-form-item>

          <!-- 密码输入 -->
          <u-form-item prop="password" v-if="currentLoginType === 'password'">
            <u-input
              v-model="form.password"
              type="password"
              placeholder="请输入密码"
              prefix-icon="lock"
              clearable
              :show-password="true"
            ></u-input>
          </u-form-item>

          <!-- 验证码输入 -->
          <u-form-item prop="smsCode" v-if="currentLoginType === 'sms'">
            <view class="sms-input-wrapper">
              <u-input
                v-model="form.smsCode"
                placeholder="请输入验证码"
                prefix-icon="checkmark-circle"
                clearable
                type="number"
                maxlength="6"
                class="sms-input"
              ></u-input>
              <u-button
                :disabled="smsCountdown > 0 || !form.mobile || !isValidMobile"
                @click="sendSmsCode"
                size="small"
                type="primary"
                plain
                class="sms-btn"
              >
                {{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
              </u-button>
            </view>
          </u-form-item>

          <!-- 记住密码 -->
          <view class="remember-section" v-if="currentLoginType === 'password'">
            <u-checkbox v-model="form.rememberMe" shape="circle">记住密码</u-checkbox>
          </view>
        </u-form>

        <!-- 登录按钮 -->
        <u-button
          type="primary"
          :loading="loading"
          @click="handleLogin"
          class="login-btn"
        >
          {{ loading ? '登录中...' : '登录' }}
        </u-button>

        <!-- 其他操作 -->
        <view class="other-actions">
          <text class="action-text" @click="goToRegister">还没有账号？立即注册</text>
          <text class="action-text" @click="goToForgotPassword" v-if="currentLoginType === 'password'">忘记密码？</text>
        </view>
      </view>

      <!-- 微信登录（备选方式） -->
      <view class="third-party-section" v-if="supportedMethods.wechat && !isRecommendedWechat">
        <view class="divider">
          <text class="divider-text">其他登录方式</text>
        </view>
        <view class="third-party-buttons">
          <view class="third-party-btn" @click="goToWechatAuth">
            <u-icon name="weixin" size="24" color="#1aad19"></u-icon>
            <text class="btn-text">微信登录</text>
          </view>
        </view>
      </view>

      <!-- 游客浏览 -->
      <view class="guest-section" v-if="supportedMethods.guest">
        <text class="guest-text" @click="guestBrowse">游客浏览</text>
      </view>
    </view>
  </view>
</template>

<script>
import { useUserStore } from '@/store/user'
import { getPlatformType, getPlatformName, getSupportedLoginMethods, getRecommendedLoginMethod } from '@/utils/platform'

export default {
  name: 'Login',

  data() {
    return {
      userStore: useUserStore(),
      loading: false,
      wechatLoading: false,
      currentLoginType: 'password', // 'password' | 'sms'
      smsCountdown: 0,
      smsTimer: null,
      platform: getPlatformType(),
      platformName: getPlatformName(),
      supportedMethods: getSupportedLoginMethods(),
      isRecommendedWechat: getRecommendedLoginMethod() === 'wechat',
      form: {
        mobile: '',
        password: '',
        smsCode: '',
        rememberMe: false
      },
      rules: {
        mobile: [
          {
            required: true,
            message: '请输入手机号',
            trigger: ['blur', 'change']
          },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: '请输入正确的手机号',
            trigger: ['blur', 'change']
          }
        ],
        password: [
          {
            required: true,
            message: '请输入密码',
            trigger: ['blur', 'change']
          },
          {
            min: 6,
            message: '密码长度不能少于6位',
            trigger: ['blur', 'change']
          }
        ],
        smsCode: [
          {
            required: true,
            message: '请输入验证码',
            trigger: ['blur', 'change']
          },
          {
            pattern: /^\d{6}$/,
            message: '请输入6位数字验证码',
            trigger: ['blur', 'change']
          }
        ]
      }
    }
  },

  computed: {
    isValidMobile() {
      return /^1[3-9]\d{9}$/.test(this.form.mobile)
    }
  },

  onLoad() {
    console.log('登录页面加载，当前平台:', this.platformName)
    this.initLoginType()
    this.loadRememberedLogin()
  },

  onUnload() {
    // 清理定时器
    if (this.smsTimer) {
      clearInterval(this.smsTimer)
    }
  },

  methods: {
    // 初始化登录方式
    initLoginType() {
      if (this.isRecommendedWechat) {
        // 微信环境优先显示微信登录
        this.currentLoginType = 'password'
      } else {
        // 非微信环境默认密码登录
        this.currentLoginType = 'password'
      }
    },

    // 加载记住的登录信息
    loadRememberedLogin() {
      try {
        const remembered = uni.getStorageSync('rememberedLogin')
        if (remembered && remembered.mobile && remembered.password) {
          this.form.mobile = remembered.mobile
          this.form.password = remembered.password
          this.form.rememberMe = true
        }
      } catch (error) {
        console.error('加载记住的登录信息失败:', error)
      }
    },

    // 切换登录方式
    switchLoginType(type) {
      this.currentLoginType = type
      // 清空表单
      this.form.password = ''
      this.form.smsCode = ''
    },

    // 统一登录处理
    async handleLogin() {
      try {
        // 表单验证
        const valid = await this.$refs.form.validate()
        if (!valid) return

        this.loading = true

        // 构建登录参数
        const loginParams = {
          loginType: this.currentLoginType,
          platform: this.platform,
          mobile: this.form.mobile,
          rememberMe: this.form.rememberMe
        }

        if (this.currentLoginType === 'password') {
          loginParams.password = this.form.password
        } else if (this.currentLoginType === 'sms') {
          loginParams.smsCode = this.form.smsCode
        }

        // 调用登录API
        const result = await this.userStore.login(loginParams)

        if (result.success) {
          uni.showToast({
            title: '登录成功',
            icon: 'success'
          })

          // 延迟跳转，让用户看到成功提示
          setTimeout(() => {
            this.navigateAfterLogin()
          }, 1500)
        } else {
          uni.showToast({
            title: result.message || '登录失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('登录失败:', error)
        uni.showToast({
          title: error.message || '登录失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    // 发送短信验证码
    async sendSmsCode() {
      if (!this.isValidMobile) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return
      }

      try {
        const result = await this.userStore.sendSmsCode(this.form.mobile, 'login')

        if (result.success) {
          uni.showToast({
            title: result.message || '验证码发送成功',
            icon: 'success'
          })

          // 开始倒计时
          this.startSmsCountdown()
        } else {
          uni.showToast({
            title: result.message || '验证码发送失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('发送验证码失败:', error)
        uni.showToast({
          title: '发送验证码失败',
          icon: 'none'
        })
      }
    },

    // 开始短信倒计时
    startSmsCountdown() {
      this.smsCountdown = 60
      this.smsTimer = setInterval(() => {
        this.smsCountdown--
        if (this.smsCountdown <= 0) {
          clearInterval(this.smsTimer)
          this.smsTimer = null
        }
      }, 1000)
    },

    // 登录后导航
    navigateAfterLogin() {
      // 返回上一页或跳转到首页
      const pages = getCurrentPages()
      if (pages.length > 1) {
        uni.navigateBack()
      } else {
        uni.switchTab({
          url: '/pages/home/<USER>'
        })
      }
    },

    // 微信登录
    async handleWechatLogin() {
      if (!this.supportedMethods.wechat) {
        uni.showToast({
          title: '当前平台不支持微信登录',
          icon: 'none'
        })
        return
      }

      try {
        this.wechatLoading = true

        // #ifdef MP-WEIXIN
        const res = await uni.login({
          provider: 'weixin'
        })

        if (res.code) {
          const result = await this.userStore.wechatLogin(res.code)

          if (result.success) {
            uni.showToast({
              title: '微信登录成功',
              icon: 'success'
            })

            setTimeout(() => {
              this.navigateAfterLogin()
            }, 1500)
          } else {
            uni.showToast({
              title: result.message || '微信登录失败',
              icon: 'none'
            })
          }
        }
        // #endif

        // #ifdef H5
        if (this.platform === 'wechat-h5') {
          // 微信公众号H5登录
          uni.showToast({
            title: '微信公众号登录功能开发中',
            icon: 'none'
          })
        } else {
          uni.showToast({
            title: '请在微信环境中使用微信登录',
            icon: 'none'
          })
        }
        // #endif
      } catch (error) {
        console.error('微信登录失败:', error)
        uni.showToast({
          title: '微信登录失败',
          icon: 'none'
        })
      } finally {
        this.wechatLoading = false
      }
    },

    // 跳转到注册页面
    goToRegister() {
      uni.navigateTo({
        url: '/pages/register/register'
      })
    },

    // 跳转到忘记密码页面
    goToForgotPassword() {
      uni.navigateTo({
        url: '/pages/login/forgot-password'
      })
    },

    // 跳转到微信授权页面
    goToWechatAuth() {
      if (!this.supportedMethods.wechat) {
        uni.showToast({
          title: '当前平台不支持微信登录',
          icon: 'none'
        })
        return
      }

      uni.navigateTo({
        url: '/pages/login/wechat-auth'
      })
    },

    // 游客浏览
    guestBrowse() {
      uni.showModal({
        title: '游客浏览',
        content: '游客模式下无法使用购物车和下单功能，确定要继续吗？',
        success: (res) => {
          if (res.confirm) {
            // 跳转到首页
            uni.switchTab({
              url: '/pages/home/<USER>'
            })
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.login {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-container {
  width: 90%;
  max-width: 600rpx;
}

.logo-section {
  text-align: center;
  margin-bottom: 60rpx;

  .logo {
    width: 120rpx;
    height: 120rpx;
    border-radius: 20rpx;
    margin-bottom: 20rpx;
  }

  .app-name {
    font-size: 36rpx;
    font-weight: bold;
    color: white;
    margin-bottom: 10rpx;
  }

  .platform-info {
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.8);
  }
}

.login-type-tabs {
  display: flex;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50rpx;
  margin-bottom: 40rpx;
  padding: 6rpx;

  .tab-item {
    flex: 1;
    text-align: center;
    padding: 20rpx;
    border-radius: 44rpx;
    color: rgba(255, 255, 255, 0.8);
    font-size: 28rpx;
    transition: all 0.3s;

    &.active {
      background: white;
      color: #333;
      font-weight: bold;
    }
  }
}

.wechat-login-section {
  margin-bottom: 40rpx;

  .wechat-login-btn {
    width: 100%;
    height: 88rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 40rpx;
  }
}

.form-section {
  background-color: white;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;

  .sms-input-wrapper {
    display: flex;
    align-items: center;
    gap: 20rpx;

    .sms-input {
      flex: 1;
    }

    .sms-btn {
      width: 180rpx;
      height: 70rpx;
      font-size: 24rpx;
    }
  }

  .remember-section {
    margin: 30rpx 0;
  }

  .login-btn {
    margin-top: 40rpx;
    height: 88rpx;
    border-radius: 44rpx;
    width: 100%;
    font-size: 32rpx;
    font-weight: bold;
  }

  .other-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 40rpx;

    .action-text {
      font-size: 28rpx;
      color: #666;
    }
  }
}

.third-party-section {
  .divider {
    text-align: center;
    margin-bottom: 40rpx;

    .divider-text {
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.8);
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 0 20rpx;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: -100rpx;
        width: 80rpx;
        height: 1rpx;
        background-color: rgba(255, 255, 255, 0.3);
      }

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        right: -100rpx;
        width: 80rpx;
        height: 1rpx;
        background-color: rgba(255, 255, 255, 0.3);
      }
    }
  }

  .third-party-buttons {
    display: flex;
    justify-content: center;

    .third-party-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20rpx;
      background-color: rgba(255, 255, 255, 0.1);
      border-radius: 10rpx;
      margin: 0 20rpx;

      .btn-text {
        font-size: 24rpx;
        color: white;
        margin-top: 10rpx;
      }
    }
  }
}

.guest-section {
  text-align: center;
  margin-top: 40rpx;

  .guest-text {
    color: rgba(255, 255, 255, 0.8);
    font-size: 28rpx;
    text-decoration: underline;
  }
}

.divider {
  text-align: center;
  margin: 40rpx 0;
  position: relative;

  .divider-text {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 0 20rpx;
    position: relative;

    &::before,
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      width: 80rpx;
      height: 1rpx;
      background-color: rgba(255, 255, 255, 0.3);
    }

    &::before {
      left: -100rpx;
    }

    &::after {
      right: -100rpx;
    }
  }
}
</style>