<template>
  <view class="login">
    <view class="login-container">
      <!-- Logo -->
      <view class="logo-section">
        <image src="/static/logo.png" class="logo"></image>
        <text class="app-name">LinkBuy商城</text>
      </view>

      <!-- 登录表单 -->
      <view class="form-section">
        <u-form :model="form" ref="form" :rules="rules">
          <u-form-item prop="username">
            <u-input 
              v-model="form.username"
              placeholder="请输入手机号/用户名"
              prefix-icon="account"
              clearable
            ></u-input>
          </u-form-item>
          
          <u-form-item prop="password">
            <u-input 
              v-model="form.password"
              type="password"
              placeholder="请输入密码"
              prefix-icon="lock"
              clearable
            ></u-input>
          </u-form-item>
        </u-form>

        <!-- 登录按钮 -->
        <u-button 
          type="primary" 
          :loading="loading"
          @click="handleLogin"
          class="login-btn"
        >
          {{ loading ? '登录中...' : '登录' }}
        </u-button>

        <!-- 其他操作 -->
        <view class="other-actions">
          <text class="action-text" @click="goToRegister">还没有账号？立即注册</text>
          <text class="action-text" @click="forgetPassword">忘记密码？</text>
        </view>
      </view>

      <!-- 第三方登录 -->
      <view class="third-party-section">
        <view class="divider">
          <text class="divider-text">其他登录方式</text>
        </view>
        <view class="third-party-buttons">
          <view class="third-party-btn" @click="wechatLogin">
            <u-icon name="weixin" size="24" color="#1aad19"></u-icon>
            <text class="btn-text">微信登录</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { useUserStore } from '@/store/user'
import { login, wechatLogin } from '@/api/index'

export default {
  data() {
    return {
      loading: false,
      form: {
        username: '',
        password: ''
      },
      rules: {
        username: [
          {
            required: true,
            message: '请输入手机号或用户名',
            trigger: 'blur'
          }
        ],
        password: [
          {
            required: true,
            message: '请输入密码',
            trigger: 'blur'
          },
          {
            min: 6,
            message: '密码长度不能少于6位',
            trigger: 'blur'
          }
        ]
      }
    }
  },

  setup() {
    const userStore = useUserStore()
    return {
      userStore
    }
  },

  methods: {
    // 登录
    async handleLogin() {
      try {
        // 表单验证
        const valid = await this.$refs.form.validate()
        if (!valid) return

        this.loading = true

        // 调用登录API
        const result = await this.userStore.login(this.form)
        
        if (result.success) {
          uni.showToast({
            title: '登录成功',
            icon: 'success'
          })
          
          // 延迟跳转，让用户看到成功提示
          setTimeout(() => {
            // 返回上一页或跳转到首页
            const pages = getCurrentPages()
            if (pages.length > 1) {
              uni.navigateBack()
            } else {
              uni.switchTab({
                url: '/pages/home/<USER>'
              })
            }
          }, 1500)
        }
      } catch (error) {
        console.error('登录失败:', error)
        uni.showToast({
          title: error.message || '登录失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    // 微信登录
    async wechatLogin() {
      try {
        // #ifdef MP-WEIXIN
        const res = await uni.login({
          provider: 'weixin'
        })
        
        if (res.code) {
          // 发送code到后端换取用户信息
          console.log('微信登录code:', res.code)
          uni.showToast({
            title: '微信登录功能开发中',
            icon: 'none'
          })
        }
        // #endif
        
        // #ifndef MP-WEIXIN
        uni.showToast({
          title: '请在微信小程序中使用微信登录',
          icon: 'none'
        })
        // #endif
      } catch (error) {
        console.error('微信登录失败:', error)
        uni.showToast({
          title: '微信登录失败',
          icon: 'none'
        })
      }
    },

    // 跳转到注册页面
    goToRegister() {
      uni.navigateTo({
        url: '/pages/register/register'
      })
    },

    // 忘记密码
    forgetPassword() {
      uni.showToast({
        title: '忘记密码功能开发中',
        icon: 'none'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.login {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-container {
  width: 90%;
  max-width: 600rpx;
}

.logo-section {
  text-align: center;
  margin-bottom: 80rpx;

  .logo {
    width: 120rpx;
    height: 120rpx;
    border-radius: 20rpx;
    margin-bottom: 20rpx;
  }

  .app-name {
    font-size: 36rpx;
    font-weight: bold;
    color: white;
  }
}

.form-section {
  background-color: white;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;

  .login-btn {
    margin-top: 40rpx;
    height: 88rpx;
    border-radius: 44rpx;
  }

  .other-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 40rpx;

    .action-text {
      font-size: 28rpx;
      color: #666;
    }
  }
}

.third-party-section {
  .divider {
    text-align: center;
    margin-bottom: 40rpx;

    .divider-text {
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.8);
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 0 20rpx;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: -100rpx;
        width: 80rpx;
        height: 1rpx;
        background-color: rgba(255, 255, 255, 0.3);
      }

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        right: -100rpx;
        width: 80rpx;
        height: 1rpx;
        background-color: rgba(255, 255, 255, 0.3);
      }
    }
  }

  .third-party-buttons {
    display: flex;
    justify-content: center;

    .third-party-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20rpx;
      background-color: rgba(255, 255, 255, 0.1);
      border-radius: 10rpx;
      margin: 0 20rpx;

      .btn-text {
        font-size: 24rpx;
        color: white;
        margin-top: 10rpx;
      }
    }
  }
}
</style> 