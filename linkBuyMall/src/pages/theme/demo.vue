<template>
  <view class="theme-demo">
    <view class="header" :style="headerStyle">
      <text class="title">主题演示</text>
      <text class="subtitle">体验不同的主题风格</text>
    </view>
    
    <view class="content">
      <!-- 当前主题信息 -->
      <view class="current-theme" :style="currentThemeStyle">
        <text class="current-title">当前主题</text>
        <text class="current-name">{{ themeName }}</text>
        <view class="color-palette">
          <view class="color-item">
            <view class="color-block" :style="{ backgroundColor: themeStore.primaryColor }"></view>
            <text>主色</text>
          </view>
          <view class="color-item">
            <view class="color-block" :style="{ backgroundColor: themeStore.accentColor }"></view>
            <text>辅色</text>
          </view>
          <view class="color-item">
            <view class="color-block" :style="{ backgroundColor: themeStore.secondaryColor }"></view>
            <text>背景</text>
          </view>
        </view>
      </view>
      
      <!-- 主题列表 -->
      <view class="theme-grid">
        <view 
          class="theme-card" 
          v-for="theme in availableThemes" 
          :key="theme.key"
          :class="{ active: theme.key === currentTheme }"
          @click="switchTheme(theme.key)"
        >
          <view class="card-header" :style="{ background: getThemeGradient(theme.key) }">
            <text class="card-title">{{ theme.name }}</text>
          </view>
          <view class="card-body">
            <view class="preview-elements">
              <view class="preview-button" :style="{ backgroundColor: theme.primary }">
                <text>按钮</text>
              </view>
              <view class="preview-text">
                <text>示例文本</text>
              </view>
            </view>
            <view class="color-dots">
              <view class="dot" :style="{ backgroundColor: theme.primary }"></view>
              <view class="dot" :style="{ backgroundColor: getThemeAccent(theme.key) }"></view>
              <view class="dot" :style="{ backgroundColor: getThemeSecondary(theme.key) }"></view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 功能演示 -->
      <view class="demo-section">
        <text class="section-title">功能演示</text>
        <view class="demo-items">
          <view class="demo-item" :style="demoItemStyle">
            <text class="demo-icon">🛍️</text>
            <text class="demo-text">购物车</text>
          </view>
          <view class="demo-item" :style="demoItemStyle">
            <text class="demo-icon">❤️</text>
            <text class="demo-text">收藏</text>
          </view>
          <view class="demo-item" :style="demoItemStyle">
            <text class="demo-icon">🎁</text>
            <text class="demo-text">优惠券</text>
          </view>
          <view class="demo-item" :style="demoItemStyle">
            <text class="demo-icon">📦</text>
            <text class="demo-text">订单</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { useThemeStore } from '@/store/theme'
import { themes } from '@/utils/theme'

export default {
  name: 'ThemeDemo',
  
  computed: {
    themeStore() {
      return useThemeStore()
    },
    
    themeName() {
      return this.themeStore.themeName
    },
    
    currentTheme() {
      return this.themeStore.currentTheme
    },
    
    availableThemes() {
      return this.themeStore.getAllThemes()
    },
    
    headerStyle() {
      return {
        background: this.themeStore.gradientColor
      }
    },
    
    currentThemeStyle() {
      return {
        borderColor: this.themeStore.primaryColor,
        backgroundColor: this.themeStore.secondaryColor
      }
    },
    
    demoItemStyle() {
      return {
        borderColor: this.themeStore.primaryColor,
        backgroundColor: this.themeStore.surfaceColor
      }
    }
  },
  
  methods: {
    switchTheme(themeKey) {
      this.themeStore.changeTheme(themeKey)
      
      uni.showToast({
        title: `已切换到${this.themeStore.themeName}`,
        icon: 'success'
      })
    },
    
    getThemeGradient(themeKey) {
      return themes[themeKey]?.gradient || themes.classic_red.gradient
    },
    
    getThemeAccent(themeKey) {
      return themes[themeKey]?.accent || themes.classic_red.accent
    },
    
    getThemeSecondary(themeKey) {
      return themes[themeKey]?.secondary || themes.classic_red.secondary
    }
  }
}
</script>

<style scoped>
.theme-demo {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  text-align: center;
  padding: 60rpx 40rpx 40rpx;
  background: linear-gradient(135deg, #E53E3E 0%, #C53030 100%);
  color: white;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

.content {
  padding: 40rpx 20rpx;
}

.current-theme {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  border: 2rpx solid #E53E3E;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.current-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.current-name {
  font-size: 48rpx;
  font-weight: bold;
  color: #E53E3E;
  display: block;
  margin-bottom: 30rpx;
}

.color-palette {
  display: flex;
  gap: 30rpx;
}

.color-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
}

.color-block {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.2);
}

.color-item text {
  font-size: 24rpx;
  color: #666;
}

.theme-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.theme-card {
  background: white;
  border-radius: 15rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.theme-card.active {
  transform: scale(1.02);
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.15);
}

.card-header {
  padding: 30rpx 20rpx;
  background: linear-gradient(135deg, #E53E3E 0%, #C53030 100%);
  text-align: center;
}

.card-title {
  color: white;
  font-size: 28rpx;
  font-weight: bold;
}

.card-body {
  padding: 20rpx;
}

.preview-elements {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.preview-button {
  padding: 10rpx 20rpx;
  border-radius: 10rpx;
  background-color: #E53E3E;
}

.preview-button text {
  color: white;
  font-size: 24rpx;
}

.preview-text text {
  font-size: 24rpx;
  color: #333;
}

.color-dots {
  display: flex;
  gap: 10rpx;
}

.dot {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
}

.demo-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 30rpx;
  text-align: center;
}

.demo-items {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.demo-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx;
  border: 2rpx solid #E53E3E;
  border-radius: 15rpx;
  background: #F7FAFC;
  gap: 10rpx;
}

.demo-icon {
  font-size: 48rpx;
}

.demo-text {
  font-size: 28rpx;
  color: #333;
}
</style> 