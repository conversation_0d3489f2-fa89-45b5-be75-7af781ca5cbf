<template>
  <view class="order-list">
    <!-- 订单状态标签 -->
    <view class="status-tabs">
      <view 
        class="status-tab"
        :class="{ active: currentStatus === status.value }"
        v-for="status in statusList"
        :key="status.value"
        @click="switchStatus(status.value)"
      >
        <text>{{ status.label }}</text>
      </view>
    </view>

    <!-- 订单列表 -->
    <view class="orders-section">
      <view 
        class="order-item"
        v-for="order in orderList"
        :key="order.id"
        @click="goToOrderDetail(order.id)"
      >
        <!-- 订单头部 -->
        <view class="order-header">
          <text class="order-number">订单号：{{ order.orderNo }}</text>
          <text class="order-status" :class="getStatusClass(order.status)">
            {{ getStatusText(order.status) }}
          </text>
        </view>

        <!-- 商品列表 -->
        <view class="order-products">
          <view 
            class="product-item"
            v-for="product in order.products"
            :key="product.id"
          >
            <image :src="product.image" class="product-image"></image>
            <view class="product-info">
              <text class="product-name">{{ product.name }}</text>
              <text class="product-spec" v-if="product.spec">{{ product.spec }}</text>
              <view class="product-price-qty">
                <text class="product-price">¥{{ product.price }}</text>
                <text class="product-quantity">x{{ product.quantity }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 订单信息 -->
        <view class="order-info">
          <text class="order-time">{{ formatTime(order.createTime) }}</text>
          <text class="order-total">共{{ order.totalQuantity }}件商品 合计：¥{{ order.totalAmount }}</text>
        </view>

        <!-- 操作按钮 -->
        <view class="order-actions">
          <u-button 
            v-if="order.status === 'pending'"
            size="small"
            type="info"
            plain
            @click.stop="cancelOrder(order)"
          >
            取消订单
          </u-button>
          <u-button 
            v-if="order.status === 'pending'"
            size="small"
            type="primary"
            @click.stop="payOrder(order)"
          >
            立即支付
          </u-button>
          <u-button 
            v-if="order.status === 'shipped'"
            size="small"
            type="primary"
            @click.stop="confirmOrder(order)"
          >
            确认收货
          </u-button>
          <u-button 
            v-if="order.status === 'completed'"
            size="small"
            type="warning"
            @click.stop="evaluateOrder(order)"
          >
            评价
          </u-button>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore">
        <u-loadmore :status="loadStatus" @loadmore="loadMore"></u-loadmore>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="!loading && orderList.length === 0">
        <image src="/static/empty-order.png" class="empty-image"></image>
        <text class="empty-text">暂无订单</text>
        <u-button type="primary" @click="goToHome">去逛逛</u-button>
      </view>
    </view>
  </view>
</template>

<script>
import { getOrderList, cancelOrder, payOrder, confirmOrder } from '@/api/index'

export default {
  data() {
    return {
      currentStatus: 'all',
      statusList: [
        { label: '全部', value: 'all' },
        { label: '待付款', value: 'pending' },
        { label: '待发货', value: 'paid' },
        { label: '待收货', value: 'shipped' },
        { label: '已完成', value: 'completed' }
      ],
      orderList: [],
      loading: false,
      hasMore: true,
      loadStatus: 'loadmore',
      page: 1,
      pageSize: 10
    }
  },

  onLoad(options) {
    if (options.status) {
      this.currentStatus = options.status
    }
    this.loadOrderList()
  },

  onShow() {
    // 从订单详情页返回时刷新列表
    this.refreshOrderList()
  },

  onReachBottom() {
    if (this.hasMore && this.loadStatus !== 'loading') {
      this.loadMore()
    }
  },

  methods: {
    // 加载订单列表
    async loadOrderList(reset = false) {
      if (this.loading) return
      
      try {
        this.loading = true
        this.loadStatus = 'loading'
        
        if (reset) {
          this.page = 1
          this.orderList = []
        }

        const params = {
          page: this.page,
          pageSize: this.pageSize,
          status: this.currentStatus === 'all' ? '' : this.currentStatus
        }

        // 实际项目中调用API
        // const res = await orderApi.getOrderList(params)
        
        // 模拟数据
        const mockData = this.generateMockData()
        
        if (reset) {
          this.orderList = mockData
        } else {
          this.orderList.push(...mockData)
        }
        
        this.hasMore = mockData.length === this.pageSize
        this.loadStatus = this.hasMore ? 'loadmore' : 'nomore'
        
      } catch (error) {
        console.error('加载订单列表失败:', error)
        this.loadStatus = 'loadmore'
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    // 生成模拟数据
    generateMockData() {
      const orders = []
      const statuses = ['pending', 'paid', 'shipped', 'completed']
      
      for (let i = 0; i < this.pageSize; i++) {
        const status = this.currentStatus === 'all' 
          ? statuses[Math.floor(Math.random() * statuses.length)]
          : this.currentStatus
          
        orders.push({
          id: Date.now() + i,
          orderNo: `LB${Date.now()}${i}`,
          status: status,
          createTime: Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000,
          totalAmount: Math.floor(Math.random() * 5000) + 100,
          totalQuantity: Math.floor(Math.random() * 5) + 1,
          products: [
            {
              id: 1,
              name: `商品名称 ${i + 1}`,
              spec: '规格信息',
              price: Math.floor(Math.random() * 1000) + 100,
              quantity: Math.floor(Math.random() * 3) + 1,
              image: '/static/product1.jpg'
            }
          ]
        })
      }
      return orders
    },

    // 刷新订单列表
    refreshOrderList() {
      this.loadOrderList(true)
    },

    // 加载更多
    loadMore() {
      this.page++
      this.loadOrderList()
    },

    // 切换状态
    switchStatus(status) {
      this.currentStatus = status
      this.loadOrderList(true)
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        pending: '待付款',
        paid: '待发货',
        shipped: '待收货',
        completed: '已完成',
        cancelled: '已取消'
      }
      return statusMap[status] || '未知状态'
    },

    // 获取状态样式类
    getStatusClass(status) {
      return `status-${status}`
    },

    // 格式化时间
    formatTime(timestamp) {
      const date = new Date(timestamp)
      const now = new Date()
      const diff = now - date
      
      if (diff < 60 * 1000) {
        return '刚刚'
      } else if (diff < 60 * 60 * 1000) {
        return `${Math.floor(diff / (60 * 1000))}分钟前`
      } else if (diff < 24 * 60 * 60 * 1000) {
        return `${Math.floor(diff / (60 * 60 * 1000))}小时前`
      } else {
        return date.toLocaleDateString()
      }
    },

    // 取消订单
    async cancelOrder(order) {
      try {
        const res = await uni.showModal({
          title: '提示',
          content: '确定要取消这个订单吗？'
        })
        
        if (res.confirm) {
          // 调用API取消订单
          // await orderApi.cancelOrder(order.id)
          
          // 更新本地状态
          order.status = 'cancelled'
          
          uni.showToast({
            title: '订单已取消',
            icon: 'success'
          })
        }
      } catch (error) {
        console.error('取消订单失败:', error)
        uni.showToast({
          title: '取消失败',
          icon: 'none'
        })
      }
    },

    // 支付订单
    payOrder(order) {
      uni.navigateTo({
        url: `/pages/order/pay?orderId=${order.id}`
      })
    },

    // 确认收货
    async confirmOrder(order) {
      try {
        const res = await uni.showModal({
          title: '提示',
          content: '确定已收到货物吗？'
        })
        
        if (res.confirm) {
          // 调用API确认收货
          // await orderApi.confirmOrder(order.id)
          
          // 更新本地状态
          order.status = 'completed'
          
          uni.showToast({
            title: '确认收货成功',
            icon: 'success'
          })
        }
      } catch (error) {
        console.error('确认收货失败:', error)
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        })
      }
    },

    // 评价订单
    evaluateOrder(order) {
      uni.navigateTo({
        url: `/pages/order/evaluate?orderId=${order.id}`
      })
    },

    // 跳转到订单详情
    goToOrderDetail(orderId) {
      uni.navigateTo({
        url: `/pages/order/detail?id=${orderId}`
      })
    },

    // 跳转到首页
    goToHome() {
      uni.switchTab({
        url: '/pages/home/<USER>'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.order-list {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.status-tabs {
  background-color: #fff;
  display: flex;
  border-bottom: 1rpx solid #eee;

  .status-tab {
    flex: 1;
    text-align: center;
    padding: 30rpx 0;
    font-size: 28rpx;
    color: #666;
    position: relative;

    &.active {
      color: #007aff;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60rpx;
        height: 4rpx;
        background-color: #007aff;
        border-radius: 2rpx;
      }
    }
  }
}

.orders-section {
  padding: 20rpx;

  .order-item {
    background-color: #fff;
    border-radius: 10rpx;
    margin-bottom: 20rpx;
    overflow: hidden;

    .order-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx;
      border-bottom: 1rpx solid #f5f5f5;

      .order-number {
        font-size: 28rpx;
        color: #666;
      }

      .order-status {
        font-size: 28rpx;
        font-weight: bold;

        &.status-pending {
          color: #ff9500;
        }

        &.status-paid {
          color: #007aff;
        }

        &.status-shipped {
          color: #4cd964;
        }

        &.status-completed {
          color: #666;
        }

        &.status-cancelled {
          color: #ff4757;
        }
      }
    }

    .order-products {
      .product-item {
        display: flex;
        padding: 30rpx;
        border-bottom: 1rpx solid #f5f5f5;

        &:last-child {
          border-bottom: none;
        }

        .product-image {
          width: 120rpx;
          height: 120rpx;
          border-radius: 10rpx;
          margin-right: 20rpx;
        }

        .product-info {
          flex: 1;
          display: flex;
          flex-direction: column;

          .product-name {
            font-size: 28rpx;
            color: #333;
            margin-bottom: 10rpx;
          }

          .product-spec {
            font-size: 24rpx;
            color: #999;
            margin-bottom: 10rpx;
          }

          .product-price-qty {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .product-price {
              font-size: 28rpx;
              color: #ff4757;
              font-weight: bold;
            }

            .product-quantity {
              font-size: 24rpx;
              color: #666;
            }
          }
        }
      }
    }

    .order-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx;
      background-color: #fafafa;

      .order-time {
        font-size: 24rpx;
        color: #999;
      }

      .order-total {
        font-size: 28rpx;
        color: #333;
      }
    }

    .order-actions {
      display: flex;
      justify-content: flex-end;
      gap: 20rpx;
      padding: 30rpx;
    }
  }
}

.load-more {
  margin-top: 40rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .empty-image {
    width: 300rpx;
    height: 300rpx;
    margin-bottom: 40rpx;
  }

  .empty-text {
    font-size: 32rpx;
    color: #999;
    margin-bottom: 40rpx;
  }
}
</style> 