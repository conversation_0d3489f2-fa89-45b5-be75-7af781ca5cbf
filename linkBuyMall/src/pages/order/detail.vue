<template>
  <view class="order-detail">
    <!-- 订单状态 -->
    <view class="status-section">
      <view class="status-icon">
        <u-icon 
          :name="getStatusIcon(order.status)" 
          size="40" 
          :color="getStatusColor(order.status)"
        ></u-icon>
      </view>
      <view class="status-info">
        <text class="status-text">{{ getStatusText(order.status) }}</text>
        <text class="status-desc" v-if="getStatusDesc(order.status)">
          {{ getStatusDesc(order.status) }}
        </text>
      </view>
    </view>

    <!-- 物流信息 -->
    <view class="logistics-section" v-if="order.status === 'shipped' || order.status === 'completed'">
      <view class="section-header" @click="goToLogistics">
        <view class="header-left">
          <u-icon name="car" size="20" color="#007aff"></u-icon>
          <text class="header-title">物流信息</text>
        </view>
        <view class="header-right">
          <text class="logistics-status">{{ order.logistics?.status || '运输中' }}</text>
          <u-icon name="arrow-right" size="16" color="#ccc"></u-icon>
        </view>
      </view>
      <text class="logistics-info">{{ order.logistics?.lastUpdate || '商品正在配送中，请耐心等待' }}</text>
    </view>

    <!-- 收货地址 -->
    <view class="address-section">
      <view class="address-header">
        <u-icon name="map" size="20" color="#007aff"></u-icon>
        <text class="address-title">收货地址</text>
      </view>
      <view class="address-info">
        <view class="address-contact">
          <text class="contact-name">{{ order.address?.name }}</text>
          <text class="contact-phone">{{ order.address?.phone }}</text>
        </view>
        <text class="address-detail">{{ order.address?.detail }}</text>
      </view>
    </view>

    <!-- 商品信息 -->
    <view class="products-section">
      <view class="section-header">
        <text class="section-title">商品信息</text>
      </view>
      <view 
        class="product-item"
        v-for="product in order.products"
        :key="product.id"
        @click="goToProductDetail(product.id)"
      >
        <image :src="product.image" class="product-image"></image>
        <view class="product-info">
          <text class="product-name">{{ product.name }}</text>
          <text class="product-spec" v-if="product.spec">{{ product.spec }}</text>
          <view class="product-price-qty">
            <text class="product-price">¥{{ product.price }}</text>
            <text class="product-quantity">x{{ product.quantity }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 订单信息 -->
    <view class="order-info-section">
      <view class="section-header">
        <text class="section-title">订单信息</text>
      </view>
      <view class="info-list">
        <view class="info-item">
          <text class="info-label">订单号</text>
          <text class="info-value">{{ order.orderNo }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">下单时间</text>
          <text class="info-value">{{ formatTime(order.createTime) }}</text>
        </view>
        <view class="info-item" v-if="order.payTime">
          <text class="info-label">支付时间</text>
          <text class="info-value">{{ formatTime(order.payTime) }}</text>
        </view>
        <view class="info-item" v-if="order.deliveryTime">
          <text class="info-label">发货时间</text>
          <text class="info-value">{{ formatTime(order.deliveryTime) }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">支付方式</text>
          <text class="info-value">{{ order.paymentMethod || '微信支付' }}</text>
        </view>
      </view>
    </view>

    <!-- 费用明细 -->
    <view class="cost-section">
      <view class="section-header">
        <text class="section-title">费用明细</text>
      </view>
      <view class="cost-list">
        <view class="cost-item">
          <text class="cost-label">商品总价</text>
          <text class="cost-value">¥{{ order.productAmount }}</text>
        </view>
        <view class="cost-item">
          <text class="cost-label">运费</text>
          <text class="cost-value">¥{{ order.shippingFee || 0 }}</text>
        </view>
        <view class="cost-item" v-if="order.discountAmount">
          <text class="cost-label">优惠金额</text>
          <text class="cost-value discount">-¥{{ order.discountAmount }}</text>
        </view>
        <view class="cost-item total">
          <text class="cost-label">实付金额</text>
          <text class="cost-value">¥{{ order.totalAmount }}</text>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-actions" v-if="hasActions">
      <u-button 
        v-if="order.status === 'pending'"
        type="info"
        plain
        @click="cancelOrder"
      >
        取消订单
      </u-button>
      <u-button 
        v-if="order.status === 'pending'"
        type="primary"
        @click="payOrder"
      >
        立即支付
      </u-button>
      <u-button 
        v-if="order.status === 'shipped'"
        type="primary"
        @click="confirmOrder"
      >
        确认收货
      </u-button>
      <u-button 
        v-if="order.status === 'completed'"
        type="warning"
        @click="evaluateOrder"
      >
        评价商品
      </u-button>
    </view>
  </view>
</template>

<script>
import { getOrderDetail, cancelOrder, payOrder, confirmOrder } from '@/api/index'

export default {
  data() {
    return {
      orderId: '',
      order: {
        id: 1,
        orderNo: 'LB202312010001',
        status: 'shipped',
        createTime: Date.now() - 2 * 24 * 60 * 60 * 1000,
        payTime: Date.now() - 2 * 24 * 60 * 60 * 1000 + 10 * 60 * 1000,
        deliveryTime: Date.now() - 1 * 24 * 60 * 60 * 1000,
        totalAmount: 9999,
        productAmount: 9999,
        shippingFee: 0,
        discountAmount: 0,
        paymentMethod: '微信支付',
        address: {
          name: '张三',
          phone: '138****8888',
          detail: '北京市朝阳区xxx街道xxx小区xxx号楼xxx室'
        },
        logistics: {
          status: '运输中',
          lastUpdate: '您的包裹已从北京分拨中心发出，正在运往目的地'
        },
        products: [
          {
            id: 1,
            name: 'iPhone 15 Pro Max',
            spec: '深空黑 256GB',
            price: 9999,
            quantity: 1,
            image: '/static/product1.jpg'
          }
        ]
      }
    }
  },

  computed: {
    hasActions() {
      return ['pending', 'shipped', 'completed'].includes(this.order.status)
    }
  },

  onLoad(options) {
    if (options.id) {
      this.orderId = options.id
      this.loadOrderDetail()
    }
  },

  methods: {
    // 加载订单详情
    async loadOrderDetail() {
      try {
        uni.showLoading({ title: '加载中...' })
        
        // 实际项目中调用API
        // const res = await orderApi.getOrderDetail(this.orderId)
        // this.order = res.data
        
        // 模拟数据
        console.log('加载订单详情:', this.orderId)
        
        uni.hideLoading()
      } catch (error) {
        uni.hideLoading()
        console.error('加载订单详情失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    },

    // 获取状态图标
    getStatusIcon(status) {
      const iconMap = {
        pending: 'clock',
        paid: 'checkmark-circle',
        shipped: 'car',
        completed: 'checkmark-circle-fill',
        cancelled: 'close-circle'
      }
      return iconMap[status] || 'help-circle'
    },

    // 获取状态颜色
    getStatusColor(status) {
      const colorMap = {
        pending: '#ff9500',
        paid: '#007aff',
        shipped: '#4cd964',
        completed: '#4cd964',
        cancelled: '#ff4757'
      }
      return colorMap[status] || '#666'
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        pending: '等待付款',
        paid: '等待发货',
        shipped: '运输中',
        completed: '已完成',
        cancelled: '已取消'
      }
      return textMap[status] || '未知状态'
    },

    // 获取状态描述
    getStatusDesc(status) {
      const descMap = {
        pending: '请在24小时内完成支付',
        paid: '商家正在准备发货',
        shipped: '商品正在配送中，请耐心等待',
        completed: '订单已完成，感谢您的购买',
        cancelled: '订单已取消'
      }
      return descMap[status] || ''
    },

    // 格式化时间
    formatTime(timestamp) {
      const date = new Date(timestamp)
      return date.toLocaleString()
    },

    // 跳转到物流详情
    goToLogistics() {
      uni.navigateTo({
        url: `/pages/order/logistics?orderId=${this.order.id}`
      })
    },

    // 跳转到商品详情
    goToProductDetail(productId) {
      uni.navigateTo({
        url: `/pages/product/detail?id=${productId}`
      })
    },

    // 取消订单
    async cancelOrder() {
      try {
        const res = await uni.showModal({
          title: '提示',
          content: '确定要取消这个订单吗？'
        })
        
        if (res.confirm) {
          // 调用API取消订单
          // await orderApi.cancelOrder(this.order.id)
          
          this.order.status = 'cancelled'
          
          uni.showToast({
            title: '订单已取消',
            icon: 'success'
          })
        }
      } catch (error) {
        console.error('取消订单失败:', error)
        uni.showToast({
          title: '取消失败',
          icon: 'none'
        })
      }
    },

    // 支付订单
    payOrder() {
      uni.navigateTo({
        url: `/pages/order/pay?orderId=${this.order.id}`
      })
    },

    // 确认收货
    async confirmOrder() {
      try {
        const res = await uni.showModal({
          title: '提示',
          content: '确定已收到货物吗？'
        })
        
        if (res.confirm) {
          // 调用API确认收货
          // await orderApi.confirmOrder(this.order.id)
          
          this.order.status = 'completed'
          
          uni.showToast({
            title: '确认收货成功',
            icon: 'success'
          })
        }
      } catch (error) {
        console.error('确认收货失败:', error)
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        })
      }
    },

    // 评价订单
    evaluateOrder() {
      uni.navigateTo({
        url: `/pages/order/evaluate?orderId=${this.order.id}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.order-detail {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.status-section {
  background-color: #fff;
  padding: 40rpx 30rpx;
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;

  .status-icon {
    margin-right: 30rpx;
  }

  .status-info {
    flex: 1;

    .status-text {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      display: block;
      margin-bottom: 10rpx;
    }

    .status-desc {
      font-size: 28rpx;
      color: #666;
    }
  }
}

.logistics-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;

    .header-left {
      display: flex;
      align-items: center;

      .header-title {
        font-size: 28rpx;
        color: #333;
        margin-left: 10rpx;
      }
    }

    .header-right {
      display: flex;
      align-items: center;

      .logistics-status {
        font-size: 28rpx;
        color: #666;
        margin-right: 10rpx;
      }
    }
  }

  .logistics-info {
    font-size: 26rpx;
    color: #666;
    line-height: 1.5;
  }
}

.address-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .address-header {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    .address-title {
      font-size: 28rpx;
      color: #333;
      margin-left: 10rpx;
    }
  }

  .address-info {
    .address-contact {
      display: flex;
      align-items: center;
      margin-bottom: 10rpx;

      .contact-name {
        font-size: 28rpx;
        color: #333;
        margin-right: 20rpx;
      }

      .contact-phone {
        font-size: 28rpx;
        color: #666;
      }
    }

    .address-detail {
      font-size: 26rpx;
      color: #666;
      line-height: 1.5;
    }
  }
}

.products-section {
  background-color: #fff;
  margin-bottom: 20rpx;

  .section-header {
    padding: 30rpx;
    border-bottom: 1rpx solid #f5f5f5;

    .section-title {
      font-size: 28rpx;
      color: #333;
      font-weight: bold;
    }
  }

  .product-item {
    display: flex;
    padding: 30rpx;
    border-bottom: 1rpx solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    .product-image {
      width: 120rpx;
      height: 120rpx;
      border-radius: 10rpx;
      margin-right: 20rpx;
    }

    .product-info {
      flex: 1;
      display: flex;
      flex-direction: column;

      .product-name {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 10rpx;
      }

      .product-spec {
        font-size: 24rpx;
        color: #999;
        margin-bottom: 10rpx;
      }

      .product-price-qty {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .product-price {
          font-size: 28rpx;
          color: #ff4757;
          font-weight: bold;
        }

        .product-quantity {
          font-size: 24rpx;
          color: #666;
        }
      }
    }
  }
}

.order-info-section, .cost-section {
  background-color: #fff;
  margin-bottom: 20rpx;

  .section-header {
    padding: 30rpx;
    border-bottom: 1rpx solid #f5f5f5;

    .section-title {
      font-size: 28rpx;
      color: #333;
      font-weight: bold;
    }
  }

  .info-list, .cost-list {
    .info-item, .cost-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 25rpx 30rpx;
      border-bottom: 1rpx solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      &.total {
        background-color: #fafafa;
        font-weight: bold;
      }

      .info-label, .cost-label {
        font-size: 28rpx;
        color: #666;
      }

      .info-value, .cost-value {
        font-size: 28rpx;
        color: #333;

        &.discount {
          color: #ff4757;
        }
      }
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx;
  border-top: 1rpx solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
}
</style> 