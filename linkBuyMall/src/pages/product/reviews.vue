<template>
  <view class="reviews-page">
    <!-- 评价统计 -->
    <view class="review-stats review-filters">
      <text class="stats-title">商品评价 ({{ totalReviews }})</text>
      <view class="filter-row">
        <view 
          class="filter-tag" 
          :class="{ active: currentFilter === 'all' }"
          :style="{ '--theme-primary': themeStore.primaryColor }"
          @click="selectFilter('all')"
        >
          <text>全部({{ totalReviews }})</text>
        </view>
        <view 
          class="filter-tag" 
          :class="{ active: currentFilter === 'withImages' }"
          :style="{ '--theme-primary': themeStore.primaryColor }"
          @click="selectFilter('withImages')"
        >
          <text>有图({{ reviewsWithImages }})</text>
        </view>
        <view 
          class="filter-tag" 
          :class="{ active: currentFilter === 'positive' }"
          :style="{ '--theme-primary': themeStore.primaryColor }"
          @click="selectFilter('positive')"
        >
          <text>好评({{ positiveReviews }})</text>
        </view>
        <view 
          class="filter-tag" 
          :class="{ active: currentFilter === 'negative' }"
          :style="{ '--theme-primary': themeStore.primaryColor }"
          @click="selectFilter('negative')"
        >
          <text>差评({{ negativeReviews }})</text>
        </view>
      </view>
    </view>

    <!-- 评价列表 -->
    <view class="reviews-list">
      <view class="review-item" v-for="review in filteredReviews" :key="review.id">
        <!-- 用户信息和评分 -->
        <view class="review-header">
          <view class="user-info">
            <image class="user-avatar" :src="review.avatar" mode="aspectFill"></image>
            <view class="user-details">
              <text class="user-name">{{ review.name }}</text>
              <view class="user-badge" v-if="review.isVip">
                <u-icon name="checkmark-circle-fill" size="14" color="#ff9500"></u-icon>
              </view>
            </view>
          </view>
          <view class="review-rating">
            <u-icon 
              v-for="i in 5" 
              :key="i" 
              name="star-fill" 
              size="14"
              :color="i <= review.rating ? '#ff4757' : '#e4e7ed'"
            ></u-icon>
          </view>
        </view>

        <!-- 评价时间 -->
        <view class="review-date">
          <text>{{ formatDate(review.date) }}</text>
        </view>

        <!-- 评价内容 -->
        <view class="review-content">
          <text>{{ review.content }}</text>
        </view>

        <!-- 评价图片 -->
        <view class="review-images" v-if="review.images && review.images.length > 0">
          <image 
            v-for="(img, index) in review.images" 
            :key="index"
            :src="img" 
            class="review-image"
            mode="aspectFill"
            @click="previewImage(img, review.images)"
          ></image>
        </view>

        <!-- 商品信息 -->
        <view class="review-product">
          <text class="product-spec">{{ review.productSpec }}</text>
        </view>

        <!-- 操作按钮 -->
        <view class="review-actions">
          <view class="action-btn" @click="likeReview(review.id)">
            <text class="action-count">{{ review.likeCount || 0 }}</text>
            <u-icon name="chat" size="16" color="#999"></u-icon>
          </view>
          <view class="action-btn" @click="likeReview(review.id)">
            <text class="action-count">{{ review.likeCount || 0 }}</text>
            <u-icon name="heart" size="16" color="#999"></u-icon>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" v-if="hasMore">
      <u-loadmore :status="loadStatus" @loadmore="loadMoreReviews"></u-loadmore>
    </view>

    <!-- 底部去购买按钮 -->
    <view class="bottom-action">
      <view 
        class="buy-btn" 
        :style="{ backgroundColor: themeStore.primaryColor }"
        @click="goToBuy"
      >
        <text>去购买</text>
      </view>
    </view>
  </view>
</template>

<script>
import { useThemeStore } from '@/store/theme'
import { useTenantStore } from '@/store/tenant'
import { getProductReviews } from '@/api/productReview'

export default {
  data() {
    return {
      productId: '',
      currentFilter: 'all', // 当前筛选类型
      reviews: [], // 所有评价
      totalReviews: 0,
      reviewsWithImages: 0,
      positiveReviews: 0,
      negativeReviews: 0,
      page: 1,
      pageSize: 20,
      hasMore: true,
      loadStatus: 'loadmore' // loadmore, loading, nomore
    }
  },

  setup() {
    const themeStore = useThemeStore()
    const tenantStore = useTenantStore()
    return {
      themeStore,
      tenantStore
    }
  },

  computed: {
    // 根据筛选条件过滤评价
    filteredReviews() {
      switch (this.currentFilter) {
        case 'withImages':
          return this.reviews.filter(review => review.images && review.images.length > 0)
        case 'positive':
          return this.reviews.filter(review => review.rating >= 4)
        case 'negative':
          return this.reviews.filter(review => review.rating <= 2)
        case 'all':
        default:
          return this.reviews
      }
    }
  },

  onLoad(options) {
    if (options.productId) {
      this.productId = options.productId
      this.loadReviews()
    }
  },

  methods: {
    // 选择筛选条件
    selectFilter(filter) {
      this.currentFilter = filter
    },

    // 加载评价列表
    async loadReviews() {
      try {
        uni.showLoading({ title: '加载中...' })

        const scode = this.tenantStore.getStoreCode
        if (!scode) {
          throw new Error('商城编码不存在')
        }

        const response = await getProductReviews({
          scode,
          productId: parseInt(this.productId),
          page: this.page,
          pageSize: this.pageSize
        })

        if (response.code === 200 && response.data) {
          const data = response.data
          
          if (this.page === 1) {
            this.reviews = data.list
          } else {
            this.reviews = [...this.reviews, ...data.list]
          }

          this.totalReviews = data.total
          this.calculateStats()

          // 判断是否还有更多数据
          this.hasMore = this.reviews.length < data.total
          this.loadStatus = this.hasMore ? 'loadmore' : 'nomore'
        }

        uni.hideLoading()
      } catch (error) {
        uni.hideLoading()
        console.error('加载评价失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    },

    // 加载更多评价
    async loadMoreReviews() {
      if (!this.hasMore || this.loadStatus === 'loading') return

      this.loadStatus = 'loading'
      this.page++
      await this.loadReviews()
    },

    // 计算各类评价统计
    calculateStats() {
      this.reviewsWithImages = this.reviews.filter(review => 
        review.images && review.images.length > 0
      ).length

      this.positiveReviews = this.reviews.filter(review => 
        review.rating >= 4
      ).length

      this.negativeReviews = this.reviews.filter(review => 
        review.rating <= 2
      ).length
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return ''
      
      // 如果是时间戳，转换为日期
      let date
      if (typeof dateStr === 'number') {
        date = new Date(dateStr)
      } else {
        date = new Date(dateStr)
      }
      
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      
      return `${year}-${month}-${day}`
    },

    // 预览图片
    previewImage(current, urls) {
      uni.previewImage({
        current,
        urls
      })
    },

    // 点赞评价
    likeReview(reviewId) {
      // TODO: 实现点赞功能
      console.log('点赞评价:', reviewId)
    },

    // 去购买
    goToBuy() {
      // 返回到对应的商品详情页
      if (this.productId) {
        // 如果有商品ID，跳转到商品详情页
        uni.redirectTo({
          url: `/pages/product/detail?id=${this.productId}`
        })
      } else {
        // 如果没有商品ID，直接返回上一页
        uni.navigateBack()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.reviews-page {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;

  .header-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
  }

  .header-right {
    width: 40rpx; // 占位，保持标题居中
  }
}

.review-stats {
  padding: 30rpx;
  background-color: #fff;
  margin-bottom: 20rpx;

  .stats-title {
    font-size: 32rpx;
    font-weight: bold;
    padding-bottom: 25rpx;
    color: #333;
    margin-bottom: 10rpx;
  }
}

.review-filters {
  padding: 0 30rpx 30rpx;
  background-color: #fff;
  margin-bottom: 20rpx;

  .filter-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20rpx;
    margin-top: 15rpx;

    &:first-child {
      margin-top: 20rpx;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  .filter-tag {
    padding: 12rpx 20rpx;
    margin-right: 15rpx;
    margin-bottom: 15rpx;
    border-radius: 25rpx;
    background-color: #f5f5f5;
    font-size: 24rpx;
    color: #666;
    white-space: nowrap;

    &.active {
      background-color: var(--theme-primary, #ff4757);
      color: #fff;
    }

    &.disabled {
      background-color: #f8f9fa;
      color: #999;
      opacity: 0.8;
    }

    &:last-child {
      margin-right: 0;
    }

    text {
      font-size: 24rpx;
      line-height: 1.2;
    }
  }
}

.reviews-list {
  .review-item {
    background-color: #fff;
    margin-bottom: 20rpx;
    padding: 30rpx;

    .review-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15rpx;

      .user-info {
        display: flex;
        align-items: center;

        .user-avatar {
          width: 60rpx;
          height: 60rpx;
          border-radius: 50%;
          margin-right: 20rpx;
        }

        .user-details {
          display: flex;
          align-items: center;

          .user-name {
            font-size: 28rpx;
            color: #333;
            margin-right: 10rpx;
          }

          .user-badge {
            display: flex;
            align-items: center;
          }
        }
      }

      .review-rating {
        display: flex;
        gap: 2rpx;
      }
    }

    .review-date {
      margin-bottom: 15rpx;

      text {
        font-size: 24rpx;
        color: #999;
      }
    }

    .review-content {
      margin-bottom: 20rpx;

      text {
        font-size: 28rpx;
        color: #333;
        line-height: 1.6;
      }
    }

    .review-images {
      display: flex;
      flex-wrap: wrap;
      gap: 15rpx;
      margin-bottom: 20rpx;

      .review-image {
        width: 150rpx;
        height: 150rpx;
        border-radius: 10rpx;
      }
    }

    .review-product {
      margin-bottom: 20rpx;
      padding: 15rpx;
      background-color: #f8f9fa;
      border-radius: 8rpx;

      .product-spec {
        font-size: 24rpx;
        color: #666;
      }
    }

    .review-actions {
      display: flex;
      justify-content: flex-end;
      gap: 30rpx;

      .action-btn {
        display: flex;
        align-items: center;
        gap: 8rpx;

        .action-count {
          font-size: 24rpx;
          color: #999;
        }
      }
    }
  }
}

.load-more {
  padding: 20rpx;
}

.bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;

  .buy-btn {
    width: 100%;
    height: 80rpx;
    border-radius: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    text {
      font-size: 30rpx;
      font-weight: 500;
      color: #fff;
    }
  }
}
</style>
