<template>
  <view class="login-test">
    <view class="test-container">
      <view class="test-header">
        <text class="test-title">登录功能测试</text>
        <text class="platform-info">当前平台：{{ platformName }}</text>
      </view>

      <!-- 平台信息 -->
      <view class="info-section">
        <view class="info-item">
          <text class="info-label">平台类型：</text>
          <text class="info-value">{{ platform }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">支持的登录方式：</text>
          <view class="methods-list">
            <text class="method-item" v-if="supportedMethods.password">密码登录</text>
            <text class="method-item" v-if="supportedMethods.sms">短信登录</text>
            <text class="method-item" v-if="supportedMethods.wechat">微信登录</text>
            <text class="method-item" v-if="supportedMethods.guest">游客模式</text>
          </view>
        </view>
        <view class="info-item">
          <text class="info-label">推荐登录方式：</text>
          <text class="info-value">{{ recommendedMethod }}</text>
        </view>
      </view>

      <!-- 用户状态 -->
      <view class="status-section">
        <view class="status-title">用户状态</view>
        <view class="status-item">
          <text class="status-label">登录状态：</text>
          <text class="status-value" :class="{ active: userStore.isLoggedIn }">
            {{ userStore.isLoggedIn ? '已登录' : '未登录' }}
          </text>
        </view>
        <view class="status-item">
          <text class="status-label">用户类型：</text>
          <text class="status-value">{{ userTypeText }}</text>
        </view>
        <view class="status-item" v-if="userStore.isLoggedIn">
          <text class="status-label">用户昵称：</text>
          <text class="status-value">{{ userStore.nickname }}</text>
        </view>
        <view class="status-item" v-if="userStore.isLoggedIn">
          <text class="status-label">Token：</text>
          <text class="status-value token">{{ userStore.token.slice(0, 20) }}...</text>
        </view>
      </view>

      <!-- 功能测试按钮 -->
      <view class="test-actions">
        <u-button type="primary" @click="testLogin" :disabled="userStore.isLoggedIn">
          测试登录
        </u-button>
        <u-button type="success" @click="testRegister" :disabled="userStore.isLoggedIn">
          测试注册
        </u-button>
        <u-button type="warning" @click="testWechatLogin" :disabled="!supportedMethods.wechat || userStore.isLoggedIn">
          测试微信登录
        </u-button>
        <u-button type="info" @click="testGuestMode">
          游客模式
        </u-button>
        <u-button type="error" @click="testLogout" :disabled="!userStore.isLoggedIn">
          退出登录
        </u-button>
      </view>

      <!-- 权限测试 -->
      <view class="permission-section">
        <view class="permission-title">权限测试</view>
        <view class="permission-actions">
          <u-button size="small" @click="testCartPermission">购物车权限</u-button>
          <u-button size="small" @click="testOrderPermission">下单权限</u-button>
          <u-button size="small" @click="testAddressPermission">地址权限</u-button>
          <u-button size="small" @click="testFavoritePermission">收藏权限</u-button>
        </view>
      </view>

      <!-- 日志输出 -->
      <view class="log-section">
        <view class="log-title">操作日志</view>
        <scroll-view class="log-content" scroll-y>
          <view class="log-item" v-for="(log, index) in logs" :key="index">
            <text class="log-time">{{ log.time }}</text>
            <text class="log-message">{{ log.message }}</text>
          </view>
        </scroll-view>
        <u-button size="small" plain @click="clearLogs">清空日志</u-button>
      </view>
    </view>
  </view>
</template>

<script>
import { useUserStore } from '@/store/user'
import { 
  getPlatformType, 
  getPlatformName, 
  getSupportedLoginMethods, 
  getRecommendedLoginMethod 
} from '@/utils/platform'
import { 
  checkCartPermission, 
  checkOrderPermission, 
  checkAddressPermission, 
  checkFavoritePermission 
} from '@/utils/auth'

export default {
  name: 'LoginTest',
  
  data() {
    return {
      userStore: useUserStore(),
      platform: getPlatformType(),
      platformName: getPlatformName(),
      supportedMethods: getSupportedLoginMethods(),
      recommendedMethod: getRecommendedLoginMethod(),
      logs: []
    }
  },

  computed: {
    userTypeText() {
      if (!this.userStore.isLoggedIn) {
        return '未登录'
      }
      if (this.userStore.isGuest) {
        return '游客用户'
      }
      if (this.userStore.isRegistered) {
        return '注册用户'
      }
      return '未知'
    }
  },

  onLoad() {
    this.addLog('页面加载完成')
    this.addLog(`当前平台：${this.platformName}`)
  },

  methods: {
    // 添加日志
    addLog(message) {
      const now = new Date()
      const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
      this.logs.unshift({
        time,
        message
      })
      
      // 限制日志数量
      if (this.logs.length > 50) {
        this.logs = this.logs.slice(0, 50)
      }
    },

    // 清空日志
    clearLogs() {
      this.logs = []
    },

    // 测试登录
    async testLogin() {
      this.addLog('开始测试登录...')
      
      try {
        const result = await this.userStore.login({
          loginType: 'password',
          platform: this.platform,
          mobile: '13800138000',
          password: '123456'
        })
        
        if (result.success) {
          this.addLog('登录测试成功')
        } else {
          this.addLog(`登录测试失败：${result.message}`)
        }
      } catch (error) {
        this.addLog(`登录测试异常：${error.message}`)
      }
    },

    // 测试注册
    async testRegister() {
      this.addLog('开始测试注册...')
      
      try {
        const result = await this.userStore.register({
          mobile: '13800138001',
          smsCode: '123456',
          password: '123456',
          confirmPassword: '123456',
          nickname: '测试用户',
          platform: this.platform
        })
        
        if (result.success) {
          this.addLog('注册测试成功')
        } else {
          this.addLog(`注册测试失败：${result.message}`)
        }
      } catch (error) {
        this.addLog(`注册测试异常：${error.message}`)
      }
    },

    // 测试微信登录
    async testWechatLogin() {
      this.addLog('开始测试微信登录...')
      
      try {
        const result = await this.userStore.wechatLogin('test_wechat_code')
        
        if (result.success) {
          this.addLog('微信登录测试成功')
        } else {
          this.addLog(`微信登录测试失败：${result.message}`)
        }
      } catch (error) {
        this.addLog(`微信登录测试异常：${error.message}`)
      }
    },

    // 测试游客模式
    testGuestMode() {
      this.addLog('切换到游客模式')
      // 这里可以模拟游客状态
      uni.showToast({
        title: '游客模式演示',
        icon: 'none'
      })
    },

    // 测试退出登录
    testLogout() {
      this.addLog('退出登录')
      this.userStore.logout()
      this.addLog('已退出登录')
    },

    // 测试购物车权限
    testCartPermission() {
      this.addLog('测试购物车权限...')
      const hasPermission = checkCartPermission()
      this.addLog(`购物车权限检查结果：${hasPermission ? '通过' : '拒绝'}`)
    },

    // 测试下单权限
    testOrderPermission() {
      this.addLog('测试下单权限...')
      const hasPermission = checkOrderPermission()
      this.addLog(`下单权限检查结果：${hasPermission ? '通过' : '拒绝'}`)
    },

    // 测试地址权限
    testAddressPermission() {
      this.addLog('测试地址权限...')
      const hasPermission = checkAddressPermission()
      this.addLog(`地址权限检查结果：${hasPermission ? '通过' : '拒绝'}`)
    },

    // 测试收藏权限
    testFavoritePermission() {
      this.addLog('测试收藏权限...')
      const hasPermission = checkFavoritePermission()
      this.addLog(`收藏权限检查结果：${hasPermission ? '通过' : '拒绝'}`)
    }
  }
}
</script>

<style lang="scss" scoped>
.login-test {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.test-container {
  max-width: 700rpx;
  margin: 0 auto;
}

.test-header {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  text-align: center;

  .test-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
    display: block;
  }

  .platform-info {
    font-size: 24rpx;
    color: #666;
  }
}

.info-section, .status-section, .permission-section, .log-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.info-item, .status-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.info-label, .status-label {
  font-size: 28rpx;
  color: #666;
  width: 200rpx;
}

.info-value, .status-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  
  &.active {
    color: #07c160;
    font-weight: bold;
  }
  
  &.token {
    font-family: monospace;
    font-size: 24rpx;
    color: #999;
  }
}

.methods-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.method-item {
  background: #f0f0f0;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #666;
}

.test-actions, .permission-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.permission-title, .log-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.log-content {
  height: 300rpx;
  border: 1rpx solid #eee;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.log-item {
  display: flex;
  margin-bottom: 10rpx;
  font-size: 24rpx;
  
  .log-time {
    color: #999;
    width: 120rpx;
    margin-right: 20rpx;
  }
  
  .log-message {
    color: #333;
    flex: 1;
  }
}
</style>
