<template>
  <view class="cart">
    <!-- 游客提示 -->
    <view class="guest-notice" v-if="userStore.isGuest">
      <view class="notice-content">
        <text class="notice-icon">👤</text>
        <text class="notice-title">您当前是游客模式</text>
        <text class="notice-desc">游客无法使用购物车功能，请先登录</text>
        <view class="notice-actions">
          <u-button type="primary" size="small" @click="goToLogin">立即登录</u-button>
          <u-button type="default" size="small" plain @click="goToRegister">注册账号</u-button>
        </view>
      </view>
    </view>

    <view class="cart-content" v-else>
      <view class="empty-cart" v-if="cartList.length === 0">
        <text class="empty-icon">🛒</text>
        <text class="empty-text">购物车空空如也</text>
        <text class="empty-desc">快去挑选心仪的商品吧</text>
        <u-button type="primary" @click="goToHome">去逛逛</u-button>
      </view>
      
      <view class="cart-list" v-else>
        <view 
          class="cart-item" 
          v-for="item in cartList" 
          :key="item.id"
        >
          <view class="item-checkbox">
            <checkbox :checked="item.selected" @change="toggleSelect(item)" />
          </view>
          <image class="item-image" :src="item.image" mode="aspectFill" />
          <view class="item-info">
            <text class="item-name">{{ item.name }}</text>
            <text class="item-spec">{{ item.spec }}</text>
            <view class="item-bottom">
              <text class="item-price">¥{{ item.price }}</text>
              <view class="quantity-control">
                <text class="quantity-btn" @click="decreaseQuantity(item)">-</text>
                <text class="quantity-text">{{ item.quantity }}</text>
                <text class="quantity-btn" @click="increaseQuantity(item)">+</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部结算栏 -->
    <view class="cart-footer" v-if="cartList.length > 0 && !userStore.isGuest">
      <view class="footer-left">
        <checkbox :checked="allSelected" @change="toggleSelectAll" />
        <text class="select-all-text">全选</text>
      </view>
      <view class="footer-right">
        <text class="total-text">合计：¥{{ totalPrice }}</text>
        <view class="checkout-btn" @click="checkout">
          <text>结算({{ selectedCount }})</text>
        </view>
      </view>
    </view>
    
    <!-- 自定义TabBar -->
    <CustomTabBar />
  </view>
</template>

<script>
import CustomTabBar from '@/components/CustomTabBar/CustomTabBar.vue'
import { useUserStore } from '@/store/user'
import { checkOrderPermission } from '@/utils/auth'

export default {
  name: 'Cart',
  components: {
    CustomTabBar
  },

  setup() {
    const userStore = useUserStore()
    return {
      userStore
    }
  },
  data() {
    return {
      cartList: [
        {
          id: 1,
          name: 'iPhone 15 Pro',
          spec: '深空黑色 256GB',
          price: 8999,
          quantity: 1,
          image: '/static/product1.jpg',
          selected: true
        },
        {
          id: 2,
          name: '华为Mate60 Pro',
          spec: '雅川青 512GB',
          price: 6999,
          quantity: 2,
          image: '/static/product2.jpg',
          selected: false
        }
      ]
    }
  },
  
  computed: {
    // 全选状态
    allSelected() {
      return this.cartList.length > 0 && this.cartList.every(item => item.selected)
    },
    
    // 已选商品数量
    selectedCount() {
      return this.cartList.filter(item => item.selected).reduce((sum, item) => sum + item.quantity, 0)
    },
    
    // 总价
    totalPrice() {
      return this.cartList
        .filter(item => item.selected)
        .reduce((sum, item) => sum + item.price * item.quantity, 0)
        .toFixed(2)
    }
  },
  
  onShow() {
    // 触发tabBar更新
    uni.$emit('tabBarChange')

    // 检查购物车权限
    if (this.userStore.isGuest) {
      // 游客模式下清空购物车数据
      this.cartList = []
    }
  },
  
  methods: {
    // 切换单个商品选中状态
    toggleSelect(item) {
      item.selected = !item.selected
    },
    
    // 切换全选状态
    toggleSelectAll() {
      const newState = !this.allSelected
      this.cartList.forEach(item => {
        item.selected = newState
      })
    },
    
    // 增加数量
    increaseQuantity(item) {
      item.quantity++
    },
    
    // 减少数量
    decreaseQuantity(item) {
      if (item.quantity > 1) {
        item.quantity--
      }
    },
    
    // 结算
    checkout() {
      if (!checkOrderPermission()) {
        return
      }

      if (this.selectedCount === 0) {
        uni.showToast({
          title: '请选择商品',
          icon: 'none'
        })
        return
      }

      uni.showToast({
        title: '跳转到结算页面',
        icon: 'success'
      })
    },

    // 跳转到登录页面
    goToLogin() {
      uni.navigateTo({
        url: '/pages/login/login'
      })
    },

    // 跳转到注册页面
    goToRegister() {
      uni.navigateTo({
        url: '/pages/register/register'
      })
    },

    // 跳转到首页
    goToHome() {
      uni.switchTab({
        url: '/pages/landingPage/landingPage'
      })
    }
  }
}
</script>

<style scoped>
.cart {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 160rpx; /* 为tabBar和结算栏留出空间 */
}

.guest-notice {
  padding: 40rpx 20rpx;

  .notice-content {
    background: white;
    border-radius: 20rpx;
    padding: 60rpx 40rpx;
    text-align: center;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

    .notice-icon {
      font-size: 120rpx;
      margin-bottom: 30rpx;
      display: block;
    }

    .notice-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 20rpx;
      display: block;
    }

    .notice-desc {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 40rpx;
      display: block;
    }

    .notice-actions {
      display: flex;
      gap: 20rpx;
      justify-content: center;
    }
  }
}

.cart-content {
  padding: 20rpx;
}

.empty-cart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 0;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
}

.cart-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.cart-item {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 15rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.item-checkbox {
  margin-right: 20rpx;
}

.item-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.item-name {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.item-spec {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 20rpx;
}

.item-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-price {
  font-size: 32rpx;
  color: #ff4757;
  font-weight: bold;
}

.quantity-control {
  display: flex;
  align-items: center;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #666;
  background: #f5f5f5;
}

.quantity-text {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #333;
  background: white;
}

.cart-footer {
  position: fixed;
  bottom: 100rpx; /* tabBar高度 */
  left: 0;
  right: 0;
  height: 100rpx;
  background: white;
  border-top: 1rpx solid #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;
  z-index: 999;
}

.footer-left {
  display: flex;
  align-items: center;
}

.select-all-text {
  font-size: 28rpx;
  color: #333;
  margin-left: 10rpx;
}

.footer-right {
  display: flex;
  align-items: center;
}

.total-text {
  font-size: 32rpx;
  color: #ff4757;
  font-weight: bold;
  margin-right: 20rpx;
}

.checkout-btn {
  background: #ff4757;
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
}
</style> 