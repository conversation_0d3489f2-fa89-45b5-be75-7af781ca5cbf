<template>
  <view class="register">
    <view class="register-container">
      <!-- Logo区域 -->
      <view class="logo-section">
        <image src="/static/logo.png" class="logo" mode="aspectFit"></image>
        <text class="app-name">LinkBuy商城</text>
        <text class="platform-info">用户注册 - {{ platformName }}</text>
      </view>

      <!-- 微信注册（推荐方式） -->
      <view class="wechat-register-section" v-if="supportedMethods.wechat && isRecommendedWechat">
        <u-button 
          type="primary" 
          :loading="wechatLoading"
          @click="handleWechatRegister"
          class="wechat-register-btn"
          color="#1aad19"
        >
          <u-icon name="weixin" size="20" color="#fff" style="margin-right: 8px;"></u-icon>
          {{ wechatLoading ? '微信注册中...' : '微信快速注册' }}
        </u-button>
        
        <view class="divider">
          <text class="divider-text">手机号注册</text>
        </view>
      </view>

      <!-- 注册表单 -->
      <view class="form-section">
        <u-form :model="form" ref="form" :rules="rules">
          <!-- 手机号输入 -->
          <u-form-item prop="mobile">
            <u-input 
              v-model="form.mobile"
              placeholder="请输入手机号"
              prefix-icon="phone"
              clearable
              type="number"
              maxlength="11"
            ></u-input>
          </u-form-item>
          
          <!-- 验证码输入 -->
          <u-form-item prop="smsCode">
            <view class="sms-input-wrapper">
              <u-input 
                v-model="form.smsCode"
                placeholder="请输入验证码"
                prefix-icon="checkmark-circle"
                clearable
                type="number"
                maxlength="6"
                class="sms-input"
              ></u-input>
              <u-button 
                :disabled="smsCountdown > 0 || !form.mobile || !isValidMobile"
                @click="sendSmsCode"
                size="small"
                type="primary"
                plain
                class="sms-btn"
              >
                {{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
              </u-button>
            </view>
          </u-form-item>

          <!-- 密码输入 -->
          <u-form-item prop="password">
            <u-input 
              v-model="form.password"
              type="password"
              placeholder="请设置密码（6-20位）"
              prefix-icon="lock"
              clearable
              :show-password="true"
            ></u-input>
          </u-form-item>

          <!-- 确认密码 -->
          <u-form-item prop="confirmPassword">
            <u-input 
              v-model="form.confirmPassword"
              type="password"
              placeholder="请确认密码"
              prefix-icon="lock"
              clearable
              :show-password="true"
            ></u-input>
          </u-form-item>

          <!-- 昵称输入（可选） -->
          <u-form-item prop="nickname">
            <u-input 
              v-model="form.nickname"
              placeholder="请输入昵称（可选）"
              prefix-icon="account"
              clearable
              maxlength="20"
            ></u-input>
          </u-form-item>
        </u-form>

        <!-- 注册按钮 -->
        <u-button 
          type="primary" 
          :loading="loading"
          @click="handleRegister"
          class="register-btn"
        >
          {{ loading ? '注册中...' : '立即注册' }}
        </u-button>

        <!-- 其他操作 -->
        <view class="other-actions">
          <text class="action-text" @click="goToLogin">已有账号？立即登录</text>
        </view>
      </view>

      <!-- 用户协议 -->
      <view class="agreement-section">
        <view class="agreement-checkbox">
          <u-checkbox v-model="agreeTerms" shape="circle">
            我已阅读并同意
          </u-checkbox>
          <text class="agreement-link" @click="showUserAgreement">《用户协议》</text>
          <text>和</text>
          <text class="agreement-link" @click="showPrivacyPolicy">《隐私政策》</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { useUserStore } from '@/store/user'
import { getPlatformType, getPlatformName, getSupportedLoginMethods, getRecommendedLoginMethod } from '@/utils/platform'

export default {
  name: 'Register',
  
  data() {
    return {
      userStore: useUserStore(),
      loading: false,
      wechatLoading: false,
      smsCountdown: 0,
      smsTimer: null,
      agreeTerms: false,
      platform: getPlatformType(),
      platformName: getPlatformName(),
      supportedMethods: getSupportedLoginMethods(),
      isRecommendedWechat: getRecommendedLoginMethod() === 'wechat',
      form: {
        mobile: '',
        smsCode: '',
        password: '',
        confirmPassword: '',
        nickname: ''
      },
      rules: {
        mobile: [
          {
            required: true,
            message: '请输入手机号',
            trigger: ['blur', 'change']
          },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: '请输入正确的手机号',
            trigger: ['blur', 'change']
          }
        ],
        smsCode: [
          {
            required: true,
            message: '请输入验证码',
            trigger: ['blur', 'change']
          },
          {
            pattern: /^\d{6}$/,
            message: '请输入6位数字验证码',
            trigger: ['blur', 'change']
          }
        ],
        password: [
          {
            required: true,
            message: '请设置密码',
            trigger: ['blur', 'change']
          },
          {
            min: 6,
            max: 20,
            message: '密码长度为6-20位',
            trigger: ['blur', 'change']
          }
        ],
        confirmPassword: [
          {
            required: true,
            message: '请确认密码',
            trigger: ['blur', 'change']
          },
          {
            validator: (rule, value, callback) => {
              if (value !== this.form.password) {
                callback(new Error('两次输入的密码不一致'))
              } else {
                callback()
              }
            },
            trigger: ['blur', 'change']
          }
        ]
      }
    }
  },

  computed: {
    isValidMobile() {
      return /^1[3-9]\d{9}$/.test(this.form.mobile)
    }
  },

  onLoad() {
    console.log('注册页面加载，当前平台:', this.platformName)
  },

  onUnload() {
    // 清理定时器
    if (this.smsTimer) {
      clearInterval(this.smsTimer)
    }
  },

  methods: {
    // 发送短信验证码
    async sendSmsCode() {
      if (!this.isValidMobile) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return
      }

      try {
        const result = await this.userStore.sendSmsCode(this.form.mobile, 'register')
        
        if (result.success) {
          uni.showToast({
            title: result.message || '验证码发送成功',
            icon: 'success'
          })
          
          // 开始倒计时
          this.startSmsCountdown()
        } else {
          uni.showToast({
            title: result.message || '验证码发送失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('发送验证码失败:', error)
        uni.showToast({
          title: '发送验证码失败',
          icon: 'none'
        })
      }
    },

    // 开始短信倒计时
    startSmsCountdown() {
      this.smsCountdown = 60
      this.smsTimer = setInterval(() => {
        this.smsCountdown--
        if (this.smsCountdown <= 0) {
          clearInterval(this.smsTimer)
          this.smsTimer = null
        }
      }, 1000)
    },

    // 注册处理
    async handleRegister() {
      if (!this.agreeTerms) {
        uni.showToast({
          title: '请先同意用户协议和隐私政策',
          icon: 'none'
        })
        return
      }

      try {
        // 表单验证
        const valid = await this.$refs.form.validate()
        if (!valid) return

        this.loading = true

        // 构建注册参数
        const registerParams = {
          mobile: this.form.mobile,
          smsCode: this.form.smsCode,
          password: this.form.password,
          confirmPassword: this.form.confirmPassword,
          nickname: this.form.nickname || `用户${this.form.mobile.slice(-4)}`,
          platform: this.platform
        }

        // 调用注册API
        const result = await this.userStore.register(registerParams)
        
        if (result.success) {
          uni.showToast({
            title: '注册成功',
            icon: 'success'
          })
          
          // 延迟跳转，让用户看到成功提示
          setTimeout(() => {
            this.navigateAfterRegister()
          }, 1500)
        } else {
          uni.showToast({
            title: result.message || '注册失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('注册失败:', error)
        uni.showToast({
          title: error.message || '注册失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
