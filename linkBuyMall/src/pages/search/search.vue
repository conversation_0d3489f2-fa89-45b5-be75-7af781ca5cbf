<template>
  <view class="search-page">
    <!-- 搜索栏 -->
    <view class="search-header">
      <view class="search-input-wrapper">
        <input 
          class="search-input" 
          :value="keyword"
          placeholder="请输入关键字搜索"
          @input="handleInput"
          @confirm="handleSearch"
          focus
        />
        <button class="search-btn" @click="handleSearch">搜索</button>
      </view>
    </view>
    
    <!-- 搜索结果 -->
    <view class="search-content">
      <view v-if="loading" class="loading">
        <text>搜索中...</text>
      </view>
      
      <view v-else-if="searchResults.length > 0" class="results">
        <view class="result-header">
          <text>找到 {{ searchResults.length }} 个相关结果</text>
        </view>
        
        <view class="result-list">
          <view 
            v-for="(item, index) in searchResults" 
            :key="index"
            class="result-item"
            @click="goToDetail(item)"
          >
            <image class="item-image" :src="item.image" mode="aspectFill" />
            <view class="item-info">
              <text class="item-title">{{ item.title }}</text>
              <text class="item-desc">{{ item.description }}</text>
              <text class="item-price">¥{{ item.price }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <view v-else-if="!loading && keyword" class="no-result">
        <text>未找到相关结果</text>
      </view>
      
      <view v-else class="search-tips">
        <text>请输入关键字进行搜索</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'SearchPage',
  
  data() {
    return {
      keyword: '',
      loading: false,
      searchResults: []
    }
  },
  
  onLoad(options) {
    if (options.keyword) {
      this.keyword = decodeURIComponent(options.keyword)
      this.performSearch()
    }
  },
  
  methods: {
    handleInput(e) {
      this.keyword = e.detail.value
    },
    
    handleSearch() {
      if (this.keyword.trim()) {
        this.performSearch()
      }
    },
    
    async performSearch() {
      this.loading = true
      
      try {
        // 模拟搜索API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // 模拟搜索结果
        this.searchResults = [
          {
            id: 1,
            title: `${this.keyword}相关商品1`,
            description: '这是一个很好的商品描述',
            price: '99.00',
            image: 'https://via.placeholder.com/200x200'
          },
          {
            id: 2,
            title: `${this.keyword}相关商品2`,
            description: '这是另一个商品描述',
            price: '199.00',
            image: 'https://via.placeholder.com/200x200'
          }
        ]
      } catch (error) {
        console.error('搜索失败:', error)
        uni.showToast({
          title: '搜索失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },
    
    goToDetail(item) {
      uni.navigateTo({
        url: `/pages/goods/detail?id=${item.id}`
      })
    }
  }
}
</script>

<style scoped>
.search-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.search-header {
  background: white;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.search-input {
  flex: 1;
  height: 70rpx;
  padding: 0 20rpx;
  background: #f5f5f5;
  border-radius: 35rpx;
  font-size: 28rpx;
}

.search-btn {
  padding: 15rpx 30rpx;
  background: #FF2C3C;
  color: white;
  border-radius: 35rpx;
  font-size: 28rpx;
  border: none;
}

.search-content {
  padding: 20rpx;
}

.loading {
  text-align: center;
  padding: 100rpx 0;
  color: #666;
}

.result-header {
  margin-bottom: 20rpx;
}

.result-header text {
  font-size: 28rpx;
  color: #666;
}

.result-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.result-item {
  display: flex;
  background: white;
  border-radius: 10rpx;
  padding: 20rpx;
  gap: 20rpx;
}

.item-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 10rpx;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.item-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.item-desc {
  font-size: 26rpx;
  color: #666;
}

.item-price {
  font-size: 32rpx;
  color: #FF2C3C;
  font-weight: bold;
}

.no-result,
.search-tips {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
}
</style> 