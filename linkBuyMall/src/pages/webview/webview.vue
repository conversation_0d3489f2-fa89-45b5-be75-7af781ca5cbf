<template>
  <view class="webview-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <text class="back-icon">‹</text>
        </view>
        <view class="navbar-title">
          <text class="title-text">{{ pageTitle }}</text>
        </view>
        <view class="navbar-right"></view>
      </view>
    </view>

    <!-- 网页内容 -->
    <web-view 
      :src="webUrl" 
      class="webview-content"
      @message="handleMessage"
      @error="handleError"
      @load="handleLoad"
    ></web-view>
    
    <!-- 加载提示 -->
    <view class="loading-container" v-if="loading">
      <text class="loading-text">页面加载中...</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      webUrl: '',
      pageTitle: '网页',
      loading: true,
      statusBarHeight: 0
    }
  },

  onLoad(options) {
    console.log('webview页面参数:', options)
    
    // 获取URL参数
    if (options.url) {
      this.webUrl = decodeURIComponent(options.url)
      console.log('要加载的网页URL:', this.webUrl)
    } else {
      uni.showToast({
        title: '缺少网页地址',
        icon: 'error'
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
      return
    }

    // 获取标题参数
    if (options.title) {
      this.pageTitle = decodeURIComponent(options.title)
    }

    // 获取系统状态栏高度
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight || 0
  },

  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack({
        fail: () => {
          // 如果无法返回，则跳转到首页
          uni.switchTab({
            url: '/pages/landingPage/landingPage'
          })
        }
      })
    },

    // 处理网页消息
    handleMessage(event) {
      console.log('收到网页消息:', event.detail)
    },

    // 处理加载错误
    handleError(event) {
      console.error('网页加载错误:', event.detail)
      this.loading = false
      uni.showToast({
        title: '网页加载失败',
        icon: 'error'
      })
    },

    // 处理加载完成
    handleLoad(event) {
      console.log('网页加载完成:', event.detail)
      this.loading = false
    }
  }
}
</script>

<style scoped>
.webview-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

.custom-navbar {
  background-color: #fff;
  border-bottom: 1px solid #e5e5e5;
  z-index: 1000;
}

.navbar-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}

.navbar-left {
  width: 60px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.back-icon {
  font-size: 24px;
  color: #333;
  font-weight: bold;
}

.navbar-title {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title-text {
  font-size: 16px;
  color: #333;
  font-weight: 500;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.navbar-right {
  width: 60px;
}

.webview-content {
  flex: 1;
  width: 100%;
}

.loading-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 999;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

/* H5环境下隐藏自定义导航栏 */
/* #ifdef H5 */
.custom-navbar {
  display: none;
}

.webview-content {
  height: 100vh;
}
/* #endif */
</style> 