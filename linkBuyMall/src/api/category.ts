import { get } from '@/utils/request'
import type { ApiResponse } from '@/types'

// ==================== 类型定义 ====================

// 分类DTO
export interface CategoryDto {
  /**
   * 分类ID
   */
  id: number
  
  /**
   * 分类名称
   */
  name: string
  
  /**
   * 分类图标
   */
  icon?: string
  
  /**
   * 父分类ID
   */
  parentId?: number
  
  /**
   * 分类层级
   */
  level: number
  
  /**
   * 排序权重
   */
  sort: number
  
  /**
   * 是否显示
   */
  isShow: boolean
  
  /**
   * 商品数量
   */
  productCount: number
}



// ==================== API接口 ====================

/**
 * 获取分类列表
 * @param scode 商城编码
 * @param level 分类层级 1-一级分类 2-二级分类 3-三级分类
 * @param parentId 父分类ID（获取子分类时必传）
 */
export function getCategoryList(params: {
  scode: string
  level: number
  parentId?: number
}): Promise<ApiResponse<CategoryDto[]>> {
  return get('/mallInfo/category/list', params)
}

 