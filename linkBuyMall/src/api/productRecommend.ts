import { get } from '@/utils/request'
import type { ApiResponse } from '@/types'
import type { ProductItem } from './product'

// 推荐商品分页响应类型
export interface ProductRecommendPageResponse {
  list: ProductItem[]
  page: number
  pageSize: number
  hasNext: boolean
}

// 获取商品的推荐商品API（支持指定数量）
export const getRecommendProductsForSpu = (params: {
    scode: string
    productId: number
    limit?: number // 返回数量上限，默认12个
}): Promise<ApiResponse<{ list: ProductItem[] }>> => {
    return get('/mallInfo/recommendsForSpu', params)
}

// 获取商城推荐商品API（支持分页）
export const getRecommendProductsForMall = (params: {
    scode: string
    productId: number
    page?: number    // 页码，默认1
    pageSize?: number // 每页数量，默认6个
}): Promise<ApiResponse<ProductRecommendPageResponse>> => {
    return get('/mallInfo/recommendsForMall', params)
}