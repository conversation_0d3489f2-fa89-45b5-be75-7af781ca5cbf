import { get, post, put, del, upload } from '@/utils/request'
import type {
  User,
  LoginParams,
  RegisterParams,
  WechatLoginRequest,
  SmsCodeRequest,
  Address,
  Product,
  Category,
  CartItem,
  Order,
  SearchParams,
  Banner,
  Notice,
  ApiResponse,
  PageData
} from '@/types'

// ==================== 用户相关API ====================

// 统一登录接口
export function login(data: LoginParams): Promise<ApiResponse<{ token: string; user: User }>> {
  return post('/user/login', data)
}

// 用户注册
export function register(data: RegisterParams): Promise<ApiResponse<User>> {
  return post('/user/register', data)
}

// 微信登录
export function wechatLogin(data: WechatLoginRequest): Promise<ApiResponse<{ token: string; user: User }>> {
  return post('/user/wechat-login', data)
}

// 发送短信验证码
export function sendSmsCode(data: SmsCodeRequest): Promise<ApiResponse<null>> {
  return post('/user/sms-code', data)
}

// 获取用户信息
export function getUserInfo(): Promise<ApiResponse<User>> {
  return get('/user/info')
}

// 更新用户信息
export function updateUserInfo(data: Partial<User>): Promise<ApiResponse<User>> {
  return put('/user/info', data)
}

// 修改密码
export function changePassword(data: { oldPassword: string; newPassword: string }): Promise<ApiResponse<null>> {
  return post('/user/change-password', data)
}

// 用户退出登录
export function logout(): Promise<ApiResponse<null>> {
  return post('/user/logout')
}

// ==================== 地址管理API ====================

// 获取地址列表
export function getAddressList(): Promise<ApiResponse<Address[]>> {
  return get('/user/address')
}

// 添加地址
export function addAddress(data: Omit<Address, 'id' | 'userId' | 'createTime'>): Promise<ApiResponse<Address>> {
  return post('/user/address', data)
}

// 更新地址
export function updateAddress(id: number, data: Partial<Address>): Promise<ApiResponse<Address>> {
  return put(`/user/address/${id}`, data)
}

// 删除地址
export function deleteAddress(id: number): Promise<ApiResponse<null>> {
  return del(`/user/address/${id}`)
}

// 设置默认地址
export function setDefaultAddress(id: number): Promise<ApiResponse<null>> {
  return post(`/user/address/${id}/default`)
}

// ==================== 商品相关API ====================

// 获取商品列表
export function getProductList(params: SearchParams): Promise<ApiResponse<PageData<Product>>> {
  return get('/product/list', params)
}

// 获取商品详情
export function getProductDetail(id: number): Promise<ApiResponse<Product>> {
  return get(`/product/${id}`)
}

// 搜索商品
export function searchProducts(params: SearchParams): Promise<ApiResponse<PageData<Product>>> {
  return get('/product/search', params)
}

// 获取商品分类
export function getCategoryList(): Promise<ApiResponse<Category[]>> {
  return get('/category/list')
}

// 获取分类商品
export function getCategoryProducts(categoryId: number, params?: SearchParams): Promise<ApiResponse<PageData<Product>>> {
  return get(`/category/${categoryId}/products`, params)
}

// ==================== 购物车相关API ====================

// 获取购物车列表
export function getCartList(): Promise<ApiResponse<CartItem[]>> {
  return get('/cart/list')
}

// 添加到购物车
export function addToCart(data: { productId: number; quantity: number; specs?: string }): Promise<ApiResponse<CartItem>> {
  return post('/cart/add', data)
}

// 更新购物车商品数量
export function updateCartQuantity(id: number, quantity: number): Promise<ApiResponse<CartItem>> {
  return put(`/cart/${id}`, { quantity })
}

// 删除购物车商品
export function removeFromCart(id: number): Promise<ApiResponse<null>> {
  return del(`/cart/${id}`)
}

// 清空购物车
export function clearCart(): Promise<ApiResponse<null>> {
  return del('/cart/clear')
}

// 批量删除购物车商品
export function batchRemoveFromCart(ids: number[]): Promise<ApiResponse<null>> {
  return post('/cart/batch-remove', { ids })
}

// ==================== 订单相关API ====================

// 创建订单
export function createOrder(data: {
  addressId: number
  items: { productId: number; quantity: number; specs?: string }[]
  remark?: string
}): Promise<ApiResponse<Order>> {
  return post('/order/create', data)
}

// 获取订单列表
export function getOrderList(params: { status?: number; page?: number; pageSize?: number }): Promise<ApiResponse<PageData<Order>>> {
  return get('/order/list', params)
}

// 获取订单详情
export function getOrderDetail(id: number): Promise<ApiResponse<Order>> {
  return get(`/order/${id}`)
}

// 取消订单
export function cancelOrder(id: number, reason?: string): Promise<ApiResponse<null>> {
  return post(`/order/${id}/cancel`, { reason })
}

// 确认收货
export function confirmOrder(id: number): Promise<ApiResponse<null>> {
  return post(`/order/${id}/confirm`)
}

// 申请退款
export function refundOrder(id: number, reason: string): Promise<ApiResponse<null>> {
  return post(`/order/${id}/refund`, { reason })
}

// 订单支付
export function payOrder(id: number, payType: number): Promise<ApiResponse<{ payInfo: any }>> {
  return post(`/order/${id}/pay`, { payType })
}

// ==================== 首页相关API ====================

// 获取轮播图
export function getBannerList(): Promise<ApiResponse<Banner[]>> {
  return get('/home/<USER>')
}

// 获取公告列表
export function getNoticeList(): Promise<ApiResponse<Notice[]>> {
  return get('/home/<USER>')
}

// 获取推荐商品
export function getRecommendProducts(): Promise<ApiResponse<Product[]>> {
  return get('/home/<USER>')
}

// 获取热门商品
export function getHotProducts(): Promise<ApiResponse<Product[]>> {
  return get('/home/<USER>')
}

// ==================== 文件上传API ====================

// 上传图片
export function uploadImage(filePath: string): Promise<ApiResponse<{ url: string }>> {
  return upload('/upload/image', filePath, 'image')
}

// 上传头像
export function uploadAvatar(filePath: string): Promise<ApiResponse<{ url: string }>> {
  return upload('/upload/avatar', filePath)
}

// ==================== 页面配置相关API ====================

// 获取页面配置数据
export function getPageConfig(pageId?: number): Promise<ApiResponse<{
  id: number
  content: Array<{
    title: string
    name: string
    show: number
    content: any
    styles: any
  }>
  common: {
    title: string
    background_type: string
    bg_color: string
    background_image: string
    background_color: string
    showTabbar: string
  }
}>> {
  const params = pageId ? { id: pageId } : {}
  return get('/page/config', params)
} 