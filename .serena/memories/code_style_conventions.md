# LinkBuy 代码规范和约定

## Java后端代码规范

### 命名约定
- **类名**: 使用PascalCase（大驼峰），如`UserService`、`OrderController`
- **方法名**: 使用camelCase（小驼峰），如`getUserInfo()`、`createOrder()`
- **变量名**: 使用camelCase，如`userId`、`orderList`
- **常量**: 使用UPPER_SNAKE_CASE，如`MAX_RETRY_COUNT`
- **包名**: 使用小写，如`com.linkBuy.mallApi`

### 数据库规范
- **表名**: 使用下划线命名，如`biz_user`、`sys_config`
- **字段名**: 使用下划线命名，如`user_id`、`create_time`
- **必备字段**: 每个表都应有`create_time`、`update_time`、`deleted`字段
- **软删除**: 使用`deleted`字段，MyBatis全局配置已自动过滤逻辑删除记录

### 代码结构
- **Controller**: 只处理HTTP请求响应，不包含业务逻辑
- **Service**: 包含业务逻辑，使用`@Service`注解
- **Entity**: 数据库实体类，使用MyBatis-Plus注解
- **DTO**: 数据传输对象，用于API接口
- **Enum**: 枚举类统一放在common模块

### 异常处理
- 使用统一异常处理机制
- 自定义业务异常继承RuntimeException
- 返回统一的响应格式

## 前端代码规范

### Vue3项目规范
- **组件名**: 使用PascalCase，如`UserProfile.vue`
- **文件名**: 组件文件使用PascalCase，其他文件使用kebab-case
- **变量名**: 使用camelCase
- **常量**: 使用UPPER_SNAKE_CASE

### TypeScript规范
- 所有变量和函数都应有明确的类型声明
- 使用interface定义对象类型
- 避免使用any类型
- 使用泛型提高代码复用性

### 样式规范
- 使用SCSS预处理器
- 遵循BEM命名规范
- 组件样式使用scoped
- 公共样式放在全局样式文件中

### API调用规范
- 所有API调用必须封装在`@/api`目录下
- 页面组件不得直接进行网络请求
- 使用统一的请求拦截器处理认证和错误

## 项目结构约定

### 后端模块划分
- **component**: 核心组件，可被多个服务复用
- **api服务**: 独立的API服务，各自负责特定业务领域
- **internal-service**: 内部服务，处理事件监听和后台任务

### 前端项目结构
```
src/
├── api/          # API接口封装
├── components/   # 可复用组件
├── views/        # 页面组件
├── store/        # 状态管理
├── router/       # 路由配置
├── utils/        # 工具函数
├── types/        # TypeScript类型定义
└── assets/       # 静态资源
```

## 开发约定

### Git提交规范
- **feat**: 新功能
- **fix**: 修复bug
- **docs**: 文档更新
- **style**: 代码格式调整
- **refactor**: 代码重构
- **test**: 测试相关
- **chore**: 构建过程或辅助工具的变动

### 分支管理
- **main**: 主分支，用于生产环境
- **develop**: 开发分支
- **feature/***: 功能分支
- **hotfix/***: 热修复分支

### 代码审查
- 所有代码变更都应通过Pull Request
- 至少需要一人审查后才能合并
- 确保所有测试通过后才能合并

## 性能约定
- 前端组件使用懒加载
- 数据库查询避免N+1问题
- 合理使用Redis缓存
- API响应数据进行分页处理