package com.linkBuy.mallAdminApi.controller;

import com.linkBuy.common.annotation.ResponseWrapper;
import com.linkBuy.common.constants.SecurityConstants;
import com.linkBuy.common.dto.SessionUser;
import com.linkBuy.common.exception.AuthenticationException;
import com.linkBuy.common.exception.BizException;
import com.linkBuy.mysql.dao.entity.biz.*;
import com.linkBuy.mysql.dao.service.biz.*;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/decoration")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@ResponseWrapper
public class MallDecorationController {

    private final BizCompanyMallService mallService;
    private final BizMallSplashAdsService splashAdsService;
    private final BizMallSplashConfigService splashConfigService;
    private final BizMallBottomNavService bottomNavService;
    private final BizMallNavConfigService navConfigService;
    private final BizMallMiniprogramPageService miniprogramPageService;
    private final BizMallCategoryDecorationService categoryDecorationService;
    private final BizMallCategoryAdsService categoryAdsService;
    private final BizMallCategoryService mallCategoryService;

    /**
     * 获取当前用户的商城信息
     */
    private BizCompanyMall getCurrentMall(HttpServletRequest request, Long mallId) {
        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null || sessionUser.getCompanyId() == null) {
            throw new AuthenticationException("未登录或客户信息缺失");
        }

        BizCompanyMall mall = mallService.getById(mallId);
        if (mall == null) {
            throw new BizException("商城不存在");
        }

        if (!mall.getCompanyId().equals(sessionUser.getCompanyId())) {
            throw new AuthenticationException("无权访问该商城");
        }

        return mall;
    }

    /**
     * 构建分类树形结构
     */
    private List<BizMallCategory> buildCategoryTree(List<BizMallCategory> flatCategories) {
        if (flatCategories == null || flatCategories.isEmpty()) {
            return new ArrayList<>();
        }

        // 创建ID到分类的映射
        Map<Long, BizMallCategory> categoryMap = new HashMap<>();
        for (BizMallCategory category : flatCategories) {
            categoryMap.put(category.getId(), category);
            // 初始化children列表
            category.setChildren(new ArrayList<>());
        }

        // 构建父子关系
        List<BizMallCategory> rootCategories = new ArrayList<>();
        for (BizMallCategory category : flatCategories) {
            if (category.getParentId() == null) {
                // 根分类
                rootCategories.add(category);
            } else {
                // 子分类，添加到父分类的children中
                BizMallCategory parent = categoryMap.get(category.getParentId());
                if (parent != null) {
                    parent.getChildren().add(category);
                }
            }
        }

        return rootCategories;
    }

    // ==================== 开屏广告相关接口 ====================

    /**
     * 获取开屏广告配置
     */
    @GetMapping("/splash/config/{mallId}")
    @PreAuthorize("isAuthenticated()")
    public SplashConfigResponse getSplashConfig(@PathVariable Long mallId, HttpServletRequest request) {
        getCurrentMall(request, mallId);

        BizMallSplashConfig config = splashConfigService.getByMallId(mallId);
        if (config == null) {
            config = splashConfigService.getDefaultConfig(mallId);
        }

        List<BizMallSplashAds> adsList = splashAdsService.getAllByMallId(mallId);

        SplashConfigResponse response = new SplashConfigResponse();
        response.setConfig(config);
        response.setAdsList(adsList);
        return response;
    }

    /**
     * 保存开屏广告配置
     */
    @PostMapping("/splash/config/{mallId}")
    @PreAuthorize("isAuthenticated()")
    @Transactional
    public void saveSplashConfig(@PathVariable Long mallId,
                                 @Valid @RequestBody SplashConfigRequest request,
                                 HttpServletRequest httpRequest) {
        getCurrentMall(httpRequest, mallId);

        // 业务逻辑校验
        if (request.getAutoClose() && (request.getDuration() == null || request.getDuration() <= 0 || request.getDuration() > 30)) {
            throw new BizException("开启自动关闭时，显示时长必须在1-30秒之间");
        }

        // 保存配置
        BizMallSplashConfig config = new BizMallSplashConfig();
        config.setMallId(mallId);
        config.setEnabled(request.getEnabled());
        config.setAutoClose(request.getAutoClose());
        config.setDuration(request.getDuration());
        config.setIndicatorStyle(request.getIndicatorStyle());

        splashConfigService.saveOrUpdateConfig(config);

        // 删除原有广告
        splashAdsService.deleteByMallId(mallId);

        // 保存新广告
        if (request.getAdsList() != null && !request.getAdsList().isEmpty()) {
            for (int i = 0; i < request.getAdsList().size(); i++) {
                SplashAdsRequest adsRequest = request.getAdsList().get(i);
                BizMallSplashAds ads = new BizMallSplashAds();
                ads.setMallId(mallId);
                ads.setTitle(adsRequest.getTitle());
                ads.setImageUrl(adsRequest.getImageUrl());
                ads.setLinkUrl(adsRequest.getLinkUrl());
                ads.setLinkType(adsRequest.getLinkType());
                ads.setSortOrder(i);
                ads.setStatus(1);
                splashAdsService.save(ads);
            }
        }
    }

    // ==================== 底部导航相关接口 ====================

    /**
     * 获取底部导航配置
     */
    @GetMapping("/navigation/config/{mallId}")
    @PreAuthorize("isAuthenticated()")
    public NavigationConfigResponse getNavigationConfig(@PathVariable Long mallId, HttpServletRequest request) {
        getCurrentMall(request, mallId);

        BizMallNavConfig config = navConfigService.getByMallId(mallId);
        if (config == null) {
            config = navConfigService.getDefaultConfig(mallId);
        }

        List<BizMallBottomNav> navList = bottomNavService.getAllByMallId(mallId);

        NavigationConfigResponse response = new NavigationConfigResponse();
        response.setConfig(config);
        response.setNavList(navList);
        return response;
    }

    /**
     * 保存底部导航配置
     */
    @PostMapping("/navigation/config/{mallId}")
    @PreAuthorize("isAuthenticated()")
    @Transactional
    public void saveNavigationConfig(@PathVariable Long mallId,
                                     @Valid @RequestBody NavigationConfigRequest request,
                                     HttpServletRequest httpRequest) {
        getCurrentMall(httpRequest, mallId);

        // 保存样式配置
        BizMallNavConfig config = new BizMallNavConfig();
        config.setMallId(mallId);
        config.setEnabled(request.getEnabled());
        config.setDisplayType(request.getDisplayType());
        config.setNavHeight(request.getNavHeight());
        config.setFontSize(request.getFontSize());
        config.setFontWeight(request.getFontWeight());
        config.setTextMarginTop(request.getTextMarginTop());
        config.setIconSize(request.getIconSize());
        config.setBackgroundColor(request.getBackgroundColor());

        navConfigService.saveOrUpdateConfig(config);

        // 删除原有导航
        bottomNavService.deleteByMallId(mallId);

        // 保存新导航
        if (request.getNavList() != null && !request.getNavList().isEmpty()) {
            for (int i = 0; i < request.getNavList().size(); i++) {
                NavigationItemRequest navRequest = request.getNavList().get(i);
                BizMallBottomNav nav = new BizMallBottomNav();
                nav.setMallId(mallId);
                nav.setNavText(navRequest.getNavText());
                nav.setIconUrl(navRequest.getIconUrl());
                nav.setActiveIconUrl(navRequest.getActiveIconUrl());
                nav.setLinkUrl(navRequest.getLinkUrl());
                nav.setLinkType(navRequest.getLinkType());
                nav.setSortOrder(i);
                nav.setStatus(1);
                bottomNavService.save(nav);
            }
        }
    }

    // ==================== 主题相关接口 ====================

    /**
     * 保存商城主题
     */
    @PostMapping("/theme/{mallId}")
    @PreAuthorize("isAuthenticated()")
    @Transactional
    public void saveTheme(@PathVariable Long mallId,
                          @Valid @RequestBody ThemeRequest request,
                          HttpServletRequest httpRequest) {
        BizCompanyMall mall = getCurrentMall(httpRequest, mallId);

        // 更新商城主题
        mall.setTheme(request.getTheme());
        mallService.updateById(mall);

        log.info("商城主题保存成功，mallId: {}, theme: {}", mallId, request.getTheme());
    }

    // ==================== 首页相关接口 ====================

    /**
     * 获取商城首页信息
     */
    @GetMapping("/homepage/{mallId}")
    @PreAuthorize("isAuthenticated()")
    public HomePageResponse getHomePage(@PathVariable Long mallId, HttpServletRequest request) {
        getCurrentMall(request, mallId);

        BizMallMiniprogramPage homePage = miniprogramPageService.getHomePage(mallId);

        HomePageResponse response = new HomePageResponse();
        if (homePage != null) {
            response.setPageId(homePage.getId());
            response.setPageName(homePage.getPageName());
            response.setPageTitle(homePage.getPageTitle());
            response.setPageDescription(homePage.getPageDescription());
            response.setBackgroundColor(homePage.getBackgroundColor());
            response.setPageConfig(homePage.getPageConfig());
            response.setStatus(homePage.getStatus());
            response.setUpdateTime(homePage.getUpdateTime());
        }

        return response;
    }

    /**
     * 设置指定页面为首页
     */
    @PostMapping("/homepage/{mallId}/set")
    @PreAuthorize("isAuthenticated()")
    @Transactional
    public void setHomePage(@PathVariable Long mallId,
                            @Valid @RequestBody SetHomePageRequest request,
                            HttpServletRequest httpRequest) {
        getCurrentMall(httpRequest, mallId);

        // 检查页面是否存在且属于该商城
        BizMallMiniprogramPage page = miniprogramPageService.getById(request.getPageId());
        if (page == null) {
            throw new BizException("页面不存在");
        }

        if (!page.getMallId().equals(mallId)) {
            throw new BizException("页面不属于该商城");
        }

        if (page.getStatus() != 1) {
            throw new BizException("页面未启用，无法设为首页");
        }

        // 设置为首页
        boolean success = miniprogramPageService.setAsHomePage(mallId, request.getPageId());
        if (!success) {
            throw new BizException("设置首页失败");
        }

        log.info("设置首页成功，mallId: {}, pageId: {}", mallId, request.getPageId());
    }

    /**
     * 创建默认首页
     */
    @PostMapping("/homepage/{mallId}/create")
    @PreAuthorize("isAuthenticated()")
    @Transactional
    public HomePageResponse createHomePage(@PathVariable Long mallId,
                                           @Valid @RequestBody CreateHomePageRequest request,
                                           HttpServletRequest httpRequest) {
        getCurrentMall(httpRequest, mallId);

        // 检查是否已有首页
        BizMallMiniprogramPage existingHomePage = miniprogramPageService.getHomePage(mallId);
        if (existingHomePage != null) {
            throw new BizException("商城已有首页，无需重复创建");
        }

        // 创建首页
        BizMallMiniprogramPage homePage = new BizMallMiniprogramPage();
        homePage.setMallId(mallId);
        homePage.setPageName(request.getPageName() != null ? request.getPageName() : "商城首页");
        homePage.setPageCode("home");
        homePage.setPageTitle(request.getPageTitle() != null ? request.getPageTitle() : "商城首页");
        homePage.setPageDescription(request.getPageDescription() != null ? request.getPageDescription() : "商城首页模板");
        homePage.setBackgroundColor("#f5f5f5");
        homePage.setPageConfig("{}");
        homePage.setIsHomePage(1);
        homePage.setStatus(1);
        homePage.setSortOrder(miniprogramPageService.getNextSortOrder(mallId));

        boolean success = miniprogramPageService.save(homePage);
        if (!success) {
            throw new BizException("创建首页失败");
        }

        log.info("创建首页成功，mallId: {}, pageId: {}", mallId, homePage.getId());

        // 返回创建的首页信息
        HomePageResponse response = new HomePageResponse();
        response.setPageId(homePage.getId());
        response.setPageName(homePage.getPageName());
        response.setPageTitle(homePage.getPageTitle());
        response.setPageDescription(homePage.getPageDescription());
        response.setBackgroundColor(homePage.getBackgroundColor());
        response.setPageConfig(homePage.getPageConfig());
        response.setStatus(homePage.getStatus());
        response.setUpdateTime(homePage.getUpdateTime());

        return response;
    }

    // ==================== 分类页装修相关接口 ====================

    /**
     * 获取分类页装修配置
     */
    @GetMapping("/category/config/{mallId}")
    @PreAuthorize("isAuthenticated()")
    public CategoryDecorationConfigResponse getCategoryDecorationConfig(@PathVariable Long mallId, HttpServletRequest request) {
        getCurrentMall(request, mallId);

        // 获取主配置
        BizMallCategoryDecoration config = categoryDecorationService.getByMallId(mallId);
        if (config == null) {
            config = categoryDecorationService.getDefaultConfig(mallId);
        }

        // 获取所有广告配置
        List<BizMallCategoryAds> allAds = categoryAdsService.getAllByMallId(mallId);

        CategoryDecorationConfigResponse response = new CategoryDecorationConfigResponse();
        response.setConfig(config);
        response.setAllAds(allAds);

        // 获取自定义分类并构建层级结构
        // 是有分类类型为自定义时，才读取
        if (!"standard".equals(config.getCategoryType())) {
            List<BizMallCategory> flatCategories = mallCategoryService.getByMallId(mallId);
            List<BizMallCategory> customCategories = buildCategoryTree(flatCategories);
            response.setCustomCategories(customCategories);
        }
        return response;
    }

    /**
     * 保存分类页装修配置
     */
    @PostMapping("/category/config/{mallId}")
    @Transactional
    public void saveCategoryDecorationConfig(@PathVariable Long mallId,
                                             @Valid @RequestBody CategoryDecorationConfigRequest request,
                                             HttpServletRequest httpRequest) {
        getCurrentMall(httpRequest, mallId);

        // 保存主配置
        BizMallCategoryDecoration config = new BizMallCategoryDecoration();
        config.setMallId(mallId);
        config.setCategoryType(request.getCategoryType());
        config.setCategoryLevel(request.getCategoryLevel());
        config.setStyleType(request.getStyleType());
        config.setShowSearch(request.getShowSearch());
        config.setSearchConfig(request.getSearchConfig());
        config.setSearchWidgetData(request.getSearchWidgetData());
        config.setStatus(1);

        categoryDecorationService.saveOrUpdateConfig(config);

        // 删除原有广告配置
        categoryAdsService.deleteByMallId(mallId);

        // 保存新的广告配置
        if (request.getAdsConfig() != null) {
            // 保存全局广告
            if (request.getAdsConfig().getGlobal() != null && !request.getAdsConfig().getGlobal().isEmpty()) {
                for (int i = 0; i < request.getAdsConfig().getGlobal().size(); i++) {
                    CategoryAdsRequest adsRequest = request.getAdsConfig().getGlobal().get(i);
                    BizMallCategoryAds ads = new BizMallCategoryAds();
                    ads.setMallId(mallId);
                    ads.setCategoryId(null);
                    ads.setCategoryType("global");
                    ads.setImageUrl(adsRequest.getImageUrl());
                    ads.setLinkUrl(adsRequest.getLinkUrl());
                    ads.setLinkText(adsRequest.getLinkText());
                    ads.setLinkType(adsRequest.getLinkType());
                    ads.setSortOrder(i);
                    ads.setStatus(1);
                    categoryAdsService.save(ads);
                }
            }

            // 保存分类专属广告
            if (request.getAdsConfig().getCategories() != null) {
                for (Map.Entry<String, List<CategoryAdsRequest>> entry : request.getAdsConfig().getCategories().entrySet()) {
                    String categoryKey = entry.getKey();
                    List<CategoryAdsRequest> categoryAdsList = entry.getValue();

                    if (categoryAdsList != null && !categoryAdsList.isEmpty()) {
                        for (int i = 0; i < categoryAdsList.size(); i++) {
                            CategoryAdsRequest adsRequest = categoryAdsList.get(i);
                            BizMallCategoryAds ads = new BizMallCategoryAds();
                            ads.setMallId(mallId);

                            // 解析categoryKey，可能是数字ID或者字符串标识
                            try {
                                ads.setCategoryId(Long.parseLong(categoryKey));
                                ads.setCategoryType("custom"); // 默认为自定义分类
                            } catch (NumberFormatException e) {
                                ads.setCategoryId(null);
                                ads.setCategoryType(categoryKey);
                            }

                            ads.setImageUrl(adsRequest.getImageUrl());
                            ads.setLinkUrl(adsRequest.getLinkUrl());
                            ads.setLinkText(adsRequest.getLinkText());
                            ads.setLinkType(adsRequest.getLinkType());
                            ads.setSortOrder(i);
                            ads.setStatus(1);
                            categoryAdsService.save(ads);
                        }
                    }
                }
            }
        }

        log.info("分类页装修配置保存成功，mallId: {}", mallId);
    }

    // ==================== 请求和响应DTO ====================

    @Data
    public static class SplashConfigRequest {
        @NotNull(message = "启用状态不能为空")
        private Boolean enabled;

        @NotNull(message = "自动关闭设置不能为空")
        private Boolean autoClose;

        private Integer duration;

        @NotBlank(message = "指示器样式不能为空")
        private String indicatorStyle;

        @Size(max = 5, message = "最多支持5个广告")
        private List<SplashAdsRequest> adsList;
    }

    @Data
    public static class SplashAdsRequest {
        @NotBlank(message = "广告标题不能为空")
        @Size(max = 100, message = "广告标题不能超过100个字符")
        private String title;

        @NotBlank(message = "广告图片不能为空")
        private String imageUrl;

        private String linkUrl;

        private String linkType = "none";
    }

    @Data
    public static class SplashConfigResponse {
        private BizMallSplashConfig config;
        private List<BizMallSplashAds> adsList;
    }

    @Data
    public static class NavigationConfigRequest {
        @NotNull(message = "启用状态不能为空")
        private Boolean enabled;

        @NotBlank(message = "显示类型不能为空")
        private String displayType;

        @NotNull(message = "导航高度不能为空")
        private Integer navHeight;

        @NotNull(message = "字体大小不能为空")
        private Integer fontSize;

        @NotNull(message = "字体粗细不能为空")
        private Integer fontWeight;

        @NotNull(message = "文字上边距不能为空")
        private Integer textMarginTop;

        @NotNull(message = "图标大小不能为空")
        private Integer iconSize;

        @NotBlank(message = "背景颜色不能为空")
        private String backgroundColor;

        @Size(min = 2, max = 5, message = "导航项数量必须在2-5个之间")
        private List<NavigationItemRequest> navList;
    }

    @Data
    public static class NavigationItemRequest {
        @NotBlank(message = "导航文字不能为空")
        @Size(max = 4, message = "导航文字不能超过4个字符")
        private String navText;

        private String iconUrl;

        private String activeIconUrl;

        private String linkUrl;

        private String linkType = "page";
    }

    @Data
    public static class NavigationConfigResponse {
        private BizMallNavConfig config;
        private List<BizMallBottomNav> navList;
    }

    @Data
    public static class ThemeRequest {
        @NotBlank(message = "主题不能为空")
        @Size(max = 50, message = "主题名称不能超过50个字符")
        private String theme;
    }

    @Data
    public static class HomePageResponse {
        private Long pageId;
        private String pageName;
        private String pageTitle;
        private String pageDescription;
        private String backgroundColor;
        private String pageConfig;
        private Integer status;
        private LocalDateTime updateTime;
    }

    @Data
    public static class SetHomePageRequest {
        @NotNull(message = "页面ID不能为空")
        private Long pageId;
    }

    @Data
    public static class CreateHomePageRequest {
        private String pageName;
        private String pageTitle;
        private String pageDescription;
    }

    // ==================== 分类页装修相关DTO ====================

    @Data
    public static class CategoryDecorationConfigRequest {
        @NotBlank(message = "分类类型不能为空")
        private String categoryType;

        @NotNull(message = "分类级别不能为空")
        private Integer categoryLevel;

        @NotNull(message = "风格类型不能为空")
        private Integer styleType;

        @NotNull(message = "搜索组件显示状态不能为空")
        private Boolean showSearch;

        private String searchConfig;

        private String searchWidgetData;

        private CategoryAdsConfigRequest adsConfig;
    }

    @Data
    public static class CategoryAdsConfigRequest {
        private List<CategoryAdsRequest> global;
        private Map<String, List<CategoryAdsRequest>> categories;
    }

    @Data
    public static class CategoryAdsRequest {
        @NotBlank(message = "广告图片不能为空")
        private String imageUrl;

        private String linkUrl;

        private String linkText;

        private String linkType = "none";
    }

    @Data
    public static class CategoryDecorationConfigResponse {
        private BizMallCategoryDecoration config;
        private List<BizMallCategoryAds> allAds;
        private List<BizMallCategory> customCategories;
    }
}
