package com.linkBuy.mallAdminApi.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linkBuy.common.annotation.ResponseWrapper;
import com.linkBuy.common.constants.SecurityConstants;
import com.linkBuy.common.dto.SessionUser;
import com.linkBuy.common.exception.ProductBusinessException;
import com.linkBuy.common.service.ProductBusinessService;
import com.linkBuy.common.service.ProductOperationLogger;
import com.linkBuy.mallAdminApi.dto.request.ProductReviewRequest;
import com.linkBuy.mallAdminApi.dto.response.ProductReviewResponse;
import com.linkBuy.mysql.dao.entity.biz.*;
import com.linkBuy.mysql.dao.service.biz.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户商品审核控制器
 * 专注于处理供应商提报的商品审核
 */
@Slf4j
@RestController
@RequestMapping("/company-product-review")
@RequiredArgsConstructor
@ResponseWrapper
public class CompanyProductReviewController {

    @Autowired
    private BizSupplierSpuService bizSupplierSpuService;

    @Autowired
    private BizSupplierSkuService bizSupplierSkuService;

    @Autowired
    private BizSupplierService bizSupplierService;

    @Autowired
    private BizCompanySpuService bizCompanySpuService;

    @Autowired
    private BizCompanySkuService bizCompanySkuService;

    @Autowired
    private BizSupplierSpuImgService bizSupplierSpuImgService;

    @Autowired
    private ProductBusinessService productBusinessService;

    @Autowired
    private ProductOperationLogger productOperationLogger;

    /**
     * 获取待审核商品列表（只显示客户供应商的商品）
     */
    @GetMapping("/list")
    public IPage<ProductReviewResponse> getPendingReviewProductList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String productName,
            @RequestParam(required = false) Long supplierId,
            @RequestParam(required = false) Integer status,
            HttpServletRequest request) {

        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null) {
            throw new ProductBusinessException("用户未登录");
        }

        productOperationLogger.logProductOperation(null, "查询待审核商品列表", 
            sessionUser.getName(), "分页查询", request);

        // 首先获取当前客户公司的客户供应商ID列表
        List<Long> customerSupplierIds = getCustomerSupplierIds(sessionUser.getCompanyId());
        if (customerSupplierIds.isEmpty()) {
            return new Page<>();
        }

        Page<BizSupplierSpu> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<BizSupplierSpu> wrapper = new LambdaQueryWrapper<>();

        // 只查询客户供应商的商品
        wrapper.in(BizSupplierSpu::getSupplierId, customerSupplierIds);

        wrapper.eq(status != null, BizSupplierSpu::getStatus, status);

        // 商品名称搜索
        if (productName != null && !productName.trim().isEmpty()) {
            wrapper.and(w -> w.like(BizSupplierSpu::getName, productName.trim())
                    .or().like(BizSupplierSpu::getSpuCode, productName.trim()));
        }

        // 供应商筛选
        if (supplierId != null) {
            if (!customerSupplierIds.contains(supplierId)) {
                throw new ProductBusinessException("无权限查看该供应商的商品");
            }
            wrapper.eq(BizSupplierSpu::getSupplierId, supplierId);
        }

        wrapper.orderByDesc(BizSupplierSpu::getCreateTime);

        IPage<BizSupplierSpu> spuPage = bizSupplierSpuService.page(pageParam, wrapper);

        // 使用优化的批量转换方法，避免N+1查询问题
        List<ProductReviewResponse> responseList = convertToReviewResponseBatch(spuPage.getRecords());
        
        // 构建分页响应
        Page<ProductReviewResponse> responsePage = new Page<>(spuPage.getCurrent(), spuPage.getSize(), spuPage.getTotal());
        responsePage.setRecords(responseList);
        
        return responsePage;
    }

    /**
     * 获取待审核商品详情
     */
    @GetMapping("/{id}")
    public ProductReviewResponse getPendingReviewProductDetail(@PathVariable Long id, HttpServletRequest request) {
        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null) {
            throw new ProductBusinessException("用户未登录");
        }

        productOperationLogger.logProductOperation(id, "查询商品审核详情", 
            sessionUser.getName(), "详情查询", request);

        BizSupplierSpu spu = bizSupplierSpuService.getById(id);
        if (spu == null) {
            throw new ProductBusinessException("商品不存在");
        }

        // 验证权限：通过supplier信息验证
        BizSupplier supplier = bizSupplierService.getById(spu.getSupplierId());
        if (supplier == null) {
            throw new ProductBusinessException("供应商不存在");
        }

        if (!supplier.getSupplierType().equals(1) || !supplier.getCompanyId().equals(sessionUser.getCompanyId())) {
            throw new ProductBusinessException("无权限查看该商品");
        }

        return convertToReviewDetailResponse(spu);
    }

    /**
     * 审核商品
     */
    @PostMapping("/review/{id}")
    @Transactional
    public void reviewProduct(@PathVariable Long id,
                              @RequestBody ProductReviewRequest request,
                              HttpServletRequest httpRequest) {
        SessionUser sessionUser = (SessionUser) httpRequest.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null) {
            throw new ProductBusinessException("用户未登录");
        }

        productOperationLogger.logProductOperation(id, "审核商品", 
            sessionUser.getName(), "商品审核", httpRequest);

        BizSupplierSpu spu = bizSupplierSpuService.getById(id);
        if (spu == null) {
            throw new ProductBusinessException("商品不存在");
        }

        // 验证权限
        BizSupplier supplier = bizSupplierService.getById(spu.getSupplierId());
        if (supplier == null) {
            throw new ProductBusinessException("供应商不存在");
        }

        if (!supplier.getSupplierType().equals(1) || !supplier.getCompanyId().equals(sessionUser.getCompanyId())) {
            throw new ProductBusinessException("无权限审核该商品");
        }

        // 使用公共服务验证状态转换
        if (!productBusinessService.canTransitionStatus(spu.getStatus(), request.getReviewResult())) {
            throw new ProductBusinessException("商品状态不允许审核");
        }

        Integer reviewResult = request.getReviewResult();
        String reviewRemark = request.getReviewRemark();

        if (reviewResult == null || (!reviewResult.equals(1) && !reviewResult.equals(4))) {
            throw new ProductBusinessException("审核结果参数错误");
        }

        // 更新商品状态
        BizSupplierSpu updateSpu = new BizSupplierSpu();
        updateSpu.setId(id);
        updateSpu.setStatus(reviewResult);
        updateSpu.setReviewDescribe(reviewRemark);
        updateSpu.setUpdateTime(LocalDateTime.now());

        bizSupplierSpuService.updateById(updateSpu);

        // 如果审核通过，验证并创建客户商品
        if (reviewResult.equals(1)) {
            // 验证SKU价格信息
            validateSkuPrices(spu, request);
            // 创建客户商品并设置价格
            createCompanyProductWithPrices(spu, sessionUser.getCompanyId(), request.getSkuPriceList());
        }

        // 记录审核操作日志
        productOperationLogger.logAuditOperation(id, "商品审核", 
            reviewResult == 1 ? "通过" : "拒绝", sessionUser.getName(), reviewRemark);

        log.info("客户管理员审核商品成功，商品ID：{}，审核结果：{}，操作人：{}",
                id, reviewResult == 1 ? "通过" : "拒绝", sessionUser.getName());
    }

    /**
     * 批量审核商品
     */
    @PostMapping("/batch/review")
    @Transactional
    public void batchReviewProducts(@RequestBody ProductReviewRequest request, HttpServletRequest httpRequest) {
        SessionUser sessionUser = (SessionUser) httpRequest.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null) {
            throw new ProductBusinessException("用户未登录");
        }

        List<Long> ids = request.getIds();
        Integer reviewResult = request.getReviewResult();
        String reviewRemark = request.getReviewRemark();

        if (ids == null || ids.isEmpty()) {
            throw new ProductBusinessException("请选择要审核的商品");
        }

        if (reviewResult == null || (!reviewResult.equals(1) && !reviewResult.equals(4))) {
            throw new ProductBusinessException("审核结果参数错误");
        }

        productOperationLogger.logProductOperation(null, "批量审核商品", 
            sessionUser.getName(), "批量审核" + ids.size() + "个商品", httpRequest);

        // 验证所有商品的权限和状态
        List<BizSupplierSpu> spuList = bizSupplierSpuService.listByIds(ids);
        List<Long> customerSupplierIds = getCustomerSupplierIds(sessionUser.getCompanyId());
        
        // 优化：批量查询供应商信息，避免N+1问题
        List<Long> supplierIds = spuList.stream()
                .map(BizSupplierSpu::getSupplierId)
                .distinct()
                .collect(Collectors.toList());
        
        Map<Long, BizSupplier> supplierMap = new HashMap<>();
        if (!supplierIds.isEmpty()) {
            List<BizSupplier> supplierList = bizSupplierService.listByIds(supplierIds);
            supplierMap = supplierList.stream()
                    .collect(Collectors.toMap(BizSupplier::getId, supplier -> supplier));
        }

        for (BizSupplierSpu spu : spuList) {
            // 验证权限
            BizSupplier supplier = supplierMap.get(spu.getSupplierId());
            if (supplier == null || !customerSupplierIds.contains(spu.getSupplierId())) {
                throw new ProductBusinessException("包含无权限审核的商品：" + spu.getName());
            }

            // 验证状态
            if (!productBusinessService.canTransitionStatus(spu.getStatus(), reviewResult)) {
                throw new ProductBusinessException("商品状态不允许审核：" + spu.getName() + 
                    "，当前状态：" + spu.getStatus());
            }
        }

        // 批量更新商品状态
        for (Long id : ids) {
            BizSupplierSpu updateSpu = new BizSupplierSpu();
            updateSpu.setId(id);
            updateSpu.setStatus(reviewResult);
            updateSpu.setReviewDescribe(reviewRemark);
            updateSpu.setUpdateTime(LocalDateTime.now());

            bizSupplierSpuService.updateById(updateSpu);
        }

        // 如果审核通过，验证SKU价格信息并创建客户商品
        if (reviewResult.equals(1)) {
            // 检查是否提供了SKU价格信息
            List<ProductReviewRequest.SkuPriceInfo> skuPriceList = request.getSkuPriceList();
            if (skuPriceList == null || skuPriceList.isEmpty()) {
                throw new ProductBusinessException("批量审核通过时必须为所有商品的SKU设置销售价格");
            }

            // 按商品分组处理SKU价格
            Map<Long, List<ProductReviewRequest.SkuPriceInfo>> spuSkuPriceMap = skuPriceList.stream()
                    .collect(Collectors.groupingBy(ProductReviewRequest.SkuPriceInfo::getSpuId));
            
            // 验证每个商品都有SKU价格信息
            for (BizSupplierSpu spu : spuList) {
                List<ProductReviewRequest.SkuPriceInfo> spuSkuPrices = spuSkuPriceMap.get(spu.getId());
                if (spuSkuPrices == null || spuSkuPrices.isEmpty()) {
                    throw new ProductBusinessException("缺少商品的SKU价格信息：" + spu.getName());
                }
                // 验证该商品的SKU价格完整性和有效性
                validateSkuPricesForSpu(spu, spuSkuPrices);
            }

            // 使用提供的SKU价格信息创建客户商品
            for (BizSupplierSpu spu : spuList) {
                List<ProductReviewRequest.SkuPriceInfo> spuSkuPrices = spuSkuPriceMap.get(spu.getId());
                // 使用提供的价格创建客户商品
                createCompanyProductWithPrices(spu, sessionUser.getCompanyId(), spuSkuPrices);
            }
        }

        // 记录批量审核操作日志
        productOperationLogger.logAuditOperation(null, "批量商品审核", 
            reviewResult == 1 ? "批量通过" : "批量拒绝", sessionUser.getName(), 
            "批量审核" + ids.size() + "个商品：" + reviewRemark);

        log.info("客户管理员批量审核商品成功，商品数量：{}，审核结果：{}，操作人：{}",
                ids.size(), reviewResult == 1 ? "通过" : "拒绝", sessionUser.getName());
    }

    /**
     * 获取审核统计
     */
    @GetMapping("/statistics")
    public Map<String, Object> getReviewStatistics(HttpServletRequest request) {
        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null) {
            throw new ProductBusinessException("用户未登录");
        }

        productOperationLogger.logProductOperation(null, "查询审核统计", 
            sessionUser.getName(), "统计查询", request);

        Map<String, Object> statistics = new HashMap<>();

        // 获取当前客户公司的客户供应商ID列表
        List<Long> customerSupplierIds = getCustomerSupplierIds(sessionUser.getCompanyId());
        if (customerSupplierIds.isEmpty()) {
            statistics.put("pending", 0);
            statistics.put("approved", 0);
            statistics.put("rejected", 0);
            return statistics;
        }

        LambdaQueryWrapper<BizSupplierSpu> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(BizSupplierSpu::getSupplierId, customerSupplierIds);

        // 待审核（审核中）
        wrapper.eq(BizSupplierSpu::getStatus, 3);
        long pendingCount = bizSupplierSpuService.count(wrapper);
        statistics.put("pending", pendingCount);

        // 已通过
        wrapper.clear();
        wrapper.in(BizSupplierSpu::getSupplierId, customerSupplierIds);
        wrapper.eq(BizSupplierSpu::getStatus, 1);
        long approvedCount = bizSupplierSpuService.count(wrapper);
        statistics.put("approved", approvedCount);

        // 已拒绝
        wrapper.clear();
        wrapper.in(BizSupplierSpu::getSupplierId, customerSupplierIds);
        wrapper.eq(BizSupplierSpu::getStatus, 4);
        long rejectedCount = bizSupplierSpuService.count(wrapper);
        statistics.put("rejected", rejectedCount);

        return statistics;
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 获取当前客户公司的客户供应商ID列表
     */
    private List<Long> getCustomerSupplierIds(Long companyId) {
        LambdaQueryWrapper<BizSupplier> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizSupplier::getSupplierType, 1); // 客户供应商
        wrapper.eq(BizSupplier::getCompanyId, companyId); // 指定公司

        return bizSupplierService.list(wrapper).stream()
                .map(BizSupplier::getId)
                .collect(Collectors.toList());
    }

    /**
     * 验证单个商品的SKU价格信息
     */
    private void validateSkuPricesForSpu(BizSupplierSpu spu, List<ProductReviewRequest.SkuPriceInfo> skuPriceList) {
        // 获取供应商SKU列表
        List<BizSupplierSku> supplierSkuList = bizSupplierSkuService.list(
                new LambdaQueryWrapper<BizSupplierSku>()
                        .eq(BizSupplierSku::getSpuId, spu.getId())
        );

        if (supplierSkuList.isEmpty()) {
            throw new ProductBusinessException("商品没有SKU信息，无法审核通过");
        }

        // 验证所有SKU都有对应的价格信息
        List<Long> supplierSkuIds = supplierSkuList.stream()
                .map(BizSupplierSku::getId)
                .toList();

        List<Long> providedSkuIds = skuPriceList.stream()
                .map(ProductReviewRequest.SkuPriceInfo::getSkuId)
                .toList();

        // 检查是否所有SKU都提供了价格
        for (Long skuId : supplierSkuIds) {
            if (!providedSkuIds.contains(skuId)) {
                throw new ProductBusinessException("缺少SKU价格信息，商品：" + spu.getName() + "，SKU ID: " + skuId);
            }
        }

        // 检查提供的SKU ID是否都有效
        for (Long skuId : providedSkuIds) {
            if (!supplierSkuIds.contains(skuId)) {
                throw new ProductBusinessException("无效的SKU ID: " + skuId + "，商品：" + spu.getName());
            }
        }

        // 验证价格有效性
        for (ProductReviewRequest.SkuPriceInfo skuPrice : skuPriceList) {
            if (skuPrice.getSalePrice() == null || skuPrice.getSalePrice().compareTo(BigDecimal.ZERO) <= 0) {
                throw new ProductBusinessException("SKU销售价格必须大于0，商品：" + spu.getName() + "，SKU ID: " + skuPrice.getSkuId());
            }
        }
    }

    /**
     * 验证SKU价格信息（审核通过时必须提供所有SKU的价格）
     */
    private void validateSkuPrices(BizSupplierSpu spu, ProductReviewRequest request) {
        // 获取供应商SKU列表
        List<BizSupplierSku> supplierSkuList = bizSupplierSkuService.list(
                new LambdaQueryWrapper<BizSupplierSku>()
                        .eq(BizSupplierSku::getSpuId, spu.getId())
        );

        if (supplierSkuList.isEmpty()) {
            throw new ProductBusinessException("商品没有SKU信息，无法审核通过");
        }

        // 检查是否提供了SKU价格信息
        List<ProductReviewRequest.SkuPriceInfo> skuPriceList = request.getSkuPriceList();
        if (skuPriceList == null || skuPriceList.isEmpty()) {
            throw new ProductBusinessException("审核通过时必须为所有SKU设置销售价格");
        }

        // 验证所有SKU都有对应的价格信息
        List<Long> supplierSkuIds = supplierSkuList.stream()
                .map(BizSupplierSku::getId)
                .toList();

        List<Long> providedSkuIds = skuPriceList.stream()
                .map(ProductReviewRequest.SkuPriceInfo::getSkuId)
                .toList();

        // 检查是否所有SKU都提供了价格
        for (Long skuId : supplierSkuIds) {
            if (!providedSkuIds.contains(skuId)) {
                throw new ProductBusinessException("缺少SKU价格信息，SKU ID: " + skuId);
            }
        }

        // 检查提供的SKU ID是否都有效
        for (Long skuId : providedSkuIds) {
            if (!supplierSkuIds.contains(skuId)) {
                throw new ProductBusinessException("无效的SKU ID: " + skuId);
            }
        }

        // 验证价格有效性
        for (ProductReviewRequest.SkuPriceInfo skuPrice : skuPriceList) {
            if (skuPrice.getSalePrice() == null || skuPrice.getSalePrice().compareTo(BigDecimal.ZERO) <= 0) {
                throw new ProductBusinessException("SKU销售价格必须大于0，SKU ID: " + skuPrice.getSkuId());
            }
        }
    }

    /**
     * 创建客户商品并设置价格（审核通过后自动创建）
     */
    private void createCompanyProductWithPrices(BizSupplierSpu spu, Long companyId, List<ProductReviewRequest.SkuPriceInfo> skuPriceList) {
        // 检查是否已经存在客户商品
        LambdaQueryWrapper<BizCompanySpu> existWrapper = new LambdaQueryWrapper<>();
        existWrapper.eq(BizCompanySpu::getSpuId, spu.getId())
                .eq(BizCompanySpu::getCompanyId, companyId);

        BizCompanySpu existCompanySpu = bizCompanySpuService.getOne(existWrapper);
        if (existCompanySpu != null) {
            return;
        }

        // 获取供应商SKU列表
        List<BizSupplierSku> supplierSkuList = bizSupplierSkuService.list(
                new LambdaQueryWrapper<BizSupplierSku>()
                        .eq(BizSupplierSku::getSpuId, spu.getId())
        );

        if (supplierSkuList.isEmpty()) {
            log.warn("商品{}没有SKU信息，跳过创建客户商品", spu.getName());
            return;
        }

        // 创建价格映射
        Map<Long, BigDecimal> skuPriceMap = skuPriceList.stream()
                .collect(Collectors.toMap(
                        ProductReviewRequest.SkuPriceInfo::getSkuId,
                        ProductReviewRequest.SkuPriceInfo::getSalePrice
                ));

        // 创建客户SPU
        BizCompanySpu companySpu = new BizCompanySpu();
        companySpu.setSpuId(spu.getId());
        companySpu.setSpuCode(spu.getSpuCode());
        companySpu.setCompanyId(companyId);
        companySpu.setSupplierId(spu.getSupplierId());
        companySpu.setCategoryId(spu.getCategoryId());
        companySpu.setStatus(1); // 启用状态
        companySpu.setSource(2); // 自有供应商商品
        companySpu.setCreateTime(LocalDateTime.now());
        companySpu.setUpdateTime(LocalDateTime.now());

        bizCompanySpuService.save(companySpu);

        // 创建客户SKU，使用审核时设置的价格
        List<BizCompanySku> companySkuList = new ArrayList<>();
        for (BizSupplierSku supplierSku : supplierSkuList) {
            BizCompanySku companySku = new BizCompanySku();
            companySku.setCompanyId(companyId);
            companySku.setSpuId(spu.getId());
            companySku.setSkuId(supplierSku.getId());
            // 使用审核时设置的销售价格
            companySku.setSalePrice(skuPriceMap.get(supplierSku.getId()));
            companySku.setStatus(1); // 启用状态
            companySku.setSource(2); // 自有供应商商品
            companySku.setCreateTime(LocalDateTime.now());
            companySku.setUpdateTime(LocalDateTime.now());

            companySkuList.add(companySku);
        }

        bizCompanySkuService.saveBatch(companySkuList);

        // 更新客户SPU的价格范围
        updateCompanySpuPriceRange(companySpu, companySkuList);

        log.info("自动创建客户商品成功，SPU：{}，SKU数量：{}，使用审核设置的价格", spu.getName(), companySkuList.size());
    }

    /**
     * 更新客户SPU价格范围
     */
    private void updateCompanySpuPriceRange(BizCompanySpu companySpu, List<BizCompanySku> companySkuList) {
        if (!companySkuList.isEmpty()) {
            List<BigDecimal> salePrices = companySkuList.stream()
                    .map(BizCompanySku::getSalePrice)
                    .filter(Objects::nonNull)
                    .toList();

            if (!salePrices.isEmpty()) {
                BigDecimal minPrice = salePrices.stream()
                        .min(BigDecimal::compareTo)
                        .orElse(BigDecimal.ZERO);
                        
                BigDecimal maxPrice = salePrices.stream()
                        .max(BigDecimal::compareTo)
                        .orElse(BigDecimal.ZERO);
                
                companySpu.setSalePriceMin(minPrice);
                companySpu.setSalePriceMax(maxPrice);
                companySpu.setUpdateTime(LocalDateTime.now());
                bizCompanySpuService.updateById(companySpu);
            }
        }
    }

    /**
     * 转换为审核响应DTO
     */
    private ProductReviewResponse convertToReviewResponse(BizSupplierSpu spu) {
        ProductReviewResponse response = new ProductReviewResponse();
        BeanUtils.copyProperties(spu, response);

        // 获取供应商信息 - 注意：这里存在N+1问题，建议在分页查询时使用批量查询优化
        BizSupplier supplier = bizSupplierService.getById(spu.getSupplierId());
        if (supplier != null) {
            // 平台供应商统一显示为"平台直供"
            if (supplier.getSupplierType() == 2) {
                response.setSupplierName("平台直供");
            } else {
                response.setSupplierName(supplier.getName());
            }
            response.setSupplierType(supplier.getSupplierType());
        }

        // 获取SKU统计信息
        List<BizSupplierSku> skuList = bizSupplierSkuService.list(
                new LambdaQueryWrapper<BizSupplierSku>()
                        .eq(BizSupplierSku::getSpuId, spu.getId())
        );

        response.setSkuCount(skuList.size());

        // 计算价格范围
        if (!skuList.isEmpty()) {
            List<BigDecimal> costPrices = skuList.stream()
                    .map(BizSupplierSku::getCostPrice)
                    .filter(Objects::nonNull)
                    .toList();

            List<BigDecimal> purchasePrices = skuList.stream()
                    .map(BizSupplierSku::getPurchasePrice)
                    .filter(Objects::nonNull)
                    .toList();

            if (!costPrices.isEmpty()) {
                response.setCostPriceMin(costPrices.stream().min(BigDecimal::compareTo).orElse(BigDecimal.ZERO));
                response.setCostPriceMax(costPrices.stream().max(BigDecimal::compareTo).orElse(BigDecimal.ZERO));
            }

            if (!purchasePrices.isEmpty()) {
                response.setPurchasePriceMin(purchasePrices.stream().min(BigDecimal::compareTo).orElse(BigDecimal.ZERO));
                response.setPurchasePriceMax(purchasePrices.stream().max(BigDecimal::compareTo).orElse(BigDecimal.ZERO));
            }
        }

        return response;
    }

    /**
     * 批量转换为审核响应DTO - 优化版本，减少N+1查询
     */
    private List<ProductReviewResponse> convertToReviewResponseBatch(List<BizSupplierSpu> spuList) {
        if (spuList.isEmpty()) {
            return new ArrayList<>();
        }

        // 批量查询供应商信息
        List<Long> supplierIds = spuList.stream()
                .map(BizSupplierSpu::getSupplierId)
                .distinct()
                .collect(Collectors.toList());
        
        Map<Long, BizSupplier> supplierMap = new HashMap<>();
        if (!supplierIds.isEmpty()) {
            List<BizSupplier> supplierList = bizSupplierService.listByIds(supplierIds);
            supplierMap = supplierList.stream()
                    .collect(Collectors.toMap(BizSupplier::getId, supplier -> supplier));
        }

        // 批量查询SKU信息
        List<Long> spuIds = spuList.stream()
                .map(BizSupplierSpu::getId)
                .collect(Collectors.toList());
        
        Map<Long, List<BizSupplierSku>> spuSkuMap = new HashMap<>();
        if (!spuIds.isEmpty()) {
            List<BizSupplierSku> allSkuList = bizSupplierSkuService.list(
                    new LambdaQueryWrapper<BizSupplierSku>()
                            .in(BizSupplierSku::getSpuId, spuIds)
            );
            spuSkuMap = allSkuList.stream()
                    .collect(Collectors.groupingBy(BizSupplierSku::getSpuId));
        }

        // 使用预查询的数据转换响应
        Map<Long, BizSupplier> finalSupplierMap = supplierMap;
        Map<Long, List<BizSupplierSku>> finalSpuSkuMap = spuSkuMap;

        return spuList.stream()
                .map(spu -> convertToReviewResponseOptimized(spu, finalSupplierMap, finalSpuSkuMap))
                .collect(Collectors.toList());
    }

    /**
     * 转换为审核响应DTO - 优化版本，使用预查询的数据
     */
    private ProductReviewResponse convertToReviewResponseOptimized(
            BizSupplierSpu spu, 
            Map<Long, BizSupplier> supplierMap, 
            Map<Long, List<BizSupplierSku>> spuSkuMap) {
        ProductReviewResponse response = new ProductReviewResponse();
        BeanUtils.copyProperties(spu, response);

        // 从预查询的Map中获取供应商信息，避免N+1查询
        BizSupplier supplier = supplierMap.get(spu.getSupplierId());
        if (supplier != null) {
            // 平台供应商统一显示为"平台直供"
            if (supplier.getSupplierType() == 2) {
                response.setSupplierName("平台直供");
            } else {
                response.setSupplierName(supplier.getName());
            }
            response.setSupplierType(supplier.getSupplierType());
        }

        // 从预查询的Map中获取SKU统计信息
        List<BizSupplierSku> skuList = spuSkuMap.getOrDefault(spu.getId(), new ArrayList<>());
        response.setSkuCount(skuList.size());

        // 计算价格范围
        if (!skuList.isEmpty()) {
            List<BigDecimal> costPrices = skuList.stream()
                    .map(BizSupplierSku::getCostPrice)
                    .filter(Objects::nonNull)
                    .toList();

            List<BigDecimal> purchasePrices = skuList.stream()
                    .map(BizSupplierSku::getPurchasePrice)
                    .filter(Objects::nonNull)
                    .toList();

            if (!costPrices.isEmpty()) {
                response.setCostPriceMin(costPrices.stream().min(BigDecimal::compareTo).orElse(BigDecimal.ZERO));
                response.setCostPriceMax(costPrices.stream().max(BigDecimal::compareTo).orElse(BigDecimal.ZERO));
            }

            if (!purchasePrices.isEmpty()) {
                response.setPurchasePriceMin(purchasePrices.stream().min(BigDecimal::compareTo).orElse(BigDecimal.ZERO));
                response.setPurchasePriceMax(purchasePrices.stream().max(BigDecimal::compareTo).orElse(BigDecimal.ZERO));
            }
        }

        return response;
    }

    /**
     * 转换为审核详情响应DTO
     */
    private ProductReviewResponse convertToReviewDetailResponse(BizSupplierSpu spu) {
        ProductReviewResponse response = convertToReviewResponse(spu);

        // 获取详细的SKU信息
        List<BizSupplierSku> skuList = bizSupplierSkuService.list(
                new LambdaQueryWrapper<BizSupplierSku>()
                        .eq(BizSupplierSku::getSpuId, spu.getId())
                        .orderByAsc(BizSupplierSku::getSortOrder)
        );

        List<ProductReviewResponse.SupplierSkuInfo> skuInfoList = skuList.stream()
                .map(this::convertToSupplierSkuInfo)
                .collect(Collectors.toList());

        response.setSkuList(skuInfoList);

        // 设置规格名称
        response.setSpec1Name(spu.getSpec1Name());
        response.setSpec2Name(spu.getSpec2Name());
        response.setSpec3Name(spu.getSpec3Name());

        // 获取商品图片信息
        List<BizSupplierSpuImg> imageList = bizSupplierSpuImgService.list(
                new LambdaQueryWrapper<BizSupplierSpuImg>()
                        .eq(BizSupplierSpuImg::getSpuId, spu.getId())
                        .orderByDesc(BizSupplierSpuImg::getIsMain)
                        .orderByAsc(BizSupplierSpuImg::getSortOrder)
        );

        List<ProductReviewResponse.ProductImageInfo> imageInfoList = imageList.stream()
                .map(this::convertToProductImageInfo)
                .collect(Collectors.toList());

        response.setImages(imageInfoList);

        return response;
    }

    /**
     * 转换为供应商SKU信息
     */
    private ProductReviewResponse.SupplierSkuInfo convertToSupplierSkuInfo(BizSupplierSku sku) {
        ProductReviewResponse.SupplierSkuInfo skuInfo = new ProductReviewResponse.SupplierSkuInfo();
        BeanUtils.copyProperties(sku, skuInfo);
        return skuInfo;
    }

    /**
     * 转换商品图片信息
     */
    private ProductReviewResponse.ProductImageInfo convertToProductImageInfo(BizSupplierSpuImg img) {
        ProductReviewResponse.ProductImageInfo imageInfo = new ProductReviewResponse.ProductImageInfo();
        BeanUtils.copyProperties(img, imageInfo);
        return imageInfo;
    }
} 