package com.linkBuy.mallAdminApi.controller;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linkBuy.common.annotation.ResponseWrapper;
import com.linkBuy.common.constants.SecurityConstants;
import com.linkBuy.common.dto.SessionUser;
import com.linkBuy.common.event.v2.EventBus;
import com.linkBuy.common.event.v2.company.CompanyUserPasswordEvent;
import com.linkBuy.common.exception.AuthenticationException;
import com.linkBuy.common.util.UserAccountUtils;
import com.linkBuy.mallAdminApi.dto.request.UserCreateRequest;
import com.linkBuy.mysql.dao.entity.biz.BizCompany;
import com.linkBuy.mysql.dao.entity.biz.BizCompanyUser;
import com.linkBuy.mysql.dao.service.biz.BizCompanyService;
import com.linkBuy.mysql.dao.service.biz.BizCompanyUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
@ResponseWrapper
public class UserController {

    private final BizCompanyUserService companyUserService;
    private final BizCompanyService companyService;
    private final PasswordEncoder passwordEncoder;
    private final EventBus eventBus;

    @Value("${spring.application.name}")
    private String applicationName;

    /**
     * 验证用户权限：检查被管理用户是否属于当前登录用户的客户
     */
    private void validateUserBelongsToCurrentSupplier(BizCompanyUser targetUser, SessionUser sessionUser, String operation) {
        if (targetUser == null) {
            throw new AuthenticationException("用户不存在");
        }
        
        if (sessionUser == null || sessionUser.getCompanyId() == null) {
            throw new AuthenticationException("未登录或客户信息缺失");
        }
        
        if (!targetUser.getCompanyId().equals(sessionUser.getCompanyId())) {
            log.warn("客户ID不匹配 - 操作: {}, 目标用户ID: {}, 目标用户客户ID: {}, 当前用户客户ID: {}, 操作人: {}", 
                    operation, targetUser.getId(), targetUser.getCompanyId(), sessionUser.getCompanyId(), sessionUser.getName());
            throw new AuthenticationException("无权" + operation + "该用户，用户不属于您的客户");
        }
        
        log.debug("客户ID验证通过 - 操作: {}, 用户ID: {}, 客户ID: {}, 操作人: {}", 
                operation, targetUser.getId(), targetUser.getCompanyId(), sessionUser.getName());
    }

    /**
     * 获取客户用户列表
     */
    @GetMapping("/list")
    @PreAuthorize("isAuthenticated()")
    public IPage<BizCompanyUser> getUserList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String query,
            HttpServletRequest request) {
        
        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null || sessionUser.getCompanyId() == null) {
            throw new AuthenticationException("未登录或客户信息缺失");
        }

        LambdaQueryWrapper<BizCompanyUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizCompanyUser::getCompanyId, sessionUser.getCompanyId());
        
        // 搜索条件
        if (StringUtils.hasText(query)) {
            wrapper.and(w -> w.like(BizCompanyUser::getUsername, query)
                    .or().like(BizCompanyUser::getEmail, query)
                    .or().like(BizCompanyUser::getMobile, query));
        }
        
        wrapper.orderByDesc(BizCompanyUser::getCreateTime);
        
        Page<BizCompanyUser> pageParam = new Page<>(page, size);
        return companyUserService.page(pageParam, wrapper);
    }

    /**
     * 获取客户用户详情
     */
    @GetMapping("/{id}")
    @PreAuthorize("isAuthenticated()")
    public BizCompanyUser getUserDetail(@PathVariable Long id, HttpServletRequest request) {
        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null || sessionUser.getCompanyId() == null) {
            throw new AuthenticationException("未登录或客户信息缺失");
        }

        BizCompanyUser user = companyUserService.getById(id);
        
        // 使用通用验证方法
        validateUserBelongsToCurrentSupplier(user, sessionUser, "访问");
        
        // 清除密码字段
        user.setPassword(null);
        return user;
    }

    /**
     * 新增客户用户
     */
    @PostMapping("/add")
    @PreAuthorize("isAuthenticated()")
    @Transactional
    public BizCompanyUser addUser(@Validated @RequestBody UserCreateRequest request,
                                   HttpServletRequest httpRequest) {
        SessionUser sessionUser = (SessionUser) httpRequest.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null || sessionUser.getCompanyId() == null) {
            throw new AuthenticationException("未登录或客户信息缺失");
        }

        // 检查用户名是否已存在（同一客户下）
        LambdaQueryWrapper<BizCompanyUser> usernameWrapper = new LambdaQueryWrapper<>();
        usernameWrapper.eq(BizCompanyUser::getCompanyId, sessionUser.getCompanyId())
                .eq(BizCompanyUser::getUsername, request.getUsername());
        BizCompanyUser existingByUsername = companyUserService.getOne(usernameWrapper);
        Assert.isNull(existingByUsername, "用户名已存在");

        // 检查手机号是否已存在（同一客户下）
        LambdaQueryWrapper<BizCompanyUser> mobileWrapper = new LambdaQueryWrapper<>();
        mobileWrapper.eq(BizCompanyUser::getCompanyId, sessionUser.getCompanyId())
                .eq(BizCompanyUser::getMobile, request.getMobile());
        BizCompanyUser existingByMobile = companyUserService.getOne(mobileWrapper);
        Assert.isNull(existingByMobile, "手机号已存在");

        // 检查邮箱是否已存在（同一客户下）
        LambdaQueryWrapper<BizCompanyUser> emailWrapper = new LambdaQueryWrapper<>();
        emailWrapper.eq(BizCompanyUser::getCompanyId, sessionUser.getCompanyId())
                .eq(BizCompanyUser::getEmail, request.getEmail());
        BizCompanyUser existingByEmail = companyUserService.getOne(emailWrapper);
        Assert.isNull(existingByEmail, "邮箱已存在");

        // 生成随机密码
        String randomPassword = UserAccountUtils.generateRandomPassword();

        BizCompanyUser user = new BizCompanyUser();
        user.setCompanyId(sessionUser.getCompanyId());
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setMobile(request.getMobile());
        user.setPassword(passwordEncoder.encode(randomPassword));
        user.setStatus(1); // 默认启用

        companyUserService.save(user);
        
        log.info("新增客户用户成功，客户ID: {}, 用户名: {}, 邮箱: {}, 操作人: {}", 
                sessionUser.getCompanyId(), user.getUsername(), user.getEmail(), sessionUser.getName());

        // 发布客户用户创建事件
        publishUserCreatedEvent(user, sessionUser, randomPassword);
        
        // 清除密码字段
        user.setPassword(null);
        return user;
    }

    /**
     * 删除客户用户
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("isAuthenticated()")
    @Transactional
    public void deleteUser(@PathVariable Long id, HttpServletRequest request) {
        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null || sessionUser.getCompanyId() == null) {
            throw new AuthenticationException("未登录或客户信息缺失");
        }

        BizCompanyUser user = companyUserService.getById(id);
        
        // 使用通用验证方法
        validateUserBelongsToCurrentSupplier(user, sessionUser, "删除");
        
        Assert.isTrue(user.getStatus() == 0, "启用中账户不能删除");

        // 不能删除自己
        if (user.getId().equals(sessionUser.getUserId())) {
            throw new AuthenticationException("不能删除自己的账号");
        }
        BizCompany company = companyService.getById(user.getCompanyId());
        if (user.getMobile().equals(company.getContactMobile())) {
            throw new AuthenticationException("不能删除主账号");
        }

        // 执行删除（逻辑删除）
        companyUserService.removeById(id);
        
        log.info("删除客户用户成功，用户ID: {}, 用户名: {}, 操作人: {}", 
                id, user.getUsername(), sessionUser.getName());

        // 发布用户删除事件
        publishUserDeletedEvent(user, sessionUser);
    }

    /**
     * 重置用户密码
     */
    @PostMapping("/{id}/resetPassword")
    @PreAuthorize("isAuthenticated()")
    @Transactional
    public void resetPassword(@PathVariable Long id, HttpServletRequest request) {
        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null || sessionUser.getCompanyId() == null) {
            throw new AuthenticationException("未登录或客户信息缺失");
        }

        BizCompanyUser user = companyUserService.getById(id);
        
        // 使用通用验证方法
        validateUserBelongsToCurrentSupplier(user, sessionUser, "重置密码");

        // 生成新的随机密码
        String newPassword = UserAccountUtils.generateRandomPassword();
        user.setPassword(passwordEncoder.encode(newPassword));
        companyUserService.updateById(user);
        
        log.info("重置客户用户密码成功，用户ID: {}, 用户名: {}, 新密码: {}, 操作人: {}", 
                id, user.getUsername(), newPassword, sessionUser.getName());

        // 发布密码重置事件
        publishPasswordResetEvent(user, sessionUser, newPassword);
    }

    /**
     * 启用/禁用用户
     */
    @PostMapping("/{id}/status")
    @PreAuthorize("isAuthenticated()")
    @Transactional
    public void updateUserStatus(@PathVariable Long id, 
                                @RequestParam Integer status,
                                HttpServletRequest request) {
        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null || sessionUser.getCompanyId() == null) {
            throw new AuthenticationException("未登录或客户信息缺失");
        }

        BizCompanyUser user = companyUserService.getById(id);
        BizCompany company = companyService.getById(user.getCompanyId());
        
        // 使用通用验证方法
        validateUserBelongsToCurrentSupplier(user, sessionUser, "修改状态");

        // 不能禁用自己
        if (user.getId().equals(sessionUser.getUserId()) && status == 0) {
            throw new AuthenticationException("不能禁用自己的账号");
        }
        if (user.getMobile().equals(company.getContactMobile()) && status == 0) {
            throw new AuthenticationException("不能禁用主账号");
        }

        // 验证状态值
        if (status != 0 && status != 1) {
            throw new IllegalArgumentException("状态值只能是0（禁用）或1（启用）");
        }

        user.setStatus(status);
        companyUserService.updateById(user);
        
        String statusText = status == 1 ? "启用" : "禁用";
        log.info("{}客户用户成功，用户ID: {}, 用户名: {}, 操作人: {}", 
                statusText, id, user.getUsername(), sessionUser.getName());

        // 发布用户状态变更事件
        publishUserStatusChangedEvent(user, sessionUser, status);
    }

    /**
     * 发布用户创建事件
     */
    private void publishUserCreatedEvent(BizCompanyUser user, SessionUser operator, String rawPassword) {
        if (StringUtils.hasText(user.getEmail())) {
            // 获取客户信息
            BizCompany company = companyService.getById(user.getCompanyId());

            CompanyUserPasswordEvent event = CompanyUserPasswordEvent.createUserCreatedEvent(
                    applicationName, user.getId(), operator.getName());
            event.setCompanyUserId(user.getId());
            event.setCompanyId(user.getCompanyId());
            event.setCompanyName(company != null ? company.getName() : "未知客户");
            event.setUsername(user.getUsername());
            event.setEmail(user.getEmail());
            event.setMobile(user.getMobile());
            event.setRawPassword(rawPassword);
            event.setStatus(user.getStatus());

            eventBus.publishAsync(event);
            log.info("客户用户创建事件已发布，用户ID: {}, 邮箱: {}", user.getId(), user.getEmail());
        }
    }

    /**
     * 发布用户删除事件
     */
    private void publishUserDeletedEvent(BizCompanyUser user, SessionUser operator) {
        // 获取客户信息
    }

    /**
     * 发布密码重置事件
     */
    private void publishPasswordResetEvent(BizCompanyUser user, SessionUser operator, String newPassword) {
        if (StringUtils.hasText(user.getEmail())) {
            // 获取客户信息
            BizCompany company = companyService.getById(user.getCompanyId());

            CompanyUserPasswordEvent event = CompanyUserPasswordEvent.createPasswordResetEvent(
                    applicationName, user.getId(), operator.getName());
            event.setCompanyUserId(user.getId());
            event.setCompanyId(user.getCompanyId());
            event.setCompanyName(company != null ? company.getName() : "未知客户");
            event.setUsername(user.getUsername());
            event.setEmail(user.getEmail());
            event.setMobile(user.getMobile());
            event.setRawPassword(newPassword);
            event.setStatus(user.getStatus());

            eventBus.publishAsync(event);
            log.info("客户用户密码重置事件已发布，用户ID: {}, 邮箱: {}", user.getId(), user.getEmail());
        }
    }

    /**
     * 发布用户状态变更事件
     */
    private void publishUserStatusChangedEvent(BizCompanyUser user, SessionUser operator, Integer newStatus) {
        // 获取客户信息
    }
}
