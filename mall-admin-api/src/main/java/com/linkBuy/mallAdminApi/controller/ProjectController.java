package com.linkBuy.mallAdminApi.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linkBuy.mysql.dao.entity.biz.BizCompanyProject;
import com.linkBuy.mysql.dao.service.biz.BizCompanyProjectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 项目管理控制器
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Api(tags = "项目管理")
@RestController
@RequestMapping("/project")
@Slf4j
public class ProjectController {

    @Autowired
    private BizCompanyProjectService projectService;

    @ApiOperation("分页查询项目列表")
    @GetMapping("/list")
    public Map<String, Object> getProjectList(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("客户ID") @RequestParam Long companyId,
            @ApiParam("查询关键词") @RequestParam(required = false) String query) {

        Map<String, Object> result = new HashMap<>();
        try {
            Page<BizCompanyProject> pageParam = new Page<>(page, size);
            IPage<BizCompanyProject> pageResult = projectService.selectProjectPage(pageParam, companyId, query);

            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", pageResult);
        } catch (Exception e) {
            log.error("查询项目列表失败", e);
            result.put("code", 500);
            result.put("message", "查询失败：" + e.getMessage());
        }

        return result;
    }

    @ApiOperation("获取项目详情")
    @GetMapping("/{id}")
    public Map<String, Object> getProjectDetail(
            @ApiParam("项目ID") @PathVariable Long id,
            @ApiParam("客户ID") @RequestParam Long companyId) {

        Map<String, Object> result = new HashMap<>();
        try {
            BizCompanyProject project = projectService.getById(id);
            if (project == null || !project.getCompanyId().equals(companyId)) {
                result.put("code", 404);
                result.put("message", "项目不存在");
                return result;
            }

            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", project);
        } catch (Exception e) {
            log.error("查询项目详情失败", e);
            result.put("code", 500);
            result.put("message", "查询失败：" + e.getMessage());
        }

        return result;
    }

    @ApiOperation("创建项目")
    @PostMapping
    @Transactional
    public Map<String, Object> createProject(
            @ApiParam("客户ID") @RequestParam Long companyId,
            @Valid @RequestBody BizCompanyProject project) {

        Map<String, Object> result = new HashMap<>();
        try {
            // 设置客户ID
            project.setCompanyId(companyId);

            boolean success = projectService.createProject(project);
            if (success) {
                result.put("code", 200);
                result.put("message", "创建成功");
                result.put("data", project);
            } else {
                result.put("code", 500);
                result.put("message", "创建失败");
            }
        } catch (Exception e) {
            log.error("创建项目失败", e);
            result.put("code", 500);
            result.put("message", "创建失败：" + e.getMessage());
        }

        return result;
    }

    @ApiOperation("更新项目")
    @PostMapping("/{id}")
    @Transactional
    public Map<String, Object> updateProject(
            @ApiParam("项目ID") @PathVariable Long id,
            @ApiParam("客户ID") @RequestParam Long companyId,
            @Valid @RequestBody BizCompanyProject project) {

        Map<String, Object> result = new HashMap<>();
        try {
            // 设置ID和客户ID
            project.setId(id);
            project.setCompanyId(companyId);

            // 检查项目是否存在
            BizCompanyProject existProject = projectService.getById(id);
            if (existProject == null || !existProject.getCompanyId().equals(companyId)) {
                result.put("code", 404);
                result.put("message", "项目不存在");
                return result;
            }

            // 检查项目编码是否重复（排除自己）
            if (StringUtils.hasText(project.getProjectCode())) {
                BizCompanyProject codeProject = projectService.selectByProjectCode(project.getProjectCode(), companyId);
                if (codeProject != null && !codeProject.getId().equals(id)) {
                    result.put("code", 400);
                    result.put("message", "项目编码已存在");
                    return result;
                }
            }

            boolean success = projectService.updateProject(project);
            if (success) {
                result.put("code", 200);
                result.put("message", "更新成功");
                result.put("data", project);
            } else {
                result.put("code", 500);
                result.put("message", "更新失败");
            }
        } catch (Exception e) {
            log.error("更新项目失败", e);
            result.put("code", 500);
            result.put("message", "更新失败：" + e.getMessage());
        }

        return result;
    }

    @ApiOperation("删除项目")
    @DeleteMapping("/{id}")
    @Transactional
    public Map<String, Object> deleteProject(
            @ApiParam("项目ID") @PathVariable Long id,
            @ApiParam("客户ID") @RequestParam Long companyId) {

        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = projectService.deleteProject(id, companyId);
            if (success) {
                result.put("code", 200);
                result.put("message", "删除成功");
            } else {
                result.put("code", 500);
                result.put("message", "删除失败");
            }
        } catch (Exception e) {
            log.error("删除项目失败", e);
            result.put("code", 500);
            result.put("message", "删除失败：" + e.getMessage());
        }

        return result;
    }

    @ApiOperation("更新项目状态")
    @PostMapping("/{id}/status")
    @Transactional
    public Map<String, Object> updateProjectStatus(
            @ApiParam("项目ID") @PathVariable Long id,
            @ApiParam("客户ID") @RequestParam Long companyId,
            @ApiParam("状态") @RequestParam Integer status) {

        Map<String, Object> result = new HashMap<>();
        try {
            // 验证状态值
            if (status != 0 && status != 1) {
                result.put("code", 400);
                result.put("message", "状态值无效");
                return result;
            }

            boolean success = projectService.updateProjectStatus(id, status, companyId);
            if (success) {
                result.put("code", 200);
                result.put("message", "状态更新成功");
            } else {
                result.put("code", 500);
                result.put("message", "状态更新失败");
            }
        } catch (Exception e) {
            log.error("更新项目状态失败", e);
            result.put("code", 500);
            result.put("message", "状态更新失败：" + e.getMessage());
        }

        return result;
    }
}
