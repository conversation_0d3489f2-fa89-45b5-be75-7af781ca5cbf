package com.linkBuy.mallAdminApi.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linkBuy.common.annotation.ResponseWrapper;
import com.linkBuy.common.constants.SecurityConstants;
import com.linkBuy.common.dto.SessionUser;
import com.linkBuy.common.exception.PriceChangeException;
import com.linkBuy.common.exception.ProductBusinessException;
import com.linkBuy.common.service.PriceCalculationService;
import com.linkBuy.common.service.ProductBusinessService;
import com.linkBuy.common.service.ProductOperationLogger;
import com.linkBuy.mallAdminApi.dto.request.PriceChangeReviewRequest;
import com.linkBuy.mallAdminApi.dto.response.PriceChangeReviewResponse;
import com.linkBuy.mysql.dao.entity.biz.*;
import com.linkBuy.mysql.dao.service.biz.*;
import com.linkBuy.mysql.dao.service.common.SpuOperationLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 价格变更审核控制器
 * 专注于处理供应商提报的价格变更申请
 */
@Slf4j
@RestController
@RequestMapping("/company-price-change-review")
@RequiredArgsConstructor
@ResponseWrapper
public class CompanyPriceChangeReviewController {

    @Autowired
    private BizPriceSpecChangeRequestService bizPriceSpecChangeRequestService;
    
    @Autowired
    private BizPriceSpecChangeRequestProductService bizPriceSpecChangeRequestProductService;
    
    @Autowired
    private BizPriceSpecChangeRequestSkuService bizPriceSpecChangeRequestSkuService;
    
    @Autowired
    private BizSupplierSpuService bizSupplierSpuService;
    
    @Autowired
    private BizSupplierSkuService bizSupplierSkuService;
    
    @Autowired
    private BizSupplierService bizSupplierService;
    
    @Autowired
    private BizCompanySpuService bizCompanySpuService;
    
    @Autowired
    private BizCompanySkuService bizCompanySkuService;

    @Autowired
    private SpuOperationLogService spuOperationLogService;

    @Autowired
    private ProductBusinessService productBusinessService;

    @Autowired
    private ProductOperationLogger productOperationLogger;

    @Autowired
    private PriceCalculationService priceCalculationService;

    /**
     * 获取价格变更申请列表
     */
    @GetMapping("/list")
    public IPage<PriceChangeReviewResponse> getPriceChangeRequestList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String requestNo,
            @RequestParam(required = false) Long supplierId,
            @RequestParam(required = false) Integer status,
            HttpServletRequest request) {
        
        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null) {
            throw new ProductBusinessException("用户未登录");
        }

        productOperationLogger.logProductOperation(null, "查询价格变更申请列表", 
            sessionUser.getName(), "分页查询", request);

        // 首先获取当前客户公司的客户供应商ID列表
        List<Long> customerSupplierIds = getCustomerSupplierIds(sessionUser.getCompanyId());
        if (customerSupplierIds.isEmpty()) {
            return new Page<>();
        }

        Page<BizPriceSpecChangeRequest> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<BizPriceSpecChangeRequest> wrapper = new LambdaQueryWrapper<>();
        
        // 只查询客户供应商的价格变更申请
        wrapper.in(BizPriceSpecChangeRequest::getSupplierId, customerSupplierIds);
        
        // 申请单号搜索
        if (requestNo != null && !requestNo.trim().isEmpty()) {
            wrapper.like(BizPriceSpecChangeRequest::getRequestNo, requestNo.trim());
        }
        
        // 供应商筛选
        if (supplierId != null) {
            if (!customerSupplierIds.contains(supplierId)) {
                throw new ProductBusinessException("无权限查看该供应商的价格变更申请");
            }
            wrapper.eq(BizPriceSpecChangeRequest::getSupplierId, supplierId);
        }
        
        // 状态筛选
        if (status != null) {
            wrapper.eq(BizPriceSpecChangeRequest::getStatus, status);
        } else {
            // 默认显示待审核的申请
            wrapper.eq(BizPriceSpecChangeRequest::getStatus, 1); // 待审核
        }
        
        wrapper.orderByDesc(BizPriceSpecChangeRequest::getCreateTime);
        
        IPage<BizPriceSpecChangeRequest> requestPage = bizPriceSpecChangeRequestService.page(pageParam, wrapper);
        
        // 转换为响应DTO

        return requestPage.convert(changeReq -> convertToPriceChangeResponse(changeReq, sessionUser.getCompanyId()));
    }

    /**
     * 获取价格变更申请详情
     */
    @GetMapping("/{id}")
    public PriceChangeReviewResponse getPriceChangeRequestDetail(@PathVariable Long id, HttpServletRequest request) {
        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null) {
            throw new ProductBusinessException("用户未登录");
        }

        BizPriceSpecChangeRequest changeRequest = bizPriceSpecChangeRequestService.getById(id);
        if (changeRequest == null) {
            throw new ProductBusinessException("价格变更申请不存在");
        }
        
        // 验证权限：检查供应商是否属于当前客户公司
        List<Long> customerSupplierIds = getCustomerSupplierIds(sessionUser.getCompanyId());
        if (!customerSupplierIds.contains(changeRequest.getSupplierId())) {
            throw new ProductBusinessException("无权限查看该价格变更申请");
        }

        productOperationLogger.logPriceChangeOperation(id, changeRequest.getRequestNo(), 
            "查看价格变更详情", sessionUser.getName(), "查看申请详情", request);
        
        PriceChangeReviewResponse response = convertToPriceChangeDetailResponse(changeRequest, sessionUser.getCompanyId());
        
        return response;
    }

    /**
     * 审核价格变更申请
     */
    @PostMapping("/{id}/review")
    @Transactional
    public void reviewPriceChangeRequest(@PathVariable Long id, 
                                       @RequestBody PriceChangeReviewRequest request,
                                       HttpServletRequest httpRequest) {
        SessionUser sessionUser = (SessionUser) httpRequest.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null) {
            throw new ProductBusinessException("用户未登录");
        }

        BizPriceSpecChangeRequest changeRequest = bizPriceSpecChangeRequestService.getById(id);
        if (changeRequest == null) {
            throw new ProductBusinessException("价格变更申请不存在");
        }
        
        // 验证权限
        List<Long> customerSupplierIds = getCustomerSupplierIds(sessionUser.getCompanyId());
        if (!customerSupplierIds.contains(changeRequest.getSupplierId())) {
            throw new ProductBusinessException("无权限审核该价格变更申请");
        }
        
        // 使用公共服务验证状态转换
        if (!productBusinessService.canTransitionPriceChangeStatus(changeRequest.getStatus(), request.getReviewResult())) {
            throw new PriceChangeException("申请状态不允许审核，当前状态：" + 
                productBusinessService.getPriceChangeStatusName(changeRequest.getStatus()));
        }
        
        Integer reviewResult = request.getReviewResult();
        String reviewRemark = request.getReviewRemark();
        
        if (reviewResult == null || (!reviewResult.equals(2) && !reviewResult.equals(3))) {
            throw new PriceChangeException("审核结果参数错误");
        }

        // 记录审核开始日志
        productOperationLogger.logPriceChangeOperation(id, changeRequest.getRequestNo(), 
            "开始审核价格变更申请", sessionUser.getName(), 
            "审核结果：" + (reviewResult == 2 ? "通过" : "拒绝") + "，审核意见：" + reviewRemark, httpRequest);
        
        // 更新申请状态
        BizPriceSpecChangeRequest updateRequest = new BizPriceSpecChangeRequest();
        updateRequest.setId(id);
        updateRequest.setStatus(reviewResult);
        // 注意：这里需要根据实际的实体类字段名调整
        // updateRequest.setReviewRemark(reviewRemark);
        updateRequest.setReviewTime(LocalDateTime.now());
        updateRequest.setReviewerId(sessionUser.getUserId());
        updateRequest.setUpdateTime(LocalDateTime.now());
        
        bizPriceSpecChangeRequestService.updateById(updateRequest);
        
        // 如果审核通过，应用价格变更
        if (reviewResult.equals(2)) {
            applyPriceChanges(changeRequest);
        }

        // 记录价格变更审核日志
        // 获取涉及的商品ID列表
        List<BizPriceSpecChangeRequestProduct> productList = bizPriceSpecChangeRequestProductService.list(
                new LambdaQueryWrapper<BizPriceSpecChangeRequestProduct>()
                        .eq(BizPriceSpecChangeRequestProduct::getRequestId, id)
        );
        List<Long> spuIds = productList.stream()
                .map(BizPriceSpecChangeRequestProduct::getSpuId)
                .toList();

        String action = reviewResult.equals(2) ? "PRICE_CHANGE_PASS" : "PRICE_CHANGE_REJECT";
        for (Long spuId : spuIds) {
            spuOperationLogService.recordCompanyOperationLog(spuId, action, 1, reviewResult,
                    String.format("价格变更审核%s，申请单号：%s，审核意见：%s", 
                            reviewResult == 2 ? "通过" : "拒绝", changeRequest.getRequestNo(), reviewRemark), httpRequest);
        }
        
        log.info("客户管理员审核价格变更申请成功，申请ID：{}，审核结果：{}，操作人：{}", 
                id, reviewResult == 2 ? "通过" : "拒绝", sessionUser.getName());
    }

    /**
     * 批量审核价格变更申请
     */
    @PostMapping("/batch/review")
    @Transactional
    public void batchReviewPriceChangeRequests(@RequestBody PriceChangeReviewRequest request, 
                                             HttpServletRequest httpRequest) {
        SessionUser sessionUser = (SessionUser) httpRequest.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null) {
            throw new ProductBusinessException("用户未登录");
        }

        List<Long> ids = request.getIds();
        Integer reviewResult = request.getReviewResult();
        String reviewRemark = request.getReviewRemark();
        
        if (ids == null || ids.isEmpty()) {
            throw new ProductBusinessException("请选择要审核的申请");
        }
        
        if (reviewResult == null || (!reviewResult.equals(2) && !reviewResult.equals(3))) {
            throw new PriceChangeException("审核结果参数错误");
        }

        productOperationLogger.logProductOperation(null, "批量审核价格变更申请", 
            sessionUser.getName(), "批量审核" + ids.size() + "个申请", httpRequest);
        
        // 验证所有申请的权限和状态
        List<BizPriceSpecChangeRequest> requestList = bizPriceSpecChangeRequestService.listByIds(ids);
        List<Long> customerSupplierIds = getCustomerSupplierIds(sessionUser.getCompanyId());
        
        for (BizPriceSpecChangeRequest changeRequest : requestList) {
            if (!customerSupplierIds.contains(changeRequest.getSupplierId())) {
                throw new ProductBusinessException("包含无权限审核的申请：" + changeRequest.getRequestNo());
            }
            
            if (!productBusinessService.canTransitionPriceChangeStatus(changeRequest.getStatus(), reviewResult)) {
                throw new PriceChangeException("申请状态不允许审核：" + changeRequest.getRequestNo() + 
                    "，当前状态：" + productBusinessService.getPriceChangeStatusName(changeRequest.getStatus()));
            }
        }
        
                 // 批量更新申请状态
         for (Long requestId : ids) {
             BizPriceSpecChangeRequest updateRequest = new BizPriceSpecChangeRequest();
             updateRequest.setId(requestId);
             updateRequest.setStatus(reviewResult);
             // 注意：这里需要根据实际的实体类字段名调整
             // updateRequest.setReviewRemark(reviewRemark);
             updateRequest.setReviewTime(LocalDateTime.now());
             updateRequest.setReviewerId(sessionUser.getUserId());
             updateRequest.setUpdateTime(LocalDateTime.now());
             
             bizPriceSpecChangeRequestService.updateById(updateRequest);
         }
        
        // 如果审核通过，批量应用价格变更
        if (reviewResult.equals(2)) {
            for (BizPriceSpecChangeRequest changeRequest : requestList) {
                applyPriceChanges(changeRequest);
            }
        }

        // 记录批量价格变更审核日志
        String action = reviewResult.equals(2) ? "PRICE_CHANGE_PASS" : "PRICE_CHANGE_REJECT";
        for (BizPriceSpecChangeRequest changeRequest : requestList) {
            // 获取该申请涉及的商品ID列表
            List<BizPriceSpecChangeRequestProduct> productList = bizPriceSpecChangeRequestProductService.list(
                    new LambdaQueryWrapper<BizPriceSpecChangeRequestProduct>()
                            .eq(BizPriceSpecChangeRequestProduct::getRequestId, changeRequest.getId())
            );
            List<Long> spuIds = productList.stream()
                    .map(BizPriceSpecChangeRequestProduct::getSpuId)
                    .toList();

            for (Long spuId : spuIds) {
                spuOperationLogService.recordCompanyOperationLog(spuId, action, 1, reviewResult,
                        String.format("批量价格变更审核%s，申请单号：%s，审核意见：%s", 
                                reviewResult == 2 ? "通过" : "拒绝", changeRequest.getRequestNo(), reviewRemark), httpRequest);
            }
        }
        
        log.info("客户管理员批量审核价格变更申请成功，申请数量：{}，审核结果：{}，操作人：{}", 
                ids.size(), reviewResult == 2 ? "通过" : "拒绝", sessionUser.getName());
    }

    /**
     * 获取价格变更审核统计
     */
    @GetMapping("/statistics")
    public Map<String, Object> getPriceChangeReviewStatistics(HttpServletRequest request) {
        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null) {
            throw new ProductBusinessException("用户未登录");
        }

        productOperationLogger.logProductOperation(null, "查询价格变更统计", 
            sessionUser.getName(), "统计查询", request);

        Map<String, Object> statistics = new HashMap<>();
        
        // 获取当前客户公司的客户供应商ID列表
        List<Long> customerSupplierIds = getCustomerSupplierIds(sessionUser.getCompanyId());
        if (customerSupplierIds.isEmpty()) {
            statistics.put("pending", 0);
            statistics.put("approved", 0);
            statistics.put("rejected", 0);
            return statistics;
        }
        
        LambdaQueryWrapper<BizPriceSpecChangeRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(BizPriceSpecChangeRequest::getSupplierId, customerSupplierIds);
        
        // 待审核
        wrapper.eq(BizPriceSpecChangeRequest::getStatus, 1);
        long pendingCount = bizPriceSpecChangeRequestService.count(wrapper);
        statistics.put("pending", pendingCount);
        
        // 已通过
        wrapper.clear();
        wrapper.in(BizPriceSpecChangeRequest::getSupplierId, customerSupplierIds);
        wrapper.eq(BizPriceSpecChangeRequest::getStatus, 2);
        long approvedCount = bizPriceSpecChangeRequestService.count(wrapper);
        statistics.put("approved", approvedCount);
        
        // 已拒绝
        wrapper.clear();
        wrapper.in(BizPriceSpecChangeRequest::getSupplierId, customerSupplierIds);
        wrapper.eq(BizPriceSpecChangeRequest::getStatus, 3);
        long rejectedCount = bizPriceSpecChangeRequestService.count(wrapper);
        statistics.put("rejected", rejectedCount);
        
        // 总数
        long totalCount = pendingCount + approvedCount + rejectedCount;
        statistics.put("total", totalCount);
        
        return statistics;
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 获取当前客户公司的客户供应商ID列表
     */
    private List<Long> getCustomerSupplierIds(Long companyId) {
        LambdaQueryWrapper<BizSupplier> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizSupplier::getSupplierType, 1); // 客户供应商
        wrapper.eq(BizSupplier::getCompanyId, companyId); // 指定公司
        
        return bizSupplierService.list(wrapper).stream()
                .map(BizSupplier::getId)
                .collect(Collectors.toList());
    }

    /**
     * 应用价格变更（审核通过后执行）
     */
    private void applyPriceChanges(BizPriceSpecChangeRequest changeRequest) {
        // 获取变更申请的商品明细
        List<BizPriceSpecChangeRequestProduct> productRequests = bizPriceSpecChangeRequestProductService.list(
            new LambdaQueryWrapper<BizPriceSpecChangeRequestProduct>()
                .eq(BizPriceSpecChangeRequestProduct::getRequestId, changeRequest.getId())
        );
        
        for (BizPriceSpecChangeRequestProduct productRequest : productRequests) {
            // 获取该商品的SKU变更明细
            List<BizPriceSpecChangeRequestSku> skuRequests = bizPriceSpecChangeRequestSkuService.list(
                new LambdaQueryWrapper<BizPriceSpecChangeRequestSku>()
                    .eq(BizPriceSpecChangeRequestSku::getProductRequestId, productRequest.getId())
            );
            
            // 应用SKU价格变更
            for (BizPriceSpecChangeRequestSku skuRequest : skuRequests) {
                applySkuPriceChange(skuRequest, changeRequest.getSupplierId());
            }
            
            // 更新客户SPU的价格范围
            updateCompanySpuPriceRangeAfterChange(productRequest.getSpuId(), changeRequest.getSupplierId());
        }
        
        log.info("应用价格变更成功，申请ID：{}，变更商品数量：{}", 
                changeRequest.getId(), productRequests.size());
    }

    /**
     * 应用SKU价格变更
     */
    private void applySkuPriceChange(BizPriceSpecChangeRequestSku skuRequest, Long supplierId) {
        // 更新供应商SKU价格
        if (skuRequest.getSkuId() != null) {
            BizSupplierSku supplierSku = bizSupplierSkuService.getById(skuRequest.getSkuId());
            if (supplierSku != null) {
                // 记录原始价格用于客户价格更新逻辑
                BigDecimal oldCostPrice = supplierSku.getCostPrice();
                BigDecimal oldPurchasePrice = supplierSku.getPurchasePrice();
                BigDecimal oldStrikethroughPrice = supplierSku.getStrikethroughPrice();
                
                // 更新供应商SKU价格
                if (skuRequest.getNewCostPrice() != null) {
                    supplierSku.setCostPrice(skuRequest.getNewCostPrice());
                }
                if (skuRequest.getNewPurchasePrice() != null) {
                    supplierSku.setPurchasePrice(skuRequest.getNewPurchasePrice());
                }
                if (skuRequest.getNewStrikethroughPrice() != null) {
                    supplierSku.setStrikethroughPrice(skuRequest.getNewStrikethroughPrice());
                }
                supplierSku.setUpdateTime(LocalDateTime.now());
                bizSupplierSkuService.updateById(supplierSku);
                
                // 同步更新客户SKU价格（如果存在）
                updateCompanySkuPriceAfterChange(supplierSku, skuRequest, oldCostPrice, oldPurchasePrice, oldStrikethroughPrice);
            }
        } else {
            // 处理新增SKU的情况
            handleNewSkuCreation(skuRequest, supplierId);
        }
    }

    /**
     * 更新客户SKU利润率（供应商成本价变更后）
     */
    private void updateCompanySkuPriceAfterChange(BizSupplierSku supplierSku, BizPriceSpecChangeRequestSku skuRequest,
                                                  BigDecimal oldCostPrice, BigDecimal oldPurchasePrice, BigDecimal oldStrikethroughPrice) {
        // 查找使用该供应商SKU的客户SKU
        List<BizCompanySku> companySkuList = bizCompanySkuService.list(
            new LambdaQueryWrapper<BizCompanySku>()
                .eq(BizCompanySku::getSkuId, supplierSku.getId())
                .eq(BizCompanySku::getStatus, 1) // 只更新有效的客户SKU
        );
        
        // 记录需要更新SPU利润率范围的公司ID和SPU ID
        Set<String> companySpuToUpdate = new HashSet<>();
        
        for (BizCompanySku companySku : companySkuList) {
            // 使用公共服务重新计算利润率
            // 注意：这里不修改销售价，只重新计算利润率
            if (companySku.getSalePrice() != null && supplierSku.getCostPrice() != null && 
                supplierSku.getCostPrice().compareTo(BigDecimal.ZERO) > 0) {
                
                BigDecimal profitRate = priceCalculationService.calculateProfitRate(
                    companySku.getSalePrice(), supplierSku.getCostPrice());
                
                companySku.setProfitRate(profitRate);
                companySku.setUpdateTime(LocalDateTime.now());
                bizCompanySkuService.updateById(companySku);
                
                log.info("客户SKU利润率重新计算，SKU ID: {}, 销售价: {}, 新成本价: {}, 新利润率: {}%", 
                        companySku.getId(), companySku.getSalePrice(), supplierSku.getCostPrice(), 
                        priceCalculationService.formatProfitRate(profitRate));
                
                // 记录需要更新SPU利润率范围的公司SPU
                companySpuToUpdate.add(companySku.getCompanyId() + "_" + companySku.getSpuId());
            } else if (supplierSku.getCostPrice() == null || supplierSku.getCostPrice().compareTo(BigDecimal.ZERO) <= 0) {
                // 如果成本价无效，将利润率设为null
                companySku.setProfitRate(null);
                companySku.setUpdateTime(LocalDateTime.now());
                bizCompanySkuService.updateById(companySku);
                
                log.info("客户SKU利润率重置为空，SKU ID: {}, 原因: 成本价无效", companySku.getId());
                
                // 记录需要更新SPU利润率范围的公司SPU
                companySpuToUpdate.add(companySku.getCompanyId() + "_" + companySku.getSpuId());
            }
        }
        
        // 批量更新客户SPU的利润率范围（不更新价格范围，因为销售价没有变化）
        for (String companySpuKey : companySpuToUpdate) {
            String[] parts = companySpuKey.split("_");
            Long companyId = Long.valueOf(parts[0]);
            Long spuId = Long.valueOf(parts[1]);
            updateCompanySpuProfitRateRange(companyId, spuId);
        }
    }
    

    
    /**
     * 处理新增SKU的情况
     */
    private void handleNewSkuCreation(BizPriceSpecChangeRequestSku skuRequest, Long supplierId) {
        log.info("处理新增SKU申请，SKU编码: {}, 供应商ID: {}", skuRequest.getSkuCode(), supplierId);
        
        // 获取商品申请信息以获取正确的SPU ID
        BizPriceSpecChangeRequestProduct productRequest = bizPriceSpecChangeRequestProductService.getById(skuRequest.getProductRequestId());
        if (productRequest == null) {
            log.error("找不到商品申请信息，产品请求ID: {}", skuRequest.getProductRequestId());
            return;
        }
        
        // 创建新的供应商SKU
        BizSupplierSku newSupplierSku = new BizSupplierSku();
        newSupplierSku.setSkuCode(skuRequest.getSkuCode());
        newSupplierSku.setSpuId(productRequest.getSpuId());
        newSupplierSku.setCostPrice(skuRequest.getNewCostPrice());
        newSupplierSku.setPurchasePrice(skuRequest.getNewPurchasePrice());
        newSupplierSku.setStrikethroughPrice(skuRequest.getNewStrikethroughPrice());
        newSupplierSku.setSpec1Value(skuRequest.getSpec1Value());
        newSupplierSku.setSpec2Value(skuRequest.getSpec2Value());
        newSupplierSku.setSpec3Value(skuRequest.getSpec3Value());
        newSupplierSku.setTotalInventory(skuRequest.getTotalInventory());
        newSupplierSku.setAvailableInventory(skuRequest.getAvailableInventory());
        newSupplierSku.setBlockedInventory(skuRequest.getBlockedInventory());
        newSupplierSku.setSafetyStock(skuRequest.getSafetyStock());
        newSupplierSku.setImgUrl(skuRequest.getImgUrl());
        newSupplierSku.setSortOrder(skuRequest.getSortOrder());
        newSupplierSku.setCreateTime(LocalDateTime.now());
        newSupplierSku.setUpdateTime(LocalDateTime.now());
        
        // 保存新的供应商SKU
        bizSupplierSkuService.save(newSupplierSku);
        
        // 为所有使用该供应商的客户公司创建对应的客户SKU
        createCompanySkuForNewSupplierSku(newSupplierSku, supplierId);
        
        log.info("新增供应商SKU创建成功，SKU ID: {}, SKU编码: {}", newSupplierSku.getId(), skuRequest.getSkuCode());
    }
    
    /**
     * 为新增的供应商SKU创建对应的客户SKU
     */
    private void createCompanySkuForNewSupplierSku(BizSupplierSku supplierSku, Long supplierId) {
        // 查找已经使用该SPU的所有客户公司
        List<BizCompanySpu> companySpuList = bizCompanySpuService.list(
            new LambdaQueryWrapper<BizCompanySpu>()
                .eq(BizCompanySpu::getSpuId, supplierSku.getSpuId())
                .eq(BizCompanySpu::getStatus, 1)
        );
        
        // 为每个使用该SPU的客户公司创建对应的客户SKU
        for (BizCompanySpu companySpu : companySpuList) {
            // 检查是否已经存在该客户SKU
            BizCompanySku existingCompanySku = bizCompanySkuService.getOne(
                new LambdaQueryWrapper<BizCompanySku>()
                    .eq(BizCompanySku::getCompanyId, companySpu.getCompanyId())
                    .eq(BizCompanySku::getSkuId, supplierSku.getId())
                    .last("LIMIT 1")
            );
            
            if (existingCompanySku == null) {
                // 创建新的客户SKU，使用供应商SKU的划线价作为默认销售价
                BizCompanySku newCompanySku = new BizCompanySku();
                newCompanySku.setCompanyId(companySpu.getCompanyId());
                newCompanySku.setSpuId(supplierSku.getSpuId());
                newCompanySku.setSkuId(supplierSku.getId());
                newCompanySku.setSalePrice(supplierSku.getStrikethroughPrice() != null ? 
                                          supplierSku.getStrikethroughPrice() : supplierSku.getPurchasePrice());
                
                // 使用公共服务计算利润率
                if (supplierSku.getCostPrice() != null && supplierSku.getCostPrice().compareTo(BigDecimal.ZERO) > 0 
                    && newCompanySku.getSalePrice() != null) {
                    BigDecimal profitRate = priceCalculationService.calculateProfitRate(
                        newCompanySku.getSalePrice(), supplierSku.getCostPrice());
                    newCompanySku.setProfitRate(profitRate);
                }
                
                newCompanySku.setStatus(1);
                newCompanySku.setSource(companySpu.getSource());
                newCompanySku.setCreateTime(LocalDateTime.now());
                newCompanySku.setUpdateTime(LocalDateTime.now());
                
                bizCompanySkuService.save(newCompanySku);
                
                log.info("为新增供应商SKU创建客户SKU，公司ID: {}, SKU ID: {}, 销售价: {}", 
                        companySpu.getCompanyId(), supplierSku.getId(), newCompanySku.getSalePrice());
                
                // 更新客户SPU的价格范围
                updateCompanySpuPriceAndProfitRange(companySpu.getCompanyId(), supplierSku.getSpuId());
            }
        }
        
        log.info("新增供应商SKU的客户SKU创建完成，供应商ID: {}, SKU ID: {}, 涉及客户公司数: {}", 
                supplierId, supplierSku.getId(), companySpuList.size());
    }

    /**
     * 更新客户SPU价格范围（价格变更后）
     */
    private void updateCompanySpuPriceRangeAfterChange(Long spuId, Long supplierId) {
        // 查找使用该供应商SPU的客户SPU
        List<BizCompanySpu> companySpuList = bizCompanySpuService.list(
            new LambdaQueryWrapper<BizCompanySpu>()
                .eq(BizCompanySpu::getSpuId, spuId)
        );
        
        for (BizCompanySpu companySpu : companySpuList) {
            updateCompanySpuPriceAndProfitRange(companySpu.getCompanyId(), spuId);
        }
    }

    /**
     * 更新客户SPU的价格范围和利润率范围
     */
    private void updateCompanySpuPriceAndProfitRange(Long companyId, Long spuId) {
        // 查找该公司该SPU下的所有客户SKU
        List<BizCompanySku> companySkuList = bizCompanySkuService.list(
            new LambdaQueryWrapper<BizCompanySku>()
                .eq(BizCompanySku::getCompanyId, companyId)
                .eq(BizCompanySku::getSpuId, spuId)
                .eq(BizCompanySku::getStatus, 1)
        );
        
        if (companySkuList.isEmpty()) {
            return;
        }
        
        // 使用公共服务计算价格范围
        BigDecimal minPrice = companySkuList.stream()
                .map(BizCompanySku::getSalePrice)
                .filter(Objects::nonNull)
                .reduce(priceCalculationService::getLowerPrice)
                .orElse(BigDecimal.ZERO);
        
        BigDecimal maxPrice = companySkuList.stream()
                .map(BizCompanySku::getSalePrice)
                .filter(Objects::nonNull)
                .reduce(priceCalculationService::getHigherPrice)
                .orElse(BigDecimal.ZERO);
        
        // 使用公共服务计算利润率范围
        BigDecimal minProfitRate = companySkuList.stream()
                .map(BizCompanySku::getProfitRate)
                .filter(Objects::nonNull)
                .reduce(priceCalculationService::getLowerPrice)
                .orElse(null);
        
        BigDecimal maxProfitRate = companySkuList.stream()
                .map(BizCompanySku::getProfitRate)
                .filter(Objects::nonNull)
                .reduce(priceCalculationService::getHigherPrice)
                .orElse(null);
        
        // 查找并更新客户SPU
        BizCompanySpu companySpu = bizCompanySpuService.getOne(
            new LambdaQueryWrapper<BizCompanySpu>()
                .eq(BizCompanySpu::getCompanyId, companyId)
                .eq(BizCompanySpu::getSpuId, spuId)
                .last("LIMIT 1")
        );
        
        if (companySpu != null) {
            companySpu.setSalePriceMin(minPrice);
            companySpu.setSalePriceMax(maxPrice);
            companySpu.setProfitRateMin(minProfitRate);
            companySpu.setProfitRateMax(maxProfitRate);
            companySpu.setUpdateTime(LocalDateTime.now());
            bizCompanySpuService.updateById(companySpu);
            
            log.info("更新客户SPU价格和利润率范围，公司ID: {}, SPU ID: {}, 价格范围: {}, 利润率范围: {}", 
                    companyId, spuId, 
                    priceCalculationService.calculatePriceRange(minPrice, maxPrice),
                    priceCalculationService.calculateProfitRateRange(minProfitRate, maxProfitRate));
        }
    }

    /**
     * 仅更新客户SPU的利润率范围（供应商成本价变更后）
     */
    private void updateCompanySpuProfitRateRange(Long companyId, Long spuId) {
        // 查找该公司该SPU下的所有客户SKU
        List<BizCompanySku> companySkuList = bizCompanySkuService.list(
            new LambdaQueryWrapper<BizCompanySku>()
                .eq(BizCompanySku::getCompanyId, companyId)
                .eq(BizCompanySku::getSpuId, spuId)
                .eq(BizCompanySku::getStatus, 1)
        );
        
        if (companySkuList.isEmpty()) {
            return;
        }
        
        // 使用公共服务计算利润率范围
        BigDecimal minProfitRate = companySkuList.stream()
                .map(BizCompanySku::getProfitRate)
                .filter(Objects::nonNull)
                .reduce(priceCalculationService::getLowerPrice)
                .orElse(null);
        
        BigDecimal maxProfitRate = companySkuList.stream()
                .map(BizCompanySku::getProfitRate)
                .filter(Objects::nonNull)
                .reduce(priceCalculationService::getHigherPrice)
                .orElse(null);
        
        // 查找并更新客户SPU的利润率范围
        BizCompanySpu companySpu = bizCompanySpuService.getOne(
            new LambdaQueryWrapper<BizCompanySpu>()
                .eq(BizCompanySpu::getCompanyId, companyId)
                .eq(BizCompanySpu::getSpuId, spuId)
                .last("LIMIT 1")
        );
        
        if (companySpu != null) {
            companySpu.setProfitRateMin(minProfitRate);
            companySpu.setProfitRateMax(maxProfitRate);
            companySpu.setUpdateTime(LocalDateTime.now());
            bizCompanySpuService.updateById(companySpu);
            
            log.info("更新客户SPU利润率范围，公司ID: {}, SPU ID: {}, 利润率范围: {}", 
                    companyId, spuId, priceCalculationService.calculateProfitRateRange(minProfitRate, maxProfitRate));
        }
    }

    /**
     * 转换为价格变更响应DTO
     */
    private PriceChangeReviewResponse convertToPriceChangeResponse(BizPriceSpecChangeRequest changeRequest, Long companyId) {
        PriceChangeReviewResponse response = new PriceChangeReviewResponse();
        
        // 基本信息
        response.setId(changeRequest.getId());
        response.setRequestNo(changeRequest.getRequestNo());
        response.setSupplierId(changeRequest.getSupplierId());
        response.setSupplierName(changeRequest.getSupplierName());
        response.setStatus(changeRequest.getStatus());
        response.setReason(changeRequest.getRequestReason());
        response.setReviewTime(changeRequest.getReviewTime());
        response.setReviewerId(changeRequest.getReviewerId());
        response.setCreateTime(changeRequest.getCreateTime());
        response.setUpdateTime(changeRequest.getUpdateTime());
        
        // 获取供应商信息 - 这里仍然需要单独查询，但可以在批量转换时优化
        BizSupplier supplier = bizSupplierService.getById(changeRequest.getSupplierId());
        if (supplier != null) {
            // 平台供应商统一显示为"平台直供"
            if (supplier.getSupplierType() == 2) {
                response.setSupplierName("平台直供");
            } else {
                response.setSupplierName(supplier.getName());
            }
        }
        
        // 获取第一个商品的信息用于列表显示
        List<BizPriceSpecChangeRequestProduct> productRequests = bizPriceSpecChangeRequestProductService.list(
            new LambdaQueryWrapper<BizPriceSpecChangeRequestProduct>()
                .eq(BizPriceSpecChangeRequestProduct::getRequestId, changeRequest.getId())
                .last("LIMIT 1")
        );
        
        if (!productRequests.isEmpty()) {
            BizPriceSpecChangeRequestProduct firstProduct = productRequests.get(0);
            
            // 获取商品基本信息
            BizSupplierSpu supplierSpu = bizSupplierSpuService.getById(firstProduct.getSpuId());
            if (supplierSpu != null) {
                response.setProductName(supplierSpu.getName());
                response.setSpuCode(supplierSpu.getSpuCode());
                response.setMainImgUrl(supplierSpu.getMainImgUrl());
                response.setCategoryId(supplierSpu.getCategoryId());
            }
            
            // 获取第一个SKU的价格信息用于显示
            List<BizPriceSpecChangeRequestSku> skuRequests = bizPriceSpecChangeRequestSkuService.list(
                new LambdaQueryWrapper<BizPriceSpecChangeRequestSku>()
                    .eq(BizPriceSpecChangeRequestSku::getProductRequestId, firstProduct.getId())
                    .last("LIMIT 1")
            );
            
            if (!skuRequests.isEmpty()) {
                BizPriceSpecChangeRequestSku firstSku = skuRequests.get(0);
                // 使用采购价作为主要显示价格
                response.setOldPrice(firstSku.getOldPurchasePrice());
                response.setNewPrice(firstSku.getNewPurchasePrice());
                response.setChangeReason(firstSku.getChangeReason());
            }
            
            response.setChangeType(firstProduct.getChangeType());
        }
        
        // 设置时间字段别名
        response.setSubmitTime(changeRequest.getSubmitTime());
        response.setReviewStatus(changeRequest.getStatus());
        
        return response;
    }

    /**
     * 转换为价格变更详情响应DTO
     */
    private PriceChangeReviewResponse convertToPriceChangeDetailResponse(BizPriceSpecChangeRequest changeRequest, Long companyId) {
        PriceChangeReviewResponse response = convertToPriceChangeResponse(changeRequest, companyId);
        
        // 获取商品明细
        List<BizPriceSpecChangeRequestProduct> productRequests = bizPriceSpecChangeRequestProductService.list(
            new LambdaQueryWrapper<BizPriceSpecChangeRequestProduct>()
                .eq(BizPriceSpecChangeRequestProduct::getRequestId, changeRequest.getId())
        );
        
        List<PriceChangeReviewResponse.ProductChangeInfo> productInfoList = new ArrayList<>();
        for (BizPriceSpecChangeRequestProduct productRequest : productRequests) {
            PriceChangeReviewResponse.ProductChangeInfo productInfo = convertToProductChangeInfo(productRequest, companyId);
            productInfoList.add(productInfo);
        }
        
        response.setProductList(productInfoList);
        
        // 对于详情页，补充完整的商品信息
        if (!productRequests.isEmpty()) {
            BizPriceSpecChangeRequestProduct firstProduct = productRequests.get(0);
            
            // 获取第一个SKU的完整信息用于详情页显示
            List<BizPriceSpecChangeRequestSku> skuRequests = bizPriceSpecChangeRequestSkuService.list(
                new LambdaQueryWrapper<BizPriceSpecChangeRequestSku>()
                    .eq(BizPriceSpecChangeRequestSku::getProductRequestId, firstProduct.getId())
                    .last("LIMIT 1")
            );
            
            if (!skuRequests.isEmpty()) {
                BizPriceSpecChangeRequestSku firstSku = skuRequests.get(0);
                
                // 设置SKU相关信息
                response.setSkuCode(firstSku.getSkuCode());
                
                // 组装规格文本
                StringBuilder specText = new StringBuilder();
                if (firstSku.getSpec1Value() != null && !firstSku.getSpec1Value().trim().isEmpty()) {
                    specText.append(firstSku.getSpec1Value());
                }
                if (firstSku.getSpec2Value() != null && !firstSku.getSpec2Value().trim().isEmpty()) {
                    if (!specText.isEmpty()) specText.append(" / ");
                    specText.append(firstSku.getSpec2Value());
                }
                if (firstSku.getSpec3Value() != null && !firstSku.getSpec3Value().trim().isEmpty()) {
                    if (!specText.isEmpty()) specText.append(" / ");
                    specText.append(firstSku.getSpec3Value());
                }
                response.setSpecText(specText.toString());
                
                // 设置详细价格信息
                response.setOldCostPrice(firstSku.getOldCostPrice());
                response.setNewCostPrice(firstSku.getNewCostPrice());
                response.setOldPurchasePrice(firstSku.getOldPurchasePrice());
                response.setNewPurchasePrice(firstSku.getNewPurchasePrice());
                response.setOldStrikethroughPrice(firstSku.getOldStrikethroughPrice());
                response.setNewStrikethroughPrice(firstSku.getNewStrikethroughPrice());
            }
        }
        
        return response;
    }

    /**
     * 转换为商品变更信息
     */
    private PriceChangeReviewResponse.ProductChangeInfo convertToProductChangeInfo(BizPriceSpecChangeRequestProduct productRequest, Long companyId) {
        PriceChangeReviewResponse.ProductChangeInfo productInfo = new PriceChangeReviewResponse.ProductChangeInfo();
        BeanUtils.copyProperties(productRequest, productInfo);
        
        // 获取商品信息
        BizSupplierSpu supplierSpu = bizSupplierSpuService.getById(productRequest.getSpuId());
        if (supplierSpu != null) {
            productInfo.setSpuName(supplierSpu.getName());
            productInfo.setSpuCode(supplierSpu.getSpuCode());
        }
        
        // 获取SKU变更明细
        List<BizPriceSpecChangeRequestSku> skuRequests = bizPriceSpecChangeRequestSkuService.list(
            new LambdaQueryWrapper<BizPriceSpecChangeRequestSku>()
                .eq(BizPriceSpecChangeRequestSku::getProductRequestId, productRequest.getId())
        );
        
        List<PriceChangeReviewResponse.SkuChangeInfo> skuInfoList = skuRequests.stream()
                .map(skuReq -> convertToSkuChangeInfo(skuReq, companyId))
                .collect(Collectors.toList());
        
        productInfo.setSkuList(skuInfoList);
        
        return productInfo;
    }

    /**
     * 转换为SKU变更信息
     */
    private PriceChangeReviewResponse.SkuChangeInfo convertToSkuChangeInfo(BizPriceSpecChangeRequestSku skuRequest, Long companyId) {
        PriceChangeReviewResponse.SkuChangeInfo skuInfo = new PriceChangeReviewResponse.SkuChangeInfo();
        BeanUtils.copyProperties(skuRequest, skuInfo);
        
        // 设置SKU编码
        skuInfo.setSkuCode(skuRequest.getSkuCode());
        
        // 设置操作类型
        skuInfo.setOperationType(skuRequest.getOperationType());
        skuInfo.setOperationTypeName(getOperationTypeName(skuRequest.getOperationType()));
        
        // 设置规格值
        skuInfo.setSpec1Value(skuRequest.getSpec1Value());
        skuInfo.setSpec2Value(skuRequest.getSpec2Value());
        skuInfo.setSpec3Value(skuRequest.getSpec3Value());
        
        // 设置规格组合文本
        StringBuilder specCombination = new StringBuilder();
        if (skuRequest.getSpec1Value() != null && !skuRequest.getSpec1Value().trim().isEmpty()) {
            specCombination.append(skuRequest.getSpec1Value().trim());
        }
        if (skuRequest.getSpec2Value() != null && !skuRequest.getSpec2Value().trim().isEmpty()) {
            if (!specCombination.isEmpty()) specCombination.append(" / ");
            specCombination.append(skuRequest.getSpec2Value().trim());
        }
        if (skuRequest.getSpec3Value() != null && !skuRequest.getSpec3Value().trim().isEmpty()) {
            if (!specCombination.isEmpty()) specCombination.append(" / ");
            specCombination.append(skuRequest.getSpec3Value().trim());
        }
        skuInfo.setSpecCombination(specCombination.toString());
        
        // 设置当前售价（从公司定制SKU价格表获取）
        if (skuRequest.getSkuId() != null && companyId != null) {
            BizCompanySku companySku = bizCompanySkuService.getOne(
                new LambdaQueryWrapper<BizCompanySku>()
                    .eq(BizCompanySku::getCompanyId, companyId)
                    .eq(BizCompanySku::getSkuId, skuRequest.getSkuId())
                    .eq(BizCompanySku::getStatus, 1)
                    .last("LIMIT 1")
            );
            if (companySku != null) {
                skuInfo.setCurrentSalePrice(companySku.getSalePrice());
            }
        }
        
        return skuInfo;
    }
    
    /**
     * 获取操作类型名称
     */
    private String getOperationTypeName(Integer operationType) {
        if (operationType == null) return "修改";
        return switch (operationType) {
            case 1 -> "新增";
            case 2 -> "修改";
            case 3 -> "删除";
            default -> "修改";
        };
    }
} 