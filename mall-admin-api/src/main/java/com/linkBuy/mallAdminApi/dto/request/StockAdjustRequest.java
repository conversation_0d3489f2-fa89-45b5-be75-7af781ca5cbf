package com.linkBuy.mallAdminApi.dto.request;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * 库存调整请求
 */
@Data
public class StockAdjustRequest {

    @NotBlank(message = "调整库存原因不能为空")
    private String updateReason;

    @NotBlank(message = "调整类型不能为空")
    @Pattern(regexp = "^(increase|decrease|set)$", message = "调整类型只能是 increase、decrease、set")
    private String updateType;

    @NotEmpty(message = "SKU调整详情不能为空")
    @Valid
    private List<SkuAdjustments> skuAdjustments;

    /**
     * SKU调整详情内部类
     */
    @Data
    public static class SkuAdjustments {

        @NotNull(message = "SKU ID不能为空")
        private Long skuId;

        @NotNull(message = "调整数量不能为空")
        private Integer adjustAmount;
    }
} 