package com.linkBuy.mallAdminApi.dto.response;

import lombok.Data;

/**
 * 优惠券统计响应DTO
 */
@Data
public class CouponStatisticsResponse {

    /**
     * 总数
     */
    private Long totalCount;

    /**
     * 未开始数量
     */
    private Long notStartedCount;

    /**
     * 进行中数量
     */
    private Long ongoingCount;

    /**
     * 已结束数量
     */
    private Long endedCount;

    public CouponStatisticsResponse() {
    }

    public CouponStatisticsResponse(Long totalCount, Long notStartedCount, Long ongoingCount, Long endedCount) {
        this.totalCount = totalCount;
        this.notStartedCount = notStartedCount;
        this.ongoingCount = ongoingCount;
        this.endedCount = endedCount;
    }
} 