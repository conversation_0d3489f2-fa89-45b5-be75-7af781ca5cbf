package com.linkBuy.mallAdminApi.dto.response;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户商品管理响应DTO
 */
@Data
public class CompanyProductManagementResponse {
    
    /**
     * 客户SPU ID
     */
    private Long id;
    
    /**
     * 供应商SPU ID
     */
    private Long spuId;
    
    /**
     * SPU编码
     */
    private String spuCode;
    
    /**
     * 商品名称
     */
    private String productName;
    
    /**
     * 商品描述
     */
    private String description;
    
    /**
     * 主图URL
     */
    private String mainImageUrl;
    
    /**
     * 客户公司ID
     */
    private Long companyId;
    
    /**
     * 供应商ID
     */
    private Long supplierId;
    
    /**
     * 供应商名称
     */
    private String supplierName;
    
    /**
     * 供应商类型：1-客户供应商，2-平台供应商
     */
    private Integer supplierType;
    
    /**
     * 分类ID
     */
    private Long categoryId;
    
    /**
     * 划线价
     */
    private BigDecimal strikethroughPrice;
    
    /**
     * 销售价 - 最低
     */
    private BigDecimal salePriceMin;
    
    /**
     * 销售价 - 最高
     */
    private BigDecimal salePriceMax;
    
    /**
     * 利润率 - 最低
     */
    private BigDecimal profitRateMin;
    
    /**
     * 利润率 - 最高
     */
    private BigDecimal profitRateMax;
    
    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;
    
    /**
     * 来源：1-平台下发商品，2-自有供应商商品
     */
    private Integer source;
    
    /**
     * SKU数量
     */
    private Integer skuCount;
    
    /**
     * 总库存
     */
    private Integer totalStock;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 条形码
     */
    private String barcode;
    
    /**
     * 商品单位
     */
    private String unit;
    
    /**
     * 商品类型：1-实物商品，2-虚拟商品
     */
    private Integer type;
    
    /**
     * 物理属性 - 长度(cm)
     */
    private BigDecimal length;
    
    /**
     * 物理属性 - 宽度(cm)
     */
    private BigDecimal wide;
    
    /**
     * 物理属性 - 高度(cm)
     */
    private BigDecimal tall;
    
    /**
     * 物理属性 - 体积(cm³)
     */
    private BigDecimal volume;
    
    /**
     * 物理属性 - 重量(kg)
     */
    private BigDecimal weight;
    
    /**
     * 运费类型：unified-统一运费，template-运费模板
     */
    private String shippingType;
    
    /**
     * 运费金额
     */
    private BigDecimal shippingFee;
    
    /**
     * 运费模板ID
     */
    private Long shippingTemplateId;
    
    /**
     * 售后服务类型：no_return-不可退换，support_return-支持退换
     */
    private String afterSaleType;
    
    /**
     * 支持的售后服务
     */
    private String afterSaleServices;
    
    /**
     * 商品图片列表
     */
    private List<ProductImage> images;
    
    /**
     * SKU列表（详情页使用）
     */
    private List<CompanySkuInfo> skuList;
    
    /**
     * 商品图片信息
     */
    @Data
    public static class ProductImage {
        /**
         * 图片URL
         */
        private String imgUrl;
        
        /**
         * 是否主图
         */
        private Boolean isMain;
        
        /**
         * 排序
         */
        private Integer sort;
    }
    
    /**
     * 客户SKU信息
     */
    @Data
    public static class CompanySkuInfo {
        
        /**
         * 客户SKU ID
         */
        private Long id;
        
        /**
         * 供应商SKU ID
         */
        private Long skuId;
        
        /**
         * SKU编码
         */
        private String skuCode;
        
        /**
         * 规格1值
         */
        private String spec1Value;
        
        /**
         * 规格2值
         */
        private String spec2Value;
        
        /**
         * 规格3值
         */
        private String spec3Value;
        
        /**
         * 成本价
         */
        private BigDecimal costPrice;
        
        /**
         * 采购价
         */
        private BigDecimal purchasePrice;
        
        /**
         * 销售价格（客户定制价格）
         */
        private BigDecimal salePrice;
        
        /**
         * 划线价
         */
        private BigDecimal strikethroughPrice;
        
        /**
         * 利润率(%)
         */
        private BigDecimal profitRate;
        
        /**
         * 最小订购量
         */
        private Integer minOrderQuantity;
        
        /**
         * 最大订购量
         */
        private Integer maxOrderQuantity;
        
        /**
         * 总库存
         */
        private Integer totalInventory;
        
        /**
         * 可用库存
         */
        private Integer availableInventory;
        
        /**
         * 锁定库存
         */
        private Integer blockedInventory;
        
        /**
         * 安全库存
         */
        private Integer safetyStock;
        
        /**
         * SKU图片URL
         */
        private String imgUrl;
        
        /**
         * 状态：0-禁用，1-启用
         */
        private Integer status;
        
        /**
         * 创建时间
         */
        private LocalDateTime createTime;
        
        /**
         * 更新时间
         */
        private LocalDateTime updateTime;
    }
} 