#!/bin/sh

className=$1

if [ -z $1 ];then
  className=com.linkBuy.platformApi.PlatformApiApplication
fi

waittime=0
while [ true ]; do
    PIDS=`ps -ef | grep "$className" | grep "java" |awk '{print $2}'`
    echo $PIDS
    if [ -n "$PIDS" ]; then
       echo "The $className already started, try to stop it!"
        echo "kill PID: $PIDS"
        kill $PIDS
        waittime=$(($waittime+1))
        echo $waittime
        if [ "$waittime" -gt "10" ]; then
          echo "The $className will be killed "
          kill -9 $PIDS
        fi
        sleep 1
    else
        echo "The $className stopped "
        break;
    fi
done

