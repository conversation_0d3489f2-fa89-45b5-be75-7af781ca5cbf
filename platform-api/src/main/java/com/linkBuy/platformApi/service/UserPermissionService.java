package com.linkBuy.platformApi.service;

import com.linkBuy.mysql.dao.entity.system.SysMenu;
import com.linkBuy.mysql.dao.entity.system.SysRoleMenu;
import com.linkBuy.mysql.dao.entity.system.SysUserRole;
import com.linkBuy.mysql.dao.service.system.SysMenuService;
import com.linkBuy.mysql.dao.service.system.SysRoleMenuService;
import com.linkBuy.mysql.dao.service.system.SysUserRoleService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class UserPermissionService {
    private final SysRoleMenuService roleMenuService;
    private final SysUserRoleService userRoleService;
    private final SysMenuService sysMenuService;


    public List<SysMenu> getUserMenus(Long userId) {
        // 获取用户角色
        List<SysUserRole> userRoles = userRoleService.listByUserId(userId);
        List<Long> roleIds = userRoles.stream()
                .map(SysUserRole::getRoleId)
                .collect(Collectors.toList());

        // 获取角色对应的菜单权限
        List<SysRoleMenu> roleMenus = roleMenuService.listByRoleIds(roleIds);
        List<Long> menuIds = roleMenus.stream()
                .map(SysRoleMenu::getMenuId)
                .collect(Collectors.toList());

        // 获取菜单详情
        return sysMenuService.listByIds(menuIds);
    }
}
