package com.linkBuy.platformApi.controller.system;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linkBuy.common.dto.request.*;
import com.linkBuy.common.dto.request.IdRequest;
import com.linkBuy.common.service.AliyunOssService;
import com.linkBuy.mysql.dao.entity.system.SysFsAccountFile;
import com.linkBuy.mysql.dao.entity.system.SysFsAccountTag;
import com.linkBuy.mysql.dao.service.system.SysFsAccountFileService;
import com.linkBuy.mysql.dao.service.system.SysFsAccountTagService;
import com.linkBuy.common.annotation.ResponseWrapper;
import groovy.util.logging.Slf4j;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@lombok.extern.slf4j.Slf4j
@RestController
@RequestMapping("/imageCenter")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@ResponseWrapper
@Slf4j
public class ImageCenterController {
    private final SysFsAccountTagService tagService;
    private final SysFsAccountFileService fileService;
    private final AliyunOssService aliyunOssService;

    private final String FS_ACCOUNT_CODE = "PLATFORM";

    // 1. 获取分组列表
    @GetMapping("/group/list")
    public List<SysFsAccountTag> listGroups() {
        LambdaQueryWrapper<SysFsAccountTag> qw = new LambdaQueryWrapper<>();
        qw.eq(SysFsAccountTag::getFsAccountCode, FS_ACCOUNT_CODE);
        return tagService.list(qw);
    }

    // 2. 新增分组
    @PostMapping("/group/add")
    public SysFsAccountTag addGroup(@Valid @RequestBody NameRequest req) {
        // 检查同账号下分组名唯一
        LambdaQueryWrapper<SysFsAccountTag> qw = new LambdaQueryWrapper<>();
        qw.eq(SysFsAccountTag::getFsAccountCode, FS_ACCOUNT_CODE)
                .eq(SysFsAccountTag::getName, req.getName());
        if (tagService.count(qw) > 0) {
            throw new IllegalArgumentException("分组名称已存在");
        }
        SysFsAccountTag tag = new SysFsAccountTag();
        tag.setFsAccountCode(FS_ACCOUNT_CODE);
        tag.setName(req.getName());
        tagService.save(tag);
        return tag;
    }

    // 3. 重命名分组
    @PostMapping("/group/rename")
    public boolean renameGroup(@Valid @RequestBody IdNameRequest req) {
        SysFsAccountTag tag = tagService.getById(req.getId());
        if (tag == null) return false;
        // 检查同账号下分组名唯一（排除自己）
        LambdaQueryWrapper<SysFsAccountTag> qw = new LambdaQueryWrapper<>();
        qw.eq(SysFsAccountTag::getFsAccountCode, FS_ACCOUNT_CODE)
                .eq(SysFsAccountTag::getName, req.getName())
                .ne(SysFsAccountTag::getId, req.getId());
        if (tagService.count(qw) > 0) {
            throw new IllegalArgumentException("分组名称已存在");
        }
        tag.setName(req.getName());
        return tagService.updateById(tag);
    }

    // 4. 删除分组
    @PostMapping("/group/delete")
    public boolean deleteGroup(@Valid @RequestBody IdRequest req) {
        return tagService.removeByIdAndAccountCode(req.getId(), FS_ACCOUNT_CODE);
    }

    // 5. 获取图片列表（支持分组、分页、名称过滤）
    @GetMapping("/file/list")
    public IPage<SysFsAccountFile> listFiles(
            @RequestParam(required = false) String tagId,
            @RequestParam(required = false) String name,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int pageSize
    ) {
        LambdaQueryWrapper<SysFsAccountFile> qw = new LambdaQueryWrapper<>();
        qw.eq(SysFsAccountFile::getFsAccountCode, FS_ACCOUNT_CODE);
        if (tagId != null) {
            // 处理全部和未分组
            String SPECIAL_TAG_ID_UNGROUP = "ungroup";
            String SPECIAL_TAG_ID_ALL = "all";
            if (SPECIAL_TAG_ID_UNGROUP.equals(tagId)) {
                qw.isNull(SysFsAccountFile::getTagId);
            } else if (SPECIAL_TAG_ID_ALL.equals(tagId)) {
                // TO NOTHING
                log.debug("list all images");
            } else {
                qw.eq(SysFsAccountFile::getTagId, tagId);
            }
        }
        if (name != null && !name.isEmpty()) {
            qw.like(SysFsAccountFile::getName, name);
        }
        qw.orderByDesc(SysFsAccountFile::getId);

        // 使用MyBatis-Plus的分页功能
        return fileService.page(new Page<>(page, pageSize), qw);
    }

    // 6. 上传图片 - 修复为实际文件上传
    @PostMapping("/file/upload")
    public SysFsAccountFile uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "tagId", required = false) String tagId
    ) throws IOException {
        SysFsAccountFile sysFsAccountFile = handleUpload(file, tagId);
        fileService.save(sysFsAccountFile);
        return sysFsAccountFile;
    }

    // 6.1 批量上传图片 - 修复为实际文件上传
    @PostMapping("/file/batchUpload")
    public List<SysFsAccountFile> batchUploadFile(
            @RequestParam("files") MultipartFile[] files,
            @RequestParam(value = "tagId", required = false) String tagId
    ) throws IOException {
        if (files == null || files.length == 0) {
            throw new IllegalArgumentException("请选择要上传的文件");
        }

        List<SysFsAccountFile> uploadedFiles = new ArrayList<>();

        for (MultipartFile file : files) {
            uploadedFiles.add(handleUpload(file, tagId));
        }

        // 批量保存到数据库
        fileService.saveBatch(uploadedFiles);
        return uploadedFiles;
    }

    private SysFsAccountFile handleUpload(MultipartFile file, String tagId) throws IOException {
        if (!file.isEmpty()) {
            // 上传文件到OSS
            AliyunOssService.OssUploadResult uploadResult = aliyunOssService.upload(file, FS_ACCOUNT_CODE, null);

            // 保存文件记录到数据库
            SysFsAccountFile fileRecord = new SysFsAccountFile();
            fileRecord.setFsAccountCode(FS_ACCOUNT_CODE);
            fileRecord.setPath(uploadResult.getFileName());
            fileRecord.setOssKey(uploadResult.getOssKey());
            fileRecord.setName(file.getOriginalFilename());
            fileRecord.setType(file.getContentType());
            if (tagId != null && !tagId.isEmpty() && !"all".equals(tagId) && !"ungroup".equals(tagId)) {
                fileRecord.setTagId(tagId);
            }
            return fileRecord;
        } else {
            throw new RuntimeException("上传失败");
        }
    }

    // 7. 删除图片
    @PostMapping("/file/delete")
    public boolean deleteFile(@Valid @RequestBody IdRequest req) {
        return fileService.removeById(req.getId());
    }

    // 8. 批量删除图片
    @PostMapping("/file/batchDelete")
    public boolean batchDeleteFile(@Valid @RequestBody IdsRequest req) {
        return fileService.removeByIds(req.getIds());
    }

    // 9. 批量移动图片
    @PostMapping("/file/batchMove")
    public boolean batchMoveFile(@Valid @RequestBody FileBatchMoveRequest req) {
        List<SysFsAccountFile> files = fileService.listByIds(req.getIds());
        for (SysFsAccountFile file : files) {
            file.setTagId(req.getTargetTagId());
        }
        return fileService.updateBatchById(files);
    }

    // 10. 重命名图片
    @PostMapping("/file/rename")
    public boolean renameFile(@Valid @RequestBody IdNameRequest req) {
        SysFsAccountFile file = fileService.getById(req.getId());
        if (file == null) return false;
        file.setName(req.getName());
        return fileService.updateById(file);
    }
}
