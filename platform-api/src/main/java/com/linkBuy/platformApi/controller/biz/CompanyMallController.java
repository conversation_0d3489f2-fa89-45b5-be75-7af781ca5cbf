package com.linkBuy.platformApi.controller.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linkBuy.common.annotation.ResponseWrapper;
import com.linkBuy.common.exception.BizException;
import com.linkBuy.mysql.dao.entity.biz.BizCompany;
import com.linkBuy.mysql.dao.entity.biz.BizCompanyMall;
import com.linkBuy.mysql.dao.service.biz.BizCompanyMallService;
import com.linkBuy.mysql.dao.service.biz.BizCompanyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 客户商城管理控制器
 */
@RestController
@RequestMapping("/biz/mall")
@ResponseWrapper
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CompanyMallController {

    private final BizCompanyMallService bizCompanyMallService;
    private final BizCompanyService bizCompanyService;
    
    @Value("${mall.base-url:https://mall.linkbuy.com}")
    private String mallBaseUrl;

    /**
     * 分页查询商城列表
     *
     * @param page      页码
     * @param size      每页大小
     * @param name      商城名称
     * @param companyId 客户ID
     * @param status    状态
     * @return 商城分页数据
     */
    @GetMapping("/list")
    public IPage<BizCompanyMall> getMallList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) Long companyId,
            @RequestParam(required = false) Integer status) {

        Page<BizCompanyMall> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<BizCompanyMall> wrapper = new LambdaQueryWrapper<>();

        // 搜索条件
        if (StringUtils.hasText(name)) {
            wrapper.like(BizCompanyMall::getName, name);
        }
        if (companyId != null) {
            wrapper.eq(BizCompanyMall::getCompanyId, companyId);
        }
        if (status != null) {
            wrapper.eq(BizCompanyMall::getStatus, status);
        }

        // 按创建时间倒序
        wrapper.orderByDesc(BizCompanyMall::getCreateTime);

        return bizCompanyMallService.page(pageParam, wrapper);
    }

    /**
     * 获取商城详情
     *
     * @param id 商城ID
     * @return 商城详情
     */
    @GetMapping("/{id}")
    public BizCompanyMall getMallDetail(@PathVariable Long id) {
        BizCompanyMall mall = bizCompanyMallService.getById(id);
        if (mall == null) {
            throw new BizException("商城不存在");
        }
        return mall;
    }
    
    /**
     * 根据客户ID获取商城列表
     *
     * @param companyId 客户ID
     * @return 商城列表
     */
    @GetMapping("/by-company/{companyId}")
    public List<BizCompanyMall> getMallsByCompanyId(@PathVariable Long companyId) {
        return bizCompanyMallService.getByCompanyId(companyId);
    }

    /**
     * 新增商城
     *
     * @param mall 商城信息
     * @return 新增的商城
     */
    @PostMapping
    public BizCompanyMall addMall(@RequestBody BizCompanyMall mall) {
        // 验证客户是否存在
        BizCompany company = bizCompanyService.getById(mall.getCompanyId());
        if (company == null) {
            throw new BizException("客户不存在");
        }

        // 验证商城名称是否重复
        BizCompanyMall existingMall = bizCompanyMallService.getByNameAndCompanyId(mall.getName(), mall.getCompanyId());
        if (existingMall != null) {
            throw new BizException("同一客户下商城名称不能重复：" + mall.getName());
        }

        // 设置默认状态为启用
        if (mall.getStatus() == null) {
            mall.setStatus(1);
        }
        
        // 生成商城编码
        mall.setCode(generateMallCode());
        
        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        mall.setCreateTime(now);
        mall.setUpdateTime(now);
        mall.setIsDeleted(0);

        // 保存商城
        bizCompanyMallService.save(mall);
        
        log.info("新增客户商城：客户ID={}, 商城名称={}, 商城编码={}", 
                mall.getCompanyId(), mall.getName(), mall.getCode());

        return mall;
    }

    /**
     * 更新商城
     *
     * @param id   商城ID
     * @param mall 商城信息
     * @return 更新后的商城
     */
    @PutMapping("/{id}")
    public BizCompanyMall updateMall(@PathVariable Long id, @RequestBody BizCompanyMall mall) {
        // 检查商城是否存在
        BizCompanyMall existingMall = bizCompanyMallService.getById(id);
        if (existingMall == null) {
            throw new BizException("商城不存在");
        }

        // 验证商城名称是否重复（排除自身）
        if (!existingMall.getName().equals(mall.getName())) {
            BizCompanyMall nameCheck = bizCompanyMallService.getByNameAndCompanyId(mall.getName(), existingMall.getCompanyId());
            if (nameCheck != null && !nameCheck.getId().equals(id)) {
                throw new BizException("同一客户下商城名称不能重复：" + mall.getName());
            }
        }

        // 设置ID和客户ID（不允许修改客户ID）
        mall.setId(id);
        mall.setCompanyId(existingMall.getCompanyId());
        mall.setCode(existingMall.getCode()); // 商城编码不允许修改
        mall.setUpdateTime(LocalDateTime.now());

        // 更新商城
        bizCompanyMallService.updateById(mall);
        
        log.info("更新客户商城：ID={}, 商城名称={}", id, mall.getName());

        return bizCompanyMallService.getById(id);
    }

    /**
     * 删除商城
     *
     * @param id 商城ID
     */
    @DeleteMapping("/{id}")
    public void deleteMall(@PathVariable Long id) {
        // 检查商城是否存在
        BizCompanyMall mall = bizCompanyMallService.getById(id);
        if (mall == null) {
            throw new BizException("商城不存在");
        }

        // 执行删除（逻辑删除）
        bizCompanyMallService.removeById(id);
        
        log.info("删除客户商城：ID={}, 商城名称={}", id, mall.getName());
    }

    /**
     * 更新商城状态
     *
     * @param id     商城ID
     * @param status 状态信息
     */
    @PostMapping("/{id}/status")
    public void updateMallStatus(@PathVariable Long id, @RequestBody Map<String, Integer> status) {
        // 检查商城是否存在
        BizCompanyMall mall = bizCompanyMallService.getById(id);
        if (mall == null) {
            throw new BizException("商城不存在");
        }

        // 更新状态
        Integer newStatus = status.get("status");
        if (newStatus == null || (newStatus != 0 && newStatus != 1)) {
            throw new BizException("无效的状态值，只能为0或1");
        }

        bizCompanyMallService.updateStatus(id, newStatus);
        
        String statusText = newStatus == 1 ? "启用" : "禁用";
        log.info("{}客户商城：ID={}, 商城名称={}", statusText, id, mall.getName());
    }

    /**
     * 生成商城访问链接
     *
     * @param id 商城ID
     * @return 商城链接信息
     */
    @GetMapping("/{id}/link")
    public Map<String, String> generateMallLink(@PathVariable Long id) {
        // 检查商城是否存在
        BizCompanyMall mall = bizCompanyMallService.getById(id);
        if (mall == null) {
            throw new BizException("商城不存在");
        }

        // 检查商城状态
        if (mall.getStatus() != 1) {
            throw new BizException("商城已禁用，无法生成链接");
        }

        // 生成商城链接
        String mallUrl = mallBaseUrl + "/" + mall.getCode();
        
        Map<String, String> result = new HashMap<>();
        result.put("mallId", mall.getId().toString());
        result.put("mallName", mall.getName());
        result.put("mallCode", mall.getCode());
        result.put("mallUrl", mallUrl);
        
        log.info("生成商城链接：ID={}, 商城名称={}, 链接={}", id, mall.getName(), mallUrl);
        
        return result;
    }
    
    /**
     * 生成商城编码
     * 格式：MALL_ + 时间戳后8位 + UUID去掉横线后取前8位
     * 例如：MALL_12345678a1b2c3d4
     *
     * @return 商城编码
     */
    private String generateMallCode() {
        String code;
        int maxAttempts = 10; // 最大重试次数
        int attempts = 0;

        do {
            // 获取当前时间戳的后8位
            long timestamp = System.currentTimeMillis();
            String timestampSuffix = String.valueOf(timestamp).substring(5);

            // 生成UUID并取前8位（去掉横线）
            String uuid = UUID.randomUUID().toString().replace("-", "");
            String uuidPrefix = uuid.substring(0, 8);

            // 组合最终编码
            code = "MALL_" + timestampSuffix + uuidPrefix;

            attempts++;

            // 检查编码是否已存在
            BizCompanyMall existingMall = bizCompanyMallService.getByCode(code);
            if (existingMall == null) {
                break; // 编码唯一，跳出循环
            }

            if (attempts >= maxAttempts) {
                log.warn("生成商城编码重试次数达到上限，使用当前编码: {}", code);
                break;
            }

            // 如果编码重复，等待1毫秒后重试（确保时间戳不同）
            try {
                Thread.sleep(1);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        } while (true);

        log.info("生成商城编码: {}, 重试次数: {}", code, attempts);
        return code;
    }
} 