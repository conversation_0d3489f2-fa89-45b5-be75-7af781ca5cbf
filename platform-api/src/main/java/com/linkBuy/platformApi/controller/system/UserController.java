package com.linkBuy.platformApi.controller.system;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.linkBuy.mysql.dao.entity.system.SysRole;
import com.linkBuy.mysql.dao.entity.system.SysUser;
import com.linkBuy.mysql.dao.entity.system.SysUserRole;
import com.linkBuy.mysql.dao.service.system.SysRoleService;
import com.linkBuy.mysql.dao.service.system.SysUserRoleService;
import com.linkBuy.mysql.dao.service.system.SysUserService;
import com.linkBuy.common.annotation.ResponseWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/system/users")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@ResponseWrapper
public class UserController {

    private final SysUserService userService;
    private final SysUserRoleService userRoleService;
    private final SysRoleService roleService;

    /**
     * 获取用户列表
     */
    @GetMapping
    public IPage<SysUser> list(
        @RequestParam(defaultValue = "1") int page,
        @RequestParam(defaultValue = "10") int size,
        @RequestParam(required = false) String query
    ) {
        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(query)) {
            wrapper.like(SysUser::getName, query)
                  .or()
                  .like(SysUser::getEmail, query)
                  .or()
                  .like(SysUser::getMobile, query);
        }
        return userService.page(new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(page, size), wrapper);
    }

    /**
     * 获取用户详情
     */
    @GetMapping("/{id}")
    public SysUser getById(@PathVariable Long id) {
        return userService.getById(id);
    }

    /**
     * 添加用户
     */
    @PostMapping
    public void add(@RequestBody SysUser user) {
        Assert.notNull(user, "用户信息不能为空");
        // TODO: 生成一个随机密码，然后发送到账号的邮箱和手机号上
        userService.save(user);

    }

    /**
     * 更新用户
     */
    @PostMapping("/{id}")
    public void update(@PathVariable Long id, @RequestBody SysUser user) {
        Assert.notNull(user, "用户信息不能为空");
        user.setId(id);
        userService.updateById(user);
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    public void delete(@PathVariable Long id) {
        userService.removeById(id);
    }

    /**
     * 获取角色关联的用户ID列表
     */
    @GetMapping("/{id}/roles")
    public List<SysRole> getRoles(@PathVariable Long id) {
        List<SysUserRole> userRoles = userRoleService.listByUserId(id);
        if (CollectionUtils.isEmpty(userRoles)) {
            return null;
        } else {
            return roleService.listByIds(userRoles.stream().map(SysUserRole::getRoleId).toList());
        }
    }

    /**
     * 绑定角色
     */
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/{id}/bindRole")
    public void bindRole(@RequestBody List<Long> roleIds, @PathVariable Long id)  {
        // 1. 查询当前用户已绑定的角色
        List<SysUserRole> userRoles = userRoleService.listByUserId(id);
        List<Long> oldRoleIds = userRoles.stream().map(SysUserRole::getRoleId).toList();

        // 2. 计算需要新增和删除的角色
        // 新增
        List<Long> toAdd = roleIds.stream().filter(rid -> !oldRoleIds.contains(rid)).toList();
        // 删除
        List<Long> toRemove = oldRoleIds.stream().filter(rid -> !roleIds.contains(rid)).toList();

        // 3. 执行新增
        if (!toAdd.isEmpty()) {
            List<SysUserRole> addList = toAdd.stream().map(rid -> {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(id);
                ur.setRoleId(rid);
                return ur;
            }).toList();
            userRoleService.saveBatch(addList);
        }

        // 4. 执行删除
        if (!toRemove.isEmpty()) {
            userRoleService.removeByUserIdAndRoleIds(id, toRemove);
        }
    }
}
