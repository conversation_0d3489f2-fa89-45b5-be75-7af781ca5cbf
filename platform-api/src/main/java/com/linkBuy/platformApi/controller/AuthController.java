package com.linkBuy.platformApi.controller;

import cn.hutool.core.lang.Assert;
import com.linkBuy.common.annotation.ResponseWrapper;
import com.linkBuy.common.constants.SecurityConstants;
import com.linkBuy.common.dto.SessionUser;
import com.linkBuy.common.exception.AuthenticationException;
import com.linkBuy.common.service.SessionService;
import com.linkBuy.common.util.TokenUtil;
import com.linkBuy.mysql.dao.entity.system.SysMenu;
import com.linkBuy.mysql.dao.entity.system.SysUser;
import com.linkBuy.mysql.dao.service.system.SysMenuService;
import com.linkBuy.mysql.dao.service.system.SysUserRoleService;
import com.linkBuy.mysql.dao.service.system.SysUserService;
import com.linkBuy.platformApi.dto.request.LoginRequest;
import com.linkBuy.platformApi.service.UserPermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户控制器
 */
@Slf4j
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
@ResponseWrapper
public class AuthController {

    private final SysUserService userService;
    private final TokenUtil tokenUtil;
    private final SessionService sessionService;
    private final AuthenticationManager authenticationManager;
    private final SysUserRoleService userRoleService;
    private final SysMenuService menuService;
    private final UserPermissionService userPermissionService;

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public SessionUser login(@Validated @RequestBody LoginRequest request) {
        try {
            // 使用Spring Security进行认证
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(
                            request.getMobile(),
                            request.getPassword()));

            // 认证成功，设置认证信息到上下文
            SecurityContextHolder.getContext().setAuthentication(authentication);

            // 根据手机号查询用户
            SysUser user = userService.getUserByMobile(request.getMobile());

            // 检查用户状态
            if (user.getStatus() == null || user.getStatus() != 1) {
                throw new AuthenticationException("账号已被禁用，请联系管理员");
            }

            SessionUser sessionUser = new SessionUser();
            sessionUser.setUserId(user.getId());
            sessionUser.setName(user.getName());
            sessionUser.setMobile(user.getMobile());
            sessionUser.setEmail(user.getEmail());
            sessionUser.setLoginTime(System.currentTimeMillis());

            List<SysMenu> menuList = userPermissionService.getUserMenus(user.getId());
            sessionUser.setPermissions(menuList.stream().map(SysMenu::getCode).collect(Collectors.toList()));

            // 保存到 Redis
            sessionService.saveSession(user.getId(), sessionUser);

            // 生成token
            String token = tokenUtil.generateToken(sessionUser);
            sessionUser.setToken(token);

            sessionService.saveSession(user.getId(), sessionUser);

            return sessionUser;
        } catch (org.springframework.security.core.AuthenticationException e) {
            throw new AuthenticationException(e.getMessage());
        }
    }

    /**
     * 获取当前登录用户信息
     */
    @GetMapping("/info")
    @PreAuthorize("isAuthenticated()")
    public SessionUser userInfo(HttpServletRequest request) {
        return (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);
    }

    /**
     * 退出登录
     */
    @PostMapping("/logout")
    public void logout(HttpServletRequest request) {
        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser != null) {
            sessionService.removeSession(sessionUser.getUserId());
            // 清除安全上下文
            SecurityContextHolder.clearContext();
        }
    }

    @PostMapping("/setPassword")
    @PreAuthorize("isAuthenticated()")
    public void setPassword(@RequestBody Map<String, String> params, HttpServletRequest request) {
        String oldPassword = params.get("oldPassword");
        String newPassword = params.get("newPassword");
        Assert.notBlank(oldPassword, "原密码不能为空");
        Assert.notBlank(newPassword, "新密码不能为空");

        // 获取当前登录用户
        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null) {
            throw new AuthenticationException("未登录");
        }

        // 查询数据库用户
        SysUser user = userService.getById(sessionUser.getUserId());
        if (user == null) {
            throw new AuthenticationException("用户不存在");
        }

        // 校验原密码
        // 假设你用的是BCrypt加密
        org.springframework.security.crypto.password.PasswordEncoder passwordEncoder = new org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder();
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new AuthenticationException("原密码错误");
        }

        // 设置新密码
        user.setPassword(passwordEncoder.encode(newPassword));
        userService.updateById(user);
    }
}