package com.linkBuy.platformApi.controller;

import com.linkBuy.common.annotation.ResponseWrapper;
import com.linkBuy.common.exception.BizException;
import com.linkBuy.mysql.dao.entity.system.GenProductCategory;
import com.linkBuy.mysql.dao.service.system.GenProductCategoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/system")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@ResponseWrapper
public class ProductCategoryController {

    private final GenProductCategoryService genProductCategoryService;

    /**
     * 获取所有分类树
     */
    @GetMapping("/{categoryType}/allCategory")
    public List<CategoryDto> allCategory(@PathVariable String categoryType) {
        // 获取所有分类
        List<GenProductCategory> allCategories = genProductCategoryService.list();
        // 构建树形结构
        List<GenProductCategory> categoryTree = buildCategoryTree(allCategories);
        // 转换为DTO
        return categoryTree.stream().map(this::convertToDto).collect(Collectors.toList());
    }

    /**
     * 保存分类
     */
    @PostMapping("/{categoryType}/category/save")
    public CategoryDto saveCategory(@RequestBody CategoryDto categoryDto, @PathVariable String categoryType) {
        // 转换DTO为实体
        GenProductCategory category = convertToEntity(categoryDto);
        
        // 综合校验
        validateCategory(category);
        
        // 如果是新增，设置分类等级
        if (category.getId() == null) {
            if (category.getParentId() == null) {
                // 根分类
                category.setLevel(1);
            } else {
                // 子分类，获取父分类等级并+1
                GenProductCategory parent = genProductCategoryService.getById(category.getParentId());
                if (parent != null) {
                    category.setLevel(parent.getLevel() + 1);
                } else {
                    throw new BizException("父分类不存在");
                }
            }
        }
        
        genProductCategoryService.saveOrUpdate(category);
        return convertToDto(category);
    }

    /**
     * 获取分类详情
     */
    @GetMapping("/{categoryType}/category/{id}")
    public CategoryDto getCategoryDetail(@PathVariable Long id, @PathVariable String categoryType) {
        GenProductCategory category = genProductCategoryService.getById(id);
        return convertToDto(category);
    }

    /**
     * 批量更新分类结构（包括层级和排序）
     */
    @PostMapping("/{categoryType}/category/structure")
    public void updateCategoryStructure(@RequestBody List<CategoryStructureDto> categoryStructures, @PathVariable String categoryType) {
        // 校验输入数据
        if (categoryStructures == null || categoryStructures.isEmpty()) {
            throw new BizException("更新数据不能为空");
        }
        
        // 校验是否存在循环引用
        validateStructureCircularReference(categoryStructures);
        
        for (CategoryStructureDto structure : categoryStructures) {
            // 校验分类是否存在
            GenProductCategory existingCategory = genProductCategoryService.getById(structure.getId());
            if (existingCategory == null) {
                throw new BizException("分类不存在，ID: " + structure.getId());
            }
            
            GenProductCategory category = new GenProductCategory();
            category.setId(structure.getId());
            category.setParentId(structure.getParentId());
            category.setSortOrder(structure.getSortOrder());
            
            // 计算分类等级
            if (structure.getParentId() == null) {
                category.setLevel(1);
            } else {
                GenProductCategory parent = genProductCategoryService.getById(structure.getParentId());
                if (parent != null) {
                    category.setLevel(parent.getLevel() + 1);
                    
                    // 检查层级深度
                    if (category.getLevel() > 3) {
                        throw new BizException("分类层级不能超过3级");
                    }
                } else {
                    throw new BizException("父分类不存在，ID: " + structure.getParentId());
                }
            }
            
            genProductCategoryService.updateById(category);
        }
    }

    /**
     * 删除分类
     */
    @DeleteMapping("/{categoryType}/category/{id}")
    public void deleteCategory(@PathVariable Long id, @PathVariable String categoryType) {
        // 检查是否有子分类
        long childCount = genProductCategoryService.lambdaQuery()
                .eq(GenProductCategory::getParentId, id)
                .count();
        
        if (childCount > 0) {
            throw new BizException("该分类下还有子分类，无法删除");
        }
        
        genProductCategoryService.removeById(id);
    }

    /**
     * 构建分类树形结构
     */
    private List<GenProductCategory> buildCategoryTree(List<GenProductCategory> allCategories) {
        // 获取根分类（parentId为null或0的分类）
        List<GenProductCategory> rootCategories = allCategories.stream()
                .filter(category -> category.getParentId() == null || category.getParentId() == 0)
                .collect(Collectors.toList());

        // 为每个根分类构建子分类树
        for (GenProductCategory rootCategory : rootCategories) {
            buildChildren(rootCategory, allCategories);
        }

        // 按排序号排序
        rootCategories.sort((a, b) -> {
            if (a.getSortOrder() == null) return 1;
            if (b.getSortOrder() == null) return -1;
            return a.getSortOrder().compareTo(b.getSortOrder());
        });

        return rootCategories;
    }

    /**
     * 递归构建子分类
     */
    private void buildChildren(GenProductCategory parent, List<GenProductCategory> allCategories) {
        List<GenProductCategory> children = allCategories.stream()
                .filter(category -> parent.getId().equals(category.getParentId()))
                .collect(Collectors.toList());

        // 按排序号排序
        children.sort((a, b) -> {
            if (a.getSortOrder() == null) return 1;
            if (b.getSortOrder() == null) return -1;
            return a.getSortOrder().compareTo(b.getSortOrder());
        });

        parent.setChildren(children);

        // 递归构建每个子分类的子分类
        for (GenProductCategory child : children) {
            buildChildren(child, allCategories);
        }
    }

    /**
     * 校验分类编码唯一性
     */
    private void validateCategoryCodeUnique(GenProductCategory category) {
        if (category.getCategoryCode() == null || category.getCategoryCode().trim().isEmpty()) {
            throw new BizException("分类编码不能为空");
        }
        
        // 查询是否存在相同编码的分类
        GenProductCategory existingCategory = genProductCategoryService.lambdaQuery()
                .eq(GenProductCategory::getCategoryCode, category.getCategoryCode())
                .one();
        
        if (existingCategory != null) {
            // 如果是更新操作，需要排除自身
            if (category.getId() == null || !category.getId().equals(existingCategory.getId())) {
                throw new BizException("分类编码已存在：" + category.getCategoryCode());
            }
        }
    }

    /**
     * 校验分类名称
     */
    private void validateCategoryName(GenProductCategory category) {
        if (category.getCategoryName() == null || category.getCategoryName().trim().isEmpty()) {
            throw new BizException("分类名称不能为空");
        }
        
        if (category.getCategoryName().length() > 100) {
            throw new BizException("分类名称长度不能超过100个字符");
        }
    }

    /**
     * 校验父分类
     */
    private void validateParentCategory(GenProductCategory category) {
        if (category.getParentId() != null) {
            GenProductCategory parent = genProductCategoryService.getById(category.getParentId());
            if (parent == null) {
                throw new BizException("父分类不存在");
            }
            
            // 检查是否会形成循环引用
            if (category.getId() != null && isCircularReference(category.getId(), category.getParentId())) {
                throw new BizException("不能将分类设置为自己的子分类");
            }
            
            // 检查层级深度
            if (parent.getLevel() != null && parent.getLevel() >= 3) {
                throw new BizException("分类最多支持三级");
            }
        }
    }

    /**
     * 检查是否存在循环引用
     */
    private boolean isCircularReference(Long categoryId, Long parentId) {
        if (categoryId.equals(parentId)) {
            return true;
        }
        
        GenProductCategory parent = genProductCategoryService.getById(parentId);
        if (parent != null && parent.getParentId() != null) {
            return isCircularReference(categoryId, parent.getParentId());
        }
        
        return false;
    }

    /**
     * 综合校验
     */
    private void validateCategory(GenProductCategory category) {
        validateCategoryName(category);
        validateCategoryCodeUnique(category);
        validateParentCategory(category);
    }

    /**
     * 校验批量更新结构时的循环引用
     */
    private void validateStructureCircularReference(List<CategoryStructureDto> categoryStructures) {
        for (CategoryStructureDto structure : categoryStructures) {
            if (structure.getParentId() != null) {
                if (hasCircularReferenceInStructure(structure.getId(), structure.getParentId(), categoryStructures)) {
                    throw new BizException("存在循环引用，分类ID: " + structure.getId());
                }
            }
        }
    }

    /**
     * 检查结构更新中的循环引用
     */
    private boolean hasCircularReferenceInStructure(Long categoryId, Long parentId, List<CategoryStructureDto> structures) {
        if (categoryId.equals(parentId)) {
            return true;
        }
        
        // 在更新结构中查找父分类
        CategoryStructureDto parentStructure = structures.stream()
                .filter(s -> s.getId().equals(parentId))
                .findFirst()
                .orElse(null);
        
        if (parentStructure != null && parentStructure.getParentId() != null) {
            return hasCircularReferenceInStructure(categoryId, parentStructure.getParentId(), structures);
        }
        
        // 如果在更新结构中没找到，查询数据库
        GenProductCategory parent = genProductCategoryService.getById(parentId);
        if (parent != null && parent.getParentId() != null) {
            return isCircularReference(categoryId, parent.getParentId());
        }
        
        return false;
    }

    /**
     * 实体转DTO
     */
    private CategoryDto convertToDto(GenProductCategory entity) {
        if (entity == null) return null;
        
        CategoryDto dto = new CategoryDto();
        dto.setId(entity.getId());
        dto.setName(entity.getCategoryName());
        dto.setCode(entity.getCategoryCode());
        dto.setImage(entity.getIcon());
        dto.setDescription(entity.getDescription());
        dto.setSortOrder(entity.getSortOrder());
        dto.setParentId(entity.getParentId());
        dto.setLevel(entity.getLevel());
        
        // 转换子分类
        if (entity.getChildren() != null && !entity.getChildren().isEmpty()) {
            List<CategoryDto> childrenDto = entity.getChildren().stream()
                    .map(this::convertToDto)
                    .collect(Collectors.toList());
            dto.setChildren(childrenDto);
        }
        
        return dto;
    }

    /**
     * DTO转实体
     */
    private GenProductCategory convertToEntity(CategoryDto dto) {
        if (dto == null) return null;
        
        GenProductCategory entity = new GenProductCategory();
        entity.setId(dto.getId());
        entity.setCategoryName(dto.getName());
        entity.setCategoryCode(dto.getCode());
        entity.setIcon(dto.getImage());
        entity.setDescription(dto.getDescription());
        entity.setSortOrder(dto.getSortOrder());
        entity.setParentId(dto.getParentId());
        entity.setLevel(dto.getLevel());
        
        return entity;
    }

    /**
     * 分类DTO
     */
    public static class CategoryDto {
        private Long id;
        private String name;
        private String code;
        private String image;
        private String description;
        private Integer sortOrder;
        private Long parentId;
        private Integer level;
        private List<CategoryDto> children;

        // Getters and Setters
        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getImage() {
            return image;
        }

        public void setImage(String image) {
            this.image = image;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public Integer getSortOrder() {
            return sortOrder;
        }

        public void setSortOrder(Integer sortOrder) {
            this.sortOrder = sortOrder;
        }

        public Long getParentId() {
            return parentId;
        }

        public void setParentId(Long parentId) {
            this.parentId = parentId;
        }

        public Integer getLevel() {
            return level;
        }

        public void setLevel(Integer level) {
            this.level = level;
        }

        public List<CategoryDto> getChildren() {
            return children;
        }

        public void setChildren(List<CategoryDto> children) {
            this.children = children;
        }
    }

    /**
     * 分类结构DTO
     */
    public static class CategoryStructureDto {
        private Long id;
        private Long parentId;
        private Integer sortOrder;

        // Getters and Setters
        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public Long getParentId() {
            return parentId;
        }

        public void setParentId(Long parentId) {
            this.parentId = parentId;
        }

        public Integer getSortOrder() {
            return sortOrder;
        }

        public void setSortOrder(Integer sortOrder) {
            this.sortOrder = sortOrder;
        }
    }
}
