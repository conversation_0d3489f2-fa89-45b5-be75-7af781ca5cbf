package com.linkBuy.platformApi.controller.system;

import com.linkBuy.common.annotation.ResponseWrapper;
import com.linkBuy.common.enums.SysConfigKeyEnum;
import com.linkBuy.common.service.AliyunSmsService;
import com.linkBuy.mysql.dao.service.system.SysConfigService;
import com.linkBuy.platformApi.dto.request.SystemConfigRequest;
import com.linkBuy.platformApi.dto.response.*;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/system")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@ResponseWrapper
public class ConfigController {

    private final SysConfigService sysConfigService;
    private final AliyunSmsService smsService;

    /**
     * 保存存储设置
     */
    @PostMapping("/config/storage")
    public void saveStorage(@RequestBody @Validated SystemConfigRequest.AliyunOss param) {
        sysConfigService.setValueByKey(SysConfigKeyEnum.ALIYUN_OSS_DOMAIN.getCode(), param.getDomain());
        sysConfigService.setValueByKey(SysConfigKeyEnum.ALIYUN_OSS_BUCKET.getCode(), param.getBucket());
        sysConfigService.setValueByKey(SysConfigKeyEnum.ALIYUN_OSS_ACCESS_KEY.getCode(), param.getAccessKey());
        sysConfigService.setValueByKey(SysConfigKeyEnum.ALIYUN_OSS_SECRET_KEY.getCode(), param.getSecretKey());
    }

    /**
     * 保存存储设置
     */
    @GetMapping("/config/storage")
    public StorageConfigDto getStorage() {
        StorageConfigDto result = new StorageConfigDto();
        result.setDomain(sysConfigService.getValueByKey(SysConfigKeyEnum.ALIYUN_OSS_DOMAIN.getCode()));
        result.setBucket(sysConfigService.getValueByKey(SysConfigKeyEnum.ALIYUN_OSS_BUCKET.getCode()));
        result.setAccessKey(sysConfigService.getValueByKey(SysConfigKeyEnum.ALIYUN_OSS_ACCESS_KEY.getCode()));
        result.setSecretKey(sysConfigService.getValueByKey(SysConfigKeyEnum.ALIYUN_OSS_SECRET_KEY.getCode()));
        return result;
    }

    /**
     * 保存存储设置
     */
    @PostMapping("/config/logistics")
    public void saveLogistics(@RequestBody @Validated SystemConfigRequest.Kuaidi100 param) {
        sysConfigService.setValueByKey(SysConfigKeyEnum.KUAIDI100_CUSTOMER.getCode(), param.getCustomer());
        sysConfigService.setValueByKey(SysConfigKeyEnum.KUAIDI100_APPKEY.getCode(), param.getAppKey());
    }

    /**
     * 保存存储设置
     */
    @GetMapping("/config/logistics")
    public LogisticsConfigDto getLogistics() {
        LogisticsConfigDto result = new LogisticsConfigDto();
        result.setCustomer(sysConfigService.getValueByKey(SysConfigKeyEnum.KUAIDI100_CUSTOMER.getCode()));
        result.setAppKey(sysConfigService.getValueByKey(SysConfigKeyEnum.KUAIDI100_APPKEY.getCode()));
        return result;
    }

    /**
     * 保存邮件设置
     */
    @PostMapping("/config/email")
    public void saveEmail(@RequestBody @Validated SystemConfigRequest.Email param) {
        sysConfigService.setValueByKey(SysConfigKeyEnum.EMAIL_SMTP_HOST.getCode(), param.getSmtpHost());
        sysConfigService.setValueByKey(SysConfigKeyEnum.EMAIL_SMTP_PORT.getCode(), param.getSmtpPort());
        sysConfigService.setValueByKey(SysConfigKeyEnum.EMAIL_SMTP_USERNAME.getCode(), param.getSmtpUsername());
        sysConfigService.setValueByKey(SysConfigKeyEnum.EMAIL_SMTP_PASSWORD.getCode(), param.getSmtpPassword());
        sysConfigService.setValueByKey(SysConfigKeyEnum.EMAIL_FROM_ADDRESS.getCode(), param.getFromAddress());
        sysConfigService.setValueByKey(SysConfigKeyEnum.EMAIL_FROM_NAME.getCode(), param.getFromName());
        sysConfigService.setValueByKey(SysConfigKeyEnum.EMAIL_SMTP_AUTH.getCode(), param.getSmtpAuth());
        sysConfigService.setValueByKey(SysConfigKeyEnum.EMAIL_SMTP_STARTTLS_ENABLE.getCode(), param.getStarttlsEnable());
    }

    /**
     * 获取邮件设置
     */
    @GetMapping("/config/email")
    public EmailConfigDto getEmail() {
        EmailConfigDto result = new EmailConfigDto();
        result.setSmtpHost(sysConfigService.getValueByKey(SysConfigKeyEnum.EMAIL_SMTP_HOST.getCode()));
        result.setSmtpPort(sysConfigService.getValueByKey(SysConfigKeyEnum.EMAIL_SMTP_PORT.getCode()));
        result.setSmtpUsername(sysConfigService.getValueByKey(SysConfigKeyEnum.EMAIL_SMTP_USERNAME.getCode()));
        result.setSmtpPassword(sysConfigService.getValueByKey(SysConfigKeyEnum.EMAIL_SMTP_PASSWORD.getCode()));
        result.setFromAddress(sysConfigService.getValueByKey(SysConfigKeyEnum.EMAIL_FROM_ADDRESS.getCode()));
        result.setFromName(sysConfigService.getValueByKey(SysConfigKeyEnum.EMAIL_FROM_NAME.getCode()));
        result.setSmtpAuth(sysConfigService.getValueByKey(SysConfigKeyEnum.EMAIL_SMTP_AUTH.getCode()));
        result.setStarttlsEnable(sysConfigService.getValueByKey(SysConfigKeyEnum.EMAIL_SMTP_STARTTLS_ENABLE.getCode()));
        return result;
    }

    /**
     * 发送测试邮件
     */
    @PostMapping("/config/email/test")
    public void sendTestEmail(@RequestParam String testEmail) {
        // 这里可以注入EmailService来发送测试邮件
        // emailService.sendTestEmail(testEmail);
    }

    /**
     * 保存短信设置
     */
    @PostMapping("/config/sms")
    public void saveSms(@RequestBody @Validated SystemConfigRequest.Sms param) {
        sysConfigService.setValueByKey(SysConfigKeyEnum.ALIYUN_SMS_SECRET_KEY.getCode(), param.getAccessKey());
        sysConfigService.setValueByKey(SysConfigKeyEnum.ALIYUN_SMS_ACCESS_SECRET.getCode(), param.getAccessSecret());
        sysConfigService.setValueByKey(SysConfigKeyEnum.ALIYUN_SMS_SIGN_NAME.getCode(), param.getSignName());
        sysConfigService.setValueByKey(SysConfigKeyEnum.ALIYUN_SMS_TEMPLATE_CODE.getCode(), param.getTemplateCode());
    }

    /**
     * 获取短信设置
     */
    @GetMapping("/config/sms")
    public SmsConfigDto getSms() {
        SmsConfigDto result = new SmsConfigDto();
        result.setAccessKey(sysConfigService.getValueByKey(SysConfigKeyEnum.ALIYUN_SMS_SECRET_KEY.getCode()));
        result.setAccessSecret(sysConfigService.getValueByKey(SysConfigKeyEnum.ALIYUN_SMS_ACCESS_SECRET.getCode()));
        result.setSignName(sysConfigService.getValueByKey(SysConfigKeyEnum.ALIYUN_SMS_SIGN_NAME.getCode()));
        result.setTemplateCode(sysConfigService.getValueByKey(SysConfigKeyEnum.ALIYUN_SMS_TEMPLATE_CODE.getCode()));
        return result;
    }

    /**
     * 保存腾讯地图设置
     */
    @PostMapping("/config/map")
    public void saveMap(@RequestBody @Validated SystemConfigRequest.Map param) {
        sysConfigService.setValueByKey(SysConfigKeyEnum.TENCENT_MAP_KEY.getCode(), param.getKey());
        sysConfigService.setValueByKey(SysConfigKeyEnum.TENCENT_MAP_SECRET_KEY.getCode(), param.getSecretKey());
    }

    /**
     * 获取腾讯地图设置
     */
    @GetMapping("/config/map")
    public TencentMapConfigDto getMap() {
        TencentMapConfigDto result = new TencentMapConfigDto();
        result.setKey(sysConfigService.getValueByKey(SysConfigKeyEnum.TENCENT_MAP_KEY.getCode()));
        result.setSecretKey(sysConfigService.getValueByKey(SysConfigKeyEnum.TENCENT_MAP_SECRET_KEY.getCode()));
        return result;
    }

    /**
     * 发送测试短信
     */
    @PostMapping("/config/sms/test")
    public void sendTestSms(@RequestParam String phoneNumber) {
        try {
            // 从系统配置中获取短信签名和模板
            String signName = sysConfigService.getValueByKey(SysConfigKeyEnum.ALIYUN_SMS_SIGN_NAME.getCode());
            String templateCode = sysConfigService.getValueByKey(SysConfigKeyEnum.ALIYUN_SMS_TEMPLATE_CODE.getCode());

            // 生成测试验证码
            String testCode = String.valueOf((int) ((Math.random() * 9 + 1) * 1000));
            String templateParam = "{\"code\":\"" + testCode + "\"}";

            // 发送测试短信
            smsService.sendSms(phoneNumber, signName, templateCode, templateParam);
        } catch (Exception e) {
            throw new RuntimeException("发送测试短信失败: " + e.getMessage(), e);
        }
    }
}
