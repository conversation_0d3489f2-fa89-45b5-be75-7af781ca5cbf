# SKU价格更新问题修复报告

## 🚨 问题描述

用户反馈：调价功能执行后，`biz_company_sku` 表的 `sale_price` 字段没有被更新。

## 🔍 问题分析

### 根本原因
前端发送的SKU ID与后端查询逻辑不匹配：

1. **前端获取的数据结构**：
   ```javascript
   // 从 getProductDetail 接口获取的SKU数据
   sku: {
     id: 123,        // 这是客户SKU的ID (biz_company_sku.id)
     skuId: 456,     // 这是供应商SKU的ID (biz_company_sku.sku_id)
     salePrice: 100.00,
     // ... 其他字段
   }
   ```

2. **前端错误的调价请求**：
   ```javascript
   // ❌ 错误：使用了客户SKU的ID
   skuPriceUpdates: [{
     skuId: sku.id,  // 123 (客户SKU ID)
     newPrice: 120.00,
     oldPrice: 100.00
   }]
   ```

3. **后端查询逻辑**：
   ```java
   // 后端根据供应商SKU ID查找客户SKU记录
   BizCompanySku companySku = bizCompanySkuService.getOne(
       new LambdaQueryWrapper<BizCompanySku>()
           .eq(BizCompanySku::getCompanyId, companyId)
           .eq(BizCompanySku::getSkuId, skuUpdate.getSkuId()) // 期望的是供应商SKU ID
   );
   ```

### 数据库关系说明
```
biz_company_sku 表：
- id: 客户SKU的主键ID
- sku_id: 指向供应商SKU的ID (biz_supplier_sku.id)
- company_id: 客户公司ID
- sale_price: 客户定制的销售价格

biz_supplier_sku 表：
- id: 供应商SKU的主键ID
- spu_id: 指向供应商SPU的ID
- cost_price, purchase_price 等: 供应商设置的价格
```

## ✅ 解决方案

### 1. 修复前端调价请求
**文件**: `LinkBuy-FED/linkBuyMallAdmin/src/views/goods/companyProduct/index.vue`

**修改前**:
```javascript
const skuPriceUpdates = currentPriceProduct.value.skuList.map((sku: any) => ({
  skuId: sku.id, // ❌ 错误：这是客户SKU的ID
  newPrice: sku.adjustedSalePrice,
  oldPrice: sku.salePrice
}))
```

**修改后**:
```javascript
const skuPriceUpdates = currentPriceProduct.value.skuList.map((sku: any) => ({
  skuId: sku.skuId, // ✅ 正确：这是供应商SKU的ID
  newPrice: sku.adjustedSalePrice,
  oldPrice: sku.salePrice
}))
```

### 2. 增强后端调试日志
**文件**: `LinkBuy/mall-admin-api/src/main/java/com/linkBuy/mallAdminApi/controller/CompanyProductManagementController.java`

**增加的调试信息**:
```java
// 开始处理时的日志
log.info("开始处理SKU调价，供应商SKU ID：{}，新价格：{}，原价格：{}", 
        skuUpdate.getSkuId(), skuUpdate.getNewPrice(), skuUpdate.getOldPrice());

// 查找失败时的警告
if (companySku == null) {
    log.warn("未找到对应的客户SKU记录，公司ID：{}，供应商SKU ID：{}", companyId, skuUpdate.getSkuId());
    continue;
}

// 更新结果的日志
boolean updateResult = bizCompanySkuService.updateById(companySku);
if (updateResult) {
    log.info("SKU级别调价成功，客户SKU ID：{}，供应商SKU ID：{}，原价格：{}，新价格：{}",
            companySku.getId(), skuUpdate.getSkuId(), oldPrice, newPrice);
} else {
    log.error("SKU级别调价失败，客户SKU ID：{}，供应商SKU ID：{}，原价格：{}，新价格：{}",
            companySku.getId(), skuUpdate.getSkuId(), oldPrice, newPrice);
}
```

## 🧪 验证方法

### 1. 检查日志输出
修复后，后端日志应该显示：
```
INFO  - 开始处理SKU调价，供应商SKU ID：456，新价格：120.00，原价格：100.00
INFO  - SKU级别调价成功，客户SKU ID：123，供应商SKU ID：456，原价格：100.00，新价格：120.00
```

而不是：
```
WARN  - 未找到对应的客户SKU记录，公司ID：1，供应商SKU ID：123
```

### 2. 检查数据库更新
```sql
-- 检查客户SKU的价格是否更新
SELECT id, sku_id, sale_price, update_time 
FROM biz_company_sku 
WHERE company_id = ? AND sku_id = ?
ORDER BY update_time DESC;
```

### 3. 前端验证
1. 在调价对话框中设置不同的SKU价格
2. 提交调价请求
3. 刷新页面，检查价格是否正确显示
4. 查看商品详情，确认SKU价格已更新

## 📊 数据流对比

### 修复前（错误流程）
```
前端获取SKU数据:
{id: 123, skuId: 456, salePrice: 100.00}

前端发送调价请求:
{skuId: 123, newPrice: 120.00} // ❌ 使用了客户SKU ID

后端查询:
SELECT * FROM biz_company_sku 
WHERE company_id = 1 AND sku_id = 123 // ❌ 查找不到记录

结果: 价格更新失败
```

### 修复后（正确流程）
```
前端获取SKU数据:
{id: 123, skuId: 456, salePrice: 100.00}

前端发送调价请求:
{skuId: 456, newPrice: 120.00} // ✅ 使用供应商SKU ID

后端查询:
SELECT * FROM biz_company_sku 
WHERE company_id = 1 AND sku_id = 456 // ✅ 找到记录 (id=123)

更新操作:
UPDATE biz_company_sku 
SET sale_price = 120.00, update_time = NOW() 
WHERE id = 123

结果: 价格更新成功 ✅
```

## 🎯 关键要点

1. **ID字段含义**：
   - `sku.id`: 客户SKU的主键ID
   - `sku.skuId`: 供应商SKU的ID（外键）

2. **查询逻辑**：
   - 后端通过 `company_id + sku_id` 来定位客户SKU记录
   - `sku_id` 字段存储的是供应商SKU的ID

3. **数据一致性**：
   - 修复确保了前端发送的ID与后端查询逻辑匹配
   - 避免了因ID类型错误导致的查询失败

## 📝 总结

通过修复前端调价请求中的SKU ID字段，解决了 `biz_company_sku` 表价格更新失败的问题。关键在于理解数据库表之间的关系，确保前端发送的是正确的供应商SKU ID，而不是客户SKU的主键ID。

修复后，SKU级别的精确调价功能将正常工作，每个SKU都能设置为用户指定的价格，真正实现"所见即所得"的调价体验。 