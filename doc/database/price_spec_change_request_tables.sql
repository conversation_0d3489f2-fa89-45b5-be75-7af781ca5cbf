-- =============================================
-- 价格规格修改申请表结构设计
-- 支持价格修改和规格调整的完整业务流程
-- =============================================

-- 1. 主申请表 - 记录申请的基本信息
DROP TABLE IF EXISTS `price_spec_change_request`;
CREATE TABLE `price_spec_change_request` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `request_no` varchar(64) NOT NULL COMMENT '申请单号，格式：PSCR+时间戳+随机数',
  `supplier_id` bigint(20) NOT NULL COMMENT '供应商ID',
  `supplier_name` varchar(128) NOT NULL COMMENT '供应商名称',
  `request_type` tinyint(2) NOT NULL COMMENT '申请类型：1-价格修改，2-规格调整，3-价格+规格',
  `request_reason` text NOT NULL COMMENT '申请说明/原因',
  `product_count` int(11) DEFAULT 0 COMMENT '涉及商品数量',
  `sku_count` int(11) DEFAULT 0 COMMENT '涉及SKU数量',
  `status` tinyint(2) NOT NULL DEFAULT 1 COMMENT '申请状态：1-待审核，2-审核通过，3-审核拒绝，4-已撤回，5-已过期',
  `submit_time` datetime NOT NULL COMMENT '提交时间',
  `review_time` datetime DEFAULT NULL COMMENT '审核时间',
  `reviewer_id` bigint(20) DEFAULT NULL COMMENT '审核人ID',
  `reviewer_name` varchar(64) DEFAULT NULL COMMENT '审核人姓名',
  `review_comment` text DEFAULT NULL COMMENT '审核意见',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间（审核通过后的有效期）',
  `apply_time` datetime DEFAULT NULL COMMENT '应用时间（实际生效时间）',
  `apply_status` tinyint(2) DEFAULT 0 COMMENT '应用状态：0-未应用，1-已应用，2-应用失败',
  `apply_error` text DEFAULT NULL COMMENT '应用失败原因',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-否，1-是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_request_no` (`request_no`),
  KEY `idx_supplier_id` (`supplier_id`),
  KEY `idx_status` (`status`),
  KEY `idx_request_type` (`request_type`),
  KEY `idx_submit_time` (`submit_time`),
  KEY `idx_review_time` (`review_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='价格规格修改申请主表';

-- 2. 商品申请明细表 - 记录每个商品的申请信息
DROP TABLE IF EXISTS `price_spec_change_request_product`;
CREATE TABLE `price_spec_change_request_product` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `request_id` bigint(20) NOT NULL COMMENT '申请ID',
  `spu_id` bigint(20) NOT NULL COMMENT '商品SPU ID',
  `product_name` varchar(256) NOT NULL COMMENT '商品名称',
  `product_code` varchar(128) DEFAULT NULL COMMENT '商品编码',
  `category_id` bigint(20) DEFAULT NULL COMMENT '分类ID',
  `category_name` varchar(256) DEFAULT NULL COMMENT '分类名称',
  `spec_type` tinyint(2) NOT NULL COMMENT '规格类型：1-单规格，2-多规格',
  `change_type` tinyint(2) NOT NULL COMMENT '变更类型：1-仅价格，2-仅规格，3-价格+规格',
  `original_data` longtext COMMENT '原始商品数据（JSON格式）',
  `new_data` longtext COMMENT '新商品数据（JSON格式）',
  `spec1_name` varchar(64) DEFAULT NULL COMMENT '规格1名称',
  `spec2_name` varchar(64) DEFAULT NULL COMMENT '规格2名称',
  `spec3_name` varchar(64) DEFAULT NULL COMMENT '规格3名称',
  `sku_add_count` int(11) DEFAULT 0 COMMENT '新增SKU数量',
  `sku_update_count` int(11) DEFAULT 0 COMMENT '修改SKU数量',
  `sku_delete_count` int(11) DEFAULT 0 COMMENT '删除SKU数量',
  `price_change_summary` text COMMENT '价格变更摘要',
  `spec_change_summary` text COMMENT '规格变更摘要',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_request_id` (`request_id`),
  KEY `idx_spu_id` (`spu_id`),
  KEY `idx_change_type` (`change_type`),
  CONSTRAINT `fk_product_request_id` FOREIGN KEY (`request_id`) REFERENCES `price_spec_change_request` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='价格规格修改申请商品明细表';

-- 3. SKU变更明细表 - 记录每个SKU的具体变更
DROP TABLE IF EXISTS `price_spec_change_request_sku`;
CREATE TABLE `price_spec_change_request_sku` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `request_id` bigint(20) NOT NULL COMMENT '申请ID',
  `product_request_id` bigint(20) NOT NULL COMMENT '商品申请明细ID',
  `sku_id` bigint(20) DEFAULT NULL COMMENT 'SKU ID（新增SKU为NULL）',
  `sku_code` varchar(128) DEFAULT NULL COMMENT 'SKU编码',
  `operation_type` tinyint(2) NOT NULL COMMENT '操作类型：1-新增，2-修改，3-删除',
  
  -- 规格信息
  `spec1_value` varchar(128) DEFAULT NULL COMMENT '规格1值',
  `spec2_value` varchar(128) DEFAULT NULL COMMENT '规格2值',
  `spec3_value` varchar(128) DEFAULT NULL COMMENT '规格3值',
  `spec_combination` varchar(512) DEFAULT NULL COMMENT '规格组合字符串',
  
  -- 价格相关字段（原值 -> 新值）
  `old_cost_price` decimal(10,2) DEFAULT NULL COMMENT '原成本价',
  `new_cost_price` decimal(10,2) DEFAULT NULL COMMENT '新成本价',
  `old_purchase_price` decimal(10,2) DEFAULT NULL COMMENT '原采购价',
  `new_purchase_price` decimal(10,2) DEFAULT NULL COMMENT '新采购价',
  `old_strikethrough_price` decimal(10,2) DEFAULT NULL COMMENT '原划线价',
  `new_strikethrough_price` decimal(10,2) DEFAULT NULL COMMENT '新划线价',
  
  -- 库存相关字段（通常不在改价申请中修改，但记录当前值）
  `total_inventory` int(11) DEFAULT 0 COMMENT '总库存',
  `available_inventory` int(11) DEFAULT 0 COMMENT '可用库存',
  `blocked_inventory` int(11) DEFAULT 0 COMMENT '冻结库存',
  `safety_stock` int(11) DEFAULT 0 COMMENT '安全库存',
  
  -- 其他信息
  `img_url` varchar(512) DEFAULT NULL COMMENT 'SKU图片URL',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `change_fields` varchar(512) DEFAULT NULL COMMENT '变更字段列表（逗号分隔）',
  `change_reason` varchar(512) DEFAULT NULL COMMENT '变更原因',
  
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_request_id` (`request_id`),
  KEY `idx_product_request_id` (`product_request_id`),
  KEY `idx_sku_id` (`sku_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_spec_combination` (`spec_combination`),
  CONSTRAINT `fk_sku_request_id` FOREIGN KEY (`request_id`) REFERENCES `price_spec_change_request` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_sku_product_request_id` FOREIGN KEY (`product_request_id`) REFERENCES `price_spec_change_request_product` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='价格规格修改申请SKU明细表';

-- 4. 申请操作日志表 - 记录申请的所有操作历史
DROP TABLE IF EXISTS `price_spec_change_request_log`;
CREATE TABLE `price_spec_change_request_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `request_id` bigint(20) NOT NULL COMMENT '申请ID',
  `operation_type` varchar(32) NOT NULL COMMENT '操作类型：SUBMIT,REVIEW_PASS,REVIEW_REJECT,WITHDRAW,APPLY,EXPIRE',
  `operator_id` bigint(20) NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(64) NOT NULL COMMENT '操作人姓名',
  `operator_type` varchar(32) NOT NULL COMMENT '操作人类型：SUPPLIER,ADMIN,SYSTEM',
  `operation_desc` varchar(512) NOT NULL COMMENT '操作描述',
  `operation_comment` text DEFAULT NULL COMMENT '操作备注',
  `before_status` tinyint(2) DEFAULT NULL COMMENT '操作前状态',
  `after_status` tinyint(2) DEFAULT NULL COMMENT '操作后状态',
  `operation_data` longtext DEFAULT NULL COMMENT '操作相关数据（JSON格式）',
  `ip_address` varchar(64) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(512) DEFAULT NULL COMMENT '用户代理',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_request_id` (`request_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_created_time` (`created_time`),
  CONSTRAINT `fk_log_request_id` FOREIGN KEY (`request_id`) REFERENCES `price_spec_change_request` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='价格规格修改申请操作日志表';

-- 5. 申请统计表 - 用于快速查询统计信息
DROP TABLE IF EXISTS `price_spec_change_request_statistics`;
CREATE TABLE `price_spec_change_request_statistics` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `supplier_id` bigint(20) NOT NULL COMMENT '供应商ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `total_requests` int(11) DEFAULT 0 COMMENT '总申请数',
  `pending_requests` int(11) DEFAULT 0 COMMENT '待审核申请数',
  `approved_requests` int(11) DEFAULT 0 COMMENT '已通过申请数',
  `rejected_requests` int(11) DEFAULT 0 COMMENT '已拒绝申请数',
  `withdrawn_requests` int(11) DEFAULT 0 COMMENT '已撤回申请数',
  `price_change_requests` int(11) DEFAULT 0 COMMENT '价格修改申请数',
  `spec_change_requests` int(11) DEFAULT 0 COMMENT '规格调整申请数',
  `mixed_change_requests` int(11) DEFAULT 0 COMMENT '混合修改申请数',
  `avg_review_hours` decimal(10,2) DEFAULT NULL COMMENT '平均审核时长（小时）',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_supplier_date` (`supplier_id`, `stat_date`),
  KEY `idx_stat_date` (`stat_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='价格规格修改申请统计表';

-- =============================================
-- 索引优化建议
-- =============================================

-- 复合索引，优化常用查询
ALTER TABLE `price_spec_change_request` ADD INDEX `idx_supplier_status_time` (`supplier_id`, `status`, `submit_time`);
ALTER TABLE `price_spec_change_request` ADD INDEX `idx_status_type_time` (`status`, `request_type`, `submit_time`);

-- 优化SKU明细表查询
ALTER TABLE `price_spec_change_request_sku` ADD INDEX `idx_request_operation` (`request_id`, `operation_type`);
ALTER TABLE `price_spec_change_request_sku` ADD INDEX `idx_sku_operation` (`sku_id`, `operation_type`);

-- =============================================
-- 注意：原视图功能已通过Mapper方法实现
-- =============================================
-- 申请汇总查询 -> BizPriceSpecChangeRequestStatisticsMapper.selectRequestStatsSummary()
-- SKU变更明细查询 -> BizPriceSpecChangeRequestStatisticsMapper.selectSupplierRequestTrend()
-- 申请类型分布查询 -> BizPriceSpecChangeRequestStatisticsMapper.selectRequestTypeDistribution()

-- =============================================
-- 触发器 - 自动维护统计数据
-- =============================================

DELIMITER $$

-- 申请提交后自动更新统计
CREATE TRIGGER `tr_price_spec_change_request_after_insert`
AFTER INSERT ON `price_spec_change_request`
FOR EACH ROW
BEGIN
    INSERT INTO `price_spec_change_request_statistics` 
    (`supplier_id`, `stat_date`, `total_requests`, `pending_requests`, 
     `price_change_requests`, `spec_change_requests`, `mixed_change_requests`)
    VALUES 
    (NEW.supplier_id, DATE(NEW.created_time), 1, 1,
     CASE WHEN NEW.request_type = 1 THEN 1 ELSE 0 END,
     CASE WHEN NEW.request_type = 2 THEN 1 ELSE 0 END,
     CASE WHEN NEW.request_type = 3 THEN 1 ELSE 0 END)
    ON DUPLICATE KEY UPDATE
    `total_requests` = `total_requests` + 1,
    `pending_requests` = `pending_requests` + 1,
    `price_change_requests` = `price_change_requests` + CASE WHEN NEW.request_type = 1 THEN 1 ELSE 0 END,
    `spec_change_requests` = `spec_change_requests` + CASE WHEN NEW.request_type = 2 THEN 1 ELSE 0 END,
    `mixed_change_requests` = `mixed_change_requests` + CASE WHEN NEW.request_type = 3 THEN 1 ELSE 0 END,
    `updated_time` = NOW();
END$$

-- 申请状态更新后自动更新统计
CREATE TRIGGER `tr_price_spec_change_request_after_update`
AFTER UPDATE ON `price_spec_change_request`
FOR EACH ROW
BEGIN
    IF OLD.status != NEW.status THEN
        -- 更新统计数据
        UPDATE `price_spec_change_request_statistics` 
        SET 
            `pending_requests` = `pending_requests` + 
                CASE WHEN NEW.status = 1 THEN 1 ELSE 0 END -
                CASE WHEN OLD.status = 1 THEN 1 ELSE 0 END,
            `approved_requests` = `approved_requests` + 
                CASE WHEN NEW.status = 2 THEN 1 ELSE 0 END -
                CASE WHEN OLD.status = 2 THEN 1 ELSE 0 END,
            `rejected_requests` = `rejected_requests` + 
                CASE WHEN NEW.status = 3 THEN 1 ELSE 0 END -
                CASE WHEN OLD.status = 3 THEN 1 ELSE 0 END,
            `withdrawn_requests` = `withdrawn_requests` + 
                CASE WHEN NEW.status = 4 THEN 1 ELSE 0 END -
                CASE WHEN OLD.status = 4 THEN 1 ELSE 0 END,
            `updated_time` = NOW()
        WHERE `supplier_id` = NEW.supplier_id 
        AND `stat_date` = DATE(NEW.created_time);
        
        -- 如果是审核完成，计算平均审核时长
        IF NEW.status IN (2, 3) AND NEW.review_time IS NOT NULL THEN
            UPDATE `price_spec_change_request_statistics` 
            SET `avg_review_hours` = (
                SELECT AVG(TIMESTAMPDIFF(HOUR, submit_time, review_time))
                FROM `price_spec_change_request`
                WHERE supplier_id = NEW.supplier_id 
                AND DATE(created_time) = DATE(NEW.created_time)
                AND review_time IS NOT NULL
                AND is_delete = 0
            )
            WHERE `supplier_id` = NEW.supplier_id 
            AND `stat_date` = DATE(NEW.created_time);
        END IF;
    END IF;
END$$

DELIMITER ;

-- =============================================
-- 初始化数据和配置
-- =============================================

-- 申请单号序列配置（如果需要）
-- 可以使用单独的序列表或者基于时间戳+随机数生成

-- 示例数据（测试用）
-- INSERT INTO `price_spec_change_request` (...) VALUES (...);

-- =============================================
-- 数据清理和维护建议
-- =============================================

-- 1. 定期清理过期的已处理申请（建议保留6个月到1年）
-- 2. 定期归档历史数据到历史表
-- 3. 定期更新统计数据
-- 4. 监控表空间使用情况

-- 清理脚本示例（需要根据实际业务需求调整）
/*
-- 清理6个月前已处理的申请
DELETE FROM price_spec_change_request 
WHERE status IN (2, 3, 4, 5) 
AND created_time < DATE_SUB(NOW(), INTERVAL 6 MONTH);

-- 重建统计数据
TRUNCATE TABLE price_spec_change_request_statistics;
INSERT INTO price_spec_change_request_statistics (...)
SELECT ... FROM price_spec_change_request GROUP BY ...;
*/ 