-- ================================
-- 价格修改申请系统数据库表结构
-- ================================
-- 说明：本系统用于管理供应商的商品价格修改申请流程
-- 版本：v2.1
-- 创建时间：2024-01-01
-- 修改时间：2024-01-15
-- 注意：禁止使用视图和触发器，所有数据维护在程序中完成
-- 修复：与ER图保持一致

-- ================================
-- 1. 价格修改申请主表
-- ================================
CREATE TABLE `biz_price_spec_change_request` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `request_no` VARCHAR(50) NOT NULL COMMENT '申请单号，格式：PSCR+时间戳+随机数',
    `supplier_id` BIGINT UNSIGNED NOT NULL COMMENT '供应商ID',
    `supplier_name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '供应商名称',
    `submitter_id` BIGINT UNSIGNED NOT NULL COMMENT '提交人ID',
    `submitter_name` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '提交人姓名',
    `request_type` TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '申请类型：1-价格修改，2-规格调整，3-价格+规格',
    `request_reason` TEXT COMMENT '申请说明/原因',
    `product_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '涉及商品数量（程序维护）',
    `sku_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '涉及SKU数量（程序维护）',
    `total_price_change_amount` DECIMAL(15,2) DEFAULT 0.00 COMMENT '总价格变动金额（程序维护）',
    `avg_price_change_rate` DECIMAL(8,4) DEFAULT 0.0000 COMMENT '平均价格变动比例（程序维护）',
    `max_price_change_rate` DECIMAL(8,4) DEFAULT 0.0000 COMMENT '最大价格变动比例（程序维护）',
    `status` TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '申请状态：1-待审核，2-审核通过，3-审核拒绝，4-已撤回，5-已过期',
    `submit_time` DATETIME NOT NULL COMMENT '提交时间',
    `reviewer_id` BIGINT UNSIGNED NULL COMMENT '审核人ID',
    `reviewer_name` VARCHAR(50) DEFAULT '' COMMENT '审核人姓名',
    `review_time` DATETIME NULL COMMENT '审核时间',
    `review_comment` TEXT COMMENT '审核意见',
    `expire_time` DATETIME NULL COMMENT '过期时间（审核通过后的有效期）',
    `apply_time` DATETIME NULL COMMENT '应用时间（实际生效时间）',
    `apply_status` TINYINT UNSIGNED DEFAULT 0 COMMENT '应用状态：0-未应用，1-已应用，2-应用失败',
    `apply_error` TEXT COMMENT '应用失败原因',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除：0-否，1-是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_request_no` (`request_no`),
    KEY `idx_supplier_id` (`supplier_id`),
    KEY `idx_submitter_id` (`submitter_id`),
    KEY `idx_status` (`status`),
    KEY `idx_submit_time` (`submit_time`),
    KEY `idx_reviewer_id` (`reviewer_id`),
    KEY `idx_apply_status` (`apply_status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='价格规格修改申请主表';

-- ================================
-- 2. 价格修改申请商品明细表
-- ================================
CREATE TABLE `biz_price_spec_change_request_product` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `request_id` BIGINT UNSIGNED NOT NULL COMMENT '申请ID，关联biz_price_spec_change_request.id',
    `spu_id` BIGINT UNSIGNED NOT NULL COMMENT '商品SPU ID',
    `product_name` VARCHAR(200) DEFAULT '' COMMENT '商品名称',
    `product_code` VARCHAR(100) DEFAULT '' COMMENT '商品编码',
    `category_id` BIGINT UNSIGNED NULL COMMENT '分类ID',
    `category_name` VARCHAR(100) DEFAULT '' COMMENT '分类名称',
    `spec_type` TINYINT UNSIGNED DEFAULT 1 COMMENT '规格类型：1-单规格，2-多规格',
    `change_type` TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '变更类型：1-仅价格，2-仅规格，3-价格+规格',
    `original_data` LONGTEXT COMMENT '原始商品数据（JSON格式）',
    `new_data` LONGTEXT COMMENT '新商品数据（JSON格式）',
    `spec1_name` VARCHAR(50) DEFAULT '' COMMENT '规格1名称',
    `spec2_name` VARCHAR(50) DEFAULT '' COMMENT '规格2名称',
    `spec3_name` VARCHAR(50) DEFAULT '' COMMENT '规格3名称',
    `sku_add_count` INT UNSIGNED DEFAULT 0 COMMENT '新增SKU数量',
    `sku_update_count` INT UNSIGNED DEFAULT 0 COMMENT '修改SKU数量',
    `sku_delete_count` INT UNSIGNED DEFAULT 0 COMMENT '删除SKU数量',
    `total_sku_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '该商品的SKU总数（程序维护）',
    `changed_sku_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '变更的SKU数量（程序维护）',
    `price_change_summary` TEXT COMMENT '价格变更摘要',
    `spec_change_summary` TEXT COMMENT '规格变更摘要',
    `avg_price_change_amount` DECIMAL(15,2) DEFAULT 0.00 COMMENT '平均价格变动金额（程序维护）',
    `max_price_change_amount` DECIMAL(15,2) DEFAULT 0.00 COMMENT '最大价格变动金额（程序维护）',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除：0-否，1-是',
    PRIMARY KEY (`id`),
    KEY `idx_request_id` (`request_id`),
    KEY `idx_spu_id` (`spu_id`),
    KEY `idx_category_id` (`category_id`),
    KEY `idx_change_type` (`change_type`),
    KEY `idx_create_time` (`create_time`),
    CONSTRAINT `fk_price_request_product_request` FOREIGN KEY (`request_id`) REFERENCES `biz_price_spec_change_request` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='价格规格修改申请商品明细表';

-- ================================
-- 3. 价格修改申请SKU明细表
-- ================================
CREATE TABLE `biz_price_spec_change_request_sku` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `request_id` BIGINT UNSIGNED NOT NULL COMMENT '申请ID，关联biz_price_spec_change_request.id',
    `product_request_id` BIGINT UNSIGNED NOT NULL COMMENT '商品申请明细ID，关联biz_price_spec_change_request_product.id',
    `sku_id` BIGINT UNSIGNED NULL COMMENT 'SKU ID（新增SKU为NULL）',
    `sku_code` VARCHAR(100) DEFAULT '' COMMENT 'SKU编码',
    `sku_name` VARCHAR(200) DEFAULT '' COMMENT 'SKU名称（冗余字段）',
    `spec_info` VARCHAR(500) DEFAULT '' COMMENT '规格信息（冗余字段）',
    `operation_type` TINYINT UNSIGNED NOT NULL DEFAULT 2 COMMENT '操作类型：1-新增，2-修改，3-删除',
    `spec1_value` VARCHAR(100) DEFAULT '' COMMENT '规格1值',
    `spec2_value` VARCHAR(100) DEFAULT '' COMMENT '规格2值',
    `spec3_value` VARCHAR(100) DEFAULT '' COMMENT '规格3值',
    `spec_combination` VARCHAR(300) DEFAULT '' COMMENT '规格组合字符串',
    `change_fields` VARCHAR(200) DEFAULT '' COMMENT '变更字段列表，逗号分隔（如：costPrice,purchasePrice）',
    `change_reason` VARCHAR(500) DEFAULT '' COMMENT '变更原因',
    
    -- 成本价变更
    `old_cost_price` DECIMAL(15,2) NULL COMMENT '原成本价',
    `new_cost_price` DECIMAL(15,2) NULL COMMENT '新成本价',
    `cost_price_change_amount` DECIMAL(15,2) DEFAULT 0.00 COMMENT '成本价变动金额（程序维护）',
    `cost_price_change_rate` DECIMAL(8,4) DEFAULT 0.0000 COMMENT '成本价变动比例（程序维护）',
    
    -- 采购价变更
    `old_purchase_price` DECIMAL(15,2) NULL COMMENT '原采购价',
    `new_purchase_price` DECIMAL(15,2) NULL COMMENT '新采购价',
    `purchase_price_change_amount` DECIMAL(15,2) DEFAULT 0.00 COMMENT '采购价变动金额（程序维护）',
    `purchase_price_change_rate` DECIMAL(8,4) DEFAULT 0.0000 COMMENT '采购价变动比例（程序维护）',
    
    -- 划线价变更
    `old_strikethrough_price` DECIMAL(15,2) NULL COMMENT '原划线价',
    `new_strikethrough_price` DECIMAL(15,2) NULL COMMENT '新划线价',
    `strikethrough_price_change_amount` DECIMAL(15,2) DEFAULT 0.00 COMMENT '划线价变动金额（程序维护）',
    `strikethrough_price_change_rate` DECIMAL(8,4) DEFAULT 0.0000 COMMENT '划线价变动比例（程序维护）',
    
    -- 销售价变更（如果需要）
    `old_sale_price` DECIMAL(15,2) NULL COMMENT '原销售价',
    `new_sale_price` DECIMAL(15,2) NULL COMMENT '新销售价',
    `sale_price_change_amount` DECIMAL(15,2) DEFAULT 0.00 COMMENT '销售价变动金额（程序维护）',
    `sale_price_change_rate` DECIMAL(8,4) DEFAULT 0.0000 COMMENT '销售价变动比例（程序维护）',
    
    -- 库存信息（来自ER图）
    `total_inventory` INT UNSIGNED DEFAULT 0 COMMENT '总库存',
    `available_inventory` INT UNSIGNED DEFAULT 0 COMMENT '可用库存',
    `blocked_inventory` INT UNSIGNED DEFAULT 0 COMMENT '冻结库存',
    `safety_stock` INT UNSIGNED DEFAULT 0 COMMENT '安全库存',
    `img_url` VARCHAR(500) DEFAULT '' COMMENT 'SKU图片URL',
    `sort_order` INT UNSIGNED DEFAULT 0 COMMENT '排序',
    
    -- 审核状态
    `sku_status` TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT 'SKU审核状态：1-待审核，2-审核通过，3-审核拒绝',
    `sku_review_comment` VARCHAR(500) DEFAULT '' COMMENT 'SKU审核备注',
    
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除：0-否，1-是',
    PRIMARY KEY (`id`),
    KEY `idx_request_id` (`request_id`),
    KEY `idx_product_request_id` (`product_request_id`),
    KEY `idx_sku_id` (`sku_id`),
    KEY `idx_sku_code` (`sku_code`),
    KEY `idx_operation_type` (`operation_type`),
    KEY `idx_sku_status` (`sku_status`),
    KEY `idx_create_time` (`create_time`),
    CONSTRAINT `fk_price_request_sku_request` FOREIGN KEY (`request_id`) REFERENCES `biz_price_spec_change_request` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_price_request_sku_product` FOREIGN KEY (`product_request_id`) REFERENCES `biz_price_spec_change_request_product` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='价格规格修改申请SKU明细表';

-- ================================
-- 4. 价格修改申请审核日志表
-- ================================
CREATE TABLE `biz_price_spec_change_request_log` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `request_id` BIGINT UNSIGNED NOT NULL COMMENT '申请ID，关联biz_price_spec_change_request.id',
    `operation_type` VARCHAR(50) NOT NULL COMMENT '操作类型：SUBMIT,REVIEW_PASS,REVIEW_REJECT,WITHDRAW,APPLY,EXPIRE',
    `operator_id` BIGINT UNSIGNED NOT NULL COMMENT '操作人ID',
    `operator_name` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '操作人姓名',
    `operator_type` VARCHAR(20) NOT NULL DEFAULT 'SUPPLIER' COMMENT '操作人类型：SUPPLIER,ADMIN,SYSTEM',
    `operation_desc` VARCHAR(200) DEFAULT '' COMMENT '操作描述',
    `operation_comment` TEXT COMMENT '操作备注',
    `before_status` TINYINT UNSIGNED NULL COMMENT '操作前状态',
    `after_status` TINYINT UNSIGNED NOT NULL COMMENT '操作后状态',
    `operation_data` LONGTEXT COMMENT '操作相关数据（JSON格式）',
    `operation_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    `ip_address` VARCHAR(50) DEFAULT '' COMMENT 'IP地址',
    `user_agent` VARCHAR(500) DEFAULT '' COMMENT '用户代理',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除：0-否，1-是',
    PRIMARY KEY (`id`),
    KEY `idx_request_id` (`request_id`),
    KEY `idx_operation_type` (`operation_type`),
    KEY `idx_operator_id` (`operator_id`),
    KEY `idx_operation_time` (`operation_time`),
    KEY `idx_create_time` (`create_time`),
    CONSTRAINT `fk_price_request_log_request` FOREIGN KEY (`request_id`) REFERENCES `biz_price_spec_change_request` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='价格规格修改申请审核日志表';

-- ================================
-- 5. 价格修改申请附件表
-- ================================
CREATE TABLE `biz_price_spec_change_request_attachment` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `request_id` BIGINT UNSIGNED NOT NULL COMMENT '申请ID，关联biz_price_spec_change_request.id',
    `file_name` VARCHAR(255) NOT NULL COMMENT '文件名',
    `file_url` VARCHAR(500) NOT NULL COMMENT '文件URL',
    `file_size` BIGINT UNSIGNED NOT NULL COMMENT '文件大小（字节）',
    `file_type` VARCHAR(100) DEFAULT '' COMMENT '文件类型',
    `attachment_type` VARCHAR(50) DEFAULT 'OTHER' COMMENT '附件类型：PRICE_COMPARE,SPEC_CHANGE,OTHER',
    `upload_user_id` BIGINT UNSIGNED NOT NULL COMMENT '上传人ID',
    `upload_user_name` VARCHAR(50) DEFAULT '' COMMENT '上传人姓名',
    `description` VARCHAR(500) DEFAULT '' COMMENT '附件描述',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除：0-否，1-是',
    PRIMARY KEY (`id`),
    KEY `idx_request_id` (`request_id`),
    KEY `idx_attachment_type` (`attachment_type`),
    KEY `idx_upload_user_id` (`upload_user_id`),
    KEY `idx_create_time` (`create_time`),
    CONSTRAINT `fk_price_request_attachment_request` FOREIGN KEY (`request_id`) REFERENCES `biz_price_spec_change_request` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='价格规格修改申请附件表';

-- ================================
-- 6. 价格修改申请统计表
-- ================================
CREATE TABLE `biz_price_spec_change_request_statistics` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `supplier_id` BIGINT UNSIGNED NOT NULL COMMENT '供应商ID',
    `stat_date` DATE NOT NULL COMMENT '统计日期',
    `stat_type` TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '统计类型：1-日统计，2-月统计，3-年统计',
    
    -- 申请数量统计
    `total_requests` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '总申请数',
    `pending_requests` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '待审核申请数',
    `approved_requests` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '已通过申请数',
    `rejected_requests` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '已拒绝申请数',
    `withdrawn_requests` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '已撤回申请数',
    `price_change_requests` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '价格修改申请数',
    `spec_change_requests` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '规格调整申请数',
    `mixed_change_requests` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '混合修改申请数',
    
    -- 商品和SKU统计
    `total_products` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '涉及商品总数',
    `total_skus` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '涉及SKU总数',
    `avg_products_per_request` DECIMAL(8,2) DEFAULT 0.00 COMMENT '平均每个申请涉及商品数',
    `avg_skus_per_request` DECIMAL(8,2) DEFAULT 0.00 COMMENT '平均每个申请涉及SKU数',
    
    -- 价格变动统计
    `total_price_change_amount` DECIMAL(15,2) DEFAULT 0.00 COMMENT '总价格变动金额',
    `avg_price_change_amount` DECIMAL(15,2) DEFAULT 0.00 COMMENT '平均价格变动金额',
    `max_price_change_amount` DECIMAL(15,2) DEFAULT 0.00 COMMENT '最大价格变动金额',
    `avg_price_change_rate` DECIMAL(8,4) DEFAULT 0.0000 COMMENT '平均价格变动比例',
    `max_price_change_rate` DECIMAL(8,4) DEFAULT 0.0000 COMMENT '最大价格变动比例',
    
    -- 审核效率统计
    `avg_review_hours` DECIMAL(8,2) DEFAULT 0.00 COMMENT '平均审核时长（小时）',
    `max_review_hours` DECIMAL(8,2) DEFAULT 0.00 COMMENT '最大审核时长（小时）',
    `approval_rate` DECIMAL(8,4) DEFAULT 0.0000 COMMENT '审核通过率',
    
    -- 时间统计
    `first_request_time` DATETIME NULL COMMENT '首次申请时间',
    `last_request_time` DATETIME NULL COMMENT '最近申请时间',
    
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除：0-否，1-是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_supplier_date_type` (`supplier_id`, `stat_date`, `stat_type`),
    KEY `idx_supplier_id` (`supplier_id`),
    KEY `idx_stat_date` (`stat_date`),
    KEY `idx_stat_type` (`stat_type`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='价格规格修改申请统计表';

-- ================================
-- 7. 索引优化建议
-- ================================
-- 为了提高查询性能，建议根据实际业务场景添加复合索引：

-- 申请主表的复合索引
ALTER TABLE `biz_price_spec_change_request` 
ADD INDEX `idx_supplier_status_time` (`supplier_id`, `status`, `submit_time`);

ALTER TABLE `biz_price_spec_change_request` 
ADD INDEX `idx_status_submit_time` (`status`, `submit_time`);

-- 商品明细表的复合索引
ALTER TABLE `biz_price_spec_change_request_product` 
ADD INDEX `idx_request_spu` (`request_id`, `spu_id`);

-- SKU明细表的复合索引
ALTER TABLE `biz_price_spec_change_request_sku` 
ADD INDEX `idx_request_sku` (`request_id`, `sku_id`);

ALTER TABLE `biz_price_spec_change_request_sku` 
ADD INDEX `idx_request_status` (`request_id`, `sku_status`);

-- ================================
-- 8. 数据维护说明
-- ================================
/*
由于禁止使用视图和触发器，以下字段需要在程序中维护：

1. biz_price_spec_change_request 表：
   - product_count: 申请涉及的商品数量
   - sku_count: 申请涉及的SKU数量
   - total_price_change_amount: 总价格变动金额
   - avg_price_change_rate: 平均价格变动比例
   - max_price_change_rate: 最大价格变动比例

2. biz_price_spec_change_request_product 表：
   - sku_add_count, sku_update_count, sku_delete_count: SKU数量统计
   - total_sku_count: 该商品的SKU总数
   - changed_sku_count: 变更的SKU数量
   - avg_price_change_amount: 平均价格变动金额
   - max_price_change_amount: 最大价格变动金额
   - price_change_summary: 价格变更摘要
   - spec_change_summary: 规格变更摘要

3. biz_price_spec_change_request_sku 表：
   - cost_price_change_amount: 成本价变动金额
   - cost_price_change_rate: 成本价变动比例
   - purchase_price_change_amount: 采购价变动金额
   - purchase_price_change_rate: 采购价变动比例
   - strikethrough_price_change_amount: 划线价变动金额
   - strikethrough_price_change_rate: 划线价变动比例
   - sale_price_change_amount: 销售价变动金额
   - sale_price_change_rate: 销售价变动比例

4. biz_price_spec_change_request_statistics 表：
   - 所有统计字段都需要通过定时任务或实时计算维护

维护时机：
- 插入/更新申请时：实时更新相关统计字段
- 审核申请时：更新状态相关统计
- 定时任务：每日/每月更新统计表数据
*/

-- ================================
-- 9. 初始化数据和配置
-- ================================

-- 插入申请状态配置（如果需要配置表）
-- INSERT INTO `sys_dict_data` (`dict_type`, `dict_label`, `dict_value`, `dict_sort`, `status`) VALUES
-- ('price_request_status', '待审核', '1', 1, 1),
-- ('price_request_status', '审核通过', '2', 2, 1),
-- ('price_request_status', '审核拒绝', '3', 3, 1),
-- ('price_request_status', '已撤回', '4', 4, 1),
-- ('price_request_status', '已过期', '5', 5, 1);

-- 插入操作类型配置
-- INSERT INTO `sys_dict_data` (`dict_type`, `dict_label`, `dict_value`, `dict_sort`, `status`) VALUES
-- ('price_request_operation', '新增', '1', 1, 1),
-- ('price_request_operation', '修改', '2', 2, 1),
-- ('price_request_operation', '删除', '3', 3, 1);

-- ================================
-- DDL 创建完成
-- ================================
 