-- 客户商品操作日志表
CREATE TABLE `biz_company_spu_operation_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `company_spu_id` bigint(20) DEFAULT NULL COMMENT '客户商品SPU ID',
  `supplier_spu_id` bigint(20) DEFAULT NULL COMMENT '供应商商品SPU ID（关联原始商品）',
  `company_id` bigint(20) NOT NULL COMMENT '客户公司ID',
  `operation_type` varchar(50) NOT NULL COMMENT '操作类型：COMPANY_REVIEW_PASS,COMPANY_REVIEW_REJECT,COMPANY_BATCH_REVIEW,COMPANY_PRICE_UPDATE,COMPANY_BATCH_PRICE_UPDATE,COMPANY_STATUS_UPDATE,COMPANY_BATCH_STATUS_UPDATE,COMPANY_PRICE_CHANGE_REVIEW,ONLINE,OFFLINE',
  `operation_category` varchar(50) NOT NULL COMMENT '操作分类：REVIEW,PRICE_CHANGE,STATUS,COMPANY_MANAGEMENT',
  `operator_id` bigint(20) NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) NOT NULL COMMENT '操作人姓名',
  `operator_type` varchar(50) NOT NULL COMMENT '操作人类型：COMPANY_ADMIN,CUSTOMER,SYSTEM',
  `operation_desc` varchar(500) DEFAULT NULL COMMENT '操作描述',
  `operation_comment` varchar(1000) DEFAULT NULL COMMENT '操作备注/原因',
  `before_status` int(11) DEFAULT NULL COMMENT '操作前状态',
  `after_status` int(11) DEFAULT NULL COMMENT '操作后状态',
  `before_data` longtext COMMENT '操作前数据（JSON格式）',
  `after_data` longtext COMMENT '操作后数据（JSON格式）',
  `change_fields` varchar(500) DEFAULT NULL COMMENT '变更字段列表（JSON格式）',
  `related_request_id` bigint(20) DEFAULT NULL COMMENT '关联的申请ID（改价申请等）',
  `related_request_no` varchar(100) DEFAULT NULL COMMENT '关联的申请单号',
  `operation_time` datetime NOT NULL COMMENT '操作时间',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `client_info` varchar(100) DEFAULT NULL COMMENT '客户端信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  PRIMARY KEY (`id`),
  KEY `idx_company_spu_id` (`company_spu_id`),
  KEY `idx_supplier_spu_id` (`supplier_spu_id`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_operation_time` (`operation_time`),
  KEY `idx_related_request_id` (`related_request_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户商品操作日志表'; 