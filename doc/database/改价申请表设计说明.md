# 改价申请表设计说明

## 1. 设计概述

### 1.1 业务背景
供应商在商品上架后，可能因为市场变化、成本调整、规格扩展等原因需要修改商品信息。为了保证平台商品信息的准确性和审核的规范性，设计了改价申请审核流程。

### 1.2 设计原则
- **数据完整性**：记录完整的变更前后数据对比
- **审核可追溯**：完整的审核流程和操作日志
- **灵活扩展**：支持多种申请类型和审核场景
- **性能优化**：合理的索引设计和数据分离
- **业务友好**：支持批量操作和模板化申请

## 2. 表结构设计

### 2.1 核心表关系图

```
price_change_request (主表)
├── price_change_request_item (商品明细)
│   └── price_change_request_sku (SKU明细)
├── price_change_request_log (操作日志)
├── price_change_request_attachment (附件)
└── price_change_request_template (申请模板)
```

### 2.2 主要表说明

#### 2.2.1 改价申请主表 (price_change_request)

**核心字段说明：**

| 字段组 | 字段名 | 说明 | 设计考虑 |
|--------|--------|------|----------|
| 基础信息 | request_no | 申请单号 | 格式：PCR+yyyyMMdd+6位序号，便于查找和排序 |
| | supplier_id/name | 供应商信息 | 冗余供应商名称，提升查询效率 |
| | request_type | 申请类型 | 1-价格修改，2-规格调整，3-价格+规格 |
| 申请内容 | request_reason | 申请原因 | 必填，用于审核参考 |
| | product_count/sku_count | 数量统计 | 便于快速了解申请规模 |
| 流程控制 | status | 申请状态 | 支持部分通过状态，灵活处理复杂场景 |
| | priority | 优先级 | 支持紧急申请的优先处理 |
| 时间管理 | expected_effect_time | 期望生效时间 | 供应商可指定期望的生效时间 |
| | review_deadline | 审核截止时间 | 自动计算或手动设置 |
| 统计分析 | total_price_change_amount | 总价格变动 | 用于影响分析和统计报表 |
| | avg_price_change_rate | 平均变动比例 | 快速评估变动幅度 |

**状态流转：**
```
待审核(1) → 审核通过(2) / 审核拒绝(3) / 部分通过(5)
待审核(1) → 已撤回(4)
```

#### 2.2.2 商品明细表 (price_change_request_item)

**设计特点：**
- **数据快照**：original_data 和 new_data 保存完整的变更前后数据
- **变更摘要**：change_summary 提取关键变更点，便于快速审核
- **影响分析**：price_impact_analysis 和 inventory_impact_analysis 自动计算业务影响
- **细粒度状态**：支持单个商品的独立审核状态

#### 2.2.3 SKU明细表 (price_change_request_sku)

**核心功能：**
- **操作类型**：新增、修改、删除、恢复，支持复杂的规格调整
- **规格信息**：详细记录规格名称和值，便于审核和展示
- **变动比例**：自动计算各价格字段的变动比例
- **业务影响**：记录变更原因和业务影响说明

**价格字段设计：**
```sql
-- 每个价格字段都有原值、新值、变动比例三个字段
original_cost_price → new_cost_price → cost_price_change_rate
original_purchase_price → new_purchase_price → purchase_price_change_rate
original_strikethrough_price → new_strikethrough_price → strikethrough_price_change_rate
```

#### 2.2.4 操作日志表 (price_change_request_log)

**日志完整性：**
- 记录所有操作的详细信息
- 支持操作详情的JSON存储
- 记录操作环境信息（IP、User-Agent）
- 支持系统自动操作的记录

#### 2.2.5 附件表 (price_change_request_attachment)

**文件管理：**
- 支持多种文件类型和分类
- 记录文件元信息（大小、类型、上传时间）
- 软删除设计，保证数据安全

#### 2.2.6 申请模板表 (price_change_request_template)

**模板化申请：**
- 支持供应商自定义模板和系统公共模板
- JSON格式存储模板内容，灵活配置
- 统计使用次数，优化模板推荐

## 3. 索引设计策略

### 3.1 查询场景分析

| 查询场景 | 索引设计 | 说明 |
|----------|----------|------|
| 供应商查看自己的申请 | `idx_supplier_status_time` | 复合索引：supplier_id + status + submit_time DESC |
| 管理员按状态查看申请 | `idx_status_submit_time` | 复合索引：status + submit_time DESC |
| 申请类型统计 | `idx_type_status` | 复合索引：request_type + status |
| 审核截止时间提醒 | `idx_review_deadline` | 单列索引：review_deadline |
| 申请单号查询 | `uk_request_no` | 唯一索引：request_no |

### 3.2 性能优化建议

1. **分区策略**：按提交时间分区，提升历史数据查询性能
2. **归档策略**：定期归档超过一年的已完成申请
3. **读写分离**：统计查询使用只读从库
4. **缓存策略**：热点数据使用Redis缓存

## 4. 业务流程设计

### 4.1 申请提交流程

```mermaid
graph TD
    A[供应商发起申请] --> B[选择申请类型]
    B --> C[填写申请信息]
    C --> D[上传附件]
    D --> E[提交申请]
    E --> F[生成申请单号]
    F --> G[记录操作日志]
    G --> H[发送审核通知]
```

### 4.2 审核处理流程

```mermaid
graph TD
    A[管理员接收申请] --> B[查看申请详情]
    B --> C[对比变更内容]
    C --> D{审核决策}
    D -->|通过| E[更新商品信息]
    D -->|拒绝| F[记录拒绝原因]
    D -->|部分通过| G[选择通过项目]
    E --> H[发送通知]
    F --> H
    G --> H
    H --> I[记录操作日志]
```

### 4.3 状态机设计

```mermaid
stateDiagram-v2
    [*] --> 待审核
    待审核 --> 审核通过
    待审核 --> 审核拒绝
    待审核 --> 部分通过
    待审核 --> 已撤回
    审核拒绝 --> 待审核 : 重新提交
    部分通过 --> 待审核 : 补充申请
    已撤回 --> [*]
    审核通过 --> [*]
```

## 5. 数据完整性保证

### 5.1 外键约束
- 商品明细表关联主表：级联删除
- SKU明细表关联商品明细：级联删除
- 操作日志表关联主表：级联删除
- 附件表关联主表：级联删除

### 5.2 触发器设计
- 自动更新商品数量和SKU数量统计
- 自动计算价格变动金额和比例
- 自动记录状态变更日志

### 5.3 数据校验规则
- 申请单号唯一性校验
- 状态流转合法性校验
- 价格变动合理性校验
- 文件类型和大小限制

## 6. 扩展性考虑

### 6.1 支持的申请类型扩展
- 当前：价格修改、规格调整、价格+规格
- 未来：物流信息修改、商品描述修改、分类调整等

### 6.2 审核流程扩展
- 多级审核：支持初审、复审等多级流程
- 条件审核：根据变动幅度自动分配审核级别
- 批量审核：支持批量通过/拒绝操作

### 6.3 数据分析扩展
- 供应商信用评级：基于申请通过率和变动合理性
- 市场趋势分析：基于价格变动数据分析市场趋势
- 审核效率分析：优化审核流程和人员配置

## 7. 安全性设计

### 7.1 数据安全
- 敏感数据加密存储
- 操作日志完整记录
- 数据备份和恢复机制

### 7.2 权限控制
- 供应商只能查看和操作自己的申请
- 管理员按角色分配不同的审核权限
- 操作记录包含完整的权限验证信息

### 7.3 审计要求
- 所有操作可追溯
- 数据变更有完整记录
- 支持合规性检查和报告

## 8. 监控和告警

### 8.1 业务监控
- 申请提交量监控
- 审核时效监控
- 异常申请监控（大幅价格变动）

### 8.2 技术监控
- 数据库性能监控
- 接口响应时间监控
- 错误率监控

### 8.3 告警机制
- 审核超时告警
- 异常数据告警
- 系统故障告警 