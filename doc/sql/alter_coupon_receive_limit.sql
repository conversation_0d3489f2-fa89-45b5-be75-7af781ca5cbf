-- 优惠券表添加领取限制相关字段
-- 执行前请备份数据库

-- 1. 删除旧的每人限领数量字段（如果存在）
ALTER TABLE biz_mall_coupon DROP COLUMN IF EXISTS per_user_limit;

-- 2. 添加新的领取限制字段
ALTER TABLE biz_mall_coupon 
ADD COLUMN receive_limit_type INT DEFAULT 1 COMMENT '领取限制类型：1-不限制领取次数，2-限制领取，3-每天限制领取' AFTER received_quantity,
ADD COLUMN per_user_total_limit INT DEFAULT NULL COMMENT '每人总共限领次数' AFTER receive_limit_type,
ADD COLUMN per_user_daily_limit INT DEFAULT NULL COMMENT '每人每天限领次数' AFTER per_user_total_limit;

-- 3. 更新现有数据，设置默认值
UPDATE biz_mall_coupon 
SET receive_limit_type = 1, 
    per_user_total_limit = NULL, 
    per_user_daily_limit = NULL 
WHERE receive_limit_type IS NULL;

-- 4. 查看表结构确认修改
DESCRIBE biz_mall_coupon;

-- 5. 查看现有数据
SELECT 
    id, 
    name, 
    receive_limit_type,
    per_user_total_limit,
    per_user_daily_limit,
    promotion_type
FROM biz_mall_coupon 
ORDER BY id DESC 
LIMIT 10; 