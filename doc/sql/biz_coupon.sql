-- 商城优惠券主表
CREATE TABLE `biz_mall_coupon` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `mall_id` bigint(20) NOT NULL COMMENT '商城ID',
  `coupon_code` varchar(32) NOT NULL COMMENT '优惠券编号',
  `name` varchar(100) NOT NULL COMMENT '优惠券名称',
  `description` varchar(500) DEFAULT NULL COMMENT '优惠券描述',
  `type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '优惠券类型：1-满减券，2-折扣券，3-免邮券',
  `discount_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '优惠类型：1-固定金额，2-百分比折扣',
  `discount_value` decimal(10,2) NOT NULL COMMENT '优惠金额/折扣百分比',
  `min_order_amount` decimal(10,2) DEFAULT '0.00' COMMENT '最小订单金额',
  `max_discount_amount` decimal(10,2) DEFAULT NULL COMMENT '最大优惠金额（折扣券专用）',
  `promotion_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '推广方式：1-买家领取，2-卖家发放',
  `total_quantity` int(11) NOT NULL DEFAULT '0' COMMENT '发放总量',
  `used_quantity` int(11) NOT NULL DEFAULT '0' COMMENT '已使用数量',
  `received_quantity` int(11) NOT NULL DEFAULT '0' COMMENT '已领取数量',
  `per_user_limit` int(11) DEFAULT '1' COMMENT '每人限领数量',
  `validity_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '有效期类型：1-固定日期，2-领券当日起，3-领券次日起',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间（固定日期）',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间（固定日期）',
  `validity_days` int(11) DEFAULT NULL COMMENT '有效天数（相对日期）',
  `receive_start_time` datetime DEFAULT NULL COMMENT '领取开始时间',
  `receive_end_time` datetime DEFAULT NULL COMMENT '领取结束时间',
  `use_scope` tinyint(4) NOT NULL DEFAULT '1' COMMENT '使用范围：1-全场通用，2-部分商品可用，3-部分商品不可用',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-未开始，1-进行中，2-已结束',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_coupon_code` (`coupon_code`),
  KEY `idx_mall_id` (`mall_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商城优惠券表';

-- 优惠券商品关联表
CREATE TABLE `biz_mall_coupon_product` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `coupon_id` bigint(20) NOT NULL COMMENT '优惠券ID',
  `spu_id` bigint(20) NOT NULL COMMENT '商品SPU ID',
  `relation_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '关联类型：1-可用商品，2-不可用商品',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_coupon_id` (`coupon_id`),
  KEY `idx_spu_id` (`spu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='优惠券商品关联表';

-- 优惠券分类关联表
CREATE TABLE `biz_mall_coupon_category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `coupon_id` bigint(20) NOT NULL COMMENT '优惠券ID',
  `category_id` bigint(20) NOT NULL COMMENT '分类ID',
  `relation_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '关联类型：1-可用分类，2-不可用分类',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_coupon_id` (`coupon_id`),
  KEY `idx_category_id` (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='优惠券分类关联表';

-- 用户优惠券表
CREATE TABLE `biz_user_coupon` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `coupon_id` bigint(20) NOT NULL COMMENT '优惠券ID',
  `coupon_code` varchar(32) NOT NULL COMMENT '优惠券编号',
  `receive_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '领取方式：1-用户主动领取，2-系统发放',
  `use_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '使用状态：0-未使用，1-已使用，2-已过期',
  `use_time` datetime DEFAULT NULL COMMENT '使用时间',
  `order_id` bigint(20) DEFAULT NULL COMMENT '使用订单ID',
  `valid_start_time` datetime NOT NULL COMMENT '有效期开始时间',
  `valid_end_time` datetime NOT NULL COMMENT '有效期结束时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '领取时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_coupon_id` (`coupon_id`),
  KEY `idx_use_status` (`use_status`),
  KEY `idx_valid_time` (`valid_start_time`, `valid_end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户优惠券表';

-- 优惠券发放记录表
CREATE TABLE `biz_coupon_send_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `coupon_id` bigint(20) NOT NULL COMMENT '优惠券ID',
  `send_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '发放类型：1-批量发放，2-单个发放',
  `send_quantity` int(11) NOT NULL DEFAULT '0' COMMENT '发放数量',
  `success_quantity` int(11) NOT NULL DEFAULT '0' COMMENT '发放成功数量',
  `failed_quantity` int(11) NOT NULL DEFAULT '0' COMMENT '发放失败数量',
  `target_user_condition` text COMMENT '目标用户条件（JSON格式）',
  `send_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '发放状态：0-发放中，1-发放完成，2-发放失败',
  `sender_id` bigint(20) DEFAULT NULL COMMENT '发放人ID',
  `sender_name` varchar(50) DEFAULT NULL COMMENT '发放人姓名',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_coupon_id` (`coupon_id`),
  KEY `idx_send_status` (`send_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='优惠券发放记录表'; 