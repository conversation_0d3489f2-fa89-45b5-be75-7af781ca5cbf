# 商城分类自动绑定标准分类下SPU功能

## 功能概述

在保存商城自定义分类时，系统会自动将绑定的标准分类下的所有SPU绑定到该商城分类上。但是，如果SPU已经被手动绑定到该商城分类，则不会重复绑定。

## 实现逻辑

### 1. 触发时机
- 在保存商城自定义分类时（`CategoryController.saveCustomCategory`方法）
- 当请求中包含`boundProductCategoryIds`（绑定的标准分类ID列表）时触发

### 2. 绑定逻辑
1. **清理旧绑定**：先删除之前通过分类绑定的SPU关系（`bind_type='category'`）
2. **获取SPU列表**：遍历每个标准分类，获取该分类下的所有启用状态的SPU
3. **检查重复绑定**：对于每个SPU，检查是否已经手动绑定到该商城分类
4. **跳过手动绑定**：如果SPU已经手动绑定，则跳过自动绑定
5. **创建绑定关系**：为未手动绑定的SPU创建分类绑定关系

### 3. 绑定关系区分
- **手动绑定**：`bind_type='manual'`，用户在"绑定产品"Tab中手动选择的SPU
- **分类绑定**：`bind_type='category'`，通过绑定标准分类自动绑定的SPU
- **来源标识**：分类绑定的记录会在`source_category_id`字段记录来源标准分类ID

## 核心方法

### CategoryController.autoBindSpusFromStandardCategories
```java
private void autoBindSpusFromStandardCategories(Long mallCategoryId, Long mallId, 
                                               List<Long> standardCategoryIds, Long companyId)
```

**参数说明**：
- `mallCategoryId`：商城分类ID
- `mallId`：商城ID  
- `standardCategoryIds`：绑定的标准分类ID列表
- `companyId`：客户公司ID

**处理流程**：
1. 删除之前的分类绑定关系
2. 遍历标准分类，获取SPU列表
3. 检查SPU是否已手动绑定
4. 创建新的分类绑定关系
5. 批量保存绑定关系

### BizCompanySpuService.getSpusByCategoryId
```java
public List<BizCompanySpu> getSpusByCategoryId(Long companyId, Long categoryId)
```

根据客户公司ID和标准分类ID获取SPU列表，只返回启用状态的SPU。

### BizMallCategorySpuService.isSpuManuallyBound
```java
public boolean isSpuManuallyBound(Long mallCategoryId, Long companySpuId)
```

检查SPU是否已经手动绑定到商城分类，避免重复绑定。

### BizMallCategorySpuService.deleteByMallCategoryIdAndBindTypeAndSourceCategory
```java
public boolean deleteByMallCategoryIdAndBindTypeAndSourceCategory(Long mallCategoryId, String bindType, Long sourceCategoryId)
```

删除指定商城分类下特定绑定类型和来源分类的绑定关系，用于清理旧的分类绑定。

## 数据库表结构

### biz_mall_category_spu表关键字段
- `bind_type`：绑定类型（'manual'或'category'）
- `source_category_id`：来源分类ID（分类绑定时记录来源标准分类）
- `mall_category_id`：商城分类ID
- `company_spu_id`：公司SPU ID

## 业务规则

1. **优先级**：手动绑定优先于分类绑定，已手动绑定的SPU不会被分类绑定覆盖
2. **增量更新**：每次保存时会先清理旧的分类绑定，再创建新的分类绑定
3. **状态过滤**：只绑定启用状态（`status=1`）的SPU
4. **去重机制**：通过检查手动绑定状态避免重复绑定
5. **日志记录**：详细记录绑定过程，包括绑定数量、跳过数量等统计信息

## 使用场景

1. **批量商品管理**：通过绑定标准分类快速将大量SPU归类到商城分类
2. **动态分类**：当标准分类下新增SPU时，下次保存商城分类时会自动绑定新SPU
3. **精细控制**：重要SPU可以手动绑定，确保不被自动绑定逻辑影响
4. **分类重组**：修改绑定的标准分类时，系统会自动更新SPU绑定关系

## 注意事项

1. **性能考虑**：大量SPU绑定时可能影响保存性能，建议分批处理
2. **数据一致性**：使用事务确保绑定关系的一致性
3. **权限控制**：只有有权限的用户才能触发自动绑定
4. **日志监控**：通过日志监控绑定过程，便于问题排查 