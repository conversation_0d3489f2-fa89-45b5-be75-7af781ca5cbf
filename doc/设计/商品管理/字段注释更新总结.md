# 商品状态字段更新总结

## 更新概述

根据ER图中的最新定义，将商品状态管理从双字段设计（status + review_status）简化为单字段设计（status），统一管理商品的完整生命周期状态。

## 字段变更详情

### 原设计（双字段）
```sql
status INT NOT NULL DEFAULT 2 COMMENT '状态：0-已下架，1-销售中，2-仓库中，3-审核中'
review_status INT NOT NULL DEFAULT 0 COMMENT '审核状态：0-未提交，1-审核中，2-审核拒绝，3-审核通过'
```

### 新设计（单字段）
```sql
status INT NOT NULL DEFAULT 2 COMMENT '状态：0-已下架，1-销售中，2-草稿，3-审核中，4-审核拒绝'
```

## 状态值对应关系

| 新状态值 | 状态名称 | 原状态组合 | 说明 |
|----------|----------|------------|------|
| 0 | 已下架 | status=0, review_status=3 | 商品已下架停止销售 |
| 1 | 销售中 | status=1, review_status=3 | 商品审核通过正在销售 |
| 2 | 草稿 | status=2, review_status=0 | 商品创建但未提交审核 |
| 3 | 审核中 | status=3, review_status=1 | 商品已提交等待审核 |
| 4 | 审核拒绝 | status=2, review_status=2 | 商品审核被拒绝 |

## 更新的文档列表

### 1. 数据库设计文档
- 更新了 `biz_supplier_spu` 表的status字段注释
- 删除了 `review_status` 字段定义
- 更新了审核记录表中的字段说明
- 简化了数据库升级脚本

### 2. 业务流程设计文档
- 更新了状态定义表格
- 修改了状态流转图
- 更新了权限操作表格
- 简化了状态过滤代码示例

### 3. 系统架构设计文档
- 更新了数据模型中的字段说明
- 简化了状态管理描述

### 4. 产品需求文档
- 更新了商品状态定义表格
- 统一了状态值说明

### 5. API接口设计文档
- 删除了响应数据中的 `reviewStatus` 字段
- 更新了审核记录接口的数据结构
- 简化了状态相关的接口说明

### 6. README文档
- 更新了核心业务特性中的状态管理说明
- 修改了关键设计决策的描述

### 7. ER图文档
- 确认了 `biz_supplier_spu.status` 字段的最新注释定义

## 业务逻辑影响

### 状态流转简化
- 原来需要同时判断两个字段的组合状态
- 现在只需要判断单一status字段
- 减少了状态判断的复杂度

### 代码实现简化
```java
// 原来的状态过滤（双字段）
wrapper.and(w -> w
    .or(subWrapper -> subWrapper.eq(BizSupplierSpu::getStatus, 3).eq(BizSupplierSpu::getReviewStatus, 1)) // 审核中
    .or(subWrapper -> subWrapper.eq(BizSupplierSpu::getStatus, 1).eq(BizSupplierSpu::getReviewStatus, 3)) // 销售中
    .or(subWrapper -> subWrapper.eq(BizSupplierSpu::getStatus, 0).eq(BizSupplierSpu::getReviewStatus, 3)) // 已下架
);

// 现在的状态过滤（单字段）
wrapper.in(BizSupplierSpu::getStatus, Arrays.asList(3, 1, 0)); // 审核中、销售中、已下架
```

### 数据库优化
- 减少了一个字段的存储空间
- 简化了索引设计
- 降低了查询复杂度

## 兼容性考虑

### 数据迁移
如果现有系统使用双字段设计，需要进行数据迁移：

```sql
-- 数据迁移脚本示例
UPDATE biz_supplier_spu SET 
  status = CASE 
    WHEN status = 2 AND review_status = 0 THEN 2  -- 草稿
    WHEN status = 3 AND review_status = 1 THEN 3  -- 审核中
    WHEN status = 2 AND review_status = 2 THEN 4  -- 审核拒绝
    WHEN status = 1 AND review_status = 3 THEN 1  -- 销售中
    WHEN status = 0 AND review_status = 3 THEN 0  -- 已下架
    ELSE status
  END;

-- 删除review_status字段
ALTER TABLE biz_supplier_spu DROP COLUMN review_status;
```

### API兼容性
- 前端需要更新状态判断逻辑
- 移除对 `reviewStatus` 字段的依赖
- 更新状态显示组件

## 优势总结

1. **简化设计**: 单字段管理所有状态，降低复杂度
2. **提高性能**: 减少字段数量，优化查询效率
3. **易于维护**: 状态逻辑更清晰，代码更简洁
4. **降低错误**: 减少状态组合错误的可能性
5. **便于扩展**: 新增状态只需要增加状态值

## 注意事项

1. 确保所有相关代码都已更新状态判断逻辑
2. 前端组件需要同步更新状态显示
3. 测试用例需要覆盖所有新的状态值
4. 文档需要保持与实际实现的一致性

---

**更新日期**: 2024-01-XX  
**更新人员**: LinkBuy技术团队  
**影响范围**: 商品管理模块全部文档 