# 商品管理模块系统架构设计

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2024-01-XX
- **最后更新**: 2024-01-XX
- **架构师**: LinkBuy技术团队
- **技术负责人**: LinkBuy技术团队

## 1. 架构概述

### 1.1 系统架构图

```mermaid
graph TB
    subgraph "前端层"
        A[平台管理端<br/>linkBuyAdmin]
        B[客户管理端<br/>linkBuyMallAdmin]
        C[供应商端<br/>linkBuySupplier]
    end
    
    subgraph "API网关层"
        D[API Gateway]
    end
    
    subgraph "服务层"
        E[平台API<br/>platform-api]
        F[客户API<br/>mall-admin-api]
        G[供应商API<br/>supplier-api]
    end
    
    subgraph "业务逻辑层"
        H[商品管理服务<br/>ProductService]
        I[供应商管理服务<br/>SupplierService]
        J[审核服务<br/>ReviewService]
        K[权限服务<br/>AuthService]
    end
    
    subgraph "数据访问层"
        L[商品数据访问<br/>ProductMapper]
        M[供应商数据访问<br/>SupplierMapper]
        N[审核记录访问<br/>ReviewMapper]
    end
    
    subgraph "数据存储层"
        O[(MySQL数据库)]
        P[(Redis缓存)]
        Q[文件存储<br/>OSS/MinIO]
    end
    
    A --> D
    B --> D
    C --> D
    D --> E
    D --> F
    D --> G
    E --> H
    F --> H
    G --> H
    H --> I
    H --> J
    H --> K
    H --> L
    I --> M
    J --> N
    L --> O
    M --> O
    N --> O
    H --> P
    H --> Q
```

### 1.2 技术栈选择

#### 前端技术栈
- **框架**: Vue 3 + TypeScript
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router
- **构建工具**: Vite

#### 后端技术栈
- **框架**: Spring Boot 2.7+
- **ORM**: MyBatis Plus
- **数据库**: MySQL 8.0
- **缓存**: Redis
- **文件存储**: OSS/MinIO
- **权限控制**: Spring Security

### 1.3 架构原则

- **分层架构**: 清晰的分层结构，职责分离
- **数据隔离**: 严格的数据权限控制
- **服务化**: 按业务域拆分服务
- **可扩展性**: 支持水平扩展
- **高可用**: 无单点故障

## 2. 模块架构

### 2.1 前端架构

#### 2.1.1 平台管理端
```
linkBuyAdmin/
├── src/
│   ├── views/biz/goods/          # 商品管理页面
│   │   ├── index.vue            # 商品列表
│   │   ├── detail.vue           # 商品详情
│   │   └── components/          # 商品组件
│   ├── api/goods/               # 商品API
│   └── utils/                   # 工具函数
```

**功能特性**:
- 平台供应商商品管理
- 客户供应商商品审核
- 强制上下架功能
- 数据统计报表

#### 2.1.2 客户管理端
```
linkBuyMallAdmin/
├── src/
│   ├── views/goods/             # 商品管理页面
│   ├── api/goods/               # 商品API
```

**功能特性**:
- 客户供应商商品管理
- 商品审核功能
- 供应商商品统计

#### 2.1.3 供应商端
```
linkBuySupplier/
├── src/
│   ├── views/goods/             # 商品管理页面
│   ├── api/goods/               # 商品API
```

**功能特性**:
- 商品CRUD操作
- 商品状态管理
- 审核提交和查看

### 2.2 后端架构

#### 2.2.1 API层设计
```java
// 平台管理端API
@RestController
@RequestMapping("/product")
public class ProductController {
    // 平台供应商商品管理
    // 客户供应商商品审核
    // 强制操作功能
}

// 供应商端API
@RestController
@RequestMapping("/product")
public class ProductController {
    // 商品CRUD操作
    // 状态管理
    // 审核提交
}
```

#### 2.2.2 业务逻辑层
```java
@Service
public class BizSupplierSpuService {
    // 商品业务逻辑
    // 状态流转控制
    // 权限验证
}

@Service
public class ProductReviewService {
    // 审核业务逻辑
    // 审核记录管理
}
```

#### 2.2.3 数据访问层
```java
@Mapper
public interface BizSupplierSpuMapper {
    // 商品数据访问
    // 复杂查询
}

@Mapper
public interface BizSupplierMapper {
    // 供应商数据访问
}
```

### 2.3 数据库架构

#### 2.3.1 数据库选型
- **主数据库**: MySQL 8.0
- **缓存数据库**: Redis 6.0
- **搜索引擎**: Elasticsearch (预留)

#### 2.3.2 表结构设计
```sql
-- 商品主表
biz_supplier_spu
├── id (主键)
├── supplier_id (供应商ID)
├── name (商品名称)
├── status (商品状态：0-已下架，1-销售中，2-草稿，3-审核中，4-审核拒绝)
└── ...

-- 商品SKU表
biz_supplier_sku
├── id (主键)
├── spu_id (商品ID)
├── spec_info (规格信息)
├── price (价格)
└── stock (库存)

-- 供应商表
biz_supplier
├── id (主键)
├── supplier_type (供应商类型)
├── company_id (所属公司)
└── ...
```

#### 2.3.3 索引设计
```sql
-- 商品表索引
CREATE INDEX idx_supplier_id ON biz_supplier_spu(supplier_id);
CREATE INDEX idx_status ON biz_supplier_spu(status);
CREATE INDEX idx_create_time ON biz_supplier_spu(create_time);

-- 供应商表索引
CREATE INDEX idx_supplier_type ON biz_supplier(supplier_type);
CREATE INDEX idx_company_id ON biz_supplier(company_id);
```

## 3. 服务架构

### 3.1 应用架构

#### 单体应用架构
- platform-api: 平台管理端服务
- mall-admin-api: 客户管理端服务  
- supplier-api: 供应商端服务

### 3.2 服务间通信

#### 通信方式
- 直接数据库访问
- 共享数据模型
- 内部方法调用

### 3.3 配置管理

#### 配置方式
- Spring Boot配置文件
- 环境变量配置
- 数据库配置表

## 4. 数据架构

### 4.1 数据模型

#### 核心实体关系
```mermaid
erDiagram
    BizSupplier ||--o{ BizSupplierSpu : "拥有"
    BizSupplierSpu ||--o{ BizSupplierSku : "包含"
    BizSupplierSpu ||--o{ ReviewLog : "审核记录"
    
    BizSupplier {
        bigint id PK
        int supplier_type
        bigint company_id
        string name
    }
    
    BizSupplierSpu {
        bigint id PK
        bigint supplier_id FK
        string name
        int status
    }
    
    BizSupplierSku {
        bigint id PK
        bigint spu_id FK
        json spec_info
        decimal price
        int stock
    }
    
    ReviewLog {
        bigint id PK
        bigint spu_id FK
        string action
        bigint reviewer_id
    }
```

### 4.2 数据流转

#### 商品数据流转
1. 供应商创建商品 → 草稿状态
2. 提交审核 → 审核中状态
3. 审核通过 → 销售中状态
4. 审核拒绝 → 草稿状态

#### 权限数据流转
1. 用户登录 → 获取角色权限
2. 访问商品 → 验证数据权限
3. 操作商品 → 验证操作权限

### 4.3 数据隔离

#### 平台管理端数据隔离
```java
// 只能查看平台供应商商品
wrapper.in(BizSupplierSpu::getSupplierId, platformSupplierIds);

// 状态可见性过滤
wrapper.in(BizSupplierSpu::getStatus, Arrays.asList(3, 1, 0)); // 审核中、销售中、已下架
```

#### 供应商端数据隔离
```java
// 只能查看自己的商品
wrapper.eq(BizSupplierSpu::getSupplierId, currentSupplierId);
```

## 5. 安全架构

### 5.1 认证授权

#### 认证机制
- JWT Token认证
- Session管理
- 多端登录控制

#### 授权机制
```java
// 基于角色的权限控制
@PreAuthorize("hasRole('PLATFORM_ADMIN')")
@PreAuthorize("hasRole('SUPPLIER')")

// 基于数据的权限控制
if (supplier.getSupplierType() != 2) {
    throw new BizException("无权访问");
}
```

### 5.2 数据安全

#### 数据加密
- 敏感数据加密存储
- 传输层TLS加密

#### 数据脱敏
- 日志数据脱敏
- 接口返回数据脱敏

### 5.3 接口安全

#### 参数校验
```java
@Valid
@NotNull
@Size(min = 1, max = 100)
```

#### 防护措施
- SQL注入防护
- XSS攻击防护
- CSRF防护

## 6. 性能架构

### 6.1 缓存策略

#### Redis缓存
```java
// 商品详情缓存
@Cacheable(value = "product", key = "#id")
public ProductResponse getProductById(Long id);

// 供应商列表缓存
@Cacheable(value = "suppliers", key = "#companyId")
public List<Supplier> getSuppliersByCompany(Long companyId);
```

#### 本地缓存
- 字典数据缓存
- 配置信息缓存

### 6.2 负载均衡

#### 负载均衡
- Nginx反向代理
- 应用集群部署

### 6.3 性能优化

#### 数据库优化
- 索引优化
- 查询优化
- 连接池配置

#### 应用优化
- 分页查询
- 批量操作
- 异步处理

## 7. 部署架构

### 7.1 环境规划

#### 环境分类
- 开发环境 (dev)
- 测试环境 (test)
- 预生产环境 (pre)
- 生产环境 (prod)

#### 配置管理
```yaml
# application-prod.yml
spring:
  datasource:
    url: *********************************
  redis:
    host: prod-redis
```

### 7.2 容器化部署

#### Docker配置
```dockerfile
FROM openjdk:11-jre-slim
COPY target/platform-api.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

#### Docker Compose
```yaml
version: '3.8'
services:
  platform-api:
    image: linkbuy/platform-api:latest
    ports:
      - "8080:8080"
    depends_on:
      - mysql
      - redis
  
  mall-admin-api:
    image: linkbuy/mall-admin-api:latest
    ports:
      - "8081:8080"
    depends_on:
      - mysql
      - redis
  
  supplier-api:
    image: linkbuy/supplier-api:latest
    ports:
      - "8082:8080"
    depends_on:
      - mysql
      - redis
```

### 7.3 CI/CD流程

#### 构建流程
1. 代码提交 → Git仓库
2. 触发构建 → Jenkins/GitLab CI
3. 单元测试 → 测试报告
4. 构建镜像 → Docker Registry
5. 部署应用 → 目标环境

## 8. 监控运维

### 8.1 监控体系

#### 应用监控
- Spring Boot Actuator
- 应用健康检查
- 性能指标收集

#### 业务监控
- 商品创建成功率
- 审核处理时长
- 接口响应时间

### 8.2 日志管理

#### 日志分类
```java
// 业务日志
log.info("商品审核通过: spuId={}, reviewerId={}", spuId, reviewerId);

// 操作日志
log.info("用户操作: userId={}, action={}, resource={}", userId, action, resource);

// 错误日志
log.error("商品创建失败: {}", e.getMessage(), e);
```

#### 日志收集
- 文件日志输出
- 日志轮转和归档
- 错误日志告警

### 8.3 故障处理

#### 故障预案
- 数据库故障处理
- 缓存故障处理
- 服务故障处理

#### 恢复策略
- 自动重试机制
- 异常处理和回滚
- 数据备份和恢复 