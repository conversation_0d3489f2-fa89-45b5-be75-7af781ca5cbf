# 商品管理模块业务流程设计

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2024-01-XX
- **最后更新**: 2024-01-XX
- **业务分析师**: LinkBuy技术团队
- **流程设计师**: LinkBuy技术团队

## 1. 业务流程概述

### 1.1 核心业务流程
- **商品创建流程**: 供应商创建商品到上架销售的完整流程
- **商品审核流程**: 平台和客户对商品的审核管理流程
- **商品状态管理流程**: 商品在不同状态间的流转规则
- **权限控制流程**: 不同角色的操作权限和数据访问控制

### 1.2 参与角色
- **供应商**: 创建和管理商品，提交审核申请
- **平台管理员**: 管理平台供应商商品，审核平台供应商商品
- **客户管理员**: 管理客户供应商商品，审核客户供应商商品

### 1.3 业务规则
- **数据隔离**: 平台供应商和客户供应商数据严格隔离
- **审核权限**: 平台审核平台供应商商品，客户审核客户供应商商品
- **状态可见性**: 不同角色看到的商品状态范围不同

## 2. 商品创建流程

### 2.1 流程图

```mermaid
flowchart TD
    A[供应商登录] --> B[进入商品管理]
    B --> C[点击新增商品]
    C --> D[填写商品基本信息]
    D --> E[上传商品图片]
    E --> F[设置SKU信息]
    F --> G[编辑商品详情]
    G --> H{选择保存方式}
    H -->|保存草稿| I[保存为草稿状态]
    H -->|保存并提交审核| J[提交审核]
    I --> K[商品状态: 仓库中_草稿]
    J --> L[商品状态: 审核中]
    K --> M[可继续编辑]
    L --> N[等待审核结果]
    M --> O[后续可提交审核]
    N --> P{审核结果}
    P -->|审核通过| Q[商品状态: 销售中]
    P -->|审核拒绝| R[商品状态: 仓库中_拒绝]
    Q --> S[商品可正常销售]
    R --> T[供应商可查看拒绝原因]
    T --> U[修改后重新提交]
```

### 2.2 详细步骤

#### 2.2.1 商品信息录入
1. **基本信息填写**
   - 商品名称（必填，1-200字符）
   - 商品分类（必填，选择分类树）
   - 商品品牌（可选）
   - 规格类型（单规格/多规格）

2. **图片上传**
   - 主图上传（必填，1张）
   - 轮播图上传（可选，最多5张）
   - 支持格式：JPG、PNG、GIF
   - 图片大小限制：单张不超过2MB

3. **SKU信息设置**
   - 单规格：直接设置价格和库存
   - 多规格：先设置规格项，再设置各SKU的价格库存
   - 必填字段：采购价、库存数量
   - 可选字段：成本价、划线价、预警库存

4. **商品详情编辑**
   - 使用富文本编辑器
   - 支持图片、文字、表格等内容
   - 可插入商品参数表

#### 2.2.2 保存和提交
1. **保存草稿**
   - 商品状态：status=2, review_status=0
   - 可多次编辑修改
   - 不进入审核流程

2. **保存并提交审核**
   - 先进行表单验证
   - 验证通过后保存商品信息
   - 商品状态：status=3, review_status=1
   - 记录审核日志
   - 发送审核通知

### 2.3 业务规则

#### 2.3.1 数据验证规则
- 商品名称不能重复（同一供应商下）
- SKU编码自动生成，确保唯一性
- 价格必须大于0
- 库存必须大于等于0
- 图片URL必须有效

#### 2.3.2 权限控制
- 供应商只能创建属于自己的商品
- 商品创建后自动关联当前供应商
- 不能修改供应商归属关系

## 3. 商品审核流程

### 3.1 审核流程图

```mermaid
flowchart TD
    A[商品提交审核] --> B[审核状态: 审核中]
    B --> C{审核人类型}
    C -->|平台管理员| D[审核平台供应商商品]
    C -->|客户管理员| E[审核客户供应商商品]
    D --> F[平台管理员审核]
    E --> G[客户管理员审核]
    F --> H{审核结果}
    G --> I{审核结果}
    H -->|审核通过| J[商品状态: 销售中]
    H -->|审核拒绝| K[商品状态: 仓库中_拒绝]
    I -->|审核通过| L[商品状态: 销售中]
    I -->|审核拒绝| M[商品状态: 仓库中_拒绝]
    J --> N[商品可正常销售]
    L --> O[商品可正常销售]
    K --> P[供应商收到拒绝通知]
    M --> Q[供应商收到拒绝通知]
    P --> R[供应商修改后重新提交]
    Q --> S[供应商修改后重新提交]
```

### 3.2 审核权限矩阵

| 供应商类型 | 审核人 | 权限说明 |
|------------|--------|----------|
| 平台供应商 (type=2) | 平台管理员 | 只能审核平台供应商的商品 |
| 客户供应商 (type=1) | 客户管理员 | 只能审核自己公司的供应商商品 |

### 3.3 审核操作流程

#### 3.3.1 审核前置条件
1. **权限验证**
   ```java
   // 平台管理员审核平台供应商商品
   if (supplier.getSupplierType() != 2) {
       throw new BizException("平台只能审核平台供应商商品");
   }
   
   // 客户管理员审核客户供应商商品
   if (supplier.getSupplierType() != 1 || 
       !supplier.getCompanyId().equals(currentUser.getCompanyId())) {
       throw new BizException("只能审核自己公司的供应商商品");
   }
   ```

2. **状态验证**
   - 商品必须处于审核中状态 (status=3, review_status=1)
   - 商品信息必须完整

#### 3.3.2 审核操作
1. **审核通过**
   - 更新商品状态：status=1, review_status=3
   - 记录审核人和审核时间
   - 记录审核日志
   - 发送通过通知

2. **审核拒绝**
   - 更新商品状态：status=2, review_status=2
   - 记录拒绝原因
   - 记录审核人和审核时间
   - 记录审核日志
   - 发送拒绝通知

#### 3.3.3 批量审核
1. **批量审核通过**
   - 验证所有商品的审核权限
   - 批量更新商品状态
   - 批量记录审核日志
   - 发送批量通知

2. **批量审核拒绝**
   - 统一拒绝原因
   - 批量更新状态和原因
   - 批量记录日志

## 4. 商品状态管理流程

### 4.1 状态流转图

```mermaid
stateDiagram-v2
    [*] --> 草稿: 创建商品
    草稿 --> 审核中: 提交审核
    草稿 --> 草稿: 编辑保存
    审核中 --> 销售中: 审核通过
    审核中 --> 审核拒绝: 审核拒绝
    审核中 --> 草稿: 撤回审核
    审核拒绝 --> 审核中: 重新提交审核
    销售中 --> 已下架: 下架操作
    已下架 --> 销售中: 重新上架
    销售中 --> 销售中: 编辑商品(不影响状态)
    
    note right of 草稿
        status: 2<br/>草稿状态<br/>可编辑全部信息
    end note
    
    note right of 审核拒绝
        status: 4<br/>审核拒绝<br/>显示拒绝原因
    end note
    
    note right of 审核中
        status: 3<br/>等待审核<br/>限制编辑
    end note
    
    note right of 销售中
        status: 1<br/>正常销售<br/>限制编辑
    end note
    
    note right of 已下架
        status: 0<br/>暂停销售<br/>限制编辑
    end note
```

### 4.2 状态定义

| 状态值 | 状态名称 | 说明 |
|--------|----------|------|
| 2 | 草稿 | 商品创建但未提交审核 |
| 3 | 审核中 | 商品已提交等待审核 |
| 4 | 审核拒绝 | 商品审核被拒绝 |
| 1 | 销售中 | 商品审核通过正在销售 |
| 0 | 已下架 | 商品已下架停止销售 |

### 4.3 状态操作权限

#### 4.3.1 供应商操作权限
| 当前状态 | 状态值 | 允许操作 | 操作结果 |
|----------|--------|----------|----------|
| 草稿 | 2 | 编辑、删除、提交审核 | 保持草稿(2)或变为审核中(3) |
| 审核中 | 3 | 查看、撤回审核 | 变为草稿(2) |
| 审核拒绝 | 4 | 编辑、删除、重新提交 | 保持拒绝(4)或变为审核中(3) |
| 销售中 | 1 | 查看、编辑、下架 | 保持销售中(1)或变为已下架(0) |
| 已下架 | 0 | 查看、编辑、上架 | 保持已下架(0)或变为销售中(1) |

#### 4.3.2 平台/客户管理员操作权限
| 当前状态 | 状态值 | 允许操作 | 操作结果 |
|----------|--------|----------|----------|
| 审核中 | 3 | 审核通过、审核拒绝 | 变为销售中(1)或审核拒绝(4) |
| 销售中 | 1 | 强制下架 | 变为已下架(0) |
| 已下架 | 0 | 强制上架 | 变为销售中(1) |

### 4.4 状态可见性规则

#### 4.4.1 供应商端可见性
- **可见状态**: 所有状态（草稿(2)、审核中(3)、审核拒绝(4)、销售中(1)、已下架(0)）
- **业务逻辑**: 供应商需要看到商品的完整生命周期

#### 4.4.2 平台/客户管理端可见性
- **可见状态**: 审核中(3)、销售中(1)、已下架(0)
- **不可见状态**: 草稿(2)、审核拒绝(4)
- **业务逻辑**: 管理端只关注需要处理的商品和已上线的商品

```java
// 平台管理端状态过滤
wrapper.in(BizSupplierSpu::getStatus, Arrays.asList(3, 1, 0)); // 审核中、销售中、已下架
```

## 5. 权限控制流程

### 5.1 权限验证流程图

```mermaid
flowchart TD
    A[用户请求] --> B[身份认证]
    B --> C{认证成功?}
    C -->|否| D[返回401未授权]
    C -->|是| E[获取用户角色]
    E --> F{角色类型}
    F -->|供应商| G[供应商权限验证]
    F -->|平台管理员| H[平台管理员权限验证]
    F -->|客户管理员| I[客户管理员权限验证]
    G --> J[验证商品归属]
    H --> K[验证供应商类型=2]
    I --> L[验证供应商类型=1且公司匹配]
    J --> M{归属验证}
    K --> N{类型验证}
    L --> O{类型和公司验证}
    M -->|通过| P[允许操作]
    M -->|失败| Q[返回403权限不足]
    N -->|通过| P
    N -->|失败| Q
    O -->|通过| P
    O -->|失败| Q
```

### 5.2 数据隔离规则

#### 5.2.1 供应商数据隔离
```java
// 供应商只能访问自己的商品
@PreAuthorize("@permissionService.checkSupplierProduct(#productId)")
public ProductResponse getProduct(@PathVariable Long productId) {
    // 业务逻辑
}

// 权限服务实现
public boolean checkSupplierProduct(Long productId) {
    Product product = productService.getById(productId);
    return product.getSupplierId().equals(getCurrentUser().getSupplierId());
}
```

#### 5.2.2 平台管理员数据隔离
```java
// 平台管理员只能访问平台供应商商品
public List<Product> getPlatformProducts(ProductQuery query) {
    return productService.list(
        Wrappers.<Product>lambdaQuery()
            .eq(Product::getSupplierType, 2) // 只查询平台供应商
            .eq(Product::getStatus, query.getStatus())
    );
}
```

#### 5.2.3 客户管理员数据隔离
```java
// 客户管理员只能访问自己公司的供应商商品
public List<Product> getCustomerProducts(ProductQuery query) {
    return productService.list(
        Wrappers.<Product>lambdaQuery()
            .eq(Product::getSupplierType, 1) // 只查询客户供应商
            .eq(Product::getCompanyId, getCurrentUser().getCompanyId()) // 同公司
            .eq(Product::getStatus, query.getStatus())
    );
}
```

## 6. 异常处理流程

### 6.1 业务异常处理

#### 6.1.1 权限异常
```java
// 权限不足异常
if (!hasPermission) {
    throw new BizException("无权访问该商品", 403);
}

// 数据不存在异常
if (product == null) {
    throw new BizException("商品不存在", 404);
}
```

#### 6.1.2 状态异常
```java
// 状态不允许操作异常
if (!canOperate(product.getStatus())) {
    throw new BizException("当前状态不允许该操作", 400);
}

// 审核状态异常
if (product.getStatus() != 3) {
    throw new BizException("商品不在审核状态", 400);
}
```

### 6.2 系统异常处理

#### 6.2.1 数据库异常
- 连接超时处理
- 事务回滚机制
- 数据一致性保证

#### 6.2.2 网络异常
- 请求重试机制
- 超时处理
- 降级处理

## 7. 通知流程

### 7.1 审核通知流程

```mermaid
sequenceDiagram
    participant S as 供应商
    participant A as 审核员
    participant N as 通知服务
    participant M as 消息队列
    
    S->>A: 提交商品审核
    A->>A: 执行审核操作
    A->>N: 发送审核结果
    N->>M: 推送通知消息
    M->>S: 发送审核通知
    S->>S: 接收审核结果
```

### 7.2 通知类型

#### 7.2.1 审核通知
- **提交审核通知**: 通知审核员有新的审核任务
- **审核通过通知**: 通知供应商商品审核通过
- **审核拒绝通知**: 通知供应商商品审核被拒绝，包含拒绝原因

#### 7.2.2 状态变更通知
- **上架通知**: 商品成功上架销售
- **下架通知**: 商品被下架停止销售
- **库存预警通知**: 商品库存低于预警值

## 8. 数据同步流程

### 8.1 商品数据同步

#### 8.1.1 创建同步
1. 商品基本信息同步
2. SKU信息同步
3. 图片信息同步
4. 分类品牌关联同步

#### 8.1.2 更新同步
1. 增量数据同步
2. 状态变更同步
3. 价格库存同步
4. 审核信息同步

### 8.2 缓存更新流程

#### 8.2.1 缓存策略
- 商品基本信息缓存（30分钟）
- 商品列表缓存（10分钟）
- 分类品牌缓存（1小时）

#### 8.2.2 缓存更新
- 商品变更时清除相关缓存
- 定时刷新热点数据缓存
- 缓存穿透保护

## 9. 监控告警流程

### 9.1 业务监控

#### 9.1.1 关键指标
- 商品创建成功率
- 审核处理时长
- 状态变更频率
- 接口响应时间

#### 9.1.2 告警规则
- 审核积压超过100个商品
- 商品创建失败率超过5%
- 接口响应时间超过2秒
- 数据库连接异常

### 9.2 告警处理流程

```mermaid
flowchart TD
    A[监控系统检测异常] --> B[触发告警规则]
    B --> C[发送告警通知]
    C --> D[运维人员接收]
    D --> E[问题排查]
    E --> F{问题类型}
    F -->|业务问题| G[联系业务人员]
    F -->|技术问题| H[技术人员处理]
    G --> I[业务处理]
    H --> J[技术修复]
    I --> K[问题解决]
    J --> K
    K --> L[告警关闭]
```

## 10. 性能优化流程

### 10.1 查询优化

#### 10.1.1 索引优化
- 商品列表查询索引
- 供应商商品关联索引
- 状态筛选索引
- 时间范围查询索引

#### 10.1.2 分页优化
- 深分页优化
- 游标分页
- 缓存分页结果

### 10.2 并发优化

#### 10.2.1 锁机制
- 商品编辑时的乐观锁
- 库存扣减的悲观锁
- 状态变更的分布式锁

#### 10.2.2 异步处理
- 图片上传异步处理
- 审核通知异步发送
- 数据同步异步执行 