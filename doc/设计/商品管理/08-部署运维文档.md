# 商品管理模块部署运维文档

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2024-01-XX
- **最后更新**: 2024-01-XX
- **运维负责人**: LinkBuy技术团队
- **系统管理员**: LinkBuy技术团队

## 1. 部署环境

### 1.1 环境要求

#### 1.1.1 硬件要求
- **CPU**: 4核心以上
- **内存**: 8GB以上
- **存储**: 100GB以上SSD
- **网络**: 千兆网卡

#### 1.1.2 软件要求
- **操作系统**: CentOS 7.x / Ubuntu 18.04+
- **Java**: OpenJDK 11+
- **数据库**: MySQL 8.0+
- **缓存**: Redis 6.0+
- **Web服务器**: Nginx 1.18+

### 1.2 环境配置

#### 1.2.1 数据库配置
```sql
-- 创建数据库
CREATE DATABASE linkbuy DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'linkbuy'@'%' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON linkbuy.* TO 'linkbuy'@'%';
FLUSH PRIVILEGES;
```

#### 1.2.2 Redis配置
```conf
# redis.conf
bind 0.0.0.0
port 6379
requirepass your_redis_password
maxmemory 2gb
maxmemory-policy allkeys-lru
```

## 2. 应用部署

### 2.1 后端部署

#### 2.1.1 构建应用
```bash
# 构建后端应用
cd LinkBuy
mvn clean package -DskipTests

# 构建Docker镜像
docker build -t linkbuy-api:latest .
```

#### 2.1.2 配置文件
```yaml
# application-prod.yml
spring:
  datasource:
    url: ***************************************************************************************************
    username: linkbuy
    password: ${DB_PASSWORD}
  redis:
    host: redis
    port: 6379
    password: ${REDIS_PASSWORD}
```

### 2.2 前端部署

#### 2.2.1 构建前端应用
```bash
# 构建平台管理端
cd LinkBuy-FED/linkBuyAdmin
npm run build:prod

# 构建供应商端
cd LinkBuy-FED/linkBuySupplier
npm run build:prod
```

#### 2.2.2 Nginx配置
```nginx
server {
    listen 80;
    server_name admin.linkbuy.com;
    
    location / {
        root /var/www/linkbuy-admin;
        try_files $uri $uri/ /index.html;
    }
    
    location /api/ {
        proxy_pass http://linkbuy-api:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 3. Docker部署

### 3.1 Docker Compose配置
```yaml
version: '3.8'
services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: linkbuy
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"

  redis:
    image: redis:6.0
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"

  linkbuy-api:
    image: linkbuy-api:latest
    environment:
      DB_PASSWORD: ${DB_PASSWORD}
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    depends_on:
      - mysql
      - redis
    ports:
      - "8080:8080"

  nginx:
    image: nginx:alpine
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./dist:/var/www/linkbuy-admin
    ports:
      - "80:80"
    depends_on:
      - linkbuy-api

volumes:
  mysql_data:
  redis_data:
```

## 4. 监控告警

### 4.1 应用监控
- **健康检查**: Spring Boot Actuator
- **性能监控**: Micrometer + Prometheus
- **日志监控**: ELK Stack
- **错误追踪**: Sentry

### 4.2 基础设施监控
- **服务器监控**: Node Exporter
- **数据库监控**: MySQL Exporter
- **缓存监控**: Redis Exporter

## 5. 备份策略

### 5.1 数据库备份
```bash
# 每日全量备份
mysqldump -u root -p linkbuy > backup_$(date +%Y%m%d).sql

# 增量备份
mysqlbinlog --start-datetime="2024-01-01 00:00:00" mysql-bin.000001 > incremental_backup.sql
```

### 5.2 文件备份
```bash
# 应用文件备份
tar -czf app_backup_$(date +%Y%m%d).tar.gz /opt/linkbuy/

# 配置文件备份
cp -r /etc/nginx/ /backup/nginx_$(date +%Y%m%d)/
```

## 6. 故障处理

### 6.1 常见问题
1. **数据库连接超时**: 检查数据库连接池配置
2. **内存溢出**: 调整JVM堆内存大小
3. **接口响应慢**: 检查数据库索引和查询优化
4. **文件上传失败**: 检查磁盘空间和权限

### 6.2 应急预案
- **服务降级**: 关闭非核心功能
- **数据库切换**: 主从切换
- **缓存清理**: 清理Redis缓存
- **重启服务**: 滚动重启应用实例

## 7. 性能优化

### 7.1 数据库优化
- 索引优化
- 查询优化
- 连接池调优
- 读写分离

### 7.2 应用优化
- JVM参数调优
- 缓存策略优化
- 异步处理
- 连接池优化

## 8. 安全配置

### 8.1 网络安全
- 防火墙配置
- SSL证书配置
- API限流
- 访问控制

### 8.2 数据安全
- 数据加密
- 敏感信息脱敏
- 访问日志记录
- 权限控制 