# 商品管理模块数据库设计文档

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2024-01-XX
- **最后更新**: 2024-01-XX
- **数据库架构师**: LinkBuy技术团队
- **DBA**: LinkBuy技术团队

## 1. 数据库概述

### 1.1 设计原则
- **数据一致性**: 确保数据的完整性和一致性
- **性能优化**: 合理的索引设计和查询优化
- **扩展性**: 支持业务扩展和数据增长
- **安全性**: 数据隔离和权限控制
- **可维护性**: 清晰的表结构和命名规范

### 1.2 数据库选型
- **主数据库**: MySQL 8.0
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci
- **存储引擎**: InnoDB

### 1.3 命名规范
- **表名**: 小写字母，下划线分隔，前缀标识业务模块
- **字段名**: 小写字母，下划线分隔，语义明确
- **索引名**: idx_表名_字段名 或 uk_表名_字段名
- **外键名**: fk_表名_字段名

## 2. 核心表结构

### 2.1 供应商表 (biz_supplier)

```sql
CREATE TABLE `biz_supplier` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `supplier_code` varchar(50) NOT NULL COMMENT '供应商编码',
  `name` varchar(100) NOT NULL COMMENT '供应商名称',
  `supplier_type` int NOT NULL DEFAULT '1' COMMENT '供应商类型：1-客户供应商，2-平台供应商',
  `company_id` bigint DEFAULT NULL COMMENT '所属公司ID（客户供应商时有值）',
  `contact_person` varchar(50) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
  `address` varchar(200) DEFAULT NULL COMMENT '地址',
  `status` int NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_supplier_code` (`supplier_code`),
  KEY `idx_supplier_type` (`supplier_type`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='供应商表';
```

**字段说明**:
- `supplier_type`: 区分平台供应商和客户供应商，是数据隔离的关键字段
- `company_id`: 客户供应商关联的公司ID，平台供应商为NULL
- `status`: 供应商启用状态，影响商品可见性

### 2.2 商品主表 (biz_supplier_spu)

```sql
CREATE TABLE `biz_supplier_spu` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `supplier_id` bigint NOT NULL COMMENT '供应商ID',
  `spu_code` varchar(50) NOT NULL COMMENT '商品编码',
  `name` varchar(200) NOT NULL COMMENT '商品名称',
  `category_id` bigint DEFAULT NULL COMMENT '分类ID',
  `brand_id` bigint DEFAULT NULL COMMENT '品牌ID',
  `spec_type` int NOT NULL DEFAULT '1' COMMENT '规格类型：1-单规格，2-多规格',
  `main_image_url` varchar(500) DEFAULT NULL COMMENT '主图URL',
  `image_urls` json DEFAULT NULL COMMENT '商品图片URLs',
  `detail_content` longtext COMMENT '商品详情',
  `weight` decimal(10,3) DEFAULT NULL COMMENT '重量(kg)',
  `volume` decimal(10,3) DEFAULT NULL COMMENT '体积(m³)',
  `status` int NOT NULL DEFAULT '2' COMMENT '状态：0-已下架，1-销售中，2-草稿，3-审核中，4-审核拒绝',
  `review_reason` varchar(500) DEFAULT NULL COMMENT '审核拒绝原因',
  `reviewer_id` bigint DEFAULT NULL COMMENT '审核人ID',
  `review_time` datetime DEFAULT NULL COMMENT '审核时间',
  `online_time` datetime DEFAULT NULL COMMENT '上架时间',
  `offline_time` datetime DEFAULT NULL COMMENT '下架时间',
  `sort_order` int DEFAULT '0' COMMENT '排序值',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_spu_code` (`spu_code`),
  KEY `idx_supplier_id` (`supplier_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_brand_id` (`brand_id`),
  KEY `idx_status_review` (`status`, `review_status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_online_time` (`online_time`),
  KEY `idx_sort_order` (`sort_order`),
  CONSTRAINT `fk_spu_supplier` FOREIGN KEY (`supplier_id`) REFERENCES `biz_supplier` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品主表(SPU)';
```

**关键字段说明**:
- `status`: 商品状态，包含完整的状态流转
- `spec_type`: 区分单规格和多规格商品
- `image_urls`: JSON格式存储多张图片URL
- `review_*`: 审核相关字段，支持审核流程

### 2.3 商品SKU表 (biz_supplier_sku)

```sql
CREATE TABLE `biz_supplier_sku` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `spu_id` bigint NOT NULL COMMENT '商品SPU ID',
  `sku_code` varchar(50) NOT NULL COMMENT 'SKU编码',
  `sku_name` varchar(200) DEFAULT NULL COMMENT 'SKU名称',
  `spec_info` json DEFAULT NULL COMMENT '规格信息JSON',
  `image_url` varchar(500) DEFAULT NULL COMMENT 'SKU图片URL',
  `cost_price` decimal(10,2) DEFAULT NULL COMMENT '成本价',
  `purchase_price` decimal(10,2) NOT NULL COMMENT '采购价',
  `strike_price` decimal(10,2) DEFAULT NULL COMMENT '划线价',
  `stock` int NOT NULL DEFAULT '0' COMMENT '库存数量',
  `warning_stock` int DEFAULT '0' COMMENT '预警库存',
  `weight` decimal(10,3) DEFAULT NULL COMMENT '重量(kg)',
  `volume` decimal(10,3) DEFAULT NULL COMMENT '体积(m³)',
  `barcode` varchar(50) DEFAULT NULL COMMENT '条形码',
  `status` int NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `sort_order` int DEFAULT '0' COMMENT '排序值',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_sku_code` (`sku_code`),
  KEY `idx_spu_id` (`spu_id`),
  KEY `idx_status` (`status`),
  KEY `idx_stock` (`stock`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_sku_spu` FOREIGN KEY (`spu_id`) REFERENCES `biz_supplier_spu` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品SKU表';
```

**关键字段说明**:
- `spec_info`: JSON格式存储规格信息，如 `{"颜色": "红色", "尺寸": "L"}`
- `cost_price`: 成本价，供内部核算使用
- `purchase_price`: 采购价，对外销售价格
- `strike_price`: 划线价，营销展示用

### 2.4 商品分类表 (biz_product_category)

```sql
CREATE TABLE `biz_product_category` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `parent_id` bigint NOT NULL DEFAULT '0' COMMENT '父分类ID，0表示顶级分类',
  `category_code` varchar(50) NOT NULL COMMENT '分类编码',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `level` int NOT NULL DEFAULT '1' COMMENT '分类层级：1-一级，2-二级，3-三级',
  `path` varchar(500) DEFAULT NULL COMMENT '分类路径，如：1,2,3',
  `image_url` varchar(500) DEFAULT NULL COMMENT '分类图片URL',
  `description` varchar(500) DEFAULT NULL COMMENT '分类描述',
  `status` int NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `sort_order` int DEFAULT '0' COMMENT '排序值',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_category_code` (`category_code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_level` (`level`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品分类表';
```

### 2.5 商品品牌表 (biz_product_brand)

```sql
CREATE TABLE `biz_product_brand` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `brand_code` varchar(50) NOT NULL COMMENT '品牌编码',
  `name` varchar(100) NOT NULL COMMENT '品牌名称',
  `name_en` varchar(100) DEFAULT NULL COMMENT '品牌英文名称',
  `logo_url` varchar(500) DEFAULT NULL COMMENT '品牌Logo URL',
  `description` varchar(500) DEFAULT NULL COMMENT '品牌描述',
  `status` int NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `sort_order` int DEFAULT '0' COMMENT '排序值',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_brand_code` (`brand_code`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品品牌表';
```

## 3. 审核相关表

### 3.1 商品审核记录表 (biz_spu_review_log)

```sql
CREATE TABLE `biz_spu_review_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `spu_id` bigint NOT NULL COMMENT '商品SPU ID',
  `supplier_id` bigint NOT NULL COMMENT '供应商ID',
  `review_type` varchar(20) NOT NULL COMMENT '操作类型：SUBMIT-提交审核，APPROVE-审核通过，REJECT-审核拒绝，WITHDRAW-撤回审核',
  `old_status` int DEFAULT NULL COMMENT '原商品状态',
  `new_status` int DEFAULT NULL COMMENT '新商品状态',
  `reviewer_id` bigint DEFAULT NULL COMMENT '审核人ID',
  `reviewer_type` varchar(20) DEFAULT NULL COMMENT '审核人类型：PLATFORM-平台，CUSTOMER-客户',
  `review_reason` varchar(500) DEFAULT NULL COMMENT '审核意见/拒绝原因',
  `submit_data` json DEFAULT NULL COMMENT '提交时的商品数据快照',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_spu_id` (`spu_id`),
  KEY `idx_supplier_id` (`supplier_id`),
  KEY `idx_reviewer_id` (`reviewer_id`),
  KEY `idx_review_type` (`review_type`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_review_log_spu` FOREIGN KEY (`spu_id`) REFERENCES `biz_supplier_spu` (`id`),
  CONSTRAINT `fk_review_log_supplier` FOREIGN KEY (`supplier_id`) REFERENCES `biz_supplier` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品审核记录表';
```

**关键字段说明**:
- `review_type`: 记录具体的操作类型
- `submit_data`: JSON格式保存提交时的商品数据快照，便于审核对比
- `reviewer_type`: 区分平台审核和客户审核

## 4. 扩展表结构 (预留)

### 4.1 规格调整申请表 (biz_spec_change_request)

```sql
CREATE TABLE `biz_spec_change_request` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `spu_id` bigint NOT NULL COMMENT '商品SPU ID',
  `supplier_id` bigint NOT NULL COMMENT '供应商ID',
  `request_type` varchar(20) NOT NULL COMMENT '变更类型：ADD_SPEC_VALUE-新增规格值，DELETE_SPEC_VALUE-删除规格值，MODIFY_SPEC_NAME-修改规格名称',
  `old_spec_data` json DEFAULT NULL COMMENT '原规格数据',
  `new_spec_data` json DEFAULT NULL COMMENT '新规格数据',
  `reason` varchar(500) DEFAULT NULL COMMENT '申请理由',
  `status` int NOT NULL DEFAULT '0' COMMENT '状态：0-待审核，1-审核通过，2-审核拒绝',
  `reviewer_id` bigint DEFAULT NULL COMMENT '审核人ID',
  `review_time` datetime DEFAULT NULL COMMENT '审核时间',
  `review_comment` varchar(500) DEFAULT NULL COMMENT '审核备注',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_spu_id` (`spu_id`),
  KEY `idx_supplier_id` (`supplier_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_spec_change_spu` FOREIGN KEY (`spu_id`) REFERENCES `biz_supplier_spu` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='规格调整申请表';
```

### 4.2 价格修改申请表 (biz_price_change_request)

```sql
CREATE TABLE `biz_price_change_request` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `spu_id` bigint NOT NULL COMMENT '商品SPU ID',
  `sku_id` bigint DEFAULT NULL COMMENT 'SKU ID',
  `supplier_id` bigint NOT NULL COMMENT '供应商ID',
  `price_type` varchar(20) NOT NULL COMMENT '价格类型：COST_PRICE-成本价，PURCHASE_PRICE-采购价，STRIKE_PRICE-划线价',
  `old_price` decimal(10,2) DEFAULT NULL COMMENT '原价格',
  `new_price` decimal(10,2) NOT NULL COMMENT '新价格',
  `reason` varchar(500) DEFAULT NULL COMMENT '申请理由',
  `status` int NOT NULL DEFAULT '0' COMMENT '状态：0-待审核，1-审核通过，2-审核拒绝',
  `reviewer_id` bigint DEFAULT NULL COMMENT '审核人ID',
  `review_time` datetime DEFAULT NULL COMMENT '审核时间',
  `review_comment` varchar(500) DEFAULT NULL COMMENT '审核备注',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_spu_id` (`spu_id`),
  KEY `idx_sku_id` (`sku_id`),
  KEY `idx_supplier_id` (`supplier_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_price_change_spu` FOREIGN KEY (`spu_id`) REFERENCES `biz_supplier_spu` (`id`),
  CONSTRAINT `fk_price_change_sku` FOREIGN KEY (`sku_id`) REFERENCES `biz_supplier_sku` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='价格修改申请表';
```

## 5. 索引设计

### 5.1 核心查询索引

#### 商品列表查询优化
```sql
-- 供应商商品查询（最常用）
CREATE INDEX idx_supplier_status_review ON biz_supplier_spu(supplier_id, status, review_status);

-- 分类商品查询
CREATE INDEX idx_category_status ON biz_supplier_spu(category_id, status);

-- 时间范围查询
CREATE INDEX idx_create_time_status ON biz_supplier_spu(create_time, status);

-- 审核查询
CREATE INDEX idx_review_status_time ON biz_supplier_spu(review_status, review_time);
```

#### SKU查询优化
```sql
-- SPU关联SKU查询
CREATE INDEX idx_spu_status ON biz_supplier_sku(spu_id, status);

-- 库存预警查询
CREATE INDEX idx_stock_warning ON biz_supplier_sku(stock, warning_stock);
```

### 5.2 统计查询索引

```sql
-- 供应商商品统计
CREATE INDEX idx_supplier_status_count ON biz_supplier_spu(supplier_id, status, review_status);

-- 分类商品统计
CREATE INDEX idx_category_status_count ON biz_supplier_spu(category_id, status);

-- 审核统计
CREATE INDEX idx_review_stats ON biz_spu_review_log(reviewer_type, review_type, create_time);
```

### 5.3 全文搜索索引 (预留)

```sql
-- 商品名称全文搜索
ALTER TABLE biz_supplier_spu ADD FULLTEXT(name);

-- 商品详情全文搜索
ALTER TABLE biz_supplier_spu ADD FULLTEXT(detail_content);
```

## 6. 数据约束

### 6.1 业务约束

#### 商品状态约束
```sql
-- 商品状态检查约束
ALTER TABLE biz_supplier_spu ADD CONSTRAINT chk_status 
CHECK (status IN (0, 1, 2, 3));

-- 审核状态检查约束
ALTER TABLE biz_supplier_spu ADD CONSTRAINT chk_review_status 
CHECK (review_status IN (0, 1, 2, 3));

-- 规格类型检查约束
ALTER TABLE biz_supplier_spu ADD CONSTRAINT chk_spec_type 
CHECK (spec_type IN (1, 2));
```

#### 供应商类型约束
```sql
-- 供应商类型检查约束
ALTER TABLE biz_supplier ADD CONSTRAINT chk_supplier_type 
CHECK (supplier_type IN (1, 2));
```

#### 价格约束
```sql
-- 价格非负约束
ALTER TABLE biz_supplier_sku ADD CONSTRAINT chk_cost_price 
CHECK (cost_price >= 0);

ALTER TABLE biz_supplier_sku ADD CONSTRAINT chk_purchase_price 
CHECK (purchase_price >= 0);

ALTER TABLE biz_supplier_sku ADD CONSTRAINT chk_strike_price 
CHECK (strike_price >= 0);
```

### 6.2 数据完整性约束

#### 外键约束
```sql
-- 商品必须属于有效供应商
ALTER TABLE biz_supplier_spu ADD CONSTRAINT fk_spu_supplier 
FOREIGN KEY (supplier_id) REFERENCES biz_supplier(id);

-- SKU必须属于有效商品
ALTER TABLE biz_supplier_sku ADD CONSTRAINT fk_sku_spu 
FOREIGN KEY (spu_id) REFERENCES biz_supplier_spu(id);

-- 审核记录必须关联有效商品
ALTER TABLE biz_spu_review_log ADD CONSTRAINT fk_review_spu 
FOREIGN KEY (spu_id) REFERENCES biz_supplier_spu(id);
```

## 7. 数据初始化

### 7.1 基础数据

#### 商品分类初始化
```sql
INSERT INTO biz_product_category (category_code, name, level, parent_id, sort_order) VALUES
('ELECTRONICS', '电子产品', 1, 0, 1),
('CLOTHING', '服装服饰', 1, 0, 2),
('HOME', '家居用品', 1, 0, 3),
('FOOD', '食品饮料', 1, 0, 4);
```

#### 商品品牌初始化
```sql
INSERT INTO biz_product_brand (brand_code, name, name_en, sort_order) VALUES
('APPLE', '苹果', 'Apple', 1),
('SAMSUNG', '三星', 'Samsung', 2),
('HUAWEI', '华为', 'Huawei', 3);
```

### 7.2 测试数据

#### 供应商测试数据
```sql
INSERT INTO biz_supplier (supplier_code, name, supplier_type, company_id, status) VALUES
('SUP001', '平台自营供应商', 2, NULL, 1),
('SUP002', '客户供应商A', 1, 1001, 1),
('SUP003', '客户供应商B', 1, 1002, 1);
```

## 8. 数据库维护

### 8.1 定期维护任务

#### 数据清理
```sql
-- 清理软删除的数据（保留3个月）
DELETE FROM biz_supplier_spu 
WHERE is_deleted = 1 AND update_time < DATE_SUB(NOW(), INTERVAL 3 MONTH);

-- 清理过期的审核记录（保留1年）
DELETE FROM biz_spu_review_log 
WHERE create_time < DATE_SUB(NOW(), INTERVAL 1 YEAR);
```

#### 统计信息更新
```sql
-- 更新表统计信息
ANALYZE TABLE biz_supplier_spu;
ANALYZE TABLE biz_supplier_sku;
ANALYZE TABLE biz_spu_review_log;
```

### 8.2 性能监控

#### 慢查询监控
```sql
-- 开启慢查询日志
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;
```

#### 索引使用情况监控
```sql
-- 查看索引使用情况
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    CARDINALITY,
    SUB_PART,
    PACKED,
    NULLABLE,
    INDEX_TYPE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = 'linkbuy' 
AND TABLE_NAME LIKE 'biz_%';
```

### 8.3 备份策略

#### 全量备份
```bash
# 每日全量备份
mysqldump -u root -p --single-transaction --routines --triggers linkbuy > linkbuy_full_$(date +%Y%m%d).sql
```

#### 增量备份
```bash
# 开启binlog
SET GLOBAL log_bin = ON;
SET GLOBAL binlog_format = ROW;
```

## 9. 数据迁移

### 9.1 版本升级脚本

#### V1.1 添加审核状态字段
```sql
-- 添加审核状态相关字段
ALTER TABLE biz_supplier_spu 
-- 状态字段已包含审核状态，无需额外字段
ADD COLUMN review_reason VARCHAR(500) DEFAULT NULL COMMENT '审核拒绝原因' AFTER review_status,
ADD COLUMN reviewer_id BIGINT DEFAULT NULL COMMENT '审核人ID' AFTER review_reason,
ADD COLUMN review_time DATETIME DEFAULT NULL COMMENT '审核时间' AFTER reviewer_id;

-- 更新现有数据的审核状态
UPDATE biz_supplier_spu SET review_status = 3 WHERE status IN (0, 1);
UPDATE biz_supplier_spu SET review_status = 0 WHERE status = 2;
UPDATE biz_supplier_spu SET review_status = 1 WHERE status = 3;

-- 添加索引
CREATE INDEX idx_status_review ON biz_supplier_spu(status, review_status);
```

### 9.2 数据修复脚本

#### 修复商品状态不一致
```sql
-- 修复状态不一致的商品
UPDATE biz_supplier_spu 
SET review_status = 3 
WHERE status IN (0, 1) AND review_status != 3;

UPDATE biz_supplier_spu 
SET review_status = 0 
WHERE status = 2 AND review_status NOT IN (0, 2);
```

## 10. 安全设计

### 10.1 数据权限

#### 行级安全策略 (预留)
```sql
-- 创建安全策略（MySQL 8.0+）
-- 供应商只能访问自己的商品
CREATE POLICY supplier_policy ON biz_supplier_spu
FOR ALL TO 'supplier_role'
USING (supplier_id = @current_supplier_id);
```

### 10.2 敏感数据保护

#### 数据脱敏
```sql
-- 创建脱敏视图
CREATE VIEW v_supplier_spu_masked AS
SELECT 
    id,
    supplier_id,
    spu_code,
    name,
    category_id,
    status,
    review_status,
    CASE 
        WHEN @user_role = 'ADMIN' THEN cost_price 
        ELSE NULL 
    END AS cost_price,
    create_time,
    update_time
FROM biz_supplier_spu;
```

### 10.3 审计日志

#### 数据变更审计
```sql
-- 创建审计表
CREATE TABLE audit_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    table_name VARCHAR(50) NOT NULL,
    operation VARCHAR(10) NOT NULL,
    old_values JSON,
    new_values JSON,
    user_id BIGINT,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建触发器记录变更
DELIMITER $$
CREATE TRIGGER tr_spu_audit_update
AFTER UPDATE ON biz_supplier_spu
FOR EACH ROW
BEGIN
    INSERT INTO audit_log (table_name, operation, old_values, new_values, user_id)
    VALUES ('biz_supplier_spu', 'UPDATE', 
            JSON_OBJECT('id', OLD.id, 'name', OLD.name, 'status', OLD.status),
            JSON_OBJECT('id', NEW.id, 'name', NEW.name, 'status', NEW.status),
            @current_user_id);
END$$
DELIMITER ;
``` 