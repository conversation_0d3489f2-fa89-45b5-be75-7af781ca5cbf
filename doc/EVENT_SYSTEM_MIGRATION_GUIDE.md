# 事件系统迁移指南

## 概述

旧的事件系统已被删除，需要迁移到新的V2版本。本指南将帮助你完成迁移。

## 受影响的文件

### 编译错误的文件
以下文件因为引用了已删除的旧事件系统而无法编译：

#### API服务中的Controller
- `LinkBuy/supplier-api/src/main/java/com/linkBuy/supplierApi/controller/UserController.java`
- `LinkBuy/supplier-api/src/main/java/com/linkBuy/supplierApi/controller/SupplierController.java`
- `LinkBuy/mall-admin-api/src/main/java/com/linkBuy/mallAdminApi/controller/UserController.java`
- `LinkBuy/mall-admin-api/src/main/java/com/linkBuy/mallAdminApi/controller/SupplierController.java`
- `LinkBuy/platform-api/src/main/java/com/linkBuy/platformApi/controller/biz/SupplierController.java`
- `LinkBuy/platform-api/src/main/java/com/linkBuy/platformApi/controller/biz/CompanyController.java`

#### 业务服务
- `LinkBuy/supplier-api/src/main/java/com/linkBuy/supplierApi/service/SupplierUserBusinessService.java`

## 迁移步骤

### 1. 更新Import语句

#### 旧版本 (已删除)
```java
import com.linkBuy.common.event.EventBus;
import com.linkBuy.common.event.supplier.SupplierUserPasswordEvent;
import com.linkBuy.common.event.company.CompanyUserPasswordEvent;
import com.linkBuy.common.event.supplier.SupplierUserCreatedEvent;
```

#### 新版本 (V2)
```java
import com.linkBuy.common.event.v2.EventBus;
import com.linkBuy.common.event.v2.supplier.SupplierUserPasswordEvent;
import com.linkBuy.common.event.v2.company.CompanyUserPasswordEvent;
import com.linkBuy.common.event.v2.supplier.SupplierUserCreatedEvent;
```

### 2. 更新事件发布代码

#### 旧版本
```java
// 同步发布 (实际上不是真正同步)
eventBus.publishEvent(event);

// 异步发布 (实际上被注释掉了)
eventBus.publishEventAsync(event);
```

#### 新版本
```java
// 同步发布
EventPublishResult result = eventBus.publish(event);
if (result.isSuccess()) {
    log.info("事件发布成功: {}", result.getMessageId());
} else {
    log.error("事件发布失败: {}", result.getError());
}

// 异步发布
eventBus.publishAsync(event)
    .thenAccept(result -> {
        if (result.isSuccess()) {
            log.info("事件发布成功: {}", result.getMessageId());
        } else {
            log.error("事件发布失败: {}", result.getError());
        }
    });

// 事务性发布 (新功能)
eventBus.publishInTransaction(event);

// 批量发布 (新功能)
eventBus.publishBatchAsync(events)
    .thenAccept(results -> {
        long successCount = results.stream().mapToLong(r -> r.isSuccess() ? 1 : 0).sum();
        log.info("批量发布完成: 成功={}, 总数={}", successCount, results.size());
    });
```

### 3. 事件类的变化

#### 构造方法
```java
// 旧版本
SupplierUserPasswordEvent event = new SupplierUserPasswordEvent(source, operatorId, operatorName);

// 新版本 (相同)
SupplierUserPasswordEvent event = new SupplierUserPasswordEvent(source, operatorId, operatorName);

// 或使用静态工厂方法
SupplierUserPasswordEvent event = SupplierUserPasswordEvent.createPasswordResetEvent(source, operatorId, operatorName);
```

#### 新增方法
```java
// 新版本支持的方法
event.getDomain();        // 返回 "supplier"
event.isOrdered();        // 返回 true (密码事件需要保证顺序)
event.getPartitionKey();  // 返回分区键用于有序处理
```

### 4. 创建新的事件处理器

由于旧的事件监听器已被删除，需要创建新的事件处理器：

```java
@Component
@RequiredArgsConstructor
@Handler(
    value = "supplierUserPasswordHandler",
    async = true,
    maxRetries = 3,
    retryInterval = 2000,
    timeout = 30000
)
public class SupplierUserPasswordEventHandler implements EventHandler<SupplierUserPasswordEvent> {
    
    private final EmailService emailService;
    
    @Override
    public EventHandleResult handle(SupplierUserPasswordEvent event, EventHandleContext context) {
        try {
            // 处理逻辑
            if ("CREATE".equals(event.getOperationType())) {
                emailService.sendAccountCreatedEmail(
                    event.getEmail(),
                    event.getMobile(),
                    event.getRawPassword(),
                    "供应商"
                );
            } else if ("RESET".equals(event.getOperationType())) {
                emailService.sendPasswordResetEmail(
                    event.getEmail(),
                    event.getMobile(),
                    event.getRawPassword(),
                    "供应商"
                );
            }
            
            return EventHandleResult.success();
            
        } catch (Exception e) {
            return EventHandleResult.failure(e.getMessage(), shouldRetry(e));
        }
    }
    
    @Override
    public boolean filter(SupplierUserPasswordEvent event) {
        return StringUtils.hasText(event.getEmail());
    }
}
```

### 5. 配置更新

#### 应用配置
```yaml
linkbuy:
  event:
    enabled: true
    consumer:
      group: linkbuy-consumers-v2
      batch-size: 10
      poll-timeout: 1000
      max-retries: 3
      retry-interval: 2000
    producer:
      async: true
      batch-enabled: true
      batch-size: 100
      timeout: 5000
```

## 迁移检查清单

### 必须完成的任务

- [ ] 更新所有Controller中的import语句
- [ ] 更新所有Service中的import语句
- [ ] 修改事件发布代码以使用新的API
- [ ] 创建新的事件处理器替代旧的监听器
- [ ] 更新配置文件
- [ ] 测试事件发布和处理功能

### 可选的改进

- [ ] 使用事务性发布功能
- [ ] 使用批量发布功能
- [ ] 添加性能监控
- [ ] 实现自定义重试逻辑
- [ ] 添加事件过滤器

## 新功能优势

### 1. 真正的异步处理
- 使用CompletableFuture实现真正的异步
- 支持回调处理发布结果

### 2. 批量处理
- 支持批量发布事件
- 提高性能和吞吐量

### 3. 事务性发布
- 事务提交后才发布事件
- 事务回滚时取消发布

### 4. 更好的错误处理
- 详细的发布结果反馈
- 可配置的重试机制
- 智能的错误分类

### 5. 性能监控
- 处理时间统计
- 成功率监控
- 详细的日志记录

## 常见问题

### Q: 旧的事件还会被处理吗？
A: 不会。旧的事件监听器已被删除，需要创建新的事件处理器。

### Q: 可以同时运行新旧系统吗？
A: 旧系统已被删除，只能使用新的V2系统。

### Q: 如何确保事件不丢失？
A: 新系统使用Redis Streams提供更好的持久化和可靠性保证。

### Q: 性能会有影响吗？
A: 新系统性能更好，支持批量处理和真正的异步操作。

## 获取帮助

如果在迁移过程中遇到问题，请参考：
- `LinkBuy/doc/EVENT_SYSTEM_V2_DESIGN.md` - 详细的设计文档
- 新的事件处理器示例代码
- 单元测试示例

## 迁移时间表

建议按以下顺序进行迁移：

1. **第一阶段**：修复编译错误
   - 更新import语句
   - 修改事件发布代码

2. **第二阶段**：创建事件处理器
   - 实现新的Handler
   - 测试事件处理功能

3. **第三阶段**：功能验证
   - 端到端测试
   - 性能测试

4. **第四阶段**：优化和监控
   - 添加监控指标
   - 性能调优 