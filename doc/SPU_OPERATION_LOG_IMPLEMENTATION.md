# SPU操作日志系统实现说明

## 概述

本文档描述了SPU操作日志系统的设计和实现，该系统用于记录供应商SPU的各类操作，包括创建、修改、审核、改价等操作的完整日志记录。

## 数据库设计

### 表结构：biz_supplier_spu_operation_log

```sql
CREATE TABLE `biz_supplier_spu_operation_log` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `spu_id` bigint(20) NOT NULL COMMENT 'SPU ID',
    `operation_type` varchar(50) NOT NULL COMMENT '操作类型',
    `operation_category` varchar(20) NOT NULL COMMENT '操作分类',
    `operator_id` bigint(20) NOT NULL COMMENT '操作人ID',
    `operator_name` varchar(100) NOT NULL DEFAULT '' COMMENT '操作人姓名',
    `operator_type` varchar(20) NOT NULL COMMENT '操作人类型',
    `operation_desc` varchar(500) NOT NULL DEFAULT '' COMMENT '操作描述',
    `operation_comment` longtext COMMENT '操作备注/原因',
    `before_status` int(11) DEFAULT NULL COMMENT '操作前状态',
    `after_status` int(11) DEFAULT NULL COMMENT '操作后状态',
    `before_data` longtext COMMENT '操作前数据（JSON格式）',
    `after_data` longtext COMMENT '操作后数据（JSON格式）',
    `change_fields` longtext COMMENT '变更字段列表（JSON格式）',
    `related_request_id` bigint(20) DEFAULT NULL COMMENT '关联的申请ID（改价申请等）',
    `related_request_no` varchar(100) DEFAULT NULL COMMENT '关联的申请单号',
    `operation_time` datetime NOT NULL COMMENT '操作时间',
    `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
    `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
    `client_info` varchar(200) DEFAULT NULL COMMENT '客户端信息',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-否，1-是',
    PRIMARY KEY (`id`),
    KEY `idx_spu_id` (`spu_id`),
    KEY `idx_operation_type` (`operation_type`),
    KEY `idx_operation_category` (`operation_category`),
    KEY `idx_operator_id` (`operator_id`),
    KEY `idx_operator_type` (`operator_type`),
    KEY `idx_operation_time` (`operation_time`),
    KEY `idx_related_request_id` (`related_request_id`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='SPU操作日志表';
```

### 字段说明

- **operation_type**: 操作类型，支持以下值：
  - CREATE: 创建
  - UPDATE: 更新
  - DELETE: 删除
  - SUBMIT_REVIEW: 提交审核
  - REVIEW_PASS: 审核通过
  - REVIEW_REJECT: 审核拒绝
  - WITHDRAW_REVIEW: 撤回审核
  - SUBMIT_PRICE_CHANGE: 提交改价申请
  - WITHDRAW_PRICE_CHANGE: 撤回改价申请
  - PRICE_CHANGE_PASS: 改价申请通过
  - PRICE_CHANGE_REJECT: 改价申请拒绝
  - ONLINE: 上架
  - OFFLINE: 下架
  - WAREHOUSE: 放入仓库

- **operation_category**: 操作分类：
  - BASIC: 基础操作（创建、更新、删除）
  - REVIEW: 审核操作
  - PRICE_CHANGE: 改价操作
  - STATUS: 状态操作

- **operator_type**: 操作人类型：
  - SUPPLIER: 供应商
  - ADMIN: 管理员
  - SYSTEM: 系统

## 代码实现

### 1. 实体类

`BizSupplierSpuOperationLog.java` - 包含完整的字段定义和枚举类型

### 2. Mapper接口

`BizSupplierSpuOperationLogMapper.java` - 提供基础查询方法：
- 根据SPU ID查询日志
- 根据操作类型和时间范围查询
- 根据操作人ID查询
- 根据关联申请ID查询

### 3. Service层

`BizSupplierSpuOperationLogService.java` - 提供业务方法：
- `recordOperationLog()`: 记录操作日志
- `getOperationLogsBySpuId()`: 获取SPU的操作日志
- `getRecentOperationLogs()`: 获取最近的操作日志
- `countOperationLogs()`: 统计操作日志数量

### 4. Controller层集成

在 `ProductController.java` 中集成了日志记录功能：

#### 已集成的操作：

1. **商品创建** - CREATE
2. **商品更新** - UPDATE  
3. **商品删除** - DELETE
4. **提交审核** - SUBMIT_REVIEW
5. **撤回审核** - WITHDRAW_REVIEW
6. **批量提交审核** - SUBMIT_REVIEW
7. **批量删除** - DELETE
8. **批量下架** - OFFLINE
9. **批量放入仓库** - WAREHOUSE
10. **提交改价申请** - SUBMIT_PRICE_CHANGE
11. **撤回改价申请** - WITHDRAW_PRICE_CHANGE

#### 核心方法：

- `recordReviewLog()`: 记录审核相关日志
- `recordReviewLogWithRequest()`: 带HTTP请求参数的日志记录
- `determineOperationCategory()`: 确定操作分类
- `generateOperationDesc()`: 生成操作描述
- `determineStatusAction()`: 根据状态确定操作动作

## API接口

### 获取SPU操作日志

```
GET /product/{id}/review-logs?limit=20
```

**响应示例：**
```json
[
  {
    "id": 1,
    "operationType": "CREATE",
    "operationCategory": "BASIC",
    "operatorName": "张三",
    "operatorType": "SUPPLIER",
    "operationDesc": "创建商品",
    "operationComment": "创建新商品",
    "beforeStatus": null,
    "afterStatus": 2,
    "operationTime": "2024-01-01T10:30:00",
    "relatedRequestNo": null
  }
]
```

## 特性和优势

### 1. 完整的操作记录
- 记录所有SPU相关操作
- 包含操作前后的状态变化
- 支持JSON格式的数据记录

### 2. 灵活的查询方式
- 按SPU ID查询
- 按操作类型查询
- 按时间范围查询
- 按操作人查询

### 3. 丰富的上下文信息
- IP地址记录
- 用户代理信息
- 客户端信息
- 关联申请信息

### 4. 分类管理
- 按操作分类组织（基础、审核、改价、状态）
- 支持枚举类型，便于扩展

### 5. 性能优化
- 合理的索引设计
- 支持分页查询
- 避免N+1查询问题

## 扩展性

### 1. 新增操作类型
只需在 `OperationType` 枚举中添加新的类型即可

### 2. 新增操作分类
在 `OperationCategory` 枚举中添加新分类

### 3. 新增查询维度
在Mapper中添加新的查询方法

### 4. 数据分析
可基于日志数据进行：
- 操作频率分析
- 用户行为分析
- 系统使用情况统计

## 注意事项

1. **性能考虑**: 日志表会快速增长，建议定期归档历史数据
2. **存储空间**: JSON字段可能占用较多空间，根据需要优化
3. **敏感信息**: 避免在日志中记录敏感的业务数据
4. **并发处理**: 日志记录失败不应影响主业务流程
5. **数据一致性**: 日志记录采用异步方式，避免影响主业务性能

## 未来改进

1. **异步日志记录**: 使用消息队列异步处理日志记录
2. **日志压缩**: 对历史日志进行压缩存储
3. **实时监控**: 基于日志数据建立实时监控告警
4. **数据可视化**: 提供日志数据的可视化分析界面 