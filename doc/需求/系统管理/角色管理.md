# 角色管理功能需求文档

## 1. 功能概述
实现一个角色管理系统，用于定义不同的用户角色，并为角色分配相应的权限，以控制用户对系统资源（菜单、页面、API等）的访问。

## 2. 界面布局
### 2.1 整体布局
- 采用左右或上下布局（具体布局可根据设计进一步确定）
- 主要展示角色列表
- 提供角色添加、编辑、删除的入口
- 提供管理角色权限和用户关联的功能入口

### 2.2 角色列表
- 以表格形式展示系统中的所有角色
- 包含以下列：角色名称、角色编码、描述、创建时间、更新时间
- 支持分页、排序和搜索
- 每行操作列包含：编辑、删除、权限管理、用户关联

### 2.3 角色编辑/添加表单
- 弹出框或独立页面形式
- 包含以下字段：
  - 角色名称 (role_name): 必填，唯一
  - 角色编码 (role_code): 必填，唯一，用于系统内部标识
  - 描述 (description): 选填

### 2.4 权限管理界面
- 弹出框或独立页面形式
- 左侧展示权限树形结构或列表（按类型分类：菜单权限、API权限等）
- 右侧展示当前角色已拥有的权限
- 支持通过勾选/取消勾选方式为角色分配/取消分配权限
- 提供全选/全不选功能
- 提供保存按钮

### 2.5 用户关联界面
- 弹出框或独立页面形式
- 左侧展示所有用户列表（可搜索、分页）
- 右侧展示已关联到当前角色的用户列表
- 支持将用户从左侧移动到右侧（关联），或从右侧移动到左侧（取消关联）
- 提供保存按钮

## 3. 功能需求
### 3.1 角色管理
1. 添加角色：
   - 点击"添加角色"按钮
   - 填写角色基本信息
   - 保存新角色
2. 编辑角色：
   - 点击角色列表中的"编辑"按钮
   - 修改角色基本信息
   - 保存修改
3. 删除角色：
   - 点击角色列表中的"删除"按钮
   - 弹出确认提示框
   - 确认后逻辑删除角色
   - 删除角色时，需要解除该角色与用户和权限的所有关联关系

### 3.2 权限管理
1. 查看角色权限：
   - 点击角色列表中的"权限管理"按钮
   - 进入权限管理界面，显示当前角色已分配的权限
2. 分配/取消分配权限：
   - 在权限管理界面，通过勾选操作为角色分配或取消分配权限
   - 保存权限分配结果

### 3.3 用户关联
1. 查看角色用户：
   - 点击角色列表中的"用户关联"按钮
   - 进入用户关联界面，显示已关联到当前角色的用户
2. 关联/取消关联用户：
   - 在用户关联界面，通过移动操作将用户关联或取消关联到当前角色
   - 保存用户关联结果

## 4. 接口需求
### 4.1 角色管理接口
- 获取角色列表：`GET /system/roles` (支持分页、搜索、排序)
- 获取角色详情：`GET /system/roles/{id}`
- 添加角色：`POST /system/roles`
- 更新角色：`PUT /system/roles/{id}`
- 删除角色：`DELETE /system/roles/{id}` (逻辑删除)

### 4.2 权限管理接口
- 获取所有权限树/列表：`GET /system/permissions`
- 获取角色已分配权限：`GET /system/roles/{roleId}/permissions`
- 更新角色权限：`POST /system/roles/{roleId}/permissions` (请求体包含权限ID列表)

### 4.3 用户关联接口
- 获取所有用户列表：`GET /system/users` (支持分页、搜索)
- 获取角色已关联用户：`GET /system/roles/{roleId}/users`
- 更新角色关联用户：`POST /system/roles/{roleId}/users` (请求体包含用户ID列表)

## 5. 数据结构
### 5.1 角色 (SYS_ROLE)

### 5.2 权限 (SYS_PERMISSION)
```
- id: string PK
- permission_name: string (权限名称)
- permission_code: string (权限编码)
- permission_type: string (权限类型，如 menu, api, button)
- description: string (描述)
- create_time: datetime
- update_time: datetime
- is_deleted: boolean (逻辑删除标识)
```
（权限的结构可能需要进一步细化，例如关联具体的菜单ID或API路径和方法）

### 5.3 角色权限关联 (SYS_ROLE_PERMISSION)
```
- id: string PK
- role_id: string FK (角色ID)
- permission_id: string FK (权限ID)
- create_time: datetime
```

### 5.4 用户角色关联 (SYS_USER_ROLE)
```
- id: string PK
- user_id: string FK (用户ID)
- role_id: string FK (角色ID)
- create_time: datetime
```

## 6. 非功能需求
### 6.1 性能需求
- 角色列表加载时间不超过2秒
- 保存操作响应时间不超过1秒
- 支持处理大量角色、权限和用户数据

### 6.2 安全需求
- 所有接口需要进行权限验证
- 确保只有具备相应权限的用户才能进行角色和权限管理操作
- 防止恶意修改角色和权限数据

### 6.3 兼容性需求
- 支持主流浏览器
- 支持响应式布局

## 7. 注意事项
- 角色名称和编码需要唯一性校验。
- 删除角色时需要解除所有关联，避免数据孤儿。
- 权限类型（菜单、API、按钮等）需要明确定义和管理。
- 权限的层级结构（如果存在）需要在权限管理界面中体现。
- 用户权限是用户所有角色的权限集合。
- 需要考虑超级管理员角色，拥有所有权限且不可删除。
