```mermaid
erDiagram
    SYS_USER ||--o{ SYS_USER_ROLE : has
    SYS_ROLE ||--o{ SYS_USER_ROLE : assigned_to
    SYS_MENU ||--o{ SYS_MENU : has_parent
    SYS_DICT ||--o{ SYS_DICT_ITEM : contains
    SYS_USER ||--o{ SYS_USER_LOGIN_LOG : generates
    SYS_USER ||--o{ SYS_USER_OPERATION_LOG : generates
    SYS_ROLE ||--o{ SYS_ROLE_MENU : has
    SYS_MENU ||--o{ SYS_ROLE_MENU : assigned_to
    T_GEN_REGION ||--o{ T_GEN_REGION : has_parent
    t_gen_product_category ||--o{ t_gen_product_category : has_parent
    SYS_ROLE ||--o{ SYS_FUNCTION : "角色关联功能权限"
    SYS_FUNCTION ||--o{ SYS_API : "功能包含多个API"
    SYS_MENU ||--o{ SYS_FUNCTION : "菜单关联功能"

    SYS_USER {
        bigint(20) id PK
        string username
        string password
        string email
        string mobile
        string avatar
        string bio
        string gender
        datetime birthday
        int status
        datetime create_time
        datetime update_time
        boolean is_deleted
    }

    SYS_USER_LOGIN_LOG {
        bigint(20) id PK
        string user_id FK
        string ip_address
        string user_agent
        string login_status
        string login_message
        datetime login_time
        datetime create_time
    }

    SYS_USER_OPERATION_LOG {
        bigint(20) id PK
        string user_id FK
        string module
        string operation
        string method
        string params
        string ip_address
        string user_agent
        datetime operation_time
        datetime create_time
    }

    SYS_ROLE_MENU {
        bigint(20) id PK
        string role_id FK
        string menu_id FK
        datetime create_time
    }

    SYS_CONFIG {
        bigint(20) id PK
        string config_key
        string config_value
        string description
        datetime create_time
        datetime update_time
        boolean is_deleted
    }

    SYS_PARAM {
        bigint(20) id PK
        string param_key
        string param_value
        string param_type
        string description
        datetime create_time
        datetime update_time
        boolean is_deleted
    }

    SYS_ROLE {
        bigint(20) id PK
        string role_name
        string role_code
        string description
        datetime create_time
        datetime update_time
        boolean is_deleted
    }

    SYS_USER_ROLE {
        bigint(20) id PK
        string user_id FK
        string role_id FK
        datetime create_time
    }

    SYS_MENU {
        bigint(20) id PK
        string name
        string code
        string type
        string parent_id FK
        int sort_order
        string icon
        string path
        string page_component
        boolean is_visible
        boolean is_enabled
        datetime create_time
        datetime update_time
        boolean is_deleted
    }

    SYS_SUPPLIER_MENU {
        bigint(20) id PK
        string name
        string code
        string type
        string parent_id FK
        int sort_order
        string icon
        string path
        string page_component
        boolean is_visible
        boolean is_enabled
        datetime create_time
        datetime update_time
        boolean is_deleted
    }

    SYS_MALL_ADMIN_MENU {
        bigint(20) id PK
        string name
        string code
        string type
        string parent_id FK
        int sort_order
        string icon
        string path
        string page_component
        boolean is_visible
        boolean is_enabled
        datetime create_time
        datetime update_time
        boolean is_deleted
    }

    SYS_DICT {
        bigint(20) id PK
        string dict_name
        string dict_code
        string description
        datetime create_time
        datetime update_time
        boolean is_deleted
    }

    SYS_DICT_ITEM {
        bigint(20) id PK
        string dict_id FK
        string item_name
        string item_value
        int sort_order
        datetime create_time
        datetime update_time
        boolean is_deleted
    }

    SYS_API {
        bigint(20) id PK
        string api_name "API名称"
        string api_path "API路径"
        string api_method "API方法"
        string description "描述"
        datetime create_time
        datetime update_time
        boolean is_deleted
    }

    SYS_FUNCTION {
        bigint(20) id PK
        string function_name "功能名称"
        string function_code "功能编码"
        string route_path "路由路径"
        string view_path "视图路径"
        string description "描述"
        datetime create_time
        datetime update_time
        boolean is_deleted
    }

    SYS_FS_ACCOUNT_TAG {
        bigint(20) id PK
        string fs_account_code
        string name
        datetime create_time
        datetime update_time
        boolean is_deleted
    }

    SYS_FS_ACCOUNT_FILE {
        bigint(20) id PK
        string fs_account_code
        string type
        string name
        string path
        string tag_id
        string oss_key
        datetime create_time
        datetime update_time
        boolean is_deleted
    }

    T_GEN_EXPRESS_COMPANY {
        bigint(20) id PK
        string company_name
        string company_code
        string icon
        boolean is_enabled
        datetime create_time
        datetime update_time
        boolean is_deleted
    }

    T_GEN_REGION {
        bigint(20) id PK
        string region_code
        string region_name
        string parent_id FK
        int level
        int sort_order
        string short_name
        string pinyin
        string jianpin
        string full_name
        boolean is_enabled
        datetime create_time
        datetime update_time
        boolean is_deleted
    }

    T_GEN_UNIT {
        bigint(20) id PK
        string unit_name
        string unit_symbol
        int sort_order
        datetime create_time
        datetime update_time
        boolean is_deleted
    }
    
    t_gen_product_category {
        bigint(20) id PK
        string category_code
        string category_name
        string parent_id FK
        int level
        int sort_order
        string icon
        string description
        datetime create_time
        datetime update_time
        boolean is_deleted
    }

    t_gen_product_category ||--o{ BIZ_SUPPLIER_SPU : "商品类目"
    T_GEN_UNIT ||--o{ BIZ_SUPPLIER_SKU : "商品单位"
    T_GEN_EXPRESS_COMPANY ||--o{ BIZ_FREIGHT_TEMPLATE : "快递公司"
```