-- 客户表
CREATE TABLE `biz_company` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name` VARCHAR(100) NOT NULL COMMENT '客户名称',
    `code` VARCHAR(50) DEFAULT NULL COMMENT '客户编码',
    `invoice_title` VARCHAR(200) DEFAULT NULL COMMENT '发票抬头',
    `invoice_tax_no` VARCHAR(50) DEFAULT NULL COMMENT '发票税号',
    `contact_mobile` VARCHAR(20) DEFAULT NULL COMMENT '联系人手机号',
    `contact_name` VARCHAR(50) DEFAULT NULL COMMENT '联系人姓名',
    `contact_email` VARCHAR(100) DEFAULT NULL COMMENT '联系人邮箱',
    `address` VARCHAR(500) DEFAULT NULL COMMENT '地址',
    `usci_code` VARCHAR(50) DEFAULT NULL COMMENT '统一社会信用代码',
    `biz_license_pic_path` VARCHAR(500) DEFAULT NULL COMMENT '营业执照图片路径',
    `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `remark` TEXT DEFAULT NULL COMMENT '备注',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_usci_code` (`usci_code`),
    KEY `idx_name` (`name`),
    KEY `idx_contact_mobile` (`contact_mobile`),
    KEY `idx_contact_name` (`contact_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户表'; 