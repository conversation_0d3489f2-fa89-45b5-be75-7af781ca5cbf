-- =====================================================
-- 供应商管理相关表 DDL
-- 创建时间: 2024-12-19
-- 更新时间: 2024-12-19
-- 描述: 包含供应商表和供应商资质表的完整建表语句
-- 版本: v2.0 - 完善资质管理功能
-- =====================================================

-- 1. 供应商表
DROP TABLE IF EXISTS `biz_supplier`;
CREATE TABLE `biz_supplier` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `code` VARCHAR(50) DEFAULT NULL COMMENT '供应商编码',
    `name` VARCHAR(100) NOT NULL COMMENT '供应商名称',
    `usci_code` VARCHAR(50) DEFAULT NULL COMMENT '统一社会信用代码',
    `supplier_type` TINYINT(4) NOT NULL DEFAULT 1 COMMENT '供应商类型：1-客户供应商，2-平台供应商',
    `company_id` BIGINT(20) DEFAULT NULL COMMENT '关联客户ID（供应商类型为客户供应商时使用）',
    `legal_person` VARCHAR(50) DEFAULT NULL COMMENT '法人代表',
    `contact_name` VARCHAR(50) NOT NULL COMMENT '联系人姓名',
    `contact_mobile` VARCHAR(20) NOT NULL COMMENT '联系人手机号',
    `contact_email` VARCHAR(100) DEFAULT NULL COMMENT '联系人邮箱',
    `address` VARCHAR(500) DEFAULT NULL COMMENT '详细地址',
    `bank_name` VARCHAR(100) DEFAULT NULL COMMENT '银行名称',
    `subbranch_name` VARCHAR(100) DEFAULT NULL COMMENT '支行名称',
    `bank_account` VARCHAR(50) DEFAULT NULL COMMENT '银行账号',
    `invoice_title` VARCHAR(200) DEFAULT NULL COMMENT '发票抬头',
    `invoice_tax_no` VARCHAR(50) DEFAULT NULL COMMENT '发票税号',
    `status` TINYINT(4) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `remark` TEXT DEFAULT NULL COMMENT '备注',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` TINYINT(4) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_supplier_name` (`name`),
    UNIQUE KEY `uk_supplier_code` (`code`),
    UNIQUE KEY `uk_supplier_usci_code` (`usci_code`),
    KEY `idx_supplier_type` (`supplier_type`),
    KEY `idx_company_id` (`company_id`),
    KEY `idx_contact_name` (`contact_name`),
    KEY `idx_contact_mobile` (`contact_mobile`),
    KEY `idx_legal_person` (`legal_person`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='供应商表';

-- 2. 供应商资质表
DROP TABLE IF EXISTS `biz_supplier_license`;
CREATE TABLE `biz_supplier_license` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `supplier_id` BIGINT(20) NOT NULL COMMENT '供应商ID',
    `license_name` VARCHAR(100) NOT NULL COMMENT '资质名称',
    `license_type` VARCHAR(50) NOT NULL COMMENT '资质类型',
    `license_number` VARCHAR(100) DEFAULT NULL COMMENT '资质证书编号',
    `file_path` VARCHAR(500) NOT NULL COMMENT '资质文件路径',
    `issue_date` DATE DEFAULT NULL COMMENT '颁发日期',
    `expire_date` DATE DEFAULT NULL COMMENT '有效期至',
    `issuing_authority` VARCHAR(200) DEFAULT NULL COMMENT '颁发机构',
    `status` TINYINT(4) NOT NULL DEFAULT 1 COMMENT '状态：1-有效，2-即将过期，3-已过期',
    `remark` TEXT DEFAULT NULL COMMENT '备注',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` TINYINT(4) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_supplier_id` (`supplier_id`),
    KEY `idx_license_type` (`license_type`),
    KEY `idx_expire_date` (`expire_date`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='供应商资质表';

-- 3. 添加外键约束
-- 供应商表与客户表的关联（可选，根据业务需求决定是否启用）
-- ALTER TABLE `biz_supplier` ADD CONSTRAINT `fk_supplier_company` FOREIGN KEY (`company_id`) REFERENCES `biz_company` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- 供应商资质表与供应商表的关联
ALTER TABLE `biz_supplier_license` ADD CONSTRAINT `fk_license_supplier` FOREIGN KEY (`supplier_id`) REFERENCES `biz_supplier` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- 4. 插入示例数据（可选）
-- INSERT INTO `biz_supplier` (`name`, `supplier_type`, `contact_name`, `contact_mobile`, `status`) VALUES
-- ('测试平台供应商1', 2, '张三', '***********', 1),
-- ('测试平台供应商2', 2, '李四', '***********', 1);

-- INSERT INTO `biz_supplier_license` (`supplier_id`, `license_name`, `license_type`, `license_number`, `file_path`, `status`) VALUES
-- (1, '营业执照', '营业执照', '91330100MA28F1234X', '/uploads/license/business_license_1.jpg', 1),
-- (1, 'ISO9001质量管理体系认证', '质量管理体系认证', 'ISO9001-2024-001', '/uploads/license/iso9001_1.jpg', 1);

-- =====================================================
-- DDL 执行完成
-- 说明：
-- 1. 供应商表支持平台供应商和客户供应商两种类型
-- 2. 供应商资质表支持完整的资质信息管理
-- 3. 包含完整的索引优化和外键约束
-- 4. 支持软删除和自动时间戳
-- =====================================================