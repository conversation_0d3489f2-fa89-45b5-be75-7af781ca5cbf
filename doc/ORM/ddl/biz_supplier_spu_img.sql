-- 供应商SPU图片表
CREATE TABLE `biz_supplier_spu_img` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `supplier_id` BIGINT(20) NOT NULL COMMENT '供应商ID',
    `img_url` VARCHAR(500) NOT NULL COMMENT '图片URL',
    `is_main` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否主图：0-否，1-是',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_supplier_id` (`supplier_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='供应商SPU图片表'; 