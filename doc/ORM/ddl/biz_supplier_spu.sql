-- 供应商SPU表
CREATE TABLE `biz_supplier_spu` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name` VARCHAR(200) NOT NULL COMMENT 'SPU名称',
    `supplier_id` BIGINT(20) NOT NULL COMMENT '供应商ID',
    `category_id` BIGINT(20) NOT NULL COMMENT '类目ID',
    `img_url` VARCHAR(500) DEFAULT NULL COMMENT '图片URL',
    `barcode` VARCHAR(50) DEFAULT NULL COMMENT '条形码',
    `type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '类型：1-普通商品，2-特殊商品',
    `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `service_status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '服务状态：0-未开通，1-已开通',
    `review_describe` VARCHAR(500) DEFAULT NULL COMMENT '审核描述',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_supplier_id` (`supplier_id`),
    KEY `idx_category_id` (`category_id`),
    KEY `idx_barcode` (`barcode`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='供应商SPU表'; 