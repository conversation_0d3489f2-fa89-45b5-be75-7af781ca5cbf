-- =====================================================
-- 供应商资质表 DDL
-- 创建时间: 2024-12-19
-- 更新时间: 2024-12-19
-- 版本: v2.0 - 完善资质管理功能
-- =====================================================

-- 供应商资质表
CREATE TABLE `biz_supplier_license` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `supplier_id` BIGINT(20) NOT NULL COMMENT '供应商ID',
    `license_name` VARCHAR(100) NOT NULL COMMENT '资质名称',
    `license_type` VARCHAR(50) NOT NULL COMMENT '资质类型',
    `license_number` VARCHAR(100) DEFAULT NULL COMMENT '资质证书编号',
    `file_path` VARCHAR(500) NOT NULL COMMENT '资质文件路径',
    `issue_date` DATE DEFAULT NULL COMMENT '颁发日期',
    `expire_date` DATE DEFAULT NULL COMMENT '有效期至',
    `issuing_authority` VARCHAR(200) DEFAULT NULL COMMENT '颁发机构',
    `status` TINYINT(4) NOT NULL DEFAULT 1 COMMENT '状态：1-有效，2-即将过期，3-已过期',
    `remark` TEXT DEFAULT NULL COMMENT '备注',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` TINYINT(4) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_supplier_id` (`supplier_id`),
    KEY `idx_license_type` (`license_type`),
    KEY `idx_expire_date` (`expire_date`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='供应商资质表';

-- 添加外键约束
ALTER TABLE `biz_supplier_license` ADD CONSTRAINT `fk_license_supplier` FOREIGN KEY (`supplier_id`) REFERENCES `biz_supplier` (`id`) ON DELETE CASCADE ON UPDATE CASCADE; 