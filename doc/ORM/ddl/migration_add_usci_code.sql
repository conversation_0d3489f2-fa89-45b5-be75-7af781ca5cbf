-- =====================================================
-- 供应商表添加统一社会信用代码字段迁移脚本
-- 创建时间: 2024-12-19
-- 描述: 为现有的biz_supplier表添加usci_code字段和唯一索引
-- =====================================================

-- 1. 添加统一社会信用代码字段
ALTER TABLE `biz_supplier` ADD COLUMN `usci_code` VARCHAR(50) DEFAULT NULL COMMENT '统一社会信用代码' AFTER `name`;

-- 2. 添加唯一索引
ALTER TABLE `biz_supplier` ADD UNIQUE KEY `uk_supplier_usci_code` (`usci_code`);

-- 3. 验证字段添加成功
-- SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
-- FROM INFORMATION_SCHEMA.COLUMNS 
-- WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'biz_supplier' AND COLUMN_NAME = 'usci_code';

-- 4. 验证索引添加成功
-- SHOW INDEX FROM `biz_supplier` WHERE Key_name = 'uk_supplier_usci_code';

-- =====================================================
-- 迁移脚本执行完成
-- 注意事项：
-- 1. 执行前请备份数据库
-- 2. 确保没有重复的统一社会信用代码数据
-- 3. 如果有重复数据，请先清理后再执行
-- ===================================================== 