create table sys_user_role
(
    id          bigint auto_increment comment '主键ID'
        primary key,
    user_id     varchar(32)                        not null comment '用户ID',
    role_id     varchar(32)                        not null comment '角色ID',
    create_time datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time datetime default CURRENT_TIMESTAMP null,
    is_deleted  int      default 0                 null
)
    comment '用户角色关联表' charset = utf8mb4;

create index idx_role_id
    on sys_user_role (role_id);

create index idx_user_id
    on sys_user_role (user_id);

INSERT INTO lefu_test.sys_user_role (id, user_id, role_id, create_time, update_time, is_deleted) VALUES (1, '1', '1', '2025-05-22 10:25:02', '2025-05-25 08:19:46', 1);
INSERT INTO lefu_test.sys_user_role (id, user_id, role_id, create_time, update_time, is_deleted) VALUES (2, '1', '1', '2025-05-25 17:53:47', '2025-05-25 17:53:47', 0);
INSERT INTO lefu_test.sys_user_role (id, user_id, role_id, create_time, update_time, is_deleted) VALUES (3, '7', '1', '2025-05-25 17:54:05', '2025-05-25 17:54:05', 0);
INSERT INTO lefu_test.sys_user_role (id, user_id, role_id, create_time, update_time, is_deleted) VALUES (4, '1', '6', '2025-05-25 17:56:13', '2025-05-25 17:56:13', 1);
INSERT INTO lefu_test.sys_user_role (id, user_id, role_id, create_time, update_time, is_deleted) VALUES (5, '1', '3', '2025-05-25 17:56:13', '2025-05-25 17:56:13', 1);
INSERT INTO lefu_test.sys_user_role (id, user_id, role_id, create_time, update_time, is_deleted) VALUES (6, '1', '4', '2025-05-25 17:56:13', '2025-05-25 17:56:13', 1);
INSERT INTO lefu_test.sys_user_role (id, user_id, role_id, create_time, update_time, is_deleted) VALUES (7, '1', '5', '2025-05-25 17:56:19', '2025-05-25 17:56:19', 1);
INSERT INTO lefu_test.sys_user_role (id, user_id, role_id, create_time, update_time, is_deleted) VALUES (8, '10', '6', '2025-05-26 03:09:58', '2025-05-26 03:09:58', 0);
INSERT INTO lefu_test.sys_user_role (id, user_id, role_id, create_time, update_time, is_deleted) VALUES (9, '10', '5', '2025-05-26 03:09:58', '2025-05-26 03:09:58', 0);
INSERT INTO lefu_test.sys_user_role (id, user_id, role_id, create_time, update_time, is_deleted) VALUES (10, '1', '2', '2025-05-26 16:59:04', '2025-05-26 16:59:04', 1);
INSERT INTO lefu_test.sys_user_role (id, user_id, role_id, create_time, update_time, is_deleted) VALUES (11, '1', '2', '2025-05-26 17:12:28', '2025-05-26 17:12:28', 1);
INSERT INTO lefu_test.sys_user_role (id, user_id, role_id, create_time, update_time, is_deleted) VALUES (12, '1', '2', '2025-05-26 17:15:00', '2025-05-26 17:15:00', 1);
INSERT INTO lefu_test.sys_user_role (id, user_id, role_id, create_time, update_time, is_deleted) VALUES (13, '1', '2', '2025-05-26 17:18:37', '2025-05-26 17:18:37', 0);
