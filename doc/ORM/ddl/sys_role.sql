create table sys_role
(
    id          bigint auto_increment comment '主键ID'
        primary key,
    role_name   varchar(50)                          not null comment '角色名称',
    role_code   varchar(50)                          not null comment '角色编码',
    description varchar(200)                         null comment '角色描述',
    create_time datetime   default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time datetime   default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    is_deleted  tinyint(1) default 0                 not null comment '是否删除：0-未删除，1-已删除'
)
    comment '系统角色表' charset = utf8mb4;

create index idx_role_name
    on sys_role (role_name);

INSERT INTO lefu_test.sys_role (id, role_name, role_code, description, create_time, update_time, is_deleted) VALUES (1, '系统管理员', 'ADMIN', '系统管理员，拥有所有权限，不可编辑', '2025-05-22 10:23:23', '2025-05-22 10:23:23', 0);
INSERT INTO lefu_test.sys_role (id, role_name, role_code, description, create_time, update_time, is_deleted) VALUES (2, '业务运营', 'OPERATOR', '', '2025-05-25 08:29:59', '2025-05-26 16:58:33', 0);
INSERT INTO lefu_test.sys_role (id, role_name, role_code, description, create_time, update_time, is_deleted) VALUES (3, 'sdf', 'asdf', '', '2025-05-25 17:55:44', '2025-05-25 17:55:44', 0);
INSERT INTO lefu_test.sys_role (id, role_name, role_code, description, create_time, update_time, is_deleted) VALUES (4, 'sadf', 'safd', '', '2025-05-25 17:55:47', '2025-05-25 17:55:47', 0);
INSERT INTO lefu_test.sys_role (id, role_name, role_code, description, create_time, update_time, is_deleted) VALUES (5, 'sadfasdf', 'sadf', '', '2025-05-25 17:55:53', '2025-05-25 17:55:53', 0);
INSERT INTO lefu_test.sys_role (id, role_name, role_code, description, create_time, update_time, is_deleted) VALUES (6, 'sadfasdf12', 'sadfsafdasfd', '', '2025-05-25 17:56:04', '2025-05-25 17:56:04', 0);
