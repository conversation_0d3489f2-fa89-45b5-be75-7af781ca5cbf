create table sys_dict
(
    id          bigint auto_increment comment '主键ID'
        primary key,
    dict_name   varchar(100)                       not null comment '字典名称',
    dict_code   varchar(50)                        not null comment '字典编码',
    description varchar(2000)                      null comment '描述',
    create_time datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    is_deleted  tinyint  default 0                 not null comment '是否删除：0-未删除，1-已删除',
    constraint uk_dict_code
        unique (dict_code)
)
    comment '字典表';

INSERT INTO lefu_test.sys_dict (id, dict_name, dict_code, description, create_time, update_time, is_deleted) VALUES (1, '通用状态', 'common_status', null, '2025-05-22 06:49:04', '2025-05-22 06:49:04', 0);
INSERT INTO lefu_test.sys_dict (id, dict_name, dict_code, description, create_time, update_time, is_deleted) VALUES (2, '菜单类型', 'menu_type', null, '2025-05-24 03:40:23', '2025-05-24 03:40:23', 0);
