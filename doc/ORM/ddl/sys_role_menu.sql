create table sys_role_menu
(
    id          bigint auto_increment comment '主键ID'
        primary key,
    role_id     bigint                             not null comment '角色ID',
    menu_id     bigint                             not null comment '权限ID',
    create_time datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time int                                null,
    is_deleted  tinyint  default 0                 not null comment '是否删除：0-未删除，1-已删除'
)
    comment '角色权限关联表';

INSERT INTO lefu_test.sys_role_menu (id, role_id, menu_id, create_time, update_time, is_deleted) VALUES (27, 1, 2, '2025-05-25 16:34:58', null, 0);
INSERT INTO lefu_test.sys_role_menu (id, role_id, menu_id, create_time, update_time, is_deleted) VALUES (47, 1, 1, '2025-05-25 16:44:35', null, 0);
INSERT INTO lefu_test.sys_role_menu (id, role_id, menu_id, create_time, update_time, is_deleted) VALUES (48, 1, 4, '2025-05-25 16:44:35', null, 0);
INSERT INTO lefu_test.sys_role_menu (id, role_id, menu_id, create_time, update_time, is_deleted) VALUES (49, 1, 6, '2025-05-25 16:44:35', null, 0);
INSERT INTO lefu_test.sys_role_menu (id, role_id, menu_id, create_time, update_time, is_deleted) VALUES (50, 2, 37, '2025-05-26 16:58:54', null, 1);
INSERT INTO lefu_test.sys_role_menu (id, role_id, menu_id, create_time, update_time, is_deleted) VALUES (51, 2, 38, '2025-05-26 16:58:54', null, 1);
INSERT INTO lefu_test.sys_role_menu (id, role_id, menu_id, create_time, update_time, is_deleted) VALUES (52, 2, 35, '2025-05-26 17:27:40', null, 1);
INSERT INTO lefu_test.sys_role_menu (id, role_id, menu_id, create_time, update_time, is_deleted) VALUES (53, 2, 36, '2025-05-26 17:27:40', null, 1);
INSERT INTO lefu_test.sys_role_menu (id, role_id, menu_id, create_time, update_time, is_deleted) VALUES (54, 2, 30, '2025-05-27 08:53:14', null, 1);
INSERT INTO lefu_test.sys_role_menu (id, role_id, menu_id, create_time, update_time, is_deleted) VALUES (55, 2, 39, '2025-05-27 09:12:12', null, 1);
INSERT INTO lefu_test.sys_role_menu (id, role_id, menu_id, create_time, update_time, is_deleted) VALUES (56, 2, 39, '2025-05-27 09:13:19', null, 1);
INSERT INTO lefu_test.sys_role_menu (id, role_id, menu_id, create_time, update_time, is_deleted) VALUES (57, 2, 30, '2025-05-27 09:13:19', null, 1);
INSERT INTO lefu_test.sys_role_menu (id, role_id, menu_id, create_time, update_time, is_deleted) VALUES (58, 2, 39, '2025-05-27 09:14:46', null, 1);
INSERT INTO lefu_test.sys_role_menu (id, role_id, menu_id, create_time, update_time, is_deleted) VALUES (59, 2, 30, '2025-05-27 09:14:46', null, 1);
INSERT INTO lefu_test.sys_role_menu (id, role_id, menu_id, create_time, update_time, is_deleted) VALUES (60, 2, 39, '2025-05-27 09:23:48', null, 1);
INSERT INTO lefu_test.sys_role_menu (id, role_id, menu_id, create_time, update_time, is_deleted) VALUES (61, 2, 30, '2025-05-27 09:23:48', null, 1);
INSERT INTO lefu_test.sys_role_menu (id, role_id, menu_id, create_time, update_time, is_deleted) VALUES (62, 2, 39, '2025-05-27 09:28:36', null, 0);
INSERT INTO lefu_test.sys_role_menu (id, role_id, menu_id, create_time, update_time, is_deleted) VALUES (63, 2, 30, '2025-05-27 09:28:36', null, 0);
INSERT INTO lefu_test.sys_role_menu (id, role_id, menu_id, create_time, update_time, is_deleted) VALUES (64, 1, 40, '2025-05-27 13:58:06', null, 0);
INSERT INTO lefu_test.sys_role_menu (id, role_id, menu_id, create_time, update_time, is_deleted) VALUES (65, 1, 41, '2025-05-27 14:30:23', null, 0);
INSERT INTO lefu_test.sys_role_menu (id, role_id, menu_id, create_time, update_time, is_deleted) VALUES (66, 1, 42, '2025-05-27 16:30:43', null, 0);
