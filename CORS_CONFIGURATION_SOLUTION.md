# CORS跨域问题完整解决方案

## 问题描述

前端调用后端API时出现CORS错误，主要原因是：
1. 前端配置混乱，开发环境直接访问外部IP绕过代理
2. 后端CORS配置缺少生产环境域名
3. 不同环境的配置不统一

## 解决方案

### 1. 前端配置修复

#### 开发环境 (.env.development)
```bash
# 使用代理路径，通过Vite代理转发到后端
VITE_APP_BASE_URL=/api
```

#### 测试环境 (.env.test)
```bash
# 直接访问测试环境API域名
VITE_APP_BASE_URL=https://test-api.linkbuy.com
```

#### 预发布环境 (.env.pre)
```bash
# 直接访问预发布环境API域名
VITE_APP_BASE_URL=https://pre-api.linkbuy.com
```

#### 生产环境 (.env.production)
```bash
# 直接访问生产环境API域名
VITE_APP_BASE_URL=https://api.linkbuy.com
```

### 2. 后端CORS配置修复

#### platform-api配置 (application.yml)
```yaml
cors:
  allowed-origins: http://127.0.0.1:3000,http://localhost:3000,http://dev.mob.com:3000,http://dev.mob.com:3001,http://dev.mob.com:3002,https://admin.linkbuy.com,https://supplier.linkbuy.com,https://mall.linkbuy.com
```

#### CorsConfig.java默认配置
```java
@Value("${cors.allowed-origins:http://127.0.0.1:3000,http://localhost:3000,http://localhost:3001,http://localhost:3002,http://dev.mob.com:3000,http://dev.mob.com:3001,http://dev.mob.com:3002,https://admin.linkbuy.com,https://supplier.linkbuy.com,https://mall.linkbuy.com,https://test-admin.linkbuy.com,https://pre-admin.linkbuy.com}")
```

### 3. 工作流程

#### 开发环境
```
前端(dev.mob.com:3000) → Vite代理(/api) → 后端(127.0.0.1:8080)
```

#### 生产环境
```
前端(https://admin.linkbuy.com) → 直接请求 → 后端(https://api.linkbuy.com)
```

## 配置验证

### 1. 检查前端配置
```bash
# 开发环境
cat LinkBuy-FED/linkBuyAdmin/.env.development

# 生产环境
cat LinkBuy-FED/linkBuyAdmin/.env.production
```

### 2. 检查后端配置
```yaml
# 查看CORS配置
grep -A 2 "cors:" LinkBuy/platform-api/src/main/resources/application.yml
```

### 3. 测试CORS
```bash
# 开发环境测试
curl -H "Origin: http://dev.mob.com:3000" \
     -H "Access-Control-Request-Method: POST" \
     -H "Access-Control-Request-Headers: X-Requested-With" \
     -X OPTIONS \
     http://127.0.0.1:8080/api/test

# 生产环境测试
curl -H "Origin: https://admin.linkbuy.com" \
     -H "Access-Control-Request-Method: POST" \
     -H "Access-Control-Request-Headers: X-Requested-With" \
     -X OPTIONS \
     https://api.linkbuy.com/api/test
```

## 部署注意事项

### 1. 域名配置
确保以下域名正确解析：
- `https://admin.linkbuy.com` → 前端管理系统
- `https://api.linkbuy.com` → 后端API服务
- `https://supplier.linkbuy.com` → 供应商系统
- `https://mall.linkbuy.com` → 商城系统

### 2. 反向代理配置 (Nginx)
```nginx
# 前端静态文件
server {
    listen 443 ssl;
    server_name admin.linkbuy.com;
    
    location / {
        root /var/www/linkbuy-admin;
        try_files $uri $uri/ /index.html;
    }
}

# 后端API服务
server {
    listen 443 ssl;
    server_name api.linkbuy.com;
    
    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 3. SSL证书
确保所有域名都配置了有效的SSL证书，避免混合内容问题。

## 故障排查

### 1. 常见错误
- `Access to fetch at 'xxx' from origin 'xxx' has been blocked by CORS policy`
- `Cross-Origin Request Blocked`

### 2. 排查步骤
1. 检查前端请求的实际URL
2. 检查后端CORS配置是否包含前端域名
3. 检查是否有代理配置冲突
4. 验证SSL证书配置

### 3. 调试工具
- 浏览器开发者工具 Network 标签
- `curl` 命令测试预检请求
- 后端日志查看CORS处理过程

## 最佳实践

1. **环境隔离**：不同环境使用不同的域名和配置
2. **配置集中**：CORS配置统一管理，避免重复配置
3. **安全原则**：生产环境只允许必要的域名，避免使用通配符
4. **监控告警**：配置CORS错误的监控和告警机制 