# Redis Cluster 本机配置 - 端口 7104
port 7104

# 绑定所有接口
bind 0.0.0.0

# 启用集群模式
cluster-enabled yes

# 集群通告配置
cluster-announce-ip 127.0.0.1
cluster-announce-port 7104
cluster-announce-bus-port 17104

# 集群配置文件
cluster-config-file nodes-7104.conf

# 集群超时设置
cluster-node-timeout 15000

# 允许部分slot覆盖
cluster-require-full-coverage no

# 持久化配置
appendonly yes
appendfilename "appendonly-7104.aof"
dbfilename dump-7104.rdb

# 目录配置
dir /Users/<USER>/LinkBuy-Repo/redis/data/7104
logfile /Users/<USER>/LinkBuy-Repo/redis/logs/redis-7104.log
pidfile /Users/<USER>/LinkBuy-Repo/redis/pids/redis-7104.pid

# 运行配置
daemonize yes
loglevel notice
protected-mode no

# 认证配置
requirepass redis123456
masterauth redis123456

# 性能配置
maxmemory 256mb
maxmemory-policy allkeys-lru
tcp-keepalive 300
timeout 0
databases 1
