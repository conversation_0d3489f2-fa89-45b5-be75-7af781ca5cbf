#!/bin/bash

# Redis Cluster 状态检查脚本
# 作者: Redis Cluster Manager
# 版本: 1.0

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置参数
REDIS_CLI="redis-cli"
BASE_PORT=7100
NODE_COUNT=6
REDIS_PASSWORD="redis123456"  # Redis集群密码

# 检查节点状态
check_nodes() {
    echo -e "${BLUE}检查Redis节点状态...${NC}"
    echo ""
    
    running_nodes=0
    
    for ((i=0; i<$NODE_COUNT; i++)); do
        port=$((BASE_PORT + i))
        
        if $REDIS_CLI -a $REDIS_PASSWORD -p $port ping > /dev/null 2>&1; then
            echo -e "${GREEN}✓ 节点 127.0.0.1:$port 运行正常${NC}"
            running_nodes=$((running_nodes + 1))
        else
            echo -e "${RED}✗ 节点 127.0.0.1:$port 未运行${NC}"
        fi
    done
    
    echo ""
    echo -e "${BLUE}运行中的节点: $running_nodes/$NODE_COUNT${NC}"
    
    if [ $running_nodes -eq $NODE_COUNT ]; then
        echo -e "${GREEN}所有节点运行正常${NC}"
        return 0
    else
        echo -e "${YELLOW}部分节点未运行${NC}"
        return 1
    fi
}

# 检查集群状态
check_cluster() {
    echo -e "${BLUE}检查集群状态...${NC}"
    echo ""
    
    # 检查第一个节点是否可用
    if ! $REDIS_CLI -a $REDIS_PASSWORD -p $BASE_PORT ping > /dev/null 2>&1; then
        echo -e "${RED}无法连接到主节点 127.0.0.1:$BASE_PORT${NC}"
        return 1
    fi
    
    # 获取集群信息
    cluster_state=$($REDIS_CLI -a $REDIS_PASSWORD -p $BASE_PORT cluster info | grep cluster_state | cut -d: -f2 | tr -d '\r')
    cluster_slots_assigned=$($REDIS_CLI -a $REDIS_PASSWORD -p $BASE_PORT cluster info | grep cluster_slots_assigned | cut -d: -f2 | tr -d '\r')
    cluster_slots_ok=$($REDIS_CLI -a $REDIS_PASSWORD -p $BASE_PORT cluster info | grep cluster_slots_ok | cut -d: -f2 | tr -d '\r')
    cluster_known_nodes=$($REDIS_CLI -a $REDIS_PASSWORD -p $BASE_PORT cluster info | grep cluster_known_nodes | cut -d: -f2 | tr -d '\r')
    
    echo -e "${BLUE}集群状态: ${NC}$cluster_state"
    echo -e "${BLUE}已分配槽位: ${NC}$cluster_slots_assigned"
    echo -e "${BLUE}正常槽位: ${NC}$cluster_slots_ok"
    echo -e "${BLUE}已知节点: ${NC}$cluster_known_nodes"
    
    if [ "$cluster_state" = "ok" ]; then
        echo -e "${GREEN}✓ 集群状态正常${NC}"
    else
        echo -e "${RED}✗ 集群状态异常${NC}"
    fi
    
    echo ""
}

# 显示集群节点详情
show_cluster_nodes() {
    echo -e "${BLUE}集群节点详情:${NC}"
    echo ""
    
    if $REDIS_CLI -a $REDIS_PASSWORD -p $BASE_PORT ping > /dev/null 2>&1; then
        $REDIS_CLI -a $REDIS_PASSWORD -p $BASE_PORT cluster nodes | while read line; do
            node_id=$(echo $line | awk '{print $1}')
            node_addr=$(echo $line | awk '{print $2}')
            node_flags=$(echo $line | awk '{print $3}')
            node_master=$(echo $line | awk '{print $4}')
            node_slots=$(echo $line | awk '{print $9}')
            
            if [[ $node_flags == *"master"* ]]; then
                echo -e "${GREEN}主节点: $node_addr ($node_id)${NC}"
                if [ "$node_slots" != "" ]; then
                    echo -e "  槽位: $node_slots"
                fi
            elif [[ $node_flags == *"slave"* ]]; then
                echo -e "${YELLOW}从节点: $node_addr ($node_id)${NC}"
                echo -e "  主节点: $node_master"
            fi
            echo ""
        done
    else
        echo -e "${RED}无法获取集群节点信息${NC}"
    fi
}

# 检查内存使用情况
check_memory() {
    echo -e "${BLUE}内存使用情况:${NC}"
    echo ""
    
    for ((i=0; i<$NODE_COUNT; i++)); do
        port=$((BASE_PORT + i))
        
        if $REDIS_CLI -a $REDIS_PASSWORD -p $port ping > /dev/null 2>&1; then
            used_memory=$($REDIS_CLI -a $REDIS_PASSWORD -p $port info memory | grep used_memory_human | cut -d: -f2 | tr -d '\r')
            max_memory=$($REDIS_CLI -a $REDIS_PASSWORD -p $port config get maxmemory | tail -1)
            
            if [ "$max_memory" = "0" ]; then
                max_memory="无限制"
            else
                max_memory=$(echo "scale=2; $max_memory / 1024 / 1024" | bc)"MB"
            fi
            
            echo -e "${BLUE}节点 127.0.0.1:$port:${NC} 已使用 $used_memory / 最大 $max_memory"
        fi
    done
    echo ""
}

# 测试集群功能
test_cluster() {
    echo -e "${BLUE}测试集群功能...${NC}"
    echo ""
    
    if ! $REDIS_CLI -a $REDIS_PASSWORD -p $BASE_PORT ping > /dev/null 2>&1; then
        echo -e "${RED}无法连接到集群${NC}"
        return 1
    fi
    
    # 测试写入
    test_key="cluster_test_$(date +%s)"
    test_value="test_value_$(date +%s)"
    
    echo -e "${YELLOW}测试写入数据...${NC}"
    if $REDIS_CLI -a $REDIS_PASSWORD -c -p $BASE_PORT set $test_key $test_value > /dev/null 2>&1; then
        echo -e "${GREEN}✓ 写入测试成功${NC}"
        
        # 测试读取
        echo -e "${YELLOW}测试读取数据...${NC}"
        read_value=$($REDIS_CLI -a $REDIS_PASSWORD -c -p $BASE_PORT get $test_key 2>/dev/null)
        
        if [ "$read_value" = "$test_value" ]; then
            echo -e "${GREEN}✓ 读取测试成功${NC}"
            
            # 清理测试数据
            $REDIS_CLI -a $REDIS_PASSWORD -c -p $BASE_PORT del $test_key > /dev/null 2>&1
            echo -e "${GREEN}✓ 集群功能正常${NC}"
        else
            echo -e "${RED}✗ 读取测试失败${NC}"
        fi
    else
        echo -e "${RED}✗ 写入测试失败${NC}"
    fi
    
    echo ""
}

# 显示连接信息
show_connection_info() {
    echo -e "${BLUE}连接信息:${NC}"
    echo ""
    echo -e "${YELLOW}集群连接命令:${NC}"
    echo "redis-cli -c -a $REDIS_PASSWORD -p $BASE_PORT"
    echo ""
    echo -e "${YELLOW}各节点端口:${NC}"
    for ((i=0; i<$NODE_COUNT; i++)); do
        port=$((BASE_PORT + i))
        echo "127.0.0.1:$port"
    done
    echo ""
}

# 主函数
main() {
    echo -e "${GREEN}========================================${NC}"
    echo -e "${GREEN}    Redis Cluster 状态检查${NC}"
    echo -e "${GREEN}========================================${NC}"
    echo ""
    
    # 检查节点状态
    if check_nodes; then
        # 检查集群状态
        check_cluster
        
        # 显示节点详情
        show_cluster_nodes
        
        # 检查内存使用
        check_memory
        
        # 测试集群功能
        test_cluster
        
        # 显示连接信息
        show_connection_info
    else
        echo -e "${RED}部分节点未运行，请检查集群配置${NC}"
    fi
    
    echo -e "${GREEN}========================================${NC}"
    echo -e "${GREEN}状态检查完成${NC}"
    echo -e "${GREEN}========================================${NC}"
    echo ""
}

# 执行主函数
main 