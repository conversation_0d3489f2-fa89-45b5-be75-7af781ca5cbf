20747:C 09 Jun 2025 23:30:41.055 * oO0OoO0OoO0Oo Redis is starting oO0OoO0OoO0Oo
20747:C 09 Jun 2025 23:30:41.055 * Redis version=7.2.7, bits=64, commit=00000000, modified=0, pid=20747, just started
20747:C 09 Jun 2025 23:30:41.055 * Configuration loaded
20747:M 09 Jun 2025 23:30:41.056 * Increased maximum number of open files to 10032 (it was originally set to 256).
20747:M 09 Jun 2025 23:30:41.056 * monotonic clock: POSIX clock_gettime
20747:M 09 Jun 2025 23:30:41.056 * Running mode=cluster, port=7102.
20747:M 09 Jun 2025 23:30:41.056 # WARNING: The TCP backlog setting of 511 cannot be enforced because kern.ipc.somaxconn is set to the lower value of 128.
20747:M 09 Jun 2025 23:30:41.057 * Node configuration loaded, I'm 64b368be9cca12e935dbfa0a35735395a1c3eafe
20747:M 09 Jun 2025 23:30:41.057 * Server initialized
20747:M 09 Jun 2025 23:30:41.057 * Reading RDB base file on AOF loading...
20747:M 09 Jun 2025 23:30:41.057 * Loading RDB produced by version 7.2.7
20747:M 09 Jun 2025 23:30:41.057 * RDB age 32170 seconds
20747:M 09 Jun 2025 23:30:41.057 * RDB memory usage when created 1.70 Mb
20747:M 09 Jun 2025 23:30:41.057 * RDB is base AOF
20747:M 09 Jun 2025 23:30:41.057 * Done loading RDB, keys loaded: 0, keys expired: 0.
20747:M 09 Jun 2025 23:30:41.058 * DB loaded from base file appendonly-7102.aof.1.base.rdb: 0.001 seconds
20747:M 09 Jun 2025 23:30:41.064 * DB saved on disk
20747:M 09 Jun 2025 23:30:41.068 * DB saved on disk
20747:M 09 Jun 2025 23:30:41.073 * DB saved on disk
20747:M 09 Jun 2025 23:30:41.079 * DB saved on disk
20747:M 09 Jun 2025 23:30:41.084 * DB saved on disk
20747:M 09 Jun 2025 23:30:41.088 * DB saved on disk
20747:M 09 Jun 2025 23:30:41.093 * DB saved on disk
20747:M 09 Jun 2025 23:30:41.099 * DB saved on disk
20747:M 09 Jun 2025 23:30:41.104 * DB saved on disk
20747:M 09 Jun 2025 23:30:41.108 * DB saved on disk
20747:M 09 Jun 2025 23:30:41.108 * DB loaded from incr file appendonly-7102.aof.1.incr.aof: 0.051 seconds
20747:M 09 Jun 2025 23:30:41.109 * DB loaded from append only file: 0.052 seconds
20747:M 09 Jun 2025 23:30:41.109 * Opening AOF incr file appendonly-7102.aof.1.incr.aof on server start
20747:M 09 Jun 2025 23:30:41.109 * Ready to accept connections tcp
20747:M 09 Jun 2025 23:30:52.274 * DB saved on disk
20747:M 09 Jun 2025 23:30:52.279 * configEpoch set to 0 via CLUSTER RESET HARD
20747:M 09 Jun 2025 23:30:52.279 * Node hard reset, now I'm d8d9d9bbbea438dc779b216b617fffe1b7ad725e
20747:M 09 Jun 2025 23:30:55.418 * configEpoch set to 3 via CLUSTER SET-CONFIG-EPOCH
20747:M 09 Jun 2025 23:30:57.444 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 09 Jun 2025 23:30:57.444 * Partial resynchronization not accepted: Replication ID mismatch (Replica asked for 'f56ed29f7a756487acf1026b1b3992762ee5a1da', my replication IDs are '5cc994317af19ca2f747682c8c0ed50cbc2fa5fc' and '0000000000000000000000000000000000000000')
20747:M 09 Jun 2025 23:30:57.444 * Replication backlog created, my new replication IDs are '78a2db116a2b651f4af4cc43816743021d99f466' and '0000000000000000000000000000000000000000'
20747:M 09 Jun 2025 23:30:57.444 * Delay next BGSAVE for diskless SYNC
20747:M 09 Jun 2025 23:31:00.489 * Cluster state changed: ok
20747:M 09 Jun 2025 23:31:02.312 * Starting BGSAVE for SYNC with target: replicas sockets
20747:M 09 Jun 2025 23:31:02.316 * Background RDB transfer started by pid 20937
20937:C 09 Jun 2025 23:31:02.320 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20747:M 09 Jun 2025 23:31:02.322 * Diskless rdb transfer, done reading from pipe, 1 replicas still up.
20747:M 09 Jun 2025 23:31:02.341 * Background RDB transfer terminated with success
20747:M 09 Jun 2025 23:31:02.342 * Streamed RDB transfer with replica 127.0.0.1:7105 succeeded (socket). Waiting for REPLCONF ACK from replica to enable streaming
20747:M 09 Jun 2025 23:31:02.342 * Synchronization with replica 127.0.0.1:7105 succeeded
20747:M 10 Jun 2025 00:30:53.083 * 1 changes in 3600 seconds. Saving...
20747:M 10 Jun 2025 00:30:53.087 * Background saving started by pid 44543
44543:C 10 Jun 2025 00:30:53.126 * DB saved on disk
44543:C 10 Jun 2025 00:30:53.135 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20747:M 10 Jun 2025 00:30:53.190 * Background saving terminated with success
20747:M 10 Jun 2025 01:21:57.908 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 01:21:57.908 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 01:21:57.912 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 01:21:57.912 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 9711.
20747:M 10 Jun 2025 01:38:10.647 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 01:38:10.669 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 01:38:10.689 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 01:38:10.690 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 9781.
20747:M 10 Jun 2025 01:54:23.461 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 01:54:23.573 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 01:54:23.576 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 01:54:23.577 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 9837.
20747:M 10 Jun 2025 02:10:33.515 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 02:10:33.549 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 02:10:33.567 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 02:10:33.567 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 9907.
20747:M 10 Jun 2025 02:26:21.658 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 02:26:21.658 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 02:26:21.661 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 02:26:21.662 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 9963.
20747:M 10 Jun 2025 02:58:34.763 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 02:58:34.764 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 02:58:34.769 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 02:58:34.769 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 10143.
20747:M 10 Jun 2025 03:07:27.185 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 03:07:27.186 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 03:07:27.190 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 03:07:27.191 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 10199.
20747:M 10 Jun 2025 03:23:39.802 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 03:23:39.802 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 03:23:39.805 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 03:23:39.806 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 10269.
20747:M 10 Jun 2025 03:39:48.919 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 03:39:48.920 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 03:39:48.930 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 03:39:48.931 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 10325.
20747:M 10 Jun 2025 03:56:01.705 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 03:56:01.834 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 03:56:01.840 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 03:56:01.840 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 10395.
20747:M 10 Jun 2025 04:12:14.393 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 04:12:14.517 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 04:12:14.523 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 04:12:14.523 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 10465.
20747:M 10 Jun 2025 04:28:27.232 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 04:28:27.369 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 04:28:27.382 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 04:28:27.382 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 10521.
20747:M 10 Jun 2025 04:44:40.079 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 04:44:40.199 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 04:44:40.204 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 04:44:40.205 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 10591.
20747:M 10 Jun 2025 05:00:53.925 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 05:00:53.925 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 05:00:53.932 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 05:00:53.932 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 10647.
20747:M 10 Jun 2025 05:17:06.720 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 05:17:06.751 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 05:17:06.771 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 05:17:06.771 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 10717.
20747:M 10 Jun 2025 05:33:19.551 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 05:33:19.554 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 05:33:19.556 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 05:33:19.556 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 10787.
20747:M 10 Jun 2025 05:49:32.222 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 05:49:32.236 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 05:49:32.237 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 05:49:32.237 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 10843.
20747:M 10 Jun 2025 06:05:44.828 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 06:05:44.948 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 06:05:44.952 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 06:05:44.952 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 10913.
20747:M 10 Jun 2025 06:21:30.448 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 06:21:30.464 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 06:21:30.480 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 06:21:30.480 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 10969.
20747:M 10 Jun 2025 06:37:41.187 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 06:37:41.187 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 06:37:41.205 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 06:37:41.205 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 11039.
20747:M 10 Jun 2025 06:53:27.818 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 06:53:27.818 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 06:53:27.821 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 06:53:27.821 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 11095.
20747:M 10 Jun 2025 06:59:15.680 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 06:59:15.680 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 06:59:15.683 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 06:59:15.683 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 11165.
20747:M 10 Jun 2025 07:15:02.579 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 07:15:02.579 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 07:15:02.586 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 07:15:02.586 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 11235.
20747:M 10 Jun 2025 07:31:11.121 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 07:31:11.122 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 07:31:11.132 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 07:31:11.132 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 11291.
20747:M 10 Jun 2025 07:35:19.796 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 07:35:19.796 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 07:35:19.801 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 07:35:19.801 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 11347.
20747:M 10 Jun 2025 07:51:04.697 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 07:51:04.697 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 07:51:04.702 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 07:51:04.702 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 11417.
20747:M 10 Jun 2025 08:07:17.298 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 08:07:17.298 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 08:07:17.302 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 08:07:17.302 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 11487.
20747:M 10 Jun 2025 08:23:29.171 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 08:23:29.184 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 08:23:29.214 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 08:23:29.214 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 11543.
20747:M 10 Jun 2025 08:39:41.990 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 08:39:42.031 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 08:39:42.054 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 08:39:42.054 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 11613.
20747:M 10 Jun 2025 08:47:24.594 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 08:47:24.630 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 08:47:24.648 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 08:47:24.648 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 11669.
20747:M 10 Jun 2025 09:00:15.609 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 09:00:15.609 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 09:00:15.613 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 09:00:15.613 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 11739.
20747:M 10 Jun 2025 09:19:24.989 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 09:19:24.990 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 09:19:24.996 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 09:19:24.996 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 11935.
20747:M 10 Jun 2025 09:36:43.880 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 09:36:43.880 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 09:36:43.884 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 09:36:43.884 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 11949.
20747:M 10 Jun 2025 09:50:32.952 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 09:50:32.953 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 09:50:32.958 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 09:50:32.958 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 11949.
20747:M 10 Jun 2025 09:54:12.469 * 1 changes in 3600 seconds. Saving...
20747:M 10 Jun 2025 09:54:12.470 * Background saving started by pid 71760
71760:C 10 Jun 2025 09:54:12.479 * DB saved on disk
71760:C 10 Jun 2025 09:54:12.480 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20747:M 10 Jun 2025 09:54:12.570 * Background saving terminated with success
20747:M 10 Jun 2025 10:19:54.437 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 10:19:54.439 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 10:19:54.439 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 15615.
20747:M 10 Jun 2025 11:33:00.116 * 1 changes in 3600 seconds. Saving...
20747:M 10 Jun 2025 11:33:00.117 * Background saving started by pid 9206
9206:C 10 Jun 2025 11:33:00.128 * DB saved on disk
9206:C 10 Jun 2025 11:33:00.129 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20747:M 10 Jun 2025 11:33:00.223 * Background saving terminated with success
20747:M 10 Jun 2025 13:02:22.679 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 13:02:22.680 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 13:02:22.680 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 30621.
20747:M 10 Jun 2025 13:04:39.760 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 13:04:39.780 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 13:04:39.780 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 30677.
20747:M 10 Jun 2025 13:08:41.744 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 13:08:41.746 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 13:08:41.746 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 30747.
20747:M 10 Jun 2025 13:24:52.408 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 13:24:52.410 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 13:24:52.410 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 30803.
20747:M 10 Jun 2025 17:10:09.235 * 1 changes in 3600 seconds. Saving...
20747:M 10 Jun 2025 17:10:09.236 * Background saving started by pid 36451
36451:C 10 Jun 2025 17:10:09.247 * DB saved on disk
36451:C 10 Jun 2025 17:10:09.248 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20747:M 10 Jun 2025 17:10:09.338 * Background saving terminated with success
20747:M 10 Jun 2025 18:47:06.966 * 1 changes in 3600 seconds. Saving...
20747:M 10 Jun 2025 18:47:06.973 * Background saving started by pid 89342
89342:C 10 Jun 2025 18:47:06.990 * DB saved on disk
89342:C 10 Jun 2025 18:47:06.991 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20747:M 10 Jun 2025 18:47:07.076 * Background saving terminated with success
20747:M 10 Jun 2025 21:29:00.809 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 21:29:00.809 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 21:29:00.816 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 21:29:00.817 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 72792.
20747:M 10 Jun 2025 21:47:27.509 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 21:47:27.509 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 21:47:27.512 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 21:47:27.512 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 72848.
20747:M 10 Jun 2025 22:04:13.407 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 22:04:13.430 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 22:04:13.438 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 22:04:13.438 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 72862.
20747:M 10 Jun 2025 22:21:29.301 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 22:21:29.420 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 22:21:29.423 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 22:21:29.423 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 72862.
20747:M 10 Jun 2025 22:36:31.729 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 22:36:31.848 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 22:36:31.851 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 22:36:31.851 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 72876.
20747:M 10 Jun 2025 22:52:26.692 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 10 Jun 2025 22:52:26.801 * Connection with replica 127.0.0.1:7105 lost.
20747:M 10 Jun 2025 22:52:26.803 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 10 Jun 2025 22:52:26.803 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 72932.
20747:M 10 Jun 2025 23:04:14.003 * 1 changes in 3600 seconds. Saving...
20747:M 10 Jun 2025 23:04:14.004 * Background saving started by pid 81132
81132:C 10 Jun 2025 23:04:14.018 * DB saved on disk
81132:C 10 Jun 2025 23:04:14.019 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20747:M 10 Jun 2025 23:04:14.105 * Background saving terminated with success
20747:M 11 Jun 2025 00:52:02.531 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 00:52:02.534 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 00:52:02.535 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 83178.
20747:M 11 Jun 2025 01:08:10.963 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 01:08:10.963 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 01:08:10.967 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 01:08:10.968 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 83234.
20747:M 11 Jun 2025 01:24:23.550 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 01:24:23.551 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 01:24:23.561 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 01:24:23.572 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 83304.
20747:M 11 Jun 2025 01:40:36.275 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 01:40:36.275 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 01:40:36.278 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 01:40:36.278 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 83360.
20747:M 11 Jun 2025 01:56:49.772 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 01:56:49.772 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 01:56:49.774 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 01:56:49.774 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 83430.
20747:M 11 Jun 2025 02:29:14.971 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 02:29:14.971 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 02:29:14.973 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 02:29:14.973 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 83596.
20747:M 11 Jun 2025 02:35:35.235 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 02:35:35.248 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 02:35:35.251 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 02:35:35.251 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 83666.
20747:M 11 Jun 2025 02:51:47.975 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 02:51:48.088 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 02:51:48.097 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 02:51:48.097 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 83722.
20747:M 11 Jun 2025 03:08:00.853 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 03:08:00.972 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 03:08:00.976 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 03:08:00.976 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 83792.
20747:M 11 Jun 2025 03:24:13.738 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 03:24:13.871 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 03:24:13.877 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 03:24:13.877 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 83862.
20747:M 11 Jun 2025 03:40:25.717 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 03:40:25.857 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 03:40:25.862 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 03:40:25.862 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 83918.
20747:M 11 Jun 2025 03:56:19.353 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 03:56:19.492 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 03:56:19.496 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 03:56:19.496 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 83988.
20747:M 11 Jun 2025 04:12:29.104 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 04:12:29.105 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 04:12:29.109 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 04:12:29.109 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 84044.
20747:M 11 Jun 2025 04:28:40.994 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 04:28:41.120 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 04:28:41.124 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 04:28:41.124 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 84114.
20747:M 11 Jun 2025 04:44:53.812 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 04:44:53.941 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 04:44:53.946 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 04:44:53.946 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 84170.
20747:M 11 Jun 2025 05:01:06.643 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 05:01:06.781 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 05:01:06.785 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 05:01:06.785 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 84240.
20747:M 11 Jun 2025 05:17:18.736 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 05:17:18.753 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 05:17:18.772 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 05:17:18.772 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 84296.
20747:M 11 Jun 2025 05:33:31.418 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 05:33:31.560 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 05:33:31.564 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 05:33:31.564 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 84366.
20747:M 11 Jun 2025 05:49:43.405 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 05:49:43.526 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 05:49:43.529 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 05:49:43.529 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 84436.
20747:M 11 Jun 2025 06:05:56.204 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 06:05:56.338 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 06:05:56.341 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 06:05:56.341 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 84492.
20747:M 11 Jun 2025 06:22:08.900 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 06:22:09.017 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 06:22:09.020 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 06:22:09.021 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 84562.
20747:M 11 Jun 2025 06:31:42.102 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 06:31:42.105 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 06:31:42.105 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 84632.
20747:M 11 Jun 2025 06:47:44.971 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 06:47:44.971 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 06:47:44.979 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 06:47:44.980 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 84688.
20747:M 11 Jun 2025 07:03:56.892 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 07:03:56.892 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 07:03:56.895 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 07:03:56.895 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 84758.
20747:M 11 Jun 2025 07:19:48.602 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 07:19:48.603 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 07:19:48.606 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 07:19:48.607 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 84814.
20747:M 11 Jun 2025 07:36:02.331 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 07:36:02.331 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 07:36:02.337 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 07:36:02.337 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 84884.
20747:M 11 Jun 2025 07:52:14.954 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 07:52:14.956 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 07:52:14.956 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 84954.
20747:M 11 Jun 2025 08:08:27.644 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 08:08:27.644 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 08:08:27.646 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 08:08:27.647 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 85010.
20747:M 11 Jun 2025 08:14:22.456 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 08:14:22.456 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 08:14:22.460 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 08:14:22.460 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 85080.
20747:M 11 Jun 2025 08:30:34.302 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 08:30:34.329 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 08:30:34.331 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 08:30:34.332 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 85136.
20747:M 11 Jun 2025 08:32:41.288 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 08:32:41.408 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 08:32:41.411 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 08:32:41.411 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 85206.
20747:M 11 Jun 2025 08:36:30.684 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 08:36:30.809 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 08:36:30.811 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 08:36:30.811 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 85276.
20747:M 11 Jun 2025 08:52:45.474 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 08:52:45.590 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 08:52:45.594 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 08:52:45.594 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 85332.
20747:M 11 Jun 2025 09:05:22.550 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 09:05:22.682 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 09:05:22.685 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 09:05:22.685 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 85402.
20747:M 11 Jun 2025 09:23:32.450 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 09:23:32.483 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 09:23:32.505 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 09:23:32.505 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 85458.
20747:M 11 Jun 2025 09:33:41.467 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 09:33:41.485 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 09:33:41.495 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 09:33:41.495 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 85472.
20747:M 11 Jun 2025 09:52:07.578 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 09:52:07.578 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 09:52:07.581 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 09:52:07.581 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 85542.
20747:M 11 Jun 2025 10:06:46.654 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 10:06:46.656 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 10:06:46.656 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 85976.
20747:M 11 Jun 2025 10:27:05.131 * 1 changes in 3600 seconds. Saving...
20747:M 11 Jun 2025 10:27:05.134 * Background saving started by pid 39461
39461:C 11 Jun 2025 10:27:05.156 * DB saved on disk
39461:C 11 Jun 2025 10:27:05.156 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20747:M 11 Jun 2025 10:27:05.238 * Background saving terminated with success
20747:M 11 Jun 2025 11:06:59.001 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 11:06:59.001 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 11:06:59.005 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 11:06:59.005 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 92610.
20747:M 11 Jun 2025 11:10:11.904 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 11:10:12.013 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 11:10:12.016 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 11:10:12.017 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 92680.
20747:M 11 Jun 2025 13:12:23.149 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 13:12:23.151 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 13:12:23.151 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 102102.
20747:M 11 Jun 2025 13:29:45.395 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 13:29:45.400 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 13:29:45.406 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 13:29:45.406 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 40 bytes of backlog starting from offset 102172.
20747:M 11 Jun 2025 13:46:06.425 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 13:46:06.441 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 13:46:06.455 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 13:46:06.455 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 102268.
20747:M 11 Jun 2025 14:01:53.439 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 14:01:53.565 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 14:01:53.569 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 14:01:53.569 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 102338.
20747:M 11 Jun 2025 14:33:47.500 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 14:33:47.500 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 14:33:47.509 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 14:33:47.509 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 104494.
20747:M 11 Jun 2025 15:04:34.590 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 15:04:34.591 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 15:04:34.595 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 15:04:34.595 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 106622.
20747:M 11 Jun 2025 15:51:10.622 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 15:51:10.623 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 15:51:10.626 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 15:51:10.627 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 108974.
20747:M 11 Jun 2025 15:59:57.785 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 15:59:57.797 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 15:59:57.797 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 109114.
20747:M 11 Jun 2025 16:04:02.836 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 16:04:02.843 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 16:04:02.870 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 16:04:02.870 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 109128.
20747:M 11 Jun 2025 16:13:05.117 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 16:13:05.117 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 16:13:05.119 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 16:13:05.119 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 109198.
20747:M 11 Jun 2025 16:16:19.228 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 16:16:19.240 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 16:16:19.240 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 109240.
20747:M 11 Jun 2025 16:27:22.202 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 16:27:22.321 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 16:27:22.323 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 16:27:22.323 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 109296.
20747:M 11 Jun 2025 16:48:11.094 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 16:48:11.094 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 16:48:11.103 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 16:48:11.104 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 109744.
20747:M 11 Jun 2025 17:04:03.008 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 17:04:03.009 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 17:04:03.018 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 17:04:03.018 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 109744.
20747:M 11 Jun 2025 17:05:14.177 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 17:05:14.177 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 17:05:14.183 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 17:05:14.183 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 109758.
20747:M 11 Jun 2025 17:43:59.380 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 17:43:59.380 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 17:43:59.383 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 17:43:59.384 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 111802.
20747:M 11 Jun 2025 17:45:57.388 * 1 changes in 3600 seconds. Saving...
20747:M 11 Jun 2025 17:45:57.390 * Background saving started by pid 46169
46169:C 11 Jun 2025 17:45:57.397 * DB saved on disk
46169:C 11 Jun 2025 17:45:57.398 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20747:M 11 Jun 2025 17:45:57.491 * Background saving terminated with success
20747:M 11 Jun 2025 19:43:00.267 * 1 changes in 3600 seconds. Saving...
20747:M 11 Jun 2025 19:43:00.269 * Background saving started by pid 2193
2193:C 11 Jun 2025 19:43:00.277 * DB saved on disk
2193:C 11 Jun 2025 19:43:00.277 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20747:M 11 Jun 2025 19:43:00.370 * Background saving terminated with success
20747:M 11 Jun 2025 20:43:01.098 * 1 changes in 3600 seconds. Saving...
20747:M 11 Jun 2025 20:43:01.099 * Background saving started by pid 40962
40962:C 11 Jun 2025 20:43:01.109 * DB saved on disk
40962:C 11 Jun 2025 20:43:01.110 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20747:M 11 Jun 2025 20:43:01.200 * Background saving terminated with success
20747:M 11 Jun 2025 22:14:51.568 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 22:14:51.570 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 22:14:51.570 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 140011.
20747:M 11 Jun 2025 22:31:53.703 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 22:31:53.703 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 22:31:53.707 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 22:31:53.708 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 140067.
20747:M 11 Jun 2025 22:47:38.596 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 22:47:38.596 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 22:47:38.598 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 22:47:38.598 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 140166.
20747:M 11 Jun 2025 23:03:11.513 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 23:03:11.543 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 23:03:11.554 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 23:03:11.554 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 140206.
20747:M 11 Jun 2025 23:19:19.523 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 23:19:19.550 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 23:19:19.573 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 23:19:19.573 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 140220.
20747:M 11 Jun 2025 23:26:40.063 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 11 Jun 2025 23:26:40.083 * Connection with replica 127.0.0.1:7105 lost.
20747:M 11 Jun 2025 23:26:40.100 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 11 Jun 2025 23:26:40.101 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 140220.
20747:M 11 Jun 2025 23:37:42.005 * 1 changes in 3600 seconds. Saving...
20747:M 11 Jun 2025 23:37:42.014 * Background saving started by pid 81905
81905:C 11 Jun 2025 23:37:42.024 * DB saved on disk
81905:C 11 Jun 2025 23:37:42.025 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20747:M 11 Jun 2025 23:37:42.117 * Background saving terminated with success
20747:M 12 Jun 2025 00:15:33.715 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 00:15:33.715 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 00:15:33.720 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 00:15:33.720 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 145762.
20747:M 12 Jun 2025 01:00:14.691 * 1 changes in 3600 seconds. Saving...
20747:M 12 Jun 2025 01:00:14.693 * Background saving started by pid 18757
18757:C 12 Jun 2025 01:00:14.699 * DB saved on disk
18757:C 12 Jun 2025 01:00:14.699 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20747:M 12 Jun 2025 01:00:14.794 * Background saving terminated with success
20747:M 12 Jun 2025 03:02:38.527 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 03:02:38.545 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 03:02:38.548 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 03:02:38.549 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 158495.
20747:M 12 Jun 2025 03:18:46.832 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 03:18:46.834 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 03:18:46.834 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 158551.
20747:M 12 Jun 2025 03:34:59.597 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 03:34:59.602 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 03:34:59.602 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 158621.
20747:M 12 Jun 2025 03:51:12.395 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 03:51:12.396 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 03:51:12.396 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 158677.
20747:M 12 Jun 2025 04:07:25.078 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 04:07:25.080 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 04:07:25.080 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 158747.
20747:M 12 Jun 2025 04:23:37.887 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 04:23:37.889 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 04:23:37.889 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 158817.
20747:M 12 Jun 2025 04:39:50.048 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 04:39:50.158 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 04:39:50.170 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 04:39:50.170 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 158873.
20747:M 12 Jun 2025 04:44:35.405 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 04:44:35.541 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 04:44:35.543 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 04:44:35.543 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 158943.
20747:M 12 Jun 2025 05:00:48.273 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 05:00:48.284 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 05:00:48.305 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 05:00:48.305 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 158999.
20747:M 12 Jun 2025 05:17:00.906 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 05:17:01.051 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 05:17:01.053 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 05:17:01.053 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 159069.
20747:M 12 Jun 2025 05:33:13.716 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 05:33:13.833 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 05:33:13.836 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 05:33:13.836 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 159139.
20747:M 12 Jun 2025 05:45:35.314 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 05:45:35.450 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 05:45:35.452 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 05:45:35.453 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 159195.
20747:M 12 Jun 2025 06:01:49.109 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 06:01:49.109 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 06:01:49.129 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 06:01:49.129 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 159265.
20747:M 12 Jun 2025 06:17:36.939 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 06:17:36.939 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 06:17:36.944 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 06:17:36.945 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 159321.
20747:M 12 Jun 2025 06:33:44.734 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 06:33:44.735 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 06:33:44.735 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 159391.
20747:M 12 Jun 2025 06:46:35.440 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 06:46:35.442 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 06:46:35.443 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 159447.
20747:M 12 Jun 2025 07:02:48.141 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 07:02:48.143 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 07:02:48.143 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 159517.
20747:M 12 Jun 2025 07:19:00.985 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 07:19:00.987 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 07:19:00.987 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 159573.
20747:M 12 Jun 2025 07:35:13.815 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 07:35:13.817 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 07:35:13.817 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 159643.
20747:M 12 Jun 2025 07:47:35.462 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 07:47:35.464 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 07:47:35.464 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 159713.
20747:M 12 Jun 2025 08:03:48.267 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 08:03:48.300 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 08:03:48.309 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 08:03:48.322 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 159769.
20747:M 12 Jun 2025 08:20:00.866 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 08:20:00.986 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 08:20:00.988 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 08:20:00.988 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 159839.
20747:M 12 Jun 2025 08:36:14.539 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 08:36:14.541 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 08:36:14.541 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 159909.
20747:M 12 Jun 2025 08:48:35.467 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 08:48:35.469 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 08:48:35.469 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 159965.
20747:M 12 Jun 2025 08:53:25.164 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 08:53:25.170 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 08:53:25.170 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 160035.
20747:M 12 Jun 2025 08:55:09.700 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 08:55:09.703 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 08:55:09.703 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 160091.
20747:M 12 Jun 2025 09:00:52.665 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 09:00:52.668 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 09:00:52.668 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 160175.
20747:M 12 Jun 2025 09:17:08.882 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 09:17:08.885 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 09:17:08.885 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 160245.
20747:M 12 Jun 2025 09:34:27.785 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 09:34:27.788 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 09:34:27.788 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 160245.
20747:M 12 Jun 2025 09:50:02.686 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 09:50:02.689 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 09:50:02.689 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 160259.
20747:M 12 Jun 2025 09:55:37.478 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 09:55:37.479 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 09:55:37.479 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 160259.
20747:M 12 Jun 2025 10:15:15.701 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 10:15:15.702 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 10:15:15.718 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 10:15:15.719 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 161491.
20747:M 12 Jun 2025 10:39:14.334 * 1 changes in 3600 seconds. Saving...
20747:M 12 Jun 2025 10:39:14.335 * Background saving started by pid 92049
92049:C 12 Jun 2025 10:39:14.342 * DB saved on disk
92049:C 12 Jun 2025 10:39:14.343 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20747:M 12 Jun 2025 10:39:14.436 * Background saving terminated with success
20747:M 12 Jun 2025 11:30:02.776 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 11:30:02.776 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 11:30:02.780 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 11:30:02.780 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 169399.
20747:M 12 Jun 2025 11:36:08.598 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 11:36:08.598 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 11:36:08.601 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 11:36:08.601 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 169469.
20747:M 12 Jun 2025 11:44:28.532 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 11:44:28.533 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 11:44:28.534 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 11:44:28.534 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 169525.
20747:M 12 Jun 2025 13:00:17.659 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 13:00:17.659 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 13:00:17.663 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 13:00:17.663 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 174509.
20747:M 12 Jun 2025 13:17:09.712 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 13:17:09.712 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 13:17:09.715 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 13:17:09.715 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 174593.
20747:M 12 Jun 2025 13:34:30.530 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 13:34:30.531 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 13:34:30.532 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 13:34:30.532 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 174649.
20747:M 12 Jun 2025 13:45:52.054 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 13:45:52.067 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 13:45:52.086 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 13:45:52.086 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 174759.
20747:M 12 Jun 2025 14:02:20.071 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 14:02:20.201 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 14:02:20.205 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 14:02:20.205 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 174815.
20747:M 12 Jun 2025 14:07:05.963 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 14:07:05.963 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 14:07:05.969 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 14:07:05.969 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 174913.
20747:M 12 Jun 2025 15:13:52.586 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 15:13:52.589 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 15:13:52.589 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 180275.
20747:M 12 Jun 2025 15:16:15.651 * 1 changes in 3600 seconds. Saving...
20747:M 12 Jun 2025 15:16:15.652 * Background saving started by pid 7178
7178:C 12 Jun 2025 15:16:15.659 * DB saved on disk
7178:C 12 Jun 2025 15:16:15.660 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20747:M 12 Jun 2025 15:16:15.754 * Background saving terminated with success
20747:M 12 Jun 2025 18:46:38.343 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 18:46:38.343 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 18:46:38.346 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 18:46:38.346 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 199647.
20747:M 12 Jun 2025 19:02:37.685 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 19:02:37.685 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 19:02:37.689 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 19:02:37.689 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 199703.
20747:M 12 Jun 2025 19:17:52.582 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 19:17:52.583 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 19:17:52.585 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 19:17:52.586 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 199717.
20747:M 12 Jun 2025 19:35:47.480 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 19:35:47.481 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 19:35:47.484 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 19:35:47.485 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 199717.
20747:M 12 Jun 2025 19:52:39.356 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 19:52:39.397 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 19:52:39.406 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 19:52:39.406 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 199731.
20747:M 12 Jun 2025 20:09:46.392 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 20:09:46.392 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 20:09:46.397 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 20:09:46.397 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 199731.
20747:M 12 Jun 2025 20:24:52.357 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 20:24:52.369 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 20:24:52.386 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 20:24:52.386 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 199745.
20747:M 12 Jun 2025 20:42:32.244 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 20:42:32.378 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 20:42:32.380 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 20:42:32.380 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 199745.
20747:M 12 Jun 2025 20:46:27.235 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 20:46:27.375 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 20:46:27.377 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 20:46:27.377 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 199759.
20747:M 12 Jun 2025 21:02:50.334 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 21:02:50.354 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 21:02:50.367 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 21:02:50.367 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 199829.
20747:M 12 Jun 2025 21:18:07.331 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 21:18:07.365 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 21:18:07.385 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 21:18:07.385 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 199829.
20747:M 12 Jun 2025 21:36:01.324 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 21:36:01.342 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 21:36:01.350 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 21:36:01.350 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 199843.
20747:M 12 Jun 2025 21:47:27.313 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 21:47:27.351 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 21:47:27.357 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 21:47:27.357 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 199843.
20747:M 12 Jun 2025 22:04:12.319 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 22:04:12.338 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 22:04:12.356 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 22:04:12.356 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 199913.
20747:M 12 Jun 2025 22:21:48.305 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 22:21:48.336 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 22:21:48.338 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 22:21:48.338 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 199913.
20747:M 12 Jun 2025 22:40:39.418 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 22:40:39.418 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 22:40:39.432 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 22:40:39.432 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 199997.
20747:M 12 Jun 2025 22:41:42.180 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 22:41:42.198 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 22:41:42.198 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 200011.
20747:M 12 Jun 2025 22:42:15.183 * 1 changes in 3600 seconds. Saving...
20747:M 12 Jun 2025 22:42:15.184 * Background saving started by pid 50829
50829:C 12 Jun 2025 22:42:15.191 * DB saved on disk
50829:C 12 Jun 2025 22:42:15.192 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20747:M 12 Jun 2025 22:42:15.285 * Background saving terminated with success
20747:M 12 Jun 2025 23:03:26.507 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 12 Jun 2025 23:03:26.507 * Connection with replica 127.0.0.1:7105 lost.
20747:M 12 Jun 2025 23:03:26.511 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 12 Jun 2025 23:03:26.512 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 203271.
20747:M 13 Jun 2025 01:36:30.394 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 01:36:30.406 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 01:36:30.407 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 214677.
20747:M 13 Jun 2025 01:54:24.091 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 01:54:24.092 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 01:54:24.100 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 01:54:24.100 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 214731.
20747:M 13 Jun 2025 02:02:30.883 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 02:02:31.011 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 02:02:31.028 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 02:02:31.029 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 214731.
20747:M 13 Jun 2025 02:18:30.361 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 02:18:30.498 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 02:18:30.502 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 02:18:30.502 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 214801.
20747:M 13 Jun 2025 02:34:59.521 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 02:34:59.523 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 02:34:59.523 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 214801.
20747:M 13 Jun 2025 02:50:58.154 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 02:50:58.155 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 02:50:58.165 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 02:50:58.165 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 214815.
20747:M 13 Jun 2025 03:08:20.043 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 03:08:20.043 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 03:08:20.049 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 03:08:20.050 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 214815.
20747:M 13 Jun 2025 03:22:03.952 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 03:22:03.953 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 03:22:03.958 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 03:22:03.958 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 214829.
20747:M 13 Jun 2025 03:39:49.908 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 03:39:49.908 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 03:39:49.912 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 03:39:49.912 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 214885.
20747:M 13 Jun 2025 03:57:18.734 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 03:57:18.734 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 03:57:18.737 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 03:57:18.737 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 214899.
20747:M 13 Jun 2025 04:02:24.734 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 04:02:24.864 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 04:02:24.866 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 04:02:24.866 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 214899.
20747:M 13 Jun 2025 04:20:17.528 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 04:20:17.655 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 04:20:17.659 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 04:20:17.660 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 214969.
20747:M 13 Jun 2025 04:37:48.911 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 04:37:48.911 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 04:37:48.919 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 04:37:48.921 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 215039.
20747:M 13 Jun 2025 04:55:27.764 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 04:55:27.764 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 04:55:27.768 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 04:55:27.768 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 215053.
20747:M 13 Jun 2025 05:02:24.846 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 05:02:24.847 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 05:02:24.851 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 05:02:24.851 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 215053.
20747:M 13 Jun 2025 05:21:04.667 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 05:21:04.667 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 05:21:04.670 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 05:21:04.670 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 215193.
20747:M 13 Jun 2025 05:37:20.453 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 05:37:20.453 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 05:37:20.457 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 05:37:20.457 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 215193.
20747:M 13 Jun 2025 05:53:04.262 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 05:53:04.262 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 05:53:04.267 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 05:53:04.268 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 215207.
20747:M 13 Jun 2025 06:02:41.822 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 06:02:41.823 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 06:02:41.832 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 06:02:41.833 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 215207.
20747:M 13 Jun 2025 06:20:22.050 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 06:20:22.058 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 06:20:22.058 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 215277.
20747:M 13 Jun 2025 06:36:05.851 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 06:36:05.852 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 06:36:05.856 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 06:36:05.856 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 215277.
20747:M 13 Jun 2025 06:51:28.671 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 06:51:28.671 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 06:51:28.676 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 06:51:28.676 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 215291.
20747:M 13 Jun 2025 07:07:16.459 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 07:07:16.459 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 07:07:16.461 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 07:07:16.461 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 215291.
20747:M 13 Jun 2025 07:17:29.215 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 07:17:29.344 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 07:17:29.347 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 07:17:29.347 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 215305.
20747:M 13 Jun 2025 07:36:07.198 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 07:36:07.337 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 07:36:07.341 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 07:36:07.341 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 215361.
20747:M 13 Jun 2025 07:51:25.160 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 07:51:25.164 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 07:51:25.166 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 215375.
20747:M 13 Jun 2025 08:08:51.926 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 08:08:51.927 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 08:08:51.931 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 08:08:51.932 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 215389.
20747:M 13 Jun 2025 08:25:34.849 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 08:25:34.849 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 08:25:34.851 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 08:25:34.851 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 215389.
20747:M 13 Jun 2025 08:42:12.673 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 08:42:12.673 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 08:42:12.676 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 08:42:12.676 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 215403.
20747:M 13 Jun 2025 08:59:24.657 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 08:59:24.657 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 08:59:24.661 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 08:59:24.661 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 215403.
20747:M 13 Jun 2025 09:15:18.597 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 09:15:18.598 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 09:15:18.600 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 09:15:18.600 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 215417.
20747:M 13 Jun 2025 09:18:29.573 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 09:18:29.574 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 09:18:29.576 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 09:18:29.576 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 215417.
20747:M 13 Jun 2025 09:32:28.605 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 09:32:28.608 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 09:32:28.608 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 215487.
20747:M 13 Jun 2025 09:34:10.618 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 09:34:10.620 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 09:34:10.620 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 215543.
20747:M 13 Jun 2025 11:02:49.505 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 11:02:49.508 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 11:02:49.508 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 222655.
20747:M 13 Jun 2025 11:27:01.389 * 1 changes in 3600 seconds. Saving...
20747:M 13 Jun 2025 11:27:01.391 * Background saving started by pid 42718
42718:C 13 Jun 2025 11:27:01.399 * DB saved on disk
42718:C 13 Jun 2025 11:27:01.399 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20747:M 13 Jun 2025 11:27:01.494 * Background saving terminated with success
20747:M 13 Jun 2025 12:26:31.283 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 12:26:31.284 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 12:26:31.287 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 12:26:31.288 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 230955.
20747:M 13 Jun 2025 12:58:21.612 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 12:58:21.612 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 12:58:21.615 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 12:58:21.615 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 232173.
20747:M 13 Jun 2025 13:16:07.866 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 13:16:07.866 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 13:16:07.870 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 13:16:07.871 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 232327.
20747:M 13 Jun 2025 13:32:01.765 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 13:32:01.765 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 13:32:01.768 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 13:32:01.769 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 232397.
20747:M 13 Jun 2025 13:52:57.705 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 13:52:57.706 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 13:52:57.709 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 13:52:57.709 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 232831.
20747:M 13 Jun 2025 14:02:03.637 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 14:02:03.637 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 14:02:03.641 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 14:02:03.641 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 233307.
20747:M 13 Jun 2025 14:13:34.787 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 14:13:34.791 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 14:13:34.791 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 233601.
20747:M 13 Jun 2025 14:48:51.652 * 1 changes in 3600 seconds. Saving...
20747:M 13 Jun 2025 14:48:51.662 * Background saving started by pid 25435
25435:C 13 Jun 2025 14:48:51.673 * DB saved on disk
25435:C 13 Jun 2025 14:48:51.673 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20747:M 13 Jun 2025 14:48:51.768 * Background saving terminated with success
20747:M 13 Jun 2025 16:11:05.584 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 16:11:05.584 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 16:11:05.587 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 16:11:05.587 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 245021.
20747:M 13 Jun 2025 17:03:55.623 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 17:03:55.623 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 17:03:55.626 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 17:03:55.626 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 248157.
20747:M 13 Jun 2025 17:15:36.615 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 17:15:36.619 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 17:15:36.619 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 248857.
20747:M 13 Jun 2025 17:18:12.440 * 1 changes in 3600 seconds. Saving...
20747:M 13 Jun 2025 17:18:12.441 * Background saving started by pid 8776
8776:C 13 Jun 2025 17:18:12.459 * DB saved on disk
8776:C 13 Jun 2025 17:18:12.459 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20747:M 13 Jun 2025 17:18:12.542 * Background saving terminated with success
20747:M 13 Jun 2025 18:34:58.658 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 18:34:58.665 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 18:34:58.666 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 257017.
20747:M 13 Jun 2025 18:51:47.276 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 18:51:47.396 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 18:51:47.398 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 18:51:47.398 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 257073.
20747:M 13 Jun 2025 19:09:07.272 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 19:09:07.390 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 19:09:07.392 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 19:09:07.392 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 257171.
20747:M 13 Jun 2025 19:26:44.268 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 19:26:44.399 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 19:26:44.402 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 19:26:44.402 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 257171.
20747:M 13 Jun 2025 19:43:54.367 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 19:43:54.388 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 19:43:54.401 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 19:43:54.402 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 257185.
20747:M 13 Jun 2025 19:59:18.261 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 19:59:18.391 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 19:59:18.396 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 19:59:18.396 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 257185.
20747:M 13 Jun 2025 20:17:03.365 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 20:17:03.377 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 20:17:03.391 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 20:17:03.391 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 257199.
20747:M 13 Jun 2025 20:33:27.386 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 20:33:27.391 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 20:33:27.410 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 20:33:27.410 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 257239.
20747:M 13 Jun 2025 20:49:58.454 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 20:49:58.454 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 20:49:58.457 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 20:49:58.458 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 257309.
20747:M 13 Jun 2025 21:06:34.353 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 21:06:34.367 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 21:06:34.384 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 21:06:34.384 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 257309.
20747:M 13 Jun 2025 21:22:46.235 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 21:22:46.376 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 21:22:46.380 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 21:22:46.380 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 257323.
20747:M 13 Jun 2025 21:34:27.350 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 21:34:27.362 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 21:34:27.373 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 21:34:27.374 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 257323.
20747:M 13 Jun 2025 21:54:53.748 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 21:54:53.749 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 21:54:53.753 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 21:54:53.753 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 257533.
20747:M 13 Jun 2025 22:12:14.640 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 22:12:14.641 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 22:12:14.645 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 22:12:14.645 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 257547.
20747:M 13 Jun 2025 22:17:26.826 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 22:17:26.826 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 22:17:26.831 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 22:17:26.831 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 257547.
20747:M 13 Jun 2025 22:33:20.289 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 22:33:20.289 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 22:33:20.292 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 22:33:20.292 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 257617.
20747:M 13 Jun 2025 22:35:27.791 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 22:35:27.792 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 22:35:27.797 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 22:35:27.797 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 257673.
20747:M 13 Jun 2025 22:51:40.550 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 22:51:40.550 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 22:51:40.555 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 22:51:40.555 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 257743.
20747:M 13 Jun 2025 23:07:53.257 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 23:07:53.257 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 23:07:53.260 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 23:07:53.260 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 257799.
20747:M 13 Jun 2025 23:24:10.564 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 23:24:10.564 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 23:24:10.567 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 23:24:10.568 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 257869.
20747:M 13 Jun 2025 23:36:27.766 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 23:36:27.766 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 23:36:27.772 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 23:36:27.772 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 257939.
20747:M 13 Jun 2025 23:52:45.168 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 13 Jun 2025 23:52:45.168 * Connection with replica 127.0.0.1:7105 lost.
20747:M 13 Jun 2025 23:52:45.175 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 13 Jun 2025 23:52:45.175 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 257995.
20747:M 14 Jun 2025 00:09:02.489 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 00:09:02.489 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 00:09:02.493 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 00:09:02.494 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 258065.
20747:M 14 Jun 2025 00:25:15.352 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 00:25:15.352 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 00:25:15.356 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 00:25:15.356 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 258121.
20747:M 14 Jun 2025 00:37:27.407 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 00:37:27.431 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 00:37:27.448 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 00:37:27.448 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 258261.
20747:M 14 Jun 2025 00:53:40.136 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 00:53:40.253 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 00:53:40.255 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 00:53:40.255 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 258317.
20747:M 14 Jun 2025 01:09:52.964 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 01:09:53.095 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 01:09:53.099 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 01:09:53.099 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 258387.
20747:M 14 Jun 2025 01:26:05.783 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 01:26:05.897 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 01:26:05.903 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 01:26:05.903 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 258443.
20747:M 14 Jun 2025 01:38:27.330 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 01:38:27.465 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 01:38:27.469 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 01:38:27.469 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 258513.
20747:M 14 Jun 2025 01:54:40.171 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 01:54:40.283 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 01:54:40.286 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 01:54:40.286 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 258583.
20747:M 14 Jun 2025 01:58:31.239 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 01:58:31.252 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 01:58:31.253 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 258709.
20747:M 14 Jun 2025 02:02:22.037 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 02:02:22.038 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 02:02:22.044 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 02:02:22.044 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 258779.
20747:M 14 Jun 2025 02:18:34.706 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 02:18:34.708 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 02:18:34.708 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 258835.
20747:M 14 Jun 2025 02:34:47.412 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 02:34:47.412 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 02:34:47.418 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 02:34:47.419 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 258905.
20747:M 14 Jun 2025 02:51:00.107 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 02:51:00.107 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 02:51:00.110 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 02:51:00.110 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 258961.
20747:M 14 Jun 2025 03:07:11.963 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 03:07:11.963 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 03:07:11.967 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 03:07:11.967 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 259031.
20747:M 14 Jun 2025 03:23:21.621 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 03:23:21.621 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 03:23:21.625 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 03:23:21.625 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 259101.
20747:M 14 Jun 2025 03:39:32.223 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 03:39:32.224 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 03:39:32.231 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 03:39:32.231 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 259157.
20747:M 14 Jun 2025 03:55:44.878 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 03:55:44.878 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 03:55:44.883 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 03:55:44.883 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 259227.
20747:M 14 Jun 2025 04:11:57.576 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 04:11:57.577 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 04:11:57.580 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 04:11:57.580 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 259283.
20747:M 14 Jun 2025 04:28:10.426 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 04:28:10.450 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 04:28:10.460 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 04:28:10.460 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 259353.
20747:M 14 Jun 2025 04:44:05.253 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 04:44:05.368 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 04:44:05.371 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 04:44:05.371 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 259409.
20747:M 14 Jun 2025 05:00:15.065 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 05:00:15.069 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 05:00:15.069 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 259479.
20747:M 14 Jun 2025 05:02:19.973 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 05:02:19.973 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 05:02:19.977 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 05:02:19.977 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 259535.
20747:M 14 Jun 2025 05:18:32.660 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 05:18:32.667 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 05:18:32.673 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 05:18:32.674 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 259605.
20747:M 14 Jun 2025 05:30:34.341 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 05:30:34.373 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 05:30:34.376 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 05:30:34.376 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 259661.
20747:M 14 Jun 2025 05:46:31.239 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 05:46:31.353 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 05:46:31.356 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 05:46:31.356 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 259731.
20747:M 14 Jun 2025 06:02:28.670 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 06:02:28.676 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 06:02:28.677 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 259787.
20747:M 14 Jun 2025 06:18:13.990 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 06:18:13.990 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 06:18:13.998 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 06:18:13.998 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 259843.
20747:M 14 Jun 2025 06:34:25.618 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 06:34:25.618 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 06:34:25.625 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 06:34:25.626 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 259913.
20747:M 14 Jun 2025 06:50:39.247 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 06:50:39.247 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 06:50:39.250 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 06:50:39.250 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 259983.
20747:M 14 Jun 2025 07:06:51.925 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 07:06:51.925 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 07:06:51.929 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 07:06:51.929 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 260039.
20747:M 14 Jun 2025 07:22:56.544 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 07:22:56.544 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 07:22:56.548 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 07:22:56.548 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 260109.
20747:M 14 Jun 2025 07:31:34.535 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 07:31:34.535 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 07:31:34.539 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 07:31:34.539 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 260165.
20747:M 14 Jun 2025 07:47:47.172 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 07:47:47.172 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 07:47:47.175 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 07:47:47.175 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 260235.
20747:M 14 Jun 2025 07:51:58.309 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 07:51:58.327 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 07:51:58.333 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 07:51:58.333 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 260291.
20747:M 14 Jun 2025 08:08:10.971 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 08:08:11.085 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 08:08:11.087 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 08:08:11.088 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 260361.
20747:M 14 Jun 2025 08:24:23.815 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 08:24:23.948 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 08:24:23.951 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 08:24:23.951 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 260431.
20747:M 14 Jun 2025 08:32:34.253 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 08:32:34.388 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 08:32:34.390 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 08:32:34.390 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 260487.
20747:M 14 Jun 2025 08:48:23.268 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 08:48:23.398 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 08:48:23.401 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 08:48:23.401 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 260557.
20747:M 14 Jun 2025 09:04:33.289 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 09:04:33.290 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 09:04:33.298 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 09:04:33.298 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 260613.
20747:M 14 Jun 2025 09:20:18.789 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 09:20:18.794 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 09:20:18.794 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 260683.
20747:M 14 Jun 2025 09:33:34.343 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 09:33:34.344 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 09:33:34.346 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 09:33:34.346 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 260739.
20747:M 14 Jun 2025 09:49:46.257 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 09:49:46.264 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 09:49:46.265 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 260809.
20747:M 14 Jun 2025 10:00:35.239 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 10:00:35.242 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 10:00:35.243 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 260865.
20747:M 14 Jun 2025 10:57:54.055 * 1 changes in 3600 seconds. Saving...
20747:M 14 Jun 2025 10:57:54.057 * Background saving started by pid 77459
77459:C 14 Jun 2025 10:57:54.083 * DB saved on disk
77459:C 14 Jun 2025 10:57:54.085 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20747:M 14 Jun 2025 10:57:54.160 * Background saving terminated with success
20747:M 14 Jun 2025 14:00:54.241 * 1 changes in 3600 seconds. Saving...
20747:M 14 Jun 2025 14:00:54.253 * Background saving started by pid 51056
51056:C 14 Jun 2025 14:00:54.266 * DB saved on disk
51056:C 14 Jun 2025 14:00:54.268 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20747:M 14 Jun 2025 14:00:54.358 * Background saving terminated with success
20747:M 14 Jun 2025 17:28:25.164 * 1 changes in 3600 seconds. Saving...
20747:M 14 Jun 2025 17:28:25.165 * Background saving started by pid 32659
32659:C 14 Jun 2025 17:28:25.174 * DB saved on disk
32659:C 14 Jun 2025 17:28:25.175 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20747:M 14 Jun 2025 17:28:25.267 * Background saving terminated with success
20747:M 14 Jun 2025 20:29:25.170 * 1 changes in 3600 seconds. Saving...
20747:M 14 Jun 2025 20:29:25.172 * Background saving started by pid 2792
2792:C 14 Jun 2025 20:29:25.179 * DB saved on disk
2792:C 14 Jun 2025 20:29:25.180 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20747:M 14 Jun 2025 20:29:25.275 * Background saving terminated with success
20747:M 14 Jun 2025 21:06:41.435 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 21:06:41.435 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 21:06:41.438 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 21:06:41.438 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 322213.
20747:M 14 Jun 2025 21:22:46.313 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 21:22:46.447 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 21:22:46.450 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 21:22:46.450 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 322269.
20747:M 14 Jun 2025 21:38:52.442 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 14 Jun 2025 21:38:52.458 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 21:38:52.476 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 21:38:52.476 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 322339.
20747:M 14 Jun 2025 21:55:05.400 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 21:55:05.404 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 21:55:05.404 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 322409.
20747:M 14 Jun 2025 22:03:24.358 * Connection with replica 127.0.0.1:7105 lost.
20747:M 14 Jun 2025 22:03:24.359 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 14 Jun 2025 22:03:24.359 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 322493.
20747:M 15 Jun 2025 04:50:06.759 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 15 Jun 2025 04:50:06.759 * Connection with replica 127.0.0.1:7105 lost.
20747:M 15 Jun 2025 04:50:06.765 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 15 Jun 2025 04:50:06.766 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 354845.
20747:M 15 Jun 2025 06:07:26.768 * Connection with replica 127.0.0.1:7105 lost.
20747:M 15 Jun 2025 06:07:26.769 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 15 Jun 2025 06:07:26.770 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 359913.
20747:M 15 Jun 2025 06:23:36.390 * Connection with replica 127.0.0.1:7105 lost.
20747:M 15 Jun 2025 06:23:36.392 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 15 Jun 2025 06:23:36.392 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 359983.
20747:M 15 Jun 2025 06:29:10.415 * Connection with replica 127.0.0.1:7105 lost.
20747:M 15 Jun 2025 06:29:10.417 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 15 Jun 2025 06:29:10.417 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 360039.
20747:M 15 Jun 2025 06:57:18.169 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 15 Jun 2025 06:57:18.170 * Connection with replica 127.0.0.1:7105 lost.
20747:M 15 Jun 2025 06:57:18.176 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 15 Jun 2025 06:57:18.176 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 361019.
20747:M 15 Jun 2025 07:13:45.362 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 15 Jun 2025 07:13:45.363 * Connection with replica 127.0.0.1:7105 lost.
20747:M 15 Jun 2025 07:13:45.367 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 15 Jun 2025 07:13:45.367 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 361103.
20747:M 15 Jun 2025 08:30:10.604 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 15 Jun 2025 08:30:10.604 * Connection with replica 127.0.0.1:7105 lost.
20747:M 15 Jun 2025 08:30:10.608 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 15 Jun 2025 08:30:10.608 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 366185.
20747:M 15 Jun 2025 08:46:22.491 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 15 Jun 2025 08:46:22.503 * Connection with replica 127.0.0.1:7105 lost.
20747:M 15 Jun 2025 08:46:22.517 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 15 Jun 2025 08:46:22.517 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 366255.
20747:M 15 Jun 2025 09:03:24.396 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 15 Jun 2025 09:03:24.396 * Connection with replica 127.0.0.1:7105 lost.
20747:M 15 Jun 2025 09:03:24.402 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 15 Jun 2025 09:03:24.402 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 366381.
20747:M 15 Jun 2025 10:20:40.290 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 15 Jun 2025 10:20:40.291 * Connection with replica 127.0.0.1:7105 lost.
20747:M 15 Jun 2025 10:20:40.292 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 15 Jun 2025 10:20:40.292 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 371449.
20747:M 15 Jun 2025 10:31:10.407 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 15 Jun 2025 10:31:10.407 * Connection with replica 127.0.0.1:7105 lost.
20747:M 15 Jun 2025 10:31:10.410 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 15 Jun 2025 10:31:10.410 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 371519.
20747:M 15 Jun 2025 10:34:06.771 * Connection with replica 127.0.0.1:7105 lost.
20747:M 15 Jun 2025 10:34:06.773 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 15 Jun 2025 10:34:06.774 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 371575.
20747:M 15 Jun 2025 10:50:18.727 * Connection with replica 127.0.0.1:7105 lost.
20747:M 15 Jun 2025 10:50:18.731 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 15 Jun 2025 10:50:18.731 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 371645.
20747:M 15 Jun 2025 11:07:09.003 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
20747:M 15 Jun 2025 11:07:09.138 * Connection with replica 127.0.0.1:7105 lost.
20747:M 15 Jun 2025 11:07:09.141 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 15 Jun 2025 11:07:09.141 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 371757.
20747:M 15 Jun 2025 12:11:18.478 * Connection with replica 127.0.0.1:7105 lost.
20747:M 15 Jun 2025 12:11:18.481 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 15 Jun 2025 12:11:18.481 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 375747.
20747:M 15 Jun 2025 12:15:18.196 * Connection with replica 127.0.0.1:7105 lost.
20747:M 15 Jun 2025 12:15:18.198 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 15 Jun 2025 12:15:18.198 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 375817.
20747:M 15 Jun 2025 13:02:24.687 * 1 changes in 3600 seconds. Saving...
20747:M 15 Jun 2025 13:02:24.690 * Background saving started by pid 65543
65543:C 15 Jun 2025 13:02:24.697 * DB saved on disk
65543:C 15 Jun 2025 13:02:24.697 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20747:M 15 Jun 2025 13:02:24.792 * Background saving terminated with success
20747:M 15 Jun 2025 16:24:12.535 * Connection with replica 127.0.0.1:7105 lost.
20747:M 15 Jun 2025 16:24:12.536 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 15 Jun 2025 16:24:12.536 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 397009.
20747:M 15 Jun 2025 16:40:22.518 * Connection with replica 127.0.0.1:7105 lost.
20747:M 15 Jun 2025 16:40:22.520 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 15 Jun 2025 16:40:22.520 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 397079.
20747:M 15 Jun 2025 17:49:12.496 * Connection with replica 127.0.0.1:7105 lost.
20747:M 15 Jun 2025 17:49:12.498 * Replica 127.0.0.1:7105 asks for synchronization
20747:M 15 Jun 2025 17:49:12.499 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 402203.
20747:M 15 Jun 2025 18:05:32.808 * 1 changes in 3600 seconds. Saving...
20747:M 15 Jun 2025 18:05:32.811 * Background saving started by pid 66577
66577:C 15 Jun 2025 18:05:32.821 * DB saved on disk
66577:C 15 Jun 2025 18:05:32.821 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20747:M 15 Jun 2025 18:05:32.917 * Background saving terminated with success
20747:signal-handler (1749990448) Received SIGTERM scheduling shutdown...
20747:M 15 Jun 2025 20:27:29.239 * User requested shutdown...
20747:M 15 Jun 2025 20:27:29.740 * Waiting for replicas before shutting down.
20747:M 15 Jun 2025 20:27:30.242 * Connection with replica client id #26238 lost.
20747:M 15 Jun 2025 20:27:30.243 * Calling fsync() on the AOF file.
20747:M 15 Jun 2025 20:27:30.753 * Saving the final RDB snapshot before exiting.
20747:M 15 Jun 2025 20:27:30.761 * DB saved on disk
20747:M 15 Jun 2025 20:27:30.761 * Removing the pid file.
20747:M 15 Jun 2025 20:27:30.763 # Redis is now ready to exit, bye bye...
12968:C 15 Jun 2025 20:50:05.181 * oO0OoO0OoO0Oo Redis is starting oO0OoO0OoO0Oo
12968:C 15 Jun 2025 20:50:05.181 * Redis version=7.2.7, bits=64, commit=00000000, modified=0, pid=12968, just started
12968:C 15 Jun 2025 20:50:05.182 * Configuration loaded
12968:M 15 Jun 2025 20:50:05.182 * Increased maximum number of open files to 10032 (it was originally set to 256).
12968:M 15 Jun 2025 20:50:05.183 * monotonic clock: POSIX clock_gettime
12968:M 15 Jun 2025 20:50:05.183 * Running mode=cluster, port=7102.
12968:M 15 Jun 2025 20:50:05.183 # WARNING: The TCP backlog setting of 511 cannot be enforced because kern.ipc.somaxconn is set to the lower value of 128.
12968:M 15 Jun 2025 20:50:05.184 * Node configuration loaded, I'm d8d9d9bbbea438dc779b216b617fffe1b7ad725e
12968:M 15 Jun 2025 20:50:05.184 * Server initialized
12968:M 15 Jun 2025 20:50:05.184 * Reading RDB base file on AOF loading...
12968:M 15 Jun 2025 20:50:05.184 * Loading RDB produced by version 7.2.7
12968:M 15 Jun 2025 20:50:05.184 * RDB age 540934 seconds
12968:M 15 Jun 2025 20:50:05.184 * RDB memory usage when created 1.70 Mb
12968:M 15 Jun 2025 20:50:05.185 * RDB is base AOF
12968:M 15 Jun 2025 20:50:05.185 * Done loading RDB, keys loaded: 0, keys expired: 0.
12968:M 15 Jun 2025 20:50:05.185 * DB loaded from base file appendonly-7102.aof.1.base.rdb: 0.001 seconds
12968:M 15 Jun 2025 20:50:05.191 * DB saved on disk
12968:M 15 Jun 2025 20:50:05.197 * DB saved on disk
12968:M 15 Jun 2025 20:50:05.203 * DB saved on disk
12968:M 15 Jun 2025 20:50:05.209 * DB saved on disk
12968:M 15 Jun 2025 20:50:05.214 * DB saved on disk
12968:M 15 Jun 2025 20:50:05.219 * DB saved on disk
12968:M 15 Jun 2025 20:50:05.224 * DB saved on disk
12968:M 15 Jun 2025 20:50:05.229 * DB saved on disk
12968:M 15 Jun 2025 20:50:05.234 * DB saved on disk
12968:M 15 Jun 2025 20:50:05.239 * DB saved on disk
12968:M 15 Jun 2025 20:50:05.244 * DB saved on disk
12968:M 15 Jun 2025 20:50:05.244 * DB loaded from incr file appendonly-7102.aof.1.incr.aof: 0.059 seconds
12968:M 15 Jun 2025 20:50:05.244 * DB loaded from append only file: 0.060 seconds
12968:M 15 Jun 2025 20:50:05.244 * Opening AOF incr file appendonly-7102.aof.1.incr.aof on server start
12968:M 15 Jun 2025 20:50:05.244 * Ready to accept connections tcp
12968:M 15 Jun 2025 20:50:07.276 * Cluster state changed: ok
12968:M 15 Jun 2025 20:50:11.339 * Replica 127.0.0.1:7105 asks for synchronization
12968:M 15 Jun 2025 20:50:11.339 * Partial resynchronization not accepted: Replication ID mismatch (Replica asked for '8aa84a47eeb2420a6b3b05eafc6d308df3bbcbbf', my replication IDs are '70ded225a6338788095a46e09850c3212a25c318' and '0000000000000000000000000000000000000000')
12968:M 15 Jun 2025 20:50:11.339 * Replication backlog created, my new replication IDs are '613e8438cdd00be4f4caab36efc22405af34eccb' and '0000000000000000000000000000000000000000'
12968:M 15 Jun 2025 20:50:11.339 * Delay next BGSAVE for diskless SYNC
12968:M 15 Jun 2025 20:50:16.309 * Starting BGSAVE for SYNC with target: replicas sockets
12968:M 15 Jun 2025 20:50:16.310 * Background RDB transfer started by pid 13051
13051:C 15 Jun 2025 20:50:16.311 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 15 Jun 2025 20:50:16.311 * Diskless rdb transfer, done reading from pipe, 1 replicas still up.
12968:M 15 Jun 2025 20:50:16.329 * Background RDB transfer terminated with success
12968:M 15 Jun 2025 20:50:16.330 * Streamed RDB transfer with replica 127.0.0.1:7105 succeeded (socket). Waiting for REPLCONF ACK from replica to enable streaming
12968:M 15 Jun 2025 20:50:16.330 * Synchronization with replica 127.0.0.1:7105 succeeded
12968:M 15 Jun 2025 20:50:16.425 * DB saved on disk
12968:M 15 Jun 2025 20:50:16.431 * configEpoch set to 0 via CLUSTER RESET HARD
12968:M 15 Jun 2025 20:50:16.432 * Node hard reset, now I'm 15f04fa7dabe54f878e48e88868b872357d1c0de
12968:M 15 Jun 2025 20:50:16.436 # Cluster state changed: fail
12968:M 15 Jun 2025 20:50:16.507 * Connection with replica 127.0.0.1:7105 lost.
12968:M 15 Jun 2025 20:50:19.556 * configEpoch set to 3 via CLUSTER SET-CONFIG-EPOCH
12968:M 15 Jun 2025 20:50:21.580 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 15 Jun 2025 20:50:21.581 * Partial resynchronization not accepted: Replication ID mismatch (Replica asked for '042f2c5f7cb61e8d9f99a8f06cc24af72deb5208', my replication IDs are '613e8438cdd00be4f4caab36efc22405af34eccb' and '0000000000000000000000000000000000000000')
12968:M 15 Jun 2025 20:50:21.581 * Delay next BGSAVE for diskless SYNC
12968:M 15 Jun 2025 20:50:24.610 * Cluster state changed: ok
12968:M 15 Jun 2025 20:50:26.434 * Starting BGSAVE for SYNC with target: replicas sockets
12968:M 15 Jun 2025 20:50:26.436 * Background RDB transfer started by pid 13149
13149:C 15 Jun 2025 20:50:26.437 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 15 Jun 2025 20:50:26.438 * Diskless rdb transfer, done reading from pipe, 1 replicas still up.
12968:M 15 Jun 2025 20:50:26.454 * Background RDB transfer terminated with success
12968:M 15 Jun 2025 20:50:26.455 * Streamed RDB transfer with replica 127.0.0.1:7104 succeeded (socket). Waiting for REPLCONF ACK from replica to enable streaming
12968:M 15 Jun 2025 20:50:26.455 * Synchronization with replica 127.0.0.1:7104 succeeded
12968:M 15 Jun 2025 21:50:17.043 * 1 changes in 3600 seconds. Saving...
12968:M 15 Jun 2025 21:50:17.046 * Background saving started by pid 36291
36291:C 15 Jun 2025 21:50:17.068 * DB saved on disk
36291:C 15 Jun 2025 21:50:17.079 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 15 Jun 2025 21:50:17.149 * Background saving terminated with success
12968:M 16 Jun 2025 00:16:49.354 * 1 changes in 3600 seconds. Saving...
12968:M 16 Jun 2025 00:16:49.356 * Background saving started by pid 98185
98185:C 16 Jun 2025 00:16:49.364 * DB saved on disk
98185:C 16 Jun 2025 00:16:49.364 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 16 Jun 2025 00:16:49.459 * Background saving terminated with success
12968:M 16 Jun 2025 01:10:07.638 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 01:10:07.639 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 01:10:07.639 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 23845.
12968:M 16 Jun 2025 01:26:20.366 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 01:26:20.367 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 01:26:20.367 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 23915.
12968:M 16 Jun 2025 01:42:32.242 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 01:42:32.244 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 01:42:32.244 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 23985.
12968:M 16 Jun 2025 01:58:45.523 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 01:58:45.524 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 01:58:45.530 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 01:58:45.531 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 24041.
12968:M 16 Jun 2025 02:14:58.207 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 02:14:58.207 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 02:14:58.210 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 02:14:58.210 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 24111.
12968:M 16 Jun 2025 02:31:02.674 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 02:31:02.675 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 02:31:02.676 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 02:31:02.676 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 24167.
12968:M 16 Jun 2025 02:47:11.764 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 02:47:11.764 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 02:47:11.768 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 02:47:11.768 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 24237.
12968:M 16 Jun 2025 02:53:37.974 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 02:53:37.987 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 02:53:37.988 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 24293.
12968:M 16 Jun 2025 03:09:47.882 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 03:09:47.886 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 03:09:47.886 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 24363.
12968:M 16 Jun 2025 03:26:00.962 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 03:26:00.970 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 03:26:00.980 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 03:26:00.980 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 24459.
12968:M 16 Jun 2025 03:34:19.486 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 03:34:19.502 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 03:34:19.504 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 03:34:19.505 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 24529.
12968:M 16 Jun 2025 03:35:58.614 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 03:35:58.618 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 03:35:58.618 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 24585.
12968:M 16 Jun 2025 03:52:10.847 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 03:52:10.864 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 03:52:10.876 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 03:52:10.876 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 24655.
12968:M 16 Jun 2025 04:08:09.618 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 04:08:09.618 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 04:08:09.618 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 24725.
12968:M 16 Jun 2025 04:24:18.239 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 04:24:18.241 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 04:24:18.241 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 24781.
12968:M 16 Jun 2025 04:40:30.979 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 04:40:30.980 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 04:40:30.980 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 24837.
12968:M 16 Jun 2025 04:56:43.688 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 04:56:43.690 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 04:56:43.690 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 24907.
12968:M 16 Jun 2025 05:12:55.704 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 05:12:55.706 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 05:12:55.706 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 24977.
12968:M 16 Jun 2025 05:29:08.512 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 05:29:08.514 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 05:29:08.514 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 25033.
12968:M 16 Jun 2025 05:45:21.330 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 05:45:21.331 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 05:45:21.331 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 25103.
12968:M 16 Jun 2025 06:01:34.101 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 06:01:34.103 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 06:01:34.103 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 25159.
12968:M 16 Jun 2025 06:17:46.897 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 06:17:46.926 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 06:17:46.929 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 06:17:46.929 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 25229.
12968:M 16 Jun 2025 06:33:59.599 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 06:33:59.715 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 06:33:59.719 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 06:33:59.719 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 25299.
12968:M 16 Jun 2025 06:50:12.405 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 06:50:12.524 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 06:50:12.527 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 06:50:12.528 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 25355.
12968:M 16 Jun 2025 07:06:24.293 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 07:06:24.405 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 07:06:24.407 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 07:06:24.408 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 25425.
12968:M 16 Jun 2025 07:22:33.033 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 07:22:33.034 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 07:22:33.038 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 07:22:33.038 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 25481.
12968:M 16 Jun 2025 07:38:24.525 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 07:38:24.528 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 07:38:24.528 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 25551.
12968:M 16 Jun 2025 07:54:32.345 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 07:54:32.465 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 07:54:32.468 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 07:54:32.468 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 25607.
12968:M 16 Jun 2025 07:57:36.284 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 07:57:36.415 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 07:57:36.418 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 07:57:36.418 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 25663.
12968:M 16 Jun 2025 08:02:21.100 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 08:02:21.226 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 08:02:21.230 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 08:02:21.230 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 25733.
12968:M 16 Jun 2025 08:18:33.101 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 08:18:33.219 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 08:18:33.222 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 08:18:33.222 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 25803.
12968:M 16 Jun 2025 08:34:45.135 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 08:34:45.255 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 08:34:45.257 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 08:34:45.257 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 25859.
12968:M 16 Jun 2025 08:51:12.972 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 08:51:12.973 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 08:51:12.983 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 08:51:12.983 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 25999.
12968:M 16 Jun 2025 09:09:25.079 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 09:09:25.080 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 09:09:25.087 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 09:09:25.087 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 26195.
12968:M 16 Jun 2025 09:24:49.982 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 09:24:49.982 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 09:24:49.985 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 09:24:49.985 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 26195.
12968:M 16 Jun 2025 09:36:11.203 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 09:36:11.204 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 09:36:11.209 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 09:36:11.210 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 26209.
12968:M 16 Jun 2025 09:40:31.164 * 1 changes in 3600 seconds. Saving...
12968:M 16 Jun 2025 09:40:31.166 * Background saving started by pid 27104
27104:C 16 Jun 2025 09:40:31.185 * DB saved on disk
27104:C 16 Jun 2025 09:40:31.185 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 16 Jun 2025 09:40:31.267 * Background saving terminated with success
12968:M 16 Jun 2025 10:07:18.497 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 10:07:18.498 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 10:07:18.516 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 10:07:18.516 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 29987.
12968:M 16 Jun 2025 10:15:47.851 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 10:15:47.865 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 10:15:47.881 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 10:15:47.881 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 30043.
12968:M 16 Jun 2025 10:25:11.901 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 10:25:11.926 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 10:25:11.932 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 10:25:11.932 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 30547.
12968:M 16 Jun 2025 13:30:24.126 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 13:30:24.131 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 13:30:24.132 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 44559.
12968:M 16 Jun 2025 13:47:57.614 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 13:47:57.615 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 13:47:57.615 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 44685.
12968:M 16 Jun 2025 14:05:48.515 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 14:05:48.531 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 14:05:48.542 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 14:05:48.542 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 44811.
12968:M 16 Jun 2025 15:40:07.077 * 1 changes in 3600 seconds. Saving...
12968:M 16 Jun 2025 15:40:07.081 * Background saving started by pid 51379
51379:C 16 Jun 2025 15:40:07.101 * DB saved on disk
51379:C 16 Jun 2025 15:40:07.101 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 16 Jun 2025 15:40:07.183 * Background saving terminated with success
12968:M 16 Jun 2025 18:49:24.913 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 18:49:24.916 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 18:49:24.916 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 69937.
12968:M 16 Jun 2025 18:52:08.488 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 18:52:08.488 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 18:52:08.489 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 18:52:08.489 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 70063.
12968:M 16 Jun 2025 18:55:44.487 * 1 changes in 3600 seconds. Saving...
12968:M 16 Jun 2025 18:55:44.488 * Background saving started by pid 24590
24590:C 16 Jun 2025 18:55:44.495 * DB saved on disk
24590:C 16 Jun 2025 18:55:44.496 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 16 Jun 2025 18:55:44.590 * Background saving terminated with success
12968:M 16 Jun 2025 20:10:13.894 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 20:10:13.894 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 20:10:13.897 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 20:10:13.897 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 78251.
12968:M 16 Jun 2025 20:26:42.750 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 20:26:42.750 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 20:26:42.753 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 20:26:42.754 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 78321.
12968:M 16 Jun 2025 20:44:24.656 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 20:44:24.656 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 20:44:24.657 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 20:44:24.657 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 78321.
12968:M 16 Jun 2025 21:01:30.566 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 21:01:30.589 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 21:01:30.595 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 21:01:30.595 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 78335.
12968:M 16 Jun 2025 21:07:13.458 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 21:07:13.583 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 21:07:13.587 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 21:07:13.587 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 78335.
12968:M 16 Jun 2025 21:23:10.565 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 21:23:10.592 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 21:23:10.602 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 21:23:10.602 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 78405.
12968:M 16 Jun 2025 21:40:08.600 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 21:40:08.601 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 21:40:08.607 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 21:40:08.608 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 78405.
12968:M 16 Jun 2025 21:57:33.614 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 21:57:33.631 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 21:57:33.649 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 21:57:33.649 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 78459.
12968:M 16 Jun 2025 22:10:04.586 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 22:10:04.609 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 22:10:04.620 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 22:10:04.620 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 78459.
12968:M 16 Jun 2025 22:28:33.597 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 22:28:33.613 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 22:28:33.634 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 22:28:33.634 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 78529.
12968:M 16 Jun 2025 22:44:42.601 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 22:44:42.623 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 22:44:42.636 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 22:44:42.636 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 78529.
12968:M 16 Jun 2025 22:57:46.534 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 22:57:46.557 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 22:57:46.563 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 22:57:46.564 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 78543.
12968:M 16 Jun 2025 23:11:04.514 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 23:11:04.646 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 23:11:04.649 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 23:11:04.649 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 78613.
12968:M 16 Jun 2025 23:27:17.290 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 23:27:17.410 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 23:27:17.413 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 23:27:17.413 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 78669.
12968:M 16 Jun 2025 23:30:19.306 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 23:30:19.442 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 23:30:19.445 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 23:30:19.445 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 78739.
12968:M 16 Jun 2025 23:46:33.061 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 16 Jun 2025 23:46:33.191 * Connection with replica 127.0.0.1:7104 lost.
12968:M 16 Jun 2025 23:46:33.195 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 16 Jun 2025 23:46:33.195 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 78795.
12968:M 17 Jun 2025 00:02:45.866 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 00:02:45.985 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 00:02:45.993 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 00:02:45.993 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 78865.
12968:M 17 Jun 2025 00:12:04.116 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 00:12:04.243 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 00:12:04.246 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 00:12:04.246 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 78935.
12968:M 17 Jun 2025 00:28:16.851 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 00:28:16.971 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 00:28:16.976 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 00:28:16.976 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 78991.
12968:M 17 Jun 2025 00:39:48.458 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 00:39:48.577 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 00:39:48.581 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 00:39:48.581 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 79061.
12968:M 17 Jun 2025 01:12:25.353 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 01:12:25.353 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 01:12:25.356 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 01:12:25.357 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 80475.
12968:M 17 Jun 2025 01:28:38.003 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 01:28:38.003 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 01:28:38.007 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 01:28:38.007 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 80545.
12968:M 17 Jun 2025 01:44:51.638 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 01:44:51.638 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 01:44:51.645 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 01:44:51.645 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 80615.
12968:M 17 Jun 2025 02:01:04.443 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 02:01:04.443 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 02:01:04.448 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 02:01:04.448 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 80671.
12968:M 17 Jun 2025 02:17:17.096 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 02:17:17.107 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 02:17:17.107 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 80741.
12968:M 17 Jun 2025 02:33:29.761 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 02:33:29.762 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 02:33:29.764 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 02:33:29.764 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 80797.
12968:M 17 Jun 2025 02:49:42.443 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 02:49:42.446 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 02:49:42.446 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 80867.
12968:M 17 Jun 2025 02:56:30.279 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 02:56:30.416 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 02:56:30.418 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 02:56:30.418 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 80937.
12968:M 17 Jun 2025 03:12:44.028 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 03:12:44.031 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 03:12:44.032 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 80993.
12968:M 17 Jun 2025 03:15:50.954 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 03:15:50.955 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 03:15:50.966 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 03:15:50.967 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 81063.
12968:M 17 Jun 2025 03:32:03.622 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 03:32:03.622 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 03:32:03.624 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 03:32:03.625 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 81133.
12968:M 17 Jun 2025 03:48:16.344 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 03:48:16.344 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 03:48:16.347 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 03:48:16.348 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 81189.
12968:M 17 Jun 2025 04:04:28.863 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 04:04:28.863 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 04:04:28.867 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 04:04:28.867 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 81259.
12968:M 17 Jun 2025 04:20:40.803 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 04:20:40.803 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 04:20:40.804 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 04:20:40.804 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 81315.
12968:M 17 Jun 2025 04:36:53.634 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 04:36:53.648 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 04:36:53.664 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 04:36:53.664 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 81385.
12968:M 17 Jun 2025 04:53:06.354 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 04:53:06.494 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 04:53:06.497 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 04:53:06.497 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 81455.
12968:M 17 Jun 2025 05:09:19.083 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 05:09:19.199 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 05:09:19.204 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 05:09:19.204 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 81511.
12968:M 17 Jun 2025 05:20:13.404 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 05:20:13.518 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 05:20:13.521 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 05:20:13.521 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 81581.
12968:M 17 Jun 2025 05:36:27.162 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 05:36:27.162 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 05:36:27.169 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 05:36:27.170 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 81637.
12968:M 17 Jun 2025 05:52:39.758 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 05:52:39.758 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 05:52:39.773 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 05:52:39.773 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 81707.
12968:M 17 Jun 2025 06:08:51.648 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 06:08:51.648 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 06:08:51.653 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 06:08:51.653 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 81777.
12968:M 17 Jun 2025 06:25:03.500 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 06:25:03.502 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 06:25:03.503 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 81833.
12968:M 17 Jun 2025 06:41:17.178 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 06:41:17.183 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 06:41:17.183 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 81903.
12968:M 17 Jun 2025 06:53:02.021 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 06:53:02.021 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 06:53:02.030 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 06:53:02.030 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 81973.
12968:M 17 Jun 2025 07:09:15.624 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 07:09:15.624 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 07:09:15.627 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 07:09:15.627 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 82029.
12968:M 17 Jun 2025 07:25:27.496 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 07:25:27.496 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 07:25:27.503 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 07:25:27.504 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 82099.
12968:M 17 Jun 2025 07:41:40.030 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 07:41:40.032 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 07:41:40.032 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 82155.
12968:M 17 Jun 2025 07:57:51.902 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 07:57:51.902 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 07:57:51.904 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 07:57:51.905 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 82225.
12968:M 17 Jun 2025 08:14:05.409 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 08:14:05.409 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 08:14:05.416 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 08:14:05.416 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 82295.
12968:M 17 Jun 2025 08:30:18.106 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 08:30:18.107 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 08:30:18.112 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 08:30:18.112 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 82351.
12968:M 17 Jun 2025 08:34:58.730 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 08:34:58.730 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 08:34:58.733 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 08:34:58.733 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 82421.
12968:M 17 Jun 2025 08:43:15.903 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 08:43:15.904 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 08:43:15.908 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 08:43:15.908 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 82477.
12968:M 17 Jun 2025 09:00:42.254 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 09:00:42.255 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 09:00:42.261 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 09:00:42.261 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 82603.
12968:M 17 Jun 2025 09:18:19.249 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 09:18:19.254 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 09:18:19.255 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 82617.
12968:M 17 Jun 2025 09:36:07.238 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 09:36:07.239 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 09:36:07.246 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 09:36:07.247 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 82617.
12968:M 17 Jun 2025 09:44:44.170 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 09:44:44.170 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 09:44:44.177 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 09:44:44.178 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 82631.
12968:M 17 Jun 2025 10:34:38.963 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 10:34:38.964 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 10:34:38.967 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 10:34:38.967 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 86173.
12968:M 17 Jun 2025 10:42:46.426 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 10:42:46.539 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 10:42:46.542 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 10:42:46.542 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 86243.
12968:M 17 Jun 2025 10:59:23.584 * 1 changes in 3600 seconds. Saving...
12968:M 17 Jun 2025 10:59:23.588 * Background saving started by pid 98476
98476:C 17 Jun 2025 10:59:23.598 * DB saved on disk
98476:C 17 Jun 2025 10:59:23.598 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 17 Jun 2025 10:59:23.690 * Background saving terminated with success
12968:M 17 Jun 2025 13:18:04.648 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 13:18:04.648 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 13:18:04.651 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 13:18:04.651 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 99611.
12968:M 17 Jun 2025 13:33:13.850 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 13:33:13.850 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 13:33:13.860 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 13:33:13.861 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 99765.
12968:M 17 Jun 2025 13:51:00.858 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 13:51:00.859 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 13:51:00.865 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 13:51:00.865 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 99821.
12968:M 17 Jun 2025 14:03:44.166 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 14:03:44.166 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 14:03:44.171 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 14:03:44.171 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 99931.
12968:M 17 Jun 2025 14:09:38.549 * 1 changes in 3600 seconds. Saving...
12968:M 17 Jun 2025 14:09:38.550 * Background saving started by pid 75561
75561:C 17 Jun 2025 14:09:38.559 * DB saved on disk
75561:C 17 Jun 2025 14:09:38.560 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 17 Jun 2025 14:09:38.651 * Background saving terminated with success
12968:M 17 Jun 2025 14:56:35.578 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 14:56:35.578 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 14:56:35.585 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 14:56:35.585 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 104843.
12968:M 17 Jun 2025 15:07:50.332 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 15:07:50.334 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 15:07:50.334 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 104913.
12968:M 17 Jun 2025 15:57:30.962 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 15:57:30.967 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 15:57:30.967 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 108721.
12968:M 17 Jun 2025 16:02:20.915 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 16:02:20.917 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 16:02:20.917 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 108791.
12968:M 17 Jun 2025 16:19:42.754 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 16:19:42.757 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 16:19:42.757 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 108847.
12968:M 17 Jun 2025 16:38:04.051 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 16:38:04.057 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 16:38:04.057 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 109015.
12968:M 17 Jun 2025 16:55:21.746 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 16:55:21.749 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 16:55:21.750 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 109141.
12968:M 17 Jun 2025 16:57:58.450 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 16:57:58.453 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 16:57:58.453 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 109211.
12968:M 17 Jun 2025 17:13:34.125 * 1 changes in 3600 seconds. Saving...
12968:M 17 Jun 2025 17:13:34.127 * Background saving started by pid 44822
44822:C 17 Jun 2025 17:13:34.136 * DB saved on disk
44822:C 17 Jun 2025 17:13:34.137 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 17 Jun 2025 17:13:34.229 * Background saving terminated with success
12968:M 17 Jun 2025 18:59:01.993 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 18:59:01.994 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 18:59:01.994 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 120631.
12968:M 17 Jun 2025 19:16:26.596 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 19:16:26.598 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 19:16:26.599 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 120687.
12968:M 17 Jun 2025 19:32:18.511 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 19:32:18.521 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 19:32:18.521 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 120701.
12968:M 17 Jun 2025 19:49:03.525 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 19:49:03.527 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 19:49:03.527 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 120701.
12968:M 17 Jun 2025 20:05:34.519 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 20:05:34.523 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 20:05:34.523 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 120715.
12968:M 17 Jun 2025 20:21:22.518 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 20:21:22.530 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 20:21:22.537 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 20:21:22.537 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 40 bytes of backlog starting from offset 120715.
12968:M 17 Jun 2025 20:36:26.532 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 20:36:26.532 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 20:36:26.537 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 20:36:26.537 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 120769.
12968:M 17 Jun 2025 20:51:56.410 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 20:51:56.525 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 20:51:56.537 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 20:51:56.538 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 120769.
12968:M 17 Jun 2025 20:54:17.397 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 20:54:17.538 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 20:54:17.540 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 20:54:17.540 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 120783.
12968:M 17 Jun 2025 21:10:38.548 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 21:10:38.549 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 21:10:38.562 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 21:10:38.562 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 120839.
12968:M 17 Jun 2025 21:15:53.769 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 17 Jun 2025 21:15:53.788 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 21:15:53.805 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 21:15:53.806 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 120853.
12968:M 17 Jun 2025 21:17:38.830 * Connection with replica client id #7217 lost.
12968:M 17 Jun 2025 21:17:38.831 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 21:17:38.832 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 120909.
12968:M 17 Jun 2025 21:33:51.604 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 21:33:51.607 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 21:33:51.607 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 120979.
12968:M 17 Jun 2025 21:46:13.491 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 21:46:13.494 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 21:46:13.494 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 121049.
12968:M 17 Jun 2025 21:55:17.486 * Connection with replica 127.0.0.1:7104 lost.
12968:M 17 Jun 2025 21:55:17.488 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 17 Jun 2025 21:55:17.488 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 121105.
12968:M 17 Jun 2025 22:22:07.246 * 1 changes in 3600 seconds. Saving...
12968:M 17 Jun 2025 22:22:07.248 * Background saving started by pid 36755
36755:C 17 Jun 2025 22:22:07.275 * DB saved on disk
36755:C 17 Jun 2025 22:22:07.276 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 17 Jun 2025 22:22:07.349 * Background saving terminated with success
12968:M 18 Jun 2025 01:35:15.615 * 1 changes in 3600 seconds. Saving...
12968:M 18 Jun 2025 01:35:15.617 * Background saving started by pid 71787
71787:C 18 Jun 2025 01:35:15.626 * DB saved on disk
71787:C 18 Jun 2025 01:35:15.627 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 18 Jun 2025 01:35:15.720 * Background saving terminated with success
12968:M 18 Jun 2025 02:02:54.962 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 02:02:55.076 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 02:02:55.099 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 02:02:55.099 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 144003.
12968:M 18 Jun 2025 02:19:07.764 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 02:19:07.878 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 02:19:07.883 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 02:19:07.884 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 144059.
12968:M 18 Jun 2025 02:35:20.554 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 02:35:20.692 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 02:35:20.713 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 02:35:20.713 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 144129.
12968:M 18 Jun 2025 02:51:34.286 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 02:51:34.287 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 02:51:34.298 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 02:51:34.298 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 144199.
12968:M 18 Jun 2025 03:07:46.987 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 03:07:47.117 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 03:07:47.120 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 03:07:47.120 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 144255.
12968:M 18 Jun 2025 03:23:51.866 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 03:23:51.866 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 03:23:51.872 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 03:23:51.872 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 144325.
12968:M 18 Jun 2025 03:40:05.543 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 03:40:05.544 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 03:40:05.549 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 03:40:05.550 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 144395.
12968:M 18 Jun 2025 03:46:44.630 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 03:46:44.630 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 03:46:44.634 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 03:46:44.635 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 144451.
12968:M 18 Jun 2025 04:02:57.277 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 04:02:57.277 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 04:02:57.280 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 04:02:57.281 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 144521.
12968:M 18 Jun 2025 04:18:51.298 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 04:18:51.315 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 04:18:51.321 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 04:18:51.329 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 144577.
12968:M 18 Jun 2025 04:35:02.184 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 04:35:02.299 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 04:35:02.302 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 04:35:02.302 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 144647.
12968:M 18 Jun 2025 04:51:15.914 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 04:51:15.914 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 04:51:15.923 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 04:51:15.924 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 144757.
12968:M 18 Jun 2025 05:07:28.656 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 05:07:28.656 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 05:07:28.668 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 05:07:28.668 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 144813.
12968:M 18 Jun 2025 05:23:41.360 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 05:23:41.361 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 05:23:41.364 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 05:23:41.364 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 144883.
12968:M 18 Jun 2025 05:39:54.007 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 05:39:54.008 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 05:39:54.014 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 05:39:54.014 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 144939.
12968:M 18 Jun 2025 05:56:05.862 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 05:56:05.982 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 05:56:05.985 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 05:56:05.985 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 145009.
12968:M 18 Jun 2025 05:59:13.134 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 05:59:13.261 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 05:59:13.263 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 05:59:13.263 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 145079.
12968:M 18 Jun 2025 06:15:25.993 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 06:15:26.125 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 06:15:26.129 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 06:15:26.129 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 145135.
12968:M 18 Jun 2025 06:31:38.790 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 06:31:38.910 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 06:31:38.913 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 06:31:38.913 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 145205.
12968:M 18 Jun 2025 06:47:47.849 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 06:47:47.978 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 06:47:47.981 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 06:47:47.981 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 145261.
12968:M 18 Jun 2025 07:04:01.594 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 07:04:01.594 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 07:04:01.598 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 07:04:01.600 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 145331.
12968:M 18 Jun 2025 07:20:14.308 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 07:20:14.309 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 07:20:14.312 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 07:20:14.313 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 145401.
12968:M 18 Jun 2025 07:36:28.091 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 07:36:28.092 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 07:36:28.098 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 07:36:28.098 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 145457.
12968:M 18 Jun 2025 07:41:51.123 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 07:41:51.123 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 07:41:51.128 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 07:41:51.128 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 145527.
12968:M 18 Jun 2025 07:55:59.951 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 07:55:59.952 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 07:55:59.954 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 07:55:59.954 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 145597.
12968:M 18 Jun 2025 08:10:09.125 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 08:10:09.126 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 08:10:09.129 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 08:10:09.129 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 145653.
12968:M 18 Jun 2025 08:28:34.695 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 08:28:34.695 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 08:28:34.697 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 08:28:34.697 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 145723.
12968:M 18 Jun 2025 08:30:10.072 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 08:30:10.072 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 08:30:10.075 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 08:30:10.075 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 145723.
12968:M 18 Jun 2025 08:33:15.244 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 08:33:15.246 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 08:33:15.266 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 08:33:15.267 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 145793.
12968:M 18 Jun 2025 08:42:31.609 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 08:42:31.610 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 08:42:31.617 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 08:42:31.618 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 145933.
12968:M 18 Jun 2025 09:00:22.174 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 09:00:22.174 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 09:00:22.177 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 09:00:22.177 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 146003.
12968:M 18 Jun 2025 09:18:02.066 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 09:18:02.066 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 09:18:02.076 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 09:18:02.076 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 146003.
12968:M 18 Jun 2025 09:33:53.946 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 09:33:53.946 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 09:33:53.949 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 09:33:53.950 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 146017.
12968:M 18 Jun 2025 09:38:48.638 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 09:38:48.639 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 09:38:48.642 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 09:38:48.642 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 146017.
12968:M 18 Jun 2025 09:41:37.617 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 09:41:37.618 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 09:41:37.621 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 09:41:37.621 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 146087.
12968:M 18 Jun 2025 11:33:04.012 * 1 changes in 3600 seconds. Saving...
12968:M 18 Jun 2025 11:33:04.014 * Background saving started by pid 79949
79949:C 18 Jun 2025 11:33:04.025 * DB saved on disk
79949:C 18 Jun 2025 11:33:04.026 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 18 Jun 2025 11:33:04.116 * Background saving terminated with success
12968:M 18 Jun 2025 12:36:01.081 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 12:36:01.085 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 12:36:01.085 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 161709.
12968:M 18 Jun 2025 12:42:56.282 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 12:42:56.287 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 12:42:56.287 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 161835.
12968:M 18 Jun 2025 12:50:24.846 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 12:50:24.849 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 12:50:24.849 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 161891.
12968:M 18 Jun 2025 12:53:12.140 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 12:53:12.179 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 12:53:12.181 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 12:53:12.181 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 161975.
12968:M 18 Jun 2025 13:02:20.984 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 13:02:20.984 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 13:02:20.986 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 13:02:20.986 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 162031.
12968:M 18 Jun 2025 13:20:21.558 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 13:20:21.558 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 13:20:21.562 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 13:20:21.562 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 162171.
12968:M 18 Jun 2025 13:37:51.896 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 13:37:51.896 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 13:37:51.901 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 13:37:51.902 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 162241.
12968:M 18 Jun 2025 13:54:24.622 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 13:54:24.623 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 13:54:24.628 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 13:54:24.629 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 162325.
12968:M 18 Jun 2025 14:05:05.629 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 14:05:05.629 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 14:05:05.632 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 14:05:05.633 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 162829.
12968:M 18 Jun 2025 15:18:17.652 * 1 changes in 3600 seconds. Saving...
12968:M 18 Jun 2025 15:18:17.653 * Background saving started by pid 77338
77338:C 18 Jun 2025 15:18:17.661 * DB saved on disk
77338:C 18 Jun 2025 15:18:17.661 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 18 Jun 2025 15:18:17.755 * Background saving terminated with success
12968:M 18 Jun 2025 16:18:18.016 * 1 changes in 3600 seconds. Saving...
12968:M 18 Jun 2025 16:18:18.019 * Background saving started by pid 18199
18199:C 18 Jun 2025 16:18:18.033 * DB saved on disk
18199:C 18 Jun 2025 16:18:18.034 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 18 Jun 2025 16:18:18.122 * Background saving terminated with success
12968:M 18 Jun 2025 19:33:25.818 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 19:33:25.818 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 19:33:25.822 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 19:33:25.822 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 190945.
12968:M 18 Jun 2025 19:53:51.916 * 1 changes in 3600 seconds. Saving...
12968:M 18 Jun 2025 19:53:51.919 * Background saving started by pid 55311
55311:C 18 Jun 2025 19:53:51.935 * DB saved on disk
55311:C 18 Jun 2025 19:53:51.935 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 18 Jun 2025 19:53:52.022 * Background saving terminated with success
12968:M 18 Jun 2025 22:16:16.464 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 22:16:16.466 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 22:16:16.467 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 204831.
12968:M 18 Jun 2025 22:25:15.481 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 22:25:15.494 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 22:25:15.494 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 204831.
12968:M 18 Jun 2025 22:41:24.482 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 22:41:24.486 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 22:41:24.487 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 204901.
12968:M 18 Jun 2025 23:16:22.494 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 23:16:22.501 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 23:16:22.501 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 205039.
12968:M 18 Jun 2025 23:24:35.385 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 23:24:35.405 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 23:24:35.405 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 205039.
12968:M 18 Jun 2025 23:39:14.594 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 18 Jun 2025 23:39:14.594 * Connection with replica 127.0.0.1:7104 lost.
12968:M 18 Jun 2025 23:39:14.597 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 18 Jun 2025 23:39:14.598 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 205109.
12968:M 18 Jun 2025 23:53:40.600 * 1 changes in 3600 seconds. Saving...
12968:M 18 Jun 2025 23:53:40.601 * Background saving started by pid 17741
17741:C 18 Jun 2025 23:53:40.609 * DB saved on disk
17741:C 18 Jun 2025 23:53:40.609 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 18 Jun 2025 23:53:40.704 * Background saving terminated with success
12968:M 19 Jun 2025 02:44:16.268 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 02:44:16.271 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 02:44:16.271 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 220969.
12968:M 19 Jun 2025 03:00:29.454 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 19 Jun 2025 03:00:29.454 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 03:00:29.457 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 03:00:29.457 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 221079.
12968:M 19 Jun 2025 03:16:41.760 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 03:16:41.767 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 03:16:41.768 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 221149.
12968:M 19 Jun 2025 03:32:49.694 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 03:32:49.698 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 03:32:49.698 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 221205.
12968:M 19 Jun 2025 03:49:03.564 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 03:49:03.574 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 03:49:03.575 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 221261.
12968:M 19 Jun 2025 04:05:16.379 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 04:05:16.380 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 04:05:16.381 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 221331.
12968:M 19 Jun 2025 04:22:21.579 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 04:22:21.585 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 04:22:21.586 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 221457.
12968:M 19 Jun 2025 04:29:45.074 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 04:29:45.078 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 04:29:45.078 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 221527.
12968:M 19 Jun 2025 04:45:43.467 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 04:45:43.483 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 04:45:43.483 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 221597.
12968:M 19 Jun 2025 05:01:42.794 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 05:01:42.797 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 05:01:42.798 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 221653.
12968:M 19 Jun 2025 05:17:53.701 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 05:17:53.703 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 05:17:53.704 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 221709.
12968:M 19 Jun 2025 05:30:44.608 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 05:30:44.609 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 05:30:44.609 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 221779.
12968:M 19 Jun 2025 05:46:59.416 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 05:46:59.423 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 05:46:59.424 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 221835.
12968:M 19 Jun 2025 06:03:12.082 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 06:03:12.084 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 06:03:12.084 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 221905.
12968:M 19 Jun 2025 06:19:25.837 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 06:19:25.841 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 06:19:25.842 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 221975.
12968:M 19 Jun 2025 06:31:44.691 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 06:31:44.693 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 06:31:44.693 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 222031.
12968:M 19 Jun 2025 06:38:15.498 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 19 Jun 2025 06:38:15.518 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 06:38:15.522 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 06:38:15.522 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 222101.
12968:M 19 Jun 2025 06:54:29.291 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 19 Jun 2025 06:54:29.291 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 06:54:29.301 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 06:54:29.302 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 222171.
12968:M 19 Jun 2025 07:10:35.603 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 19 Jun 2025 07:10:35.603 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 07:10:35.604 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 07:10:35.604 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 222227.
12968:M 19 Jun 2025 07:26:44.279 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 19 Jun 2025 07:26:44.280 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 07:26:44.296 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 07:26:44.298 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 222283.
12968:M 19 Jun 2025 07:32:44.806 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 19 Jun 2025 07:32:44.806 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 07:32:44.809 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 07:32:44.809 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 222353.
12968:M 19 Jun 2025 07:48:37.527 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 19 Jun 2025 07:48:37.527 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 07:48:37.530 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 07:48:37.530 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 222423.
12968:M 19 Jun 2025 08:04:46.174 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 08:04:46.178 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 08:04:46.179 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 222479.
12968:M 19 Jun 2025 08:20:59.798 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 19 Jun 2025 08:20:59.814 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 08:20:59.829 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 08:20:59.829 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 222535.
12968:M 19 Jun 2025 08:33:44.992 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 19 Jun 2025 08:33:44.993 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 08:33:44.999 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 08:33:44.999 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 222605.
12968:M 19 Jun 2025 08:42:46.031 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 19 Jun 2025 08:42:46.031 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 08:42:46.036 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 08:42:46.036 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 222675.
12968:M 19 Jun 2025 08:50:23.890 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 19 Jun 2025 08:50:23.891 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 08:50:23.895 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 08:50:23.895 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 222731.
12968:M 19 Jun 2025 09:08:41.881 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 19 Jun 2025 09:08:41.881 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 09:08:41.885 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 09:08:41.886 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 222801.
12968:M 19 Jun 2025 09:24:10.774 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 19 Jun 2025 09:24:10.774 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 09:24:10.777 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 09:24:10.777 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 222801.
12968:M 19 Jun 2025 09:34:45.006 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 19 Jun 2025 09:34:45.006 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 09:34:45.013 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 09:34:45.014 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 222899.
12968:M 19 Jun 2025 09:42:03.203 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 19 Jun 2025 09:42:03.203 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 09:42:03.208 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 09:42:03.209 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 222955.
12968:M 19 Jun 2025 09:59:48.770 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 19 Jun 2025 09:59:48.771 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 09:59:48.775 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 09:59:48.776 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 223081.
12968:M 19 Jun 2025 10:02:19.981 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 19 Jun 2025 10:02:19.982 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 10:02:19.985 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 10:02:19.985 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 223137.
12968:M 19 Jun 2025 10:05:15.515 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 19 Jun 2025 10:05:15.515 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 10:05:15.519 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 10:05:15.519 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 223207.
12968:M 19 Jun 2025 10:09:29.911 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 19 Jun 2025 10:09:29.921 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 10:09:29.927 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 10:09:29.928 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 223263.
12968:M 19 Jun 2025 10:12:47.242 * 1 changes in 3600 seconds. Saving...
12968:M 19 Jun 2025 10:12:47.244 * Background saving started by pid 89468
89468:C 19 Jun 2025 10:12:47.264 * DB saved on disk
89468:C 19 Jun 2025 10:12:47.265 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 19 Jun 2025 10:12:47.346 * Background saving terminated with success
12968:M 19 Jun 2025 11:02:12.942 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 19 Jun 2025 11:02:12.943 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 11:02:12.945 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 11:02:12.945 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 228763.
12968:M 19 Jun 2025 11:05:47.269 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 19 Jun 2025 11:05:47.269 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 11:05:47.275 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 11:05:47.275 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 228833.
12968:M 19 Jun 2025 11:12:48.056 * 1 changes in 3600 seconds. Saving...
12968:M 19 Jun 2025 11:12:48.057 * Background saving started by pid 8341
8341:C 19 Jun 2025 11:12:48.064 * DB saved on disk
8341:C 19 Jun 2025 11:12:48.065 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 19 Jun 2025 11:12:48.159 * Background saving terminated with success
12968:M 19 Jun 2025 13:02:19.428 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 19 Jun 2025 13:02:19.550 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 13:02:19.566 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 13:02:19.567 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 238157.
12968:M 19 Jun 2025 13:19:27.333 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 19 Jun 2025 13:19:27.334 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 13:19:27.341 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 13:19:27.341 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 40 bytes of backlog starting from offset 238213.
12968:M 19 Jun 2025 13:26:21.462 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 19 Jun 2025 13:26:21.463 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 13:26:21.472 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 13:26:21.473 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 238533.
12968:M 19 Jun 2025 13:41:43.334 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 19 Jun 2025 13:41:43.357 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 13:41:43.371 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 13:41:43.371 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 239583.
12968:M 19 Jun 2025 14:02:08.517 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 14:02:08.518 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 14:02:08.519 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 239947.
12968:M 19 Jun 2025 14:05:51.130 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 14:05:51.132 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 14:05:51.132 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 240017.
12968:M 19 Jun 2025 16:47:58.787 * 1 changes in 3600 seconds. Saving...
12968:M 19 Jun 2025 16:47:58.789 * Background saving started by pid 26151
26151:C 19 Jun 2025 16:47:58.796 * DB saved on disk
26151:C 19 Jun 2025 16:47:58.797 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 19 Jun 2025 16:47:58.890 * Background saving terminated with success
12968:M 19 Jun 2025 18:02:58.778 * 1 changes in 3600 seconds. Saving...
12968:M 19 Jun 2025 18:02:58.780 * Background saving started by pid 61951
61951:C 19 Jun 2025 18:02:58.786 * DB saved on disk
61951:C 19 Jun 2025 18:02:58.787 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 19 Jun 2025 18:02:58.882 * Background saving terminated with success
12968:M 19 Jun 2025 18:07:59.011 * 100 changes in 300 seconds. Saving...
12968:M 19 Jun 2025 18:07:59.014 * Background saving started by pid 64241
64241:C 19 Jun 2025 18:07:59.023 * DB saved on disk
64241:C 19 Jun 2025 18:07:59.024 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 19 Jun 2025 18:07:59.117 * Background saving terminated with success
12968:M 19 Jun 2025 19:08:00.098 * 1 changes in 3600 seconds. Saving...
12968:M 19 Jun 2025 19:08:00.102 * Background saving started by pid 88262
88262:C 19 Jun 2025 19:08:00.111 * DB saved on disk
88262:C 19 Jun 2025 19:08:00.112 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 19 Jun 2025 19:08:00.205 * Background saving terminated with success
12968:M 19 Jun 2025 21:45:57.066 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 21:45:57.069 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 21:45:57.069 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 280895.
12968:M 19 Jun 2025 22:02:10.964 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 19 Jun 2025 22:02:10.964 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 22:02:10.967 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 22:02:10.968 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 280909.
12968:M 19 Jun 2025 22:18:27.888 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 22:18:27.890 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 22:18:27.890 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 280909.
12968:M 19 Jun 2025 22:35:28.787 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 19 Jun 2025 22:35:28.787 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 22:35:28.792 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 22:35:28.792 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 280923.
12968:M 19 Jun 2025 22:46:45.391 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 19 Jun 2025 22:46:45.391 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 22:46:45.394 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 22:46:45.394 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 280923.
12968:M 19 Jun 2025 23:02:57.251 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 23:02:57.251 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 23:02:57.251 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 280993.
12968:M 19 Jun 2025 23:06:06.941 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 19 Jun 2025 23:06:07.059 * Connection with replica 127.0.0.1:7104 lost.
12968:M 19 Jun 2025 23:06:07.061 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 19 Jun 2025 23:06:07.061 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 281119.
12968:M 19 Jun 2025 23:31:57.090 * 1 changes in 3600 seconds. Saving...
12968:M 19 Jun 2025 23:31:57.091 * Background saving started by pid 53955
53955:C 19 Jun 2025 23:31:57.100 * DB saved on disk
53955:C 19 Jun 2025 23:31:57.101 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 19 Jun 2025 23:31:57.193 * Background saving terminated with success
12968:M 20 Jun 2025 02:02:51.513 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 02:02:51.523 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 02:02:51.524 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 296097.
12968:M 20 Jun 2025 02:13:57.543 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 02:13:57.545 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 02:13:57.546 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 296111.
12968:M 20 Jun 2025 02:31:48.361 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 20 Jun 2025 02:31:48.492 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 02:31:48.494 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 02:31:48.495 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 296167.
12968:M 20 Jun 2025 02:47:37.836 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 20 Jun 2025 02:47:37.850 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 02:47:37.862 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 02:47:37.863 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 296249.
12968:M 20 Jun 2025 03:03:51.500 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 03:03:51.503 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 03:03:51.503 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 296305.
12968:M 20 Jun 2025 03:20:03.340 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 03:20:03.342 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 03:20:03.342 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 296375.
12968:M 20 Jun 2025 03:36:16.159 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 03:36:16.173 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 03:36:16.174 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 296431.
12968:M 20 Jun 2025 03:46:00.504 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 03:46:00.506 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 03:46:00.507 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 296501.
12968:M 20 Jun 2025 04:02:13.365 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 20 Jun 2025 04:02:13.400 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 04:02:13.406 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 04:02:13.406 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 296571.
12968:M 20 Jun 2025 04:18:26.457 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 20 Jun 2025 04:18:26.458 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 04:18:26.466 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 04:18:26.466 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 296627.
12968:M 20 Jun 2025 04:34:39.208 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 20 Jun 2025 04:34:39.209 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 04:34:39.214 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 04:34:39.214 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 296697.
12968:M 20 Jun 2025 04:50:52.001 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 20 Jun 2025 04:50:52.013 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 04:50:52.030 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 04:50:52.030 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 296767.
12968:M 20 Jun 2025 05:06:42.397 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 05:06:42.399 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 05:06:42.399 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 296823.
12968:M 20 Jun 2025 05:22:52.097 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 20 Jun 2025 05:22:52.097 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 05:22:52.100 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 05:22:52.100 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 296893.
12968:M 20 Jun 2025 05:39:04.939 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 20 Jun 2025 05:39:04.940 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 05:39:04.943 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 05:39:04.943 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 296949.
12968:M 20 Jun 2025 05:55:17.910 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 20 Jun 2025 05:55:17.910 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 05:55:17.914 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 05:55:17.914 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 297019.
12968:M 20 Jun 2025 06:11:30.701 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 20 Jun 2025 06:11:30.701 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 06:11:30.703 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 06:11:30.704 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 297089.
12968:M 20 Jun 2025 06:27:42.860 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 06:27:42.864 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 06:27:42.871 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 297145.
12968:M 20 Jun 2025 06:43:56.341 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 20 Jun 2025 06:43:56.342 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 06:43:56.347 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 06:43:56.348 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 297215.
12968:M 20 Jun 2025 07:00:09.262 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 07:00:09.264 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 07:00:09.264 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 297285.
12968:M 20 Jun 2025 07:16:22.704 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 20 Jun 2025 07:16:22.704 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 07:16:22.717 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 07:16:22.717 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 297341.
12968:M 20 Jun 2025 07:33:30.374 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 20 Jun 2025 07:33:30.374 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 07:33:30.390 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 07:33:30.390 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 297467.
12968:M 20 Jun 2025 07:40:52.479 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 07:40:52.496 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 07:40:52.497 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 297537.
12968:M 20 Jun 2025 07:57:06.068 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 20 Jun 2025 07:57:06.068 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 07:57:06.073 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 07:57:06.073 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 297607.
12968:M 20 Jun 2025 08:13:16.541 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 08:13:16.542 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 08:13:16.542 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 297663.
12968:M 20 Jun 2025 08:29:25.924 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 20 Jun 2025 08:29:25.924 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 08:29:25.927 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 08:29:25.927 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 297733.
12968:M 20 Jun 2025 08:46:31.177 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 20 Jun 2025 08:46:31.178 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 08:46:31.182 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 08:46:31.182 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 297859.
12968:M 20 Jun 2025 09:02:22.377 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 20 Jun 2025 09:02:22.386 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 09:02:22.404 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 09:02:22.405 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 297915.
12968:M 20 Jun 2025 09:04:10.609 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 09:04:10.611 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 09:04:10.611 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 297985.
12968:M 20 Jun 2025 09:21:58.464 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 09:21:58.466 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 09:21:58.466 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 298041.
12968:M 20 Jun 2025 09:39:40.441 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 09:39:40.443 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 09:39:40.443 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 298139.
12968:M 20 Jun 2025 09:41:52.362 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 09:41:52.381 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 09:41:52.381 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 298139.
12968:M 20 Jun 2025 09:52:01.360 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 09:52:01.363 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 09:52:01.364 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 298209.
12968:M 20 Jun 2025 13:09:00.555 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 20 Jun 2025 13:09:00.555 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 13:09:00.558 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 13:09:00.559 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 313245.
12968:M 20 Jun 2025 13:44:50.396 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 20 Jun 2025 13:44:50.396 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 13:44:50.423 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 13:44:50.423 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 314967.
12968:M 20 Jun 2025 14:07:23.127 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 20 Jun 2025 14:07:23.127 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 14:07:23.131 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 14:07:23.131 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 315765.
12968:M 20 Jun 2025 17:45:00.571 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 20 Jun 2025 17:45:00.571 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 17:45:00.574 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 17:45:00.574 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 333139.
12968:M 20 Jun 2025 17:54:03.842 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 17:54:03.844 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 17:54:03.844 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 333755.
12968:M 20 Jun 2025 18:29:47.728 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 18:29:47.741 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 18:29:47.742 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 335729.
12968:M 20 Jun 2025 18:52:57.695 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 20 Jun 2025 18:52:57.696 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 18:52:57.699 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 18:52:57.699 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 336387.
12968:M 20 Jun 2025 19:48:27.435 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 19:48:27.437 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 19:48:27.437 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 339658.
12968:M 20 Jun 2025 20:02:20.353 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 20 Jun 2025 20:02:20.353 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 20:02:20.369 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 20:02:20.370 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 339658.
12968:M 20 Jun 2025 20:13:27.047 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 20:13:27.049 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 20:13:27.049 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 340316.
12968:M 20 Jun 2025 20:15:34.848 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 20:15:34.851 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 20:15:34.852 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 340372.
12968:M 20 Jun 2025 20:33:46.942 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 20:33:46.945 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 20:33:46.945 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 340442.
12968:M 20 Jun 2025 20:50:05.842 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 20:50:05.846 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 20:50:05.846 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 340442.
12968:M 20 Jun 2025 21:05:35.733 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 21:05:35.736 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 21:05:35.736 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 340456.
12968:M 20 Jun 2025 21:16:34.636 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 21:16:34.639 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 21:16:34.639 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 340456.
12968:M 20 Jun 2025 21:35:18.767 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 21:35:18.770 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 21:35:18.771 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 340526.
12968:M 20 Jun 2025 21:48:41.301 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 21:48:41.303 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 21:48:41.303 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 340526.
12968:M 20 Jun 2025 22:05:42.645 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 22:05:42.648 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 22:05:42.648 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 340666.
12968:M 20 Jun 2025 22:17:35.034 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 22:17:35.036 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 22:17:35.037 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 340792.
12968:M 20 Jun 2025 22:22:19.691 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 22:22:19.694 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 22:22:19.694 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 340848.
12968:M 20 Jun 2025 22:40:15.956 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 20 Jun 2025 22:40:15.965 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 22:40:15.972 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 22:40:15.973 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 341044.
12968:M 20 Jun 2025 22:53:51.666 * Connection with replica 127.0.0.1:7104 lost.
12968:M 20 Jun 2025 22:53:51.669 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 20 Jun 2025 22:53:51.669 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 341184.
12968:M 20 Jun 2025 22:56:50.968 * 1 changes in 3600 seconds. Saving...
12968:M 20 Jun 2025 22:56:50.973 * Background saving started by pid 10947
10947:C 20 Jun 2025 22:56:50.989 * DB saved on disk
10947:C 20 Jun 2025 22:56:50.990 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 20 Jun 2025 22:56:51.074 * Background saving terminated with success
12968:M 20 Jun 2025 23:56:52.016 * 1 changes in 3600 seconds. Saving...
12968:M 20 Jun 2025 23:56:52.017 * Background saving started by pid 34719
34719:C 20 Jun 2025 23:56:52.028 * DB saved on disk
34719:C 20 Jun 2025 23:56:52.029 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 20 Jun 2025 23:56:52.120 * Background saving terminated with success
12968:M 21 Jun 2025 01:25:28.906 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 21 Jun 2025 01:25:28.906 * Connection with replica 127.0.0.1:7104 lost.
12968:M 21 Jun 2025 01:25:28.908 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 21 Jun 2025 01:25:28.909 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 354230.
12968:M 21 Jun 2025 09:02:20.997 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 21 Jun 2025 09:02:20.998 * Connection with replica 127.0.0.1:7104 lost.
12968:M 21 Jun 2025 09:02:21.000 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 21 Jun 2025 09:02:21.001 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 391636.
12968:M 21 Jun 2025 09:21:45.652 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 21 Jun 2025 09:21:45.652 * Connection with replica 127.0.0.1:7104 lost.
12968:M 21 Jun 2025 09:21:45.656 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 21 Jun 2025 09:21:45.657 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 391958.
12968:M 21 Jun 2025 09:39:33.483 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 21 Jun 2025 09:39:33.483 * Connection with replica 127.0.0.1:7104 lost.
12968:M 21 Jun 2025 09:39:33.486 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 21 Jun 2025 09:39:33.487 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 392154.
12968:M 21 Jun 2025 09:55:39.539 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 21 Jun 2025 09:55:39.539 * Connection with replica 127.0.0.1:7104 lost.
12968:M 21 Jun 2025 09:55:39.543 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 21 Jun 2025 09:55:39.543 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 392210.
12968:M 21 Jun 2025 10:19:31.558 * Connection with replica 127.0.0.1:7104 lost.
12968:M 21 Jun 2025 10:19:31.559 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 21 Jun 2025 10:19:31.559 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 392910.
12968:M 21 Jun 2025 10:22:14.407 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 21 Jun 2025 10:22:14.407 * Connection with replica 127.0.0.1:7104 lost.
12968:M 21 Jun 2025 10:22:14.410 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 21 Jun 2025 10:22:14.410 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 392966.
12968:M 21 Jun 2025 10:24:42.056 * Connection with replica 127.0.0.1:7104 lost.
12968:M 21 Jun 2025 10:24:42.061 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 21 Jun 2025 10:24:42.062 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 393078.
12968:M 21 Jun 2025 10:44:31.608 * 1 changes in 3600 seconds. Saving...
12968:M 21 Jun 2025 10:44:31.609 * Background saving started by pid 40600
40600:C 21 Jun 2025 10:44:31.618 * DB saved on disk
40600:C 21 Jun 2025 10:44:31.620 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 21 Jun 2025 10:44:31.711 * Background saving terminated with success
12968:M 21 Jun 2025 14:16:45.496 * 1 changes in 3600 seconds. Saving...
12968:M 21 Jun 2025 14:16:45.497 * Background saving started by pid 28802
28802:C 21 Jun 2025 14:16:45.503 * DB saved on disk
28802:C 21 Jun 2025 14:16:45.504 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 21 Jun 2025 14:16:45.598 * Background saving terminated with success
12968:M 21 Jun 2025 17:23:13.527 * 1 changes in 3600 seconds. Saving...
12968:M 21 Jun 2025 17:23:13.529 * Background saving started by pid 3401
3401:C 21 Jun 2025 17:23:13.538 * DB saved on disk
3401:C 21 Jun 2025 17:23:13.538 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 21 Jun 2025 17:23:13.631 * Background saving terminated with success
12968:M 22 Jun 2025 10:20:07.695 * 1 changes in 3600 seconds. Saving...
12968:M 22 Jun 2025 10:20:07.704 * Background saving started by pid 78734
78734:C 22 Jun 2025 10:20:07.719 * DB saved on disk
78734:C 22 Jun 2025 10:20:07.722 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 22 Jun 2025 10:20:07.807 * Background saving terminated with success
12968:M 22 Jun 2025 11:20:08.086 * 1 changes in 3600 seconds. Saving...
12968:M 22 Jun 2025 11:20:08.088 * Background saving started by pid 4055
4055:C 22 Jun 2025 11:20:08.093 * DB saved on disk
4055:C 22 Jun 2025 11:20:08.098 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 22 Jun 2025 11:20:08.189 * Background saving terminated with success
12968:M 22 Jun 2025 16:14:36.533 * 1 changes in 3600 seconds. Saving...
12968:M 22 Jun 2025 16:14:36.534 * Background saving started by pid 12837
12837:C 22 Jun 2025 16:14:36.548 * DB saved on disk
12837:C 22 Jun 2025 16:14:36.548 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 22 Jun 2025 16:14:36.642 * Background saving terminated with success
12968:M 22 Jun 2025 17:14:37.085 * 1 changes in 3600 seconds. Saving...
12968:M 22 Jun 2025 17:14:37.087 * Background saving started by pid 36097
36097:C 22 Jun 2025 17:14:37.100 * DB saved on disk
36097:C 22 Jun 2025 17:14:37.101 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 22 Jun 2025 17:14:37.189 * Background saving terminated with success
12968:M 22 Jun 2025 19:46:43.928 * 1 changes in 3600 seconds. Saving...
12968:M 22 Jun 2025 19:46:43.930 * Background saving started by pid 91407
91407:C 22 Jun 2025 19:46:43.937 * DB saved on disk
91407:C 22 Jun 2025 19:46:43.937 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 22 Jun 2025 19:46:44.031 * Background saving terminated with success
12968:M 22 Jun 2025 23:43:16.460 * 1 changes in 3600 seconds. Saving...
12968:M 22 Jun 2025 23:43:16.461 * Background saving started by pid 84654
84654:C 22 Jun 2025 23:43:16.468 * DB saved on disk
84654:C 22 Jun 2025 23:43:16.470 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 22 Jun 2025 23:43:16.562 * Background saving terminated with success
12968:M 23 Jun 2025 08:46:05.474 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 23 Jun 2025 08:46:05.475 * Connection with replica 127.0.0.1:7104 lost.
12968:M 23 Jun 2025 08:46:05.478 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 23 Jun 2025 08:46:05.478 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 636756.
12968:M 23 Jun 2025 09:03:37.809 * Connection with replica 127.0.0.1:7104 lost.
12968:M 23 Jun 2025 09:03:37.814 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 23 Jun 2025 09:03:37.814 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 636812.
12968:M 23 Jun 2025 09:14:33.784 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 23 Jun 2025 09:14:33.821 * Connection with replica 127.0.0.1:7104 lost.
12968:M 23 Jun 2025 09:14:33.841 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 23 Jun 2025 09:14:33.841 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 636826.
12968:M 23 Jun 2025 09:32:26.799 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 23 Jun 2025 09:32:26.816 * Connection with replica 127.0.0.1:7104 lost.
12968:M 23 Jun 2025 09:32:26.829 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 23 Jun 2025 09:32:26.829 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 636882.
12968:M 23 Jun 2025 09:49:52.809 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 23 Jun 2025 09:49:52.837 * Connection with replica 127.0.0.1:7104 lost.
12968:M 23 Jun 2025 09:49:52.839 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 23 Jun 2025 09:49:52.840 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 636896.
12968:M 23 Jun 2025 10:05:31.020 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 23 Jun 2025 10:05:31.023 * Connection with replica 127.0.0.1:7104 lost.
12968:M 23 Jun 2025 10:05:31.032 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 23 Jun 2025 10:05:31.032 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 637932.
12968:M 23 Jun 2025 10:30:21.560 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 23 Jun 2025 10:30:21.561 * Connection with replica 127.0.0.1:7104 lost.
12968:M 23 Jun 2025 10:30:21.566 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 23 Jun 2025 10:30:21.567 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 639024.
12968:M 23 Jun 2025 10:31:08.882 * 1 changes in 3600 seconds. Saving...
12968:M 23 Jun 2025 10:31:08.884 * Background saving started by pid 1331
1331:C 23 Jun 2025 10:31:08.895 * DB saved on disk
1331:C 23 Jun 2025 10:31:08.896 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 23 Jun 2025 10:31:08.986 * Background saving terminated with success
12968:M 23 Jun 2025 12:17:01.947 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 23 Jun 2025 12:17:01.948 * Connection with replica 127.0.0.1:7104 lost.
12968:M 23 Jun 2025 12:17:01.952 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 23 Jun 2025 12:17:01.952 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 648374.
12968:M 23 Jun 2025 12:23:31.873 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 23 Jun 2025 12:23:31.874 * Connection with replica 127.0.0.1:7104 lost.
12968:M 23 Jun 2025 12:23:31.876 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 23 Jun 2025 12:23:31.876 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 648486.
12968:M 23 Jun 2025 12:51:04.823 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 23 Jun 2025 12:51:04.828 * Connection with replica 127.0.0.1:7104 lost.
12968:M 23 Jun 2025 12:51:04.834 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 23 Jun 2025 12:51:04.835 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 649774.
12968:M 23 Jun 2025 13:24:05.964 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 23 Jun 2025 13:24:05.965 * Connection with replica 127.0.0.1:7104 lost.
12968:M 23 Jun 2025 13:24:05.974 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 23 Jun 2025 13:24:05.974 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 651146.
12968:M 23 Jun 2025 13:51:45.760 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 23 Jun 2025 13:51:45.762 * Connection with replica 127.0.0.1:7104 lost.
12968:M 23 Jun 2025 13:51:45.765 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 23 Jun 2025 13:51:45.765 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 652180.
12968:M 23 Jun 2025 15:21:23.563 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 23 Jun 2025 15:21:23.563 * Connection with replica 127.0.0.1:7104 lost.
12968:M 23 Jun 2025 15:21:23.567 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 23 Jun 2025 15:21:23.567 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 658620.
12968:M 23 Jun 2025 15:26:31.006 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
12968:M 23 Jun 2025 15:26:31.006 * Connection with replica 127.0.0.1:7104 lost.
12968:M 23 Jun 2025 15:26:31.010 * Replica 127.0.0.1:7104 asks for synchronization
12968:M 23 Jun 2025 15:26:31.010 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 658690.
12968:M 23 Jun 2025 15:30:58.680 * 1 changes in 3600 seconds. Saving...
12968:M 23 Jun 2025 15:30:58.682 * Background saving started by pid 88072
88072:C 23 Jun 2025 15:30:58.695 * DB saved on disk
88072:C 23 Jun 2025 15:30:58.696 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12968:M 23 Jun 2025 15:30:58.784 * Background saving terminated with success
12968:signal-handler (1750669757) Received SIGTERM scheduling shutdown...
12968:M 23 Jun 2025 17:09:17.282 * User requested shutdown...
12968:M 23 Jun 2025 17:09:17.283 * 1 of 1 replicas are in sync when shutting down.
12968:M 23 Jun 2025 17:09:17.283 * Calling fsync() on the AOF file.
12968:M 23 Jun 2025 17:09:17.305 * Saving the final RDB snapshot before exiting.
12968:M 23 Jun 2025 17:09:17.330 * DB saved on disk
12968:M 23 Jun 2025 17:09:17.330 * Removing the pid file.
12968:M 23 Jun 2025 17:09:17.344 # Redis is now ready to exit, bye bye...
3146:C 23 Jun 2025 17:13:10.969 * oO0OoO0OoO0Oo Redis is starting oO0OoO0OoO0Oo
3146:C 23 Jun 2025 17:13:10.970 * Redis version=7.2.7, bits=64, commit=00000000, modified=0, pid=3146, just started
3146:C 23 Jun 2025 17:13:10.970 * Configuration loaded
3146:M 23 Jun 2025 17:13:10.971 * Increased maximum number of open files to 10032 (it was originally set to 256).
3146:M 23 Jun 2025 17:13:10.971 * monotonic clock: POSIX clock_gettime
3146:M 23 Jun 2025 17:13:10.972 * Running mode=cluster, port=7102.
3146:M 23 Jun 2025 17:13:10.972 # WARNING: The TCP backlog setting of 511 cannot be enforced because kern.ipc.somaxconn is set to the lower value of 128.
3146:M 23 Jun 2025 17:13:10.972 * Node configuration loaded, I'm 15f04fa7dabe54f878e48e88868b872357d1c0de
3146:M 23 Jun 2025 17:13:10.972 * Server initialized
3146:M 23 Jun 2025 17:13:10.973 * Reading RDB base file on AOF loading...
3146:M 23 Jun 2025 17:13:10.973 * Loading RDB produced by version 7.2.7
3146:M 23 Jun 2025 17:13:10.973 * RDB age 1219119 seconds
3146:M 23 Jun 2025 17:13:10.974 * RDB memory usage when created 1.70 Mb
3146:M 23 Jun 2025 17:13:10.974 * RDB is base AOF
3146:M 23 Jun 2025 17:13:10.974 * Done loading RDB, keys loaded: 0, keys expired: 0.
3146:M 23 Jun 2025 17:13:10.974 * DB loaded from base file appendonly-7102.aof.1.base.rdb: 0.001 seconds
3146:M 23 Jun 2025 17:13:10.982 * DB saved on disk
3146:M 23 Jun 2025 17:13:10.986 * DB saved on disk
3146:M 23 Jun 2025 17:13:10.991 * DB saved on disk
3146:M 23 Jun 2025 17:13:10.995 * DB saved on disk
3146:M 23 Jun 2025 17:13:11.001 * DB saved on disk
3146:M 23 Jun 2025 17:13:11.005 * DB saved on disk
3146:M 23 Jun 2025 17:13:11.009 * DB saved on disk
3146:M 23 Jun 2025 17:13:11.014 * DB saved on disk
3146:M 23 Jun 2025 17:13:11.020 * DB saved on disk
3146:M 23 Jun 2025 17:13:11.024 * DB saved on disk
3146:M 23 Jun 2025 17:13:11.029 * DB saved on disk
3146:M 23 Jun 2025 17:13:11.035 * DB saved on disk
3146:M 23 Jun 2025 17:13:11.036 * DB loaded from incr file appendonly-7102.aof.1.incr.aof: 0.062 seconds
3146:M 23 Jun 2025 17:13:11.037 * DB loaded from append only file: 0.064 seconds
3146:M 23 Jun 2025 17:13:11.037 * Opening AOF incr file appendonly-7102.aof.1.incr.aof on server start
3146:M 23 Jun 2025 17:13:11.037 * Ready to accept connections tcp
3146:M 23 Jun 2025 17:13:13.068 * Cluster state changed: ok
3146:M 23 Jun 2025 17:13:15.132 * Replica 127.0.0.1:7104 asks for synchronization
3146:M 23 Jun 2025 17:13:15.132 * Partial resynchronization not accepted: Replication ID mismatch (Replica asked for '74d31d3d10e74b75ad7b103fb44973ce6df66269', my replication IDs are '4f1036791d714e271842f05ea56c3b691778d6ee' and '0000000000000000000000000000000000000000')
3146:M 23 Jun 2025 17:13:15.133 * Replication backlog created, my new replication IDs are 'a8b01e1277807c02612d29d0f1bd8ab433e9fc82' and '0000000000000000000000000000000000000000'
3146:M 23 Jun 2025 17:13:15.133 * Delay next BGSAVE for diskless SYNC
3146:M 23 Jun 2025 17:13:20.086 * Starting BGSAVE for SYNC with target: replicas sockets
3146:M 23 Jun 2025 17:13:20.087 * Background RDB transfer started by pid 3180
3180:C 23 Jun 2025 17:13:20.088 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3146:M 23 Jun 2025 17:13:20.089 * Diskless rdb transfer, done reading from pipe, 1 replicas still up.
3146:M 23 Jun 2025 17:13:20.113 * Background RDB transfer terminated with success
3146:M 23 Jun 2025 17:13:20.114 * Streamed RDB transfer with replica 127.0.0.1:7104 succeeded (socket). Waiting for REPLCONF ACK from replica to enable streaming
3146:M 23 Jun 2025 17:13:20.114 * Synchronization with replica 127.0.0.1:7104 succeeded
3146:M 23 Jun 2025 17:13:22.209 * DB saved on disk
3146:M 23 Jun 2025 17:13:22.214 * configEpoch set to 0 via CLUSTER RESET HARD
3146:M 23 Jun 2025 17:13:22.214 * Node hard reset, now I'm 543daaa188add5e4df5e4862c5a895bbfab6b4aa
3146:M 23 Jun 2025 17:13:22.217 # Cluster state changed: fail
3146:M 23 Jun 2025 17:13:22.275 * Connection with replica 127.0.0.1:7104 lost.
3146:M 23 Jun 2025 17:13:25.361 * configEpoch set to 3 via CLUSTER SET-CONFIG-EPOCH
3146:M 23 Jun 2025 17:13:26.402 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 23 Jun 2025 17:13:26.402 * Partial resynchronization not accepted: Replication ID mismatch (Replica asked for '32d396dbb0e8cd48b62f75c5a87b50b55c2ca33f', my replication IDs are 'a8b01e1277807c02612d29d0f1bd8ab433e9fc82' and '0000000000000000000000000000000000000000')
3146:M 23 Jun 2025 17:13:26.402 * Delay next BGSAVE for diskless SYNC
3146:M 23 Jun 2025 17:13:30.282 * Cluster state changed: ok
3146:M 23 Jun 2025 17:13:31.301 * Starting BGSAVE for SYNC with target: replicas sockets
3146:M 23 Jun 2025 17:13:31.314 * Background RDB transfer started by pid 3229
3229:C 23 Jun 2025 17:13:31.317 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3146:M 23 Jun 2025 17:13:31.318 * Diskless rdb transfer, done reading from pipe, 1 replicas still up.
3146:M 23 Jun 2025 17:13:31.334 * Background RDB transfer terminated with success
3146:M 23 Jun 2025 17:13:31.334 * Streamed RDB transfer with replica 127.0.0.1:7105 succeeded (socket). Waiting for REPLCONF ACK from replica to enable streaming
3146:M 23 Jun 2025 17:13:31.334 * Synchronization with replica 127.0.0.1:7105 succeeded
3146:M 23 Jun 2025 17:56:04.599 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 23 Jun 2025 17:56:04.600 * Connection with replica 127.0.0.1:7105 lost.
3146:M 23 Jun 2025 17:56:04.603 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 23 Jun 2025 17:56:04.603 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 5285.
3146:M 23 Jun 2025 18:02:19.682 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 23 Jun 2025 18:02:19.812 * Connection with replica 127.0.0.1:7105 lost.
3146:M 23 Jun 2025 18:02:19.814 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 23 Jun 2025 18:02:19.814 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 5355.
3146:M 23 Jun 2025 18:19:20.599 * 1 changes in 3600 seconds. Saving...
3146:M 23 Jun 2025 18:19:20.733 * Background saving started by pid 21094
21094:C 23 Jun 2025 18:19:20.757 * DB saved on disk
21094:C 23 Jun 2025 18:19:20.757 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3146:M 23 Jun 2025 18:19:20.836 * Background saving terminated with success
3146:M 23 Jun 2025 18:19:20.836 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 23 Jun 2025 18:19:20.836 * Connection with replica 127.0.0.1:7105 lost.
3146:M 23 Jun 2025 18:19:20.838 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 23 Jun 2025 18:19:20.838 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 5411.
3146:M 23 Jun 2025 18:42:16.810 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 23 Jun 2025 18:42:16.810 * Connection with replica 127.0.0.1:7105 lost.
3146:M 23 Jun 2025 18:42:16.813 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 23 Jun 2025 18:42:16.813 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 6041.
3146:M 23 Jun 2025 19:19:21.023 * 1 changes in 3600 seconds. Saving...
3146:M 23 Jun 2025 19:19:21.026 * Background saving started by pid 37263
37263:C 23 Jun 2025 19:19:21.034 * DB saved on disk
37263:C 23 Jun 2025 19:19:21.036 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3146:M 23 Jun 2025 19:19:21.129 * Background saving terminated with success
3146:M 23 Jun 2025 20:01:12.909 * Connection with replica 127.0.0.1:7105 lost.
3146:M 23 Jun 2025 20:01:12.913 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 23 Jun 2025 20:01:12.914 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 14227.
3146:M 23 Jun 2025 20:16:16.677 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 23 Jun 2025 20:16:16.699 * Connection with replica 127.0.0.1:7105 lost.
3146:M 23 Jun 2025 20:16:16.702 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 23 Jun 2025 20:16:16.702 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 15151.
3146:M 23 Jun 2025 20:19:07.349 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 23 Jun 2025 20:19:07.355 * Connection with replica 127.0.0.1:7105 lost.
3146:M 23 Jun 2025 20:19:07.375 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 23 Jun 2025 20:19:07.376 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 15221.
3146:M 23 Jun 2025 21:16:44.885 * Connection with replica 127.0.0.1:7105 lost.
3146:M 23 Jun 2025 21:16:44.892 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 23 Jun 2025 21:16:44.893 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 19911.
3146:M 23 Jun 2025 21:33:24.098 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 23 Jun 2025 21:33:24.099 * Connection with replica 127.0.0.1:7105 lost.
3146:M 23 Jun 2025 21:33:24.106 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 23 Jun 2025 21:33:24.106 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 19981.
3146:M 23 Jun 2025 21:51:33.012 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 23 Jun 2025 21:51:33.012 * Connection with replica 127.0.0.1:7105 lost.
3146:M 23 Jun 2025 21:51:33.016 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 23 Jun 2025 21:51:33.016 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 20065.
3146:M 23 Jun 2025 22:07:41.928 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 23 Jun 2025 22:07:41.928 * Connection with replica 127.0.0.1:7105 lost.
3146:M 23 Jun 2025 22:07:41.932 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 23 Jun 2025 22:07:41.932 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 20065.
3146:M 23 Jun 2025 22:24:07.839 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 23 Jun 2025 22:24:07.868 * Connection with replica 127.0.0.1:7105 lost.
3146:M 23 Jun 2025 22:24:07.892 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 23 Jun 2025 22:24:07.892 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 20119.
3146:M 23 Jun 2025 22:39:27.853 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 23 Jun 2025 22:39:27.876 * Connection with replica 127.0.0.1:7105 lost.
3146:M 23 Jun 2025 22:39:27.882 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 23 Jun 2025 22:39:27.882 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 20119.
3146:M 23 Jun 2025 22:43:36.255 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 23 Jun 2025 22:43:36.290 * Connection with replica 127.0.0.1:7105 lost.
3146:M 23 Jun 2025 22:43:36.309 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 23 Jun 2025 22:43:36.309 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 20133.
3146:M 23 Jun 2025 22:51:30.503 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 23 Jun 2025 22:51:30.526 * Connection with replica 127.0.0.1:7105 lost.
3146:M 23 Jun 2025 22:51:30.529 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 23 Jun 2025 22:51:30.530 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 20189.
3146:M 23 Jun 2025 23:20:09.059 * 1 changes in 3600 seconds. Saving...
3146:M 23 Jun 2025 23:20:09.060 * Background saving started by pid 90460
90460:C 23 Jun 2025 23:20:09.067 * DB saved on disk
90460:C 23 Jun 2025 23:20:09.067 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3146:M 23 Jun 2025 23:20:09.162 * Background saving terminated with success
3146:M 24 Jun 2025 09:54:38.801 * Connection with replica 127.0.0.1:7105 lost.
3146:M 24 Jun 2025 09:54:38.904 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 24 Jun 2025 09:54:38.943 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 62843.
3146:M 24 Jun 2025 10:11:35.349 * 1 changes in 3600 seconds. Saving...
3146:M 24 Jun 2025 10:11:35.350 * Background saving started by pid 68390
68390:C 24 Jun 2025 10:11:35.357 * DB saved on disk
68390:C 24 Jun 2025 10:11:35.360 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3146:M 24 Jun 2025 10:11:35.451 * Background saving terminated with success
3146:M 24 Jun 2025 17:55:13.183 * Connection with replica 127.0.0.1:7105 lost.
3146:M 24 Jun 2025 17:55:13.184 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 24 Jun 2025 17:55:13.184 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 103593.
3146:M 24 Jun 2025 19:26:55.103 * 1 changes in 3600 seconds. Saving...
3146:M 24 Jun 2025 19:26:55.105 * Background saving started by pid 57654
57654:C 24 Jun 2025 19:26:55.114 * DB saved on disk
57654:C 24 Jun 2025 19:26:55.115 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3146:M 24 Jun 2025 19:26:55.208 * Background saving terminated with success
3146:M 24 Jun 2025 20:50:51.344 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 24 Jun 2025 20:50:51.344 * Connection with replica 127.0.0.1:7105 lost.
3146:M 24 Jun 2025 20:50:51.348 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 24 Jun 2025 20:50:51.348 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 119565.
3146:M 24 Jun 2025 22:13:06.842 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 24 Jun 2025 22:13:06.844 * Connection with replica 127.0.0.1:7105 lost.
3146:M 24 Jun 2025 22:13:06.851 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 24 Jun 2025 22:13:06.852 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 126299.
3146:M 24 Jun 2025 22:31:42.396 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 24 Jun 2025 22:31:42.397 * Connection with replica 127.0.0.1:7105 lost.
3146:M 24 Jun 2025 22:31:42.407 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 24 Jun 2025 22:31:42.408 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 126395.
3146:M 24 Jun 2025 22:46:59.304 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 24 Jun 2025 22:46:59.304 * Connection with replica 127.0.0.1:7105 lost.
3146:M 24 Jun 2025 22:46:59.312 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 24 Jun 2025 22:46:59.312 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 126409.
3146:M 24 Jun 2025 23:04:31.199 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 24 Jun 2025 23:04:31.199 * Connection with replica 127.0.0.1:7105 lost.
3146:M 24 Jun 2025 23:04:31.203 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 24 Jun 2025 23:04:31.204 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 126409.
3146:M 24 Jun 2025 23:10:30.883 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 24 Jun 2025 23:10:30.883 * Connection with replica 127.0.0.1:7105 lost.
3146:M 24 Jun 2025 23:10:30.887 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 24 Jun 2025 23:10:30.887 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 126423.
3146:M 24 Jun 2025 23:14:58.632 * 1 changes in 3600 seconds. Saving...
3146:M 24 Jun 2025 23:14:58.634 * Background saving started by pid 22634
22634:C 24 Jun 2025 23:14:58.643 * DB saved on disk
22634:C 24 Jun 2025 23:14:58.644 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3146:M 24 Jun 2025 23:14:58.736 * Background saving terminated with success
3146:M 24 Jun 2025 23:30:21.538 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 24 Jun 2025 23:30:21.581 * Connection with replica 127.0.0.1:7105 lost.
3146:M 24 Jun 2025 23:30:21.597 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 24 Jun 2025 23:30:21.597 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 128675.
3146:M 24 Jun 2025 23:47:47.801 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 24 Jun 2025 23:47:47.841 * Connection with replica 127.0.0.1:7105 lost.
3146:M 24 Jun 2025 23:47:47.852 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 24 Jun 2025 23:47:47.853 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 128731.
3146:M 24 Jun 2025 23:48:59.960 * Connection with replica 127.0.0.1:7105 lost.
3146:M 24 Jun 2025 23:48:59.962 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 24 Jun 2025 23:48:59.963 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 128745.
3146:M 25 Jun 2025 02:03:13.409 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 02:03:13.410 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 02:03:13.416 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 02:03:13.416 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 138503.
3146:M 25 Jun 2025 02:19:03.200 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 02:19:03.201 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 02:19:03.215 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 02:19:03.217 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 138543.
3146:M 25 Jun 2025 02:20:41.922 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 02:20:41.922 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 02:20:41.927 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 02:20:41.927 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 138557.
3146:M 25 Jun 2025 02:22:36.260 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 02:22:36.261 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 02:22:36.264 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 02:22:36.264 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 138613.
3146:M 25 Jun 2025 02:39:15.115 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 02:39:15.115 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 02:39:15.129 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 02:39:15.130 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 138627.
3146:M 25 Jun 2025 02:56:03.894 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 02:56:03.894 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 02:56:03.899 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 02:56:03.900 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 138641.
3146:M 25 Jun 2025 03:12:20.716 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 03:12:20.716 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 03:12:20.725 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 03:12:20.725 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 138641.
3146:M 25 Jun 2025 03:29:16.190 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 03:29:16.191 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 03:29:16.195 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 03:29:16.195 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 138739.
3146:M 25 Jun 2025 03:45:28.957 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 03:45:28.957 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 03:45:28.967 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 03:45:28.968 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 138809.
3146:M 25 Jun 2025 04:01:41.770 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 04:01:41.771 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 04:01:41.778 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 04:01:41.779 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 138879.
3146:M 25 Jun 2025 04:17:54.634 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 04:17:54.634 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 04:17:54.639 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 04:17:54.639 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 138935.
3146:M 25 Jun 2025 04:23:40.329 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 04:23:40.330 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 04:23:40.339 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 04:23:40.340 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 139005.
3146:M 25 Jun 2025 04:39:53.114 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 04:39:53.115 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 04:39:53.119 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 04:39:53.119 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 139061.
3146:M 25 Jun 2025 04:56:05.126 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 04:56:05.126 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 04:56:05.138 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 04:56:05.138 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 139131.
3146:M 25 Jun 2025 05:12:17.893 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 05:12:17.893 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 05:12:17.898 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 05:12:17.898 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 139201.
3146:M 25 Jun 2025 05:24:40.422 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 05:24:40.422 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 05:24:40.430 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 05:24:40.431 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 139257.
3146:M 25 Jun 2025 05:40:53.220 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 05:40:53.220 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 05:40:53.226 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 05:40:53.226 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 139327.
3146:M 25 Jun 2025 05:57:06.054 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 05:57:06.054 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 05:57:06.057 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 05:57:06.057 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 139383.
3146:M 25 Jun 2025 06:13:18.878 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 06:13:18.878 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 06:13:18.882 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 06:13:18.882 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 139453.
3146:M 25 Jun 2025 06:25:40.021 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 06:25:40.021 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 06:25:40.024 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 06:25:40.025 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 139523.
3146:M 25 Jun 2025 06:41:52.857 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 06:41:52.857 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 06:41:52.860 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 06:41:52.861 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 139579.
3146:M 25 Jun 2025 06:58:05.691 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 06:58:05.692 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 06:58:05.697 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 06:58:05.698 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 139649.
3146:M 25 Jun 2025 07:14:10.004 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 07:14:10.005 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 07:14:10.009 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 07:14:10.009 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 139705.
3146:M 25 Jun 2025 07:26:39.906 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 07:26:39.906 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 07:26:39.909 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 07:26:39.909 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 139775.
3146:M 25 Jun 2025 07:42:51.892 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 07:42:51.927 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 07:42:51.930 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 07:42:51.930 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 139831.
3146:M 25 Jun 2025 07:59:04.722 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 07:59:04.722 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 07:59:04.727 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 07:59:04.727 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 139901.
3146:M 25 Jun 2025 08:02:20.152 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 08:02:20.152 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 08:02:20.156 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 08:02:20.156 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 139957.
3146:M 25 Jun 2025 08:18:32.934 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 08:18:32.934 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 08:18:32.936 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 08:18:32.936 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 140027.
3146:M 25 Jun 2025 08:27:39.684 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 08:27:39.721 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 08:27:39.729 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 08:27:39.730 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 140097.
3146:M 25 Jun 2025 08:30:28.685 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 08:30:28.721 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 08:30:28.725 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 08:30:28.725 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 140153.
3146:M 25 Jun 2025 08:47:32.030 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 08:47:32.043 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 08:47:32.070 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 08:47:32.070 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 140279.
3146:M 25 Jun 2025 09:01:17.932 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 09:01:17.971 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 09:01:17.988 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 09:01:17.989 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 140349.
3146:M 25 Jun 2025 09:18:56.614 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 09:18:56.614 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 09:18:56.618 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 09:18:56.618 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 140419.
3146:M 25 Jun 2025 09:28:39.490 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 09:28:39.528 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 09:28:39.543 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 09:28:39.543 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 140419.
3146:M 25 Jun 2025 09:44:51.689 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 09:44:51.689 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 09:44:51.692 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 09:44:51.692 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 140489.
3146:M 25 Jun 2025 09:58:08.073 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 09:58:08.074 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 09:58:08.077 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 09:58:08.078 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 140489.
3146:M 25 Jun 2025 10:08:01.515 * 1 changes in 3600 seconds. Saving...
3146:M 25 Jun 2025 10:08:01.519 * Background saving started by pid 62397
62397:C 25 Jun 2025 10:08:01.534 * DB saved on disk
62397:C 25 Jun 2025 10:08:01.535 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3146:M 25 Jun 2025 10:08:01.620 * Background saving terminated with success
3146:M 25 Jun 2025 14:11:54.496 * 1 changes in 3600 seconds. Saving...
3146:M 25 Jun 2025 14:11:54.497 * Background saving started by pid 57533
57533:C 25 Jun 2025 14:11:54.504 * DB saved on disk
57533:C 25 Jun 2025 14:11:54.504 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3146:M 25 Jun 2025 14:11:54.598 * Background saving terminated with success
3146:M 25 Jun 2025 15:11:55.101 * 1 changes in 3600 seconds. Saving...
3146:M 25 Jun 2025 15:11:55.103 * Background saving started by pid 83664
83664:C 25 Jun 2025 15:11:55.110 * DB saved on disk
83664:C 25 Jun 2025 15:11:55.111 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3146:M 25 Jun 2025 15:11:55.206 * Background saving terminated with success
3146:M 25 Jun 2025 16:11:56.038 * 1 changes in 3600 seconds. Saving...
3146:M 25 Jun 2025 16:11:56.040 * Background saving started by pid 40562
40562:C 25 Jun 2025 16:11:56.049 * DB saved on disk
40562:C 25 Jun 2025 16:11:56.050 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3146:M 25 Jun 2025 16:11:56.143 * Background saving terminated with success
3146:M 25 Jun 2025 17:11:57.054 * 1 changes in 3600 seconds. Saving...
3146:M 25 Jun 2025 17:11:57.056 * Background saving started by pid 94733
94733:C 25 Jun 2025 17:11:57.063 * DB saved on disk
94733:C 25 Jun 2025 17:11:57.071 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3146:M 25 Jun 2025 17:11:57.158 * Background saving terminated with success
3146:M 25 Jun 2025 18:10:22.738 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 18:10:22.859 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 18:10:22.861 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 18:10:22.862 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 200330.
3146:M 25 Jun 2025 18:11:58.027 * 1 changes in 3600 seconds. Saving...
3146:M 25 Jun 2025 18:11:58.029 * Background saving started by pid 11881
11881:C 25 Jun 2025 18:11:58.045 * DB saved on disk
11881:C 25 Jun 2025 18:11:58.046 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3146:M 25 Jun 2025 18:11:58.131 * Background saving terminated with success
3146:M 25 Jun 2025 19:45:02.846 * 1 changes in 3600 seconds. Saving...
3146:M 25 Jun 2025 19:45:02.850 * Background saving started by pid 46981
46981:C 25 Jun 2025 19:45:02.863 * DB saved on disk
46981:C 25 Jun 2025 19:45:02.866 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3146:M 25 Jun 2025 19:45:02.955 * Background saving terminated with success
3146:M 25 Jun 2025 20:45:03.014 * 1 changes in 3600 seconds. Saving...
3146:M 25 Jun 2025 20:45:03.018 * Background saving started by pid 69782
69782:C 25 Jun 2025 20:45:03.026 * DB saved on disk
69782:C 25 Jun 2025 20:45:03.027 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3146:M 25 Jun 2025 20:45:03.119 * Background saving terminated with success
3146:M 25 Jun 2025 21:48:06.173 * 1 changes in 3600 seconds. Saving...
3146:M 25 Jun 2025 21:48:06.174 * Background saving started by pid 94749
94749:C 25 Jun 2025 21:48:06.181 * DB saved on disk
94749:C 25 Jun 2025 21:48:06.181 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3146:M 25 Jun 2025 21:48:06.277 * Background saving terminated with success
3146:M 25 Jun 2025 22:49:16.128 * 1 changes in 3600 seconds. Saving...
3146:M 25 Jun 2025 22:49:16.129 * Background saving started by pid 21157
21157:C 25 Jun 2025 22:49:16.135 * DB saved on disk
21157:C 25 Jun 2025 22:49:16.141 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3146:M 25 Jun 2025 22:49:16.230 * Background saving terminated with success
3146:M 25 Jun 2025 22:49:16.230 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 22:49:16.230 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 22:49:16.231 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 22:49:16.231 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 230339.
3146:M 25 Jun 2025 23:06:53.249 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 23:06:53.250 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 23:06:53.259 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 23:06:53.260 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 230409.
3146:M 25 Jun 2025 23:24:51.173 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 23:24:51.173 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 23:24:51.176 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 23:24:51.176 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 230409.
3146:M 25 Jun 2025 23:42:13.059 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 23:42:13.059 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 23:42:13.063 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 23:42:13.063 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 230423.
3146:M 25 Jun 2025 23:57:24.067 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 25 Jun 2025 23:57:24.068 * Connection with replica 127.0.0.1:7105 lost.
3146:M 25 Jun 2025 23:57:24.072 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 25 Jun 2025 23:57:24.072 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 230423.
3146:M 26 Jun 2025 00:12:52.433 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 00:12:52.433 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 00:12:52.441 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 00:12:52.442 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 230507.
3146:M 26 Jun 2025 00:23:10.263 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 00:23:10.263 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 00:23:10.281 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 00:23:10.281 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 230577.
3146:M 26 Jun 2025 00:39:22.132 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 00:39:22.132 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 00:39:22.135 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 00:39:22.136 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 230647.
3146:M 26 Jun 2025 00:49:00.071 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 00:49:00.071 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 00:49:00.074 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 00:49:00.074 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 230788.
3146:M 26 Jun 2025 00:57:41.932 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 00:57:41.952 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 00:57:41.957 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 00:57:41.957 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 230914.
3146:M 26 Jun 2025 02:13:49.074 * 1 changes in 3600 seconds. Saving...
3146:M 26 Jun 2025 02:13:49.075 * Background saving started by pid 54877
54877:C 26 Jun 2025 02:13:49.081 * DB saved on disk
54877:C 26 Jun 2025 02:13:49.082 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3146:M 26 Jun 2025 02:13:49.177 * Background saving terminated with success
3146:M 26 Jun 2025 03:17:54.992 * 1 changes in 3600 seconds. Saving...
3146:M 26 Jun 2025 03:17:54.993 * Background saving started by pid 75038
75038:C 26 Jun 2025 03:17:55.010 * DB saved on disk
75038:C 26 Jun 2025 03:17:55.011 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3146:M 26 Jun 2025 03:17:55.096 * Background saving terminated with success
3146:M 26 Jun 2025 03:17:55.609 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 03:17:55.609 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 03:17:55.615 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 03:17:55.616 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 244089.
3146:M 26 Jun 2025 03:34:07.519 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 03:34:07.519 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 03:34:07.523 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 03:34:07.524 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 244159.
3146:M 26 Jun 2025 03:50:07.613 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 03:50:07.749 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 03:50:07.751 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 03:50:07.752 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 244215.
3146:M 26 Jun 2025 04:06:16.632 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 04:06:16.633 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 04:06:16.641 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 04:06:16.641 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 244271.
3146:M 26 Jun 2025 04:22:28.559 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 04:22:28.559 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 04:22:28.561 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 04:22:28.561 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 244341.
3146:M 26 Jun 2025 04:38:42.264 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 04:38:42.264 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 04:38:42.268 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 04:38:42.268 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 244411.
3146:M 26 Jun 2025 04:54:54.973 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 04:54:54.973 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 04:54:54.979 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 04:54:54.980 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 244467.
3146:M 26 Jun 2025 05:02:01.188 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 05:02:01.188 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 05:02:01.203 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 05:02:01.203 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 244537.
3146:M 26 Jun 2025 05:18:13.918 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 05:18:13.918 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 05:18:13.921 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 05:18:13.921 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 244688.
3146:M 26 Jun 2025 05:34:26.644 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 05:34:26.644 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 05:34:26.648 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 05:34:26.648 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 244744.
3146:M 26 Jun 2025 05:50:39.255 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 05:50:39.256 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 05:50:39.257 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 05:50:39.257 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 244814.
3146:M 26 Jun 2025 06:03:00.726 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 06:03:00.765 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 06:03:00.769 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 06:03:00.769 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 244870.
3146:M 26 Jun 2025 06:19:13.477 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 06:19:13.609 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 06:19:13.612 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 06:19:13.612 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 244940.
3146:M 26 Jun 2025 06:35:25.479 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 06:35:25.614 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 06:35:25.617 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 06:35:25.617 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 245010.
3146:M 26 Jun 2025 06:51:38.438 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 06:51:38.438 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 06:51:38.441 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 06:51:38.441 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 245066.
3146:M 26 Jun 2025 07:04:01.568 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 07:04:01.569 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 07:04:01.578 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 07:04:01.579 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 245136.
3146:M 26 Jun 2025 07:20:14.379 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 07:20:14.380 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 07:20:14.382 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 07:20:14.382 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 245192.
3146:M 26 Jun 2025 07:36:26.979 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 07:36:26.979 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 07:36:26.994 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 07:36:26.994 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 245262.
3146:M 26 Jun 2025 07:51:27.956 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 07:51:27.958 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 07:51:27.959 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 245332.
3146:M 26 Jun 2025 08:05:01.331 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 08:05:01.336 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 08:05:01.337 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 245388.
3146:M 26 Jun 2025 08:21:14.044 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 08:21:14.046 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 08:21:14.047 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 245458.
3146:M 26 Jun 2025 08:37:26.768 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 08:37:26.771 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 08:37:26.772 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 245528.
3146:M 26 Jun 2025 08:53:39.524 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 08:53:39.526 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 08:53:39.527 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 245584.
3146:M 26 Jun 2025 08:57:52.726 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 08:57:52.726 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 08:57:52.729 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 08:57:52.730 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 245654.
3146:M 26 Jun 2025 09:00:46.610 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 09:00:46.610 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 09:00:46.615 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 09:00:46.615 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 245710.
3146:M 26 Jun 2025 09:06:00.891 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 09:06:00.891 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 09:06:00.894 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 09:06:00.894 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 245780.
3146:M 26 Jun 2025 09:22:55.935 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 09:22:55.935 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 09:22:55.940 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 09:22:55.940 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 245850.
3146:M 26 Jun 2025 09:40:41.906 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 09:40:41.906 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 09:40:41.911 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 09:40:41.911 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 245850.
3146:M 26 Jun 2025 09:56:17.815 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 09:56:17.854 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 09:56:17.866 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 09:56:17.866 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 245864.
3146:M 26 Jun 2025 09:59:33.238 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 09:59:33.238 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 09:59:33.241 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 09:59:33.241 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 245948.
3146:M 26 Jun 2025 10:03:37.268 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 10:03:37.268 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 10:03:37.272 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 10:03:37.273 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 246004.
3146:M 26 Jun 2025 10:47:36.641 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 10:47:36.642 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 10:47:36.647 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 10:47:36.647 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 248286.
3146:M 26 Jun 2025 11:51:23.088 * 1 changes in 3600 seconds. Saving...
3146:M 26 Jun 2025 11:51:23.090 * Background saving started by pid 48126
48126:C 26 Jun 2025 11:51:23.097 * DB saved on disk
48126:C 26 Jun 2025 11:51:23.097 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3146:M 26 Jun 2025 11:51:23.192 * Background saving terminated with success
3146:M 26 Jun 2025 12:29:08.827 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 12:29:08.827 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 12:29:08.835 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 12:29:08.835 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 257086.
3146:M 26 Jun 2025 12:42:30.925 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 12:42:31.051 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 12:42:31.053 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 12:42:31.053 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 257142.
3146:M 26 Jun 2025 12:58:20.673 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 12:58:20.792 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 12:58:20.805 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 12:58:20.809 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 257212.
3146:M 26 Jun 2025 13:21:56.769 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 13:21:56.779 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 13:21:56.779 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 257730.
3146:M 26 Jun 2025 13:45:00.139 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 13:45:00.140 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 13:45:00.146 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 13:45:00.146 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 258234.
3146:M 26 Jun 2025 14:06:29.864 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 14:06:29.864 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 14:06:29.865 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 14:06:29.865 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 258808.
3146:M 26 Jun 2025 14:17:09.056 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 14:17:09.062 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 14:17:09.091 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 14:17:09.091 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 258878.
3146:M 26 Jun 2025 18:33:10.685 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 18:33:10.686 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 18:33:10.686 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 279865.
3146:M 26 Jun 2025 19:19:42.385 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 19:19:42.385 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 19:19:42.388 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 19:19:42.388 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 283617.
3146:M 26 Jun 2025 19:25:49.752 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 19:25:49.770 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 19:25:49.773 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 19:25:49.773 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 283743.
3146:M 26 Jun 2025 19:43:48.854 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 19:43:48.855 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 19:43:48.858 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 19:43:48.858 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 283799.
3146:M 26 Jun 2025 20:00:43.746 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 20:00:43.768 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 20:00:43.786 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 20:00:43.786 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 283813.
3146:M 26 Jun 2025 20:17:32.746 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 20:17:32.760 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 20:17:32.773 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 20:17:32.773 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 283813.
3146:M 26 Jun 2025 20:34:46.632 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 20:34:46.768 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 20:34:46.773 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 20:34:46.777 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 283827.
3146:M 26 Jun 2025 20:50:04.637 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 20:50:04.751 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 20:50:04.756 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 20:50:04.756 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 283827.
3146:M 26 Jun 2025 20:56:37.989 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 20:56:37.991 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 20:56:37.991 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 283911.
3146:M 26 Jun 2025 21:20:32.344 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 26 Jun 2025 21:20:32.350 * Connection with replica 127.0.0.1:7105 lost.
3146:M 26 Jun 2025 21:20:32.365 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 26 Jun 2025 21:20:32.365 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 285507.
3146:M 26 Jun 2025 21:38:29.932 * 1 changes in 3600 seconds. Saving...
3146:M 26 Jun 2025 21:38:29.933 * Background saving started by pid 12470
12470:C 26 Jun 2025 21:38:29.939 * DB saved on disk
12470:C 26 Jun 2025 21:38:29.951 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3146:M 26 Jun 2025 21:38:30.034 * Background saving terminated with success
3146:M 27 Jun 2025 01:22:13.813 * 1 changes in 3600 seconds. Saving...
3146:M 27 Jun 2025 01:22:13.822 * Background saving started by pid 95881
95881:C 27 Jun 2025 01:22:13.829 * DB saved on disk
95881:C 27 Jun 2025 01:22:13.830 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3146:M 27 Jun 2025 01:22:13.923 * Background saving terminated with success
3146:M 27 Jun 2025 02:05:39.748 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 27 Jun 2025 02:05:39.749 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 02:05:39.752 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 02:05:39.752 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 311912.
3146:M 27 Jun 2025 02:21:52.443 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 27 Jun 2025 02:21:52.443 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 02:21:52.446 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 02:21:52.447 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 311968.
3146:M 27 Jun 2025 02:38:04.861 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 27 Jun 2025 02:38:04.862 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 02:38:04.863 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 02:38:04.863 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 312038.
3146:M 27 Jun 2025 02:54:14.227 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 02:54:14.229 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 02:54:14.229 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 312094.
3146:M 27 Jun 2025 03:10:27.031 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 03:10:27.045 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 03:10:27.046 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 312164.
3146:M 27 Jun 2025 03:26:39.854 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 03:26:39.863 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 03:26:39.868 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 312234.
3146:M 27 Jun 2025 03:38:50.175 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 03:38:50.179 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 03:38:50.179 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 312290.
3146:M 27 Jun 2025 03:50:29.011 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 27 Jun 2025 03:50:29.011 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 03:50:29.014 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 03:50:29.014 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 312360.
3146:M 27 Jun 2025 04:06:37.779 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 27 Jun 2025 04:06:37.779 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 04:06:37.784 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 04:06:37.785 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 312430.
3146:M 27 Jun 2025 04:38:59.999 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 27 Jun 2025 04:39:00.000 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 04:39:00.003 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 04:39:00.003 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 312596.
3146:M 27 Jun 2025 04:55:16.440 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 04:55:16.443 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 04:55:16.443 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 312666.
3146:M 27 Jun 2025 05:11:29.270 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 05:11:29.272 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 05:11:29.272 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 312722.
3146:M 27 Jun 2025 05:27:42.076 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 05:27:42.077 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 05:27:42.077 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 312792.
3146:M 27 Jun 2025 05:43:54.910 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 05:43:54.911 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 05:43:54.911 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 312862.
3146:M 27 Jun 2025 06:00:07.588 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 06:00:07.590 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 06:00:07.590 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 312918.
3146:M 27 Jun 2025 06:16:20.506 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 27 Jun 2025 06:16:20.633 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 06:16:20.636 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 06:16:20.636 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 312988.
3146:M 27 Jun 2025 06:32:33.426 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 27 Jun 2025 06:32:33.427 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 06:32:33.429 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 06:32:33.429 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 313058.
3146:M 27 Jun 2025 06:48:46.111 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 27 Jun 2025 06:48:46.234 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 06:48:46.238 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 06:48:46.238 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 313114.
3146:M 27 Jun 2025 07:04:58.927 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 27 Jun 2025 07:04:58.943 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 07:04:58.946 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 07:04:58.946 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 313184.
3146:M 27 Jun 2025 07:21:11.941 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 27 Jun 2025 07:21:11.941 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 07:21:11.948 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 07:21:11.948 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 313240.
3146:M 27 Jun 2025 07:37:24.649 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 27 Jun 2025 07:37:24.649 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 07:37:24.653 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 07:37:24.653 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 313310.
3146:M 27 Jun 2025 07:42:07.825 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 07:42:07.828 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 07:42:07.828 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 313380.
3146:M 27 Jun 2025 07:55:12.813 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 07:55:12.814 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 07:55:12.815 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 313436.
3146:M 27 Jun 2025 08:11:24.791 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 08:11:24.792 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 08:11:24.792 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 313506.
3146:M 27 Jun 2025 08:27:37.316 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 27 Jun 2025 08:27:37.316 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 08:27:37.319 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 08:27:37.319 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 313562.
3146:M 27 Jun 2025 08:30:17.522 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 08:30:17.538 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 08:30:17.538 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 313632.
3146:M 27 Jun 2025 08:44:38.223 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 08:44:38.224 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 08:44:38.224 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 313702.
3146:M 27 Jun 2025 09:01:28.818 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 09:01:28.823 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 09:01:28.826 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 313758.
3146:M 27 Jun 2025 09:17:15.824 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 09:17:15.826 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 09:17:15.827 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 313772.
3146:M 27 Jun 2025 09:34:20.844 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 09:34:20.845 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 09:34:20.845 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 313772.
3146:M 27 Jun 2025 09:43:07.837 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 09:43:07.843 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 09:43:07.844 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 313786.
3146:M 27 Jun 2025 11:21:30.858 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 11:21:30.860 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 11:21:30.861 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 321612.
3146:M 27 Jun 2025 11:26:42.428 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 11:26:42.429 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 11:26:42.430 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 321668.
3146:M 27 Jun 2025 12:59:42.880 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 12:59:42.882 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 12:59:42.882 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 328122.
3146:M 27 Jun 2025 13:15:55.099 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 13:15:55.106 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 13:15:55.107 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 328220.
3146:M 27 Jun 2025 13:32:19.962 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 13:32:19.964 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 13:32:19.964 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 328276.
3146:M 27 Jun 2025 13:37:05.393 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 27 Jun 2025 13:37:05.393 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 13:37:05.399 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 13:37:05.399 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 328346.
3146:M 27 Jun 2025 13:53:54.277 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 13:53:54.278 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 13:53:54.278 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 328724.
3146:M 27 Jun 2025 14:43:29.001 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 14:43:29.003 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 14:43:29.003 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 331846.
3146:M 27 Jun 2025 16:41:45.690 * 1 changes in 3600 seconds. Saving...
3146:M 27 Jun 2025 16:41:45.695 * Background saving started by pid 42203
42203:C 27 Jun 2025 16:41:45.704 * DB saved on disk
42203:C 27 Jun 2025 16:41:45.712 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3146:M 27 Jun 2025 16:41:45.798 * Background saving terminated with success
3146:M 27 Jun 2025 19:39:04.285 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 19:39:04.288 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 19:39:04.288 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 358144.
3146:M 27 Jun 2025 20:04:52.075 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 20:04:52.077 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 20:04:52.077 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 358408.
3146:M 27 Jun 2025 20:21:14.888 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 27 Jun 2025 20:21:14.888 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 20:21:14.891 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 20:21:14.891 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 358464.
3146:M 27 Jun 2025 20:39:09.784 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 27 Jun 2025 20:39:09.821 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 20:39:09.823 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 20:39:09.832 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 358478.
3146:M 27 Jun 2025 20:56:28.690 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 27 Jun 2025 20:56:28.815 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 20:56:28.818 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 20:56:28.818 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 358478.
3146:M 27 Jun 2025 21:13:47.802 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 27 Jun 2025 21:13:47.836 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 21:13:47.839 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 21:13:47.839 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 358492.
3146:M 27 Jun 2025 21:29:01.810 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 27 Jun 2025 21:29:01.837 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 21:29:01.841 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 21:29:01.842 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 358492.
3146:M 27 Jun 2025 21:38:42.658 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 27 Jun 2025 21:38:42.658 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 21:38:42.670 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 21:38:42.670 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 358576.
3146:M 27 Jun 2025 21:54:55.413 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 27 Jun 2025 21:54:55.413 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 21:54:55.421 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 21:54:55.422 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 358632.
3146:M 27 Jun 2025 22:11:07.806 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 27 Jun 2025 22:11:07.807 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 22:11:07.816 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 22:11:07.816 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 358702.
3146:M 27 Jun 2025 22:27:25.177 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 27 Jun 2025 22:27:25.177 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 22:27:25.202 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 22:27:25.202 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 358758.
3146:M 27 Jun 2025 22:39:41.900 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 22:39:41.903 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 22:39:41.903 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 358828.
3146:M 27 Jun 2025 22:55:59.192 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 27 Jun 2025 22:55:59.193 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 22:55:59.196 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 22:55:59.196 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 358898.
3146:M 27 Jun 2025 23:12:11.330 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 27 Jun 2025 23:12:11.330 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 23:12:11.334 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 23:12:11.334 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 358954.
3146:M 27 Jun 2025 23:29:26.576 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 27 Jun 2025 23:29:26.607 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 23:29:26.610 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 23:29:26.610 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 359080.
3146:M 27 Jun 2025 23:40:42.636 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 27 Jun 2025 23:40:42.640 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 23:40:42.645 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 23:40:42.646 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 359220.
3146:M 27 Jun 2025 23:56:55.402 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 27 Jun 2025 23:56:55.402 * Connection with replica 127.0.0.1:7105 lost.
3146:M 27 Jun 2025 23:56:55.406 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 27 Jun 2025 23:56:55.406 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 359276.
3146:M 28 Jun 2025 00:13:08.116 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 28 Jun 2025 00:13:08.116 * Connection with replica 127.0.0.1:7105 lost.
3146:M 28 Jun 2025 00:13:08.129 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 28 Jun 2025 00:13:08.130 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 359346.
3146:M 28 Jun 2025 00:23:32.078 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 28 Jun 2025 00:23:32.079 * Connection with replica 127.0.0.1:7105 lost.
3146:M 28 Jun 2025 00:23:32.083 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 28 Jun 2025 00:23:32.084 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 359402.
3146:M 28 Jun 2025 01:29:04.444 * 1 changes in 3600 seconds. Saving...
3146:M 28 Jun 2025 01:29:04.446 * Background saving started by pid 33412
33412:C 28 Jun 2025 01:29:04.456 * DB saved on disk
33412:C 28 Jun 2025 01:29:04.457 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3146:M 28 Jun 2025 01:29:04.548 * Background saving terminated with success
3146:M 28 Jun 2025 03:06:26.963 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 28 Jun 2025 03:06:26.964 * Connection with replica 127.0.0.1:7105 lost.
3146:M 28 Jun 2025 03:06:26.981 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 28 Jun 2025 03:06:26.981 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 373548.
3146:M 28 Jun 2025 03:22:39.024 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 28 Jun 2025 03:22:39.024 * Connection with replica 127.0.0.1:7105 lost.
3146:M 28 Jun 2025 03:22:39.028 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 28 Jun 2025 03:22:39.028 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 373618.
3146:M 28 Jun 2025 03:38:25.853 * Connection with replica 127.0.0.1:7105 lost.
3146:M 28 Jun 2025 03:38:25.855 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 28 Jun 2025 03:38:25.855 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 373688.
3146:M 28 Jun 2025 03:54:15.782 * Connection with replica 127.0.0.1:7105 lost.
3146:M 28 Jun 2025 03:54:15.785 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 28 Jun 2025 03:54:15.785 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 373744.
3146:M 28 Jun 2025 04:10:46.073 * Connection with replica 127.0.0.1:7105 lost.
3146:M 28 Jun 2025 04:10:46.076 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 28 Jun 2025 04:10:46.076 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 373842.
3146:M 28 Jun 2025 04:17:51.783 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 28 Jun 2025 04:17:51.783 * Connection with replica 127.0.0.1:7105 lost.
3146:M 28 Jun 2025 04:17:51.786 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 28 Jun 2025 04:17:51.786 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 373912.
3146:M 28 Jun 2025 04:46:45.705 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 28 Jun 2025 04:46:45.706 * Connection with replica 127.0.0.1:7105 lost.
3146:M 28 Jun 2025 04:46:45.709 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 28 Jun 2025 04:46:45.713 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 375002.
3146:M 28 Jun 2025 05:13:40.530 * Connection with replica 127.0.0.1:7105 lost.
3146:M 28 Jun 2025 05:13:40.537 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 28 Jun 2025 05:13:40.537 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 375954.
3146:M 28 Jun 2025 05:38:03.807 * Connection with replica 127.0.0.1:7105 lost.
3146:M 28 Jun 2025 05:38:03.823 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 28 Jun 2025 05:38:03.823 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 376654.
3146:M 28 Jun 2025 05:50:52.732 * Connection with replica 127.0.0.1:7105 lost.
3146:M 28 Jun 2025 05:50:52.736 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 28 Jun 2025 05:50:52.736 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 376724.
3146:M 28 Jun 2025 06:09:13.590 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 28 Jun 2025 06:09:13.592 * Connection with replica 127.0.0.1:7105 lost.
3146:M 28 Jun 2025 06:09:13.611 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 28 Jun 2025 06:09:13.611 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 376962.
3146:M 28 Jun 2025 06:27:28.234 * Connection with replica 127.0.0.1:7105 lost.
3146:M 28 Jun 2025 06:27:28.236 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 28 Jun 2025 06:27:28.236 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 377186.
3146:M 28 Jun 2025 06:43:40.187 * Connection with replica 127.0.0.1:7105 lost.
3146:M 28 Jun 2025 06:43:40.190 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 28 Jun 2025 06:43:40.190 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 377256.
3146:M 28 Jun 2025 06:51:53.578 * Connection with replica 127.0.0.1:7105 lost.
3146:M 28 Jun 2025 06:51:53.583 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 28 Jun 2025 06:51:53.584 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 377326.
3146:M 28 Jun 2025 07:08:21.202 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 28 Jun 2025 07:08:21.202 * Connection with replica 127.0.0.1:7105 lost.
3146:M 28 Jun 2025 07:08:21.206 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 28 Jun 2025 07:08:21.206 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 377410.
3146:M 28 Jun 2025 07:32:41.035 * Connection with replica 127.0.0.1:7105 lost.
3146:M 28 Jun 2025 07:32:41.037 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 28 Jun 2025 07:32:41.037 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 378110.
3146:M 28 Jun 2025 07:48:52.930 * Connection with replica 127.0.0.1:7105 lost.
3146:M 28 Jun 2025 07:48:52.931 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 28 Jun 2025 07:48:52.931 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 378180.
3146:M 28 Jun 2025 08:52:53.513 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 28 Jun 2025 08:52:53.514 * Connection with replica 127.0.0.1:7105 lost.
3146:M 28 Jun 2025 08:52:53.524 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 28 Jun 2025 08:52:53.524 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 383234.
3146:M 28 Jun 2025 09:08:46.396 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 28 Jun 2025 09:08:46.397 * Connection with replica 127.0.0.1:7105 lost.
3146:M 28 Jun 2025 09:08:46.404 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 28 Jun 2025 09:08:46.404 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 383318.
3146:M 28 Jun 2025 09:24:58.356 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 28 Jun 2025 09:24:58.356 * Connection with replica 127.0.0.1:7105 lost.
3146:M 28 Jun 2025 09:24:58.360 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 28 Jun 2025 09:24:58.361 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 383374.
3146:M 28 Jun 2025 10:42:31.001 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
3146:M 28 Jun 2025 10:42:31.002 * Connection with replica 127.0.0.1:7105 lost.
3146:M 28 Jun 2025 10:42:31.008 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 28 Jun 2025 10:42:31.009 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 388470.
3146:M 28 Jun 2025 12:28:09.620 * Connection with replica 127.0.0.1:7105 lost.
3146:M 28 Jun 2025 12:28:09.623 * Replica 127.0.0.1:7105 asks for synchronization
3146:M 28 Jun 2025 12:28:09.624 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 396072.
3146:M 28 Jun 2025 13:10:33.429 * 1 changes in 3600 seconds. Saving...
3146:M 28 Jun 2025 13:10:33.431 * Background saving started by pid 18998
18998:C 28 Jun 2025 13:10:33.439 * DB saved on disk
18998:C 28 Jun 2025 13:10:33.439 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3146:M 28 Jun 2025 13:10:33.532 * Background saving terminated with success
3146:M 28 Jun 2025 14:10:34.100 * 1 changes in 3600 seconds. Saving...
3146:M 28 Jun 2025 14:10:34.118 * Background saving started by pid 65314
65314:C 28 Jun 2025 14:10:34.255 * DB saved on disk
65314:C 28 Jun 2025 14:10:34.256 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3146:M 28 Jun 2025 14:10:34.322 * Background saving terminated with success
3146:M 29 Jun 2025 00:45:32.386 * 1 changes in 3600 seconds. Saving...
3146:M 29 Jun 2025 00:45:32.387 * Background saving started by pid 82634
82634:C 29 Jun 2025 00:45:32.395 * DB saved on disk
82634:C 29 Jun 2025 00:45:32.396 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3146:M 29 Jun 2025 00:45:32.489 * Background saving terminated with success
3146:signal-handler (1751132261) Received SIGTERM scheduling shutdown...
3146:M 29 Jun 2025 01:37:41.380 * User requested shutdown...
3146:M 29 Jun 2025 01:37:41.381 * 1 of 1 replicas are in sync when shutting down.
3146:M 29 Jun 2025 01:37:41.385 * Calling fsync() on the AOF file.
3146:M 29 Jun 2025 01:37:41.390 * Saving the final RDB snapshot before exiting.
3146:M 29 Jun 2025 01:37:41.402 * DB saved on disk
3146:M 29 Jun 2025 01:37:41.403 * Removing the pid file.
3146:M 29 Jun 2025 01:37:41.408 # Redis is now ready to exit, bye bye...
4365:C 29 Jun 2025 01:39:38.583 * oO0OoO0OoO0Oo Redis is starting oO0OoO0OoO0Oo
4365:C 29 Jun 2025 01:39:38.584 * Redis version=7.2.7, bits=64, commit=00000000, modified=0, pid=4365, just started
4365:C 29 Jun 2025 01:39:38.584 * Configuration loaded
4365:M 29 Jun 2025 01:39:38.584 * monotonic clock: POSIX clock_gettime
4365:M 29 Jun 2025 01:39:38.585 * Running mode=cluster, port=7102.
4365:M 29 Jun 2025 01:39:38.585 # WARNING: The TCP backlog setting of 511 cannot be enforced because kern.ipc.somaxconn is set to the lower value of 128.
4365:M 29 Jun 2025 01:39:38.585 * Node configuration loaded, I'm 543daaa188add5e4df5e4862c5a895bbfab6b4aa
4365:M 29 Jun 2025 01:39:38.586 * Server initialized
4365:M 29 Jun 2025 01:39:38.586 * Reading RDB base file on AOF loading...
4365:M 29 Jun 2025 01:39:38.586 * Loading RDB produced by version 7.2.7
4365:M 29 Jun 2025 01:39:38.586 * RDB age 1681507 seconds
4365:M 29 Jun 2025 01:39:38.586 * RDB memory usage when created 1.70 Mb
4365:M 29 Jun 2025 01:39:38.587 * RDB is base AOF
4365:M 29 Jun 2025 01:39:38.587 * Done loading RDB, keys loaded: 0, keys expired: 0.
4365:M 29 Jun 2025 01:39:38.587 * DB loaded from base file appendonly-7102.aof.1.base.rdb: 0.001 seconds
4365:M 29 Jun 2025 01:39:38.594 * DB saved on disk
4365:M 29 Jun 2025 01:39:38.599 * DB saved on disk
4365:M 29 Jun 2025 01:39:38.605 * DB saved on disk
4365:M 29 Jun 2025 01:39:38.610 * DB saved on disk
4365:M 29 Jun 2025 01:39:38.615 * DB saved on disk
4365:M 29 Jun 2025 01:39:38.620 * DB saved on disk
4365:M 29 Jun 2025 01:39:38.625 * DB saved on disk
4365:M 29 Jun 2025 01:39:38.632 * DB saved on disk
4365:M 29 Jun 2025 01:39:38.637 * DB saved on disk
4365:M 29 Jun 2025 01:39:38.642 * DB saved on disk
4365:M 29 Jun 2025 01:39:38.647 * DB saved on disk
4365:M 29 Jun 2025 01:39:38.652 * DB saved on disk
4365:M 29 Jun 2025 01:39:38.657 * DB saved on disk
4365:M 29 Jun 2025 01:39:38.658 * DB loaded from incr file appendonly-7102.aof.1.incr.aof: 0.071 seconds
4365:M 29 Jun 2025 01:39:38.658 * DB loaded from append only file: 0.072 seconds
4365:M 29 Jun 2025 01:39:38.658 * Opening AOF incr file appendonly-7102.aof.1.incr.aof on server start
4365:M 29 Jun 2025 01:39:38.658 * Ready to accept connections tcp
4365:M 29 Jun 2025 01:39:40.700 * Cluster state changed: ok
4365:M 29 Jun 2025 01:39:44.704 * Replica 127.0.0.1:7105 asks for synchronization
4365:M 29 Jun 2025 01:39:44.704 * Partial resynchronization not accepted: Replication ID mismatch (Replica asked for '7cb4b4d952e3609433a09713ac51f9e2cb3a16ee', my replication IDs are 'cf71eb5f62ed0a68f2564eeba16ef329e2324b8b' and '0000000000000000000000000000000000000000')
4365:M 29 Jun 2025 01:39:44.710 * Replication backlog created, my new replication IDs are 'b135a8e75ca7ab396f1983010846e39de0cca071' and '0000000000000000000000000000000000000000'
4365:M 29 Jun 2025 01:39:44.715 * Delay next BGSAVE for diskless SYNC
4365:M 29 Jun 2025 01:39:49.750 * Starting BGSAVE for SYNC with target: replicas sockets
4365:M 29 Jun 2025 01:39:49.751 * Background RDB transfer started by pid 4464
4464:C 29 Jun 2025 01:39:49.751 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
4365:M 29 Jun 2025 01:39:49.751 * Diskless rdb transfer, done reading from pipe, 1 replicas still up.
4365:M 29 Jun 2025 01:39:49.780 * Background RDB transfer terminated with success
4365:M 29 Jun 2025 01:39:49.787 * Streamed RDB transfer with replica 127.0.0.1:7105 succeeded (socket). Waiting for REPLCONF ACK from replica to enable streaming
4365:M 29 Jun 2025 01:39:49.791 * Synchronization with replica 127.0.0.1:7105 succeeded
4365:M 29 Jun 2025 01:39:49.811 * DB saved on disk
4365:M 29 Jun 2025 01:39:49.817 * configEpoch set to 0 via CLUSTER RESET HARD
4365:M 29 Jun 2025 01:39:49.817 * Node hard reset, now I'm b6893d2e15c2942dd3a0d3e1c9296eaf689bac2a
4365:M 29 Jun 2025 01:39:49.817 # Cluster state changed: fail
4365:M 29 Jun 2025 01:39:49.904 * Connection with replica 127.0.0.1:7105 lost.
4365:M 29 Jun 2025 01:39:52.939 * configEpoch set to 3 via CLUSTER SET-CONFIG-EPOCH
4365:M 29 Jun 2025 01:39:54.963 * Replica 127.0.0.1:7104 asks for synchronization
4365:M 29 Jun 2025 01:39:54.964 * Partial resynchronization not accepted: Replication ID mismatch (Replica asked for 'b5a692ba1535b676bdd8cc646488eb7561343c8e', my replication IDs are 'b135a8e75ca7ab396f1983010846e39de0cca071' and '0000000000000000000000000000000000000000')
4365:M 29 Jun 2025 01:39:54.964 * Delay next BGSAVE for diskless SYNC
4365:M 29 Jun 2025 01:39:57.948 * Cluster state changed: ok
4365:M 29 Jun 2025 01:39:59.867 * Starting BGSAVE for SYNC with target: replicas sockets
4365:M 29 Jun 2025 01:39:59.867 * Background RDB transfer started by pid 4567
4567:C 29 Jun 2025 01:39:59.868 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
4365:M 29 Jun 2025 01:39:59.868 * Diskless rdb transfer, done reading from pipe, 1 replicas still up.
4365:M 29 Jun 2025 01:39:59.889 * Background RDB transfer terminated with success
4365:M 29 Jun 2025 01:39:59.889 * Streamed RDB transfer with replica 127.0.0.1:7104 succeeded (socket). Waiting for REPLCONF ACK from replica to enable streaming
4365:M 29 Jun 2025 01:39:59.893 * Synchronization with replica 127.0.0.1:7104 succeeded
4365:M 29 Jun 2025 02:39:50.057 * 1 changes in 3600 seconds. Saving...
4365:M 29 Jun 2025 02:39:50.059 * Background saving started by pid 26193
26193:C 29 Jun 2025 02:39:50.066 * DB saved on disk
26193:C 29 Jun 2025 02:39:50.067 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
4365:M 29 Jun 2025 02:39:50.163 * Background saving terminated with success
4365:M 29 Jun 2025 03:23:49.080 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
4365:M 29 Jun 2025 03:23:49.080 * Connection with replica 127.0.0.1:7104 lost.
4365:M 29 Jun 2025 03:23:49.090 * Replica 127.0.0.1:7104 asks for synchronization
4365:M 29 Jun 2025 03:23:49.091 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 11277.
4365:M 29 Jun 2025 03:42:55.440 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
4365:M 29 Jun 2025 03:42:55.564 * Connection with replica 127.0.0.1:7104 lost.
4365:M 29 Jun 2025 03:42:55.628 * Replica 127.0.0.1:7104 asks for synchronization
4365:M 29 Jun 2025 03:42:55.636 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 11585.
4365:M 29 Jun 2025 04:00:44.561 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
4365:M 29 Jun 2025 04:00:44.562 * Connection with replica 127.0.0.1:7104 lost.
4365:M 29 Jun 2025 04:00:44.639 * Replica 127.0.0.1:7104 asks for synchronization
4365:M 29 Jun 2025 04:00:44.639 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 11781.
4365:M 29 Jun 2025 04:20:39.873 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
4365:M 29 Jun 2025 04:20:39.873 * Connection with replica 127.0.0.1:7104 lost.
4365:M 29 Jun 2025 04:20:39.898 * Replica 127.0.0.1:7104 asks for synchronization
4365:M 29 Jun 2025 04:20:39.898 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 12159.
4365:M 29 Jun 2025 05:42:32.029 * Connection with replica 127.0.0.1:7104 lost.
4365:M 29 Jun 2025 05:42:32.030 * Replica 127.0.0.1:7104 asks for synchronization
4365:M 29 Jun 2025 05:42:32.030 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 17659.
4365:M 29 Jun 2025 06:02:59.246 * Connection with replica 127.0.0.1:7104 lost.
4365:M 29 Jun 2025 06:02:59.250 * Replica 127.0.0.1:7104 asks for synchronization
4365:M 29 Jun 2025 06:02:59.250 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 18135.
4365:M 29 Jun 2025 06:19:07.057 * Connection with replica 127.0.0.1:7104 lost.
4365:M 29 Jun 2025 06:19:07.078 * Replica 127.0.0.1:7104 asks for synchronization
4365:M 29 Jun 2025 06:19:07.078 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 18233.
4365:M 29 Jun 2025 06:35:19.142 * Connection with replica 127.0.0.1:7104 lost.
4365:M 29 Jun 2025 06:35:19.145 * Replica 127.0.0.1:7104 asks for synchronization
4365:M 29 Jun 2025 06:35:19.145 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 18289.
4365:M 29 Jun 2025 06:51:30.163 * Connection with replica 127.0.0.1:7104 lost.
4365:M 29 Jun 2025 06:51:30.166 * Replica 127.0.0.1:7104 asks for synchronization
4365:M 29 Jun 2025 06:51:30.166 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 18359.
4365:M 29 Jun 2025 07:03:58.915 * Connection with replica 127.0.0.1:7104 lost.
4365:M 29 Jun 2025 07:03:58.920 * Replica 127.0.0.1:7104 asks for synchronization
4365:M 29 Jun 2025 07:03:58.920 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 18415.
4365:M 29 Jun 2025 08:22:28.907 * Connection with replica 127.0.0.1:7104 lost.
4365:M 29 Jun 2025 08:22:28.927 * Replica 127.0.0.1:7104 asks for synchronization
4365:M 29 Jun 2025 08:22:28.928 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 23609.
4365:M 29 Jun 2025 08:32:33.013 * Connection with replica 127.0.0.1:7104 lost.
4365:M 29 Jun 2025 08:32:33.034 * Replica 127.0.0.1:7104 asks for synchronization
4365:M 29 Jun 2025 08:32:33.034 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 23679.
4365:M 29 Jun 2025 10:50:45.099 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
4365:M 29 Jun 2025 10:50:45.110 * Connection with replica 127.0.0.1:7104 lost.
4365:M 29 Jun 2025 10:50:45.212 * Replica 127.0.0.1:7104 asks for synchronization
4365:M 29 Jun 2025 10:50:45.212 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 33717.
