20715:C 09 Jun 2025 23:30:36.985 * oO0OoO0OoO0Oo Redis is starting oO0OoO0OoO0Oo
20715:C 09 Jun 2025 23:30:36.985 * Redis version=7.2.7, bits=64, commit=00000000, modified=0, pid=20715, just started
20715:C 09 Jun 2025 23:30:36.985 * Configuration loaded
20715:M 09 Jun 2025 23:30:36.985 * Increased maximum number of open files to 10032 (it was originally set to 256).
20715:M 09 Jun 2025 23:30:36.985 * monotonic clock: POSIX clock_gettime
20715:M 09 Jun 2025 23:30:36.986 * Running mode=cluster, port=7100.
20715:M 09 Jun 2025 23:30:36.986 # WARNING: The TCP backlog setting of 511 cannot be enforced because kern.ipc.somaxconn is set to the lower value of 128.
20715:M 09 Jun 2025 23:30:36.986 * Node configuration loaded, I'm d1c6cb0d28195a050528199107abe17232119aaa
20715:M 09 Jun 2025 23:30:36.986 * Server initialized
20715:M 09 Jun 2025 23:30:36.986 * Reading RDB base file on AOF loading...
20715:M 09 Jun 2025 23:30:36.987 * Loading RDB produced by version 7.2.7
20715:M 09 Jun 2025 23:30:36.987 * RDB age 32168 seconds
20715:M 09 Jun 2025 23:30:36.987 * RDB memory usage when created 1.70 Mb
20715:M 09 Jun 2025 23:30:36.987 * RDB is base AOF
20715:M 09 Jun 2025 23:30:36.987 * Done loading RDB, keys loaded: 0, keys expired: 0.
20715:M 09 Jun 2025 23:30:36.987 * DB loaded from base file appendonly-7100.aof.1.base.rdb: 0.000 seconds
20715:M 09 Jun 2025 23:30:37.000 * DB saved on disk
20715:M 09 Jun 2025 23:30:37.005 * DB saved on disk
20715:M 09 Jun 2025 23:30:37.011 * DB saved on disk
20715:M 09 Jun 2025 23:30:37.029 * DB saved on disk
20715:M 09 Jun 2025 23:30:37.035 * DB saved on disk
20715:M 09 Jun 2025 23:30:37.041 * DB saved on disk
20715:M 09 Jun 2025 23:30:37.046 * DB saved on disk
20715:M 09 Jun 2025 23:30:37.051 * DB saved on disk
20715:M 09 Jun 2025 23:30:37.057 * DB saved on disk
20715:M 09 Jun 2025 23:30:37.062 * DB saved on disk
20715:M 09 Jun 2025 23:30:37.062 * DB loaded from incr file appendonly-7100.aof.1.incr.aof: 0.075 seconds
20715:M 09 Jun 2025 23:30:37.062 * DB loaded from append only file: 0.076 seconds
20715:M 09 Jun 2025 23:30:37.062 * Opening AOF incr file appendonly-7100.aof.1.incr.aof on server start
20715:M 09 Jun 2025 23:30:37.063 * Ready to accept connections tcp
20715:M 09 Jun 2025 23:30:52.213 * DB saved on disk
20715:M 09 Jun 2025 23:30:52.218 * configEpoch set to 0 via CLUSTER RESET HARD
20715:M 09 Jun 2025 23:30:52.219 * Node hard reset, now I'm f430caf1ef6d7b8d092d782bcc2057e678d0abf6
20715:M 09 Jun 2025 23:30:55.417 * configEpoch set to 1 via CLUSTER SET-CONFIG-EPOCH
20715:M 09 Jun 2025 23:30:57.442 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 09 Jun 2025 23:30:57.442 * Partial resynchronization not accepted: Replication ID mismatch (Replica asked for '5940952332ec9a39d733d5faf30fb7c1d75853c7', my replication IDs are '324e9815dcecee923b799e9ee0cd175251086bd9' and '0000000000000000000000000000000000000000')
20715:M 09 Jun 2025 23:30:57.442 * Replication backlog created, my new replication IDs are 'ec552889f6841fd565d372ae0007938b67202b82' and '0000000000000000000000000000000000000000'
20715:M 09 Jun 2025 23:30:57.442 * Delay next BGSAVE for diskless SYNC
20715:M 09 Jun 2025 23:31:00.478 * Cluster state changed: ok
20715:M 09 Jun 2025 23:31:02.303 * Starting BGSAVE for SYNC with target: replicas sockets
20715:M 09 Jun 2025 23:31:02.304 * Background RDB transfer started by pid 20936
20936:C 09 Jun 2025 23:31:02.305 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 09 Jun 2025 23:31:02.311 * Diskless rdb transfer, done reading from pipe, 1 replicas still up.
20715:M 09 Jun 2025 23:31:02.338 * Background RDB transfer terminated with success
20715:M 09 Jun 2025 23:31:02.338 * Streamed RDB transfer with replica 127.0.0.1:7103 succeeded (socket). Waiting for REPLCONF ACK from replica to enable streaming
20715:M 09 Jun 2025 23:31:02.338 * Synchronization with replica 127.0.0.1:7103 succeeded
20715:M 10 Jun 2025 00:30:53.083 * 1 changes in 3600 seconds. Saving...
20715:M 10 Jun 2025 00:30:53.088 * Background saving started by pid 44545
44545:C 10 Jun 2025 00:30:53.152 * DB saved on disk
44545:C 10 Jun 2025 00:30:53.153 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 10 Jun 2025 00:30:53.194 * Background saving terminated with success
20715:M 10 Jun 2025 01:21:57.909 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 01:21:57.912 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 01:21:57.913 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 8925.
20715:M 10 Jun 2025 01:38:10.675 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 01:38:10.697 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 01:38:10.697 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 8981.
20715:M 10 Jun 2025 01:54:23.574 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 01:54:23.577 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 01:54:23.578 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 9051.
20715:M 10 Jun 2025 02:10:33.565 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 02:10:33.567 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 02:10:33.567 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 9121.
20715:M 10 Jun 2025 02:26:21.658 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 02:26:21.661 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 02:26:21.662 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 9177.
20715:M 10 Jun 2025 02:42:17.462 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 10 Jun 2025 02:42:17.469 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 02:42:17.479 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 02:42:17.479 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 9276.
20715:M 10 Jun 2025 02:58:34.245 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 10 Jun 2025 02:58:34.246 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 02:58:34.249 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 02:58:34.249 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 9346.
20715:M 10 Jun 2025 03:07:26.661 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 10 Jun 2025 03:07:26.661 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 03:07:26.671 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 03:07:26.672 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 9416.
20715:M 10 Jun 2025 03:23:39.293 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 10 Jun 2025 03:23:39.293 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 03:23:39.298 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 03:23:39.299 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 9472.
20715:M 10 Jun 2025 03:39:48.413 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 10 Jun 2025 03:39:48.447 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 03:39:48.472 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 03:39:48.472 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 9542.
20715:M 10 Jun 2025 03:56:01.835 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 03:56:01.841 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 03:56:01.841 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 9598.
20715:M 10 Jun 2025 04:12:14.518 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 04:12:14.523 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 04:12:14.523 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 9668.
20715:M 10 Jun 2025 04:28:27.372 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 04:28:27.374 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 04:28:27.374 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 9738.
20715:M 10 Jun 2025 04:44:40.201 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 04:44:40.204 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 04:44:40.205 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 9794.
20715:M 10 Jun 2025 05:00:53.930 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 05:00:53.933 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 05:00:53.933 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 9864.
20715:M 10 Jun 2025 05:17:06.770 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 05:17:06.771 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 05:17:06.771 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 9934.
20715:M 10 Jun 2025 05:33:19.555 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 05:33:19.556 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 05:33:19.556 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 9990.
20715:M 10 Jun 2025 05:49:32.229 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 05:49:32.237 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 05:49:32.237 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 10060.
20715:M 10 Jun 2025 06:05:44.949 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 06:05:44.952 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 06:05:44.953 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 10116.
20715:M 10 Jun 2025 06:21:30.477 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 06:21:30.480 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 06:21:30.480 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 10186.
20715:M 10 Jun 2025 06:37:41.204 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 06:37:41.205 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 06:37:41.205 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 10242.
20715:M 10 Jun 2025 06:53:27.817 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 06:53:27.819 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 06:53:27.819 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 10312.
20715:M 10 Jun 2025 06:59:15.680 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 06:59:15.682 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 06:59:15.682 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 10382.
20715:M 10 Jun 2025 07:15:02.581 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 07:15:02.587 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 07:15:02.587 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 10438.
20715:M 10 Jun 2025 07:31:11.128 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 07:31:11.131 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 07:31:11.132 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 10494.
20715:M 10 Jun 2025 07:35:19.799 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 07:35:19.801 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 07:35:19.802 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 10564.
20715:M 10 Jun 2025 07:51:04.700 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 07:51:04.703 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 07:51:04.703 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 10634.
20715:M 10 Jun 2025 08:07:17.299 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 08:07:17.302 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 08:07:17.302 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 10690.
20715:M 10 Jun 2025 08:23:29.200 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 08:23:29.214 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 08:23:29.214 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 10760.
20715:M 10 Jun 2025 08:39:41.990 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 10 Jun 2025 08:39:42.034 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 08:39:42.054 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 08:39:42.054 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 10816.
20715:M 10 Jun 2025 08:47:24.594 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 10 Jun 2025 08:47:24.631 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 08:47:24.648 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 08:47:24.648 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 10886.
20715:M 10 Jun 2025 09:00:15.607 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 10 Jun 2025 09:00:15.607 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 09:00:15.612 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 09:00:15.612 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 10956.
20715:M 10 Jun 2025 09:19:24.992 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 10 Jun 2025 09:19:24.993 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 09:19:24.997 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 09:19:24.997 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 11152.
20715:M 10 Jun 2025 09:36:43.880 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 10 Jun 2025 09:36:43.880 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 09:36:43.884 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 09:36:43.884 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 11152.
20715:M 10 Jun 2025 09:50:32.956 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 10 Jun 2025 09:50:32.956 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 09:50:32.960 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 09:50:32.960 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 11166.
20715:M 10 Jun 2025 09:54:16.111 * 1 changes in 3600 seconds. Saving...
20715:M 10 Jun 2025 09:54:16.112 * Background saving started by pid 71787
71787:C 10 Jun 2025 09:54:16.122 * DB saved on disk
71787:C 10 Jun 2025 09:54:16.122 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 10 Jun 2025 09:54:16.214 * Background saving terminated with success
20715:M 10 Jun 2025 10:19:54.514 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 10:19:54.515 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 10:19:54.515 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 14018.
20715:M 10 Jun 2025 10:54:17.019 * 1 changes in 3600 seconds. Saving...
20715:M 10 Jun 2025 10:54:17.021 * Background saving started by pid 93112
93112:C 10 Jun 2025 10:54:17.028 * DB saved on disk
93112:C 10 Jun 2025 10:54:17.029 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 10 Jun 2025 10:54:17.122 * Background saving terminated with success
20715:M 10 Jun 2025 11:54:18.027 * 1 changes in 3600 seconds. Saving...
20715:M 10 Jun 2025 11:54:18.037 * Background saving started by pid 19686
19686:C 10 Jun 2025 11:54:18.055 * DB saved on disk
19686:C 10 Jun 2025 11:54:18.057 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 10 Jun 2025 11:54:18.142 * Background saving terminated with success
20715:M 10 Jun 2025 12:54:19.034 * 1 changes in 3600 seconds. Saving...
20715:M 10 Jun 2025 12:54:19.036 * Background saving started by pid 42785
42785:C 10 Jun 2025 12:54:19.047 * DB saved on disk
42785:C 10 Jun 2025 12:54:19.048 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 10 Jun 2025 12:54:19.138 * Background saving terminated with success
20715:M 10 Jun 2025 13:02:22.603 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 13:02:22.604 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 13:02:22.605 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 30278.
20715:M 10 Jun 2025 13:04:39.779 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 13:04:39.780 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 13:04:39.780 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 30348.
20715:M 10 Jun 2025 13:08:41.744 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 13:08:41.746 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 13:08:41.746 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 30404.
20715:M 10 Jun 2025 13:24:52.408 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 13:24:52.410 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 13:24:52.411 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 30474.
20715:M 10 Jun 2025 13:54:20.096 * 1 changes in 3600 seconds. Saving...
20715:M 10 Jun 2025 13:54:20.101 * Background saving started by pid 57131
57131:C 10 Jun 2025 13:54:20.108 * DB saved on disk
57131:C 10 Jun 2025 13:54:20.109 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 10 Jun 2025 13:54:20.204 * Background saving terminated with success
20715:M 10 Jun 2025 17:10:14.598 * 1 changes in 3600 seconds. Saving...
20715:M 10 Jun 2025 17:10:14.605 * Background saving started by pid 36491
36491:C 10 Jun 2025 17:10:14.614 * DB saved on disk
36491:C 10 Jun 2025 17:10:14.618 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 10 Jun 2025 17:10:14.710 * Background saving terminated with success
20715:M 10 Jun 2025 18:10:15.012 * 1 changes in 3600 seconds. Saving...
20715:M 10 Jun 2025 18:10:15.016 * Background saving started by pid 63263
63263:C 10 Jun 2025 18:10:15.028 * DB saved on disk
63263:C 10 Jun 2025 18:10:15.030 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 10 Jun 2025 18:10:15.117 * Background saving terminated with success
20715:M 10 Jun 2025 19:10:16.021 * 1 changes in 3600 seconds. Saving...
20715:M 10 Jun 2025 19:10:16.023 * Background saving started by pid 5678
5678:C 10 Jun 2025 19:10:16.036 * DB saved on disk
5678:C 10 Jun 2025 19:10:16.036 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 10 Jun 2025 19:10:16.126 * Background saving terminated with success
20715:M 10 Jun 2025 21:29:00.104 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 10 Jun 2025 21:29:00.105 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 21:29:00.108 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 21:29:00.108 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 72710.
20715:M 10 Jun 2025 21:47:27.712 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 10 Jun 2025 21:47:27.713 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 21:47:27.716 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 21:47:27.716 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 72780.
20715:M 10 Jun 2025 22:04:13.610 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 10 Jun 2025 22:04:13.610 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 22:04:13.613 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 22:04:13.613 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 72780.
20715:M 10 Jun 2025 22:21:29.506 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 10 Jun 2025 22:21:29.506 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 22:21:29.507 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 22:21:29.507 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 72794.
20715:M 10 Jun 2025 22:36:31.832 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 10 Jun 2025 22:36:31.848 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 22:36:31.851 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 22:36:31.851 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 72794.
20715:M 10 Jun 2025 22:52:26.688 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 10 Jun 2025 22:52:26.801 * Connection with replica 127.0.0.1:7103 lost.
20715:M 10 Jun 2025 22:52:26.803 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 10 Jun 2025 22:52:26.804 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 72864.
20715:M 10 Jun 2025 23:04:19.476 * 1 changes in 3600 seconds. Saving...
20715:M 10 Jun 2025 23:04:19.477 * Background saving started by pid 81174
81174:C 10 Jun 2025 23:04:19.491 * DB saved on disk
81174:C 10 Jun 2025 23:04:19.491 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 10 Jun 2025 23:04:19.579 * Background saving terminated with success
20715:M 11 Jun 2025 00:04:20.101 * 1 changes in 3600 seconds. Saving...
20715:M 11 Jun 2025 00:04:20.103 * Background saving started by pid 5330
5330:C 11 Jun 2025 00:04:20.119 * DB saved on disk
5330:C 11 Jun 2025 00:04:20.121 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 11 Jun 2025 00:04:20.205 * Background saving terminated with success
20715:M 11 Jun 2025 00:52:03.236 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 00:52:03.241 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 00:52:03.242 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 82310.
20715:M 11 Jun 2025 01:08:10.861 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 01:08:10.869 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 01:08:10.877 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 82366.
20715:M 11 Jun 2025 01:24:23.448 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 01:24:23.450 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 01:24:23.450 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 82422.
20715:M 11 Jun 2025 01:40:36.175 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 01:40:36.178 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 01:40:36.178 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 82492.
20715:M 11 Jun 2025 01:56:49.670 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 01:56:49.670 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 01:56:49.674 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 01:56:49.675 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 82548.
20715:M 11 Jun 2025 02:29:14.869 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 02:29:14.870 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 02:29:14.874 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 02:29:14.874 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 82731.
20715:M 11 Jun 2025 02:35:35.133 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 02:35:35.248 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 02:35:35.251 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 02:35:35.251 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 82787.
20715:M 11 Jun 2025 02:51:47.971 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 02:51:48.088 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 02:51:48.097 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 02:51:48.097 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 82857.
20715:M 11 Jun 2025 03:08:00.853 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 03:08:00.972 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 03:08:00.976 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 03:08:00.977 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 82913.
20715:M 11 Jun 2025 03:24:13.738 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 03:24:13.871 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 03:24:13.877 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 03:24:13.877 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 82983.
20715:M 11 Jun 2025 03:40:25.717 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 03:40:25.857 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 03:40:25.862 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 03:40:25.863 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 83053.
20715:M 11 Jun 2025 03:56:19.352 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 03:56:19.492 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 03:56:19.497 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 03:56:19.497 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 83109.
20715:M 11 Jun 2025 04:12:29.101 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 04:12:29.101 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 04:12:29.104 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 04:12:29.104 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 83179.
20715:M 11 Jun 2025 04:28:40.994 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 04:28:41.120 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 04:28:41.124 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 04:28:41.124 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 83235.
20715:M 11 Jun 2025 04:44:53.812 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 04:44:53.937 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 04:44:53.946 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 04:44:53.946 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 83305.
20715:M 11 Jun 2025 05:01:06.643 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 05:01:06.781 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 05:01:06.785 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 05:01:06.785 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 83361.
20715:M 11 Jun 2025 05:17:18.730 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 05:17:18.753 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 05:17:18.772 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 05:17:18.772 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 83431.
20715:M 11 Jun 2025 05:33:31.417 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 05:33:31.560 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 05:33:31.564 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 05:33:31.564 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 83501.
20715:M 11 Jun 2025 05:49:43.405 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 05:49:43.526 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 05:49:43.529 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 05:49:43.530 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 83557.
20715:M 11 Jun 2025 06:05:56.204 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 06:05:56.338 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 06:05:56.341 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 06:05:56.341 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 83627.
20715:M 11 Jun 2025 06:22:08.904 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 06:22:09.017 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 06:22:09.020 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 06:22:09.020 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 83683.
20715:M 11 Jun 2025 06:31:42.102 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 06:31:42.105 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 06:31:42.106 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 83753.
20715:M 11 Jun 2025 06:47:45.074 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 06:47:45.075 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 06:47:45.080 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 06:47:45.081 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 83823.
20715:M 11 Jun 2025 07:03:56.993 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 07:03:56.994 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 07:03:56.998 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 07:03:56.998 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 83879.
20715:M 11 Jun 2025 07:19:48.706 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 07:19:48.706 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 07:19:48.711 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 07:19:48.711 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 83949.
20715:M 11 Jun 2025 07:36:02.438 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 07:36:02.438 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 07:36:02.446 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 07:36:02.447 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 84019.
20715:M 11 Jun 2025 07:52:15.061 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 07:52:15.061 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 07:52:15.068 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 07:52:15.069 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 84075.
20715:M 11 Jun 2025 08:08:27.744 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 08:08:27.744 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 08:08:27.747 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 08:08:27.747 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 84145.
20715:M 11 Jun 2025 08:14:22.555 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 08:14:22.555 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 08:14:22.558 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 08:14:22.559 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 84201.
20715:M 11 Jun 2025 08:30:34.400 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 08:30:34.400 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 08:30:34.404 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 08:30:34.404 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 84271.
20715:M 11 Jun 2025 08:32:41.409 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 08:32:41.409 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 08:32:41.412 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 08:32:41.412 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 84341.
20715:M 11 Jun 2025 08:36:30.684 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 08:36:30.809 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 08:36:30.811 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 08:36:30.812 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 84397.
20715:M 11 Jun 2025 08:52:45.474 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 08:52:45.590 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 08:52:45.594 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 08:52:45.594 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 84467.
20715:M 11 Jun 2025 09:05:22.551 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 09:05:22.682 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 09:05:22.685 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 09:05:22.685 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 84523.
20715:M 11 Jun 2025 09:23:32.450 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 09:23:32.482 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 09:23:32.505 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 09:23:32.505 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 84593.
20715:M 11 Jun 2025 09:33:41.466 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 09:33:41.480 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 09:33:41.496 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 09:33:41.496 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 84593.
20715:M 11 Jun 2025 09:52:07.476 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 09:52:07.493 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 09:52:07.511 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 09:52:07.511 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 84663.
20715:M 11 Jun 2025 10:06:47.345 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 10:06:47.346 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 10:06:47.361 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 10:06:47.361 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 85097.
20715:M 11 Jun 2025 10:27:08.476 * 1 changes in 3600 seconds. Saving...
20715:M 11 Jun 2025 10:27:08.480 * Background saving started by pid 39477
39477:C 11 Jun 2025 10:27:08.487 * DB saved on disk
39477:C 11 Jun 2025 10:27:08.489 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 11 Jun 2025 10:27:08.582 * Background saving terminated with success
20715:M 11 Jun 2025 11:06:58.899 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 11:06:58.899 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 11:06:58.906 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 11:06:58.906 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 90945.
20715:M 11 Jun 2025 11:10:12.014 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 11:10:12.016 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 11:10:12.016 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 91001.
20715:M 11 Jun 2025 11:27:09.056 * 1 changes in 3600 seconds. Saving...
20715:M 11 Jun 2025 11:27:09.058 * Background saving started by pid 61741
61741:C 11 Jun 2025 11:27:09.069 * DB saved on disk
61741:C 11 Jun 2025 11:27:09.070 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 11 Jun 2025 11:27:09.161 * Background saving terminated with success
20715:M 11 Jun 2025 13:12:23.149 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 13:12:23.151 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 13:12:23.151 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 100437.
20715:M 11 Jun 2025 13:29:45.395 * Connection with replica client id #4690 lost.
20715:M 11 Jun 2025 13:29:45.406 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 13:29:45.406 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 43 bytes of backlog starting from offset 100493.
20715:M 11 Jun 2025 13:46:06.454 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 13:46:06.455 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 13:46:06.455 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 100606.
20715:M 11 Jun 2025 14:01:53.566 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 14:01:53.569 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 14:01:53.569 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 100676.
20715:M 11 Jun 2025 14:33:47.692 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 14:33:47.695 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 14:33:47.695 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 102818.
20715:M 11 Jun 2025 15:04:34.796 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 15:04:34.798 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 15:04:34.798 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 104946.
20715:M 11 Jun 2025 15:51:10.826 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 15:51:10.828 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 15:51:10.828 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 107312.
20715:M 11 Jun 2025 15:59:57.796 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 15:59:57.797 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 15:59:57.797 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 107438.
20715:M 11 Jun 2025 16:04:03.039 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 16:04:03.042 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 16:04:03.043 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 107452.
20715:M 11 Jun 2025 16:13:05.319 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 16:13:05.322 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 16:13:05.323 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 107522.
20715:M 11 Jun 2025 16:16:19.227 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 16:16:19.240 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 16:16:19.240 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 107564.
20715:M 11 Jun 2025 16:27:22.407 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 16:27:22.407 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 16:27:22.407 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 107634.
20715:M 11 Jun 2025 16:48:10.179 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 16:48:10.303 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 16:48:10.306 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 16:48:10.306 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 108068.
20715:M 11 Jun 2025 17:04:02.292 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 17:04:02.293 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 17:04:02.300 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 17:04:02.300 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 108082.
20715:M 11 Jun 2025 17:05:13.466 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 11 Jun 2025 17:05:13.601 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 17:05:13.604 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 17:05:13.604 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 108082.
20715:M 11 Jun 2025 17:43:59.283 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 17:43:59.286 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 17:43:59.286 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 110126.
20715:M 11 Jun 2025 22:14:51.472 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 22:14:51.473 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 22:14:51.474 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 132598.
20715:M 11 Jun 2025 22:31:53.602 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 22:31:53.604 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 22:31:53.604 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 132668.
20715:M 11 Jun 2025 22:47:38.538 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 22:47:38.545 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 22:47:38.545 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 132668.
20715:M 11 Jun 2025 23:03:11.552 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 23:03:11.554 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 23:03:11.557 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 132682.
20715:M 11 Jun 2025 23:19:19.554 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 23:19:19.572 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 23:19:19.572 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 132682.
20715:M 11 Jun 2025 23:26:40.099 * Connection with replica 127.0.0.1:7103 lost.
20715:M 11 Jun 2025 23:26:40.100 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 11 Jun 2025 23:26:40.100 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 132696.
20715:M 12 Jun 2025 00:15:33.614 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 00:15:33.616 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 00:15:33.617 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 136420.
20715:M 12 Jun 2025 00:32:44.560 * 1 changes in 3600 seconds. Saving...
20715:M 12 Jun 2025 00:32:44.563 * Background saving started by pid 5909
5909:C 12 Jun 2025 00:32:44.571 * DB saved on disk
5909:C 12 Jun 2025 00:32:44.571 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 12 Jun 2025 00:32:44.665 * Background saving terminated with success
20715:M 12 Jun 2025 01:32:45.078 * 1 changes in 3600 seconds. Saving...
20715:M 12 Jun 2025 01:32:45.082 * Background saving started by pid 31317
31317:C 12 Jun 2025 01:32:45.090 * DB saved on disk
31317:C 12 Jun 2025 01:32:45.091 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 12 Jun 2025 01:32:45.184 * Background saving terminated with success
20715:M 12 Jun 2025 03:02:38.425 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 03:02:38.545 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 03:02:38.548 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 03:02:38.548 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 160238.
20715:M 12 Jun 2025 03:18:47.340 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 03:18:47.340 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 03:18:47.347 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 03:18:47.347 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 160308.
20715:M 12 Jun 2025 03:34:59.987 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 03:34:59.988 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 03:34:59.992 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 03:34:59.992 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 160364.
20715:M 12 Jun 2025 03:51:12.664 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 03:51:12.664 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 03:51:12.668 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 03:51:12.668 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 160434.
20715:M 12 Jun 2025 04:07:25.264 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 04:07:25.264 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 04:07:25.267 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 04:07:25.267 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 160504.
20715:M 12 Jun 2025 04:23:37.973 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 04:23:37.973 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 04:23:37.975 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 04:23:37.975 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 160560.
20715:M 12 Jun 2025 04:39:50.870 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 04:39:50.873 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 04:39:50.879 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 04:39:50.879 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 160714.
20715:M 12 Jun 2025 04:44:36.123 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 04:44:36.125 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 04:44:36.136 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 04:44:36.137 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 160770.
20715:M 12 Jun 2025 05:00:48.776 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 05:00:48.776 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 05:00:48.781 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 05:00:48.781 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 160840.
20715:M 12 Jun 2025 05:17:01.416 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 05:17:01.418 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 05:17:01.422 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 05:17:01.423 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 160910.
20715:M 12 Jun 2025 05:33:14.140 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 05:33:14.140 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 05:33:14.143 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 05:33:14.143 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 160966.
20715:M 12 Jun 2025 05:45:35.620 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 05:45:35.621 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 05:45:35.623 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 05:45:35.623 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 161036.
20715:M 12 Jun 2025 06:01:48.534 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 06:01:48.535 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 06:01:48.535 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 161106.
20715:M 12 Jun 2025 06:17:36.452 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 06:17:36.462 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 06:17:36.463 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 161162.
20715:M 12 Jun 2025 06:33:44.697 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 06:33:44.733 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 06:33:44.736 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 06:33:44.736 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 161218.
20715:M 12 Jun 2025 06:46:35.419 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 06:46:35.440 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 06:46:35.443 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 06:46:35.443 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 161288.
20715:M 12 Jun 2025 07:02:48.018 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 07:02:48.140 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 07:02:48.143 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 07:02:48.143 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 161344.
20715:M 12 Jun 2025 07:19:00.868 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 07:19:00.984 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 07:19:00.988 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 07:19:00.988 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 161414.
20715:M 12 Jun 2025 07:35:13.815 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 07:35:13.817 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 07:35:13.817 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 161484.
20715:M 12 Jun 2025 07:47:35.462 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 07:47:35.464 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 07:47:35.464 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 161540.
20715:M 12 Jun 2025 08:03:48.774 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 08:03:48.774 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 08:03:48.782 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 08:03:48.782 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 161610.
20715:M 12 Jun 2025 08:20:01.375 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 08:20:01.375 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 08:20:01.382 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 08:20:01.382 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 161680.
20715:M 12 Jun 2025 08:36:14.539 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 08:36:14.541 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 08:36:14.542 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 161736.
20715:M 12 Jun 2025 08:48:35.467 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 08:48:35.469 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 08:48:35.469 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 161806.
20715:M 12 Jun 2025 08:53:24.651 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 08:53:24.651 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 08:53:24.654 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 08:53:24.655 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 161862.
20715:M 12 Jun 2025 08:55:09.905 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 08:55:09.907 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 08:55:09.908 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 161932.
20715:M 12 Jun 2025 09:00:52.155 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 09:00:52.271 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 09:00:52.273 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 09:00:52.274 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 162016.
20715:M 12 Jun 2025 09:17:08.575 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 09:17:08.575 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 09:17:08.578 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 09:17:08.578 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 162072.
20715:M 12 Jun 2025 09:34:27.475 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 09:34:27.497 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 09:34:27.503 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 09:34:27.503 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 162086.
20715:M 12 Jun 2025 09:50:02.378 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 09:50:02.496 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 09:50:02.499 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 09:50:02.499 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 162086.
20715:M 12 Jun 2025 09:55:37.272 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 09:55:37.389 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 09:55:37.391 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 09:55:37.391 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 162100.
20715:M 12 Jun 2025 10:15:15.704 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 10:15:15.718 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 10:15:15.719 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 163318.
20715:M 12 Jun 2025 11:30:02.771 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 11:30:02.771 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 11:30:02.775 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 11:30:02.775 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 169436.
20715:M 12 Jun 2025 11:36:08.596 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 11:36:08.596 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 11:36:08.600 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 11:36:08.600 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 169492.
20715:M 12 Jun 2025 11:44:28.532 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 11:44:28.532 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 11:44:28.534 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 11:44:28.534 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 169562.
20715:M 12 Jun 2025 13:00:18.018 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 13:00:18.019 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 13:00:18.022 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 13:00:18.022 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 174532.
20715:M 12 Jun 2025 13:17:10.018 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 13:17:10.018 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 13:17:10.027 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 13:17:10.028 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 174616.
20715:M 12 Jun 2025 13:34:30.838 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 13:34:30.838 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 13:34:30.842 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 13:34:30.843 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 174686.
20715:M 12 Jun 2025 13:45:52.358 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 13:45:52.359 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 13:45:52.363 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 13:45:52.363 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 174742.
20715:M 12 Jun 2025 14:02:20.375 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 14:02:20.375 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 14:02:20.378 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 14:02:20.378 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 174812.
20715:M 12 Jun 2025 14:07:06.167 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 14:07:06.168 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 14:07:06.172 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 14:07:06.173 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 174896.
20715:M 12 Jun 2025 15:13:52.480 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 15:13:52.496 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 15:13:52.507 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 15:13:52.508 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 180272.
20715:M 12 Jun 2025 18:46:38.751 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 18:46:38.751 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 18:46:38.758 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 18:46:38.759 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 197786.
20715:M 12 Jun 2025 19:02:38.090 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 19:02:38.090 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 19:02:38.095 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 19:02:38.095 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 197856.
20715:M 12 Jun 2025 19:17:52.989 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 19:17:52.989 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 19:17:52.994 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 19:17:52.994 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 197870.
20715:M 12 Jun 2025 19:35:47.887 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 19:35:47.887 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 19:35:47.891 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 19:35:47.891 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 197870.
20715:M 12 Jun 2025 19:52:39.761 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 19:52:39.761 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 19:52:39.764 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 19:52:39.764 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 197884.
20715:M 12 Jun 2025 20:09:46.696 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 20:09:46.696 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 20:09:46.700 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 20:09:46.700 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 197884.
20715:M 12 Jun 2025 20:24:52.664 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 20:24:52.664 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 20:24:52.668 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 20:24:52.668 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 197898.
20715:M 12 Jun 2025 20:42:32.581 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 20:42:32.581 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 20:42:32.584 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 20:42:32.584 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 197898.
20715:M 12 Jun 2025 20:46:27.439 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 20:46:27.439 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 20:46:27.440 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 20:46:27.440 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 197912.
20715:M 12 Jun 2025 21:02:50.435 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 21:02:50.435 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 21:02:50.443 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 21:02:50.443 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 197968.
20715:M 12 Jun 2025 21:18:07.331 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 21:18:07.365 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 21:18:07.384 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 21:18:07.384 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 197982.
20715:M 12 Jun 2025 21:36:01.325 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 21:36:01.343 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 21:36:01.350 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 21:36:01.350 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 197982.
20715:M 12 Jun 2025 21:47:27.211 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 21:47:27.351 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 21:47:27.357 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 21:47:27.357 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 197996.
20715:M 12 Jun 2025 22:04:12.355 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 22:04:12.356 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 22:04:12.356 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 198052.
20715:M 12 Jun 2025 22:21:48.306 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 22:21:48.336 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 22:21:48.338 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 22:21:48.338 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 198066.
20715:M 12 Jun 2025 22:40:39.424 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 12 Jun 2025 22:40:39.426 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 22:40:39.433 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 22:40:39.433 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 198150.
20715:M 12 Jun 2025 22:41:42.180 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 22:41:42.185 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 22:41:42.197 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 198150.
20715:M 12 Jun 2025 22:42:26.344 * 1 changes in 3600 seconds. Saving...
20715:M 12 Jun 2025 22:42:26.346 * Background saving started by pid 50941
50941:C 12 Jun 2025 22:42:26.352 * DB saved on disk
50941:C 12 Jun 2025 22:42:26.353 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 12 Jun 2025 22:42:26.447 * Background saving terminated with success
20715:M 12 Jun 2025 23:03:26.509 * Connection with replica 127.0.0.1:7103 lost.
20715:M 12 Jun 2025 23:03:26.511 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 12 Jun 2025 23:03:26.512 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 203303.
20715:M 12 Jun 2025 23:42:27.008 * 1 changes in 3600 seconds. Saving...
20715:M 12 Jun 2025 23:42:27.010 * Background saving started by pid 91879
91879:C 12 Jun 2025 23:42:27.030 * DB saved on disk
91879:C 12 Jun 2025 23:42:27.033 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 12 Jun 2025 23:42:27.112 * Background saving terminated with success
20715:M 13 Jun 2025 01:36:30.394 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 01:36:30.394 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 01:36:30.398 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 01:36:30.406 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 214757.
20715:M 13 Jun 2025 02:02:31.087 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 02:02:31.087 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 02:02:31.088 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 02:02:31.089 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 214814.
20715:M 13 Jun 2025 02:18:30.463 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 02:18:30.497 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 02:18:30.502 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 02:18:30.502 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 214870.
20715:M 13 Jun 2025 02:35:00.096 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 02:35:00.101 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 02:35:00.101 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 214884.
20715:M 13 Jun 2025 02:50:57.962 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 02:50:57.964 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 02:50:57.964 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 214884.
20715:M 13 Jun 2025 03:08:19.839 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 03:08:19.841 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 03:08:19.841 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 214898.
20715:M 13 Jun 2025 03:22:03.653 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 03:22:03.656 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 03:22:03.656 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 214898.
20715:M 13 Jun 2025 03:39:49.610 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 03:39:49.619 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 03:39:49.619 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 214968.
20715:M 13 Jun 2025 03:57:18.633 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 03:57:18.635 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 03:57:18.637 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 03:57:18.637 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 214982.
20715:M 13 Jun 2025 04:02:25.447 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 04:02:25.451 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 04:02:25.452 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 214982.
20715:M 13 Jun 2025 04:20:18.140 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 04:20:18.145 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 04:20:18.145 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 215052.
20715:M 13 Jun 2025 04:37:48.404 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 04:37:48.406 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 04:37:48.406 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 215122.
20715:M 13 Jun 2025 04:55:27.273 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 04:55:27.287 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 04:55:27.287 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 215122.
20715:M 13 Jun 2025 05:02:24.337 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 05:02:24.339 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 05:02:24.339 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 215136.
20715:M 13 Jun 2025 05:21:04.278 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 05:21:04.280 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 05:21:04.280 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 215262.
20715:M 13 Jun 2025 05:37:20.152 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 05:37:20.276 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 05:37:20.278 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 05:37:20.278 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 215276.
20715:M 13 Jun 2025 05:53:04.878 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 05:53:04.879 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 05:53:04.879 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 215276.
20715:M 13 Jun 2025 06:02:41.417 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 06:02:41.420 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 06:02:41.421 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 215290.
20715:M 13 Jun 2025 06:20:21.640 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 06:20:21.642 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 06:20:21.642 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 215346.
20715:M 13 Jun 2025 06:36:05.445 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 06:36:05.447 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 06:36:05.447 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 215360.
20715:M 13 Jun 2025 06:51:28.366 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 06:51:28.367 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 06:51:28.367 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 215360.
20715:M 13 Jun 2025 07:07:16.254 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 07:07:16.392 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 07:07:16.394 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 07:07:16.394 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 215374.
20715:M 13 Jun 2025 07:17:29.932 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 07:17:29.939 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 07:17:29.940 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 215388.
20715:M 13 Jun 2025 07:36:07.850 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 07:36:07.858 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 07:36:07.858 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 215444.
20715:M 13 Jun 2025 07:51:24.651 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 07:51:24.653 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 07:51:24.653 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 215458.
20715:M 13 Jun 2025 08:08:51.423 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 08:08:51.426 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 08:08:51.426 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 215458.
20715:M 13 Jun 2025 08:25:34.333 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 08:25:34.336 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 08:25:34.336 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 215472.
20715:M 13 Jun 2025 08:42:12.361 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 08:42:12.361 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 08:42:12.363 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 08:42:12.363 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 215472.
20715:M 13 Jun 2025 08:59:24.348 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 08:59:24.362 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 08:59:24.376 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 08:59:24.376 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 215486.
20715:M 13 Jun 2025 09:15:18.257 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 09:15:18.388 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 09:15:18.396 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 09:15:18.396 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 215486.
20715:M 13 Jun 2025 09:18:29.370 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 09:18:29.382 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 09:18:29.400 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 09:18:29.400 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 215500.
20715:M 13 Jun 2025 09:32:28.402 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 09:32:28.406 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 09:32:28.419 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 09:32:28.419 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 215556.
20715:M 13 Jun 2025 09:34:12.141 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 09:34:12.145 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 09:34:12.145 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 215626.
20715:M 13 Jun 2025 11:02:48.689 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 11:02:48.689 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 11:02:48.693 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 11:02:48.693 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 222738.
20715:M 13 Jun 2025 11:27:06.550 * 1 changes in 3600 seconds. Saving...
20715:M 13 Jun 2025 11:27:06.552 * Background saving started by pid 42774
42774:C 13 Jun 2025 11:27:06.562 * DB saved on disk
42774:C 13 Jun 2025 11:27:06.562 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 13 Jun 2025 11:27:06.653 * Background saving terminated with success
20715:M 13 Jun 2025 12:26:30.981 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 12:26:30.983 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 12:26:30.983 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 230182.
20715:M 13 Jun 2025 12:27:07.083 * 1 changes in 3600 seconds. Saving...
20715:M 13 Jun 2025 12:27:07.085 * Background saving started by pid 79667
79667:C 13 Jun 2025 12:27:07.103 * DB saved on disk
79667:C 13 Jun 2025 12:27:07.104 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 13 Jun 2025 12:27:07.187 * Background saving terminated with success
20715:M 13 Jun 2025 12:58:21.429 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 12:58:21.435 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 12:58:21.435 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 231414.
20715:M 13 Jun 2025 13:16:07.665 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 13:16:07.667 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 13:16:07.667 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 231568.
20715:M 13 Jun 2025 13:32:01.565 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 13:32:01.567 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 13:32:01.568 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 231624.
20715:M 13 Jun 2025 13:52:57.502 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 13:52:57.504 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 13:52:57.504 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 232058.
20715:M 13 Jun 2025 14:02:03.433 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 14:02:03.435 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 14:02:03.435 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 232548.
20715:M 13 Jun 2025 14:13:35.070 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 14:13:35.072 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 14:13:35.072 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 232842.
20715:M 13 Jun 2025 14:49:08.365 * 1 changes in 3600 seconds. Saving...
20715:M 13 Jun 2025 14:49:08.366 * Background saving started by pid 25618
25618:C 13 Jun 2025 14:49:08.373 * DB saved on disk
25618:C 13 Jun 2025 14:49:08.374 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 13 Jun 2025 14:49:08.467 * Background saving terminated with success
20715:M 13 Jun 2025 15:49:09.048 * 1 changes in 3600 seconds. Saving...
20715:M 13 Jun 2025 15:49:09.052 * Background saving started by pid 65690
65690:C 13 Jun 2025 15:49:09.064 * DB saved on disk
65690:C 13 Jun 2025 15:49:09.065 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 13 Jun 2025 15:49:09.155 * Background saving terminated with success
20715:M 13 Jun 2025 16:11:05.378 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 16:11:05.388 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 16:11:05.391 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 16:11:05.391 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 243423.
20715:M 13 Jun 2025 17:03:55.438 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 17:03:55.439 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 17:03:55.441 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 17:03:55.441 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 246559.
20715:M 13 Jun 2025 17:15:36.807 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 17:15:36.807 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 17:15:36.812 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 17:15:36.812 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 247245.
20715:M 13 Jun 2025 17:20:40.515 * 1 changes in 3600 seconds. Saving...
20715:M 13 Jun 2025 17:20:40.516 * Background saving started by pid 9747
9747:C 13 Jun 2025 17:20:40.523 * DB saved on disk
9747:C 13 Jun 2025 17:20:40.527 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 13 Jun 2025 17:20:40.618 * Background saving terminated with success
20715:M 13 Jun 2025 18:20:41.043 * 1 changes in 3600 seconds. Saving...
20715:M 13 Jun 2025 18:20:41.045 * Background saving started by pid 31408
31408:C 13 Jun 2025 18:20:41.055 * DB saved on disk
31408:C 13 Jun 2025 18:20:41.055 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 13 Jun 2025 18:20:41.148 * Background saving terminated with success
20715:M 13 Jun 2025 18:34:58.656 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 18:34:58.656 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 18:34:58.667 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 18:34:58.668 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 254707.
20715:M 13 Jun 2025 18:51:48.192 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 18:51:48.192 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 18:51:48.199 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 18:51:48.199 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 254763.
20715:M 13 Jun 2025 19:09:08.087 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 19:09:08.087 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 19:09:08.093 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 19:09:08.093 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 254847.
20715:M 13 Jun 2025 19:26:44.982 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 19:26:44.982 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 19:26:44.991 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 19:26:44.991 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 254861.
20715:M 13 Jun 2025 19:43:54.878 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 19:43:54.878 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 19:43:54.881 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 19:43:54.881 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 254861.
20715:M 13 Jun 2025 19:59:18.768 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 19:59:18.768 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 19:59:18.771 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 19:59:18.771 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 254875.
20715:M 13 Jun 2025 20:17:03.668 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 20:17:03.668 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 20:17:03.671 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 20:17:03.671 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 254875.
20715:M 13 Jun 2025 20:49:58.556 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 20:49:58.557 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 20:49:58.559 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 20:49:58.560 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 254988.
20715:M 13 Jun 2025 21:06:34.451 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 21:06:34.451 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 21:06:34.455 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 21:06:34.456 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 255002.
20715:M 13 Jun 2025 21:22:46.337 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 21:22:46.376 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 21:22:46.380 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 21:22:46.380 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 255002.
20715:M 13 Jun 2025 21:34:27.346 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 21:34:27.362 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 21:34:27.373 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 21:34:27.373 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 255016.
20715:M 13 Jun 2025 21:54:53.745 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 21:54:53.746 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 21:54:53.751 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 21:54:53.751 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 255226.
20715:M 13 Jun 2025 22:12:14.636 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 22:12:14.637 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 22:12:14.641 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 22:12:14.641 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 255240.
20715:M 13 Jun 2025 22:17:26.829 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 22:17:26.829 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 22:17:26.833 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 22:17:26.833 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 255240.
20715:M 13 Jun 2025 22:33:20.286 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 22:33:20.286 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 22:33:20.288 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 22:33:20.289 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 255310.
20715:M 13 Jun 2025 22:35:27.785 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 22:35:27.785 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 22:35:27.794 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 22:35:27.795 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 255366.
20715:M 13 Jun 2025 22:51:40.545 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 22:51:40.545 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 22:51:40.551 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 22:51:40.552 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 255436.
20715:M 13 Jun 2025 23:07:53.252 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 23:07:53.253 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 23:07:53.256 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 23:07:53.256 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 255492.
20715:M 13 Jun 2025 23:24:10.564 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 23:24:10.564 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 23:24:10.567 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 23:24:10.567 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 255562.
20715:M 13 Jun 2025 23:36:27.764 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 13 Jun 2025 23:36:27.764 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 23:36:27.768 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 23:36:27.769 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 255618.
20715:M 13 Jun 2025 23:52:45.171 * Connection with replica 127.0.0.1:7103 lost.
20715:M 13 Jun 2025 23:52:45.176 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 13 Jun 2025 23:52:45.176 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 255688.
20715:M 14 Jun 2025 00:09:02.483 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 00:09:02.484 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 00:09:02.488 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 00:09:02.488 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 255758.
20715:M 14 Jun 2025 00:25:15.370 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 00:25:15.370 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 00:25:15.374 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 00:25:15.374 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 255814.
20715:M 14 Jun 2025 00:37:27.407 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 00:37:27.433 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 00:37:27.448 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 00:37:27.448 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 255940.
20715:M 14 Jun 2025 00:53:40.140 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 00:53:40.253 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 00:53:40.255 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 00:53:40.255 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 256010.
20715:M 14 Jun 2025 01:09:52.963 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 01:09:53.095 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 01:09:53.099 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 01:09:53.099 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 256080.
20715:M 14 Jun 2025 01:26:05.785 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 01:26:05.897 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 01:26:05.903 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 01:26:05.903 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 256136.
20715:M 14 Jun 2025 01:38:27.330 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 01:38:27.466 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 01:38:27.469 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 01:38:27.469 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 256206.
20715:M 14 Jun 2025 01:54:40.169 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 01:54:40.283 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 01:54:40.286 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 01:54:40.286 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 256262.
20715:M 14 Jun 2025 01:58:31.241 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 01:58:31.241 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 01:58:31.252 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 01:58:31.252 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 256402.
20715:M 14 Jun 2025 02:02:22.035 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 02:02:22.036 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 02:02:22.039 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 02:02:22.039 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 256458.
20715:M 14 Jun 2025 02:18:34.703 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 02:18:34.704 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 02:18:34.707 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 02:18:34.707 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 256528.
20715:M 14 Jun 2025 02:34:47.412 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 02:34:47.412 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 02:34:47.418 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 02:34:47.419 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 256584.
20715:M 14 Jun 2025 02:51:00.103 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 02:51:00.103 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 02:51:00.106 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 02:51:00.106 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 256654.
20715:M 14 Jun 2025 03:07:11.963 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 03:07:11.964 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 03:07:11.967 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 03:07:11.967 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 256724.
20715:M 14 Jun 2025 03:23:21.724 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 03:23:21.724 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 03:23:21.727 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 03:23:21.728 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 256780.
20715:M 14 Jun 2025 03:39:32.317 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 03:39:32.318 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 03:39:32.323 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 03:39:32.323 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 256850.
20715:M 14 Jun 2025 03:55:44.982 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 03:55:44.994 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 03:55:44.995 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 256906.
20715:M 14 Jun 2025 04:11:57.679 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 04:11:57.679 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 04:11:57.682 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 04:11:57.682 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 256976.
20715:M 14 Jun 2025 04:28:10.528 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 04:28:10.531 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 04:28:10.531 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 257032.
20715:M 14 Jun 2025 04:44:05.348 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 04:44:05.368 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 04:44:05.371 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 04:44:05.371 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 257088.
20715:M 14 Jun 2025 05:00:15.063 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 05:00:15.063 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 05:00:15.068 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 05:00:15.069 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 257144.
20715:M 14 Jun 2025 05:02:19.971 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 05:02:19.971 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 05:02:19.974 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 05:02:19.974 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 257214.
20715:M 14 Jun 2025 05:18:32.762 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 05:18:32.762 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 05:18:32.765 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 05:18:32.766 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 257284.
20715:M 14 Jun 2025 05:30:34.341 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 05:30:34.373 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 05:30:34.375 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 05:30:34.375 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 257340.
20715:M 14 Jun 2025 05:46:31.235 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 05:46:31.353 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 05:46:31.356 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 05:46:31.356 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 257410.
20715:M 14 Jun 2025 06:02:28.668 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 06:02:28.669 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 06:02:28.675 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 06:02:28.675 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 257466.
20715:M 14 Jun 2025 06:18:13.991 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 06:18:13.991 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 06:18:13.998 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 06:18:13.999 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 257522.
20715:M 14 Jun 2025 06:34:25.619 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 06:34:25.620 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 06:34:25.628 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 06:34:25.628 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 257592.
20715:M 14 Jun 2025 06:50:39.241 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 06:50:39.242 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 06:50:39.246 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 06:50:39.247 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 257648.
20715:M 14 Jun 2025 07:06:51.923 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 07:06:51.923 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 07:06:51.928 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 07:06:51.928 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 257718.
20715:M 14 Jun 2025 07:22:56.543 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 07:22:56.543 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 07:22:56.546 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 07:22:56.546 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 257788.
20715:M 14 Jun 2025 07:31:34.635 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 07:31:34.635 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 07:31:34.638 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 07:31:34.639 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 257844.
20715:M 14 Jun 2025 07:47:47.272 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 07:47:47.272 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 07:47:47.279 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 07:47:47.280 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 257900.
20715:M 14 Jun 2025 07:51:58.412 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 07:51:58.414 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 07:51:58.415 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 257970.
20715:M 14 Jun 2025 08:08:11.073 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 08:08:11.085 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 08:08:11.087 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 08:08:11.088 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 258040.
20715:M 14 Jun 2025 08:24:23.948 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 08:24:23.951 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 08:24:23.951 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 258096.
20715:M 14 Jun 2025 08:32:34.388 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 08:32:34.390 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 08:32:34.390 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 258166.
20715:M 14 Jun 2025 08:48:23.399 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 08:48:23.401 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 08:48:23.401 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 258236.
20715:M 14 Jun 2025 09:04:32.882 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 09:04:32.882 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 09:04:32.885 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 09:04:32.886 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 258292.
20715:M 14 Jun 2025 09:20:18.385 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 09:20:18.398 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 09:20:18.412 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 09:20:18.412 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 258348.
20715:M 14 Jun 2025 09:33:34.344 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 09:33:34.346 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 09:33:34.346 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 258418.
20715:M 14 Jun 2025 09:49:46.343 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 09:49:46.346 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 09:49:46.346 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 258474.
20715:M 14 Jun 2025 10:00:35.312 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 10:00:35.315 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 10:00:35.315 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 258544.
20715:M 14 Jun 2025 10:58:01.885 * 1 changes in 3600 seconds. Saving...
20715:M 14 Jun 2025 10:58:01.886 * Background saving started by pid 77512
77512:C 14 Jun 2025 10:58:01.894 * DB saved on disk
77512:C 14 Jun 2025 10:58:01.895 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 14 Jun 2025 10:58:01.988 * Background saving terminated with success
20715:M 14 Jun 2025 11:58:02.029 * 1 changes in 3600 seconds. Saving...
20715:M 14 Jun 2025 11:58:02.031 * Background saving started by pid 2185
2185:C 14 Jun 2025 11:58:02.053 * DB saved on disk
2185:C 14 Jun 2025 11:58:02.054 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 14 Jun 2025 11:58:02.134 * Background saving terminated with success
20715:M 14 Jun 2025 14:01:00.908 * 1 changes in 3600 seconds. Saving...
20715:M 14 Jun 2025 14:01:00.909 * Background saving started by pid 51101
51101:C 14 Jun 2025 14:01:00.916 * DB saved on disk
51101:C 14 Jun 2025 14:01:00.916 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 14 Jun 2025 14:01:01.011 * Background saving terminated with success
20715:M 14 Jun 2025 15:01:02.084 * 1 changes in 3600 seconds. Saving...
20715:M 14 Jun 2025 15:01:02.086 * Background saving started by pid 74982
74982:C 14 Jun 2025 15:01:02.098 * DB saved on disk
74982:C 14 Jun 2025 15:01:02.099 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 14 Jun 2025 15:01:02.187 * Background saving terminated with success
20715:M 14 Jun 2025 17:28:33.657 * 1 changes in 3600 seconds. Saving...
20715:M 14 Jun 2025 17:28:33.658 * Background saving started by pid 32713
32713:C 14 Jun 2025 17:28:33.665 * DB saved on disk
32713:C 14 Jun 2025 17:28:33.665 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 14 Jun 2025 17:28:33.760 * Background saving terminated with success
20715:M 14 Jun 2025 18:28:34.098 * 1 changes in 3600 seconds. Saving...
20715:M 14 Jun 2025 18:28:34.100 * Background saving started by pid 55556
55556:C 14 Jun 2025 18:28:34.115 * DB saved on disk
55556:C 14 Jun 2025 18:28:34.115 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 14 Jun 2025 18:28:34.202 * Background saving terminated with success
20715:M 14 Jun 2025 20:29:37.316 * 1 changes in 3600 seconds. Saving...
20715:M 14 Jun 2025 20:29:37.317 * Background saving started by pid 2873
2873:C 14 Jun 2025 20:29:37.324 * DB saved on disk
2873:C 14 Jun 2025 20:29:37.324 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 14 Jun 2025 20:29:37.418 * Background saving terminated with success
20715:M 14 Jun 2025 21:06:41.816 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 21:06:41.816 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 21:06:41.830 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 21:06:41.832 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 317440.
20715:M 14 Jun 2025 21:22:46.619 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 21:22:46.620 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 21:22:46.623 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 21:22:46.623 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 317496.
20715:M 14 Jun 2025 21:38:52.340 * 1 changes in 3600 seconds. Saving...
20715:M 14 Jun 2025 21:38:52.459 * Background saving started by pid 11774
11774:C 14 Jun 2025 21:38:52.475 * DB saved on disk
11774:C 14 Jun 2025 21:38:52.475 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 14 Jun 2025 21:38:52.563 * Background saving terminated with success
20715:M 14 Jun 2025 21:38:52.665 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 21:38:52.665 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 21:38:52.668 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 21:38:52.669 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 317566.
20715:M 14 Jun 2025 21:55:05.909 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 21:55:05.910 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 21:55:05.920 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 21:55:05.921 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 317622.
20715:M 14 Jun 2025 22:03:24.864 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 14 Jun 2025 22:03:24.864 * Connection with replica 127.0.0.1:7103 lost.
20715:M 14 Jun 2025 22:03:24.868 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 14 Jun 2025 22:03:24.868 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 317720.
20715:M 15 Jun 2025 04:50:06.760 * Connection with replica 127.0.0.1:7103 lost.
20715:M 15 Jun 2025 04:50:06.761 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 15 Jun 2025 04:50:06.761 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 350075.
20715:M 15 Jun 2025 06:07:26.940 * Connection with replica 127.0.0.1:7103 lost.
20715:M 15 Jun 2025 06:07:26.942 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 15 Jun 2025 06:07:26.942 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 355143.
20715:M 15 Jun 2025 06:23:36.390 * Connection with replica 127.0.0.1:7103 lost.
20715:M 15 Jun 2025 06:23:36.392 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 15 Jun 2025 06:23:36.392 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 355199.
20715:M 15 Jun 2025 06:29:10.414 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 15 Jun 2025 06:29:10.415 * Connection with replica 127.0.0.1:7103 lost.
20715:M 15 Jun 2025 06:29:10.416 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 15 Jun 2025 06:29:10.417 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 355269.
20715:M 15 Jun 2025 06:57:18.473 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 15 Jun 2025 06:57:18.473 * Connection with replica 127.0.0.1:7103 lost.
20715:M 15 Jun 2025 06:57:18.482 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 15 Jun 2025 06:57:18.483 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 356249.
20715:M 15 Jun 2025 07:13:45.670 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 15 Jun 2025 07:13:45.670 * Connection with replica 127.0.0.1:7103 lost.
20715:M 15 Jun 2025 07:13:45.674 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 15 Jun 2025 07:13:45.675 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 356333.
20715:M 15 Jun 2025 08:30:10.912 * Connection with replica 127.0.0.1:7103 lost.
20715:M 15 Jun 2025 08:30:10.916 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 15 Jun 2025 08:30:10.916 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 361415.
20715:M 15 Jun 2025 08:46:22.797 * Connection with replica 127.0.0.1:7103 lost.
20715:M 15 Jun 2025 08:46:22.799 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 15 Jun 2025 08:46:22.800 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 361485.
20715:M 15 Jun 2025 09:03:23.887 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 15 Jun 2025 09:03:23.909 * Connection with replica 127.0.0.1:7103 lost.
20715:M 15 Jun 2025 09:03:23.911 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 15 Jun 2025 09:03:23.911 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 361611.
20715:M 15 Jun 2025 10:20:40.291 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 15 Jun 2025 10:20:40.291 * Connection with replica 127.0.0.1:7103 lost.
20715:M 15 Jun 2025 10:20:40.292 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 15 Jun 2025 10:20:40.292 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 366679.
20715:M 15 Jun 2025 10:31:10.407 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 15 Jun 2025 10:31:10.407 * Connection with replica 127.0.0.1:7103 lost.
20715:M 15 Jun 2025 10:31:10.409 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 15 Jun 2025 10:31:10.410 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 366735.
20715:M 15 Jun 2025 10:34:07.481 * Connection with replica 127.0.0.1:7103 lost.
20715:M 15 Jun 2025 10:34:07.484 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 15 Jun 2025 10:34:07.484 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 366805.
20715:M 15 Jun 2025 10:50:19.439 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 15 Jun 2025 10:50:19.439 * Connection with replica 127.0.0.1:7103 lost.
20715:M 15 Jun 2025 10:50:19.444 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 15 Jun 2025 10:50:19.445 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 366875.
20715:M 15 Jun 2025 11:07:09.105 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 15 Jun 2025 11:07:09.138 * Connection with replica 127.0.0.1:7103 lost.
20715:M 15 Jun 2025 11:07:09.141 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 15 Jun 2025 11:07:09.141 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 366987.
20715:M 15 Jun 2025 12:11:18.274 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 15 Jun 2025 12:11:18.275 * Connection with replica 127.0.0.1:7103 lost.
20715:M 15 Jun 2025 12:11:18.277 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 15 Jun 2025 12:11:18.277 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 370977.
20715:M 15 Jun 2025 12:15:17.992 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 15 Jun 2025 12:15:18.105 * Connection with replica 127.0.0.1:7103 lost.
20715:M 15 Jun 2025 12:15:18.111 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 15 Jun 2025 12:15:18.111 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 371033.
20715:M 15 Jun 2025 13:02:29.852 * 1 changes in 3600 seconds. Saving...
20715:M 15 Jun 2025 13:02:29.853 * Background saving started by pid 65583
65583:C 15 Jun 2025 13:02:29.870 * DB saved on disk
65583:C 15 Jun 2025 13:02:29.871 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 15 Jun 2025 13:02:29.955 * Background saving terminated with success
20715:M 15 Jun 2025 14:02:30.042 * 1 changes in 3600 seconds. Saving...
20715:M 15 Jun 2025 14:02:30.043 * Background saving started by pid 87506
87506:C 15 Jun 2025 14:02:30.056 * DB saved on disk
87506:C 15 Jun 2025 14:02:30.060 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 15 Jun 2025 14:02:30.146 * Background saving terminated with success
20715:M 15 Jun 2025 16:24:12.535 * Connection with replica 127.0.0.1:7103 lost.
20715:M 15 Jun 2025 16:24:12.537 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 15 Jun 2025 16:24:12.537 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 391400.
20715:M 15 Jun 2025 16:40:22.517 * Connection with replica 127.0.0.1:7103 lost.
20715:M 15 Jun 2025 16:40:22.519 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 15 Jun 2025 16:40:22.519 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 391470.
20715:M 15 Jun 2025 17:49:12.901 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
20715:M 15 Jun 2025 17:49:12.901 * Connection with replica 127.0.0.1:7103 lost.
20715:M 15 Jun 2025 17:49:12.904 * Replica 127.0.0.1:7103 asks for synchronization
20715:M 15 Jun 2025 17:49:12.904 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 396594.
20715:M 15 Jun 2025 18:05:36.303 * 1 changes in 3600 seconds. Saving...
20715:M 15 Jun 2025 18:05:36.309 * Background saving started by pid 66592
66592:C 15 Jun 2025 18:05:36.316 * DB saved on disk
66592:C 15 Jun 2025 18:05:36.316 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 15 Jun 2025 18:05:36.411 * Background saving terminated with success
20715:M 15 Jun 2025 19:05:37.076 * 1 changes in 3600 seconds. Saving...
20715:M 15 Jun 2025 19:05:37.078 * Background saving started by pid 89442
89442:C 15 Jun 2025 19:05:37.089 * DB saved on disk
89442:C 15 Jun 2025 19:05:37.091 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20715:M 15 Jun 2025 19:05:37.180 * Background saving terminated with success
20715:signal-handler (1749990448) Received SIGTERM scheduling shutdown...
20715:M 15 Jun 2025 20:27:28.738 * User requested shutdown...
20715:M 15 Jun 2025 20:27:28.739 * 1 of 1 replicas are in sync when shutting down.
20715:M 15 Jun 2025 20:27:28.739 * Calling fsync() on the AOF file.
20715:M 15 Jun 2025 20:27:28.750 * Saving the final RDB snapshot before exiting.
20715:M 15 Jun 2025 20:27:28.762 * DB saved on disk
20715:M 15 Jun 2025 20:27:28.762 * Removing the pid file.
20715:M 15 Jun 2025 20:27:28.763 # Redis is now ready to exit, bye bye...
12936:C 15 Jun 2025 20:50:01.047 * oO0OoO0OoO0Oo Redis is starting oO0OoO0OoO0Oo
12936:C 15 Jun 2025 20:50:01.047 * Redis version=7.2.7, bits=64, commit=00000000, modified=0, pid=12936, just started
12936:C 15 Jun 2025 20:50:01.047 * Configuration loaded
12936:M 15 Jun 2025 20:50:01.048 * Increased maximum number of open files to 10032 (it was originally set to 256).
12936:M 15 Jun 2025 20:50:01.048 * monotonic clock: POSIX clock_gettime
12936:M 15 Jun 2025 20:50:01.048 * Running mode=cluster, port=7100.
12936:M 15 Jun 2025 20:50:01.048 # WARNING: The TCP backlog setting of 511 cannot be enforced because kern.ipc.somaxconn is set to the lower value of 128.
12936:M 15 Jun 2025 20:50:01.048 * Node configuration loaded, I'm f430caf1ef6d7b8d092d782bcc2057e678d0abf6
12936:M 15 Jun 2025 20:50:01.048 * Server initialized
12936:M 15 Jun 2025 20:50:01.049 * Reading RDB base file on AOF loading...
12936:M 15 Jun 2025 20:50:01.049 * Loading RDB produced by version 7.2.7
12936:M 15 Jun 2025 20:50:01.049 * RDB age 540933 seconds
12936:M 15 Jun 2025 20:50:01.049 * RDB memory usage when created 1.70 Mb
12936:M 15 Jun 2025 20:50:01.049 * RDB is base AOF
12936:M 15 Jun 2025 20:50:01.049 * Done loading RDB, keys loaded: 0, keys expired: 0.
12936:M 15 Jun 2025 20:50:01.049 * DB loaded from base file appendonly-7100.aof.1.base.rdb: 0.000 seconds
12936:M 15 Jun 2025 20:50:01.056 * DB saved on disk
12936:M 15 Jun 2025 20:50:01.064 * DB saved on disk
12936:M 15 Jun 2025 20:50:01.069 * DB saved on disk
12936:M 15 Jun 2025 20:50:01.078 * DB saved on disk
12936:M 15 Jun 2025 20:50:01.083 * DB saved on disk
12936:M 15 Jun 2025 20:50:01.088 * DB saved on disk
12936:M 15 Jun 2025 20:50:01.094 * DB saved on disk
12936:M 15 Jun 2025 20:50:01.099 * DB saved on disk
12936:M 15 Jun 2025 20:50:01.103 * DB saved on disk
12936:M 15 Jun 2025 20:50:01.108 * DB saved on disk
12936:M 15 Jun 2025 20:50:01.114 * DB saved on disk
12936:M 15 Jun 2025 20:50:01.114 * DB loaded from incr file appendonly-7100.aof.1.incr.aof: 0.065 seconds
12936:M 15 Jun 2025 20:50:01.114 * DB loaded from append only file: 0.065 seconds
12936:M 15 Jun 2025 20:50:01.114 * Opening AOF incr file appendonly-7100.aof.1.incr.aof on server start
12936:M 15 Jun 2025 20:50:01.114 * Ready to accept connections tcp
12936:M 15 Jun 2025 20:50:03.151 * Cluster state changed: ok
12936:M 15 Jun 2025 20:50:07.255 * Replica 127.0.0.1:7103 asks for synchronization
12936:M 15 Jun 2025 20:50:07.256 * Partial resynchronization not accepted: Replication ID mismatch (Replica asked for '952ea10dfbe37acf1fd690914fb90fb25df93494', my replication IDs are 'a98d8224fc017091cc26d8888743a74de71946f0' and '0000000000000000000000000000000000000000')
12936:M 15 Jun 2025 20:50:07.256 * Replication backlog created, my new replication IDs are '8945a3b5c65069c9857f7324c64bc1daabfd9f4f' and '0000000000000000000000000000000000000000'
12936:M 15 Jun 2025 20:50:07.256 * Delay next BGSAVE for diskless SYNC
12936:M 15 Jun 2025 20:50:12.329 * Starting BGSAVE for SYNC with target: replicas sockets
12936:M 15 Jun 2025 20:50:12.331 * Background RDB transfer started by pid 13021
13021:C 15 Jun 2025 20:50:12.332 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 15 Jun 2025 20:50:12.333 * Diskless rdb transfer, done reading from pipe, 1 replicas still up.
12936:M 15 Jun 2025 20:50:12.345 * Background RDB transfer terminated with success
12936:M 15 Jun 2025 20:50:12.345 * Streamed RDB transfer with replica 127.0.0.1:7103 succeeded (socket). Waiting for REPLCONF ACK from replica to enable streaming
12936:M 15 Jun 2025 20:50:12.345 * Synchronization with replica 127.0.0.1:7103 succeeded
12936:M 15 Jun 2025 20:50:16.329 * DB saved on disk
12936:M 15 Jun 2025 20:50:16.336 * configEpoch set to 0 via CLUSTER RESET HARD
12936:M 15 Jun 2025 20:50:16.336 * Node hard reset, now I'm ae5435a2fdf1e95cd782d831916bd1b9c024f143
12936:M 15 Jun 2025 20:50:16.340 # Cluster state changed: fail
12936:M 15 Jun 2025 20:50:16.461 * Connection with replica 127.0.0.1:7103 lost.
12936:M 15 Jun 2025 20:50:19.555 * configEpoch set to 1 via CLUSTER SET-CONFIG-EPOCH
12936:M 15 Jun 2025 20:50:21.582 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 15 Jun 2025 20:50:21.582 * Partial resynchronization not accepted: Replication ID mismatch (Replica asked for '7e72aa789f032895cee5832ded78ea6d70b384b4', my replication IDs are '8945a3b5c65069c9857f7324c64bc1daabfd9f4f' and '0000000000000000000000000000000000000000')
12936:M 15 Jun 2025 20:50:21.582 * Delay next BGSAVE for diskless SYNC
12936:M 15 Jun 2025 20:50:24.586 * Cluster state changed: ok
12936:M 15 Jun 2025 20:50:26.510 * Starting BGSAVE for SYNC with target: replicas sockets
12936:M 15 Jun 2025 20:50:26.511 * Background RDB transfer started by pid 13151
13151:C 15 Jun 2025 20:50:26.512 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 15 Jun 2025 20:50:26.512 * Diskless rdb transfer, done reading from pipe, 1 replicas still up.
12936:M 15 Jun 2025 20:50:26.539 * Background RDB transfer terminated with success
12936:M 15 Jun 2025 20:50:26.539 * Streamed RDB transfer with replica 127.0.0.1:7105 succeeded (socket). Waiting for REPLCONF ACK from replica to enable streaming
12936:M 15 Jun 2025 20:50:26.540 * Synchronization with replica 127.0.0.1:7105 succeeded
12936:M 15 Jun 2025 21:50:17.043 * 1 changes in 3600 seconds. Saving...
12936:M 15 Jun 2025 21:50:17.046 * Background saving started by pid 36290
36290:C 15 Jun 2025 21:50:17.059 * DB saved on disk
36290:C 15 Jun 2025 21:50:17.064 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 15 Jun 2025 21:50:17.147 * Background saving terminated with success
12936:M 16 Jun 2025 00:16:52.393 * 1 changes in 3600 seconds. Saving...
12936:M 16 Jun 2025 00:16:52.394 * Background saving started by pid 98198
98198:C 16 Jun 2025 00:16:52.403 * DB saved on disk
98198:C 16 Jun 2025 00:16:52.404 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 16 Jun 2025 00:16:52.496 * Background saving terminated with success
12936:M 16 Jun 2025 01:10:07.558 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 01:10:07.560 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 01:10:07.560 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 22164.
12936:M 16 Jun 2025 01:26:20.160 * 1 changes in 3600 seconds. Saving...
12936:M 16 Jun 2025 01:26:20.279 * Background saving started by pid 13822
12936:M 16 Jun 2025 01:26:20.279 * Connection with replica 127.0.0.1:7105 lost.
13822:C 16 Jun 2025 01:26:20.284 * DB saved on disk
13822:C 16 Jun 2025 01:26:20.284 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 16 Jun 2025 01:26:20.284 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 01:26:20.284 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 22220.
12936:M 16 Jun 2025 01:26:20.381 * Background saving terminated with success
12936:M 16 Jun 2025 01:42:32.242 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 01:42:32.244 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 01:42:32.245 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 22290.
12936:M 16 Jun 2025 01:58:45.621 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 01:58:45.622 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 01:58:45.626 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 01:58:45.627 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 22360.
12936:M 16 Jun 2025 02:14:58.307 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 02:14:58.307 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 02:14:58.321 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 02:14:58.321 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 22416.
12936:M 16 Jun 2025 02:31:02.777 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 02:31:02.777 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 02:31:02.780 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 02:31:02.780 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 22486.
12936:M 16 Jun 2025 02:47:11.867 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 02:47:11.868 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 02:47:11.870 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 02:47:11.870 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 22542.
12936:M 16 Jun 2025 02:53:37.463 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 02:53:37.581 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 02:53:37.584 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 02:53:37.584 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 22598.
12936:M 16 Jun 2025 03:09:47.474 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 03:09:47.602 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 03:09:47.605 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 03:09:47.605 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 22668.
12936:M 16 Jun 2025 03:26:01.268 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 03:26:01.268 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 03:26:01.274 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 03:26:01.274 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 22781.
12936:M 16 Jun 2025 03:34:19.691 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 03:34:19.691 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 03:34:19.698 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 03:34:19.698 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 22837.
12936:M 16 Jun 2025 03:36:03.501 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 03:36:03.505 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 03:36:03.505 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 22907.
12936:M 16 Jun 2025 03:52:11.049 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 03:52:11.049 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 03:52:11.052 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 03:52:11.052 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 22963.
12936:M 16 Jun 2025 04:08:09.516 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 04:08:09.517 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 04:08:09.517 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 23033.
12936:M 16 Jun 2025 04:24:17.933 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 04:24:18.051 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 04:24:18.053 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 04:24:18.054 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 23089.
12936:M 16 Jun 2025 04:40:30.752 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 04:40:30.876 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 04:40:30.879 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 04:40:30.879 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 23159.
12936:M 16 Jun 2025 04:56:43.688 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 04:56:43.690 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 04:56:43.690 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 23229.
12936:M 16 Jun 2025 05:12:55.704 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 05:12:55.706 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 05:12:55.706 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 23285.
12936:M 16 Jun 2025 05:29:08.512 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 05:29:08.514 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 05:29:08.514 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 23355.
12936:M 16 Jun 2025 05:45:21.329 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 05:45:21.331 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 05:45:21.331 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 23411.
12936:M 16 Jun 2025 06:01:34.101 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 06:01:34.103 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 06:01:34.103 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 23481.
12936:M 16 Jun 2025 06:17:46.927 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 06:17:46.929 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 06:17:46.929 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 23551.
12936:M 16 Jun 2025 06:33:59.716 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 06:33:59.719 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 06:33:59.719 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 23607.
12936:M 16 Jun 2025 06:50:12.525 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 06:50:12.527 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 06:50:12.528 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 23677.
12936:M 16 Jun 2025 07:06:24.393 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 07:06:24.404 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 07:06:24.407 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 07:06:24.408 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 23733.
12936:M 16 Jun 2025 07:22:33.036 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 07:22:33.036 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 07:22:33.038 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 07:22:33.038 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 23803.
12936:M 16 Jun 2025 07:38:24.523 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 07:38:24.523 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 07:38:24.527 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 07:38:24.527 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 23859.
12936:M 16 Jun 2025 07:54:32.345 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 07:54:32.465 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 07:54:32.468 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 07:54:32.468 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 23915.
12936:M 16 Jun 2025 07:57:36.284 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 07:57:36.415 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 07:57:36.417 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 07:57:36.417 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 23985.
12936:M 16 Jun 2025 08:02:21.099 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 08:02:21.226 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 08:02:21.236 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 08:02:21.236 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 24055.
12936:M 16 Jun 2025 08:18:33.102 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 08:18:33.219 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 08:18:33.222 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 08:18:33.222 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 24111.
12936:M 16 Jun 2025 08:34:45.136 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 08:34:45.255 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 08:34:45.257 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 08:34:45.258 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 24181.
12936:M 16 Jun 2025 08:51:12.972 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 08:51:12.974 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 08:51:12.983 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 08:51:12.983 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 24307.
12936:M 16 Jun 2025 09:09:25.081 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 09:09:25.081 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 09:09:25.087 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 09:09:25.087 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 24503.
12936:M 16 Jun 2025 09:24:49.982 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 09:24:49.982 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 09:24:49.984 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 09:24:49.984 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 24517.
12936:M 16 Jun 2025 09:36:11.202 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 09:36:11.208 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 09:36:11.208 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 24517.
12936:M 16 Jun 2025 09:40:38.255 * 1 changes in 3600 seconds. Saving...
12936:M 16 Jun 2025 09:40:38.256 * Background saving started by pid 27148
27148:C 16 Jun 2025 09:40:38.267 * DB saved on disk
27148:C 16 Jun 2025 09:40:38.267 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 16 Jun 2025 09:40:38.358 * Background saving terminated with success
12936:M 16 Jun 2025 10:07:18.687 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 10:07:18.687 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 10:07:18.691 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 10:07:18.691 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 27453.
12936:M 16 Jun 2025 10:15:48.054 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 10:15:48.056 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 10:15:48.061 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 10:15:48.062 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 27523.
12936:M 16 Jun 2025 10:25:12.100 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 10:25:12.100 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 10:25:12.103 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 10:25:12.103 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 28013.
12936:M 16 Jun 2025 10:40:39.020 * 1 changes in 3600 seconds. Saving...
12936:M 16 Jun 2025 10:40:39.021 * Background saving started by pid 45238
45238:C 16 Jun 2025 10:40:39.031 * DB saved on disk
45238:C 16 Jun 2025 10:40:39.032 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 16 Jun 2025 10:40:39.124 * Background saving terminated with success
12936:M 16 Jun 2025 12:38:17.925 * 1 changes in 3600 seconds. Saving...
12936:M 16 Jun 2025 12:38:17.928 * Background saving started by pid 96387
96387:C 16 Jun 2025 12:38:17.937 * DB saved on disk
96387:C 16 Jun 2025 12:38:17.937 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 16 Jun 2025 12:38:18.029 * Background saving terminated with success
12936:M 16 Jun 2025 13:30:23.538 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 13:30:23.543 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 13:30:23.550 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 42961.
12936:M 16 Jun 2025 13:47:57.409 * 1 changes in 3600 seconds. Saving...
12936:M 16 Jun 2025 13:47:57.531 * Background saving started by pid 11865
11865:C 16 Jun 2025 13:47:57.541 * DB saved on disk
11865:C 16 Jun 2025 13:47:57.542 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 16 Jun 2025 13:47:57.633 * Background saving terminated with success
12936:M 16 Jun 2025 13:47:58.255 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 13:47:58.267 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 13:47:58.267 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 43087.
12936:M 16 Jun 2025 14:05:49.025 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 14:05:49.026 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 14:05:49.031 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 14:05:49.032 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 43213.
12936:M 16 Jun 2025 15:40:11.235 * 1 changes in 3600 seconds. Saving...
12936:M 16 Jun 2025 15:40:11.236 * Background saving started by pid 51414
51414:C 16 Jun 2025 15:40:11.242 * DB saved on disk
51414:C 16 Jun 2025 15:40:11.242 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 16 Jun 2025 15:40:11.337 * Background saving terminated with success
12936:M 16 Jun 2025 16:40:12.070 * 1 changes in 3600 seconds. Saving...
12936:M 16 Jun 2025 16:40:12.072 * Background saving started by pid 74009
74009:C 16 Jun 2025 16:40:12.080 * DB saved on disk
74009:C 16 Jun 2025 16:40:12.081 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 16 Jun 2025 16:40:12.174 * Background saving terminated with success
12936:M 16 Jun 2025 18:49:25.092 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 18:49:25.094 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 18:49:25.095 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 67529.
12936:M 16 Jun 2025 18:52:08.402 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 18:52:08.404 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 18:52:08.404 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 67655.
12936:M 16 Jun 2025 18:55:50.061 * 1 changes in 3600 seconds. Saving...
12936:M 16 Jun 2025 18:55:50.064 * Background saving started by pid 24628
24628:C 16 Jun 2025 18:55:50.072 * DB saved on disk
24628:C 16 Jun 2025 18:55:50.073 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 16 Jun 2025 18:55:50.166 * Background saving terminated with success
12936:M 16 Jun 2025 19:55:51.026 * 1 changes in 3600 seconds. Saving...
12936:M 16 Jun 2025 19:55:51.028 * Background saving started by pid 48831
48831:C 16 Jun 2025 19:55:51.034 * DB saved on disk
48831:C 16 Jun 2025 19:55:51.035 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 16 Jun 2025 19:55:51.129 * Background saving terminated with success
12936:M 16 Jun 2025 20:10:13.696 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 20:10:13.700 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 20:10:13.700 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 75015.
12936:M 16 Jun 2025 20:26:42.563 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 20:26:42.570 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 20:26:42.570 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 75071.
12936:M 16 Jun 2025 20:44:24.572 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 20:44:24.574 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 20:44:24.574 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 75085.
12936:M 16 Jun 2025 21:01:30.593 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 21:01:30.594 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 21:01:30.594 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 75085.
12936:M 16 Jun 2025 21:07:13.584 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 21:07:13.587 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 21:07:13.587 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 75099.
12936:M 16 Jun 2025 21:23:10.594 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 21:23:10.600 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 21:23:10.600 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 75155.
12936:M 16 Jun 2025 21:40:08.598 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 21:40:08.599 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 21:40:08.606 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 21:40:08.606 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 75169.
12936:M 16 Jun 2025 21:57:33.614 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 21:57:33.631 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 21:57:33.648 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 21:57:33.648 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 43 bytes of backlog starting from offset 75169.
12936:M 16 Jun 2025 22:10:04.586 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 22:10:04.610 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 22:10:04.619 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 22:10:04.619 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 75226.
12936:M 16 Jun 2025 22:28:33.595 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 22:28:33.614 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 22:28:33.634 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 22:28:33.634 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 75282.
12936:M 16 Jun 2025 22:44:42.601 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 22:44:42.624 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 22:44:42.636 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 22:44:42.636 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 75296.
12936:M 16 Jun 2025 22:57:46.535 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 22:57:46.560 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 22:57:46.563 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 22:57:46.563 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 75296.
12936:M 16 Jun 2025 23:11:04.513 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 23:11:04.646 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 23:11:04.649 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 23:11:04.649 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 75366.
12936:M 16 Jun 2025 23:27:17.290 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 23:27:17.410 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 23:27:17.413 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 23:27:17.413 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 75436.
12936:M 16 Jun 2025 23:30:19.306 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 23:30:19.442 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 23:30:19.445 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 23:30:19.445 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 75492.
12936:M 16 Jun 2025 23:46:33.061 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 16 Jun 2025 23:46:33.191 * Connection with replica 127.0.0.1:7105 lost.
12936:M 16 Jun 2025 23:46:33.194 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 16 Jun 2025 23:46:33.195 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 75562.
12936:M 17 Jun 2025 00:02:45.862 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 00:02:45.985 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 00:02:45.993 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 00:02:45.993 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 75618.
12936:M 17 Jun 2025 00:12:04.115 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 00:12:04.243 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 00:12:04.246 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 00:12:04.246 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 75688.
12936:M 17 Jun 2025 00:28:16.851 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 00:28:16.971 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 00:28:16.976 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 00:28:16.976 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 75758.
12936:M 17 Jun 2025 00:39:48.458 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 00:39:48.577 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 00:39:48.581 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 00:39:48.581 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 75814.
12936:M 17 Jun 2025 01:12:25.352 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 01:12:25.352 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 01:12:25.355 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 01:12:25.356 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 77242.
12936:M 17 Jun 2025 01:28:38.003 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 01:28:38.004 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 01:28:38.008 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 01:28:38.009 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 77298.
12936:M 17 Jun 2025 01:44:51.636 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 01:44:51.637 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 01:44:51.645 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 01:44:51.646 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 77368.
12936:M 17 Jun 2025 02:01:04.443 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 02:01:04.443 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 02:01:04.446 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 02:01:04.446 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 77438.
12936:M 17 Jun 2025 02:17:16.994 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 02:17:16.995 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 02:17:16.998 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 02:17:16.998 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 77494.
12936:M 17 Jun 2025 02:33:29.659 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 02:33:29.659 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 02:33:29.662 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 02:33:29.662 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 77564.
12936:M 17 Jun 2025 02:49:42.337 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 02:49:42.359 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 02:49:42.367 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 02:49:42.367 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 77634.
12936:M 17 Jun 2025 02:56:31.227 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 02:56:31.228 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 02:56:31.232 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 02:56:31.233 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 77690.
12936:M 17 Jun 2025 03:12:43.722 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 03:12:43.722 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 03:12:43.730 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 03:12:43.731 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 77760.
12936:M 17 Jun 2025 03:15:50.651 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 03:15:50.652 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 03:15:50.665 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 03:15:50.665 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 77816.
12936:M 17 Jun 2025 03:32:03.418 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 03:32:03.421 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 03:32:03.421 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 77886.
12936:M 17 Jun 2025 03:48:16.137 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 03:48:16.137 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 03:48:16.143 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 03:48:16.144 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 77956.
12936:M 17 Jun 2025 04:04:28.656 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 04:04:28.681 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 04:04:28.695 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 04:04:28.695 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 78012.
12936:M 17 Jun 2025 04:20:40.600 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 04:20:40.714 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 04:20:40.717 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 04:20:40.717 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 78082.
12936:M 17 Jun 2025 04:36:53.534 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 04:36:53.648 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 04:36:53.664 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 04:36:53.664 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 78138.
12936:M 17 Jun 2025 04:53:06.354 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 04:53:06.494 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 04:53:06.497 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 04:53:06.497 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 78208.
12936:M 17 Jun 2025 05:09:19.083 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 05:09:19.199 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 05:09:19.204 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 05:09:19.204 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 78278.
12936:M 17 Jun 2025 05:20:13.406 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 05:20:13.518 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 05:20:13.521 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 05:20:13.521 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 78334.
12936:M 17 Jun 2025 05:36:27.162 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 05:36:27.164 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 05:36:27.170 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 05:36:27.171 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 78404.
12936:M 17 Jun 2025 05:52:39.766 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 05:52:39.766 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 05:52:39.773 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 05:52:39.773 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 78474.
12936:M 17 Jun 2025 06:08:51.650 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 06:08:51.650 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 06:08:51.653 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 06:08:51.653 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 78530.
12936:M 17 Jun 2025 06:25:03.501 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 06:25:03.501 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 06:25:03.504 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 06:25:03.504 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 78600.
12936:M 17 Jun 2025 06:41:17.180 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 06:41:17.181 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 06:41:17.187 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 06:41:17.187 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 78656.
12936:M 17 Jun 2025 06:53:02.021 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 06:53:02.021 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 06:53:02.031 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 06:53:02.031 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 78726.
12936:M 17 Jun 2025 07:09:15.623 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 07:09:15.623 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 07:09:15.627 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 07:09:15.627 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 78796.
12936:M 17 Jun 2025 07:25:27.497 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 07:25:27.497 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 07:25:27.503 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 07:25:27.504 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 78852.
12936:M 17 Jun 2025 07:41:40.031 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 07:41:40.031 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 07:41:40.032 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 07:41:40.032 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 78922.
12936:M 17 Jun 2025 07:57:51.782 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 07:57:51.901 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 07:57:51.905 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 07:57:51.905 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 78978.
12936:M 17 Jun 2025 08:14:05.405 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 08:14:05.406 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 08:14:05.413 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 08:14:05.413 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 79048.
12936:M 17 Jun 2025 08:30:18.106 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 08:30:18.110 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 08:30:18.110 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 79118.
12936:M 17 Jun 2025 08:34:58.730 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 08:34:58.730 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 08:34:58.733 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 08:34:58.733 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 79174.
12936:M 17 Jun 2025 08:43:15.900 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 08:43:15.900 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 08:43:15.905 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 08:43:15.905 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 79244.
12936:M 17 Jun 2025 09:00:42.255 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 09:00:42.255 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 09:00:42.261 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 09:00:42.262 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 79370.
12936:M 17 Jun 2025 09:18:19.256 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 09:18:19.257 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 09:18:19.263 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 09:18:19.263 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 79370.
12936:M 17 Jun 2025 09:36:07.236 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 09:36:07.237 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 09:36:07.246 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 09:36:07.246 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 79384.
12936:M 17 Jun 2025 09:44:44.164 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 09:44:44.167 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 09:44:44.169 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 79384.
12936:M 17 Jun 2025 10:34:38.963 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 10:34:38.964 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 10:34:38.967 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 10:34:38.967 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 82940.
12936:M 17 Jun 2025 10:42:46.428 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 10:42:46.539 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 10:42:46.542 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 10:42:46.542 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 83010.
12936:M 17 Jun 2025 10:59:31.074 * 1 changes in 3600 seconds. Saving...
12936:M 17 Jun 2025 10:59:31.075 * Background saving started by pid 98513
98513:C 17 Jun 2025 10:59:31.091 * DB saved on disk
98513:C 17 Jun 2025 10:59:31.091 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 17 Jun 2025 10:59:31.177 * Background saving terminated with success
12936:M 17 Jun 2025 11:59:32.082 * 1 changes in 3600 seconds. Saving...
12936:M 17 Jun 2025 11:59:32.084 * Background saving started by pid 23692
23692:C 17 Jun 2025 11:59:32.101 * DB saved on disk
23692:C 17 Jun 2025 11:59:32.101 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 17 Jun 2025 11:59:32.186 * Background saving terminated with success
12936:M 17 Jun 2025 13:18:04.544 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 13:18:04.544 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 13:18:04.547 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 13:18:04.548 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 95522.
12936:M 17 Jun 2025 13:33:13.749 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 13:33:13.750 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 13:33:13.753 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 13:33:13.753 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 95676.
12936:M 17 Jun 2025 13:51:00.857 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 13:51:00.858 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 13:51:00.864 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 13:51:00.864 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 95746.
12936:M 17 Jun 2025 14:03:44.162 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 14:03:44.163 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 14:03:44.167 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 14:03:44.168 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 95845.
12936:M 17 Jun 2025 14:09:43.557 * 1 changes in 3600 seconds. Saving...
12936:M 17 Jun 2025 14:09:43.558 * Background saving started by pid 75607
75607:C 17 Jun 2025 14:09:43.566 * DB saved on disk
75607:C 17 Jun 2025 14:09:43.567 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 17 Jun 2025 14:09:43.660 * Background saving terminated with success
12936:M 17 Jun 2025 14:56:35.574 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 14:56:35.575 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 14:56:35.583 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 14:56:35.583 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 99929.
12936:M 17 Jun 2025 15:07:50.125 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 15:07:50.126 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 15:07:50.126 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 99985.
12936:M 17 Jun 2025 15:09:44.063 * 1 changes in 3600 seconds. Saving...
12936:M 17 Jun 2025 15:09:44.064 * Background saving started by pid 99295
99295:C 17 Jun 2025 15:09:44.070 * DB saved on disk
99295:C 17 Jun 2025 15:09:44.072 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 17 Jun 2025 15:09:44.165 * Background saving terminated with success
12936:M 17 Jun 2025 15:57:30.757 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 15:57:30.760 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 15:57:30.761 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 103807.
12936:M 17 Jun 2025 16:02:20.712 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 16:02:20.716 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 16:02:20.716 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 103863.
12936:M 17 Jun 2025 16:19:42.549 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 16:19:42.551 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 16:19:42.551 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 103933.
12936:M 17 Jun 2025 16:38:03.744 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 16:38:03.747 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 16:38:03.748 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 104087.
12936:M 17 Jun 2025 16:55:21.542 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 16:55:21.543 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 16:55:21.543 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 104227.
12936:M 17 Jun 2025 16:57:58.255 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 16:57:58.257 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 16:57:58.257 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 104297.
12936:M 17 Jun 2025 17:13:42.429 * 1 changes in 3600 seconds. Saving...
12936:M 17 Jun 2025 17:13:42.430 * Background saving started by pid 44911
44911:C 17 Jun 2025 17:13:42.438 * DB saved on disk
44911:C 17 Jun 2025 17:13:42.438 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 17 Jun 2025 17:13:42.531 * Background saving terminated with success
12936:M 17 Jun 2025 18:13:43.096 * 1 changes in 3600 seconds. Saving...
12936:M 17 Jun 2025 18:13:43.099 * Background saving started by pid 85796
85796:C 17 Jun 2025 18:13:43.107 * DB saved on disk
85796:C 17 Jun 2025 18:13:43.108 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 17 Jun 2025 18:13:43.202 * Background saving terminated with success
12936:M 17 Jun 2025 18:59:02.095 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 18:59:02.095 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 18:59:02.098 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 18:59:02.098 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 114864.
12936:M 17 Jun 2025 19:16:26.692 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 19:16:26.692 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 19:16:26.695 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 19:16:26.696 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 114934.
12936:M 17 Jun 2025 19:32:18.591 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 19:32:18.591 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 19:32:18.594 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 19:32:18.594 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 114934.
12936:M 17 Jun 2025 19:49:03.521 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 19:49:03.525 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 19:49:03.527 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 19:49:03.527 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 114948.
12936:M 17 Jun 2025 20:05:34.516 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 20:05:34.516 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 20:05:34.523 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 20:05:34.524 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 114948.
12936:M 17 Jun 2025 20:21:22.398 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 20:21:22.517 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 20:21:22.537 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 20:21:22.537 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 43 bytes of backlog starting from offset 114962.
12936:M 17 Jun 2025 20:36:26.534 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 20:36:26.535 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 20:36:26.537 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 20:36:26.537 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 115005.
12936:M 17 Jun 2025 20:51:56.526 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 20:51:56.526 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 20:51:56.539 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 20:51:56.539 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 115019.
12936:M 17 Jun 2025 20:54:17.397 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 20:54:17.538 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 20:54:17.540 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 20:54:17.541 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 115019.
12936:M 17 Jun 2025 21:10:38.550 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 21:10:38.550 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 21:10:38.562 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 21:10:38.562 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 115089.
12936:M 17 Jun 2025 21:15:53.769 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 17 Jun 2025 21:15:53.788 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 21:15:53.807 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 21:15:53.807 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 115089.
12936:M 17 Jun 2025 21:17:38.916 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 21:17:38.916 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 21:17:38.916 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 115159.
12936:M 17 Jun 2025 21:33:51.604 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 21:33:51.607 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 21:33:51.607 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 115229.
12936:M 17 Jun 2025 21:46:13.491 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 21:46:13.494 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 21:46:13.494 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 115285.
12936:M 17 Jun 2025 21:55:17.486 * Connection with replica 127.0.0.1:7105 lost.
12936:M 17 Jun 2025 21:55:17.488 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 17 Jun 2025 21:55:17.488 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 115355.
12936:M 17 Jun 2025 22:22:14.927 * 1 changes in 3600 seconds. Saving...
12936:M 17 Jun 2025 22:22:14.928 * Background saving started by pid 36830
36830:C 17 Jun 2025 22:22:14.937 * DB saved on disk
36830:C 17 Jun 2025 22:22:14.938 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 17 Jun 2025 22:22:15.029 * Background saving terminated with success
12936:M 17 Jun 2025 23:22:16.032 * 1 changes in 3600 seconds. Saving...
12936:M 17 Jun 2025 23:22:16.034 * Background saving started by pid 78675
78675:C 17 Jun 2025 23:22:16.048 * DB saved on disk
78675:C 17 Jun 2025 23:22:16.049 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 17 Jun 2025 23:22:16.137 * Background saving terminated with success
12936:M 18 Jun 2025 01:35:19.660 * 1 changes in 3600 seconds. Saving...
12936:M 18 Jun 2025 01:35:19.663 * Background saving started by pid 71835
71835:C 18 Jun 2025 01:35:19.670 * DB saved on disk
71835:C 18 Jun 2025 01:35:19.671 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 18 Jun 2025 01:35:19.765 * Background saving terminated with success
12936:M 18 Jun 2025 02:02:55.061 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 02:02:55.076 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 02:02:55.099 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 02:02:55.099 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 136558.
12936:M 18 Jun 2025 02:19:07.765 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 02:19:07.878 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 02:19:07.883 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 02:19:07.884 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 136628.
12936:M 18 Jun 2025 02:35:20.549 * 1 changes in 3600 seconds. Saving...
12936:M 18 Jun 2025 02:35:20.693 * Background saving started by pid 81258
12936:M 18 Jun 2025 02:35:20.694 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 02:35:20.694 * Connection with replica 127.0.0.1:7105 lost.
81258:C 18 Jun 2025 02:35:20.705 * DB saved on disk
81258:C 18 Jun 2025 02:35:20.707 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 18 Jun 2025 02:35:20.713 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 02:35:20.713 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 136698.
12936:M 18 Jun 2025 02:35:20.795 * Background saving terminated with success
12936:M 18 Jun 2025 02:51:34.280 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 02:51:34.280 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 02:51:34.297 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 02:51:34.297 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 136754.
12936:M 18 Jun 2025 03:07:46.984 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 03:07:47.117 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 03:07:47.120 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 03:07:47.120 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 136824.
12936:M 18 Jun 2025 03:23:51.864 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 03:23:51.865 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 03:23:51.870 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 03:23:51.870 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 136894.
12936:M 18 Jun 2025 03:40:05.541 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 03:40:05.541 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 03:40:05.548 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 03:40:05.548 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 136950.
12936:M 18 Jun 2025 03:46:44.630 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 03:46:44.630 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 03:46:44.634 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 03:46:44.635 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 137020.
12936:M 18 Jun 2025 04:02:57.381 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 04:02:57.381 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 04:02:57.385 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 04:02:57.386 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 137076.
12936:M 18 Jun 2025 04:18:51.396 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 04:18:51.397 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 04:18:51.400 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 04:18:51.400 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 137146.
12936:M 18 Jun 2025 04:35:02.299 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 04:35:02.300 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 04:35:02.302 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 04:35:02.302 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 137216.
12936:M 18 Jun 2025 04:51:15.920 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 04:51:15.920 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 04:51:15.923 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 04:51:15.923 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 137315.
12936:M 18 Jun 2025 05:07:28.550 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 05:07:28.550 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 05:07:28.554 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 05:07:28.555 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 137385.
12936:M 18 Jun 2025 05:23:41.260 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 05:23:41.260 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 05:23:41.266 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 05:23:41.267 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 137441.
12936:M 18 Jun 2025 05:39:53.906 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 05:39:53.906 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 05:39:53.909 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 05:39:53.909 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 137511.
12936:M 18 Jun 2025 05:56:05.983 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 05:56:05.985 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 05:56:05.985 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 137581.
12936:M 18 Jun 2025 05:59:13.261 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 05:59:13.263 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 05:59:13.263 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 137637.
12936:M 18 Jun 2025 06:15:26.126 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 06:15:26.129 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 06:15:26.129 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 137707.
12936:M 18 Jun 2025 06:31:38.911 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 06:31:38.913 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 06:31:38.913 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 137777.
12936:M 18 Jun 2025 06:47:47.979 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 06:47:47.981 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 06:47:47.981 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 137833.
12936:M 18 Jun 2025 07:04:01.594 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 07:04:01.598 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 07:04:01.600 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 137903.
12936:M 18 Jun 2025 07:20:14.427 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 07:20:14.430 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 07:20:14.430 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 137959.
12936:M 18 Jun 2025 07:36:27.297 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 07:36:27.298 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 07:36:27.298 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 138029.
12936:M 18 Jun 2025 07:41:50.444 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 07:41:50.445 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 07:41:50.445 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 138099.
12936:M 18 Jun 2025 07:55:59.309 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 07:55:59.437 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 07:55:59.440 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 07:55:59.440 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 138155.
12936:M 18 Jun 2025 08:10:08.605 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 08:10:08.727 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 08:10:08.729 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 08:10:08.729 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 138225.
12936:M 18 Jun 2025 08:28:34.282 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 08:28:34.403 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 08:28:34.406 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 08:28:34.406 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 138281.
12936:M 18 Jun 2025 08:30:10.784 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 08:30:10.785 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 08:30:10.789 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 08:30:10.789 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 138295.
12936:M 18 Jun 2025 08:33:15.959 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 08:33:15.960 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 08:33:15.968 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 08:33:15.968 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 138365.
12936:M 18 Jun 2025 08:42:31.304 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 08:42:31.305 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 08:42:31.311 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 08:42:31.311 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 138505.
12936:M 18 Jun 2025 09:00:21.869 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 09:00:21.873 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 09:00:21.873 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 138561.
12936:M 18 Jun 2025 09:18:01.759 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 09:18:01.761 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 09:18:01.761 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 138575.
12936:M 18 Jun 2025 09:33:53.641 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 09:33:53.642 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 09:33:53.646 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 09:33:53.646 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 138575.
12936:M 18 Jun 2025 09:38:48.330 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 09:38:48.331 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 09:38:48.335 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 09:38:48.335 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 138589.
12936:M 18 Jun 2025 09:41:37.314 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 09:41:37.329 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 09:41:37.347 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 09:41:37.347 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 138645.
12936:M 18 Jun 2025 11:33:07.658 * 1 changes in 3600 seconds. Saving...
12936:M 18 Jun 2025 11:33:07.660 * Background saving started by pid 79985
79985:C 18 Jun 2025 11:33:07.669 * DB saved on disk
79985:C 18 Jun 2025 11:33:07.669 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 18 Jun 2025 11:33:07.762 * Background saving terminated with success
12936:M 18 Jun 2025 12:36:00.593 * 1 changes in 3600 seconds. Saving...
12936:M 18 Jun 2025 12:36:00.594 * Background saving started by pid 17923
17923:C 18 Jun 2025 12:36:00.605 * DB saved on disk
17923:C 18 Jun 2025 12:36:00.605 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 18 Jun 2025 12:36:00.697 * Background saving terminated with success
12936:M 18 Jun 2025 12:36:01.408 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 12:36:01.408 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 12:36:01.413 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 12:36:01.413 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 153425.
12936:M 18 Jun 2025 12:42:56.581 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 12:42:56.581 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 12:42:56.589 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 12:42:56.589 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 153551.
12936:M 18 Jun 2025 12:50:25.147 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 12:50:25.148 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 12:50:25.153 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 12:50:25.153 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 153621.
12936:M 18 Jun 2025 12:53:12.242 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 12:53:12.242 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 12:53:12.244 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 12:53:12.244 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 153691.
12936:M 18 Jun 2025 13:02:21.086 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 13:02:21.087 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 13:02:21.093 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 13:02:21.094 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 153761.
12936:M 18 Jun 2025 13:20:21.660 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 13:20:21.660 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 13:20:21.663 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 13:20:21.664 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 153901.
12936:M 18 Jun 2025 13:37:52.000 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 13:37:52.001 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 13:37:52.004 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 13:37:52.004 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 153971.
12936:M 18 Jun 2025 13:54:24.723 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 13:54:24.723 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 13:54:24.729 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 13:54:24.730 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 154055.
12936:M 18 Jun 2025 14:05:05.624 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 14:05:05.624 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 14:05:05.628 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 14:05:05.628 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 154559.
12936:M 18 Jun 2025 15:18:23.022 * 1 changes in 3600 seconds. Saving...
12936:M 18 Jun 2025 15:18:23.026 * Background saving started by pid 77405
77405:C 18 Jun 2025 15:18:23.052 * DB saved on disk
77405:C 18 Jun 2025 15:18:23.053 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 18 Jun 2025 15:18:23.139 * Background saving terminated with success
12936:M 18 Jun 2025 16:18:24.096 * 1 changes in 3600 seconds. Saving...
12936:M 18 Jun 2025 16:18:24.098 * Background saving started by pid 18265
18265:C 18 Jun 2025 16:18:24.110 * DB saved on disk
18265:C 18 Jun 2025 16:18:24.110 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 18 Jun 2025 16:18:24.200 * Background saving terminated with success
12936:M 18 Jun 2025 17:18:25.030 * 1 changes in 3600 seconds. Saving...
12936:M 18 Jun 2025 17:18:25.032 * Background saving started by pid 59404
59404:C 18 Jun 2025 17:18:25.048 * DB saved on disk
59404:C 18 Jun 2025 17:18:25.052 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 18 Jun 2025 17:18:25.135 * Background saving terminated with success
12936:M 18 Jun 2025 19:33:26.043 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 19:33:26.049 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 19:33:26.050 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 183482.
12936:M 18 Jun 2025 19:53:59.005 * 1 changes in 3600 seconds. Saving...
12936:M 18 Jun 2025 19:53:59.006 * Background saving started by pid 55390
55390:C 18 Jun 2025 19:53:59.014 * DB saved on disk
55390:C 18 Jun 2025 19:53:59.015 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 18 Jun 2025 19:53:59.107 * Background saving terminated with success
12936:M 18 Jun 2025 20:54:00.080 * 1 changes in 3600 seconds. Saving...
12936:M 18 Jun 2025 20:54:00.083 * Background saving started by pid 83982
83982:C 18 Jun 2025 20:54:00.090 * DB saved on disk
83982:C 18 Jun 2025 20:54:00.091 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 18 Jun 2025 20:54:00.184 * Background saving terminated with success
12936:M 18 Jun 2025 22:16:16.464 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 22:16:16.464 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 22:16:16.467 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 22:16:16.472 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 196555.
12936:M 18 Jun 2025 22:25:15.440 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 22:25:15.476 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 22:25:15.494 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 22:25:15.494 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 196569.
12936:M 18 Jun 2025 22:41:24.455 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 22:41:24.481 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 22:41:24.486 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 22:41:24.487 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 196625.
12936:M 18 Jun 2025 22:58:02.446 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 22:58:02.479 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 22:58:02.517 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 22:58:02.517 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 196682.
12936:M 18 Jun 2025 23:16:22.448 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 23:16:22.485 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 23:16:22.500 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 23:16:22.500 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 196766.
12936:M 18 Jun 2025 23:24:35.346 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 23:24:35.379 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 23:24:35.405 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 23:24:35.405 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 196780.
12936:M 18 Jun 2025 23:39:15.304 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 18 Jun 2025 23:39:15.305 * Connection with replica 127.0.0.1:7105 lost.
12936:M 18 Jun 2025 23:39:15.313 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 18 Jun 2025 23:39:15.313 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 196836.
12936:M 19 Jun 2025 00:05:56.434 * 1 changes in 3600 seconds. Saving...
12936:M 19 Jun 2025 00:05:56.438 * Background saving started by pid 22257
22257:C 19 Jun 2025 00:05:56.454 * DB saved on disk
22257:C 19 Jun 2025 00:05:56.455 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 19 Jun 2025 00:05:56.542 * Background saving terminated with success
12936:M 19 Jun 2025 01:05:57.038 * 1 changes in 3600 seconds. Saving...
12936:M 19 Jun 2025 01:05:57.040 * Background saving started by pid 44879
44879:C 19 Jun 2025 01:05:57.047 * DB saved on disk
44879:C 19 Jun 2025 01:05:57.052 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 19 Jun 2025 01:05:57.141 * Background saving terminated with success
12936:M 19 Jun 2025 02:05:58.019 * 1 changes in 3600 seconds. Saving...
12936:M 19 Jun 2025 02:05:58.021 * Background saving started by pid 67886
67886:C 19 Jun 2025 02:05:58.039 * DB saved on disk
67886:C 19 Jun 2025 02:05:58.039 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 19 Jun 2025 02:05:58.122 * Background saving terminated with success
12936:M 19 Jun 2025 02:44:16.166 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 02:44:16.166 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 02:44:16.169 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 02:44:16.169 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 212830.
12936:M 19 Jun 2025 03:00:29.963 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 03:00:29.963 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 03:00:29.968 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 03:00:29.969 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 212900.
12936:M 19 Jun 2025 03:16:41.550 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 03:16:41.552 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 03:16:41.552 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 212956.
12936:M 19 Jun 2025 03:32:50.352 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 03:32:50.353 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 03:32:50.358 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 03:32:50.359 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 213012.
12936:M 19 Jun 2025 03:49:03.151 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 03:49:03.151 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 03:49:03.155 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 03:49:03.156 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 213082.
12936:M 19 Jun 2025 04:05:15.973 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 04:05:16.110 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 04:05:16.114 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 04:05:16.114 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 213152.
12936:M 19 Jun 2025 04:29:44.655 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 04:29:44.655 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 04:29:44.657 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 04:29:44.658 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 213391.
12936:M 19 Jun 2025 04:45:44.061 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 04:45:44.062 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 04:45:44.068 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 04:45:44.069 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 213447.
12936:M 19 Jun 2025 05:01:42.350 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 05:01:42.486 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 05:01:42.493 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 05:01:42.493 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 213503.
12936:M 19 Jun 2025 05:17:53.389 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 05:17:53.507 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 05:17:53.511 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 05:17:53.512 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 213573.
12936:M 19 Jun 2025 05:30:44.403 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 05:30:44.518 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 05:30:44.520 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 05:30:44.520 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 213629.
12936:M 19 Jun 2025 05:46:59.307 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 05:46:59.307 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 05:46:59.312 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 05:46:59.312 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 213699.
12936:M 19 Jun 2025 06:03:11.980 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 06:03:11.980 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 06:03:11.983 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 06:03:11.983 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 213769.
12936:M 19 Jun 2025 06:19:25.730 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 06:19:25.731 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 06:19:25.739 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 06:19:25.740 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 213825.
12936:M 19 Jun 2025 06:31:44.588 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 06:31:44.588 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 06:31:44.591 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 06:31:44.591 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 213895.
12936:M 19 Jun 2025 06:38:15.519 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 06:38:15.522 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 06:38:15.522 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 213965.
12936:M 19 Jun 2025 06:54:29.086 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 06:54:29.086 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 06:54:29.089 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 06:54:29.089 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 214021.
12936:M 19 Jun 2025 07:10:35.398 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 07:10:35.521 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 07:10:35.525 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 07:10:35.525 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 214091.
12936:M 19 Jun 2025 07:26:44.164 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 07:26:44.165 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 07:26:44.191 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 07:26:44.191 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 214147.
12936:M 19 Jun 2025 07:32:44.703 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 07:32:44.703 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 07:32:44.706 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 07:32:44.706 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 214217.
12936:M 19 Jun 2025 07:48:37.408 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 07:48:37.525 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 07:48:37.529 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 07:48:37.530 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 214273.
12936:M 19 Jun 2025 08:04:46.174 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 08:04:46.174 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 08:04:46.179 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 08:04:46.179 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 214329.
12936:M 19 Jun 2025 08:20:59.798 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 08:20:59.817 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 08:20:59.829 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 08:20:59.829 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 214399.
12936:M 19 Jun 2025 08:33:45.096 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 08:33:45.097 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 08:33:45.101 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 08:33:45.101 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 214469.
12936:M 19 Jun 2025 08:42:46.148 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 08:42:46.149 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 08:42:46.152 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 08:42:46.152 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 214525.
12936:M 19 Jun 2025 08:50:23.990 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 08:50:23.991 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 08:50:23.999 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 08:50:23.999 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 214595.
12936:M 19 Jun 2025 09:08:41.983 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 09:08:41.983 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 09:08:41.991 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 09:08:41.991 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 214651.
12936:M 19 Jun 2025 09:24:10.875 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 09:24:10.875 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 09:24:10.878 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 09:24:10.878 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 214665.
12936:M 19 Jun 2025 09:34:45.112 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 09:34:45.112 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 09:34:45.118 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 09:34:45.119 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 214749.
12936:M 19 Jun 2025 09:42:03.277 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 09:42:03.277 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 09:42:03.280 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 09:42:03.281 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 214819.
12936:M 19 Jun 2025 09:59:48.872 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 09:59:48.873 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 09:59:48.876 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 09:59:48.876 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 214931.
12936:M 19 Jun 2025 10:02:20.084 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 10:02:20.084 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 10:02:20.088 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 10:02:20.088 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 215001.
12936:M 19 Jun 2025 10:05:15.614 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 10:05:15.614 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 10:05:15.617 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 10:05:15.617 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 215057.
12936:M 19 Jun 2025 10:09:30.012 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 10:09:30.013 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 10:09:30.016 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 10:09:30.016 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 215127.
12936:M 19 Jun 2025 10:12:52.509 * 1 changes in 3600 seconds. Saving...
12936:M 19 Jun 2025 10:12:52.512 * Background saving started by pid 89495
89495:C 19 Jun 2025 10:12:52.519 * DB saved on disk
89495:C 19 Jun 2025 10:12:52.520 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 19 Jun 2025 10:12:52.613 * Background saving terminated with success
12936:M 19 Jun 2025 11:02:12.933 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 11:02:12.933 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 11:02:12.937 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 11:02:12.937 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 219785.
12936:M 19 Jun 2025 11:05:47.268 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 11:05:47.269 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 11:05:47.274 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 11:05:47.274 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 219841.
12936:M 19 Jun 2025 11:12:53.023 * 1 changes in 3600 seconds. Saving...
12936:M 19 Jun 2025 11:12:53.025 * Background saving started by pid 8369
8369:C 19 Jun 2025 11:12:53.040 * DB saved on disk
8369:C 19 Jun 2025 11:12:53.043 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 19 Jun 2025 11:12:53.127 * Background saving terminated with success
12936:M 19 Jun 2025 12:49:24.586 * 1 changes in 3600 seconds. Saving...
12936:M 19 Jun 2025 12:49:24.587 * Background saving started by pid 49292
49292:C 19 Jun 2025 12:49:24.594 * DB saved on disk
49292:C 19 Jun 2025 12:49:24.596 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 19 Jun 2025 12:49:24.689 * Background saving terminated with success
12936:M 19 Jun 2025 13:19:27.339 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 13:19:27.340 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 13:19:27.341 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 230197.
12936:M 19 Jun 2025 13:26:20.954 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 13:26:20.954 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 13:26:20.957 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 13:26:20.957 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 230463.
12936:M 19 Jun 2025 13:41:43.436 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 13:41:43.438 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 13:41:43.438 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 231527.
12936:M 19 Jun 2025 14:02:08.297 * 1 changes in 3600 seconds. Saving...
12936:M 19 Jun 2025 14:02:08.414 * Background saving started by pid 60495
60495:C 19 Jun 2025 14:02:08.423 * DB saved on disk
60495:C 19 Jun 2025 14:02:08.428 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 19 Jun 2025 14:02:08.518 * Background saving terminated with success
12936:M 19 Jun 2025 14:02:08.720 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 14:02:08.721 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 14:02:08.724 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 14:02:08.724 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 231891.
12936:M 19 Jun 2025 14:05:51.334 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 14:05:51.334 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 14:05:51.337 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 14:05:51.338 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 231947.
12936:M 19 Jun 2025 16:47:32.662 * 1 changes in 3600 seconds. Saving...
12936:M 19 Jun 2025 16:47:32.663 * Background saving started by pid 25965
25965:C 19 Jun 2025 16:47:32.674 * DB saved on disk
25965:C 19 Jun 2025 16:47:32.675 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 19 Jun 2025 16:47:32.766 * Background saving terminated with success
12936:M 19 Jun 2025 17:47:33.005 * 1 changes in 3600 seconds. Saving...
12936:M 19 Jun 2025 17:47:33.008 * Background saving started by pid 55065
55065:C 19 Jun 2025 17:47:33.026 * DB saved on disk
55065:C 19 Jun 2025 17:47:33.028 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 19 Jun 2025 17:47:33.111 * Background saving terminated with success
12936:M 19 Jun 2025 18:03:03.845 * 100 changes in 300 seconds. Saving...
12936:M 19 Jun 2025 18:03:03.848 * Background saving started by pid 61983
61983:C 19 Jun 2025 18:03:03.862 * DB saved on disk
61983:C 19 Jun 2025 18:03:03.863 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 19 Jun 2025 18:03:03.951 * Background saving terminated with success
12936:M 19 Jun 2025 18:08:04.079 * 100 changes in 300 seconds. Saving...
12936:M 19 Jun 2025 18:08:04.082 * Background saving started by pid 64286
64286:C 19 Jun 2025 18:08:04.093 * DB saved on disk
64286:C 19 Jun 2025 18:08:04.094 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 19 Jun 2025 18:08:04.184 * Background saving terminated with success
12936:M 19 Jun 2025 19:08:05.064 * 1 changes in 3600 seconds. Saving...
12936:M 19 Jun 2025 19:08:05.067 * Background saving started by pid 88294
88294:C 19 Jun 2025 19:08:05.076 * DB saved on disk
88294:C 19 Jun 2025 19:08:05.077 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 19 Jun 2025 19:08:05.170 * Background saving terminated with success
12936:M 19 Jun 2025 20:08:06.030 * 1 changes in 3600 seconds. Saving...
12936:M 19 Jun 2025 20:08:06.031 * Background saving started by pid 11924
11924:C 19 Jun 2025 20:08:06.040 * DB saved on disk
11924:C 19 Jun 2025 20:08:06.041 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 19 Jun 2025 20:08:06.132 * Background saving terminated with success
12936:M 19 Jun 2025 21:45:56.475 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 21:45:56.478 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 21:45:56.478 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 279918.
12936:M 19 Jun 2025 22:02:10.456 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 22:02:10.498 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 22:02:10.516 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 22:02:10.516 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 279918.
12936:M 19 Jun 2025 22:18:27.481 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 22:18:27.509 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 22:18:27.529 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 22:18:27.529 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 279932.
12936:M 19 Jun 2025 22:35:28.480 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 22:35:28.504 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 22:35:28.523 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 22:35:28.523 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 279975.
12936:M 19 Jun 2025 22:46:45.085 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 22:46:45.209 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 22:46:45.211 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 22:46:45.211 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 279989.
12936:M 19 Jun 2025 23:02:57.047 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 23:02:57.164 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 23:02:57.167 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 23:02:57.167 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 280059.
12936:M 19 Jun 2025 23:06:07.853 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 19 Jun 2025 23:06:07.853 * Connection with replica 127.0.0.1:7105 lost.
12936:M 19 Jun 2025 23:06:07.857 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 19 Jun 2025 23:06:07.857 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 280185.
12936:M 19 Jun 2025 23:31:59.722 * 1 changes in 3600 seconds. Saving...
12936:M 19 Jun 2025 23:31:59.723 * Background saving started by pid 53969
53969:C 19 Jun 2025 23:31:59.730 * DB saved on disk
53969:C 19 Jun 2025 23:31:59.730 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 19 Jun 2025 23:31:59.825 * Background saving terminated with success
12936:M 20 Jun 2025 00:32:00.046 * 1 changes in 3600 seconds. Saving...
12936:M 20 Jun 2025 00:32:00.049 * Background saving started by pid 76613
76613:C 20 Jun 2025 00:32:00.060 * DB saved on disk
76613:C 20 Jun 2025 00:32:00.061 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 20 Jun 2025 00:32:00.152 * Background saving terminated with success
12936:M 20 Jun 2025 02:02:52.309 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 02:02:52.309 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 02:02:52.315 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 02:02:52.316 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 294321.
12936:M 20 Jun 2025 02:13:57.339 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 02:13:57.339 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 02:13:57.342 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 02:13:57.342 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 294321.
12936:M 20 Jun 2025 02:31:49.075 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 02:31:49.075 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 02:31:49.079 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 02:31:49.079 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 294391.
12936:M 20 Jun 2025 02:47:38.449 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 02:47:38.450 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 02:47:38.460 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 02:47:38.460 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 294462.
12936:M 20 Jun 2025 03:03:51.186 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 03:03:51.187 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 03:03:51.189 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 03:03:51.190 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 294532.
12936:M 20 Jun 2025 03:20:03.033 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 03:20:03.061 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 03:20:03.064 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 03:20:03.064 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 294588.
12936:M 20 Jun 2025 03:36:16.828 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 03:36:16.829 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 03:36:16.834 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 03:36:16.835 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 294658.
12936:M 20 Jun 2025 03:46:01.117 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 03:46:01.117 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 03:46:01.125 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 03:46:01.126 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 294728.
12936:M 20 Jun 2025 04:02:13.773 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 04:02:13.773 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 04:02:13.777 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 04:02:13.778 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 294784.
12936:M 20 Jun 2025 04:18:25.743 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 04:18:25.744 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 04:18:25.748 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 04:18:25.748 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 294854.
12936:M 20 Jun 2025 04:34:39.514 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 04:34:39.514 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 04:34:39.523 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 04:34:39.524 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 294924.
12936:M 20 Jun 2025 04:50:52.307 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 04:50:52.307 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 04:50:52.310 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 04:50:52.310 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 294980.
12936:M 20 Jun 2025 05:06:42.481 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 05:06:42.481 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 05:06:42.483 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 05:06:42.483 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 295050.
12936:M 20 Jun 2025 05:22:51.386 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 05:22:51.504 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 05:22:51.507 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 05:22:51.507 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 295106.
12936:M 20 Jun 2025 05:39:05.347 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 05:39:05.347 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 05:39:05.350 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 05:39:05.350 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 295176.
12936:M 20 Jun 2025 05:55:17.297 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 05:55:17.333 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 05:55:17.346 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 05:55:17.346 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 295246.
12936:M 20 Jun 2025 06:11:31.113 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 06:11:31.115 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 06:11:31.115 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 295302.
12936:M 20 Jun 2025 06:27:43.025 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 06:27:43.027 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 06:27:43.028 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 295372.
12936:M 20 Jun 2025 06:43:56.748 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 06:43:56.748 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 06:43:56.757 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 06:43:56.758 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 295442.
12936:M 20 Jun 2025 07:00:09.359 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 07:00:09.359 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 07:00:09.365 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 07:00:09.365 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 295498.
12936:M 20 Jun 2025 07:16:23.111 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 07:16:23.112 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 07:16:23.122 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 07:16:23.122 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 295568.
12936:M 20 Jun 2025 07:33:30.667 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 07:33:30.669 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 07:33:30.669 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 295694.
12936:M 20 Jun 2025 07:40:52.561 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 07:40:52.562 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 07:40:52.565 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 07:40:52.566 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 295764.
12936:M 20 Jun 2025 07:57:06.369 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 07:57:06.370 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 07:57:06.372 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 07:57:06.372 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 295820.
12936:M 20 Jun 2025 08:13:16.643 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 08:13:16.644 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 08:13:16.646 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 08:13:16.646 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 295890.
12936:M 20 Jun 2025 08:29:26.329 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 08:29:26.329 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 08:29:26.333 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 08:29:26.333 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 295946.
12936:M 20 Jun 2025 08:46:31.513 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 08:46:31.514 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 08:46:31.519 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 08:46:31.519 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 296086.
12936:M 20 Jun 2025 09:02:22.783 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 09:02:22.786 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 09:02:22.795 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 09:02:22.795 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 296142.
12936:M 20 Jun 2025 09:04:10.587 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 09:04:10.608 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 09:04:10.611 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 09:04:10.611 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 296198.
12936:M 20 Jun 2025 09:21:58.358 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 09:21:58.384 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 09:21:58.409 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 09:21:58.409 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 296268.
12936:M 20 Jun 2025 09:39:40.338 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 09:39:40.362 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 09:39:40.402 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 09:39:40.402 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 296352.
12936:M 20 Jun 2025 09:41:52.341 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 09:41:52.357 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 09:41:52.365 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 09:41:52.366 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 296366.
12936:M 20 Jun 2025 09:52:01.359 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 09:52:01.359 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 09:52:01.363 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 09:52:01.363 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 296422.
12936:M 20 Jun 2025 13:09:00.467 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 13:09:00.484 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 13:09:00.484 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 311472.
12936:M 20 Jun 2025 13:44:50.399 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 13:44:50.423 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 13:44:50.423 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 313194.
12936:M 20 Jun 2025 14:07:23.224 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 14:07:23.224 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 14:07:23.243 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 14:07:23.243 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 313978.
12936:M 20 Jun 2025 17:45:00.672 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 17:45:00.673 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 17:45:00.677 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 17:45:00.677 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 331366.
12936:M 20 Jun 2025 17:54:03.842 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 17:54:03.844 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 17:54:03.844 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 331982.
12936:M 20 Jun 2025 18:29:47.630 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 18:29:47.632 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 18:29:47.632 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 333956.
12936:M 20 Jun 2025 18:52:57.695 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 18:52:57.695 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 18:52:57.699 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 18:52:57.699 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 334614.
12936:M 20 Jun 2025 19:48:27.724 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 19:48:27.724 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 19:48:27.729 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 19:48:27.729 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 337835.
12936:M 20 Jun 2025 20:02:19.947 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 20:02:19.947 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 20:02:19.949 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 20:02:19.949 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 337835.
12936:M 20 Jun 2025 20:13:27.148 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 20:13:27.149 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 20:13:27.153 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 20:13:27.154 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 338479.
12936:M 20 Jun 2025 20:15:34.947 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 20:15:34.947 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 20:15:34.951 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 20:15:34.951 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 338549.
12936:M 20 Jun 2025 20:33:47.045 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 20:33:47.046 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 20:33:47.055 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 20:33:47.055 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 338605.
12936:M 20 Jun 2025 20:50:05.943 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 20:50:05.944 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 20:50:05.948 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 20:50:05.949 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 338619.
12936:M 20 Jun 2025 21:05:35.832 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 21:05:35.832 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 21:05:35.835 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 21:05:35.835 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 338619.
12936:M 20 Jun 2025 21:16:34.729 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 21:16:34.730 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 21:16:34.733 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 21:16:34.733 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 338633.
12936:M 20 Jun 2025 21:35:18.766 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 21:35:18.767 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 21:35:18.773 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 21:35:18.773 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 338703.
12936:M 20 Jun 2025 21:48:41.300 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 21:48:41.300 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 21:48:41.303 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 21:48:41.303 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 338703.
12936:M 20 Jun 2025 22:05:42.544 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 22:05:42.544 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 22:05:42.545 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 22:05:42.546 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 338829.
12936:M 20 Jun 2025 22:17:34.932 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 22:17:34.933 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 22:17:34.936 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 22:17:34.936 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 338969.
12936:M 20 Jun 2025 22:22:19.591 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 20 Jun 2025 22:22:19.591 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 22:22:19.593 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 22:22:19.594 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 339025.
12936:M 20 Jun 2025 22:40:15.966 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 22:40:15.973 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 22:40:15.973 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 339221.
12936:M 20 Jun 2025 22:53:50.880 * Connection with replica 127.0.0.1:7105 lost.
12936:M 20 Jun 2025 22:53:50.882 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 20 Jun 2025 22:53:50.882 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 339347.
12936:M 20 Jun 2025 23:04:34.586 * 1 changes in 3600 seconds. Saving...
12936:M 20 Jun 2025 23:04:34.587 * Background saving started by pid 14373
14373:C 20 Jun 2025 23:04:34.598 * DB saved on disk
14373:C 20 Jun 2025 23:04:34.598 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 20 Jun 2025 23:04:34.688 * Background saving terminated with success
12936:M 21 Jun 2025 00:04:35.041 * 1 changes in 3600 seconds. Saving...
12936:M 21 Jun 2025 00:04:35.042 * Background saving started by pid 37623
37623:C 21 Jun 2025 00:04:35.055 * DB saved on disk
37623:C 21 Jun 2025 00:04:35.055 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 21 Jun 2025 00:04:35.145 * Background saving terminated with success
12936:M 21 Jun 2025 01:25:29.094 * Connection with replica 127.0.0.1:7105 lost.
12936:M 21 Jun 2025 01:25:29.096 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 21 Jun 2025 01:25:29.096 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 351565.
12936:M 21 Jun 2025 09:02:20.895 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 21 Jun 2025 09:02:20.895 * Connection with replica 127.0.0.1:7105 lost.
12936:M 21 Jun 2025 09:02:20.898 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 21 Jun 2025 09:02:20.898 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 388974.
12936:M 21 Jun 2025 09:21:45.654 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 21 Jun 2025 09:21:45.654 * Connection with replica 127.0.0.1:7105 lost.
12936:M 21 Jun 2025 09:21:45.659 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 21 Jun 2025 09:21:45.659 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 389282.
12936:M 21 Jun 2025 09:39:33.481 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 21 Jun 2025 09:39:33.481 * Connection with replica 127.0.0.1:7105 lost.
12936:M 21 Jun 2025 09:39:33.485 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 21 Jun 2025 09:39:33.485 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 389478.
12936:M 21 Jun 2025 09:55:39.539 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 21 Jun 2025 09:55:39.539 * Connection with replica 127.0.0.1:7105 lost.
12936:M 21 Jun 2025 09:55:39.543 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 21 Jun 2025 09:55:39.543 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 389548.
12936:M 21 Jun 2025 10:19:31.484 * Connection with replica 127.0.0.1:7105 lost.
12936:M 21 Jun 2025 10:19:31.486 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 21 Jun 2025 10:19:31.486 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 390248.
12936:M 21 Jun 2025 10:22:14.407 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 21 Jun 2025 10:22:14.407 * Connection with replica 127.0.0.1:7105 lost.
12936:M 21 Jun 2025 10:22:14.410 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 21 Jun 2025 10:22:14.410 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 390304.
12936:M 21 Jun 2025 10:24:42.059 * Connection with replica 127.0.0.1:7105 lost.
12936:M 21 Jun 2025 10:24:42.062 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 21 Jun 2025 10:24:42.062 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 390416.
12936:M 21 Jun 2025 11:14:39.759 * 1 changes in 3600 seconds. Saving...
12936:M 21 Jun 2025 11:14:39.761 * Background saving started by pid 51840
51840:C 21 Jun 2025 11:14:39.776 * DB saved on disk
51840:C 21 Jun 2025 11:14:39.777 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 21 Jun 2025 11:14:39.862 * Background saving terminated with success
12936:M 21 Jun 2025 12:14:40.049 * 1 changes in 3600 seconds. Saving...
12936:M 21 Jun 2025 12:14:40.050 * Background saving started by pid 81700
81700:C 21 Jun 2025 12:14:40.062 * DB saved on disk
81700:C 21 Jun 2025 12:14:40.062 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 21 Jun 2025 12:14:40.151 * Background saving terminated with success
12936:M 21 Jun 2025 14:16:51.504 * 1 changes in 3600 seconds. Saving...
12936:M 21 Jun 2025 14:16:51.505 * Background saving started by pid 28845
28845:C 21 Jun 2025 14:16:51.511 * DB saved on disk
28845:C 21 Jun 2025 14:16:51.512 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 21 Jun 2025 14:16:51.607 * Background saving terminated with success
12936:M 21 Jun 2025 15:16:52.070 * 1 changes in 3600 seconds. Saving...
12936:M 21 Jun 2025 15:16:52.072 * Background saving started by pid 51676
51676:C 21 Jun 2025 15:16:52.080 * DB saved on disk
51676:C 21 Jun 2025 15:16:52.080 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 21 Jun 2025 15:16:52.174 * Background saving terminated with success
12936:M 21 Jun 2025 17:23:17.884 * 1 changes in 3600 seconds. Saving...
12936:M 21 Jun 2025 17:23:17.885 * Background saving started by pid 3431
3431:C 21 Jun 2025 17:23:17.893 * DB saved on disk
3431:C 21 Jun 2025 17:23:17.895 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 21 Jun 2025 17:23:17.987 * Background saving terminated with success
12936:M 21 Jun 2025 18:23:18.059 * 1 changes in 3600 seconds. Saving...
12936:M 21 Jun 2025 18:23:18.062 * Background saving started by pid 27397
27397:C 21 Jun 2025 18:23:18.077 * DB saved on disk
27397:C 21 Jun 2025 18:23:18.080 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 21 Jun 2025 18:23:18.165 * Background saving terminated with success
12936:M 22 Jun 2025 11:24:38.311 * 1 changes in 3600 seconds. Saving...
12936:M 22 Jun 2025 11:24:38.312 * Background saving started by pid 5791
5791:C 22 Jun 2025 11:24:38.331 * DB saved on disk
5791:C 22 Jun 2025 11:24:38.331 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 22 Jun 2025 11:24:38.413 * Background saving terminated with success
12936:M 22 Jun 2025 12:24:39.059 * 1 changes in 3600 seconds. Saving...
12936:M 22 Jun 2025 12:24:39.061 * Background saving started by pid 29003
29003:C 22 Jun 2025 12:24:39.076 * DB saved on disk
29003:C 22 Jun 2025 12:24:39.078 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 22 Jun 2025 12:24:39.163 * Background saving terminated with success
12936:M 22 Jun 2025 16:39:51.362 * 1 changes in 3600 seconds. Saving...
12936:M 22 Jun 2025 16:39:51.364 * Background saving started by pid 22792
22792:C 22 Jun 2025 16:39:51.371 * DB saved on disk
22792:C 22 Jun 2025 16:39:51.371 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 22 Jun 2025 16:39:51.465 * Background saving terminated with success
12936:M 22 Jun 2025 17:39:52.053 * 1 changes in 3600 seconds. Saving...
12936:M 22 Jun 2025 17:39:52.055 * Background saving started by pid 45506
45506:C 22 Jun 2025 17:39:52.065 * DB saved on disk
45506:C 22 Jun 2025 17:39:52.065 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 22 Jun 2025 17:39:52.159 * Background saving terminated with success
12936:M 22 Jun 2025 19:46:47.169 * 1 changes in 3600 seconds. Saving...
12936:M 22 Jun 2025 19:46:47.170 * Background saving started by pid 91433
91433:C 22 Jun 2025 19:46:47.176 * DB saved on disk
91433:C 22 Jun 2025 19:46:47.176 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 22 Jun 2025 19:46:47.271 * Background saving terminated with success
12936:M 22 Jun 2025 20:46:48.023 * 1 changes in 3600 seconds. Saving...
12936:M 22 Jun 2025 20:46:48.026 * Background saving started by pid 15212
15212:C 22 Jun 2025 20:46:48.034 * DB saved on disk
15212:C 22 Jun 2025 20:46:48.035 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 22 Jun 2025 20:46:48.128 * Background saving terminated with success
12936:M 22 Jun 2025 23:43:26.678 * 1 changes in 3600 seconds. Saving...
12936:M 22 Jun 2025 23:43:26.688 * Background saving started by pid 84719
84719:C 22 Jun 2025 23:43:26.695 * DB saved on disk
84719:C 22 Jun 2025 23:43:26.696 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 22 Jun 2025 23:43:26.791 * Background saving terminated with success
12936:M 23 Jun 2025 00:43:27.052 * 1 changes in 3600 seconds. Saving...
12936:M 23 Jun 2025 00:43:27.053 * Background saving started by pid 10656
10656:C 23 Jun 2025 00:43:27.061 * DB saved on disk
10656:C 23 Jun 2025 00:43:27.061 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 23 Jun 2025 00:43:27.155 * Background saving terminated with success
12936:M 23 Jun 2025 08:46:05.475 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 23 Jun 2025 08:46:05.475 * Connection with replica 127.0.0.1:7105 lost.
12936:M 23 Jun 2025 08:46:05.477 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 23 Jun 2025 08:46:05.478 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 14 bytes of backlog starting from offset 632883.
12936:M 23 Jun 2025 09:03:37.877 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 23 Jun 2025 09:03:37.877 * Connection with replica 127.0.0.1:7105 lost.
12936:M 23 Jun 2025 09:03:37.881 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 23 Jun 2025 09:03:37.881 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 632953.
12936:M 23 Jun 2025 09:14:33.783 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 23 Jun 2025 09:14:33.820 * Connection with replica 127.0.0.1:7105 lost.
12936:M 23 Jun 2025 09:14:33.841 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 23 Jun 2025 09:14:33.841 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 632967.
12936:M 23 Jun 2025 09:32:26.797 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 23 Jun 2025 09:32:26.817 * Connection with replica 127.0.0.1:7105 lost.
12936:M 23 Jun 2025 09:32:26.829 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 23 Jun 2025 09:32:26.829 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 633023.
12936:M 23 Jun 2025 09:49:52.809 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 23 Jun 2025 09:49:52.837 * Connection with replica 127.0.0.1:7105 lost.
12936:M 23 Jun 2025 09:49:52.840 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 23 Jun 2025 09:49:52.840 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 633037.
12936:M 23 Jun 2025 09:50:53.652 * Connection with replica 127.0.0.1:7105 lost.
12936:M 23 Jun 2025 09:50:53.654 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 23 Jun 2025 09:50:53.654 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 633037.
12936:M 23 Jun 2025 10:05:30.821 * Connection with replica 127.0.0.1:7105 lost.
12936:M 23 Jun 2025 10:05:30.823 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 23 Jun 2025 10:05:30.823 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 634073.
12936:M 23 Jun 2025 10:30:21.365 * Connection with replica 127.0.0.1:7105 lost.
12936:M 23 Jun 2025 10:30:21.367 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 23 Jun 2025 10:30:21.367 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 635151.
12936:M 23 Jun 2025 10:35:53.875 * 1 changes in 3600 seconds. Saving...
12936:M 23 Jun 2025 10:35:53.876 * Background saving started by pid 3239
3239:C 23 Jun 2025 10:35:53.888 * DB saved on disk
3239:C 23 Jun 2025 10:35:53.888 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 23 Jun 2025 10:35:53.978 * Background saving terminated with success
12936:M 23 Jun 2025 11:35:54.067 * 1 changes in 3600 seconds. Saving...
12936:M 23 Jun 2025 11:35:54.068 * Background saving started by pid 28273
28273:C 23 Jun 2025 11:35:54.074 * DB saved on disk
28273:C 23 Jun 2025 11:35:54.074 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 23 Jun 2025 11:35:54.170 * Background saving terminated with success
12936:M 23 Jun 2025 12:17:01.846 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 23 Jun 2025 12:17:01.847 * Connection with replica 127.0.0.1:7105 lost.
12936:M 23 Jun 2025 12:17:01.850 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 23 Jun 2025 12:17:01.850 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 647082.
12936:M 23 Jun 2025 12:23:31.762 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 23 Jun 2025 12:23:31.873 * Connection with replica 127.0.0.1:7105 lost.
12936:M 23 Jun 2025 12:23:31.876 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 23 Jun 2025 12:23:31.876 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 647194.
12936:M 23 Jun 2025 12:35:55.016 * 1 changes in 3600 seconds. Saving...
12936:M 23 Jun 2025 12:35:55.019 * Background saving started by pid 44452
44452:C 23 Jun 2025 12:35:55.032 * DB saved on disk
44452:C 23 Jun 2025 12:35:55.033 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 23 Jun 2025 12:35:55.120 * Background saving terminated with success
12936:M 23 Jun 2025 12:51:04.819 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 23 Jun 2025 12:51:04.819 * Connection with replica 127.0.0.1:7105 lost.
12936:M 23 Jun 2025 12:51:04.834 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 23 Jun 2025 12:51:04.834 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 648468.
12936:M 23 Jun 2025 13:24:05.859 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7105
12936:M 23 Jun 2025 13:24:05.859 * Connection with replica 127.0.0.1:7105 lost.
12936:M 23 Jun 2025 13:24:05.864 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 23 Jun 2025 13:24:05.864 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 649854.
12936:M 23 Jun 2025 13:51:45.850 * Connection with replica 127.0.0.1:7105 lost.
12936:M 23 Jun 2025 13:51:45.851 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 23 Jun 2025 13:51:45.851 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 650848.
12936:M 23 Jun 2025 15:21:23.870 * Connection with replica 127.0.0.1:7105 lost.
12936:M 23 Jun 2025 15:21:23.874 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 23 Jun 2025 15:21:23.874 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 657372.
12936:M 23 Jun 2025 15:26:31.313 * Connection with replica 127.0.0.1:7105 lost.
12936:M 23 Jun 2025 15:26:31.315 * Replica 127.0.0.1:7105 asks for synchronization
12936:M 23 Jun 2025 15:26:31.316 * Partial resynchronization request from 127.0.0.1:7105 accepted. Sending 0 bytes of backlog starting from offset 657442.
12936:M 23 Jun 2025 15:31:03.930 * 1 changes in 3600 seconds. Saving...
12936:M 23 Jun 2025 15:31:03.931 * Background saving started by pid 88100
88100:C 23 Jun 2025 15:31:03.938 * DB saved on disk
88100:C 23 Jun 2025 15:31:03.938 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 23 Jun 2025 15:31:04.033 * Background saving terminated with success
12936:M 23 Jun 2025 16:31:05.092 * 1 changes in 3600 seconds. Saving...
12936:M 23 Jun 2025 16:31:05.093 * Background saving started by pid 11955
11955:C 23 Jun 2025 16:31:05.102 * DB saved on disk
11955:C 23 Jun 2025 16:31:05.102 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12936:M 23 Jun 2025 16:31:05.195 * Background saving terminated with success
12936:signal-handler (1750669757) Received SIGTERM scheduling shutdown...
12936:M 23 Jun 2025 17:09:17.282 * User requested shutdown...
12936:M 23 Jun 2025 17:09:17.283 * 1 of 1 replicas are in sync when shutting down.
12936:M 23 Jun 2025 17:09:17.283 * Calling fsync() on the AOF file.
12936:M 23 Jun 2025 17:09:17.295 * Saving the final RDB snapshot before exiting.
12936:M 23 Jun 2025 17:09:17.334 * DB saved on disk
12936:M 23 Jun 2025 17:09:17.339 * Removing the pid file.
12936:M 23 Jun 2025 17:09:17.349 # Redis is now ready to exit, bye bye...
3132:C 23 Jun 2025 17:13:06.905 * oO0OoO0OoO0Oo Redis is starting oO0OoO0OoO0Oo
3132:C 23 Jun 2025 17:13:06.905 * Redis version=7.2.7, bits=64, commit=00000000, modified=0, pid=3132, just started
3132:C 23 Jun 2025 17:13:06.905 * Configuration loaded
3132:M 23 Jun 2025 17:13:06.906 * Increased maximum number of open files to 10032 (it was originally set to 256).
3132:M 23 Jun 2025 17:13:06.906 * monotonic clock: POSIX clock_gettime
3132:M 23 Jun 2025 17:13:06.906 * Running mode=cluster, port=7100.
3132:M 23 Jun 2025 17:13:06.906 # WARNING: The TCP backlog setting of 511 cannot be enforced because kern.ipc.somaxconn is set to the lower value of 128.
3132:M 23 Jun 2025 17:13:06.907 * Node configuration loaded, I'm ae5435a2fdf1e95cd782d831916bd1b9c024f143
3132:M 23 Jun 2025 17:13:06.907 * Server initialized
3132:M 23 Jun 2025 17:13:06.908 * Reading RDB base file on AOF loading...
3132:M 23 Jun 2025 17:13:06.908 * Loading RDB produced by version 7.2.7
3132:M 23 Jun 2025 17:13:06.908 * RDB age 1219118 seconds
3132:M 23 Jun 2025 17:13:06.908 * RDB memory usage when created 1.70 Mb
3132:M 23 Jun 2025 17:13:06.908 * RDB is base AOF
3132:M 23 Jun 2025 17:13:06.908 * Done loading RDB, keys loaded: 0, keys expired: 0.
3132:M 23 Jun 2025 17:13:06.908 * DB loaded from base file appendonly-7100.aof.1.base.rdb: 0.001 seconds
3132:M 23 Jun 2025 17:13:06.919 * DB saved on disk
3132:M 23 Jun 2025 17:13:06.924 * DB saved on disk
3132:M 23 Jun 2025 17:13:06.929 * DB saved on disk
3132:M 23 Jun 2025 17:13:06.935 * DB saved on disk
3132:M 23 Jun 2025 17:13:06.941 * DB saved on disk
3132:M 23 Jun 2025 17:13:06.945 * DB saved on disk
3132:M 23 Jun 2025 17:13:06.949 * DB saved on disk
3132:M 23 Jun 2025 17:13:06.954 * DB saved on disk
3132:M 23 Jun 2025 17:13:06.960 * DB saved on disk
3132:M 23 Jun 2025 17:13:06.964 * DB saved on disk
3132:M 23 Jun 2025 17:13:06.969 * DB saved on disk
3132:M 23 Jun 2025 17:13:06.974 * DB saved on disk
3132:M 23 Jun 2025 17:13:06.975 * DB loaded from incr file appendonly-7100.aof.1.incr.aof: 0.067 seconds
3132:M 23 Jun 2025 17:13:06.975 * DB loaded from append only file: 0.068 seconds
3132:M 23 Jun 2025 17:13:06.975 * Opening AOF incr file appendonly-7100.aof.1.incr.aof on server start
3132:M 23 Jun 2025 17:13:06.975 * Ready to accept connections tcp
3132:M 23 Jun 2025 17:13:09.020 * Cluster state changed: ok
3132:M 23 Jun 2025 17:13:17.118 * Replica 127.0.0.1:7105 asks for synchronization
3132:M 23 Jun 2025 17:13:17.118 * Partial resynchronization not accepted: Replication ID mismatch (Replica asked for 'bb88214ad006c5b5a57ba8952744496f4a519ccf', my replication IDs are 'c6a203ada4a20b5ab7b4da01daedd0e313df0a5b' and '0000000000000000000000000000000000000000')
3132:M 23 Jun 2025 17:13:17.122 * Replication backlog created, my new replication IDs are 'e4789b67c38c7388411b67bf2b22899d8e6c683c' and '0000000000000000000000000000000000000000'
3132:M 23 Jun 2025 17:13:17.122 * Delay next BGSAVE for diskless SYNC
3132:M 23 Jun 2025 17:13:22.120 * Starting BGSAVE for SYNC with target: replicas sockets
3132:M 23 Jun 2025 17:13:22.121 * Background RDB transfer started by pid 3185
3185:C 23 Jun 2025 17:13:22.121 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 23 Jun 2025 17:13:22.121 * Diskless rdb transfer, done reading from pipe, 1 replicas still up.
3132:M 23 Jun 2025 17:13:22.133 * Background RDB transfer terminated with success
3132:M 23 Jun 2025 17:13:22.134 * Streamed RDB transfer with replica 127.0.0.1:7105 succeeded (socket). Waiting for REPLCONF ACK from replica to enable streaming
3132:M 23 Jun 2025 17:13:22.134 * Synchronization with replica 127.0.0.1:7105 succeeded
3132:M 23 Jun 2025 17:13:22.139 * DB saved on disk
3132:M 23 Jun 2025 17:13:22.146 * configEpoch set to 0 via CLUSTER RESET HARD
3132:M 23 Jun 2025 17:13:22.150 * Node hard reset, now I'm 57ad239be037fbf48439d833094adbcd814a6ec9
3132:M 23 Jun 2025 17:13:22.150 # Cluster state changed: fail
3132:M 23 Jun 2025 17:13:22.297 * Connection with replica 127.0.0.1:7105 lost.
3132:M 23 Jun 2025 17:13:25.360 * configEpoch set to 1 via CLUSTER SET-CONFIG-EPOCH
3132:M 23 Jun 2025 17:13:26.398 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 23 Jun 2025 17:13:26.398 * Partial resynchronization not accepted: Replication ID mismatch (Replica asked for '6d4f9b3112c5f83c281d301418054bdff9175467', my replication IDs are 'e4789b67c38c7388411b67bf2b22899d8e6c683c' and '0000000000000000000000000000000000000000')
3132:M 23 Jun 2025 17:13:26.398 * Delay next BGSAVE for diskless SYNC
3132:M 23 Jun 2025 17:13:30.329 * Cluster state changed: ok
3132:M 23 Jun 2025 17:13:31.242 * Starting BGSAVE for SYNC with target: replicas sockets
3132:M 23 Jun 2025 17:13:31.244 * Background RDB transfer started by pid 3226
3226:C 23 Jun 2025 17:13:31.245 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 23 Jun 2025 17:13:31.246 * Diskless rdb transfer, done reading from pipe, 1 replicas still up.
3132:M 23 Jun 2025 17:13:31.283 * Background RDB transfer terminated with success
3132:M 23 Jun 2025 17:13:31.283 * Streamed RDB transfer with replica 127.0.0.1:7103 succeeded (socket). Waiting for REPLCONF ACK from replica to enable streaming
3132:M 23 Jun 2025 17:13:31.312 * Synchronization with replica 127.0.0.1:7103 succeeded
3132:M 23 Jun 2025 17:56:04.805 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 23 Jun 2025 17:56:04.805 * Connection with replica 127.0.0.1:7103 lost.
3132:M 23 Jun 2025 17:56:04.808 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 23 Jun 2025 17:56:04.809 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 5227.
3132:M 23 Jun 2025 18:02:19.886 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 23 Jun 2025 18:02:19.886 * Connection with replica 127.0.0.1:7103 lost.
3132:M 23 Jun 2025 18:02:19.887 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 23 Jun 2025 18:02:19.887 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 5283.
3132:M 23 Jun 2025 18:19:20.601 * 1 changes in 3600 seconds. Saving...
3132:M 23 Jun 2025 18:19:20.733 * Background saving started by pid 21098
21098:C 23 Jun 2025 18:19:20.752 * DB saved on disk
21098:C 23 Jun 2025 18:19:20.753 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 23 Jun 2025 18:19:20.836 * Background saving terminated with success
3132:M 23 Jun 2025 18:19:20.937 * Connection with replica 127.0.0.1:7103 lost.
3132:M 23 Jun 2025 18:19:20.939 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 23 Jun 2025 18:19:20.939 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 5353.
3132:M 23 Jun 2025 18:42:16.912 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 23 Jun 2025 18:42:16.912 * Connection with replica 127.0.0.1:7103 lost.
3132:M 23 Jun 2025 18:42:16.916 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 23 Jun 2025 18:42:16.917 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 5969.
3132:M 23 Jun 2025 19:19:21.023 * 1 changes in 3600 seconds. Saving...
3132:M 23 Jun 2025 19:19:21.027 * Background saving started by pid 37260
37260:C 23 Jun 2025 19:19:21.042 * DB saved on disk
37260:C 23 Jun 2025 19:19:21.045 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 23 Jun 2025 19:19:21.129 * Background saving terminated with success
3132:M 23 Jun 2025 20:01:12.221 * Connection with replica 127.0.0.1:7103 lost.
3132:M 23 Jun 2025 20:01:12.240 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 23 Jun 2025 20:01:12.240 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 13273.
3132:M 23 Jun 2025 20:16:16.673 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 23 Jun 2025 20:16:16.699 * Connection with replica 127.0.0.1:7103 lost.
3132:M 23 Jun 2025 20:16:16.702 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 23 Jun 2025 20:16:16.702 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 14197.
3132:M 23 Jun 2025 20:19:07.356 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 23 Jun 2025 20:19:07.361 * Connection with replica 127.0.0.1:7103 lost.
3132:M 23 Jun 2025 20:19:07.376 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 23 Jun 2025 20:19:07.376 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 14308.
3132:M 23 Jun 2025 20:19:54.948 * 1 changes in 3600 seconds. Saving...
3132:M 23 Jun 2025 20:19:54.949 * Background saving started by pid 56587
56587:C 23 Jun 2025 20:19:54.956 * DB saved on disk
56587:C 23 Jun 2025 20:19:54.957 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 23 Jun 2025 20:19:55.051 * Background saving terminated with success
3132:M 23 Jun 2025 21:16:43.146 * Connection with replica 127.0.0.1:7103 lost.
3132:M 23 Jun 2025 21:16:43.150 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 23 Jun 2025 21:16:43.150 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 20730.
3132:M 23 Jun 2025 21:33:23.694 * 1 changes in 3600 seconds. Saving...
3132:M 23 Jun 2025 21:33:23.810 * Background saving started by pid 77806
3132:M 23 Jun 2025 21:33:23.811 * Connection with replica 127.0.0.1:7103 lost.
77806:C 23 Jun 2025 21:33:23.825 * DB saved on disk
77806:C 23 Jun 2025 21:33:23.826 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 23 Jun 2025 21:33:23.827 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 23 Jun 2025 21:33:23.827 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 20800.
3132:M 23 Jun 2025 21:33:23.912 * Background saving terminated with success
3132:M 23 Jun 2025 21:51:32.825 * Connection with replica 127.0.0.1:7103 lost.
3132:M 23 Jun 2025 21:51:32.827 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 23 Jun 2025 21:51:32.827 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 20884.
3132:M 23 Jun 2025 22:07:41.852 * Connection with replica 127.0.0.1:7103 lost.
3132:M 23 Jun 2025 22:07:41.853 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 23 Jun 2025 22:07:41.853 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 20898.
3132:M 23 Jun 2025 22:24:07.878 * Connection with replica 127.0.0.1:7103 lost.
3132:M 23 Jun 2025 22:24:07.892 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 23 Jun 2025 22:24:07.892 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 20898.
3132:M 23 Jun 2025 22:39:27.854 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 23 Jun 2025 22:39:27.879 * Connection with replica 127.0.0.1:7103 lost.
3132:M 23 Jun 2025 22:39:27.881 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 23 Jun 2025 22:39:27.881 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 20912.
3132:M 23 Jun 2025 22:43:36.255 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 23 Jun 2025 22:43:36.290 * Connection with replica 127.0.0.1:7103 lost.
3132:M 23 Jun 2025 22:43:36.308 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 23 Jun 2025 22:43:36.308 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 20912.
3132:M 23 Jun 2025 22:51:30.395 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 23 Jun 2025 22:51:30.526 * Connection with replica 127.0.0.1:7103 lost.
3132:M 23 Jun 2025 22:51:30.529 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 23 Jun 2025 22:51:30.529 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 20982.
3132:M 23 Jun 2025 23:20:16.755 * 1 changes in 3600 seconds. Saving...
3132:M 23 Jun 2025 23:20:16.756 * Background saving started by pid 90509
90509:C 23 Jun 2025 23:20:16.763 * DB saved on disk
90509:C 23 Jun 2025 23:20:16.763 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 23 Jun 2025 23:20:16.858 * Background saving terminated with success
3132:M 24 Jun 2025 00:20:17.098 * 1 changes in 3600 seconds. Saving...
3132:M 24 Jun 2025 00:20:17.099 * Background saving started by pid 14367
14367:C 24 Jun 2025 00:20:17.108 * DB saved on disk
14367:C 24 Jun 2025 00:20:17.108 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 24 Jun 2025 00:20:17.201 * Background saving terminated with success
3132:M 24 Jun 2025 09:54:38.803 * Connection with replica 127.0.0.1:7103 lost.
3132:M 24 Jun 2025 09:54:38.902 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 24 Jun 2025 09:54:38.904 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 64448.
3132:M 24 Jun 2025 10:11:41.225 * 1 changes in 3600 seconds. Saving...
3132:M 24 Jun 2025 10:11:41.227 * Background saving started by pid 68426
68426:C 24 Jun 2025 10:11:41.238 * DB saved on disk
68426:C 24 Jun 2025 10:11:41.238 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 24 Jun 2025 10:11:41.329 * Background saving terminated with success
3132:M 24 Jun 2025 11:11:42.056 * 1 changes in 3600 seconds. Saving...
3132:M 24 Jun 2025 11:11:42.057 * Background saving started by pid 90787
90787:C 24 Jun 2025 11:11:42.063 * DB saved on disk
90787:C 24 Jun 2025 11:11:42.063 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 24 Jun 2025 11:11:42.159 * Background saving terminated with success
3132:M 24 Jun 2025 17:55:13.085 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 24 Jun 2025 17:55:13.085 * Connection with replica 127.0.0.1:7103 lost.
3132:M 24 Jun 2025 17:55:13.088 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 24 Jun 2025 17:55:13.089 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 104373.
3132:M 24 Jun 2025 19:26:58.237 * 1 changes in 3600 seconds. Saving...
3132:M 24 Jun 2025 19:26:58.239 * Background saving started by pid 57679
57679:C 24 Jun 2025 19:26:58.251 * DB saved on disk
57679:C 24 Jun 2025 19:26:58.252 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 24 Jun 2025 19:26:58.341 * Background saving terminated with success
3132:M 24 Jun 2025 20:26:59.052 * 1 changes in 3600 seconds. Saving...
3132:M 24 Jun 2025 20:26:59.053 * Background saving started by pid 81067
81067:C 24 Jun 2025 20:26:59.061 * DB saved on disk
81067:C 24 Jun 2025 20:26:59.062 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 24 Jun 2025 20:26:59.156 * Background saving terminated with success
3132:M 24 Jun 2025 20:50:51.345 * Connection with replica 127.0.0.1:7103 lost.
3132:M 24 Jun 2025 20:50:51.346 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 24 Jun 2025 20:50:51.347 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 120259.
3132:M 24 Jun 2025 22:13:06.846 * Connection with replica 127.0.0.1:7103 lost.
3132:M 24 Jun 2025 22:13:06.851 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 24 Jun 2025 22:13:06.851 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 126993.
3132:M 24 Jun 2025 22:46:59.418 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 24 Jun 2025 22:46:59.420 * Connection with replica 127.0.0.1:7103 lost.
3132:M 24 Jun 2025 22:46:59.422 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 24 Jun 2025 22:46:59.422 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 127147.
3132:M 24 Jun 2025 23:04:31.404 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 24 Jun 2025 23:04:31.405 * Connection with replica 127.0.0.1:7103 lost.
3132:M 24 Jun 2025 23:04:31.414 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 24 Jun 2025 23:04:31.414 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 127161.
3132:M 24 Jun 2025 23:10:31.086 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 24 Jun 2025 23:10:31.086 * Connection with replica 127.0.0.1:7103 lost.
3132:M 24 Jun 2025 23:10:31.091 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 24 Jun 2025 23:10:31.092 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 127161.
3132:M 24 Jun 2025 23:15:01.265 * 1 changes in 3600 seconds. Saving...
3132:M 24 Jun 2025 23:15:01.267 * Background saving started by pid 22648
22648:C 24 Jun 2025 23:15:01.274 * DB saved on disk
22648:C 24 Jun 2025 23:15:01.276 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 24 Jun 2025 23:15:01.368 * Background saving terminated with success
3132:M 24 Jun 2025 23:30:21.742 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 24 Jun 2025 23:30:21.742 * Connection with replica 127.0.0.1:7103 lost.
3132:M 24 Jun 2025 23:30:21.743 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 24 Jun 2025 23:30:21.743 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 130248.
3132:M 24 Jun 2025 23:47:47.903 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 24 Jun 2025 23:47:47.903 * Connection with replica 127.0.0.1:7103 lost.
3132:M 24 Jun 2025 23:47:47.905 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 24 Jun 2025 23:47:47.905 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 130318.
3132:M 24 Jun 2025 23:48:59.871 * Connection with replica 127.0.0.1:7103 lost.
3132:M 24 Jun 2025 23:48:59.887 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 24 Jun 2025 23:48:59.887 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 130318.
3132:M 25 Jun 2025 00:15:02.082 * 1 changes in 3600 seconds. Saving...
3132:M 25 Jun 2025 00:15:02.084 * Background saving started by pid 33316
33316:C 25 Jun 2025 00:15:02.098 * DB saved on disk
33316:C 25 Jun 2025 00:15:02.101 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 25 Jun 2025 00:15:02.186 * Background saving terminated with success
3132:M 25 Jun 2025 01:15:03.095 * 1 changes in 3600 seconds. Saving...
3132:M 25 Jun 2025 01:15:03.097 * Background saving started by pid 51618
51618:C 25 Jun 2025 01:15:03.106 * DB saved on disk
51618:C 25 Jun 2025 01:15:03.108 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 25 Jun 2025 01:15:03.201 * Background saving terminated with success
3132:M 25 Jun 2025 02:03:12.810 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 02:03:12.813 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 02:03:12.814 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 142578.
3132:M 25 Jun 2025 02:19:02.773 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 25 Jun 2025 02:19:02.792 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 02:19:02.814 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 02:19:02.814 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 142592.
3132:M 25 Jun 2025 02:20:42.336 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 02:20:42.342 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 02:20:42.342 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 142592.
3132:M 25 Jun 2025 02:22:36.667 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 02:22:36.672 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 02:22:36.672 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 142662.
3132:M 25 Jun 2025 02:39:14.710 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 25 Jun 2025 02:39:14.721 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 02:39:14.738 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 02:39:14.738 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 142676.
3132:M 25 Jun 2025 02:56:03.587 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 25 Jun 2025 02:56:03.722 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 02:56:03.724 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 02:56:03.724 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 142676.
3132:M 25 Jun 2025 03:12:20.598 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 25 Jun 2025 03:12:20.715 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 03:12:20.725 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 03:12:20.725 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 142690.
3132:M 25 Jun 2025 03:29:15.686 * Connection with replica client id #17654 lost.
3132:M 25 Jun 2025 03:29:15.694 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 03:29:15.694 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 84 bytes of backlog starting from offset 142788.
3132:M 25 Jun 2025 03:45:28.466 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 03:45:28.472 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 03:45:28.472 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 142928.
3132:M 25 Jun 2025 04:01:41.364 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 04:01:41.365 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 04:01:41.366 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 142998.
3132:M 25 Jun 2025 04:17:54.222 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 04:17:54.224 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 04:17:54.224 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 143068.
3132:M 25 Jun 2025 04:23:39.811 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 04:23:39.812 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 04:23:39.813 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 143124.
3132:M 25 Jun 2025 04:39:52.604 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 04:39:52.607 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 04:39:52.607 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 143194.
3132:M 25 Jun 2025 04:56:04.616 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 04:56:04.619 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 04:56:04.619 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 143250.
3132:M 25 Jun 2025 05:12:17.376 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 05:12:17.378 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 05:12:17.378 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 143320.
3132:M 25 Jun 2025 05:24:39.912 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 05:24:39.914 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 05:24:39.914 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 143390.
3132:M 25 Jun 2025 05:40:52.711 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 05:40:52.714 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 05:40:52.714 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 143446.
3132:M 25 Jun 2025 05:57:06.157 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 25 Jun 2025 05:57:06.157 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 05:57:06.160 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 05:57:06.160 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 143516.
3132:M 25 Jun 2025 06:13:18.978 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 25 Jun 2025 06:13:18.978 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 06:13:18.983 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 06:13:18.983 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 143572.
3132:M 25 Jun 2025 06:25:40.124 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 25 Jun 2025 06:25:40.125 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 06:25:40.131 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 06:25:40.132 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 143642.
3132:M 25 Jun 2025 06:41:52.959 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 25 Jun 2025 06:41:52.959 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 06:41:52.962 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 06:41:52.963 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 143712.
3132:M 25 Jun 2025 06:58:05.791 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 25 Jun 2025 06:58:05.791 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 06:58:05.795 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 06:58:05.795 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 143768.
3132:M 25 Jun 2025 07:14:10.102 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 25 Jun 2025 07:14:10.103 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 07:14:10.108 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 07:14:10.108 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 143838.
3132:M 25 Jun 2025 07:26:40.007 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 25 Jun 2025 07:26:40.007 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 07:26:40.011 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 07:26:40.011 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 143894.
3132:M 25 Jun 2025 07:42:52.000 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 25 Jun 2025 07:42:52.000 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 07:42:52.003 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 07:42:52.003 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 143964.
3132:M 25 Jun 2025 07:59:04.721 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 25 Jun 2025 07:59:04.721 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 07:59:04.725 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 07:59:04.725 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 144020.
3132:M 25 Jun 2025 08:02:20.152 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 25 Jun 2025 08:02:20.152 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 08:02:20.156 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 08:02:20.156 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 144090.
3132:M 25 Jun 2025 08:18:32.930 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 25 Jun 2025 08:18:32.930 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 08:18:32.937 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 08:18:32.937 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 144146.
3132:M 25 Jun 2025 08:27:39.685 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 25 Jun 2025 08:27:39.721 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 08:27:39.735 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 08:27:39.736 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 144216.
3132:M 25 Jun 2025 08:30:28.685 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 25 Jun 2025 08:30:28.720 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 08:30:28.725 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 08:30:28.725 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 144286.
3132:M 25 Jun 2025 08:47:32.032 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 25 Jun 2025 08:47:32.045 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 08:47:32.070 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 08:47:32.070 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 144412.
3132:M 25 Jun 2025 09:01:17.932 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 25 Jun 2025 09:01:17.966 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 09:01:17.988 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 09:01:17.988 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 144468.
3132:M 25 Jun 2025 09:18:56.614 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 25 Jun 2025 09:18:56.614 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 09:18:56.618 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 09:18:56.618 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 144538.
3132:M 25 Jun 2025 09:28:39.491 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 25 Jun 2025 09:28:39.529 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 09:28:39.543 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 09:28:39.543 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 144552.
3132:M 25 Jun 2025 09:44:51.596 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 25 Jun 2025 09:44:51.596 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 09:44:51.599 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 09:44:51.599 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 144608.
3132:M 25 Jun 2025 09:58:07.973 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 25 Jun 2025 09:58:07.995 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 09:58:08.007 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 09:58:08.007 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 144622.
3132:M 25 Jun 2025 14:28:27.971 * 1 changes in 3600 seconds. Saving...
3132:M 25 Jun 2025 14:28:27.972 * Background saving started by pid 64015
64015:C 25 Jun 2025 14:28:27.983 * DB saved on disk
64015:C 25 Jun 2025 14:28:27.984 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 25 Jun 2025 14:28:28.074 * Background saving terminated with success
3132:M 25 Jun 2025 15:28:29.085 * 1 changes in 3600 seconds. Saving...
3132:M 25 Jun 2025 15:28:29.088 * Background saving started by pid 96237
96237:C 25 Jun 2025 15:28:29.097 * DB saved on disk
96237:C 25 Jun 2025 15:28:29.101 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 25 Jun 2025 15:28:29.190 * Background saving terminated with success
3132:M 25 Jun 2025 16:28:30.018 * 1 changes in 3600 seconds. Saving...
3132:M 25 Jun 2025 16:28:30.020 * Background saving started by pid 57968
57968:C 25 Jun 2025 16:28:30.037 * DB saved on disk
57968:C 25 Jun 2025 16:28:30.037 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 25 Jun 2025 16:28:30.122 * Background saving terminated with success
3132:M 25 Jun 2025 17:28:31.033 * 1 changes in 3600 seconds. Saving...
3132:M 25 Jun 2025 17:28:31.035 * Background saving started by pid 2168
2168:C 25 Jun 2025 17:28:31.047 * DB saved on disk
2168:C 25 Jun 2025 17:28:31.048 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 25 Jun 2025 17:28:31.138 * Background saving terminated with success
3132:M 25 Jun 2025 18:10:23.358 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 18:10:23.365 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 18:10:23.365 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 195994.
3132:M 25 Jun 2025 18:28:32.054 * 1 changes in 3600 seconds. Saving...
3132:M 25 Jun 2025 18:28:32.056 * Background saving started by pid 17984
17984:C 25 Jun 2025 18:28:32.064 * DB saved on disk
17984:C 25 Jun 2025 18:28:32.064 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 25 Jun 2025 18:28:32.158 * Background saving terminated with success
3132:M 25 Jun 2025 19:28:33.026 * 1 changes in 3600 seconds. Saving...
3132:M 25 Jun 2025 19:28:33.030 * Background saving started by pid 40904
40904:C 25 Jun 2025 19:28:33.051 * DB saved on disk
40904:C 25 Jun 2025 19:28:33.052 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 25 Jun 2025 19:28:33.131 * Background saving terminated with success
3132:M 25 Jun 2025 20:28:34.097 * 1 changes in 3600 seconds. Saving...
3132:M 25 Jun 2025 20:28:34.098 * Background saving started by pid 63479
63479:C 25 Jun 2025 20:28:34.117 * DB saved on disk
63479:C 25 Jun 2025 20:28:34.117 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 25 Jun 2025 20:28:34.199 * Background saving terminated with success
3132:M 25 Jun 2025 21:28:37.008 * 1 changes in 3600 seconds. Saving...
3132:M 25 Jun 2025 21:28:37.008 * Background saving started by pid 86757
86757:C 25 Jun 2025 21:28:37.016 * DB saved on disk
86757:C 25 Jun 2025 21:28:37.016 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 25 Jun 2025 21:28:37.110 * Background saving terminated with success
3132:M 25 Jun 2025 22:28:38.045 * 1 changes in 3600 seconds. Saving...
3132:M 25 Jun 2025 22:28:38.046 * Background saving started by pid 11900
11900:C 25 Jun 2025 22:28:38.054 * DB saved on disk
11900:C 25 Jun 2025 22:28:38.054 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 25 Jun 2025 22:28:38.148 * Background saving terminated with success
3132:M 25 Jun 2025 22:49:16.213 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 22:49:16.214 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 22:49:16.214 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 223441.
3132:M 25 Jun 2025 23:06:53.149 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 23:06:53.150 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 23:06:53.151 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 223497.
3132:M 25 Jun 2025 23:24:51.071 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 23:24:51.074 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 23:24:51.074 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 223511.
3132:M 25 Jun 2025 23:42:12.752 * 1 changes in 3600 seconds. Saving...
3132:M 25 Jun 2025 23:42:12.873 * Background saving started by pid 21583
21583:C 25 Jun 2025 23:42:12.892 * DB saved on disk
21583:C 25 Jun 2025 23:42:12.892 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 25 Jun 2025 23:42:12.979 * Background saving terminated with success
3132:M 25 Jun 2025 23:42:13.083 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 23:42:13.085 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 23:42:13.085 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 223511.
3132:M 25 Jun 2025 23:57:24.068 * Connection with replica 127.0.0.1:7103 lost.
3132:M 25 Jun 2025 23:57:24.070 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 25 Jun 2025 23:57:24.071 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 223525.
3132:M 26 Jun 2025 00:12:52.434 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 00:12:52.439 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 00:12:52.440 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 223609.
3132:M 26 Jun 2025 00:23:10.264 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 00:23:10.280 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 00:23:10.280 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 223679.
3132:M 26 Jun 2025 00:49:00.174 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 00:49:00.179 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 00:49:00.179 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 223848.
3132:M 26 Jun 2025 00:57:42.033 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 00:57:42.035 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 00:57:42.035 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 223974.
3132:M 26 Jun 2025 01:20:35.754 * 1 changes in 3600 seconds. Saving...
3132:M 26 Jun 2025 01:20:35.755 * Background saving started by pid 32888
32888:C 26 Jun 2025 01:20:35.762 * DB saved on disk
32888:C 26 Jun 2025 01:20:35.762 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 26 Jun 2025 01:20:35.856 * Background saving terminated with success
3132:M 26 Jun 2025 02:20:36.004 * 1 changes in 3600 seconds. Saving...
3132:M 26 Jun 2025 02:20:36.007 * Background saving started by pid 57756
57756:C 26 Jun 2025 02:20:36.014 * DB saved on disk
57756:C 26 Jun 2025 02:20:36.015 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 26 Jun 2025 02:20:36.109 * Background saving terminated with success
3132:M 26 Jun 2025 03:17:55.276 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 03:17:55.278 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 03:17:55.278 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 235282.
3132:M 26 Jun 2025 03:34:07.215 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 03:34:07.217 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 03:34:07.217 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 235338.
3132:M 26 Jun 2025 03:50:08.337 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 03:50:08.340 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 03:50:08.340 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 235408.
3132:M 26 Jun 2025 04:06:16.221 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 04:06:16.223 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 04:06:16.223 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 235464.
3132:M 26 Jun 2025 04:22:28.342 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 04:22:28.456 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 04:22:28.465 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 04:22:28.465 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 43 bytes of backlog starting from offset 235534.
3132:M 26 Jun 2025 04:38:41.794 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 04:38:41.797 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 04:38:41.797 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 235633.
3132:M 26 Jun 2025 04:54:54.566 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 04:54:54.568 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 04:54:54.568 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 235703.
3132:M 26 Jun 2025 05:02:00.780 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 05:02:00.781 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 05:02:00.781 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 235773.
3132:M 26 Jun 2025 05:18:13.612 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 05:18:13.626 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 05:18:13.626 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 235829.
3132:M 26 Jun 2025 05:34:26.465 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 05:34:26.469 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 05:34:26.476 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 235899.
3132:M 26 Jun 2025 05:50:39.169 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 05:50:39.173 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 05:50:39.179 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 05:50:39.179 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 235955.
3132:M 26 Jun 2025 06:03:00.624 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 06:03:00.765 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 06:03:00.768 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 06:03:00.769 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 236025.
3132:M 26 Jun 2025 06:19:13.478 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 06:19:13.609 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 06:19:13.612 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 06:19:13.612 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 236095.
3132:M 26 Jun 2025 06:35:25.479 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 06:35:25.614 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 06:35:25.617 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 06:35:25.617 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 236151.
3132:M 26 Jun 2025 06:51:38.438 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 06:51:38.438 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 06:51:38.441 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 06:51:38.441 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 236221.
3132:M 26 Jun 2025 07:04:01.570 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 07:04:01.571 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 07:04:01.580 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 07:04:01.580 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 236277.
3132:M 26 Jun 2025 07:20:14.277 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 07:20:14.277 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 07:20:14.280 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 07:20:14.280 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 236347.
3132:M 26 Jun 2025 07:36:26.875 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 07:36:26.877 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 07:36:26.886 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 07:36:26.887 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 236417.
3132:M 26 Jun 2025 07:51:27.849 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 07:51:27.852 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 07:51:27.852 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 236473.
3132:M 26 Jun 2025 08:05:01.223 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 08:05:01.226 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 08:05:01.228 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 08:05:01.228 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 236543.
3132:M 26 Jun 2025 08:21:13.942 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 08:21:13.944 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 08:21:13.950 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 08:21:13.950 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 236613.
3132:M 26 Jun 2025 08:37:26.661 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 08:37:26.661 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 08:37:26.664 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 08:37:26.665 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 236669.
3132:M 26 Jun 2025 08:53:39.418 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 08:53:39.418 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 08:53:39.422 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 08:53:39.422 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 236739.
3132:M 26 Jun 2025 08:57:52.624 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 08:57:52.624 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 08:57:52.628 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 08:57:52.628 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 236795.
3132:M 26 Jun 2025 09:00:46.509 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 09:00:46.541 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 09:00:46.545 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 09:00:46.545 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 236865.
3132:M 26 Jun 2025 09:06:00.892 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 09:06:00.892 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 09:06:00.895 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 09:06:00.895 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 236935.
3132:M 26 Jun 2025 09:22:55.937 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 09:22:55.939 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 09:22:55.939 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 236991.
3132:M 26 Jun 2025 09:40:41.905 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 09:40:41.908 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 09:40:41.908 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 237005.
3132:M 26 Jun 2025 09:56:17.815 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 09:56:17.855 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 09:56:17.866 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 09:56:17.866 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 237005.
3132:M 26 Jun 2025 09:59:33.236 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 09:59:33.236 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 09:59:33.239 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 09:59:33.240 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 237089.
3132:M 26 Jun 2025 10:03:37.267 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 10:03:37.267 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 10:03:37.271 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 10:03:37.271 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 237159.
3132:M 26 Jun 2025 10:47:35.834 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 10:47:35.835 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 10:47:35.835 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 239441.
3132:M 26 Jun 2025 11:51:28.460 * 1 changes in 3600 seconds. Saving...
3132:M 26 Jun 2025 11:51:28.461 * Background saving started by pid 48190
48190:C 26 Jun 2025 11:51:28.469 * DB saved on disk
48190:C 26 Jun 2025 11:51:28.469 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 26 Jun 2025 11:51:28.563 * Background saving terminated with success
3132:M 26 Jun 2025 12:29:08.828 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 12:29:08.834 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 12:29:08.834 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 247347.
3132:M 26 Jun 2025 12:42:31.051 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 12:42:31.053 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 12:42:31.053 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 247417.
3132:M 26 Jun 2025 12:58:20.673 * 1 changes in 3600 seconds. Saving...
3132:M 26 Jun 2025 12:58:20.793 * Background saving started by pid 62787
3132:M 26 Jun 2025 12:58:20.795 * Connection with replica 127.0.0.1:7103 lost.
62787:C 26 Jun 2025 12:58:20.799 * DB saved on disk
62787:C 26 Jun 2025 12:58:20.804 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 26 Jun 2025 12:58:20.810 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 12:58:20.810 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 247473.
3132:M 26 Jun 2025 12:58:20.896 * Background saving terminated with success
3132:M 26 Jun 2025 13:21:56.957 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 13:21:56.958 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 13:21:56.961 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 13:21:56.961 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 247991.
3132:M 26 Jun 2025 13:44:59.771 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 13:44:59.772 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 13:44:59.788 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 13:44:59.788 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 248509.
3132:M 26 Jun 2025 14:06:29.655 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 14:06:29.780 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 14:06:29.785 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 14:06:29.785 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 249083.
3132:M 26 Jun 2025 14:17:09.048 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 14:17:09.054 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 14:17:09.085 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 14:17:09.091 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 249139.
3132:M 26 Jun 2025 18:33:11.088 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 18:33:11.090 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 18:33:11.091 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 269982.
3132:M 26 Jun 2025 19:19:42.385 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 19:19:42.386 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 19:19:42.388 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 19:19:42.388 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 273720.
3132:M 26 Jun 2025 19:25:49.650 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 19:25:49.770 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 19:25:49.772 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 19:25:49.772 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 273846.
3132:M 26 Jun 2025 19:43:48.854 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 19:43:48.855 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 19:43:48.857 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 19:43:48.857 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 273916.
3132:M 26 Jun 2025 20:00:43.745 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 20:00:43.768 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 20:00:43.785 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 20:00:43.786 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 273916.
3132:M 26 Jun 2025 20:17:32.744 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 20:17:32.761 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 20:17:32.772 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 20:17:32.772 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 273930.
3132:M 26 Jun 2025 20:34:46.632 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 20:34:46.768 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 20:34:46.772 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 20:34:46.772 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 273930.
3132:M 26 Jun 2025 20:50:04.637 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 20:50:04.755 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 20:50:04.756 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 20:50:04.756 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 273944.
3132:M 26 Jun 2025 20:56:38.782 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 20:56:38.782 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 20:56:38.789 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 20:56:38.790 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 274014.
3132:M 26 Jun 2025 21:20:32.235 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 26 Jun 2025 21:20:32.350 * Connection with replica 127.0.0.1:7103 lost.
3132:M 26 Jun 2025 21:20:32.365 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 26 Jun 2025 21:20:32.365 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 275610.
3132:M 26 Jun 2025 21:38:37.835 * 1 changes in 3600 seconds. Saving...
3132:M 26 Jun 2025 21:38:37.836 * Background saving started by pid 12520
12520:C 26 Jun 2025 21:38:37.847 * DB saved on disk
12520:C 26 Jun 2025 21:38:37.848 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 26 Jun 2025 21:38:37.937 * Background saving terminated with success
3132:M 26 Jun 2025 22:38:38.039 * 1 changes in 3600 seconds. Saving...
3132:M 26 Jun 2025 22:38:38.040 * Background saving started by pid 34232
34232:C 26 Jun 2025 22:38:38.055 * DB saved on disk
34232:C 26 Jun 2025 22:38:38.055 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 26 Jun 2025 22:38:38.142 * Background saving terminated with success
3132:M 27 Jun 2025 01:22:18.374 * 1 changes in 3600 seconds. Saving...
3132:M 27 Jun 2025 01:22:18.375 * Background saving started by pid 95912
95912:C 27 Jun 2025 01:22:18.380 * DB saved on disk
95912:C 27 Jun 2025 01:22:18.381 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 27 Jun 2025 01:22:18.476 * Background saving terminated with success
3132:M 27 Jun 2025 02:05:39.750 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 02:05:39.752 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 02:05:39.752 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 300974.
3132:M 27 Jun 2025 02:21:52.445 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 02:21:52.447 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 02:21:52.447 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 301044.
3132:M 27 Jun 2025 02:22:19.072 * 1 changes in 3600 seconds. Saving...
3132:M 27 Jun 2025 02:22:19.076 * Background saving started by pid 7929
7929:C 27 Jun 2025 02:22:19.088 * DB saved on disk
7929:C 27 Jun 2025 02:22:19.089 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 27 Jun 2025 02:22:19.178 * Background saving terminated with success
3132:M 27 Jun 2025 02:38:04.863 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 02:38:04.863 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 02:38:04.863 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 301114.
3132:M 27 Jun 2025 02:54:14.725 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 02:54:14.727 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 02:54:14.727 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 301170.
3132:M 27 Jun 2025 03:10:27.418 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 03:10:27.419 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 03:10:27.419 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 301240.
3132:M 27 Jun 2025 03:26:40.160 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 03:26:40.163 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 03:26:40.163 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 301296.
3132:M 27 Jun 2025 03:38:49.665 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 27 Jun 2025 03:38:49.790 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 03:38:49.792 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 03:38:49.792 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 301366.
3132:M 27 Jun 2025 03:50:28.910 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 03:50:28.912 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 03:50:28.912 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 301436.
3132:M 27 Jun 2025 04:06:37.722 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 04:06:37.725 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 04:06:37.725 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 301492.
3132:M 27 Jun 2025 04:38:59.896 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 04:38:59.898 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 04:38:59.898 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 301661.
3132:M 27 Jun 2025 04:55:16.644 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 04:55:16.646 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 04:55:16.647 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 301731.
3132:M 27 Jun 2025 05:11:29.353 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 05:11:29.354 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 05:11:29.354 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 301801.
3132:M 27 Jun 2025 05:27:42.076 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 05:27:42.077 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 05:27:42.077 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 301871.
3132:M 27 Jun 2025 05:43:55.014 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 05:43:55.015 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 05:43:55.015 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 301927.
3132:M 27 Jun 2025 06:00:07.689 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 06:00:07.692 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 06:00:07.692 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 301997.
3132:M 27 Jun 2025 06:16:20.711 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 27 Jun 2025 06:16:20.711 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 06:16:20.712 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 06:16:20.712 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 302067.
3132:M 27 Jun 2025 06:32:33.427 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 27 Jun 2025 06:32:33.427 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 06:32:33.429 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 06:32:33.429 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 302123.
3132:M 27 Jun 2025 06:48:46.111 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 27 Jun 2025 06:48:46.234 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 06:48:46.238 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 06:48:46.238 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 302193.
3132:M 27 Jun 2025 07:04:58.825 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 27 Jun 2025 07:04:58.943 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 07:04:58.946 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 07:04:58.946 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 302249.
3132:M 27 Jun 2025 07:21:11.940 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 27 Jun 2025 07:21:11.940 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 07:21:11.945 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 07:21:11.946 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 302319.
3132:M 27 Jun 2025 07:37:24.649 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 27 Jun 2025 07:37:24.650 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 07:37:24.653 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 07:37:24.654 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 302389.
3132:M 27 Jun 2025 07:42:07.896 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 07:42:07.898 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 07:42:07.899 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 302445.
3132:M 27 Jun 2025 07:55:12.813 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 07:55:12.814 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 07:55:12.815 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 302515.
3132:M 27 Jun 2025 08:11:24.791 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 08:11:24.792 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 08:11:24.792 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 302571.
3132:M 27 Jun 2025 08:27:37.316 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 27 Jun 2025 08:27:37.316 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 08:27:37.319 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 08:27:37.319 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 302641.
3132:M 27 Jun 2025 08:30:17.703 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 08:30:17.705 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 08:30:17.705 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 302711.
3132:M 27 Jun 2025 08:44:38.422 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 08:44:38.423 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 08:44:38.424 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 302767.
3132:M 27 Jun 2025 09:01:29.006 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 09:01:29.008 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 09:01:29.008 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 302837.
3132:M 27 Jun 2025 09:17:15.898 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 09:17:15.901 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 09:17:15.901 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 302837.
3132:M 27 Jun 2025 09:34:20.828 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 09:34:20.844 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 09:34:20.844 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 302851.
3132:M 27 Jun 2025 09:43:07.836 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 09:43:07.843 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 09:43:07.844 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 302851.
3132:M 27 Jun 2025 11:21:30.753 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 11:21:30.756 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 11:21:30.756 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 310677.
3132:M 27 Jun 2025 11:26:42.331 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 11:26:42.334 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 11:26:42.335 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 310747.
3132:M 27 Jun 2025 12:59:42.676 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 12:59:42.679 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 12:59:42.679 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 317201.
3132:M 27 Jun 2025 13:15:54.994 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 13:15:54.998 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 13:15:54.999 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 317285.
3132:M 27 Jun 2025 13:32:19.859 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 13:32:19.861 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 13:32:19.861 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 317355.
3132:M 27 Jun 2025 13:37:05.089 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 13:37:05.092 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 13:37:05.092 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 317425.
3132:M 27 Jun 2025 13:53:54.769 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 13:53:54.772 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 13:53:54.772 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 317803.
3132:M 27 Jun 2025 14:43:29.564 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 14:43:29.569 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 14:43:29.570 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 320911.
3132:M 27 Jun 2025 16:41:48.317 * 1 changes in 3600 seconds. Saving...
3132:M 27 Jun 2025 16:41:48.321 * Background saving started by pid 42231
42231:C 27 Jun 2025 16:41:48.344 * DB saved on disk
42231:C 27 Jun 2025 16:41:48.344 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 27 Jun 2025 16:41:48.425 * Background saving terminated with success
3132:M 27 Jun 2025 17:41:49.027 * 1 changes in 3600 seconds. Saving...
3132:M 27 Jun 2025 17:41:49.029 * Background saving started by pid 83372
83372:C 27 Jun 2025 17:41:49.083 * DB saved on disk
83372:C 27 Jun 2025 17:41:49.084 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 27 Jun 2025 17:41:49.131 * Background saving terminated with success
3132:M 27 Jun 2025 19:25:28.674 * 1 changes in 3600 seconds. Saving...
3132:M 27 Jun 2025 19:25:28.680 * Background saving started by pid 60379
60379:C 27 Jun 2025 19:25:28.687 * DB saved on disk
60379:C 27 Jun 2025 19:25:28.688 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 27 Jun 2025 19:25:28.782 * Background saving terminated with success
3132:M 27 Jun 2025 19:39:04.180 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 19:39:04.182 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 19:39:04.183 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 348735.
3132:M 27 Jun 2025 19:57:52.817 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 19:57:52.829 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 19:57:52.829 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 348875.
3132:M 27 Jun 2025 20:04:51.875 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 20:04:51.877 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 20:04:51.877 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 348959.
3132:M 27 Jun 2025 20:21:14.807 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 20:21:14.817 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 20:21:14.817 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 349029.
3132:M 27 Jun 2025 20:39:09.682 * 1 changes in 3600 seconds. Saving...
3132:M 27 Jun 2025 20:39:09.821 * Background saving started by pid 71433
71433:C 27 Jun 2025 20:39:09.831 * DB saved on disk
71433:C 27 Jun 2025 20:39:09.832 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 27 Jun 2025 20:39:09.924 * Background saving terminated with success
3132:M 27 Jun 2025 20:39:09.924 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 27 Jun 2025 20:39:09.924 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 20:39:09.925 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 20:39:09.925 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 349029.
3132:M 27 Jun 2025 20:56:28.889 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 27 Jun 2025 20:56:28.889 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 20:56:28.890 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 20:56:28.890 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 349043.
3132:M 27 Jun 2025 21:13:47.796 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 27 Jun 2025 21:13:47.836 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 21:13:47.839 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 21:13:47.839 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 349043.
3132:M 27 Jun 2025 21:29:01.809 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 27 Jun 2025 21:29:01.840 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 21:29:01.841 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 21:29:01.842 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 349057.
3132:M 27 Jun 2025 21:38:42.663 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 27 Jun 2025 21:38:42.663 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 21:38:42.670 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 21:38:42.671 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 349127.
3132:M 27 Jun 2025 21:54:55.416 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 27 Jun 2025 21:54:55.416 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 21:54:55.423 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 21:54:55.423 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 349197.
3132:M 27 Jun 2025 22:11:07.810 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 27 Jun 2025 22:11:07.814 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 22:11:07.816 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 22:11:07.817 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 349253.
3132:M 27 Jun 2025 22:39:41.901 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 27 Jun 2025 22:39:41.902 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 22:39:41.906 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 22:39:41.906 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 349477.
3132:M 27 Jun 2025 22:55:59.193 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 27 Jun 2025 22:55:59.193 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 22:55:59.196 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 22:55:59.197 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 349533.
3132:M 27 Jun 2025 23:12:11.330 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 27 Jun 2025 23:12:11.330 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 23:12:11.334 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 23:12:11.335 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 349603.
3132:M 27 Jun 2025 23:29:26.569 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 27 Jun 2025 23:29:26.607 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 23:29:26.616 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 23:29:26.616 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 349729.
3132:M 27 Jun 2025 23:40:41.845 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 23:40:41.847 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 23:40:41.847 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 349855.
3132:M 27 Jun 2025 23:56:54.692 * Connection with replica 127.0.0.1:7103 lost.
3132:M 27 Jun 2025 23:56:54.694 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 27 Jun 2025 23:56:54.694 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 349925.
3132:M 28 Jun 2025 00:13:07.526 * Connection with replica 127.0.0.1:7103 lost.
3132:M 28 Jun 2025 00:13:07.528 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 28 Jun 2025 00:13:07.528 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 349981.
3132:M 28 Jun 2025 00:23:31.592 * Connection with replica 127.0.0.1:7103 lost.
3132:M 28 Jun 2025 00:23:31.594 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 28 Jun 2025 00:23:31.594 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 350051.
3132:M 28 Jun 2025 01:29:10.012 * 1 changes in 3600 seconds. Saving...
3132:M 28 Jun 2025 01:29:10.014 * Background saving started by pid 33479
33479:C 28 Jun 2025 01:29:10.029 * DB saved on disk
33479:C 28 Jun 2025 01:29:10.029 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 28 Jun 2025 01:29:10.116 * Background saving terminated with success
3132:M 28 Jun 2025 02:29:11.051 * 1 changes in 3600 seconds. Saving...
3132:M 28 Jun 2025 02:29:11.054 * Background saving started by pid 87415
87415:C 28 Jun 2025 02:29:11.063 * DB saved on disk
87415:C 28 Jun 2025 02:29:11.064 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 28 Jun 2025 02:29:11.157 * Background saving terminated with success
3132:M 28 Jun 2025 03:06:27.032 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 28 Jun 2025 03:06:27.032 * Connection with replica 127.0.0.1:7103 lost.
3132:M 28 Jun 2025 03:06:27.035 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 28 Jun 2025 03:06:27.035 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 364898.
3132:M 28 Jun 2025 03:22:39.024 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 28 Jun 2025 03:22:39.024 * Connection with replica 127.0.0.1:7103 lost.
3132:M 28 Jun 2025 03:22:39.027 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 28 Jun 2025 03:22:39.027 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 364968.
3132:M 28 Jun 2025 03:38:26.053 * Connection with replica 127.0.0.1:7103 lost.
3132:M 28 Jun 2025 03:38:26.056 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 28 Jun 2025 03:38:26.056 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 365024.
3132:M 28 Jun 2025 03:54:15.869 * Connection with replica 127.0.0.1:7103 lost.
3132:M 28 Jun 2025 03:54:15.871 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 28 Jun 2025 03:54:15.871 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 365094.
3132:M 28 Jun 2025 04:10:46.074 * Connection with replica 127.0.0.1:7103 lost.
3132:M 28 Jun 2025 04:10:46.077 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 28 Jun 2025 04:10:46.077 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 365178.
3132:M 28 Jun 2025 04:17:51.783 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 28 Jun 2025 04:17:51.783 * Connection with replica 127.0.0.1:7103 lost.
3132:M 28 Jun 2025 04:17:51.786 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 28 Jun 2025 04:17:51.786 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 365262.
3132:M 28 Jun 2025 04:46:45.705 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 28 Jun 2025 04:46:45.706 * Connection with replica 127.0.0.1:7103 lost.
3132:M 28 Jun 2025 04:46:45.709 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 28 Jun 2025 04:46:45.713 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 366382.
3132:M 28 Jun 2025 05:13:40.406 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 28 Jun 2025 05:13:40.529 * Connection with replica 127.0.0.1:7103 lost.
3132:M 28 Jun 2025 05:13:40.537 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 28 Jun 2025 05:13:40.537 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 367334.
3132:M 28 Jun 2025 05:38:03.806 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 28 Jun 2025 05:38:03.806 * Connection with replica 127.0.0.1:7103 lost.
3132:M 28 Jun 2025 05:38:03.824 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 28 Jun 2025 05:38:03.824 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 368034.
3132:M 28 Jun 2025 05:50:52.596 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 28 Jun 2025 05:50:52.732 * Connection with replica 127.0.0.1:7103 lost.
3132:M 28 Jun 2025 05:50:52.736 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 28 Jun 2025 05:50:52.736 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 368104.
3132:M 28 Jun 2025 06:09:14.068 * Connection with replica 127.0.0.1:7103 lost.
3132:M 28 Jun 2025 06:09:14.074 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 28 Jun 2025 06:09:14.074 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 368342.
3132:M 28 Jun 2025 06:27:28.129 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 28 Jun 2025 06:27:28.129 * Connection with replica 127.0.0.1:7103 lost.
3132:M 28 Jun 2025 06:27:28.132 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 28 Jun 2025 06:27:28.133 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 368580.
3132:M 28 Jun 2025 06:43:40.061 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 28 Jun 2025 06:43:40.186 * Connection with replica 127.0.0.1:7103 lost.
3132:M 28 Jun 2025 06:43:40.190 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 28 Jun 2025 06:43:40.190 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 368650.
3132:M 28 Jun 2025 06:51:52.646 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 28 Jun 2025 06:51:52.760 * Connection with replica 127.0.0.1:7103 lost.
3132:M 28 Jun 2025 06:51:52.763 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 28 Jun 2025 06:51:52.763 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 368706.
3132:M 28 Jun 2025 07:08:21.000 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 28 Jun 2025 07:08:21.004 * Connection with replica 127.0.0.1:7103 lost.
3132:M 28 Jun 2025 07:08:21.008 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 28 Jun 2025 07:08:21.008 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 368790.
3132:M 28 Jun 2025 07:32:40.833 * Connection with replica 127.0.0.1:7103 lost.
3132:M 28 Jun 2025 07:32:40.835 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 28 Jun 2025 07:32:40.835 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 369490.
3132:M 28 Jun 2025 07:48:52.865 * Connection with replica 127.0.0.1:7103 lost.
3132:M 28 Jun 2025 07:48:52.867 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 28 Jun 2025 07:48:52.868 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 369560.
3132:M 28 Jun 2025 08:52:53.414 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 28 Jun 2025 08:52:53.416 * Connection with replica 127.0.0.1:7103 lost.
3132:M 28 Jun 2025 08:52:53.425 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 28 Jun 2025 08:52:53.425 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 374628.
3132:M 28 Jun 2025 09:08:46.294 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 28 Jun 2025 09:08:46.295 * Connection with replica 127.0.0.1:7103 lost.
3132:M 28 Jun 2025 09:08:46.301 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 28 Jun 2025 09:08:46.302 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 374698.
3132:M 28 Jun 2025 09:24:58.252 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
3132:M 28 Jun 2025 09:24:58.252 * Connection with replica 127.0.0.1:7103 lost.
3132:M 28 Jun 2025 09:24:58.258 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 28 Jun 2025 09:24:58.259 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 374768.
3132:M 28 Jun 2025 10:42:30.605 * Connection with replica 127.0.0.1:7103 lost.
3132:M 28 Jun 2025 10:42:30.608 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 28 Jun 2025 10:42:30.608 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 379864.
3132:M 28 Jun 2025 12:28:09.533 * Connection with replica 127.0.0.1:7103 lost.
3132:M 28 Jun 2025 12:28:09.539 * Replica 127.0.0.1:7103 asks for synchronization
3132:M 28 Jun 2025 12:28:09.539 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 387452.
3132:M 28 Jun 2025 13:10:52.235 * 1 changes in 3600 seconds. Saving...
3132:M 28 Jun 2025 13:10:52.237 * Background saving started by pid 19206
19206:C 28 Jun 2025 13:10:52.248 * DB saved on disk
19206:C 28 Jun 2025 13:10:52.250 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3132:M 28 Jun 2025 13:10:52.340 * Background saving terminated with success
3132:signal-handler (1751132261) Received SIGTERM scheduling shutdown...
3132:M 29 Jun 2025 01:37:41.293 * User requested shutdown...
3132:M 29 Jun 2025 01:37:41.293 * 1 of 1 replicas are in sync when shutting down.
3132:M 29 Jun 2025 01:37:41.294 * Calling fsync() on the AOF file.
3132:M 29 Jun 2025 01:37:41.333 * Saving the final RDB snapshot before exiting.
3132:M 29 Jun 2025 01:37:41.367 * DB saved on disk
3132:M 29 Jun 2025 01:37:41.368 * Removing the pid file.
3132:M 29 Jun 2025 01:37:41.372 # Redis is now ready to exit, bye bye...
4291:C 29 Jun 2025 01:39:34.484 * oO0OoO0OoO0Oo Redis is starting oO0OoO0OoO0Oo
4291:C 29 Jun 2025 01:39:34.484 * Redis version=7.2.7, bits=64, commit=00000000, modified=0, pid=4291, just started
4291:C 29 Jun 2025 01:39:34.484 * Configuration loaded
4291:M 29 Jun 2025 01:39:34.485 * monotonic clock: POSIX clock_gettime
4291:M 29 Jun 2025 01:39:34.485 * Running mode=cluster, port=7100.
4291:M 29 Jun 2025 01:39:34.485 # WARNING: The TCP backlog setting of 511 cannot be enforced because kern.ipc.somaxconn is set to the lower value of 128.
4291:M 29 Jun 2025 01:39:34.486 * Node configuration loaded, I'm 57ad239be037fbf48439d833094adbcd814a6ec9
4291:M 29 Jun 2025 01:39:34.486 * Server initialized
4291:M 29 Jun 2025 01:39:34.486 * Reading RDB base file on AOF loading...
4291:M 29 Jun 2025 01:39:34.486 * Loading RDB produced by version 7.2.7
4291:M 29 Jun 2025 01:39:34.486 * RDB age 1681506 seconds
4291:M 29 Jun 2025 01:39:34.486 * RDB memory usage when created 1.70 Mb
4291:M 29 Jun 2025 01:39:34.486 * RDB is base AOF
4291:M 29 Jun 2025 01:39:34.486 * Done loading RDB, keys loaded: 0, keys expired: 0.
4291:M 29 Jun 2025 01:39:34.487 * DB loaded from base file appendonly-7100.aof.1.base.rdb: 0.001 seconds
4291:M 29 Jun 2025 01:39:34.497 * DB saved on disk
4291:M 29 Jun 2025 01:39:34.503 * DB saved on disk
4291:M 29 Jun 2025 01:39:34.510 * DB saved on disk
4291:M 29 Jun 2025 01:39:34.519 * DB saved on disk
4291:M 29 Jun 2025 01:39:34.524 * DB saved on disk
4291:M 29 Jun 2025 01:39:34.529 * DB saved on disk
4291:M 29 Jun 2025 01:39:34.534 * DB saved on disk
4291:M 29 Jun 2025 01:39:34.544 * DB saved on disk
4291:M 29 Jun 2025 01:39:34.550 * DB saved on disk
4291:M 29 Jun 2025 01:39:34.555 * DB saved on disk
4291:M 29 Jun 2025 01:39:34.560 * DB saved on disk
4291:M 29 Jun 2025 01:39:34.566 * DB saved on disk
4291:M 29 Jun 2025 01:39:34.572 * DB saved on disk
4291:M 29 Jun 2025 01:39:34.572 * DB loaded from incr file appendonly-7100.aof.1.incr.aof: 0.086 seconds
4291:M 29 Jun 2025 01:39:34.573 * DB loaded from append only file: 0.087 seconds
4291:M 29 Jun 2025 01:39:34.573 * Opening AOF incr file appendonly-7100.aof.1.incr.aof on server start
4291:M 29 Jun 2025 01:39:34.573 * Ready to accept connections tcp
4291:M 29 Jun 2025 01:39:36.618 * Cluster state changed: ok
4291:M 29 Jun 2025 01:39:40.651 * Replica 127.0.0.1:7103 asks for synchronization
4291:M 29 Jun 2025 01:39:40.651 * Partial resynchronization not accepted: Replication ID mismatch (Replica asked for 'a966b9c364cd7487651bc4da9617830a6bb013f0', my replication IDs are '80550764da775182146f57fd62c2b7952b88292e' and '0000000000000000000000000000000000000000')
4291:M 29 Jun 2025 01:39:40.651 * Replication backlog created, my new replication IDs are '84be8b4a5ef00e050e6eba226f1dbd8d999e1cca' and '0000000000000000000000000000000000000000'
4291:M 29 Jun 2025 01:39:40.651 * Delay next BGSAVE for diskless SYNC
4291:M 29 Jun 2025 01:39:45.715 * Starting BGSAVE for SYNC with target: replicas sockets
4291:M 29 Jun 2025 01:39:45.716 * Background RDB transfer started by pid 4421
4421:C 29 Jun 2025 01:39:45.716 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
4291:M 29 Jun 2025 01:39:45.717 * Diskless rdb transfer, done reading from pipe, 1 replicas still up.
4291:M 29 Jun 2025 01:39:45.731 * Background RDB transfer terminated with success
4291:M 29 Jun 2025 01:39:45.731 * Streamed RDB transfer with replica 127.0.0.1:7103 succeeded (socket). Waiting for REPLCONF ACK from replica to enable streaming
4291:M 29 Jun 2025 01:39:45.732 * Synchronization with replica 127.0.0.1:7103 succeeded
4291:M 29 Jun 2025 01:39:49.716 * DB saved on disk
4291:M 29 Jun 2025 01:39:49.722 * configEpoch set to 0 via CLUSTER RESET HARD
4291:M 29 Jun 2025 01:39:49.724 * Node hard reset, now I'm 6bedf35858d5471c63c98a3b8e1f002efc12f60b
4291:M 29 Jun 2025 01:39:49.724 # Cluster state changed: fail
4291:M 29 Jun 2025 01:39:49.849 * Connection with replica 127.0.0.1:7103 lost.
4291:M 29 Jun 2025 01:39:52.938 * configEpoch set to 1 via CLUSTER SET-CONFIG-EPOCH
4291:M 29 Jun 2025 01:39:54.966 * Replica 127.0.0.1:7105 asks for synchronization
4291:M 29 Jun 2025 01:39:54.966 * Partial resynchronization not accepted: Replication ID mismatch (Replica asked for '4808b4fd64521f801269352974e04e44de3352cf', my replication IDs are '84be8b4a5ef00e050e6eba226f1dbd8d999e1cca' and '0000000000000000000000000000000000000000')
4291:M 29 Jun 2025 01:39:54.967 * Delay next BGSAVE for diskless SYNC
4291:M 29 Jun 2025 01:39:57.966 * Cluster state changed: ok
4291:M 29 Jun 2025 01:39:59.887 * Starting BGSAVE for SYNC with target: replicas sockets
4291:M 29 Jun 2025 01:39:59.888 * Background RDB transfer started by pid 4568
4568:C 29 Jun 2025 01:39:59.888 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
4291:M 29 Jun 2025 01:39:59.888 * Diskless rdb transfer, done reading from pipe, 1 replicas still up.
4291:M 29 Jun 2025 01:39:59.899 * Background RDB transfer terminated with success
4291:M 29 Jun 2025 01:39:59.899 * Streamed RDB transfer with replica 127.0.0.1:7105 succeeded (socket). Waiting for REPLCONF ACK from replica to enable streaming
4291:M 29 Jun 2025 01:39:59.899 * Synchronization with replica 127.0.0.1:7105 succeeded
