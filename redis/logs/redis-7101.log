20732:C 09 Jun 2025 23:30:39.019 * oO0OoO0OoO0Oo Redis is starting oO0OoO0OoO0Oo
20732:C 09 Jun 2025 23:30:39.020 * Redis version=7.2.7, bits=64, commit=00000000, modified=0, pid=20732, just started
20732:C 09 Jun 2025 23:30:39.020 * Configuration loaded
20732:M 09 Jun 2025 23:30:39.021 * Increased maximum number of open files to 10032 (it was originally set to 256).
20732:M 09 Jun 2025 23:30:39.021 * monotonic clock: POSIX clock_gettime
20732:M 09 Jun 2025 23:30:39.021 * Running mode=cluster, port=7101.
20732:M 09 Jun 2025 23:30:39.022 # WARNING: The TCP backlog setting of 511 cannot be enforced because kern.ipc.somaxconn is set to the lower value of 128.
20732:M 09 Jun 2025 23:30:39.022 * Node configuration loaded, I'm 2c1de2bafab21c05652419e216d2370070c154c4
20732:M 09 Jun 2025 23:30:39.022 * Server initialized
20732:M 09 Jun 2025 23:30:39.022 * Reading RDB base file on AOF loading...
20732:M 09 Jun 2025 23:30:39.022 * Loading RDB produced by version 7.2.7
20732:M 09 Jun 2025 23:30:39.022 * RDB age 32169 seconds
20732:M 09 Jun 2025 23:30:39.022 * RDB memory usage when created 1.70 Mb
20732:M 09 Jun 2025 23:30:39.023 * RDB is base AOF
20732:M 09 Jun 2025 23:30:39.023 * Done loading RDB, keys loaded: 0, keys expired: 0.
20732:M 09 Jun 2025 23:30:39.023 * DB loaded from base file appendonly-7101.aof.1.base.rdb: 0.001 seconds
20732:M 09 Jun 2025 23:30:39.031 * DB saved on disk
20732:M 09 Jun 2025 23:30:39.037 * DB saved on disk
20732:M 09 Jun 2025 23:30:39.042 * DB saved on disk
20732:M 09 Jun 2025 23:30:39.047 * DB saved on disk
20732:M 09 Jun 2025 23:30:39.056 * DB saved on disk
20732:M 09 Jun 2025 23:30:39.061 * DB saved on disk
20732:M 09 Jun 2025 23:30:39.067 * DB saved on disk
20732:M 09 Jun 2025 23:30:39.071 * DB saved on disk
20732:M 09 Jun 2025 23:30:39.076 * DB saved on disk
20732:M 09 Jun 2025 23:30:39.081 * DB saved on disk
20732:M 09 Jun 2025 23:30:39.081 * DB loaded from incr file appendonly-7101.aof.1.incr.aof: 0.059 seconds
20732:M 09 Jun 2025 23:30:39.082 * DB loaded from append only file: 0.059 seconds
20732:M 09 Jun 2025 23:30:39.082 * Opening AOF incr file appendonly-7101.aof.1.incr.aof on server start
20732:M 09 Jun 2025 23:30:39.082 * Ready to accept connections tcp
20732:M 09 Jun 2025 23:30:52.245 * DB saved on disk
20732:M 09 Jun 2025 23:30:52.249 * configEpoch set to 0 via CLUSTER RESET HARD
20732:M 09 Jun 2025 23:30:52.250 * Node hard reset, now I'm 23443f72260886460cb1475efe4af4ce9d98a150
20732:M 09 Jun 2025 23:30:55.418 * configEpoch set to 2 via CLUSTER SET-CONFIG-EPOCH
20732:M 09 Jun 2025 23:30:57.443 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 09 Jun 2025 23:30:57.443 * Partial resynchronization not accepted: Replication ID mismatch (Replica asked for 'f09f246676fa33efb2c3b0c38ccf9f7b653f0515', my replication IDs are 'c120cf93487063c06fc2825cda0957ed58c77931' and '0000000000000000000000000000000000000000')
20732:M 09 Jun 2025 23:30:57.444 * Replication backlog created, my new replication IDs are '10cea4136b75b07e0f2027a161fb7cb482e4e9cb' and '0000000000000000000000000000000000000000'
20732:M 09 Jun 2025 23:30:57.444 * Delay next BGSAVE for diskless SYNC
20732:M 09 Jun 2025 23:31:00.473 * Cluster state changed: ok
20732:M 09 Jun 2025 23:31:02.296 * Starting BGSAVE for SYNC with target: replicas sockets
20732:M 09 Jun 2025 23:31:02.299 * Background RDB transfer started by pid 20935
20935:C 09 Jun 2025 23:31:02.299 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20732:M 09 Jun 2025 23:31:02.299 * Diskless rdb transfer, done reading from pipe, 1 replicas still up.
20732:M 09 Jun 2025 23:31:02.332 * Background RDB transfer terminated with success
20732:M 09 Jun 2025 23:31:02.332 * Streamed RDB transfer with replica 127.0.0.1:7104 succeeded (socket). Waiting for REPLCONF ACK from replica to enable streaming
20732:M 09 Jun 2025 23:31:02.333 * Synchronization with replica 127.0.0.1:7104 succeeded
20732:M 10 Jun 2025 01:21:58.604 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 01:21:58.608 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 01:21:58.609 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 7940.
20732:M 10 Jun 2025 01:38:11.259 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 01:38:11.260 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 01:38:11.268 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 01:38:11.269 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 7996.
20732:M 10 Jun 2025 01:54:24.069 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 01:54:24.070 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 01:54:24.072 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 01:54:24.072 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 8066.
20732:M 10 Jun 2025 02:10:33.920 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 02:10:33.920 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 02:10:33.923 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 02:10:33.924 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 8122.
20732:M 10 Jun 2025 02:26:22.067 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 02:26:22.068 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 02:26:22.074 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 02:26:22.075 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 8192.
20732:M 10 Jun 2025 02:42:17.339 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 02:42:17.461 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 02:42:17.479 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 02:42:17.479 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 8248.
20732:M 10 Jun 2025 02:58:34.250 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 02:58:34.250 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 02:58:34.252 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 02:58:34.252 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 8318.
20732:M 10 Jun 2025 03:07:26.661 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 03:07:26.662 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 03:07:26.670 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 03:07:26.671 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 8374.
20732:M 10 Jun 2025 03:23:39.293 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 03:23:39.293 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 03:23:39.299 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 03:23:39.299 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 8444.
20732:M 10 Jun 2025 03:39:48.413 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 03:39:48.447 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 03:39:48.472 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 03:39:48.472 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 8514.
20732:M 10 Jun 2025 03:56:02.215 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 03:56:02.216 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 03:56:02.220 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 03:56:02.221 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 8570.
20732:M 10 Jun 2025 04:12:14.802 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 04:12:14.802 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 04:12:14.806 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 04:12:14.806 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 8640.
20732:M 10 Jun 2025 04:28:27.574 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 04:28:27.574 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 04:28:27.577 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 04:28:27.577 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 8696.
20732:M 10 Jun 2025 04:44:40.404 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 04:44:40.405 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 04:44:40.409 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 04:44:40.409 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 8766.
20732:M 10 Jun 2025 05:00:54.124 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 05:00:54.125 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 05:00:54.144 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 05:00:54.144 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 8836.
20732:M 10 Jun 2025 05:17:06.924 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 05:17:06.925 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 05:17:06.929 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 05:17:06.930 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 8892.
20732:M 10 Jun 2025 05:33:19.649 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 05:33:19.650 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 05:33:19.652 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 05:33:19.653 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 8962.
20732:M 10 Jun 2025 05:49:32.328 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 05:49:32.329 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 05:49:32.331 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 05:49:32.332 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 9032.
20732:M 10 Jun 2025 06:05:44.931 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 06:05:44.948 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 06:05:44.952 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 06:05:44.952 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 9088.
20732:M 10 Jun 2025 06:21:30.446 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 06:21:30.463 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 06:21:30.480 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 06:21:30.480 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 9158.
20732:M 10 Jun 2025 06:37:41.188 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 06:37:41.189 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 06:37:41.205 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 06:37:41.206 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 9214.
20732:M 10 Jun 2025 06:53:27.817 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 06:53:27.817 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 06:53:27.821 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 06:53:27.821 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 9284.
20732:M 10 Jun 2025 06:59:15.678 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 06:59:15.678 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 06:59:15.681 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 06:59:15.682 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 9340.
20732:M 10 Jun 2025 07:15:02.581 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 07:15:02.581 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 07:15:02.588 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 07:15:02.588 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 9410.
20732:M 10 Jun 2025 07:31:11.120 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 07:31:11.121 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 07:31:11.130 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 07:31:11.131 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 9466.
20732:M 10 Jun 2025 07:35:19.795 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 07:35:19.795 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 07:35:19.801 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 07:35:19.801 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 9536.
20732:M 10 Jun 2025 07:51:04.694 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 07:51:04.694 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 07:51:04.701 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 07:51:04.702 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 9592.
20732:M 10 Jun 2025 08:07:17.299 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 08:07:17.299 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 08:07:17.303 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 08:07:17.303 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 9662.
20732:M 10 Jun 2025 08:23:29.169 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 08:23:29.183 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 08:23:29.214 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 08:23:29.214 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 9732.
20732:M 10 Jun 2025 08:39:41.990 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 08:39:42.033 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 08:39:42.054 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 08:39:42.054 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 9788.
20732:M 10 Jun 2025 08:47:24.594 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 08:47:24.629 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 08:47:24.648 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 08:47:24.648 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 9858.
20732:M 10 Jun 2025 09:00:15.608 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 09:00:15.608 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 09:00:15.612 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 09:00:15.612 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 9914.
20732:M 10 Jun 2025 09:19:24.991 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 09:19:24.992 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 09:19:24.995 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 09:19:24.996 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 10110.
20732:M 10 Jun 2025 09:36:43.880 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 09:36:43.880 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 09:36:43.884 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 09:36:43.884 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 10124.
20732:M 10 Jun 2025 09:50:32.953 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 09:50:32.956 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 09:50:32.956 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 10124.
20732:M 10 Jun 2025 10:19:54.437 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 10:19:54.439 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 10:19:54.440 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 12014.
20732:M 10 Jun 2025 13:02:22.603 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 13:02:22.604 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 13:02:22.604 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 25258.
20732:M 10 Jun 2025 13:04:39.779 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 13:04:39.780 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 13:04:39.780 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 25314.
20732:M 10 Jun 2025 13:08:41.744 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 13:08:41.746 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 13:08:41.746 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 25384.
20732:M 10 Jun 2025 13:24:52.408 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 13:24:52.410 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 13:24:52.410 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 25454.
20732:M 10 Jun 2025 18:47:06.967 * 1 changes in 3600 seconds. Saving...
20732:M 10 Jun 2025 18:47:06.972 * Background saving started by pid 89341
89341:C 10 Jun 2025 18:47:06.995 * DB saved on disk
89341:C 10 Jun 2025 18:47:06.995 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20732:M 10 Jun 2025 18:47:07.075 * Background saving terminated with success
20732:M 10 Jun 2025 21:29:00.812 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 21:29:00.816 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 21:29:00.817 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 65604.
20732:M 10 Jun 2025 21:47:27.448 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 21:47:27.463 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 21:47:27.463 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 65674.
20732:M 10 Jun 2025 22:04:13.407 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 22:04:13.431 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 22:04:13.438 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 22:04:13.439 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 65674.
20732:M 10 Jun 2025 22:21:29.301 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 22:21:29.420 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 22:21:29.423 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 22:21:29.423 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 65688.
20732:M 10 Jun 2025 22:36:31.729 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 22:36:31.848 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 22:36:31.851 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 22:36:31.851 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 65688.
20732:M 10 Jun 2025 22:52:26.692 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 10 Jun 2025 22:52:26.801 * Connection with replica 127.0.0.1:7104 lost.
20732:M 10 Jun 2025 22:52:26.803 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 10 Jun 2025 22:52:26.804 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 65758.
20732:M 11 Jun 2025 00:52:03.125 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 00:52:03.126 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 00:52:03.138 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 00:52:03.139 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 74228.
20732:M 11 Jun 2025 01:08:10.758 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 01:08:10.758 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 01:08:10.762 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 01:08:10.762 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 74284.
20732:M 11 Jun 2025 01:24:23.245 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 01:24:23.245 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 01:24:23.248 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 01:24:23.248 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 74354.
20732:M 11 Jun 2025 01:40:35.970 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 01:40:35.971 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 01:40:35.974 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 01:40:35.974 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 74424.
20732:M 11 Jun 2025 01:56:49.364 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 01:56:49.494 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 01:56:49.498 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 01:56:49.499 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 74480.
20732:M 11 Jun 2025 02:13:02.192 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 02:13:02.197 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 02:13:02.215 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 02:13:02.215 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 74550.
20732:M 11 Jun 2025 02:29:14.726 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 02:29:14.868 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 02:29:14.874 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 02:29:14.875 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 74606.
20732:M 11 Jun 2025 02:35:35.133 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 02:35:35.248 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 02:35:35.251 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 02:35:35.251 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 74676.
20732:M 11 Jun 2025 02:51:47.973 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 02:51:48.088 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 02:51:48.097 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 02:51:48.097 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 74746.
20732:M 11 Jun 2025 03:08:00.853 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 03:08:00.972 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 03:08:00.976 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 03:08:00.976 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 74802.
20732:M 11 Jun 2025 03:24:13.872 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 03:24:13.874 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 03:24:13.877 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 03:24:13.877 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 74872.
20732:M 11 Jun 2025 03:40:25.717 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 03:40:25.857 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 03:40:25.861 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 03:40:25.861 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 74928.
20732:M 11 Jun 2025 03:56:19.352 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 03:56:19.492 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 03:56:19.497 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 03:56:19.497 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 74998.
20732:M 11 Jun 2025 04:12:29.102 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 04:12:29.102 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 04:12:29.106 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 04:12:29.106 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 75054.
20732:M 11 Jun 2025 04:28:41.120 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 04:28:41.121 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 04:28:41.124 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 04:28:41.124 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 75124.
20732:M 11 Jun 2025 04:44:53.812 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 04:44:53.937 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 04:44:53.946 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 04:44:53.946 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 75194.
20732:M 11 Jun 2025 05:01:06.643 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 05:01:06.781 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 05:01:06.784 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 05:01:06.785 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 75250.
20732:M 11 Jun 2025 05:17:18.732 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 05:17:18.753 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 05:17:18.772 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 05:17:18.772 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 75320.
20732:M 11 Jun 2025 05:33:31.417 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 05:33:31.560 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 05:33:31.564 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 05:33:31.564 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 75376.
20732:M 11 Jun 2025 05:49:43.405 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 05:49:43.526 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 05:49:43.529 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 05:49:43.529 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 75446.
20732:M 11 Jun 2025 06:05:56.204 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 06:05:56.338 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 06:05:56.341 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 06:05:56.342 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 75516.
20732:M 11 Jun 2025 06:22:09.017 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 06:22:09.017 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 06:22:09.020 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 06:22:09.020 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 75572.
20732:M 11 Jun 2025 06:31:42.103 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 06:31:42.106 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 06:31:42.106 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 75642.
20732:M 11 Jun 2025 06:47:44.969 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 06:47:44.970 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 06:47:44.979 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 06:47:44.980 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 75698.
20732:M 11 Jun 2025 07:03:56.891 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 07:03:56.891 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 07:03:56.895 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 07:03:56.895 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 75768.
20732:M 11 Jun 2025 07:19:48.603 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 07:19:48.603 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 07:19:48.607 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 07:19:48.607 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 75838.
20732:M 11 Jun 2025 07:36:02.332 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 07:36:02.336 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 07:36:02.336 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 75894.
20732:M 11 Jun 2025 07:52:14.956 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 07:52:14.956 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 07:52:14.959 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 07:52:14.959 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 75964.
20732:M 11 Jun 2025 08:08:27.644 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 08:08:27.644 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 08:08:27.646 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 08:08:27.646 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 76034.
20732:M 11 Jun 2025 08:14:22.457 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 08:14:22.457 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 08:14:22.460 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 08:14:22.461 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 76090.
20732:M 11 Jun 2025 08:30:34.300 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 08:30:34.329 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 08:30:34.332 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 08:30:34.332 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 76160.
20732:M 11 Jun 2025 08:32:41.408 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 08:32:41.409 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 08:32:41.411 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 08:32:41.412 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 76216.
20732:M 11 Jun 2025 08:36:30.685 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 08:36:30.809 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 08:36:30.811 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 08:36:30.811 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 76286.
20732:M 11 Jun 2025 08:52:45.474 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 08:52:45.590 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 08:52:45.594 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 08:52:45.594 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 76356.
20732:M 11 Jun 2025 09:05:22.551 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 09:05:22.682 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 09:05:22.685 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 09:05:22.685 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 76412.
20732:M 11 Jun 2025 09:23:32.450 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 09:23:32.485 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 09:23:32.505 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 09:23:32.505 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 76482.
20732:M 11 Jun 2025 09:33:41.468 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 09:33:41.485 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 09:33:41.496 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 09:33:41.496 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 76482.
20732:M 11 Jun 2025 09:52:07.495 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 09:52:07.501 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 09:52:07.510 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 09:52:07.511 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 76552.
20732:M 11 Jun 2025 10:06:47.343 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 10:06:47.344 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 10:06:47.361 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 10:06:47.361 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 76986.
20732:M 11 Jun 2025 11:06:58.899 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 11:06:58.899 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 11:06:58.906 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 11:06:58.906 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 81872.
20732:M 11 Jun 2025 11:10:12.014 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 11:10:12.016 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 11:10:12.016 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 81928.
20732:M 11 Jun 2025 13:12:23.149 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 13:12:23.151 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 13:12:23.151 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 91364.
20732:M 11 Jun 2025 13:29:45.395 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 13:29:45.406 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 13:29:45.406 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 91420.
20732:M 11 Jun 2025 13:46:06.454 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 13:46:06.455 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 13:46:06.455 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 91490.
20732:M 11 Jun 2025 14:01:53.566 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 14:01:53.569 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 14:01:53.569 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 91546.
20732:M 11 Jun 2025 14:33:47.591 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 14:33:47.594 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 14:33:47.594 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 93702.
20732:M 11 Jun 2025 15:04:34.694 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 15:04:34.696 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 15:04:34.697 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 95830.
20732:M 11 Jun 2025 15:51:10.926 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 15:51:10.926 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 15:51:10.929 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 15:51:10.929 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 98196.
20732:M 11 Jun 2025 15:59:57.782 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 15:59:57.782 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 15:59:57.798 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 15:59:57.798 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 98322.
20732:M 11 Jun 2025 16:04:03.036 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 16:04:03.037 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 16:04:03.041 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 16:04:03.041 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 98336.
20732:M 11 Jun 2025 16:13:05.318 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 16:13:05.318 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 16:13:05.323 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 16:13:05.323 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 98406.
20732:M 11 Jun 2025 16:16:19.208 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 16:16:19.222 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 16:16:19.243 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 16:16:19.243 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 98448.
20732:M 11 Jun 2025 16:27:22.405 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 16:27:22.405 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 16:27:22.406 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 16:27:22.407 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 98518.
20732:M 11 Jun 2025 16:48:10.304 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 16:48:10.306 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 16:48:10.306 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 98952.
20732:M 11 Jun 2025 17:04:02.299 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 17:04:02.300 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 17:04:02.300 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 98966.
20732:M 11 Jun 2025 17:05:13.602 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 17:05:13.603 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 17:05:13.604 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 98966.
20732:M 11 Jun 2025 17:43:59.205 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 17:43:59.210 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 17:43:59.222 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 17:43:59.222 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 101010.
20732:M 11 Jun 2025 17:46:00.628 * 1 changes in 3600 seconds. Saving...
20732:M 11 Jun 2025 17:46:00.629 * Background saving started by pid 46184
46184:C 11 Jun 2025 17:46:00.635 * DB saved on disk
46184:C 11 Jun 2025 17:46:00.635 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20732:M 11 Jun 2025 17:46:00.731 * Background saving terminated with success
20732:M 11 Jun 2025 18:46:01.053 * 1 changes in 3600 seconds. Saving...
20732:M 11 Jun 2025 18:46:01.056 * Background saving started by pid 69519
69519:C 11 Jun 2025 18:46:01.064 * DB saved on disk
69519:C 11 Jun 2025 18:46:01.066 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20732:M 11 Jun 2025 18:46:01.157 * Background saving terminated with success
20732:M 11 Jun 2025 19:46:53.519 * 1 changes in 3600 seconds. Saving...
20732:M 11 Jun 2025 19:46:53.520 * Background saving started by pid 4869
4869:C 11 Jun 2025 19:46:53.528 * DB saved on disk
4869:C 11 Jun 2025 19:46:53.529 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20732:M 11 Jun 2025 19:46:53.622 * Background saving terminated with success
20732:M 11 Jun 2025 20:46:54.059 * 1 changes in 3600 seconds. Saving...
20732:M 11 Jun 2025 20:46:54.062 * Background saving started by pid 42506
42506:C 11 Jun 2025 20:46:54.073 * DB saved on disk
42506:C 11 Jun 2025 20:46:54.073 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20732:M 11 Jun 2025 20:46:54.165 * Background saving terminated with success
20732:M 11 Jun 2025 22:14:51.373 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 22:14:51.374 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 22:14:51.389 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 22:14:51.391 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 135515.
20732:M 11 Jun 2025 22:31:53.501 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 22:31:53.514 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 22:31:53.528 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 22:31:53.528 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 135585.
20732:M 11 Jun 2025 22:47:38.496 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 22:47:38.532 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 22:47:38.545 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 22:47:38.550 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 135585.
20732:M 11 Jun 2025 23:03:11.512 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 23:03:11.543 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 23:03:11.554 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 23:03:11.554 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 135599.
20732:M 11 Jun 2025 23:19:19.523 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 23:19:19.553 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 23:19:19.573 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 23:19:19.573 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 135599.
20732:M 11 Jun 2025 23:26:40.063 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 11 Jun 2025 23:26:40.083 * Connection with replica 127.0.0.1:7104 lost.
20732:M 11 Jun 2025 23:26:40.100 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 11 Jun 2025 23:26:40.100 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 135613.
20732:M 11 Jun 2025 23:37:59.714 * 1 changes in 3600 seconds. Saving...
20732:M 11 Jun 2025 23:37:59.715 * Background saving started by pid 82007
82007:C 11 Jun 2025 23:37:59.723 * DB saved on disk
82007:C 11 Jun 2025 23:37:59.724 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20732:M 11 Jun 2025 23:37:59.816 * Background saving terminated with success
20732:M 12 Jun 2025 00:15:33.319 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 00:15:33.327 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 00:15:33.327 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 140349.
20732:M 12 Jun 2025 00:38:00.083 * 1 changes in 3600 seconds. Saving...
20732:M 12 Jun 2025 00:38:00.086 * Background saving started by pid 8850
8850:C 12 Jun 2025 00:38:00.101 * DB saved on disk
8850:C 12 Jun 2025 00:38:00.102 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20732:M 12 Jun 2025 00:38:00.187 * Background saving terminated with success
20732:M 12 Jun 2025 01:38:01.099 * 1 changes in 3600 seconds. Saving...
20732:M 12 Jun 2025 01:38:01.101 * Background saving started by pid 33328
33328:C 12 Jun 2025 01:38:01.113 * DB saved on disk
33328:C 12 Jun 2025 01:38:01.118 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20732:M 12 Jun 2025 01:38:01.204 * Background saving terminated with success
20732:M 12 Jun 2025 03:02:38.732 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 03:02:38.732 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 03:02:38.736 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 03:02:38.736 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 153467.
20732:M 12 Jun 2025 03:18:47.136 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 03:18:47.140 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 03:18:47.141 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 153537.
20732:M 12 Jun 2025 03:34:59.886 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 03:34:59.889 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 03:34:59.889 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 153593.
20732:M 12 Jun 2025 03:51:12.565 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 03:51:12.567 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 03:51:12.567 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 153663.
20732:M 12 Jun 2025 04:07:25.162 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 04:07:25.162 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 04:07:25.162 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 153719.
20732:M 12 Jun 2025 04:23:37.886 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 04:23:37.889 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 04:23:37.889 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 153789.
20732:M 12 Jun 2025 04:39:50.149 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 04:39:50.158 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 04:39:50.170 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 04:39:50.170 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 153859.
20732:M 12 Jun 2025 04:44:35.405 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 04:44:35.541 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 04:44:35.543 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 04:44:35.543 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 153915.
20732:M 12 Jun 2025 05:00:48.273 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 05:00:48.285 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 05:00:48.305 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 05:00:48.305 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 153985.
20732:M 12 Jun 2025 05:17:00.906 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 05:17:01.051 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 05:17:01.053 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 05:17:01.053 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 154041.
20732:M 12 Jun 2025 05:33:13.716 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 05:33:13.833 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 05:33:13.836 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 05:33:13.836 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 154111.
20732:M 12 Jun 2025 05:45:35.314 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 05:45:35.450 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 05:45:35.453 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 05:45:35.453 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 154181.
20732:M 12 Jun 2025 06:01:49.213 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 06:01:49.213 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 06:01:49.219 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 06:01:49.219 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 154237.
20732:M 12 Jun 2025 06:17:37.039 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 06:17:37.040 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 06:17:37.043 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 06:17:37.044 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 154307.
20732:M 12 Jun 2025 06:33:44.595 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 06:33:44.733 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 06:33:44.735 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 06:33:44.735 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 154363.
20732:M 12 Jun 2025 06:46:35.420 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 06:46:35.440 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 06:46:35.442 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 06:46:35.443 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 154433.
20732:M 12 Jun 2025 07:02:48.018 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 07:02:48.140 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 07:02:48.143 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 07:02:48.143 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 154489.
20732:M 12 Jun 2025 07:19:00.871 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 07:19:00.984 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 07:19:00.988 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 07:19:00.988 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 154559.
20732:M 12 Jun 2025 07:35:14.607 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 07:35:14.607 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 07:35:14.613 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 07:35:14.614 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 154615.
20732:M 12 Jun 2025 07:47:36.175 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 07:47:36.175 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 07:47:36.183 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 07:47:36.183 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 154685.
20732:M 12 Jun 2025 08:03:48.775 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 08:03:48.775 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 08:03:48.782 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 08:03:48.783 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 154755.
20732:M 12 Jun 2025 08:20:01.374 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 08:20:01.374 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 08:20:01.381 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 08:20:01.381 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 154811.
20732:M 12 Jun 2025 08:36:15.160 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 08:36:15.162 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 08:36:15.162 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 154881.
20732:M 12 Jun 2025 08:48:35.984 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 08:48:35.985 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 08:48:35.989 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 08:48:35.989 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 154951.
20732:M 12 Jun 2025 08:53:24.653 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 08:53:24.653 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 08:53:24.656 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 08:53:24.656 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 155007.
20732:M 12 Jun 2025 08:55:09.392 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 08:55:09.394 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 08:55:09.395 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 155077.
20732:M 12 Jun 2025 09:00:52.257 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 09:00:52.271 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 09:00:52.274 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 09:00:52.274 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 155161.
20732:M 12 Jun 2025 09:17:08.475 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 09:17:08.486 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 09:17:08.493 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 09:17:08.493 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 155217.
20732:M 12 Jun 2025 09:34:27.477 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 09:34:27.497 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 09:34:27.503 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 09:34:27.503 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 155231.
20732:M 12 Jun 2025 09:50:02.379 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 09:50:02.496 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 09:50:02.498 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 09:50:02.499 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 155231.
20732:M 12 Jun 2025 09:55:37.270 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 09:55:37.389 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 09:55:37.391 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 09:55:37.391 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 155245.
20732:M 12 Jun 2025 10:15:16.507 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 10:15:16.513 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 10:15:16.514 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 156463.
20732:M 12 Jun 2025 10:39:58.492 * 1 changes in 3600 seconds. Saving...
20732:M 12 Jun 2025 10:39:58.494 * Background saving started by pid 92313
92313:C 12 Jun 2025 10:39:58.518 * DB saved on disk
92313:C 12 Jun 2025 10:39:58.518 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20732:M 12 Jun 2025 10:39:58.596 * Background saving terminated with success
20732:M 12 Jun 2025 11:30:02.496 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 11:30:02.497 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 11:30:02.497 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 163538.
20732:M 12 Jun 2025 11:36:08.414 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 11:36:08.435 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 11:36:08.435 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 163608.
20732:M 12 Jun 2025 11:44:28.328 * 1 changes in 3600 seconds. Saving...
20732:M 12 Jun 2025 11:44:28.467 * Background saving started by pid 16364
16364:C 12 Jun 2025 11:44:28.478 * DB saved on disk
16364:C 12 Jun 2025 11:44:28.478 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20732:M 12 Jun 2025 11:44:28.569 * Background saving terminated with success
20732:M 12 Jun 2025 11:44:28.570 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 11:44:28.570 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 11:44:28.571 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 11:44:28.571 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 163678.
20732:M 12 Jun 2025 13:00:17.371 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 13:00:17.373 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 13:00:17.373 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 168648.
20732:M 12 Jun 2025 13:17:09.440 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 13:17:09.443 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 13:17:09.444 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 168732.
20732:M 12 Jun 2025 13:34:30.429 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 13:34:30.431 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 13:34:30.431 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 168802.
20732:M 12 Jun 2025 13:45:52.077 * Connection with replica client id #10019 lost.
20732:M 12 Jun 2025 13:45:52.086 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 13:45:52.086 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 41 bytes of backlog starting from offset 168858.
20732:M 12 Jun 2025 14:02:20.202 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 14:02:20.205 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 14:02:20.205 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 168969.
20732:M 12 Jun 2025 14:07:05.353 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 14:07:05.489 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 14:07:05.491 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 14:07:05.491 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 169053.
20732:M 12 Jun 2025 15:13:52.583 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 15:13:52.583 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 15:13:52.587 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 15:13:52.587 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 174415.
20732:M 12 Jun 2025 15:16:20.126 * 1 changes in 3600 seconds. Saving...
20732:M 12 Jun 2025 15:16:20.127 * Background saving started by pid 7231
7231:C 12 Jun 2025 15:16:20.133 * DB saved on disk
7231:C 12 Jun 2025 15:16:20.135 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20732:M 12 Jun 2025 15:16:20.228 * Background saving terminated with success
20732:M 12 Jun 2025 16:16:21.003 * 1 changes in 3600 seconds. Saving...
20732:M 12 Jun 2025 16:16:21.004 * Background saving started by pid 47123
47123:C 12 Jun 2025 16:16:21.013 * DB saved on disk
47123:C 12 Jun 2025 16:16:21.014 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20732:M 12 Jun 2025 16:16:21.106 * Background saving terminated with success
20732:M 12 Jun 2025 18:46:38.647 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 18:46:38.650 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 18:46:38.650 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 192955.
20732:M 12 Jun 2025 19:02:37.992 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 19:02:37.996 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 19:02:37.996 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 193025.
20732:M 12 Jun 2025 19:17:52.888 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 19:17:52.890 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 19:17:52.891 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 193025.
20732:M 12 Jun 2025 19:35:47.786 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 19:35:47.788 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 19:35:47.789 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 193039.
20732:M 12 Jun 2025 19:52:39.660 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 19:52:39.662 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 19:52:39.662 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 193039.
20732:M 12 Jun 2025 20:09:46.596 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 20:09:46.599 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 20:09:46.600 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 193053.
20732:M 12 Jun 2025 20:24:52.562 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 20:24:52.565 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 20:24:52.565 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 193053.
20732:M 12 Jun 2025 20:42:32.480 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 20:42:32.481 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 20:42:32.481 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 193067.
20732:M 12 Jun 2025 20:46:27.375 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 20:46:27.377 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 20:46:27.377 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 193067.
20732:M 12 Jun 2025 21:02:50.436 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 21:02:50.436 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 21:02:50.443 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 21:02:50.444 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 193137.
20732:M 12 Jun 2025 21:18:07.330 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 21:18:07.363 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 21:18:07.384 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 21:18:07.385 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 193137.
20732:M 12 Jun 2025 21:36:01.324 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 21:36:01.341 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 21:36:01.350 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 21:36:01.350 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 193151.
20732:M 12 Jun 2025 21:47:27.210 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 21:47:27.351 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 21:47:27.357 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 21:47:27.357 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 193151.
20732:M 12 Jun 2025 22:04:12.320 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 22:04:12.338 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 22:04:12.356 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 22:04:12.356 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 193221.
20732:M 12 Jun 2025 22:21:48.305 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 22:21:48.336 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 22:21:48.338 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 22:21:48.338 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 193221.
20732:M 12 Jun 2025 22:40:39.422 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 12 Jun 2025 22:40:39.423 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 22:40:39.434 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 22:40:39.434 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 193319.
20732:M 12 Jun 2025 22:41:42.182 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 22:41:42.194 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 22:41:42.196 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 193319.
20732:M 12 Jun 2025 22:42:32.214 * 1 changes in 3600 seconds. Saving...
20732:M 12 Jun 2025 22:42:32.215 * Background saving started by pid 51015
51015:C 12 Jun 2025 22:42:32.221 * DB saved on disk
51015:C 12 Jun 2025 22:42:32.224 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20732:M 12 Jun 2025 22:42:32.317 * Background saving terminated with success
20732:M 12 Jun 2025 23:03:26.513 * Connection with replica 127.0.0.1:7104 lost.
20732:M 12 Jun 2025 23:03:26.515 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 12 Jun 2025 23:03:26.515 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 196671.
20732:M 12 Jun 2025 23:42:33.082 * 1 changes in 3600 seconds. Saving...
20732:M 12 Jun 2025 23:42:33.083 * Background saving started by pid 91954
91954:C 12 Jun 2025 23:42:33.098 * DB saved on disk
91954:C 12 Jun 2025 23:42:33.106 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
20732:M 12 Jun 2025 23:42:33.185 * Background saving terminated with success
20732:M 13 Jun 2025 01:36:30.393 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 01:36:30.394 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 01:36:30.397 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 01:36:30.397 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208059.
20732:M 13 Jun 2025 01:54:24.297 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 01:54:24.297 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 01:54:24.305 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 01:54:24.306 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208114.
20732:M 13 Jun 2025 02:02:31.087 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 02:02:31.087 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 02:02:31.088 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 02:02:31.088 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 208114.
20732:M 13 Jun 2025 02:18:30.463 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 02:18:30.497 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 02:18:30.502 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 02:18:30.502 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208184.
20732:M 13 Jun 2025 02:34:59.521 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 02:34:59.523 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 02:34:59.523 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208198.
20732:M 13 Jun 2025 02:50:58.163 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 02:50:58.163 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 02:50:58.172 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 02:50:58.173 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208198.
20732:M 13 Jun 2025 03:08:20.043 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 03:08:20.043 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 03:08:20.050 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 03:08:20.050 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208212.
20732:M 13 Jun 2025 03:22:03.855 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 03:22:03.855 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 03:22:03.858 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 03:22:03.858 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208212.
20732:M 13 Jun 2025 03:39:49.803 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 03:39:49.803 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 03:39:49.807 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 03:39:49.807 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208282.
20732:M 13 Jun 2025 03:57:18.627 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 03:57:18.628 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 03:57:18.636 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 03:57:18.636 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208282.
20732:M 13 Jun 2025 04:02:24.864 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 04:02:24.865 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 04:02:24.865 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208296.
20732:M 13 Jun 2025 04:20:17.657 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 04:20:17.659 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 04:20:17.660 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208352.
20732:M 13 Jun 2025 04:37:48.708 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 04:37:48.708 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 04:37:48.712 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 04:37:48.712 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208436.
20732:M 13 Jun 2025 04:55:27.559 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 04:55:27.559 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 04:55:27.562 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 04:55:27.563 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208436.
20732:M 13 Jun 2025 05:02:24.642 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 05:02:24.642 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 05:02:24.656 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 05:02:24.656 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208450.
20732:M 13 Jun 2025 05:21:04.462 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 05:21:04.462 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 05:21:04.465 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 05:21:04.465 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208576.
20732:M 13 Jun 2025 05:37:20.253 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 05:37:20.276 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 05:37:20.278 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 05:37:20.278 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 208576.
20732:M 13 Jun 2025 05:53:04.267 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 05:53:04.268 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 05:53:04.268 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208590.
20732:M 13 Jun 2025 06:02:41.620 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 06:02:41.620 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 06:02:41.623 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 06:02:41.624 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208604.
20732:M 13 Jun 2025 06:20:21.849 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 06:20:21.849 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 06:20:21.858 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 06:20:21.858 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208660.
20732:M 13 Jun 2025 06:36:05.641 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 06:36:05.641 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 06:36:05.644 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 06:36:05.645 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208674.
20732:M 13 Jun 2025 06:51:28.467 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 06:51:28.467 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 06:51:28.468 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 06:51:28.468 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208674.
20732:M 13 Jun 2025 07:07:16.255 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 07:07:16.392 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 07:07:16.394 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 07:07:16.394 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208688.
20732:M 13 Jun 2025 07:17:29.215 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 07:17:29.344 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 07:17:29.347 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 07:17:29.347 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208688.
20732:M 13 Jun 2025 07:36:07.198 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 07:36:07.337 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 07:36:07.341 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 07:36:07.341 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208758.
20732:M 13 Jun 2025 07:51:24.359 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 07:51:24.370 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 07:51:24.370 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208758.
20732:M 13 Jun 2025 08:08:51.926 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 08:08:51.927 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 08:08:51.931 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 08:08:51.932 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208772.
20732:M 13 Jun 2025 08:25:34.848 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 08:25:34.848 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 08:25:34.851 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 08:25:34.851 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208772.
20732:M 13 Jun 2025 08:42:12.674 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 08:42:12.674 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 08:42:12.678 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 08:42:12.678 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208786.
20732:M 13 Jun 2025 08:59:24.656 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 08:59:24.657 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 08:59:24.662 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 08:59:24.662 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208786.
20732:M 13 Jun 2025 09:15:18.597 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 09:15:18.598 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 09:15:18.600 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 09:15:18.600 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208800.
20732:M 13 Jun 2025 09:18:29.569 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 09:18:29.569 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 09:18:29.575 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 09:18:29.575 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208800.
20732:M 13 Jun 2025 09:32:28.604 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 09:32:28.604 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 09:32:28.607 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 09:32:28.608 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208870.
20732:M 13 Jun 2025 09:34:15.918 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 09:34:15.923 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 09:34:15.923 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 208940.
20732:M 13 Jun 2025 11:02:48.690 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 11:02:48.690 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 11:02:48.698 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 11:02:48.699 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 216038.
20732:M 13 Jun 2025 12:26:30.707 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 12:26:30.710 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 12:26:30.710 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 222534.
20732:M 13 Jun 2025 12:58:21.710 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 12:58:21.710 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 12:58:21.715 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 12:58:21.715 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 223752.
20732:M 13 Jun 2025 13:16:08.169 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 13:16:08.173 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 13:16:08.174 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 223906.
20732:M 13 Jun 2025 13:32:01.762 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 13:32:01.763 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 13:32:01.766 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 13:32:01.767 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 223976.
20732:M 13 Jun 2025 13:52:57.805 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 13:52:57.805 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 13:52:57.809 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 13:52:57.809 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 224410.
20732:M 13 Jun 2025 14:02:03.259 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 14:02:03.270 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 14:02:03.270 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 224900.
20732:M 13 Jun 2025 14:13:34.968 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 14:13:34.969 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 14:13:34.970 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 225194.
20732:M 13 Jun 2025 16:11:05.389 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 16:11:05.391 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 16:11:05.391 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 234770.
20732:M 13 Jun 2025 17:03:55.439 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 17:03:55.441 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 17:03:55.441 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 237906.
20732:M 13 Jun 2025 17:15:37.022 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 17:15:37.025 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 17:15:37.025 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 238592.
20732:M 13 Jun 2025 18:34:57.970 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 18:34:57.970 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 18:34:57.972 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 18:34:57.972 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 244948.
20732:M 13 Jun 2025 18:51:47.480 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 18:51:47.480 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 18:51:47.481 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 18:51:47.481 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 245018.
20732:M 13 Jun 2025 19:09:07.375 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 19:09:07.390 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 19:09:07.392 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 19:09:07.392 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 245102.
20732:M 13 Jun 2025 19:26:44.370 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 19:26:44.399 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 19:26:44.402 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 19:26:44.402 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 245102.
20732:M 13 Jun 2025 19:43:54.367 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 19:43:54.394 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 19:43:54.401 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 19:43:54.402 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 245116.
20732:M 13 Jun 2025 19:59:18.363 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 19:59:18.391 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 19:59:18.395 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 19:59:18.395 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 245116.
20732:M 13 Jun 2025 20:17:03.366 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 20:17:03.379 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 20:17:03.391 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 20:17:03.391 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 245130.
20732:M 13 Jun 2025 20:33:27.357 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 20:33:27.386 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 20:33:27.410 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 20:33:27.410 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 245130.
20732:M 13 Jun 2025 20:49:58.353 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 20:49:58.370 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 20:49:58.378 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 20:49:58.382 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 245200.
20732:M 13 Jun 2025 21:06:34.353 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 21:06:34.368 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 21:06:34.384 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 21:06:34.384 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 245214.
20732:M 13 Jun 2025 21:22:46.235 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 21:22:46.376 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 21:22:46.380 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 21:22:46.380 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 245214.
20732:M 13 Jun 2025 21:34:27.348 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 21:34:27.362 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 21:34:27.375 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 21:34:27.375 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 245228.
20732:M 13 Jun 2025 21:54:53.848 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 21:54:53.848 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 21:54:53.853 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 21:54:53.853 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 245438.
20732:M 13 Jun 2025 22:12:14.741 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 22:12:14.742 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 22:12:14.745 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 22:12:14.745 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 245438.
20732:M 13 Jun 2025 22:17:26.932 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 22:17:26.932 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 22:17:26.934 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 22:17:26.934 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 245452.
20732:M 13 Jun 2025 22:33:20.388 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 22:33:20.388 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 22:33:20.391 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 22:33:20.391 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 245508.
20732:M 13 Jun 2025 22:35:27.888 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 22:35:27.888 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 22:35:27.893 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 22:35:27.894 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 245578.
20732:M 13 Jun 2025 22:51:40.649 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 22:51:40.650 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 22:51:40.655 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 22:51:40.655 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 245634.
20732:M 13 Jun 2025 23:07:53.358 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 23:07:53.358 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 23:07:53.363 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 23:07:53.364 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 245704.
20732:M 13 Jun 2025 23:24:10.664 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 13 Jun 2025 23:24:10.664 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 23:24:10.669 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 23:24:10.669 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 245760.
20732:M 13 Jun 2025 23:36:27.770 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 23:36:27.775 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 23:36:27.775 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 245830.
20732:M 13 Jun 2025 23:52:45.175 * Connection with replica 127.0.0.1:7104 lost.
20732:M 13 Jun 2025 23:52:45.179 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 13 Jun 2025 23:52:45.180 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 245900.
20732:M 14 Jun 2025 00:09:02.491 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 00:09:02.494 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 00:09:02.494 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 245956.
20732:M 14 Jun 2025 00:25:15.372 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 00:25:15.374 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 00:25:15.374 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 246026.
20732:M 14 Jun 2025 00:37:27.438 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 00:37:27.448 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 00:37:27.449 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 246152.
20732:M 14 Jun 2025 00:53:40.242 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 00:53:40.253 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 00:53:40.255 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 00:53:40.255 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 246222.
20732:M 14 Jun 2025 01:09:52.963 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 01:09:53.095 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 01:09:53.099 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 01:09:53.099 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 246278.
20732:M 14 Jun 2025 01:26:05.782 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 01:26:05.897 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 01:26:05.903 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 01:26:05.903 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 246348.
20732:M 14 Jun 2025 01:38:27.330 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 01:38:27.465 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 01:38:27.469 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 01:38:27.469 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 246404.
20732:M 14 Jun 2025 01:54:40.170 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 01:54:40.283 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 01:54:40.286 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 01:54:40.286 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 246474.
20732:M 14 Jun 2025 01:58:31.231 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 01:58:31.231 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 01:58:31.241 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 01:58:31.242 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 246600.
20732:M 14 Jun 2025 02:02:22.042 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 02:02:22.046 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 02:02:22.046 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 246670.
20732:M 14 Jun 2025 02:18:34.706 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 02:18:34.707 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 02:18:34.710 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 02:18:34.710 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 246726.
20732:M 14 Jun 2025 02:34:47.412 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 02:34:47.412 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 02:34:47.419 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 02:34:47.419 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 246796.
20732:M 14 Jun 2025 02:51:00.106 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 02:51:00.106 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 02:51:00.109 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 02:51:00.109 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 246866.
20732:M 14 Jun 2025 03:07:11.962 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 03:07:11.962 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 03:07:11.966 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 03:07:11.966 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 246922.
20732:M 14 Jun 2025 03:23:21.623 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 03:23:21.623 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 03:23:21.626 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 03:23:21.627 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 246992.
20732:M 14 Jun 2025 03:39:32.214 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 03:39:32.215 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 03:39:32.229 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 03:39:32.229 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 247048.
20732:M 14 Jun 2025 03:55:44.880 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 03:55:44.880 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 03:55:44.885 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 03:55:44.885 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 247118.
20732:M 14 Jun 2025 04:11:57.576 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 04:11:57.576 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 04:11:57.579 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 04:11:57.580 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 247174.
20732:M 14 Jun 2025 04:28:10.430 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 04:28:10.449 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 04:28:10.460 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 04:28:10.460 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 247244.
20732:M 14 Jun 2025 04:44:05.254 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 04:44:05.368 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 04:44:05.371 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 04:44:05.371 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 247314.
20732:M 14 Jun 2025 05:00:15.066 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 05:00:15.066 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 05:00:15.071 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 05:00:15.071 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 247370.
20732:M 14 Jun 2025 05:02:19.972 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 05:02:19.972 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 05:02:19.975 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 05:02:19.976 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 247440.
20732:M 14 Jun 2025 05:18:32.657 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 05:18:32.660 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 05:18:32.674 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 05:18:32.674 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 247496.
20732:M 14 Jun 2025 05:30:34.239 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 05:30:34.373 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 05:30:34.376 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 05:30:34.376 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 247566.
20732:M 14 Jun 2025 05:46:31.237 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 05:46:31.353 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 05:46:31.356 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 05:46:31.356 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 247622.
20732:M 14 Jun 2025 06:02:28.671 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 06:02:28.671 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 06:02:28.678 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 06:02:28.678 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 247692.
20732:M 14 Jun 2025 06:18:13.988 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 06:18:13.988 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 06:18:13.995 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 06:18:13.995 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 247748.
20732:M 14 Jun 2025 06:34:25.615 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 06:34:25.616 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 06:34:25.623 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 06:34:25.623 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 247804.
20732:M 14 Jun 2025 06:50:39.245 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 06:50:39.245 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 06:50:39.250 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 06:50:39.250 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 247874.
20732:M 14 Jun 2025 07:06:51.925 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 07:06:51.925 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 07:06:51.929 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 07:06:51.929 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 247944.
20732:M 14 Jun 2025 07:22:56.646 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 07:22:56.646 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 07:22:56.649 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 07:22:56.649 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 248000.
20732:M 14 Jun 2025 07:31:34.633 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 07:31:34.633 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 07:31:34.637 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 07:31:34.637 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 248056.
20732:M 14 Jun 2025 07:47:47.272 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 07:47:47.272 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 07:47:47.277 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 07:47:47.278 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 248126.
20732:M 14 Jun 2025 07:51:58.411 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 07:51:58.412 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 07:51:58.414 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 07:51:58.415 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 248196.
20732:M 14 Jun 2025 08:08:11.071 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 08:08:11.085 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 08:08:11.088 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 08:08:11.088 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 248252.
20732:M 14 Jun 2025 08:24:24.734 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 08:24:24.738 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 08:24:24.738 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 248322.
20732:M 14 Jun 2025 08:32:35.068 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 08:32:35.072 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 08:32:35.073 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 248392.
20732:M 14 Jun 2025 08:48:23.982 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 08:48:23.984 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 08:48:23.984 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 248448.
20732:M 14 Jun 2025 09:04:32.884 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 09:04:32.887 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 09:04:32.887 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 248504.
20732:M 14 Jun 2025 09:20:18.400 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 09:20:18.411 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 09:20:18.411 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 248574.
20732:M 14 Jun 2025 09:33:35.061 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 09:33:35.061 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 09:33:35.065 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 09:33:35.065 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 248630.
20732:M 14 Jun 2025 09:49:47.058 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 09:49:47.059 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 09:49:47.068 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 09:49:47.069 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 248700.
20732:M 14 Jun 2025 10:00:36.028 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 10:00:36.028 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 10:00:36.031 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 10:00:36.031 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 248770.
20732:M 14 Jun 2025 21:06:41.435 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 21:06:41.435 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 21:06:41.438 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 21:06:41.438 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 302768.
20732:M 14 Jun 2025 21:22:47.029 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 21:22:47.034 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 21:22:47.035 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 302838.
20732:M 14 Jun 2025 21:38:52.959 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 21:38:52.965 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 21:38:52.965 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 302894.
20732:M 14 Jun 2025 21:55:05.399 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 14 Jun 2025 21:55:05.399 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 21:55:05.405 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 21:55:05.405 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 302964.
20732:M 14 Jun 2025 22:03:24.268 * Connection with replica 127.0.0.1:7104 lost.
20732:M 14 Jun 2025 22:03:24.273 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 14 Jun 2025 22:03:24.274 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 303048.
20732:M 15 Jun 2025 04:50:06.846 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 15 Jun 2025 04:50:06.846 * Connection with replica 127.0.0.1:7104 lost.
20732:M 15 Jun 2025 04:50:06.847 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 15 Jun 2025 04:50:06.848 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 335360.
20732:M 15 Jun 2025 06:07:26.768 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 15 Jun 2025 06:07:26.768 * Connection with replica 127.0.0.1:7104 lost.
20732:M 15 Jun 2025 06:07:26.770 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 15 Jun 2025 06:07:26.770 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 340428.
20732:M 15 Jun 2025 06:23:36.254 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 15 Jun 2025 06:23:36.389 * Connection with replica 127.0.0.1:7104 lost.
20732:M 15 Jun 2025 06:23:36.392 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 15 Jun 2025 06:23:36.392 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 340498.
20732:M 15 Jun 2025 06:29:10.280 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 15 Jun 2025 06:29:10.414 * Connection with replica 127.0.0.1:7104 lost.
20732:M 15 Jun 2025 06:29:10.417 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 15 Jun 2025 06:29:10.417 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 340568.
20732:M 15 Jun 2025 06:57:18.369 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 15 Jun 2025 06:57:18.370 * Connection with replica 127.0.0.1:7104 lost.
20732:M 15 Jun 2025 06:57:18.379 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 15 Jun 2025 06:57:18.380 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 341548.
20732:M 15 Jun 2025 07:13:45.567 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 15 Jun 2025 07:13:45.568 * Connection with replica 127.0.0.1:7104 lost.
20732:M 15 Jun 2025 07:13:45.571 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 15 Jun 2025 07:13:45.572 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 341632.
20732:M 15 Jun 2025 08:30:11.011 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 15 Jun 2025 08:30:11.012 * Connection with replica 127.0.0.1:7104 lost.
20732:M 15 Jun 2025 08:30:11.020 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 15 Jun 2025 08:30:11.020 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 346714.
20732:M 15 Jun 2025 08:46:23.003 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 15 Jun 2025 08:46:23.003 * Connection with replica 127.0.0.1:7104 lost.
20732:M 15 Jun 2025 08:46:23.006 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 15 Jun 2025 08:46:23.007 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 346770.
20732:M 15 Jun 2025 09:03:23.785 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 15 Jun 2025 09:03:23.909 * Connection with replica 127.0.0.1:7104 lost.
20732:M 15 Jun 2025 09:03:23.912 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 15 Jun 2025 09:03:23.912 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 346910.
20732:M 15 Jun 2025 10:20:40.200 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 15 Jun 2025 10:20:40.200 * Connection with replica 127.0.0.1:7104 lost.
20732:M 15 Jun 2025 10:20:40.203 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 15 Jun 2025 10:20:40.203 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 351964.
20732:M 15 Jun 2025 10:31:10.287 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 15 Jun 2025 10:31:10.406 * Connection with replica 127.0.0.1:7104 lost.
20732:M 15 Jun 2025 10:31:10.410 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 15 Jun 2025 10:31:10.410 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 352034.
20732:M 15 Jun 2025 10:34:06.975 * Connection with replica 127.0.0.1:7104 lost.
20732:M 15 Jun 2025 10:34:06.979 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 15 Jun 2025 10:34:06.979 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 352104.
20732:M 15 Jun 2025 10:50:18.930 * Connection with replica 127.0.0.1:7104 lost.
20732:M 15 Jun 2025 10:50:18.933 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 15 Jun 2025 10:50:18.933 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 352160.
20732:M 15 Jun 2025 11:07:09.003 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 15 Jun 2025 11:07:09.138 * Connection with replica 127.0.0.1:7104 lost.
20732:M 15 Jun 2025 11:07:09.141 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 15 Jun 2025 11:07:09.141 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 352286.
20732:M 15 Jun 2025 12:11:18.202 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 15 Jun 2025 12:11:18.202 * Connection with replica 127.0.0.1:7104 lost.
20732:M 15 Jun 2025 12:11:18.225 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 15 Jun 2025 12:11:18.225 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 356262.
20732:M 15 Jun 2025 12:15:17.993 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
20732:M 15 Jun 2025 12:15:18.104 * Connection with replica 127.0.0.1:7104 lost.
20732:M 15 Jun 2025 12:15:18.111 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 15 Jun 2025 12:15:18.111 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 356332.
20732:M 15 Jun 2025 16:24:12.732 * Connection with replica 127.0.0.1:7104 lost.
20732:M 15 Jun 2025 16:24:12.734 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 15 Jun 2025 16:24:12.735 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 375694.
20732:M 15 Jun 2025 16:40:22.619 * Connection with replica 127.0.0.1:7104 lost.
20732:M 15 Jun 2025 16:40:22.620 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 15 Jun 2025 16:40:22.621 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 375750.
20732:M 15 Jun 2025 17:49:12.706 * Connection with replica 127.0.0.1:7104 lost.
20732:M 15 Jun 2025 17:49:12.709 * Replica 127.0.0.1:7104 asks for synchronization
20732:M 15 Jun 2025 17:49:12.709 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 380888.
20732:signal-handler (1749990448) Received SIGTERM scheduling shutdown...
20732:M 15 Jun 2025 20:27:29.239 * User requested shutdown...
20732:M 15 Jun 2025 20:27:29.740 * 1 of 1 replicas are in sync when shutting down.
20732:M 15 Jun 2025 20:27:30.242 * Calling fsync() on the AOF file.
20732:M 15 Jun 2025 20:27:30.748 * Saving the final RDB snapshot before exiting.
12953:C 15 Jun 2025 20:50:03.107 * oO0OoO0OoO0Oo Redis is starting oO0OoO0OoO0Oo
12953:C 15 Jun 2025 20:50:03.107 * Redis version=7.2.7, bits=64, commit=00000000, modified=0, pid=12953, just started
12953:C 15 Jun 2025 20:50:03.107 * Configuration loaded
12953:M 15 Jun 2025 20:50:03.108 * Increased maximum number of open files to 10032 (it was originally set to 256).
12953:M 15 Jun 2025 20:50:03.108 * monotonic clock: POSIX clock_gettime
12953:M 15 Jun 2025 20:50:03.108 * Running mode=cluster, port=7101.
12953:M 15 Jun 2025 20:50:03.108 # WARNING: The TCP backlog setting of 511 cannot be enforced because kern.ipc.somaxconn is set to the lower value of 128.
12953:M 15 Jun 2025 20:50:03.108 * Node configuration loaded, I'm 23443f72260886460cb1475efe4af4ce9d98a150
12953:M 15 Jun 2025 20:50:03.109 * Server initialized
12953:M 15 Jun 2025 20:50:03.109 * Reading RDB base file on AOF loading...
12953:M 15 Jun 2025 20:50:03.109 * Loading RDB produced by version 7.2.7
12953:M 15 Jun 2025 20:50:03.109 * RDB age 540933 seconds
12953:M 15 Jun 2025 20:50:03.109 * RDB memory usage when created 1.70 Mb
12953:M 15 Jun 2025 20:50:03.109 * RDB is base AOF
12953:M 15 Jun 2025 20:50:03.109 * Done loading RDB, keys loaded: 0, keys expired: 0.
12953:M 15 Jun 2025 20:50:03.109 * DB loaded from base file appendonly-7101.aof.1.base.rdb: 0.000 seconds
12953:M 15 Jun 2025 20:50:03.117 * DB saved on disk
12953:M 15 Jun 2025 20:50:03.123 * DB saved on disk
12953:M 15 Jun 2025 20:50:03.128 * DB saved on disk
12953:M 15 Jun 2025 20:50:03.135 * DB saved on disk
12953:M 15 Jun 2025 20:50:03.140 * DB saved on disk
12953:M 15 Jun 2025 20:50:03.146 * DB saved on disk
12953:M 15 Jun 2025 20:50:03.151 * DB saved on disk
12953:M 15 Jun 2025 20:50:03.156 * DB saved on disk
12953:M 15 Jun 2025 20:50:03.161 * DB saved on disk
12953:M 15 Jun 2025 20:50:03.167 * DB saved on disk
12953:M 15 Jun 2025 20:50:03.172 * DB saved on disk
12953:M 15 Jun 2025 20:50:03.172 * DB loaded from incr file appendonly-7101.aof.1.incr.aof: 0.063 seconds
12953:M 15 Jun 2025 20:50:03.172 * DB loaded from append only file: 0.064 seconds
12953:M 15 Jun 2025 20:50:03.173 * Opening AOF incr file appendonly-7101.aof.1.incr.aof on server start
12953:M 15 Jun 2025 20:50:03.173 * Ready to accept connections tcp
12953:M 15 Jun 2025 20:50:05.221 * Cluster state changed: ok
12953:M 15 Jun 2025 20:50:09.279 * Replica 127.0.0.1:7104 asks for synchronization
12953:M 15 Jun 2025 20:50:09.284 * Partial resynchronization not accepted: Replication ID mismatch (Replica asked for '32a807b0977eb64fea96d5b8f32180542a8a0b36', my replication IDs are 'ed1fca2f473c61c7f0056792ffc904c7b443de6f' and '0000000000000000000000000000000000000000')
12953:M 15 Jun 2025 20:50:09.284 * Replication backlog created, my new replication IDs are 'db70a1b013bdd3338317c06a080afce1a0be74d1' and '0000000000000000000000000000000000000000'
12953:M 15 Jun 2025 20:50:09.284 * Delay next BGSAVE for diskless SYNC
12953:M 15 Jun 2025 20:50:14.356 * Starting BGSAVE for SYNC with target: replicas sockets
12953:M 15 Jun 2025 20:50:14.357 * Background RDB transfer started by pid 13036
13036:C 15 Jun 2025 20:50:14.358 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 15 Jun 2025 20:50:14.358 * Diskless rdb transfer, done reading from pipe, 1 replicas still up.
12953:M 15 Jun 2025 20:50:14.371 * Background RDB transfer terminated with success
12953:M 15 Jun 2025 20:50:14.372 * Streamed RDB transfer with replica 127.0.0.1:7104 succeeded (socket). Waiting for REPLCONF ACK from replica to enable streaming
12953:M 15 Jun 2025 20:50:14.372 * Synchronization with replica 127.0.0.1:7104 succeeded
12953:M 15 Jun 2025 20:50:16.370 * DB saved on disk
12953:M 15 Jun 2025 20:50:16.375 * configEpoch set to 0 via CLUSTER RESET HARD
12953:M 15 Jun 2025 20:50:16.379 * Node hard reset, now I'm 0c3e6389c113e15330ab84e33b2e38667cd6597a
12953:M 15 Jun 2025 20:50:16.380 # Cluster state changed: fail
12953:M 15 Jun 2025 20:50:16.485 * Connection with replica 127.0.0.1:7104 lost.
12953:M 15 Jun 2025 20:50:19.555 * configEpoch set to 2 via CLUSTER SET-CONFIG-EPOCH
12953:M 15 Jun 2025 20:50:21.579 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 15 Jun 2025 20:50:21.579 * Partial resynchronization not accepted: Replication ID mismatch (Replica asked for 'd49332cd124b6869d9a81cb15e51c93a822538a0', my replication IDs are 'db70a1b013bdd3338317c06a080afce1a0be74d1' and '0000000000000000000000000000000000000000')
12953:M 15 Jun 2025 20:50:21.579 * Delay next BGSAVE for diskless SYNC
12953:M 15 Jun 2025 20:50:24.616 * Cluster state changed: ok
12953:M 15 Jun 2025 20:50:26.546 * Starting BGSAVE for SYNC with target: replicas sockets
12953:M 15 Jun 2025 20:50:26.548 * Background RDB transfer started by pid 13153
13153:C 15 Jun 2025 20:50:26.548 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 15 Jun 2025 20:50:26.550 * Diskless rdb transfer, done reading from pipe, 1 replicas still up.
12953:M 15 Jun 2025 20:50:26.579 * Background RDB transfer terminated with success
12953:M 15 Jun 2025 20:50:26.583 * Streamed RDB transfer with replica 127.0.0.1:7103 succeeded (socket). Waiting for REPLCONF ACK from replica to enable streaming
12953:M 15 Jun 2025 20:50:26.583 * Synchronization with replica 127.0.0.1:7103 succeeded
12953:M 16 Jun 2025 01:10:07.433 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 01:10:07.557 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 01:10:07.560 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 01:10:07.561 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 20188.
12953:M 16 Jun 2025 01:26:20.263 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 01:26:20.278 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 01:26:20.280 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 01:26:20.284 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 20244.
12953:M 16 Jun 2025 01:42:32.227 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 01:42:32.242 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 01:42:32.245 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 01:42:32.245 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 20314.
12953:M 16 Jun 2025 01:58:45.117 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 01:58:45.117 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 01:58:45.118 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 01:58:45.118 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 20370.
12953:M 16 Jun 2025 02:14:57.798 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 02:14:57.927 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 02:14:57.929 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 02:14:57.930 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 20440.
12953:M 16 Jun 2025 02:31:02.449 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 02:31:02.574 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 02:31:02.587 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 02:31:02.592 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 20510.
12953:M 16 Jun 2025 02:47:11.665 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 02:47:11.666 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 02:47:11.668 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 02:47:11.669 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 20566.
12953:M 16 Jun 2025 02:53:38.279 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 02:53:38.279 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 02:53:38.285 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 02:53:38.286 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 20622.
12953:M 16 Jun 2025 03:09:48.192 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 03:09:48.193 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 03:09:48.204 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 03:09:48.206 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 20692.
12953:M 16 Jun 2025 03:26:00.830 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 03:26:00.961 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 03:26:00.979 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 03:26:00.979 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 20748.
12953:M 16 Jun 2025 03:34:19.385 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 03:34:19.502 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 03:34:19.504 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 03:34:19.504 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 20818.
12953:M 16 Jun 2025 03:36:06.252 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 03:36:06.256 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 03:36:06.256 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 20888.
12953:M 16 Jun 2025 03:52:10.847 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 03:52:10.862 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 03:52:10.876 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 03:52:10.876 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 20944.
12953:M 16 Jun 2025 04:08:10.126 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 04:08:10.126 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 04:08:10.133 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 04:08:10.134 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 21014.
12953:M 16 Jun 2025 04:24:18.749 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 04:24:18.750 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 04:24:18.758 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 04:24:18.759 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 21070.
12953:M 16 Jun 2025 04:40:31.490 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 04:40:31.491 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 04:40:31.497 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 04:40:31.497 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 21140.
12953:M 16 Jun 2025 04:56:44.085 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 04:56:44.086 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 04:56:44.090 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 04:56:44.090 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 21196.
12953:M 16 Jun 2025 05:12:55.993 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 05:12:55.994 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 05:12:55.997 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 05:12:55.998 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 21266.
12953:M 16 Jun 2025 05:29:08.704 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 05:29:08.704 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 05:29:08.707 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 05:29:08.707 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 21322.
12953:M 16 Jun 2025 05:45:21.417 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 05:45:21.417 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 05:45:21.418 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 05:45:21.418 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 21392.
12953:M 16 Jun 2025 06:01:34.086 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 06:01:34.101 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 06:01:34.104 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 06:01:34.104 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 21462.
12953:M 16 Jun 2025 06:17:46.796 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 06:17:46.926 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 06:17:46.929 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 06:17:46.929 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 21518.
12953:M 16 Jun 2025 06:33:59.599 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 06:33:59.715 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 06:33:59.719 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 06:33:59.719 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 21588.
12953:M 16 Jun 2025 06:50:12.405 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 06:50:12.524 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 06:50:12.527 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 06:50:12.528 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 21644.
12953:M 16 Jun 2025 07:06:24.292 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 07:06:24.405 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 07:06:24.408 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 07:06:24.408 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 21714.
12953:M 16 Jun 2025 07:22:33.033 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 07:22:33.033 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 07:22:33.038 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 07:22:33.038 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 21770.
12953:M 16 Jun 2025 07:38:24.526 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 07:38:24.528 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 07:38:24.528 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 21840.
12953:M 16 Jun 2025 07:54:32.447 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 07:54:32.465 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 07:54:32.468 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 07:54:32.468 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 21896.
12953:M 16 Jun 2025 07:57:36.284 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 07:57:36.415 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 07:57:36.418 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 07:57:36.418 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 21966.
12953:M 16 Jun 2025 08:02:21.100 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 08:02:21.226 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 08:02:21.230 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 08:02:21.230 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 22022.
12953:M 16 Jun 2025 08:18:33.102 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 08:18:33.219 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 08:18:33.222 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 08:18:33.222 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 22092.
12953:M 16 Jun 2025 08:34:45.135 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 08:34:45.255 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 08:34:45.258 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 08:34:45.258 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 22148.
12953:M 16 Jun 2025 08:51:12.972 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 08:51:12.973 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 08:51:12.983 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 08:51:12.983 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 22288.
12953:M 16 Jun 2025 09:09:25.081 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 09:09:25.081 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 09:09:25.087 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 09:09:25.087 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 22484.
12953:M 16 Jun 2025 09:24:49.982 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 09:24:49.983 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 09:24:49.985 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 09:24:49.985 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 22498.
12953:M 16 Jun 2025 09:36:11.203 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 09:36:11.204 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 09:36:11.210 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 09:36:11.210 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 22498.
12953:M 16 Jun 2025 10:07:18.497 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 10:07:18.498 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 10:07:18.516 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 10:07:18.516 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 24472.
12953:M 16 Jun 2025 10:15:47.852 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 10:15:47.865 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 10:15:47.880 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 10:15:47.881 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 24528.
12953:M 16 Jun 2025 10:25:12.000 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 10:25:12.001 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 10:25:12.003 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 10:25:12.004 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 25032.
12953:M 16 Jun 2025 13:30:23.614 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 13:30:23.614 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 13:30:23.617 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 13:30:23.617 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 39004.
12953:M 16 Jun 2025 13:47:58.122 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 13:47:58.122 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 13:47:58.132 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 13:47:58.132 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 39144.
12953:M 16 Jun 2025 14:05:48.820 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 14:05:48.820 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 14:05:48.823 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 14:05:48.824 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 39270.
12953:M 16 Jun 2025 18:49:24.992 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 18:49:24.994 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 18:49:24.994 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 62538.
12953:M 16 Jun 2025 18:52:08.402 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 18:52:08.404 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 18:52:08.404 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 62664.
12953:M 16 Jun 2025 20:10:13.894 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 20:10:13.897 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 20:10:13.897 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 69048.
12953:M 16 Jun 2025 20:26:42.749 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 20:26:42.752 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 20:26:42.752 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 69118.
12953:M 16 Jun 2025 20:44:24.656 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 20:44:24.657 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 20:44:24.657 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 69118.
12953:M 16 Jun 2025 21:01:30.593 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 21:01:30.595 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 21:01:30.595 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 69132.
12953:M 16 Jun 2025 21:07:13.560 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 21:07:13.583 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 21:07:13.587 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 21:07:13.587 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 69132.
12953:M 16 Jun 2025 21:23:10.564 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 21:23:10.593 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 21:23:10.600 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 21:23:10.600 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 69202.
12953:M 16 Jun 2025 21:40:08.595 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 21:40:08.596 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 21:40:08.607 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 21:40:08.607 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 69216.
12953:M 16 Jun 2025 21:57:33.614 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 21:57:33.631 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 21:57:33.648 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 21:57:33.648 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 69216.
12953:M 16 Jun 2025 22:10:04.586 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 22:10:04.607 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 22:10:04.619 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 22:10:04.619 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 69230.
12953:M 16 Jun 2025 22:28:33.597 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 22:28:33.614 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 22:28:33.634 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 22:28:33.634 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 69286.
12953:M 16 Jun 2025 22:44:42.601 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 22:44:42.622 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 22:44:42.636 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 22:44:42.636 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 69300.
12953:M 16 Jun 2025 22:57:46.534 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 22:57:46.560 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 22:57:46.563 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 22:57:46.564 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 69300.
12953:M 16 Jun 2025 23:11:04.514 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 23:11:04.646 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 23:11:04.649 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 23:11:04.649 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 69370.
12953:M 16 Jun 2025 23:27:17.290 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 23:27:17.410 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 23:27:17.413 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 23:27:17.413 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 69426.
12953:M 16 Jun 2025 23:30:19.306 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 23:30:19.442 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 23:30:19.445 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 23:30:19.445 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 69496.
12953:M 16 Jun 2025 23:46:33.061 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 16 Jun 2025 23:46:33.191 * Connection with replica 127.0.0.1:7103 lost.
12953:M 16 Jun 2025 23:46:33.195 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 16 Jun 2025 23:46:33.195 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 69566.
12953:M 17 Jun 2025 00:02:45.864 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 00:02:45.985 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 00:02:45.993 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 00:02:45.993 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 69622.
12953:M 17 Jun 2025 00:12:04.115 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 00:12:04.243 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 00:12:04.246 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 00:12:04.247 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 69692.
12953:M 17 Jun 2025 00:28:16.851 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 00:28:16.971 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 00:28:16.976 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 00:28:16.976 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 69748.
12953:M 17 Jun 2025 00:39:48.458 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 00:39:48.577 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 00:39:48.581 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 00:39:48.581 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 69818.
12953:M 17 Jun 2025 01:12:25.456 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 01:12:25.456 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 01:12:25.464 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 01:12:25.465 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 71246.
12953:M 17 Jun 2025 01:28:38.107 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 01:28:38.110 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 01:28:38.110 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 71302.
12953:M 17 Jun 2025 01:44:51.742 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 01:44:51.748 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 01:44:51.749 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 71372.
12953:M 17 Jun 2025 02:01:04.545 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 02:01:04.545 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 02:01:04.554 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 02:01:04.555 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 71428.
12953:M 17 Jun 2025 02:17:17.098 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 02:17:17.098 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 02:17:17.107 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 02:17:17.108 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 71498.
12953:M 17 Jun 2025 02:33:29.760 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 02:33:29.761 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 02:33:29.763 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 02:33:29.764 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 71568.
12953:M 17 Jun 2025 02:49:42.442 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 02:49:42.442 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 02:49:42.446 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 02:49:42.446 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 71624.
12953:M 17 Jun 2025 02:56:30.279 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 02:56:30.416 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 02:56:30.418 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 02:56:30.419 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 71694.
12953:M 17 Jun 2025 03:12:43.925 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 03:12:43.930 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 03:12:43.931 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 71764.
12953:M 17 Jun 2025 03:15:50.852 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 03:15:50.852 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 03:15:50.860 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 03:15:50.861 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 71820.
12953:M 17 Jun 2025 03:32:03.519 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 03:32:03.519 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 03:32:03.527 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 03:32:03.528 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 71890.
12953:M 17 Jun 2025 03:48:16.240 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 03:48:16.240 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 03:48:16.244 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 03:48:16.244 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 71946.
12953:M 17 Jun 2025 04:04:28.764 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 04:04:28.766 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 04:04:28.767 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 72016.
12953:M 17 Jun 2025 04:20:40.699 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 04:20:40.714 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 04:20:40.717 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 04:20:40.717 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 72086.
12953:M 17 Jun 2025 04:36:53.533 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 04:36:53.648 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 04:36:53.664 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 04:36:53.664 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 72142.
12953:M 17 Jun 2025 04:53:06.354 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 04:53:06.494 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 04:53:06.498 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 04:53:06.498 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 72212.
12953:M 17 Jun 2025 05:09:19.084 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 05:09:19.199 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 05:09:19.204 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 05:09:19.204 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 72268.
12953:M 17 Jun 2025 05:20:13.402 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 05:20:13.518 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 05:20:13.521 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 05:20:13.521 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 72338.
12953:M 17 Jun 2025 05:36:26.757 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 05:36:26.761 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 05:36:26.762 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 72408.
12953:M 17 Jun 2025 05:52:39.355 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 05:52:39.357 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 05:52:39.357 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 72464.
12953:M 17 Jun 2025 06:08:51.240 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 06:08:51.253 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 06:08:51.253 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 72534.
12953:M 17 Jun 2025 06:25:03.217 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 06:25:03.219 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 06:25:03.219 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 72590.
12953:M 17 Jun 2025 06:41:16.876 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 06:41:16.877 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 06:41:16.877 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 72660.
12953:M 17 Jun 2025 06:53:01.811 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 06:53:01.814 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 06:53:01.814 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 72730.
12953:M 17 Jun 2025 07:09:15.421 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 07:09:15.423 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 07:09:15.423 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 72786.
12953:M 17 Jun 2025 07:25:27.295 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 07:25:27.297 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 07:25:27.297 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 72856.
12953:M 17 Jun 2025 07:41:39.943 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 07:41:39.945 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 07:41:39.945 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 72926.
12953:M 17 Jun 2025 07:57:51.902 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 07:57:51.904 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 07:57:51.905 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 72982.
12953:M 17 Jun 2025 08:14:05.581 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 08:14:05.583 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 08:14:05.584 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 73052.
12953:M 17 Jun 2025 08:30:18.316 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 08:30:18.317 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 08:30:18.322 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 08:30:18.322 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 73108.
12953:M 17 Jun 2025 08:34:58.932 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 08:34:58.933 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 08:34:58.937 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 08:34:58.937 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 73178.
12953:M 17 Jun 2025 08:43:16.003 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 08:43:16.003 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 08:43:16.008 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 08:43:16.009 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 73248.
12953:M 17 Jun 2025 09:00:41.440 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 09:00:41.441 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 09:00:41.441 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 73360.
12953:M 17 Jun 2025 09:18:18.436 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 09:18:18.445 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 09:18:18.445 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 73374.
12953:M 17 Jun 2025 09:36:06.427 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 09:36:06.434 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 09:36:06.434 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 73374.
12953:M 17 Jun 2025 09:44:43.350 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 09:44:43.358 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 09:44:43.358 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 73388.
12953:M 17 Jun 2025 10:34:38.963 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 10:34:38.964 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 10:34:38.967 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 10:34:38.967 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 76944.
12953:M 17 Jun 2025 10:42:46.428 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 10:42:46.539 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 10:42:46.542 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 10:42:46.543 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 77000.
12953:M 17 Jun 2025 13:18:04.647 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 13:18:04.648 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 13:18:04.651 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 13:18:04.651 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 88564.
12953:M 17 Jun 2025 13:33:13.854 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 13:33:13.855 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 13:33:13.861 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 13:33:13.861 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 88718.
12953:M 17 Jun 2025 13:51:00.857 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 13:51:00.858 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 13:51:00.865 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 13:51:00.866 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 88774.
12953:M 17 Jun 2025 14:03:44.166 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 14:03:44.166 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 14:03:44.171 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 14:03:44.171 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 88844.
12953:M 17 Jun 2025 14:56:35.474 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 14:56:35.495 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 14:56:35.508 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 14:56:35.508 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 91952.
12953:M 17 Jun 2025 15:07:50.024 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 15:07:50.039 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 15:07:50.039 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 92022.
12953:M 17 Jun 2025 15:57:30.360 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 15:57:30.361 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 15:57:30.368 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 15:57:30.368 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 95830.
12953:M 17 Jun 2025 16:02:20.302 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 16:02:20.420 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 16:02:20.422 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 16:02:20.422 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 95900.
12953:M 17 Jun 2025 16:19:42.242 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 16:19:42.359 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 16:19:42.362 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 16:19:42.362 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 95956.
12953:M 17 Jun 2025 16:38:03.542 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 16:38:03.542 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 16:38:03.545 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 16:38:03.545 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 96124.
12953:M 17 Jun 2025 16:55:21.337 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 16:55:21.473 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 16:55:21.475 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 16:55:21.475 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 96264.
12953:M 17 Jun 2025 16:57:58.140 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 16:57:58.254 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 16:57:58.257 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 16:57:58.257 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 96320.
12953:M 17 Jun 2025 18:59:01.911 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 18:59:01.911 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 18:59:01.913 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 18:59:01.913 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 105896.
12953:M 17 Jun 2025 19:16:26.595 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 19:16:26.595 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 19:16:26.597 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 19:16:26.598 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 105952.
12953:M 17 Jun 2025 19:32:18.493 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 19:32:18.508 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 19:32:18.522 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 19:32:18.522 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 105966.
12953:M 17 Jun 2025 19:49:03.382 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 19:49:03.520 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 19:49:03.527 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 19:49:03.527 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 105966.
12953:M 17 Jun 2025 20:05:34.518 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 20:05:34.518 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 20:05:34.523 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 20:05:34.523 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 105980.
12953:M 17 Jun 2025 20:21:22.398 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 20:21:22.517 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 20:21:22.537 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 20:21:22.537 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 105980.
12953:M 17 Jun 2025 20:36:26.533 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 20:36:26.533 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 20:36:26.536 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 20:36:26.536 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 105994.
12953:M 17 Jun 2025 20:51:56.409 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 20:51:56.525 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 20:51:56.534 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 20:51:56.537 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 105994.
12953:M 17 Jun 2025 20:54:17.397 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 20:54:17.538 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 20:54:17.540 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 20:54:17.540 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 106008.
12953:M 17 Jun 2025 21:10:38.549 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 21:10:38.559 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 21:10:38.562 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 21:10:38.562 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 106064.
12953:M 17 Jun 2025 21:15:53.770 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 17 Jun 2025 21:15:53.787 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 21:15:53.805 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 21:15:53.805 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 106078.
12953:M 17 Jun 2025 21:17:40.856 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 21:17:40.862 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 21:17:40.862 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 106148.
12953:M 17 Jun 2025 21:33:51.604 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 21:33:51.607 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 21:33:51.607 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 106204.
12953:M 17 Jun 2025 21:46:13.491 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 21:46:13.494 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 21:46:13.494 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 106274.
12953:M 17 Jun 2025 21:55:17.486 * Connection with replica 127.0.0.1:7103 lost.
12953:M 17 Jun 2025 21:55:17.488 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 17 Jun 2025 21:55:17.488 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 106330.
12953:M 18 Jun 2025 02:02:54.960 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 02:02:55.076 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 02:02:55.099 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 02:02:55.099 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 125580.
12953:M 18 Jun 2025 02:19:07.864 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 02:19:07.878 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 02:19:07.883 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 02:19:07.883 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 125636.
12953:M 18 Jun 2025 02:35:20.551 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 02:35:20.692 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 02:35:20.701 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 02:35:20.701 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 125706.
12953:M 18 Jun 2025 02:51:34.295 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 02:51:34.297 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 02:51:34.298 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 02:51:34.298 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 125776.
12953:M 18 Jun 2025 03:07:47.088 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 03:07:47.118 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 03:07:47.120 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 03:07:47.120 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 125832.
12953:M 18 Jun 2025 03:23:51.966 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 03:23:51.968 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 03:23:51.968 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 125902.
12953:M 18 Jun 2025 03:40:05.645 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 03:40:05.646 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 03:40:05.650 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 03:40:05.650 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 125972.
12953:M 18 Jun 2025 03:46:44.733 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 03:46:44.734 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 03:46:44.745 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 03:46:44.745 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 126028.
12953:M 18 Jun 2025 04:02:57.481 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 04:02:57.481 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 04:02:57.484 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 04:02:57.484 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 126098.
12953:M 18 Jun 2025 04:18:51.499 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 04:18:51.499 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 04:18:51.502 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 04:18:51.503 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 126168.
12953:M 18 Jun 2025 04:35:02.401 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 04:35:02.401 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 04:35:02.403 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 04:35:02.403 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 126224.
12953:M 18 Jun 2025 04:51:16.016 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 04:51:16.017 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 04:51:16.026 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 04:51:16.026 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 126294.
12953:M 18 Jun 2025 05:07:28.654 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 05:07:28.655 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 05:07:28.667 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 05:07:28.668 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 126350.
12953:M 18 Jun 2025 05:23:41.359 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 05:23:41.361 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 05:23:41.362 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 126420.
12953:M 18 Jun 2025 05:39:54.007 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 05:39:54.008 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 05:39:54.013 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 05:39:54.014 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 126490.
12953:M 18 Jun 2025 05:56:05.862 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 05:56:05.982 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 05:56:05.985 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 05:56:05.985 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 126546.
12953:M 18 Jun 2025 05:59:13.134 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 05:59:13.261 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 05:59:13.263 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 05:59:13.263 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 126616.
12953:M 18 Jun 2025 06:15:25.993 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 06:15:26.125 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 06:15:26.129 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 06:15:26.129 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 126672.
12953:M 18 Jun 2025 06:31:38.790 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 06:31:38.910 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 06:31:38.912 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 06:31:38.912 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 126742.
12953:M 18 Jun 2025 06:47:47.846 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 06:47:47.978 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 06:47:47.981 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 06:47:47.981 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 126812.
12953:M 18 Jun 2025 07:04:01.590 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 07:04:01.591 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 07:04:01.598 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 07:04:01.598 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 126868.
12953:M 18 Jun 2025 07:20:14.305 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 07:20:14.306 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 07:20:14.306 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 126938.
12953:M 18 Jun 2025 07:36:28.091 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 07:36:28.092 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 07:36:28.098 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 07:36:28.098 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 127008.
12953:M 18 Jun 2025 07:41:51.123 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 07:41:51.123 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 07:41:51.128 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 07:41:51.128 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 127064.
12953:M 18 Jun 2025 07:55:59.948 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 07:55:59.949 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 07:55:59.953 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 07:55:59.953 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 127134.
12953:M 18 Jun 2025 08:10:09.120 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 08:10:09.123 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 08:10:09.128 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 08:10:09.128 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 127190.
12953:M 18 Jun 2025 08:28:34.694 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 08:28:34.696 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 08:28:34.696 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 127260.
12953:M 18 Jun 2025 08:30:10.071 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 08:30:10.071 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 08:30:10.074 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 08:30:10.075 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 127260.
12953:M 18 Jun 2025 08:33:15.245 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 08:33:15.245 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 08:33:15.268 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 08:33:15.268 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 127330.
12953:M 18 Jun 2025 08:42:31.610 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 08:42:31.611 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 08:42:31.620 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 08:42:31.620 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 127470.
12953:M 18 Jun 2025 09:00:22.174 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 09:00:22.175 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 09:00:22.178 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 09:00:22.178 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 127540.
12953:M 18 Jun 2025 09:18:02.066 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 09:18:02.066 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 09:18:02.075 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 09:18:02.075 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 127540.
12953:M 18 Jun 2025 09:33:53.943 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 09:33:53.944 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 09:33:53.948 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 09:33:53.948 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 127554.
12953:M 18 Jun 2025 09:38:48.637 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 09:38:48.640 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 09:38:48.641 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 127554.
12953:M 18 Jun 2025 09:41:37.720 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 09:41:37.720 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 09:41:37.725 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 09:41:37.725 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 127624.
12953:M 18 Jun 2025 12:36:00.594 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 12:36:00.602 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 12:36:00.605 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 141442.
12953:M 18 Jun 2025 12:42:55.866 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 12:42:55.892 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 12:42:55.894 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 12:42:55.894 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 141568.
12953:M 18 Jun 2025 12:50:25.569 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 12:50:25.569 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 12:50:25.572 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 12:50:25.573 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 141638.
12953:M 18 Jun 2025 12:53:12.550 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 12:53:12.550 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 12:53:12.555 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 12:53:12.555 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 141708.
12953:M 18 Jun 2025 13:02:20.372 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 13:02:20.390 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 13:02:20.412 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 13:02:20.412 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 141764.
12953:M 18 Jun 2025 13:20:21.964 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 13:20:21.965 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 13:20:21.973 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 13:20:21.974 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 141918.
12953:M 18 Jun 2025 13:37:51.492 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 13:37:51.493 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 13:37:51.493 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 141974.
12953:M 18 Jun 2025 13:54:25.035 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 13:54:25.035 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 13:54:25.044 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 13:54:25.044 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 142058.
12953:M 18 Jun 2025 14:05:05.117 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 14:05:05.118 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 14:05:05.121 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 14:05:05.121 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 142562.
12953:M 18 Jun 2025 15:43:11.185 * 1 changes in 3600 seconds. Saving...
12953:M 18 Jun 2025 15:43:11.187 * Background saving started by pid 93962
93962:C 18 Jun 2025 15:43:11.199 * DB saved on disk
93962:C 18 Jun 2025 15:43:11.199 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 18 Jun 2025 15:43:11.288 * Background saving terminated with success
12953:M 18 Jun 2025 16:43:12.089 * 1 changes in 3600 seconds. Saving...
12953:M 18 Jun 2025 16:43:12.092 * Background saving started by pid 35164
35164:C 18 Jun 2025 16:43:12.106 * DB saved on disk
35164:C 18 Jun 2025 16:43:12.106 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 18 Jun 2025 16:43:12.194 * Background saving terminated with success
12953:M 18 Jun 2025 19:33:25.526 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 19:33:25.526 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 19:33:25.528 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 19:33:25.528 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 169483.
12953:M 18 Jun 2025 21:43:47.411 * 1 changes in 3600 seconds. Saving...
12953:M 18 Jun 2025 21:43:47.414 * Background saving started by pid 5377
5377:C 18 Jun 2025 21:43:47.427 * DB saved on disk
5377:C 18 Jun 2025 21:43:47.429 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 18 Jun 2025 21:43:47.517 * Background saving terminated with success
12953:M 18 Jun 2025 22:16:16.464 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 22:16:16.466 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 22:16:16.466 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 183447.
12953:M 18 Jun 2025 22:25:15.482 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 22:25:15.494 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 22:25:15.494 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 183447.
12953:M 18 Jun 2025 22:41:24.482 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 22:41:24.487 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 22:41:24.487 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 183517.
12953:M 18 Jun 2025 22:58:02.346 * 1 changes in 3600 seconds. Saving...
12953:M 18 Jun 2025 22:58:02.480 * Background saving started by pid 11623
11623:C 18 Jun 2025 22:58:02.515 * DB saved on disk
11623:C 18 Jun 2025 22:58:02.516 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 18 Jun 2025 22:58:02.597 * Background saving terminated with success
12953:M 18 Jun 2025 22:58:02.597 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 22:58:02.598 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 22:58:02.598 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 183517.
12953:M 18 Jun 2025 23:16:22.551 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 23:16:22.553 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 23:16:22.553 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 183615.
12953:M 18 Jun 2025 23:24:35.402 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 23:24:35.405 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 23:24:35.405 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 183615.
12953:M 18 Jun 2025 23:39:14.593 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 18 Jun 2025 23:39:14.593 * Connection with replica 127.0.0.1:7103 lost.
12953:M 18 Jun 2025 23:39:14.597 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 18 Jun 2025 23:39:14.597 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 183685.
12953:M 19 Jun 2025 02:44:16.473 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 02:44:16.474 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 02:44:16.483 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 02:44:16.483 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 197782.
12953:M 19 Jun 2025 03:00:29.250 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 03:00:29.279 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 03:00:29.310 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 03:00:29.310 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 197852.
12953:M 19 Jun 2025 03:16:41.958 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 03:16:41.964 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 03:16:41.965 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 197922.
12953:M 19 Jun 2025 03:32:49.740 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 03:32:49.740 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 03:32:49.744 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 03:32:49.744 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 197978.
12953:M 19 Jun 2025 03:49:03.561 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 03:49:03.561 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 03:49:03.571 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 03:49:03.571 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 198034.
12953:M 19 Jun 2025 04:05:16.381 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 04:05:16.381 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 04:05:16.384 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 04:05:16.384 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 198104.
12953:M 19 Jun 2025 04:22:21.568 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 04:22:21.570 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 04:22:21.585 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 04:22:21.585 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 198230.
12953:M 19 Jun 2025 04:29:45.073 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 04:29:45.076 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 04:29:45.078 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 04:29:45.078 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 198300.
12953:M 19 Jun 2025 04:45:43.446 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 04:45:43.464 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 04:45:43.483 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 04:45:43.483 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 198370.
12953:M 19 Jun 2025 05:01:42.794 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 05:01:42.794 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 05:01:42.798 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 05:01:42.798 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 198426.
12953:M 19 Jun 2025 05:17:53.698 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 05:17:53.699 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 05:17:53.703 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 05:17:53.704 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 198482.
12953:M 19 Jun 2025 05:30:44.607 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 05:30:44.608 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 05:30:44.609 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 05:30:44.609 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 198552.
12953:M 19 Jun 2025 05:46:59.410 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 05:46:59.410 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 05:46:59.423 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 05:46:59.424 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 198608.
12953:M 19 Jun 2025 06:03:12.079 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 06:03:12.080 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 06:03:12.083 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 06:03:12.084 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 198678.
12953:M 19 Jun 2025 06:19:25.836 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 06:19:25.836 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 06:19:25.841 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 06:19:25.842 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 198748.
12953:M 19 Jun 2025 06:31:44.690 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 06:31:44.690 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 06:31:44.694 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 06:31:44.694 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 198804.
12953:M 19 Jun 2025 06:38:15.394 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 06:38:15.518 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 06:38:15.522 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 06:38:15.522 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 198874.
12953:M 19 Jun 2025 06:54:29.289 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 06:54:29.289 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 06:54:29.297 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 06:54:29.300 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 198944.
12953:M 19 Jun 2025 07:10:35.602 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 07:10:35.603 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 07:10:35.604 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 07:10:35.604 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 199000.
12953:M 19 Jun 2025 07:26:44.269 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 07:26:44.270 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 07:26:44.279 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 07:26:44.280 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 199056.
12953:M 19 Jun 2025 07:32:44.805 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 07:32:44.806 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 07:32:44.808 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 07:32:44.809 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 199126.
12953:M 19 Jun 2025 07:48:37.527 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 07:48:37.527 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 07:48:37.529 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 07:48:37.530 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 199196.
12953:M 19 Jun 2025 08:04:46.178 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 08:04:46.180 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 08:04:46.180 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 199252.
12953:M 19 Jun 2025 08:20:59.798 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 08:20:59.815 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 08:20:59.828 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 08:20:59.829 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 199308.
12953:M 19 Jun 2025 08:33:44.994 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 08:33:44.996 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 08:33:44.998 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 08:33:44.998 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 199378.
12953:M 19 Jun 2025 08:42:46.046 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 08:42:46.046 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 08:42:46.049 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 08:42:46.050 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 199448.
12953:M 19 Jun 2025 08:50:23.889 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 08:50:23.890 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 08:50:23.893 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 08:50:23.893 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 199504.
12953:M 19 Jun 2025 09:08:41.882 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 09:08:41.883 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 09:08:41.885 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 09:08:41.886 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 199574.
12953:M 19 Jun 2025 09:24:10.774 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 09:24:10.774 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 09:24:10.777 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 09:24:10.777 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 199574.
12953:M 19 Jun 2025 09:34:45.008 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 09:34:45.008 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 09:34:45.013 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 09:34:45.014 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 199672.
12953:M 19 Jun 2025 09:42:03.205 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 09:42:03.205 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 09:42:03.208 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 09:42:03.209 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 199728.
12953:M 19 Jun 2025 09:59:48.871 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 09:59:48.873 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 09:59:48.875 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 09:59:48.875 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 199854.
12953:M 19 Jun 2025 10:02:20.084 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 10:02:20.084 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 10:02:20.089 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 10:02:20.089 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 199910.
12953:M 19 Jun 2025 10:05:15.616 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 10:05:15.616 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 10:05:15.618 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 10:05:15.618 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 199980.
12953:M 19 Jun 2025 10:09:30.013 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 10:09:30.013 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 10:09:30.015 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 10:09:30.016 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 200036.
12953:M 19 Jun 2025 11:02:12.946 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 11:02:12.946 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 11:02:12.949 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 11:02:12.950 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 203732.
12953:M 19 Jun 2025 11:05:47.269 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 11:05:47.270 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 11:05:47.275 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 11:05:47.275 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 203802.
12953:M 19 Jun 2025 13:02:19.633 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 13:02:19.634 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 13:02:19.634 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 213126.
12953:M 19 Jun 2025 13:19:27.435 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 13:19:27.436 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 13:19:27.436 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 213182.
12953:M 19 Jun 2025 13:26:20.950 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 13:26:20.950 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 13:26:20.953 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 13:26:20.954 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 213462.
12953:M 19 Jun 2025 13:41:43.539 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 13:41:43.542 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 13:41:43.542 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 214512.
12953:M 19 Jun 2025 14:02:08.416 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 14:02:08.432 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 14:02:08.432 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 214876.
12953:M 19 Jun 2025 14:05:51.130 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 14:05:51.132 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 14:05:51.132 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 214946.
12953:M 19 Jun 2025 16:47:32.662 * 1 changes in 3600 seconds. Saving...
12953:M 19 Jun 2025 16:47:32.663 * Background saving started by pid 25964
25964:C 19 Jun 2025 16:47:32.669 * DB saved on disk
25964:C 19 Jun 2025 16:47:32.670 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 19 Jun 2025 16:47:32.766 * Background saving terminated with success
12953:M 19 Jun 2025 17:47:33.004 * 1 changes in 3600 seconds. Saving...
12953:M 19 Jun 2025 17:47:33.007 * Background saving started by pid 55064
55064:C 19 Jun 2025 17:47:33.021 * DB saved on disk
55064:C 19 Jun 2025 17:47:33.022 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 19 Jun 2025 17:47:33.110 * Background saving terminated with success
12953:M 19 Jun 2025 17:58:03.917 * 100 changes in 300 seconds. Saving...
12953:M 19 Jun 2025 17:58:03.922 * Background saving started by pid 60084
60084:C 19 Jun 2025 17:58:03.932 * DB saved on disk
60084:C 19 Jun 2025 17:58:03.933 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 19 Jun 2025 17:58:04.024 * Background saving terminated with success
12953:M 19 Jun 2025 18:03:05.059 * 100 changes in 300 seconds. Saving...
12953:M 19 Jun 2025 18:03:05.061 * Background saving started by pid 61996
61996:C 19 Jun 2025 18:03:05.069 * DB saved on disk
61996:C 19 Jun 2025 18:03:05.071 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 19 Jun 2025 18:03:05.163 * Background saving terminated with success
12953:M 19 Jun 2025 18:08:06.008 * 100 changes in 300 seconds. Saving...
12953:M 19 Jun 2025 18:08:06.010 * Background saving started by pid 64300
64300:C 19 Jun 2025 18:08:06.020 * DB saved on disk
64300:C 19 Jun 2025 18:08:06.020 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 19 Jun 2025 18:08:06.112 * Background saving terminated with success
12953:M 19 Jun 2025 18:13:07.099 * 100 changes in 300 seconds. Saving...
12953:M 19 Jun 2025 18:13:07.102 * Background saving started by pid 66342
66342:C 19 Jun 2025 18:13:07.112 * DB saved on disk
66342:C 19 Jun 2025 18:13:07.115 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 19 Jun 2025 18:13:07.204 * Background saving terminated with success
12953:M 19 Jun 2025 18:19:29.744 * 100 changes in 300 seconds. Saving...
12953:M 19 Jun 2025 18:19:29.746 * Background saving started by pid 69276
69276:C 19 Jun 2025 18:19:29.751 * DB saved on disk
69276:C 19 Jun 2025 18:19:29.751 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 19 Jun 2025 18:19:29.846 * Background saving terminated with success
12953:M 19 Jun 2025 18:20:30.088 * 10000 changes in 60 seconds. Saving...
12953:M 19 Jun 2025 18:20:30.092 * Background saving started by pid 69654
69654:C 19 Jun 2025 18:20:30.100 * DB saved on disk
69654:C 19 Jun 2025 18:20:30.101 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 19 Jun 2025 18:20:30.194 * Background saving terminated with success
12953:M 19 Jun 2025 18:25:31.085 * 100 changes in 300 seconds. Saving...
12953:M 19 Jun 2025 18:25:31.088 * Background saving started by pid 72061
72061:C 19 Jun 2025 18:25:31.096 * DB saved on disk
72061:C 19 Jun 2025 18:25:31.097 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 19 Jun 2025 18:25:31.190 * Background saving terminated with success
12953:M 19 Jun 2025 19:25:32.039 * 1 changes in 3600 seconds. Saving...
12953:M 19 Jun 2025 19:25:32.042 * Background saving started by pid 95563
95563:C 19 Jun 2025 19:25:32.058 * DB saved on disk
95563:C 19 Jun 2025 19:25:32.059 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 19 Jun 2025 19:25:32.145 * Background saving terminated with success
12953:M 19 Jun 2025 21:45:56.475 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 21:45:56.478 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 21:45:56.478 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 276582.
12953:M 19 Jun 2025 22:02:10.501 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 22:02:10.516 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 22:02:10.516 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 276596.
12953:M 19 Jun 2025 22:18:27.528 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 22:18:27.529 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 22:18:27.529 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 276596.
12953:M 19 Jun 2025 22:35:28.510 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 22:35:28.522 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 22:35:28.522 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 276610.
12953:M 19 Jun 2025 22:46:45.210 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 22:46:45.211 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 22:46:45.211 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 276610.
12953:M 19 Jun 2025 23:02:57.165 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 23:02:57.167 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 23:02:57.167 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 276680.
12953:M 19 Jun 2025 23:06:07.144 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 19 Jun 2025 23:06:07.144 * Connection with replica 127.0.0.1:7103 lost.
12953:M 19 Jun 2025 23:06:07.145 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 19 Jun 2025 23:06:07.145 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 276806.
12953:M 20 Jun 2025 02:02:51.803 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 02:02:51.803 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 02:02:51.806 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 02:02:51.807 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 289980.
12953:M 20 Jun 2025 02:13:57.851 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 02:13:57.852 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 02:13:57.862 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 02:13:57.862 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 289994.
12953:M 20 Jun 2025 02:31:48.565 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 02:31:48.565 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 02:31:48.566 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 02:31:48.567 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 290050.
12953:M 20 Jun 2025 02:47:37.939 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 02:47:37.940 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 02:47:37.942 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 02:47:37.943 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 290092.
12953:M 20 Jun 2025 03:03:50.985 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 03:03:50.993 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 03:03:50.994 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 290148.
12953:M 20 Jun 2025 03:20:03.062 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 03:20:03.064 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 03:20:03.064 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 290218.
12953:M 20 Jun 2025 03:36:16.520 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 03:36:16.520 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 03:36:16.524 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 03:36:16.524 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 290288.
12953:M 20 Jun 2025 03:46:00.808 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 03:46:00.808 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 03:46:00.821 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 03:46:00.822 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 290344.
12953:M 20 Jun 2025 04:02:13.467 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 04:02:13.467 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 04:02:13.469 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 04:02:13.469 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 290414.
12953:M 20 Jun 2025 04:18:25.845 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 04:18:25.847 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 04:18:25.847 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 290484.
12953:M 20 Jun 2025 04:34:39.207 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 04:34:39.207 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 04:34:39.215 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 04:34:39.215 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 290540.
12953:M 20 Jun 2025 04:50:52.000 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 04:50:52.012 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 04:50:52.032 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 04:50:52.033 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 290610.
12953:M 20 Jun 2025 05:06:42.584 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 05:06:42.587 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 05:06:42.587 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 290666.
12953:M 20 Jun 2025 05:22:51.505 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 05:22:51.506 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 05:22:51.507 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 290736.
12953:M 20 Jun 2025 05:39:04.939 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 05:39:04.939 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 05:39:04.943 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 05:39:04.943 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 290792.
12953:M 20 Jun 2025 05:55:17.346 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 05:55:17.347 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 05:55:17.347 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 290862.
12953:M 20 Jun 2025 06:11:30.701 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 06:11:30.701 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 06:11:30.703 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 06:11:30.704 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 290932.
12953:M 20 Jun 2025 06:27:43.025 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 06:27:43.027 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 06:27:43.028 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 290988.
12953:M 20 Jun 2025 06:43:56.341 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 06:43:56.342 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 06:43:56.347 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 06:43:56.347 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 291058.
12953:M 20 Jun 2025 07:00:09.364 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 07:00:09.366 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 07:00:09.366 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 291128.
12953:M 20 Jun 2025 07:16:22.703 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 07:16:22.703 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 07:16:22.718 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 07:16:22.718 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 291184.
12953:M 20 Jun 2025 07:33:30.374 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 07:33:30.374 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 07:33:30.390 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 07:33:30.390 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 291310.
12953:M 20 Jun 2025 07:40:52.564 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 07:40:52.566 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 07:40:52.566 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 291380.
12953:M 20 Jun 2025 07:57:06.064 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 07:57:06.065 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 07:57:06.072 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 07:57:06.072 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 291450.
12953:M 20 Jun 2025 08:13:16.337 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 08:13:16.455 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 08:13:16.458 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 08:13:16.458 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 291506.
12953:M 20 Jun 2025 08:29:26.226 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 08:29:26.227 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 08:29:26.230 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 08:29:26.230 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 291576.
12953:M 20 Jun 2025 08:46:31.481 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 08:46:31.482 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 08:46:31.486 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 08:46:31.486 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 291702.
12953:M 20 Jun 2025 09:02:22.679 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 09:02:22.679 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 09:02:22.683 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 09:02:22.683 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 291772.
12953:M 20 Jun 2025 09:04:10.485 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 09:04:10.608 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 09:04:10.611 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 09:04:10.611 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 291828.
12953:M 20 Jun 2025 09:21:58.359 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 09:21:58.384 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 09:21:58.407 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 09:21:58.407 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 291884.
12953:M 20 Jun 2025 09:39:40.338 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 09:39:40.399 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 09:39:40.402 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 09:39:40.402 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 291982.
12953:M 20 Jun 2025 09:41:52.341 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 09:41:52.356 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 09:41:52.365 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 09:41:52.366 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 291982.
12953:M 20 Jun 2025 09:52:01.359 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 09:52:01.360 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 09:52:01.363 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 09:52:01.364 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 292052.
12953:M 20 Jun 2025 13:09:00.483 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 13:09:00.484 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 13:09:00.484 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 307088.
12953:M 20 Jun 2025 13:44:50.670 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 13:44:50.672 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 13:44:50.672 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 308810.
12953:M 20 Jun 2025 14:07:23.346 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 14:07:23.350 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 14:07:23.350 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 309608.
12953:M 20 Jun 2025 17:45:00.513 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 17:45:00.519 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 17:45:00.519 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 326982.
12953:M 20 Jun 2025 17:54:03.830 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 17:54:03.841 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 17:54:03.844 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 17:54:03.844 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 327598.
12953:M 20 Jun 2025 18:29:47.524 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 18:29:47.524 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 18:29:47.527 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 18:29:47.528 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 329572.
12953:M 20 Jun 2025 18:52:57.896 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 18:52:57.898 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 18:52:57.898 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 330230.
12953:M 20 Jun 2025 19:48:27.435 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 19:48:27.436 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 19:48:27.436 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 333494.
12953:M 20 Jun 2025 20:02:19.729 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 20:02:19.845 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 20:02:19.847 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 20:02:19.847 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 333494.
12953:M 20 Jun 2025 20:13:27.044 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 20:13:27.044 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 20:13:27.049 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 20:13:27.050 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 334152.
12953:M 20 Jun 2025 20:15:34.845 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 20:15:34.846 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 20:15:34.851 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 20:15:34.851 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 334208.
12953:M 20 Jun 2025 20:33:46.841 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 20:33:46.841 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 20:33:46.844 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 20:33:46.844 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 334278.
12953:M 20 Jun 2025 20:50:05.738 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 20:50:05.738 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 20:50:05.742 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 20:50:05.742 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 334278.
12953:M 20 Jun 2025 21:05:35.628 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 21:05:35.629 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 21:05:35.632 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 21:05:35.632 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 334292.
12953:M 20 Jun 2025 21:16:34.529 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 21:16:34.529 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 21:16:34.533 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 21:16:34.533 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 334292.
12953:M 20 Jun 2025 21:35:18.561 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 21:35:18.561 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 21:35:18.565 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 21:35:18.565 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 334362.
12953:M 20 Jun 2025 21:48:41.097 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 21:48:41.098 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 21:48:41.102 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 21:48:41.102 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 334362.
12953:M 20 Jun 2025 22:05:42.327 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 22:05:42.442 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 22:05:42.444 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 22:05:42.445 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 334502.
12953:M 20 Jun 2025 22:17:34.825 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 22:17:34.825 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 22:17:34.834 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 22:17:34.834 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 334628.
12953:M 20 Jun 2025 22:22:19.487 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 22:22:19.525 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 22:22:19.532 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 22:22:19.544 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 334684.
12953:M 20 Jun 2025 22:40:16.665 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 22:40:16.666 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 22:40:16.672 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 22:40:16.673 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 334880.
12953:M 20 Jun 2025 22:53:51.362 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 20 Jun 2025 22:53:51.364 * Connection with replica 127.0.0.1:7103 lost.
12953:M 20 Jun 2025 22:53:51.372 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 20 Jun 2025 22:53:51.373 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 335020.
12953:M 20 Jun 2025 23:00:15.296 * 1 changes in 3600 seconds. Saving...
12953:M 20 Jun 2025 23:00:15.298 * Background saving started by pid 12242
12242:C 20 Jun 2025 23:00:15.309 * DB saved on disk
12242:C 20 Jun 2025 23:00:15.310 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 20 Jun 2025 23:00:15.399 * Background saving terminated with success
12953:M 21 Jun 2025 00:00:16.020 * 1 changes in 3600 seconds. Saving...
12953:M 21 Jun 2025 00:00:16.022 * Background saving started by pid 36035
36035:C 21 Jun 2025 00:00:16.050 * DB saved on disk
36035:C 21 Jun 2025 00:00:16.051 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 21 Jun 2025 00:00:16.125 * Background saving terminated with success
12953:M 21 Jun 2025 01:25:28.907 * Connection with replica 127.0.0.1:7103 lost.
12953:M 21 Jun 2025 01:25:28.908 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 21 Jun 2025 01:25:28.909 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 347233.
12953:M 21 Jun 2025 09:02:20.822 * Connection with replica 127.0.0.1:7103 lost.
12953:M 21 Jun 2025 09:02:20.826 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 21 Jun 2025 09:02:20.826 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 384640.
12953:M 21 Jun 2025 09:21:45.344 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 21 Jun 2025 09:21:45.345 * Connection with replica 127.0.0.1:7103 lost.
12953:M 21 Jun 2025 09:21:45.351 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 21 Jun 2025 09:21:45.351 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 384962.
12953:M 21 Jun 2025 09:39:33.179 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 21 Jun 2025 09:39:33.179 * Connection with replica 127.0.0.1:7103 lost.
12953:M 21 Jun 2025 09:39:33.183 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 21 Jun 2025 09:39:33.183 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 385158.
12953:M 21 Jun 2025 09:55:39.540 * Connection with replica 127.0.0.1:7103 lost.
12953:M 21 Jun 2025 09:55:39.543 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 21 Jun 2025 09:55:39.543 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 385214.
12953:M 21 Jun 2025 10:19:31.484 * Connection with replica 127.0.0.1:7103 lost.
12953:M 21 Jun 2025 10:19:31.486 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 21 Jun 2025 10:19:31.486 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 385914.
12953:M 21 Jun 2025 10:22:14.205 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 21 Jun 2025 10:22:14.228 * Connection with replica 127.0.0.1:7103 lost.
12953:M 21 Jun 2025 10:22:14.245 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 21 Jun 2025 10:22:14.245 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 385970.
12953:M 21 Jun 2025 10:24:42.053 * Connection with replica 127.0.0.1:7103 lost.
12953:M 21 Jun 2025 10:24:42.061 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 21 Jun 2025 10:24:42.061 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 386082.
12953:M 21 Jun 2025 10:44:36.559 * 1 changes in 3600 seconds. Saving...
12953:M 21 Jun 2025 10:44:36.562 * Background saving started by pid 40629
40629:C 21 Jun 2025 10:44:36.571 * DB saved on disk
40629:C 21 Jun 2025 10:44:36.571 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 21 Jun 2025 10:44:36.666 * Background saving terminated with success
12953:M 21 Jun 2025 11:44:37.036 * 1 changes in 3600 seconds. Saving...
12953:M 21 Jun 2025 11:44:37.039 * Background saving started by pid 68331
68331:C 21 Jun 2025 11:44:37.049 * DB saved on disk
68331:C 21 Jun 2025 11:44:37.051 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 21 Jun 2025 11:44:37.141 * Background saving terminated with success
12953:M 21 Jun 2025 16:18:22.831 * 1 changes in 3600 seconds. Saving...
12953:M 21 Jun 2025 16:18:22.832 * Background saving started by pid 75387
75387:C 21 Jun 2025 16:18:22.841 * DB saved on disk
75387:C 21 Jun 2025 16:18:22.841 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 21 Jun 2025 16:18:22.933 * Background saving terminated with success
12953:M 21 Jun 2025 18:32:46.894 * 1 changes in 3600 seconds. Saving...
12953:M 21 Jun 2025 18:32:46.895 * Background saving started by pid 33406
33406:C 21 Jun 2025 18:32:46.906 * DB saved on disk
33406:C 21 Jun 2025 18:32:46.907 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 21 Jun 2025 18:32:46.996 * Background saving terminated with success
12953:M 22 Jun 2025 10:19:44.554 * 1 changes in 3600 seconds. Saving...
12953:M 22 Jun 2025 10:19:44.555 * Background saving started by pid 78589
78589:C 22 Jun 2025 10:19:44.566 * DB saved on disk
78589:C 22 Jun 2025 10:19:44.567 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 22 Jun 2025 10:19:44.656 * Background saving terminated with success
12953:M 22 Jun 2025 11:19:45.101 * 1 changes in 3600 seconds. Saving...
12953:M 22 Jun 2025 11:19:45.102 * Background saving started by pid 3899
3899:C 22 Jun 2025 11:19:45.109 * DB saved on disk
3899:C 22 Jun 2025 11:19:45.110 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 22 Jun 2025 11:19:45.203 * Background saving terminated with success
12953:M 22 Jun 2025 12:19:46.092 * 1 changes in 3600 seconds. Saving...
12953:M 22 Jun 2025 12:19:46.095 * Background saving started by pid 27104
27104:C 22 Jun 2025 12:19:46.103 * DB saved on disk
27104:C 22 Jun 2025 12:19:46.103 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 22 Jun 2025 12:19:46.197 * Background saving terminated with success
12953:M 22 Jun 2025 16:14:50.517 * 1 changes in 3600 seconds. Saving...
12953:M 22 Jun 2025 16:14:50.519 * Background saving started by pid 12937
12937:C 22 Jun 2025 16:14:50.526 * DB saved on disk
12937:C 22 Jun 2025 16:14:50.527 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 22 Jun 2025 16:14:50.620 * Background saving terminated with success
12953:M 22 Jun 2025 17:14:51.076 * 1 changes in 3600 seconds. Saving...
12953:M 22 Jun 2025 17:14:51.077 * Background saving started by pid 36184
36184:C 22 Jun 2025 17:14:51.084 * DB saved on disk
36184:C 22 Jun 2025 17:14:51.085 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 22 Jun 2025 17:14:51.179 * Background saving terminated with success
12953:M 22 Jun 2025 19:47:01.224 * 1 changes in 3600 seconds. Saving...
12953:M 22 Jun 2025 19:47:01.226 * Background saving started by pid 91521
91521:C 22 Jun 2025 19:47:01.266 * DB saved on disk
91521:C 22 Jun 2025 19:47:01.268 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 22 Jun 2025 19:47:01.334 * Background saving terminated with success
12953:M 22 Jun 2025 20:47:02.081 * 1 changes in 3600 seconds. Saving...
12953:M 22 Jun 2025 20:47:02.084 * Background saving started by pid 15311
15311:C 22 Jun 2025 20:47:02.100 * DB saved on disk
15311:C 22 Jun 2025 20:47:02.102 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 22 Jun 2025 20:47:02.186 * Background saving terminated with success
12953:M 22 Jun 2025 23:43:21.108 * 1 changes in 3600 seconds. Saving...
12953:M 22 Jun 2025 23:43:21.109 * Background saving started by pid 84681
84681:C 22 Jun 2025 23:43:21.116 * DB saved on disk
84681:C 22 Jun 2025 23:43:21.118 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 22 Jun 2025 23:43:21.210 * Background saving terminated with success
12953:M 23 Jun 2025 00:43:22.099 * 1 changes in 3600 seconds. Saving...
12953:M 23 Jun 2025 00:43:22.100 * Background saving started by pid 10622
10622:C 23 Jun 2025 00:43:22.110 * DB saved on disk
10622:C 23 Jun 2025 00:43:22.111 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 23 Jun 2025 00:43:22.202 * Background saving terminated with success
12953:M 23 Jun 2025 08:46:05.858 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 23 Jun 2025 08:46:05.859 * Connection with replica 127.0.0.1:7103 lost.
12953:M 23 Jun 2025 08:46:05.863 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 23 Jun 2025 08:46:05.863 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 625100.
12953:M 23 Jun 2025 09:03:38.190 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 23 Jun 2025 09:03:38.190 * Connection with replica 127.0.0.1:7103 lost.
12953:M 23 Jun 2025 09:03:38.193 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 23 Jun 2025 09:03:38.193 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 625156.
12953:M 23 Jun 2025 09:14:34.088 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 23 Jun 2025 09:14:34.088 * Connection with replica 127.0.0.1:7103 lost.
12953:M 23 Jun 2025 09:14:34.094 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 23 Jun 2025 09:14:34.095 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 625170.
12953:M 23 Jun 2025 09:32:27.105 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 23 Jun 2025 09:32:27.106 * Connection with replica 127.0.0.1:7103 lost.
12953:M 23 Jun 2025 09:32:27.110 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 23 Jun 2025 09:32:27.111 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 14 bytes of backlog starting from offset 625226.
12953:M 23 Jun 2025 09:49:53.013 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 23 Jun 2025 09:49:53.013 * Connection with replica 127.0.0.1:7103 lost.
12953:M 23 Jun 2025 09:49:53.017 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 23 Jun 2025 09:49:53.018 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 625240.
12953:M 23 Jun 2025 10:05:30.633 * Connection with replica 127.0.0.1:7103 lost.
12953:M 23 Jun 2025 10:05:30.635 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 23 Jun 2025 10:05:30.635 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 626276.
12953:M 23 Jun 2025 10:30:20.796 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 23 Jun 2025 10:30:20.796 * Connection with replica 127.0.0.1:7103 lost.
12953:M 23 Jun 2025 10:30:20.811 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 23 Jun 2025 10:30:20.811 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 627368.
12953:M 23 Jun 2025 10:31:23.189 * 1 changes in 3600 seconds. Saving...
12953:M 23 Jun 2025 10:31:23.191 * Background saving started by pid 1433
1433:C 23 Jun 2025 10:31:23.202 * DB saved on disk
1433:C 23 Jun 2025 10:31:23.203 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 23 Jun 2025 10:31:23.293 * Background saving terminated with success
12953:M 23 Jun 2025 11:31:24.056 * 1 changes in 3600 seconds. Saving...
12953:M 23 Jun 2025 11:31:24.058 * Background saving started by pid 24704
24704:C 23 Jun 2025 11:31:24.079 * DB saved on disk
24704:C 23 Jun 2025 11:31:24.080 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 23 Jun 2025 11:31:24.160 * Background saving terminated with success
12953:M 23 Jun 2025 12:17:01.846 * Connection with replica 127.0.0.1:7103 lost.
12953:M 23 Jun 2025 12:17:01.849 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 23 Jun 2025 12:17:01.849 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 636275.
12953:M 23 Jun 2025 12:23:31.874 * Connection with replica 127.0.0.1:7103 lost.
12953:M 23 Jun 2025 12:23:31.876 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 23 Jun 2025 12:23:31.876 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 636387.
12953:M 23 Jun 2025 12:31:25.033 * 1 changes in 3600 seconds. Saving...
12953:M 23 Jun 2025 12:31:25.036 * Background saving started by pid 42790
42790:C 23 Jun 2025 12:31:25.054 * DB saved on disk
42790:C 23 Jun 2025 12:31:25.055 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 23 Jun 2025 12:31:25.137 * Background saving terminated with success
12953:M 23 Jun 2025 12:51:05.029 * Connection with replica 127.0.0.1:7103 lost.
12953:M 23 Jun 2025 12:51:05.039 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 23 Jun 2025 12:51:05.039 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 637675.
12953:M 23 Jun 2025 13:24:06.168 * Connection with replica 127.0.0.1:7103 lost.
12953:M 23 Jun 2025 13:24:06.170 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 23 Jun 2025 13:24:06.170 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 639047.
12953:M 23 Jun 2025 13:51:45.953 * Connection with replica 127.0.0.1:7103 lost.
12953:M 23 Jun 2025 13:51:45.957 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 23 Jun 2025 13:51:45.957 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 640082.
12953:M 23 Jun 2025 15:21:23.974 * Connection with replica 127.0.0.1:7103 lost.
12953:M 23 Jun 2025 15:21:23.977 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 23 Jun 2025 15:21:23.978 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 646522.
12953:M 23 Jun 2025 15:26:31.513 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7103
12953:M 23 Jun 2025 15:26:31.513 * Connection with replica 127.0.0.1:7103 lost.
12953:M 23 Jun 2025 15:26:31.516 * Replica 127.0.0.1:7103 asks for synchronization
12953:M 23 Jun 2025 15:26:31.516 * Partial resynchronization request from 127.0.0.1:7103 accepted. Sending 0 bytes of backlog starting from offset 646592.
12953:M 23 Jun 2025 15:31:08.073 * 1 changes in 3600 seconds. Saving...
12953:M 23 Jun 2025 15:31:08.074 * Background saving started by pid 88125
88125:C 23 Jun 2025 15:31:08.092 * DB saved on disk
88125:C 23 Jun 2025 15:31:08.093 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
12953:M 23 Jun 2025 15:31:08.175 * Background saving terminated with success
12953:signal-handler (1750669757) Received SIGTERM scheduling shutdown...
12953:M 23 Jun 2025 17:09:17.282 * User requested shutdown...
12953:M 23 Jun 2025 17:09:17.288 * 1 of 1 replicas are in sync when shutting down.
12953:M 23 Jun 2025 17:09:17.306 * Calling fsync() on the AOF file.
12953:M 23 Jun 2025 17:09:17.325 * Saving the final RDB snapshot before exiting.
12953:M 23 Jun 2025 17:09:17.354 * DB saved on disk
12953:M 23 Jun 2025 17:09:17.355 * Removing the pid file.
12953:M 23 Jun 2025 17:09:17.355 # Redis is now ready to exit, bye bye...
3138:C 23 Jun 2025 17:13:08.939 * oO0OoO0OoO0Oo Redis is starting oO0OoO0OoO0Oo
3138:C 23 Jun 2025 17:13:08.939 * Redis version=7.2.7, bits=64, commit=00000000, modified=0, pid=3138, just started
3138:C 23 Jun 2025 17:13:08.939 * Configuration loaded
3138:M 23 Jun 2025 17:13:08.940 * Increased maximum number of open files to 10032 (it was originally set to 256).
3138:M 23 Jun 2025 17:13:08.940 * monotonic clock: POSIX clock_gettime
3138:M 23 Jun 2025 17:13:08.940 * Running mode=cluster, port=7101.
3138:M 23 Jun 2025 17:13:08.940 # WARNING: The TCP backlog setting of 511 cannot be enforced because kern.ipc.somaxconn is set to the lower value of 128.
3138:M 23 Jun 2025 17:13:08.941 * Node configuration loaded, I'm 0c3e6389c113e15330ab84e33b2e38667cd6597a
3138:M 23 Jun 2025 17:13:08.941 * Server initialized
3138:M 23 Jun 2025 17:13:08.942 * Reading RDB base file on AOF loading...
3138:M 23 Jun 2025 17:13:08.942 * Loading RDB produced by version 7.2.7
3138:M 23 Jun 2025 17:13:08.942 * RDB age 1219118 seconds
3138:M 23 Jun 2025 17:13:08.942 * RDB memory usage when created 1.70 Mb
3138:M 23 Jun 2025 17:13:08.942 * RDB is base AOF
3138:M 23 Jun 2025 17:13:08.942 * Done loading RDB, keys loaded: 0, keys expired: 0.
3138:M 23 Jun 2025 17:13:08.943 * DB loaded from base file appendonly-7101.aof.1.base.rdb: 0.001 seconds
3138:M 23 Jun 2025 17:13:08.950 * DB saved on disk
3138:M 23 Jun 2025 17:13:08.955 * DB saved on disk
3138:M 23 Jun 2025 17:13:08.960 * DB saved on disk
3138:M 23 Jun 2025 17:13:08.966 * DB saved on disk
3138:M 23 Jun 2025 17:13:08.971 * DB saved on disk
3138:M 23 Jun 2025 17:13:08.976 * DB saved on disk
3138:M 23 Jun 2025 17:13:08.981 * DB saved on disk
3138:M 23 Jun 2025 17:13:08.986 * DB saved on disk
3138:M 23 Jun 2025 17:13:08.991 * DB saved on disk
3138:M 23 Jun 2025 17:13:08.996 * DB saved on disk
3138:M 23 Jun 2025 17:13:09.001 * DB saved on disk
3138:M 23 Jun 2025 17:13:09.006 * DB saved on disk
3138:M 23 Jun 2025 17:13:09.008 * DB loaded from incr file appendonly-7101.aof.1.incr.aof: 0.065 seconds
3138:M 23 Jun 2025 17:13:09.008 * DB loaded from append only file: 0.066 seconds
3138:M 23 Jun 2025 17:13:09.008 * Opening AOF incr file appendonly-7101.aof.1.incr.aof on server start
3138:M 23 Jun 2025 17:13:09.008 * Ready to accept connections tcp
3138:M 23 Jun 2025 17:13:11.053 * Cluster state changed: ok
3138:M 23 Jun 2025 17:13:13.044 * Replica 127.0.0.1:7103 asks for synchronization
3138:M 23 Jun 2025 17:13:13.044 * Partial resynchronization not accepted: Replication ID mismatch (Replica asked for '793077300d34591aecbfbb7c4b851a5677183207', my replication IDs are '11473ac0f5db3460b10043a7ded891e964c6fda0' and '0000000000000000000000000000000000000000')
3138:M 23 Jun 2025 17:13:13.044 * Replication backlog created, my new replication IDs are 'fb8eabff8275472dace6a44f701913c05a65d2e4' and '0000000000000000000000000000000000000000'
3138:M 23 Jun 2025 17:13:13.044 * Delay next BGSAVE for diskless SYNC
3138:M 23 Jun 2025 17:13:18.075 * Starting BGSAVE for SYNC with target: replicas sockets
3138:M 23 Jun 2025 17:13:18.078 * Background RDB transfer started by pid 3174
3174:C 23 Jun 2025 17:13:18.079 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3138:M 23 Jun 2025 17:13:18.079 * Diskless rdb transfer, done reading from pipe, 1 replicas still up.
3138:M 23 Jun 2025 17:13:18.100 * Background RDB transfer terminated with success
3138:M 23 Jun 2025 17:13:18.100 * Streamed RDB transfer with replica 127.0.0.1:7103 succeeded (socket). Waiting for REPLCONF ACK from replica to enable streaming
3138:M 23 Jun 2025 17:13:18.101 * Synchronization with replica 127.0.0.1:7103 succeeded
3138:M 23 Jun 2025 17:13:22.174 * DB saved on disk
3138:M 23 Jun 2025 17:13:22.180 * configEpoch set to 0 via CLUSTER RESET HARD
3138:M 23 Jun 2025 17:13:22.183 * Node hard reset, now I'm 9f1768eb210717eeb449ff0273dce5a9bc04a633
3138:M 23 Jun 2025 17:13:22.183 # Cluster state changed: fail
3138:M 23 Jun 2025 17:13:22.247 * Connection with replica 127.0.0.1:7103 lost.
3138:M 23 Jun 2025 17:13:25.361 * configEpoch set to 2 via CLUSTER SET-CONFIG-EPOCH
3138:M 23 Jun 2025 17:13:26.399 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 23 Jun 2025 17:13:26.400 * Partial resynchronization not accepted: Replication ID mismatch (Replica asked for '685e0e8fdf6929f2b92f8646cbf550ebd7adfb4b', my replication IDs are 'fb8eabff8275472dace6a44f701913c05a65d2e4' and '0000000000000000000000000000000000000000')
3138:M 23 Jun 2025 17:13:26.400 * Delay next BGSAVE for diskless SYNC
3138:M 23 Jun 2025 17:13:30.358 * Cluster state changed: ok
3138:M 23 Jun 2025 17:13:31.269 * Starting BGSAVE for SYNC with target: replicas sockets
3138:M 23 Jun 2025 17:13:31.275 * Background RDB transfer started by pid 3227
3227:C 23 Jun 2025 17:13:31.276 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3138:M 23 Jun 2025 17:13:31.277 * Diskless rdb transfer, done reading from pipe, 1 replicas still up.
3138:M 23 Jun 2025 17:13:31.325 * Background RDB transfer terminated with success
3138:M 23 Jun 2025 17:13:31.325 * Streamed RDB transfer with replica 127.0.0.1:7104 succeeded (socket). Waiting for REPLCONF ACK from replica to enable streaming
3138:M 23 Jun 2025 17:13:31.325 * Synchronization with replica 127.0.0.1:7104 succeeded
3138:M 23 Jun 2025 17:56:04.702 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 23 Jun 2025 17:56:04.702 * Connection with replica 127.0.0.1:7104 lost.
3138:M 23 Jun 2025 17:56:04.705 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 23 Jun 2025 17:56:04.705 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 3992.
3138:M 23 Jun 2025 18:02:19.784 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 23 Jun 2025 18:02:19.812 * Connection with replica 127.0.0.1:7104 lost.
3138:M 23 Jun 2025 18:02:19.814 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 23 Jun 2025 18:02:19.814 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 4062.
3138:M 23 Jun 2025 18:19:20.599 * 1 changes in 3600 seconds. Saving...
3138:M 23 Jun 2025 18:19:20.733 * Background saving started by pid 21095
21095:C 23 Jun 2025 18:19:20.752 * DB saved on disk
21095:C 23 Jun 2025 18:19:20.753 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3138:M 23 Jun 2025 18:19:20.836 * Background saving terminated with success
3138:M 23 Jun 2025 18:19:20.836 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 23 Jun 2025 18:19:20.836 * Connection with replica 127.0.0.1:7104 lost.
3138:M 23 Jun 2025 18:19:20.837 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 23 Jun 2025 18:19:20.837 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 4132.
3138:M 23 Jun 2025 18:42:16.810 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 23 Jun 2025 18:42:16.810 * Connection with replica 127.0.0.1:7104 lost.
3138:M 23 Jun 2025 18:42:16.813 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 23 Jun 2025 18:42:16.814 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 4748.
3138:M 23 Jun 2025 19:19:21.025 * 1 changes in 3600 seconds. Saving...
3138:M 23 Jun 2025 19:19:21.029 * Background saving started by pid 37265
37265:C 23 Jun 2025 19:19:21.060 * DB saved on disk
37265:C 23 Jun 2025 19:19:21.061 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3138:M 23 Jun 2025 19:19:21.137 * Background saving terminated with success
3138:M 23 Jun 2025 20:01:13.009 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 23 Jun 2025 20:01:13.010 * Connection with replica 127.0.0.1:7104 lost.
3138:M 23 Jun 2025 20:01:13.019 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 23 Jun 2025 20:01:13.019 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 11215.
3138:M 23 Jun 2025 20:16:16.780 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 23 Jun 2025 20:16:16.781 * Connection with replica 127.0.0.1:7104 lost.
3138:M 23 Jun 2025 20:16:16.784 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 23 Jun 2025 20:16:16.784 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 12139.
3138:M 23 Jun 2025 20:19:07.457 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 23 Jun 2025 20:19:07.457 * Connection with replica 127.0.0.1:7104 lost.
3138:M 23 Jun 2025 20:19:07.458 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 23 Jun 2025 20:19:07.458 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 12294.
3138:M 23 Jun 2025 20:26:43.285 * 1 changes in 3600 seconds. Saving...
3138:M 23 Jun 2025 20:26:43.286 * Background saving started by pid 59130
59130:C 23 Jun 2025 20:26:43.292 * DB saved on disk
59130:C 23 Jun 2025 20:26:43.292 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3138:M 23 Jun 2025 20:26:43.387 * Background saving terminated with success
3138:M 23 Jun 2025 21:16:42.842 * Connection with replica 127.0.0.1:7104 lost.
3138:M 23 Jun 2025 21:16:42.844 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 23 Jun 2025 21:16:42.845 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 17179.
3138:M 23 Jun 2025 21:33:24.306 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 23 Jun 2025 21:33:24.306 * Connection with replica 127.0.0.1:7104 lost.
3138:M 23 Jun 2025 21:33:24.313 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 23 Jun 2025 21:33:24.314 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 17249.
3138:M 23 Jun 2025 21:51:33.214 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 23 Jun 2025 21:51:33.214 * Connection with replica 127.0.0.1:7104 lost.
3138:M 23 Jun 2025 21:51:33.219 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 23 Jun 2025 21:51:33.219 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 17333.
3138:M 23 Jun 2025 22:07:42.129 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 23 Jun 2025 22:07:42.129 * Connection with replica 127.0.0.1:7104 lost.
3138:M 23 Jun 2025 22:07:42.135 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 23 Jun 2025 22:07:42.135 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 17333.
3138:M 23 Jun 2025 22:24:08.043 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 23 Jun 2025 22:24:08.043 * Connection with replica 127.0.0.1:7104 lost.
3138:M 23 Jun 2025 22:24:08.048 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 23 Jun 2025 22:24:08.048 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 17347.
3138:M 23 Jun 2025 22:39:27.956 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 23 Jun 2025 22:39:27.956 * Connection with replica 127.0.0.1:7104 lost.
3138:M 23 Jun 2025 22:39:27.960 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 23 Jun 2025 22:39:27.961 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 17347.
3138:M 23 Jun 2025 22:43:36.255 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 23 Jun 2025 22:43:36.292 * Connection with replica 127.0.0.1:7104 lost.
3138:M 23 Jun 2025 22:43:36.309 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 23 Jun 2025 22:43:36.309 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 17361.
3138:M 23 Jun 2025 22:51:30.397 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 23 Jun 2025 22:51:30.526 * Connection with replica 127.0.0.1:7104 lost.
3138:M 23 Jun 2025 22:51:30.529 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 23 Jun 2025 22:51:30.529 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 17431.
3138:M 23 Jun 2025 23:48:51.021 * 1 changes in 3600 seconds. Saving...
3138:M 23 Jun 2025 23:48:51.023 * Background saving started by pid 1791
1791:C 23 Jun 2025 23:48:51.032 * DB saved on disk
1791:C 23 Jun 2025 23:48:51.032 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3138:M 23 Jun 2025 23:48:51.128 * Background saving terminated with success
3138:M 24 Jun 2025 00:48:52.066 * 1 changes in 3600 seconds. Saving...
3138:M 24 Jun 2025 00:48:52.067 * Background saving started by pid 25619
25619:C 24 Jun 2025 00:48:52.078 * DB saved on disk
25619:C 24 Jun 2025 00:48:52.084 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3138:M 24 Jun 2025 00:48:52.169 * Background saving terminated with success
3138:M 24 Jun 2025 09:54:38.800 * Connection with replica 127.0.0.1:7104 lost.
3138:M 24 Jun 2025 09:54:38.976 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 24 Jun 2025 09:54:38.985 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 58557.
3138:M 24 Jun 2025 17:55:13.290 * Connection with replica 127.0.0.1:7104 lost.
3138:M 24 Jun 2025 17:55:13.293 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 24 Jun 2025 17:55:13.294 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 97463.
3138:M 24 Jun 2025 19:27:09.978 * 1 changes in 3600 seconds. Saving...
3138:M 24 Jun 2025 19:27:09.979 * Background saving started by pid 57770
57770:C 24 Jun 2025 19:27:09.986 * DB saved on disk
57770:C 24 Jun 2025 19:27:09.987 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3138:M 24 Jun 2025 19:27:10.081 * Background saving terminated with success
3138:M 24 Jun 2025 20:50:51.749 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 24 Jun 2025 20:50:51.749 * Connection with replica 127.0.0.1:7104 lost.
3138:M 24 Jun 2025 20:50:51.753 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 24 Jun 2025 20:50:51.753 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 111826.
3138:M 24 Jun 2025 21:38:34.397 * 1 changes in 3600 seconds. Saving...
3138:M 24 Jun 2025 21:38:34.399 * Background saving started by pid 7671
7671:C 24 Jun 2025 21:38:34.412 * DB saved on disk
7671:C 24 Jun 2025 21:38:34.412 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3138:M 24 Jun 2025 21:38:34.501 * Background saving terminated with success
3138:M 24 Jun 2025 22:13:06.535 * Connection with replica 127.0.0.1:7104 lost.
3138:M 24 Jun 2025 22:13:06.538 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 24 Jun 2025 22:13:06.538 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 118560.
3138:M 24 Jun 2025 22:31:42.089 * Connection with replica 127.0.0.1:7104 lost.
3138:M 24 Jun 2025 22:31:42.091 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 24 Jun 2025 22:31:42.091 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 118616.
3138:M 24 Jun 2025 22:46:59.001 * Connection with replica 127.0.0.1:7104 lost.
3138:M 24 Jun 2025 22:46:59.003 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 24 Jun 2025 22:46:59.003 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 118630.
3138:M 24 Jun 2025 23:04:30.894 * Connection with replica 127.0.0.1:7104 lost.
3138:M 24 Jun 2025 23:04:30.896 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 24 Jun 2025 23:04:30.896 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 118630.
3138:M 24 Jun 2025 23:10:30.575 * Connection with replica 127.0.0.1:7104 lost.
3138:M 24 Jun 2025 23:10:30.576 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 24 Jun 2025 23:10:30.576 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 118644.
3138:M 24 Jun 2025 23:14:25.102 * 1 changes in 3600 seconds. Saving...
3138:M 24 Jun 2025 23:14:25.103 * Background saving started by pid 22425
22425:C 24 Jun 2025 23:14:25.112 * DB saved on disk
22425:C 24 Jun 2025 23:14:25.113 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3138:M 24 Jun 2025 23:14:25.205 * Background saving terminated with success
3138:M 24 Jun 2025 23:30:21.742 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 24 Jun 2025 23:30:21.742 * Connection with replica 127.0.0.1:7104 lost.
3138:M 24 Jun 2025 23:30:21.743 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 24 Jun 2025 23:30:21.743 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 119287.
3138:M 24 Jun 2025 23:47:47.902 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 24 Jun 2025 23:47:47.902 * Connection with replica 127.0.0.1:7104 lost.
3138:M 24 Jun 2025 23:47:47.904 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 24 Jun 2025 23:47:47.904 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 119343.
3138:M 24 Jun 2025 23:49:00.167 * Connection with replica 127.0.0.1:7104 lost.
3138:M 24 Jun 2025 23:49:00.171 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 24 Jun 2025 23:49:00.171 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 119357.
3138:M 25 Jun 2025 00:14:26.009 * 1 changes in 3600 seconds. Saving...
3138:M 25 Jun 2025 00:14:26.012 * Background saving started by pid 33050
33050:C 25 Jun 2025 00:14:26.027 * DB saved on disk
33050:C 25 Jun 2025 00:14:26.029 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3138:M 25 Jun 2025 00:14:26.114 * Background saving terminated with success
3138:M 25 Jun 2025 01:14:27.047 * 1 changes in 3600 seconds. Saving...
3138:M 25 Jun 2025 01:14:27.049 * Background saving started by pid 51589
51589:C 25 Jun 2025 01:14:27.059 * DB saved on disk
51589:C 25 Jun 2025 01:14:27.061 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3138:M 25 Jun 2025 01:14:27.153 * Background saving terminated with success
3138:M 25 Jun 2025 02:03:12.897 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 02:03:12.897 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 02:03:12.900 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 02:03:12.900 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 129445.
3138:M 25 Jun 2025 02:19:02.674 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 02:19:02.792 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 02:19:02.814 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 02:19:02.814 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 129445.
3138:M 25 Jun 2025 02:20:42.537 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 02:20:42.538 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 02:20:42.543 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 02:20:42.544 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 129459.
3138:M 25 Jun 2025 02:22:36.868 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 02:22:36.868 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 02:22:36.872 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 02:22:36.873 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 129529.
3138:M 25 Jun 2025 02:39:14.706 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 02:39:14.721 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 02:39:14.738 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 02:39:14.738 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 129529.
3138:M 25 Jun 2025 02:56:03.587 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 02:56:03.722 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 02:56:03.724 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 02:56:03.724 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 129543.
3138:M 25 Jun 2025 03:12:20.598 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 03:12:20.715 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 03:12:20.724 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 03:12:20.725 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 129543.
3138:M 25 Jun 2025 03:29:16.188 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 03:29:16.188 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 03:29:16.192 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 03:29:16.192 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 129641.
3138:M 25 Jun 2025 03:45:28.958 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 03:45:28.958 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 03:45:28.966 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 03:45:28.967 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 129711.
3138:M 25 Jun 2025 04:01:41.772 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 04:01:41.773 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 04:01:41.779 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 04:01:41.780 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 129781.
3138:M 25 Jun 2025 04:17:54.632 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 04:17:54.634 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 04:17:54.638 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 04:17:54.639 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 129837.
3138:M 25 Jun 2025 04:23:40.328 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 04:23:40.331 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 04:23:40.339 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 04:23:40.339 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 129907.
3138:M 25 Jun 2025 04:39:53.112 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 04:39:53.115 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 04:39:53.115 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 129963.
3138:M 25 Jun 2025 04:56:05.125 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 04:56:05.131 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 04:56:05.132 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 130033.
3138:M 25 Jun 2025 05:12:17.890 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 05:12:17.891 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 05:12:17.896 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 05:12:17.897 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 130103.
3138:M 25 Jun 2025 05:24:40.422 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 05:24:40.423 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 05:24:40.429 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 05:24:40.430 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 130159.
3138:M 25 Jun 2025 05:40:53.222 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 05:40:53.223 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 05:40:53.225 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 05:40:53.226 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 130229.
3138:M 25 Jun 2025 05:57:06.054 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 05:57:06.054 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 05:57:06.057 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 05:57:06.057 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 130285.
3138:M 25 Jun 2025 06:13:18.881 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 06:13:18.881 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 06:13:18.883 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 06:13:18.883 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 130355.
3138:M 25 Jun 2025 06:25:40.021 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 06:25:40.022 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 06:25:40.025 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 06:25:40.026 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 130425.
3138:M 25 Jun 2025 06:41:52.857 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 06:41:52.857 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 06:41:52.860 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 06:41:52.861 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 130481.
3138:M 25 Jun 2025 06:58:05.691 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 06:58:05.692 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 06:58:05.697 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 06:58:05.697 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 130551.
3138:M 25 Jun 2025 07:14:10.004 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 07:14:10.005 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 07:14:10.009 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 07:14:10.009 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 130607.
3138:M 25 Jun 2025 07:26:39.905 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 07:26:39.905 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 07:26:39.908 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 07:26:39.908 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 130677.
3138:M 25 Jun 2025 07:42:51.897 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 07:42:51.927 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 07:42:51.929 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 07:42:51.930 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 130733.
3138:M 25 Jun 2025 07:59:04.721 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 07:59:04.721 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 07:59:04.725 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 07:59:04.726 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 130803.
3138:M 25 Jun 2025 08:02:20.153 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 08:02:20.153 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 08:02:20.156 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 08:02:20.156 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 130859.
3138:M 25 Jun 2025 08:18:32.929 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 08:18:32.929 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 08:18:32.936 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 08:18:32.936 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 130929.
3138:M 25 Jun 2025 08:27:39.685 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 08:27:39.721 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 08:27:39.737 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 08:27:39.737 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 130999.
3138:M 25 Jun 2025 08:30:28.684 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 08:30:28.720 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 08:30:28.724 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 08:30:28.725 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 131055.
3138:M 25 Jun 2025 08:47:32.028 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 08:47:32.043 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 08:47:32.070 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 08:47:32.070 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 131181.
3138:M 25 Jun 2025 09:01:17.932 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 09:01:17.969 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 09:01:17.988 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 09:01:17.988 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 131251.
3138:M 25 Jun 2025 09:18:56.615 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 09:18:56.615 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 09:18:56.619 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 09:18:56.619 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 131321.
3138:M 25 Jun 2025 09:28:39.490 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 09:28:39.528 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 09:28:39.542 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 09:28:39.543 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 131321.
3138:M 25 Jun 2025 09:44:51.596 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 09:44:51.596 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 09:44:51.599 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 09:44:51.600 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 131391.
3138:M 25 Jun 2025 09:58:07.973 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 09:58:07.995 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 09:58:08.005 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 09:58:08.006 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 131391.
3138:M 25 Jun 2025 10:07:43.214 * 1 changes in 3600 seconds. Saving...
3138:M 25 Jun 2025 10:07:43.215 * Background saving started by pid 62216
62216:C 25 Jun 2025 10:07:43.222 * DB saved on disk
62216:C 25 Jun 2025 10:07:43.223 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3138:M 25 Jun 2025 10:07:43.316 * Background saving terminated with success
3138:M 25 Jun 2025 11:56:25.451 * 1 changes in 3600 seconds. Saving...
3138:M 25 Jun 2025 11:56:25.453 * Background saving started by pid 3733
3733:C 25 Jun 2025 11:56:25.459 * DB saved on disk
3733:C 25 Jun 2025 11:56:25.461 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3138:M 25 Jun 2025 11:56:25.555 * Background saving terminated with success
3138:M 25 Jun 2025 12:56:26.037 * 1 changes in 3600 seconds. Saving...
3138:M 25 Jun 2025 12:56:26.041 * Background saving started by pid 27644
27644:C 25 Jun 2025 12:56:26.054 * DB saved on disk
27644:C 25 Jun 2025 12:56:26.055 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3138:M 25 Jun 2025 12:56:26.145 * Background saving terminated with success
3138:M 25 Jun 2025 14:28:38.607 * 1 changes in 3600 seconds. Saving...
3138:M 25 Jun 2025 14:28:38.611 * Background saving started by pid 64083
64083:C 25 Jun 2025 14:28:38.620 * DB saved on disk
64083:C 25 Jun 2025 14:28:38.621 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3138:M 25 Jun 2025 14:28:38.713 * Background saving terminated with success
3138:M 25 Jun 2025 15:28:39.014 * 1 changes in 3600 seconds. Saving...
3138:M 25 Jun 2025 15:28:39.017 * Background saving started by pid 96408
96408:C 25 Jun 2025 15:28:39.033 * DB saved on disk
96408:C 25 Jun 2025 15:28:39.034 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3138:M 25 Jun 2025 15:28:39.120 * Background saving terminated with success
3138:M 25 Jun 2025 16:28:40.039 * 1 changes in 3600 seconds. Saving...
3138:M 25 Jun 2025 16:28:40.041 * Background saving started by pid 58152
58152:C 25 Jun 2025 16:28:40.063 * DB saved on disk
58152:C 25 Jun 2025 16:28:40.065 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3138:M 25 Jun 2025 16:28:40.143 * Background saving terminated with success
3138:M 25 Jun 2025 17:28:41.065 * 1 changes in 3600 seconds. Saving...
3138:M 25 Jun 2025 17:28:41.068 * Background saving started by pid 2238
2238:C 25 Jun 2025 17:28:41.086 * DB saved on disk
2238:C 25 Jun 2025 17:28:41.087 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3138:M 25 Jun 2025 17:28:41.170 * Background saving terminated with success
3138:M 25 Jun 2025 18:10:22.942 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 18:10:22.942 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 18:10:22.943 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 18:10:22.943 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 179966.
3138:M 25 Jun 2025 18:33:01.714 * 1 changes in 3600 seconds. Saving...
3138:M 25 Jun 2025 18:33:01.716 * Background saving started by pid 19717
19717:C 25 Jun 2025 18:33:01.724 * DB saved on disk
19717:C 25 Jun 2025 18:33:01.725 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3138:M 25 Jun 2025 18:33:01.820 * Background saving terminated with success
3138:M 25 Jun 2025 19:45:07.206 * 1 changes in 3600 seconds. Saving...
3138:M 25 Jun 2025 19:45:07.208 * Background saving started by pid 47007
47007:C 25 Jun 2025 19:45:07.222 * DB saved on disk
47007:C 25 Jun 2025 19:45:07.223 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3138:M 25 Jun 2025 19:45:07.312 * Background saving terminated with success
3138:M 25 Jun 2025 21:54:15.163 * 1 changes in 3600 seconds. Saving...
3138:M 25 Jun 2025 21:54:15.166 * Background saving started by pid 97149
97149:C 25 Jun 2025 21:54:15.179 * DB saved on disk
97149:C 25 Jun 2025 21:54:15.179 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3138:M 25 Jun 2025 21:54:15.268 * Background saving terminated with success
3138:M 25 Jun 2025 22:49:16.315 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 22:49:16.317 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 22:49:16.318 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 203482.
3138:M 25 Jun 2025 23:06:52.736 * 1 changes in 3600 seconds. Saving...
3138:M 25 Jun 2025 23:06:52.855 * Background saving started by pid 21469
3138:M 25 Jun 2025 23:06:52.856 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 23:06:52.856 * Connection with replica 127.0.0.1:7104 lost.
21469:C 25 Jun 2025 23:06:52.865 * DB saved on disk
21469:C 25 Jun 2025 23:06:52.866 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3138:M 25 Jun 2025 23:06:52.867 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 23:06:52.867 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 203552.
3138:M 25 Jun 2025 23:06:52.958 * Background saving terminated with success
3138:M 25 Jun 2025 23:24:50.870 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 23:24:50.871 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 23:24:50.878 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 23:24:50.878 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 203552.
3138:M 25 Jun 2025 23:42:12.752 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 23:42:12.872 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 23:42:12.888 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 23:42:12.892 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 203566.
3138:M 25 Jun 2025 23:57:23.863 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 25 Jun 2025 23:57:23.875 * Connection with replica 127.0.0.1:7104 lost.
3138:M 25 Jun 2025 23:57:23.883 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 25 Jun 2025 23:57:23.884 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 203566.
3138:M 26 Jun 2025 00:12:52.332 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 00:12:52.332 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 00:12:52.336 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 00:12:52.336 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 203650.
3138:M 26 Jun 2025 00:23:10.058 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 00:23:10.058 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 00:23:10.061 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 00:23:10.061 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 203720.
3138:M 26 Jun 2025 00:39:21.930 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 00:39:21.930 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 00:39:21.931 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 00:39:21.931 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 203790.
3138:M 26 Jun 2025 00:48:59.867 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 00:48:59.867 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 00:48:59.884 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 00:48:59.884 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 203846.
3138:M 26 Jun 2025 00:57:42.134 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 00:57:42.136 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 00:57:42.136 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 203972.
3138:M 26 Jun 2025 01:20:12.040 * 1 changes in 3600 seconds. Saving...
3138:M 26 Jun 2025 01:20:12.041 * Background saving started by pid 32734
32734:C 26 Jun 2025 01:20:12.046 * DB saved on disk
32734:C 26 Jun 2025 01:20:12.047 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3138:M 26 Jun 2025 01:20:12.142 * Background saving terminated with success
3138:M 26 Jun 2025 02:20:13.085 * 1 changes in 3600 seconds. Saving...
3138:M 26 Jun 2025 02:20:13.089 * Background saving started by pid 57604
57604:C 26 Jun 2025 02:20:13.098 * DB saved on disk
57604:C 26 Jun 2025 02:20:13.099 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3138:M 26 Jun 2025 02:20:13.192 * Background saving terminated with success
3138:M 26 Jun 2025 03:17:55.274 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 03:17:55.275 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 03:17:55.278 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 03:17:55.278 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 215289.
3138:M 26 Jun 2025 03:34:07.213 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 03:34:07.214 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 03:34:07.220 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 03:34:07.220 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 215359.
3138:M 26 Jun 2025 03:50:07.818 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 03:50:07.819 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 03:50:07.819 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 215415.
3138:M 26 Jun 2025 04:06:16.118 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 04:06:16.118 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 04:06:16.121 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 04:06:16.122 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 215471.
3138:M 26 Jun 2025 04:22:28.664 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 04:22:28.670 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 04:22:28.671 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 215541.
3138:M 26 Jun 2025 04:38:41.693 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 04:38:41.693 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 04:38:41.696 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 04:38:41.696 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 215611.
3138:M 26 Jun 2025 04:54:54.464 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 04:54:54.497 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 04:54:54.523 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 04:54:54.523 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 215667.
3138:M 26 Jun 2025 05:02:00.658 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 05:02:00.779 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 05:02:00.786 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 05:02:00.786 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 215737.
3138:M 26 Jun 2025 05:18:13.489 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 05:18:13.611 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 05:18:13.627 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 05:18:13.627 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 41 bytes of backlog starting from offset 215807.
3138:M 26 Jun 2025 05:34:26.441 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 05:34:26.454 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 05:34:26.469 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 05:34:26.476 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 215904.
3138:M 26 Jun 2025 05:50:39.169 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 05:50:39.173 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 05:50:39.179 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 05:50:39.180 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 215974.
3138:M 26 Jun 2025 06:03:00.624 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 06:03:00.765 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 06:03:00.769 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 06:03:00.769 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 216030.
3138:M 26 Jun 2025 06:19:13.477 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 06:19:13.610 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 06:19:13.612 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 06:19:13.613 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 216100.
3138:M 26 Jun 2025 06:35:25.581 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 06:35:25.614 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 06:35:25.617 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 06:35:25.617 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 216170.
3138:M 26 Jun 2025 06:51:38.438 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 06:51:38.438 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 06:51:38.441 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 06:51:38.441 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 216226.
3138:M 26 Jun 2025 07:04:01.568 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 07:04:01.569 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 07:04:01.578 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 07:04:01.579 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 216296.
3138:M 26 Jun 2025 07:20:14.369 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 07:20:14.369 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 07:20:14.374 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 07:20:14.375 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 216352.
3138:M 26 Jun 2025 07:36:26.978 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 07:36:26.978 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 07:36:26.993 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 07:36:26.994 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 216422.
3138:M 26 Jun 2025 07:51:27.951 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 07:51:27.951 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 07:51:27.956 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 07:51:27.957 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 216492.
3138:M 26 Jun 2025 08:05:01.321 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 08:05:01.322 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 08:05:01.332 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 08:05:01.333 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 216548.
3138:M 26 Jun 2025 08:21:14.042 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 08:21:14.044 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 08:21:14.045 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 216618.
3138:M 26 Jun 2025 08:37:26.761 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 08:37:26.761 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 08:37:26.770 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 08:37:26.770 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 216688.
3138:M 26 Jun 2025 08:53:39.523 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 08:53:39.523 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 08:53:39.526 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 08:53:39.527 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 216744.
3138:M 26 Jun 2025 08:57:52.625 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 08:57:52.625 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 08:57:52.628 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 08:57:52.629 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 216814.
3138:M 26 Jun 2025 09:00:46.508 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 09:00:46.542 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 09:00:46.546 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 09:00:46.546 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 216870.
3138:M 26 Jun 2025 09:06:00.891 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 09:06:00.892 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 09:06:00.895 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 09:06:00.895 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 216940.
3138:M 26 Jun 2025 09:22:55.937 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 09:22:55.940 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 09:22:55.940 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 217010.
3138:M 26 Jun 2025 09:40:41.905 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 09:40:41.906 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 09:40:41.909 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 09:40:41.909 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 217010.
3138:M 26 Jun 2025 09:56:17.814 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 09:56:17.854 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 09:56:17.866 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 09:56:17.866 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 217024.
3138:M 26 Jun 2025 09:59:33.239 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 09:59:33.239 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 09:59:33.243 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 09:59:33.243 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 217108.
3138:M 26 Jun 2025 10:03:37.269 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 10:03:37.270 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 10:03:37.273 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 10:03:37.273 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 217164.
3138:M 26 Jun 2025 10:47:36.645 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 10:47:36.648 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 10:47:36.648 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 219446.
3138:M 26 Jun 2025 12:29:09.440 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 12:29:09.440 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 12:29:09.451 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 12:29:09.452 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 226404.
3138:M 26 Jun 2025 12:42:31.534 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 12:42:31.534 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 12:42:31.542 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 12:42:31.543 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 226460.
3138:M 26 Jun 2025 12:58:21.183 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 12:58:21.183 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 12:58:21.187 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 12:58:21.187 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 226530.
3138:M 26 Jun 2025 13:21:56.855 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 13:21:56.855 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 13:21:56.859 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 13:21:56.859 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 227048.
3138:M 26 Jun 2025 13:44:59.771 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 13:44:59.772 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 13:44:59.787 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 13:44:59.787 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 227552.
3138:M 26 Jun 2025 14:06:29.781 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 14:06:29.784 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 14:06:29.784 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 228126.
3138:M 26 Jun 2025 14:17:09.068 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 14:17:09.091 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 14:17:09.091 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 228196.
3138:M 26 Jun 2025 18:33:10.681 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 18:33:10.685 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 18:33:10.687 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 18:33:10.687 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 248974.
3138:M 26 Jun 2025 19:19:42.680 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 19:19:42.683 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 19:19:42.683 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 252726.
3138:M 26 Jun 2025 19:25:49.957 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 19:25:49.960 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 19:25:49.960 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 252852.
3138:M 26 Jun 2025 19:43:49.059 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 19:43:49.061 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 19:43:49.061 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 252908.
3138:M 26 Jun 2025 20:00:43.950 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 20:00:43.953 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 20:00:43.953 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 252922.
3138:M 26 Jun 2025 20:17:32.847 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 20:17:32.850 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 20:17:32.850 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 252922.
3138:M 26 Jun 2025 20:34:46.769 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 20:34:46.772 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 20:34:46.772 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 252936.
3138:M 26 Jun 2025 20:50:04.752 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 20:50:04.756 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 20:50:04.756 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 252936.
3138:M 26 Jun 2025 20:56:37.959 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 26 Jun 2025 20:56:37.988 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 20:56:37.991 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 20:56:37.991 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 253020.
3138:M 26 Jun 2025 21:20:32.351 * Connection with replica 127.0.0.1:7104 lost.
3138:M 26 Jun 2025 21:20:32.365 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 26 Jun 2025 21:20:32.365 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 254616.
3138:M 27 Jun 2025 02:05:39.547 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 02:05:39.550 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 02:05:39.550 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 277008.
3138:M 27 Jun 2025 02:21:52.266 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 02:21:52.288 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 02:21:52.288 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 277078.
3138:M 27 Jun 2025 02:38:04.789 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 02:38:04.791 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 02:38:04.791 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 277134.
3138:M 27 Jun 2025 02:54:14.725 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 02:54:14.727 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 02:54:14.727 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 277204.
3138:M 27 Jun 2025 03:10:27.517 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 03:10:27.520 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 03:10:27.525 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 03:10:27.525 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 277260.
3138:M 27 Jun 2025 03:26:40.158 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 03:26:40.159 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 03:26:40.163 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 03:26:40.163 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 277330.
3138:M 27 Jun 2025 03:38:50.479 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 03:38:50.480 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 03:38:50.494 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 03:38:50.495 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 277386.
3138:M 27 Jun 2025 03:50:28.909 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 03:50:28.909 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 03:50:28.912 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 03:50:28.912 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 277456.
3138:M 27 Jun 2025 04:06:37.677 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 04:06:37.709 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 04:06:37.725 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 04:06:37.725 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 277526.
3138:M 27 Jun 2025 04:22:50.691 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 04:22:50.692 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 04:22:50.699 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 04:22:50.700 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 277582.
3138:M 27 Jun 2025 04:38:59.896 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 04:38:59.896 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 04:38:59.900 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 04:38:59.900 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 277652.
3138:M 27 Jun 2025 04:55:16.743 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 04:55:16.744 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 04:55:16.748 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 04:55:16.748 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 277722.
3138:M 27 Jun 2025 05:11:29.455 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 05:11:29.455 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 05:11:29.458 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 05:11:29.458 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 277792.
3138:M 27 Jun 2025 05:27:42.177 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 05:27:42.177 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 05:27:42.178 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 05:27:42.178 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 277848.
3138:M 27 Jun 2025 05:43:55.012 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 05:43:55.013 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 05:43:55.017 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 05:43:55.017 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 277918.
3138:M 27 Jun 2025 06:00:07.689 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 06:00:07.689 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 06:00:07.693 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 06:00:07.694 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 277974.
3138:M 27 Jun 2025 06:16:20.633 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 06:16:20.636 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 06:16:20.636 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 278044.
3138:M 27 Jun 2025 06:32:33.427 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 06:32:33.429 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 06:32:33.429 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 278114.
3138:M 27 Jun 2025 06:48:46.235 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 06:48:46.237 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 06:48:46.238 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 278170.
3138:M 27 Jun 2025 07:04:58.944 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 07:04:58.946 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 07:04:58.946 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 278240.
3138:M 27 Jun 2025 07:21:11.942 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 07:21:11.945 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 07:21:11.946 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 278310.
3138:M 27 Jun 2025 07:37:24.651 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 07:37:24.653 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 07:37:24.653 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 278366.
3138:M 27 Jun 2025 07:42:07.893 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 07:42:07.893 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 07:42:07.896 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 07:42:07.896 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 278436.
3138:M 27 Jun 2025 07:55:12.793 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 07:55:12.812 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 07:55:12.815 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 07:55:12.815 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 278492.
3138:M 27 Jun 2025 08:11:24.787 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 08:11:24.790 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 08:11:24.792 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 08:11:24.792 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 278562.
3138:M 27 Jun 2025 08:27:37.317 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 08:27:37.319 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 08:27:37.319 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 278632.
3138:M 27 Jun 2025 08:30:17.519 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 08:30:17.520 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 08:30:17.538 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 08:30:17.538 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 278688.
3138:M 27 Jun 2025 08:44:38.217 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 08:44:38.218 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 08:44:38.224 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 08:44:38.225 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 278758.
3138:M 27 Jun 2025 09:01:28.799 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 09:01:28.813 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 09:01:28.821 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 09:01:28.822 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 278814.
3138:M 27 Jun 2025 09:17:15.794 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 09:17:15.824 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 09:17:15.831 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 09:17:15.832 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 278828.
3138:M 27 Jun 2025 09:34:20.787 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 09:34:20.824 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 09:34:20.845 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 09:34:20.845 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 278828.
3138:M 27 Jun 2025 09:43:07.828 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 09:43:07.829 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 09:43:07.843 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 09:43:07.844 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 278842.
3138:M 27 Jun 2025 11:21:30.752 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 11:21:30.753 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 11:21:30.756 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 11:21:30.756 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 286668.
3138:M 27 Jun 2025 11:26:42.325 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 11:26:42.326 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 11:26:42.329 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 11:26:42.329 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 286738.
3138:M 27 Jun 2025 12:59:42.675 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 12:59:42.675 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 12:59:42.679 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 12:59:42.680 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 293178.
3138:M 27 Jun 2025 13:15:54.893 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 13:15:54.893 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 13:15:54.898 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 13:15:54.898 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 293276.
3138:M 27 Jun 2025 13:32:19.756 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 13:32:19.756 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 13:32:19.759 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 13:32:19.760 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 293346.
3138:M 27 Jun 2025 13:37:04.987 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 13:37:04.987 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 13:37:04.991 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 13:37:04.991 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 293402.
3138:M 27 Jun 2025 13:53:54.762 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 13:53:54.763 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 13:53:54.772 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 13:53:54.772 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 293780.
3138:M 27 Jun 2025 14:43:29.357 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 14:43:29.358 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 14:43:29.365 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 14:43:29.365 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 296902.
3138:M 27 Jun 2025 17:05:44.145 * 1 changes in 3600 seconds. Saving...
3138:M 27 Jun 2025 17:05:44.146 * Background saving started by pid 58416
58416:C 27 Jun 2025 17:05:44.153 * DB saved on disk
58416:C 27 Jun 2025 17:05:44.158 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3138:M 27 Jun 2025 17:05:44.248 * Background saving terminated with success
3138:M 27 Jun 2025 18:05:45.022 * 1 changes in 3600 seconds. Saving...
3138:M 27 Jun 2025 18:05:45.025 * Background saving started by pid 4994
4994:C 27 Jun 2025 18:05:45.038 * DB saved on disk
4994:C 27 Jun 2025 18:05:45.039 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3138:M 27 Jun 2025 18:05:45.128 * Background saving terminated with success
3138:M 27 Jun 2025 19:25:34.136 * 1 changes in 3600 seconds. Saving...
3138:M 27 Jun 2025 19:25:34.137 * Background saving started by pid 60446
60446:C 27 Jun 2025 19:25:34.144 * DB saved on disk
60446:C 27 Jun 2025 19:25:34.146 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3138:M 27 Jun 2025 19:25:34.237 * Background saving terminated with success
3138:M 27 Jun 2025 19:39:03.691 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 19:39:03.693 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 19:39:03.693 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 321922.
3138:M 27 Jun 2025 19:57:53.280 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 19:57:53.282 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 19:57:53.283 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 322062.
3138:M 27 Jun 2025 20:04:52.280 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 20:04:52.287 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 20:04:52.287 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 322146.
3138:M 27 Jun 2025 20:21:15.194 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 20:21:15.196 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 20:21:15.196 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 322216.
3138:M 27 Jun 2025 20:39:10.229 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 20:39:10.231 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 20:39:10.232 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 322216.
3138:M 27 Jun 2025 20:56:29.199 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 20:56:29.199 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 20:56:29.201 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 20:56:29.202 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 322230.
3138:M 27 Jun 2025 21:13:48.107 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 21:13:48.107 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 21:13:48.110 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 21:13:48.111 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 322230.
3138:M 27 Jun 2025 21:29:02.014 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 21:29:02.014 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 21:29:02.017 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 21:29:02.017 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 322244.
3138:M 27 Jun 2025 21:38:41.711 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 21:38:41.845 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 21:38:41.847 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 21:38:41.847 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 322314.
3138:M 27 Jun 2025 21:54:54.601 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 21:54:54.719 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 21:54:54.721 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 21:54:54.721 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 322370.
3138:M 27 Jun 2025 22:11:07.063 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 22:11:07.188 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 22:11:07.191 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 22:11:07.191 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 322440.
3138:M 27 Jun 2025 22:27:24.568 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 22:27:24.704 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 22:27:24.714 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 22:27:24.714 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 322496.
3138:M 27 Jun 2025 22:39:41.491 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 22:39:41.497 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 22:39:41.507 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 22:39:41.507 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 322566.
3138:M 27 Jun 2025 22:55:58.883 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 22:55:58.888 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 22:55:58.890 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 22:55:58.890 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 322636.
3138:M 27 Jun 2025 23:12:11.004 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 23:12:11.126 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 23:12:11.128 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 23:12:11.128 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 322692.
3138:M 27 Jun 2025 23:29:27.389 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 23:29:27.390 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 23:29:27.398 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 23:29:27.399 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 322832.
3138:M 27 Jun 2025 23:40:42.434 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 23:40:42.434 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 23:40:42.438 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 23:40:42.438 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 322958.
3138:M 27 Jun 2025 23:56:55.201 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 27 Jun 2025 23:56:55.202 * Connection with replica 127.0.0.1:7104 lost.
3138:M 27 Jun 2025 23:56:55.207 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 27 Jun 2025 23:56:55.208 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 323014.
3138:M 28 Jun 2025 00:13:07.905 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 28 Jun 2025 00:13:07.905 * Connection with replica 127.0.0.1:7104 lost.
3138:M 28 Jun 2025 00:13:07.909 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 28 Jun 2025 00:13:07.909 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 323084.
3138:M 28 Jun 2025 00:23:31.874 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 28 Jun 2025 00:23:31.874 * Connection with replica 127.0.0.1:7104 lost.
3138:M 28 Jun 2025 00:23:31.877 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 28 Jun 2025 00:23:31.878 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 323154.
3138:M 28 Jun 2025 01:29:19.956 * 1 changes in 3600 seconds. Saving...
3138:M 28 Jun 2025 01:29:19.957 * Background saving started by pid 33591
33591:C 28 Jun 2025 01:29:19.971 * DB saved on disk
33591:C 28 Jun 2025 01:29:19.971 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3138:M 28 Jun 2025 01:29:20.059 * Background saving terminated with success
3138:M 28 Jun 2025 03:06:27.639 * Connection with replica 127.0.0.1:7104 lost.
3138:M 28 Jun 2025 03:06:27.646 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 28 Jun 2025 03:06:27.646 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 335557.
3138:M 28 Jun 2025 03:22:39.520 * Connection with replica 127.0.0.1:7104 lost.
3138:M 28 Jun 2025 03:22:39.525 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 28 Jun 2025 03:22:39.526 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 335613.
3138:M 28 Jun 2025 03:38:26.054 * Connection with replica 127.0.0.1:7104 lost.
3138:M 28 Jun 2025 03:38:26.057 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 28 Jun 2025 03:38:26.057 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 335683.
3138:M 28 Jun 2025 03:54:15.869 * Connection with replica 127.0.0.1:7104 lost.
3138:M 28 Jun 2025 03:54:15.871 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 28 Jun 2025 03:54:15.871 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 335739.
3138:M 28 Jun 2025 04:10:46.075 * Connection with replica 127.0.0.1:7104 lost.
3138:M 28 Jun 2025 04:10:46.077 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 28 Jun 2025 04:10:46.077 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 335837.
3138:M 28 Jun 2025 04:17:52.163 * Connection with replica 127.0.0.1:7104 lost.
3138:M 28 Jun 2025 04:17:52.168 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 28 Jun 2025 04:17:52.169 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 335907.
3138:M 28 Jun 2025 04:46:45.882 * Connection with replica 127.0.0.1:7104 lost.
3138:M 28 Jun 2025 04:46:45.884 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 28 Jun 2025 04:46:45.884 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 336957.
3138:M 28 Jun 2025 05:13:40.508 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 28 Jun 2025 05:13:40.529 * Connection with replica 127.0.0.1:7104 lost.
3138:M 28 Jun 2025 05:13:40.537 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 28 Jun 2025 05:13:40.537 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 337909.
3138:M 28 Jun 2025 05:38:03.806 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 28 Jun 2025 05:38:03.806 * Connection with replica 127.0.0.1:7104 lost.
3138:M 28 Jun 2025 05:38:03.823 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 28 Jun 2025 05:38:03.823 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 338609.
3138:M 28 Jun 2025 05:50:52.596 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 28 Jun 2025 05:50:52.732 * Connection with replica 127.0.0.1:7104 lost.
3138:M 28 Jun 2025 05:50:52.736 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 28 Jun 2025 05:50:52.736 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 338679.
3138:M 28 Jun 2025 06:09:13.660 * Connection with replica 127.0.0.1:7104 lost.
3138:M 28 Jun 2025 06:09:13.662 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 28 Jun 2025 06:09:13.662 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 338917.
3138:M 28 Jun 2025 06:27:28.029 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 28 Jun 2025 06:27:28.047 * Connection with replica 127.0.0.1:7104 lost.
3138:M 28 Jun 2025 06:27:28.061 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 28 Jun 2025 06:27:28.061 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 339155.
3138:M 28 Jun 2025 06:43:40.186 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 28 Jun 2025 06:43:40.186 * Connection with replica 127.0.0.1:7104 lost.
3138:M 28 Jun 2025 06:43:40.190 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 28 Jun 2025 06:43:40.190 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 339211.
3138:M 28 Jun 2025 06:51:53.171 * Connection with replica 127.0.0.1:7104 lost.
3138:M 28 Jun 2025 06:51:53.174 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 28 Jun 2025 06:51:53.174 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 339281.
3138:M 28 Jun 2025 07:08:21.413 * Connection with replica 127.0.0.1:7104 lost.
3138:M 28 Jun 2025 07:08:21.416 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 28 Jun 2025 07:08:21.417 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 339365.
3138:M 28 Jun 2025 07:32:40.828 * Connection with replica 127.0.0.1:7104 lost.
3138:M 28 Jun 2025 07:32:40.830 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 28 Jun 2025 07:32:40.830 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 340065.
3138:M 28 Jun 2025 07:48:52.864 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 28 Jun 2025 07:48:52.865 * Connection with replica 127.0.0.1:7104 lost.
3138:M 28 Jun 2025 07:48:52.867 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 28 Jun 2025 07:48:52.867 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 340135.
3138:M 28 Jun 2025 08:52:53.108 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 28 Jun 2025 08:52:53.108 * Connection with replica 127.0.0.1:7104 lost.
3138:M 28 Jun 2025 08:52:53.112 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 28 Jun 2025 08:52:53.112 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 14 bytes of backlog starting from offset 345189.
3138:M 28 Jun 2025 09:08:45.986 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 28 Jun 2025 09:08:45.986 * Connection with replica 127.0.0.1:7104 lost.
3138:M 28 Jun 2025 09:08:45.990 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 28 Jun 2025 09:08:45.991 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 345273.
3138:M 28 Jun 2025 09:24:57.948 # Disconnecting timedout replica (streaming sync): 127.0.0.1:7104
3138:M 28 Jun 2025 09:24:57.984 * Connection with replica 127.0.0.1:7104 lost.
3138:M 28 Jun 2025 09:24:57.995 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 28 Jun 2025 09:24:57.995 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 345329.
3138:M 28 Jun 2025 10:42:30.694 * Connection with replica 127.0.0.1:7104 lost.
3138:M 28 Jun 2025 10:42:30.694 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 28 Jun 2025 10:42:30.694 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 350425.
3138:M 28 Jun 2025 12:28:09.534 * Connection with replica 127.0.0.1:7104 lost.
3138:M 28 Jun 2025 12:28:09.539 * Replica 127.0.0.1:7104 asks for synchronization
3138:M 28 Jun 2025 12:28:09.539 * Partial resynchronization request from 127.0.0.1:7104 accepted. Sending 0 bytes of backlog starting from offset 358027.
3138:M 28 Jun 2025 13:10:52.234 * 1 changes in 3600 seconds. Saving...
3138:M 28 Jun 2025 13:10:52.236 * Background saving started by pid 19205
19205:C 28 Jun 2025 13:10:52.243 * DB saved on disk
19205:C 28 Jun 2025 13:10:52.249 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
3138:M 28 Jun 2025 13:10:52.338 * Background saving terminated with success
3138:signal-handler (1751132261) Received SIGTERM scheduling shutdown...
3138:M 29 Jun 2025 01:37:41.382 * Connection with replica 127.0.0.1:7104 lost.
3138:M 29 Jun 2025 01:37:41.386 * User requested shutdown...
3138:M 29 Jun 2025 01:37:41.390 * Calling fsync() on the AOF file.
3138:M 29 Jun 2025 01:37:41.394 * Saving the final RDB snapshot before exiting.
3138:M 29 Jun 2025 01:37:41.408 * DB saved on disk
3138:M 29 Jun 2025 01:37:41.408 * Removing the pid file.
3138:M 29 Jun 2025 01:37:41.409 # Redis is now ready to exit, bye bye...
4322:C 29 Jun 2025 01:39:36.551 * oO0OoO0OoO0Oo Redis is starting oO0OoO0OoO0Oo
4322:C 29 Jun 2025 01:39:36.551 * Redis version=7.2.7, bits=64, commit=00000000, modified=0, pid=4322, just started
4322:C 29 Jun 2025 01:39:36.551 * Configuration loaded
4322:M 29 Jun 2025 01:39:36.552 * monotonic clock: POSIX clock_gettime
4322:M 29 Jun 2025 01:39:36.552 * Running mode=cluster, port=7101.
4322:M 29 Jun 2025 01:39:36.552 # WARNING: The TCP backlog setting of 511 cannot be enforced because kern.ipc.somaxconn is set to the lower value of 128.
4322:M 29 Jun 2025 01:39:36.553 * Node configuration loaded, I'm 9f1768eb210717eeb449ff0273dce5a9bc04a633
4322:M 29 Jun 2025 01:39:36.553 * Server initialized
4322:M 29 Jun 2025 01:39:36.554 * Reading RDB base file on AOF loading...
4322:M 29 Jun 2025 01:39:36.554 * Loading RDB produced by version 7.2.7
4322:M 29 Jun 2025 01:39:36.554 * RDB age 1681506 seconds
4322:M 29 Jun 2025 01:39:36.554 * RDB memory usage when created 1.70 Mb
4322:M 29 Jun 2025 01:39:36.555 * RDB is base AOF
4322:M 29 Jun 2025 01:39:36.555 * Done loading RDB, keys loaded: 0, keys expired: 0.
4322:M 29 Jun 2025 01:39:36.555 * DB loaded from base file appendonly-7101.aof.1.base.rdb: 0.001 seconds
4322:M 29 Jun 2025 01:39:36.566 * DB saved on disk
4322:M 29 Jun 2025 01:39:36.571 * DB saved on disk
4322:M 29 Jun 2025 01:39:36.576 * DB saved on disk
4322:M 29 Jun 2025 01:39:36.586 * DB saved on disk
4322:M 29 Jun 2025 01:39:36.591 * DB saved on disk
4322:M 29 Jun 2025 01:39:36.596 * DB saved on disk
4322:M 29 Jun 2025 01:39:36.601 * DB saved on disk
4322:M 29 Jun 2025 01:39:36.605 * DB saved on disk
4322:M 29 Jun 2025 01:39:36.610 * DB saved on disk
4322:M 29 Jun 2025 01:39:36.614 * DB saved on disk
4322:M 29 Jun 2025 01:39:36.621 * DB saved on disk
4322:M 29 Jun 2025 01:39:36.626 * DB saved on disk
4322:M 29 Jun 2025 01:39:36.632 * DB saved on disk
4322:M 29 Jun 2025 01:39:36.632 * DB loaded from incr file appendonly-7101.aof.1.incr.aof: 0.077 seconds
4322:M 29 Jun 2025 01:39:36.632 * DB loaded from append only file: 0.079 seconds
4322:M 29 Jun 2025 01:39:36.633 * Opening AOF incr file appendonly-7101.aof.1.incr.aof on server start
4322:M 29 Jun 2025 01:39:36.633 * Ready to accept connections tcp
4322:M 29 Jun 2025 01:39:38.671 * Cluster state changed: ok
4322:M 29 Jun 2025 01:39:42.703 * Replica 127.0.0.1:7104 asks for synchronization
4322:M 29 Jun 2025 01:39:42.704 * Partial resynchronization not accepted: Replication ID mismatch (Replica asked for 'b5e33dbef3f0b20121786be8b84a948268d5298f', my replication IDs are '9b0b95ffcebd121bc718b06b129ee9f39112aa89' and '0000000000000000000000000000000000000000')
4322:M 29 Jun 2025 01:39:42.704 * Replication backlog created, my new replication IDs are '62e97dc8d5b7d028130bf81389f7a66a6b73f765' and '0000000000000000000000000000000000000000'
4322:M 29 Jun 2025 01:39:42.704 * Delay next BGSAVE for diskless SYNC
4322:M 29 Jun 2025 01:39:47.731 * Starting BGSAVE for SYNC with target: replicas sockets
4322:M 29 Jun 2025 01:39:47.732 * Background RDB transfer started by pid 4444
4444:C 29 Jun 2025 01:39:47.732 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
4322:M 29 Jun 2025 01:39:47.733 * Diskless rdb transfer, done reading from pipe, 1 replicas still up.
4322:M 29 Jun 2025 01:39:47.751 * Background RDB transfer terminated with success
4322:M 29 Jun 2025 01:39:47.751 * Streamed RDB transfer with replica 127.0.0.1:7104 succeeded (socket). Waiting for REPLCONF ACK from replica to enable streaming
4322:M 29 Jun 2025 01:39:47.751 * Synchronization with replica 127.0.0.1:7104 succeeded
4322:M 29 Jun 2025 01:39:49.762 * DB saved on disk
4322:M 29 Jun 2025 01:39:49.770 * configEpoch set to 0 via CLUSTER RESET HARD
4322:M 29 Jun 2025 01:39:49.775 * Node hard reset, now I'm e389babb1d2fe1894d34be04d733360eb96d7922
4322:M 29 Jun 2025 01:39:49.775 # Cluster state changed: fail
4322:M 29 Jun 2025 01:39:49.879 * Connection with replica 127.0.0.1:7104 lost.
4322:M 29 Jun 2025 01:39:52.938 * configEpoch set to 2 via CLUSTER SET-CONFIG-EPOCH
4322:M 29 Jun 2025 01:39:54.961 * Replica 127.0.0.1:7103 asks for synchronization
4322:M 29 Jun 2025 01:39:54.962 * Partial resynchronization not accepted: Replication ID mismatch (Replica asked for 'c9f5923fa1ab5ceb1aaa8fd86278cea2d37f1921', my replication IDs are '62e97dc8d5b7d028130bf81389f7a66a6b73f765' and '0000000000000000000000000000000000000000')
4322:M 29 Jun 2025 01:39:54.962 * Delay next BGSAVE for diskless SYNC
4322:M 29 Jun 2025 01:39:57.974 * Cluster state changed: ok
4322:M 29 Jun 2025 01:39:59.900 * Starting BGSAVE for SYNC with target: replicas sockets
4322:M 29 Jun 2025 01:39:59.900 * Background RDB transfer started by pid 4571
4571:C 29 Jun 2025 01:39:59.900 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
4322:M 29 Jun 2025 01:39:59.900 * Diskless rdb transfer, done reading from pipe, 1 replicas still up.
4322:M 29 Jun 2025 01:39:59.916 * Background RDB transfer terminated with success
4322:M 29 Jun 2025 01:39:59.916 * Streamed RDB transfer with replica 127.0.0.1:7103 succeeded (socket). Waiting for REPLCONF ACK from replica to enable streaming
4322:M 29 Jun 2025 01:39:59.916 * Synchronization with replica 127.0.0.1:7103 succeeded
