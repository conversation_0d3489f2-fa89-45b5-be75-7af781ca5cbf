# Redis Cluster 多机部署配置文件
# 编辑此文件来配置您的集群节点

# 集群节点列表
# 格式: IP:PORT
# 建议: 至少3个主节点，每个主节点配置1个从节点
CLUSTER_NODES=(
    # 服务器1 - 主节点
    "**************:7100"
    "**************:7101" 
    "**************:7102"
    
    # 服务器2 - 从节点
    "*************:7100"
    "*************:7100"
    "*************:7100"
)

# 集群配置
REDIS_PASSWORD="redis123456"
CLUSTER_REPLICAS=1

# 网络配置
CLUSTER_TIMEOUT=15000
CLUSTER_ANNOUNCE_BUS_PORT_OFFSET=10000

# 资源配置
MAX_MEMORY="256mb"
MAX_MEMORY_POLICY="allkeys-lru"

# 持久化配置
ENABLE_AOF=true
AOF_REWRITE_PERCENTAGE=100
AOF_REWRITE_MIN_SIZE="64mb"

# 日志配置
LOG_LEVEL="notice"

# 部署配置
REDIS_USER="redis"
REDIS_HOME="/opt/redis"
SYSTEMD_SERVICE=true 