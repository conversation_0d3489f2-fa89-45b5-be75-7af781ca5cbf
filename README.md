# LinkBuy 供应链管理系统

## 项目概述

LinkBuy 是一个基于 Spring Boot 的分布式供应链管理系统，采用微服务架构设计，支持供应商管理、商城运营、平台管理等多种业务场景。系统集成了事件总线、分布式锁、邮件通知等功能，确保在分布式环境下的数据一致性和业务可靠性。

## 技术栈

- **框架**: Spring Boot 2.x
- **数据库**: MySQL 8.0
- **缓存**: Redis 6.x
- **消息队列**: Redis Pub/Sub
- **ORM**: MyBatis-Plus
- **构建工具**: Maven 3.x
- **容器化**: Docker
- **日志**: Logback + SLF4J

## 项目结构

```
LinkBuy/
├── component/                          # 核心组件模块
│   ├── common/                         # 通用组件
│   │   ├── event/                      # 事件总线相关
│   │   ├── service/                    # 通用服务（邮件服务等）
│   │   └── enums/                      # 枚举定义
│   ├── mysql-dao/                      # MySQL数据访问层
│   │   ├── entity/                     # 实体类
│   │   ├── service/                    # 数据服务
│   │   └── task/                       # 定时任务
│   └── redis-dao/                      # Redis数据访问层
│       ├── event/                      # Redis事件处理
│       ├── lock/                       # 分布式锁
│       └── config/                     # Redis配置
├── internal-service/                   # 内部服务模块
├── mall-admin-api/                     # 商城管理API模块
├── mall-api/                          # 小程序API模块
├── platform-api/                      # 平台管理API模块
├── supplier-api/                      # 供应商管理API模块
└── doc/                               # 项目文档
```

## 模块说明

### 核心组件模块 (component/)

#### common - 通用组件
- **功能**: 提供系统通用的基础功能和工具类
- **主要内容**:
  - 事件总线基础类和接口
  - 邮件发送服务
  - 通用枚举定义
  - 工具类和常量定义

#### mysql-dao - MySQL数据访问层
- **功能**: 封装所有MySQL数据库操作
- **主要内容**:
  - 实体类定义 (Entity)
  - 数据访问服务 (Service)
  - 定时任务 (Task)
  - 数据库配置

#### redis-dao - Redis数据访问层
- **功能**: 封装所有Redis操作和分布式功能
- **主要内容**:
  - Redis事件总线实现
  - 分布式锁实现
  - Redis配置和连接管理

### API服务模块

#### internal-service - 内部服务
- **功能**: 处理系统内部业务逻辑，不对外提供API接口
- **主要用途**:
  - **事件监听和处理**: 统一处理所有业务事件监听
  - 邮件通知发送
  - 数据统计更新
  - 后台数据处理
  - 定时任务执行
  - 内部业务逻辑处理
- **特点**: 纯内部服务，不暴露HTTP接口，专注于事件驱动的业务处理

#### mall-admin-api - 商城管理API
- **功能**: 为商城管理后台提供API接口
- **主要用途**:
  - 商品管理
  - 订单管理
  - 用户管理
  - 营销活动管理
  - 数据统计分析
- **访问对象**: 商城管理员

#### mall-api - 小程序API
- **功能**: 为微信小程序等移动端应用提供API接口
- **主要用途**:
  - 商品展示和搜索
  - 购物车管理
  - 订单处理
  - 用户账户管理
  - 支付接口
- **访问对象**: 终端用户（消费者）

#### platform-api - 平台管理API
- **功能**: 为平台管理系统提供API接口
- **主要用途**:
  - 系统配置管理
  - 权限管理
  - 数据监控
  - 系统维护
  - 多租户管理
- **访问对象**: 平台管理员

#### supplier-api - 供应商管理API
- **功能**: 为供应商管理系统提供API接口
- **主要用途**:
  - 供应商信息管理
  - 供应商用户管理
  - 资质证书管理
  - 产品管理
  - 订单处理
- **访问对象**: 供应商用户

## 核心功能

### 分布式事件总线
- 基于 Redis Pub/Sub 实现
- 支持跨节点事件传播
- 自动事件路由和分发
- 异步事件处理
- **事件发布**: API服务发布业务事件
- **事件监听**: internal-service统一处理所有事件监听

### 分布式锁
- 基于 Redis 实现
- 支持超时自动释放
- 确保定时任务单节点执行

### 邮件通知系统
- 统一的邮件发送服务
- 支持HTML邮件模板
- 多种业务场景通知

### 定时任务
- 分布式定时任务支持
- 资质过期检查
- 数据清理和统计

## 部署架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   mall-api      │    │ mall-admin-api  │    │  platform-api   │
│   (小程序API)    │    │   (商城管理)     │    │   (平台管理)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  supplier-api   │    │ internal-service│    │     Gateway     │
│   (供应商管理)   │    │   (内部服务)     │    │   (API网关)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     MySQL       │    │     Redis       │    │   File Storage  │
│   (主数据库)     │    │  (缓存/消息)     │    │   (文件存储)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 快速开始

### 环境要求
- JDK 8+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+
- Docker (可选)

### 本地开发

1. **克隆项目**
```bash
git clone <repository-url>
cd LinkBuy
```

2. **配置数据库**
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE linkbuy DEFAULT CHARACTER SET utf8mb4;
```

3. **配置Redis**
```bash
# 启动Redis服务
redis-server
```

4. **配置应用**
```bash
# 复制配置文件模板
cp application-example.yml application.yml
# 修改数据库和Redis连接配置
```

5. **启动服务**
```bash
# 启动内部服务
cd internal-service && mvn spring-boot:run

# 启动API服务
cd supplier-api && mvn spring-boot:run
cd mall-api && mvn spring-boot:run
cd platform-api && mvn spring-boot:run
cd mall-admin-api && mvn spring-boot:run
```

### Docker部署

```bash
# 构建镜像
docker build -t linkbuy/supplier-api ./supplier-api
docker build -t linkbuy/mall-api ./mall-api
docker build -t linkbuy/platform-api ./platform-api
docker build -t linkbuy/mall-admin-api ./mall-admin-api
docker build -t linkbuy/internal-service ./internal-service

# 使用docker-compose启动
docker-compose up -d
```

## 配置说明

### 数据库配置
```yaml
spring:
  datasource:
    url: ***********************************
    username: root
    password: your-password
```

### Redis配置
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    password: your-password
```

### 邮件配置
```yaml
mail:
  smtp:
    host: smtp.163.com
    username: <EMAIL>
    password: your-password
```

## API文档

各模块API文档访问地址：
- 供应商API: http://localhost:8081/swagger-ui.html
- 商城API: http://localhost:8082/swagger-ui.html
- 平台API: http://localhost:8083/swagger-ui.html
- 商城管理API: http://localhost:8084/swagger-ui.html

## 监控和日志

### 应用监控
- Spring Boot Actuator
- 健康检查: `/actuator/health`
- 指标监控: `/actuator/metrics`

### 日志配置
```yaml
logging:
  level:
    com.linkBuy: INFO
    org.springframework: WARN
  file:
    name: logs/linkbuy.log
```

## 开发规范

### 代码规范
- 遵循阿里巴巴Java开发手册
- 使用Lombok减少样板代码
- 统一异常处理
- 接口文档使用Swagger

### 数据库规范
- 表名使用下划线命名
- 字段名使用驼峰命名
- 必须有创建时间和更新时间字段
- 软删除使用deleted字段

### API规范
- RESTful API设计
- 统一响应格式
- 统一错误码
- 接口版本控制

### 事件总线规范
- **事件发布**: 只在API服务中发布事件
- **事件监听**: 只在internal-service中处理事件监听
- **职责分离**: 确保事件处理逻辑的集中管理

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目维护者: [维护者姓名]
- 邮箱: [联系邮箱]
- 项目地址: [项目仓库地址]

## 更新日志

查看 [CHANGELOG.md](CHANGELOG.md) 了解版本更新详情
