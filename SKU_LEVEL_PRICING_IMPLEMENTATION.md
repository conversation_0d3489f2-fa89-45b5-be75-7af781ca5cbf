# SKU级别精确调价功能实现

## 🎯 问题解决

**原问题**: 用户在前端为每个SKU设置了不同的价格，但服务端接口只能为整个商品设置统一价格，导致需要使用平均价格的妥协方案。

**解决方案**: 扩展服务端接口，支持SKU级别的精确调价，让每个SKU都能设置为用户指定的价格。

## 🔧 后端修改

### 1. 扩展调价请求DTO

**文件**: `LinkBuy/mall-admin-api/src/main/java/com/linkBuy/mallAdminApi/dto/request/CompanyProductPriceUpdateRequest.java`

**新增字段**:
```java
/**
 * 调价方式：增加 SET_SKU_PRICE-设置SKU级别价格
 */
private String priceUpdateType;

/**
 * SKU级别的价格设置（当priceUpdateType为SET_SKU_PRICE时使用）
 */
private List<SkuPriceUpdate> skuPriceUpdates;

@Data
public static class SkuPriceUpdate {
    private Long skuId;        // SKU ID
    private BigDecimal newPrice; // 新价格
    private BigDecimal oldPrice; // 原价格（用于记录）
}
```

### 2. 扩展控制器方法

**文件**: `LinkBuy/mall-admin-api/src/main/java/com/linkBuy/mallAdminApi/controller/CompanyProductManagementController.java`

**新增方法**:
```java
/**
 * SKU级别的精确调价
 */
private void batchUpdateSkuPriceWithDetails(List<CompanyProductPriceUpdateRequest.SkuPriceUpdate> skuPriceUpdates, Long companyId) {
    for (CompanyProductPriceUpdateRequest.SkuPriceUpdate skuUpdate : skuPriceUpdates) {
        // 根据供应商SKU ID查找对应的客户SKU
        BizCompanySku companySku = bizCompanySkuService.getOne(
            new LambdaQueryWrapper<BizCompanySku>()
                .eq(BizCompanySku::getCompanyId, companyId)
                .eq(BizCompanySku::getSkuId, skuUpdate.getSkuId())
        );
        
        if (companySku != null && skuUpdate.getNewPrice() != null) {
            BigDecimal newPrice = skuUpdate.getNewPrice();
            if (newPrice.compareTo(BigDecimal.ZERO) < 0) {
                newPrice = BigDecimal.ZERO;
            }
            
            companySku.setSalePrice(newPrice);
            companySku.setUpdateTime(LocalDateTime.now());
            bizCompanySkuService.updateById(companySku);
        }
    }
}
```

**修改批量调价逻辑**:
```java
// 批量调价
if ("SET_SKU_PRICE".equals(priceUpdateType)) {
    // SKU级别的精确调价
    batchUpdateSkuPriceWithDetails(request.getSkuPriceUpdates(), sessionUser.getCompanyId());
    
    // 重新计算所有相关SPU的价格范围
    for (BizCompanySpu companySpu : spuList) {
        updateCompanySpuPriceRange(companySpu);
    }
} else {
    // 传统的统一调价方式
    for (BizCompanySpu companySpu : spuList) {
        batchUpdateSkuPrice(companySpu, priceUpdateType, value, sessionUser.getCompanyId());
        updateCompanySpuPriceRange(companySpu);
    }
}
```

## 🎨 前端修改

**文件**: `LinkBuy-FED/linkBuyMallAdmin/src/views/goods/companyProduct/index.vue`

**修改调价逻辑**:
```javascript
// 构建SKU级别的调价数据
const skuPriceUpdates = currentPriceProduct.value.skuList.map((sku: any) => ({
  skuId: sku.id, // 供应商SKU ID
  newPrice: sku.adjustedSalePrice,
  oldPrice: sku.salePrice
}))

// 获取涉及的商品ID列表
let productIds: number[] = []
if (currentPriceProduct.value.id === 'batch') {
  // 批量调价：获取所有涉及的商品ID
  const uniqueProductIds = [...new Set(currentPriceProduct.value.skuList.map((sku: any) => Number(sku.productId)))]
  productIds = uniqueProductIds as number[]
} else {
  // 单个商品调价
  productIds = [Number(currentPriceProduct.value.id)]
}

// 使用SKU级别的精确调价
await batchUpdatePrice({
  ids: productIds,
  priceUpdateType: 'SET_SKU_PRICE',
  reason: priceAdjustForm.reason,
  skuPriceUpdates: skuPriceUpdates
})
```

## 📊 接口对比

### 修改前
```javascript
// 前端发送
{
  ids: [productId],
  priceUpdateType: 'SET_PRICE',
  value: 统一价格, // ❌ 所有SKU使用同一价格
  reason: '调价原因'
}

// 后端处理
for (BizCompanySku sku : allSkus) {
  sku.setSalePrice(value); // ❌ 所有SKU设置相同价格
}
```

### 修改后
```javascript
// 前端发送
{
  ids: [productId1, productId2, ...],
  priceUpdateType: 'SET_SKU_PRICE',
  reason: '调价原因',
  skuPriceUpdates: [
    { skuId: 1, newPrice: 100.00, oldPrice: 90.00 }, // ✅ 每个SKU独立价格
    { skuId: 2, newPrice: 120.00, oldPrice: 110.00 },
    // ...
  ]
}

// 后端处理
for (SkuPriceUpdate update : skuPriceUpdates) {
  BizCompanySku sku = findBySkuId(update.getSkuId());
  sku.setSalePrice(update.getNewPrice()); // ✅ 每个SKU设置指定价格
}
```

## 🎯 功能特点

### ✅ 解决的问题
1. **精确调价**: 每个SKU都能设置为用户指定的价格
2. **批量支持**: 支持多商品、多SKU的批量精确调价
3. **价格记录**: 记录原价格和新价格，便于审计
4. **自动更新**: 自动重新计算商品的价格范围

### ✅ 兼容性
1. **向后兼容**: 保留原有的统一调价方式（SET_PRICE、FIXED_AMOUNT、PERCENTAGE）
2. **渐进增强**: 新增SKU级别调价（SET_SKU_PRICE）作为增强功能
3. **数据完整**: 自动更新SPU的最小/最大价格范围

### ✅ 用户体验
1. **所见即所得**: 用户在界面设置什么价格，就保存什么价格
2. **无需妥协**: 不再需要使用平均价格等妥协方案
3. **操作简单**: 用户操作方式不变，只是后端处理更精确

## 🧪 测试要点

### 1. 单规格商品调价
- ✅ 设置价格能正确保存
- ✅ 商品价格范围正确更新

### 2. 多规格商品调价（相同价格）
- ✅ 所有SKU设置为相同价格
- ✅ 商品价格范围显示单一价格

### 3. 多规格商品调价（不同价格）
- ✅ 每个SKU保存指定价格
- ✅ 商品价格范围显示最小-最大价格

### 4. 批量调价
- ✅ 多商品多SKU同时调价
- ✅ 每个SKU价格独立设置
- ✅ 所有相关商品价格范围正确更新

## 📝 总结

通过扩展服务端接口支持SKU级别的精确调价，完美解决了用户在前端设置的价格与实际保存价格不一致的问题。现在用户可以为每个SKU设置不同的价格，系统会精确地保存每个设置的价格值，实现了真正的"所见即所得"的调价体验。 