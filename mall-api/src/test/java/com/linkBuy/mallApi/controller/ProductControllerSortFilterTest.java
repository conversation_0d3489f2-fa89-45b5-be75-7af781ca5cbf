//package com.linkBuy.mallApi.controller;
//
//import org.junit.jupiter.api.Test;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//
//import java.math.BigDecimal;
//import java.util.Arrays;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//import static org.junit.jupiter.api.Assertions.*;
//
///**
// * ProductController排序和筛选功能测试
// */
//@SpringBootTest
//@ActiveProfiles("test")
//public class ProductControllerSortFilterTest {
//
//    @Test
//    public void testParsePriceRange() {
//        ProductController controller = new ProductController(null, null, null);
//        Map<String, Object> params = new HashMap<>();
//
//        // 测试价格区间解析
//        controller.parsePriceRange("100-500", params);
//        assertEquals(new BigDecimal("100"), params.get("minPrice"));
//        assertEquals(new BigDecimal("500"), params.get("maxPrice"));
//
//        // 测试只有最低价格
//        params.clear();
//        controller.parsePriceRange("100-", params);
//        assertEquals(new BigDecimal("100"), params.get("minPrice"));
//        assertNull(params.get("maxPrice"));
//
//        // 测试只有最高价格
//        params.clear();
//        controller.parsePriceRange("-500", params);
//        assertNull(params.get("minPrice"));
//        assertEquals(new BigDecimal("500"), params.get("maxPrice"));
//    }
//
//    @Test
//    public void testProcessSortParams() {
//        ProductController controller = new ProductController(null, null, null);
//        Map<String, Object> params = new HashMap<>();
//
//        // 测试价格升序
//        controller.processSortParams(params, "price_asc");
//        assertEquals("sale_price_min ASC", params.get("orderBy"));
//
//        // 测试价格降序
//        params.clear();
//        controller.processSortParams(params, "price_desc");
//        assertEquals("sale_price_min DESC", params.get("orderBy"));
//
//        // 测试销量排序
//        params.clear();
//        controller.processSortParams(params, "sales");
//        assertEquals("company_spu_status DESC, create_time DESC", params.get("orderBy"));
//
//        // 测试综合排序
//        params.clear();
//        controller.processSortParams(params, "comprehensive");
//        assertEquals("company_spu_status DESC, create_time DESC", params.get("orderBy"));
//    }
//
//
//}