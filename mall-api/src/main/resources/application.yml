server:
  port: ${server.port}
  max-http-request-header-size: 102400

logging:
  config: classpath:logback-spring.xml
  level:
    com.linkBuy.mysql.dao.mapper.system: ${logging.level.com.linkBuy.mysql.dao.mapper.system}

spring:
  application:
    name: mall-api
  servlet:
    multipart:
      maxFileSize: 100MB
      maxRequestSize: 100MB
  # Spring Security配置
  security:
    filter:
      order: -100
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${jwt.issuer}
  redis:
    cluster:
      nodes: ${redis.nodes}
    password: ${redis.password}
    database: 0
    lettuce:
      pool:
        max-active: 32
        max-idle: 32
        max-wait: 4
  ### mysql数据源相关配置
  datasource:
    dynamic:
      primary: linkBuy
      strict: false
      datasource:
        linkBuy:
          url: ${linkBuy.datasource.url}
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: ${linkBuy.datasource.username}
          password: ${linkBuy.datasource.password}
      druid:
        # 配置初始化大小、最小、最大
        initial-size: 5
        minIdle: 10
        max-active: 20
        # 配置获取连接等待超时的时间(单位：毫秒)
        max-wait: 60000
        time-between-eviction-runs-millis: 2000
        min-evictable-idle-time-millis: 600000
        max-evictable-idle-time-millis: 900000
        validationQuery: 'select 1'

### mybatis-plus相关配置
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.linkBuy.webApi.domain.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: ${mybatis-plus.configuration.log-impl}
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: is_deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# Add this new configuration at the end of the file
environment:
  type: ${environment.type}

# JWT配置
jwt:
  secret: ${JWT_SECRET}
  expiration: 10800  # 30分钟
  issuer: linkBuy-admin

# 阿里云OSS配置
aliyun:
  oss:
    endpoint: ${OSS_ENDPOINT}
    accessKeyId: ${OSS_ACCESS_KEY_ID}
    accessKeySecret: ${OSS_ACCESS_KEY_SECRET}
    bucketName: ${OSS_BUCKET_NAME}

session:
  timeout:
    seconds: 10800

# 事件系统V2配置
linkbuy:
  event:
    producer:
      async: true
      batch-enabled: true
      batch-size: 100
      timeout: 5000

mail:
  smtp:
    host: smtp.163.com
    port: 25
    auth: true
    username: <EMAIL>
    password: thunder29
    from:
      address: <EMAIL>
      name: LinkBuy系统
    starttls:
      enable: true