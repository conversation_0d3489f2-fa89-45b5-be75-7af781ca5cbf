package com.linkBuy.mallApi.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
public class MallInfoDto {
    /**
     * 商城ID
     */
    private Long mallId;
    
    /**
     * 商城名称
     */
    private String mallName;
    
    /**
     * 商城Logo
     */
    private String mallLogo;
    
    /**
     * 商城描述
     */
    private String mallDesc;
    
    /**
     * 商城主题
     */
    private String themes;
    
    /**
     * 底部导航配置
     */
    private List<NavBarDto> navBars;
    
    /**
     * 底部导航样式配置
     */
    private NavConfigDto navConfig;
    
    /**
     * 开屏广告配置
     */
    private SplashConfigDto splashConfig;
    
    /**
     * 开屏广告列表
     */
    private List<SplashAdsDto> splashAds;
    
    /**
     * 分类页装修配置
     */
    private CategoryDecorationDto categoryDecoration;

    /**
     * 底部导航项
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class NavBarDto {
        private String name;
        private String icon;
        private String iconActive;
        private String path;
    }

    /**
     * 底部导航样式配置
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class NavConfigDto {
        private Boolean enabled;
        private String displayType;
        private Integer navHeight;
        private Integer fontSize;
        private Integer fontWeight;
        private Integer textMarginTop;
        private Integer iconSize;
        private String backgroundColor;
    }

    /**
     * 开屏广告配置
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SplashConfigDto {
        private Boolean enabled;
        private Boolean autoClose;
        private Integer duration;
        private String buttonPosition;
        private String indicatorStyle;
    }

    /**
     * 开屏广告
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SplashAdsDto {
        private String title;
        private String imageUrl;
        private String linkUrl;
        private String linkType;
        private Integer sortOrder;
    }

    /**
     * 分类页装修配置
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CategoryDecorationDto {
        private String categoryType;
        private Integer categoryLevel;
        private Integer styleType;
        private Boolean showSearch;
        private String searchConfig;
        private String searchWidgetData;
    }
} 