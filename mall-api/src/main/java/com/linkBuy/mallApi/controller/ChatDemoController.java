package com.linkBuy.mallApi.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import org.springframework.http.MediaType;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/mallInfo")
public class ChatDemoController {

    // 从文件读取SSE数据的方法
    private String loadChartResponseData() {
        try {
            // 从resources目录读取chartResponse.txt文件
            ClassPathResource resource = new ClassPathResource("chartResponse.txt");
            
            try (InputStream inputStream = resource.getInputStream()) {
                return StreamUtils.copyToString(inputStream, StandardCharsets.UTF_8);
            }
        } catch (IOException e) {
            // 如果文件读取失败，返回一个简单的测试数据
            return "data: {\"event\": \"error\", \"message\": \"Failed to load chart response data: " + e.getMessage() + "\"}\n";
        }
    }


        @PostMapping(value = "/chat/sse", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter streamChartResponse() {
        SseEmitter emitter = new SseEmitter(300000L); // 设置5分钟超时
        
        // 为每个连接创建独立的线程池
        ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

        // 从文件读取数据并解析SSE格式
        String chartResponseData = loadChartResponseData();
        List<String> sseEvents = parseSseData(chartResponseData);
        int[] currentIndex = {0};

        // 设置连接完成和超时的回调
        emitter.onCompletion(() -> {
            if (!scheduler.isShutdown()) {
                scheduler.shutdown();
            }
        });

        emitter.onTimeout(() -> {
            if (!scheduler.isShutdown()) {
                scheduler.shutdown();
            }
        });

        emitter.onError((throwable) -> {
            if (!scheduler.isShutdown()) {
                scheduler.shutdown();
            }
        });

        try {
            scheduler.scheduleAtFixedRate(() -> {
                try {
                    if (currentIndex[0] < sseEvents.size()) {
                        String eventData = sseEvents.get(currentIndex[0]);
                        emitter.send(SseEmitter.event().data(eventData));
                        currentIndex[0]++;
                    } else {
                        emitter.complete();
                        if (!scheduler.isShutdown()) {
                            scheduler.shutdown();
                        }
                    }
                } catch (IOException e) {
                    emitter.completeWithError(e);
                    if (!scheduler.isShutdown()) {
                        scheduler.shutdown();
                    }
                } catch (Exception e) {
                    // 处理其他可能的异常
                    emitter.completeWithError(e);
                    if (!scheduler.isShutdown()) {
                        scheduler.shutdown();
                    }
                }
            }, 0, 200, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            // 如果调度任务失败，立即清理资源
            if (!scheduler.isShutdown()) {
                scheduler.shutdown();
            }
            emitter.completeWithError(e);
        }

        return emitter;
    }

    /**
     * 解析SSE格式的数据
     * 正确处理JSON中可能包含的换行符
     */
    private List<String> parseSseData(String rawData) {
        List<String> events = new ArrayList<>();
        String[] lines = rawData.split("\n");

        StringBuilder currentEvent = new StringBuilder();
        boolean inDataBlock = false;
        int braceCount = 0;

        for (String line : lines) {
            line = line.trim();

            if (line.startsWith("data: ")) {
                if (inDataBlock && currentEvent.length() > 0) {
                    // 完成前一个事件
                    events.add(currentEvent.toString());
                    currentEvent = new StringBuilder();
                    braceCount = 0;
                }

                inDataBlock = true;
                String jsonPart = line.substring(6); // 移除 "data: " 前缀
                currentEvent.append(jsonPart);

                // 计算大括号数量来判断JSON是否完整
                for (char c : jsonPart.toCharArray()) {
                    if (c == '{') braceCount++;
                    else if (c == '}') braceCount--;
                }

                // 如果大括号平衡，说明JSON完整
                if (braceCount == 0) {
                    events.add(currentEvent.toString());
                    currentEvent = new StringBuilder();
                    inDataBlock = false;
                }
            } else if (inDataBlock && line.length() > 0) {
                // 继续构建当前事件的JSON数据
                currentEvent.append(line);

                // 继续计算大括号
                for (char c : line.toCharArray()) {
                    if (c == '{') braceCount++;
                    else if (c == '}') braceCount--;
                }

                // 检查JSON是否完整
                if (braceCount == 0) {
                    events.add(currentEvent.toString());
                    currentEvent = new StringBuilder();
                    inDataBlock = false;
                }
            }
        }

        // 处理最后一个未完成的事件（如果有的话）
        if (inDataBlock && currentEvent.length() > 0) {
            events.add(currentEvent.toString());
        }

        return events;
    }
}
