package com.linkBuy.mallApi.controller;

import com.linkBuy.common.annotation.ResponseWrapper;
import com.linkBuy.common.exception.BizException;
import com.linkBuy.mysql.dao.entity.biz.BizCompanyMall;
import com.linkBuy.mysql.dao.mapper.biz.MallProductViewMapper;
import com.linkBuy.mysql.dao.service.biz.BizCompanyMallService;
import com.linkBuy.mysql.dao.vo.mall.MallSpuBaseVO;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequiredArgsConstructor
@ResponseWrapper
@RequestMapping("/mallInfo/product/recommend")
public class ProductRecommendController {

    private final BizCompanyMallService mallService;
    private final MallProductViewMapper mallProductViewMapper;

    /**
     * 按商品获取推荐商品列表（SPU级别）
     *
     * @param scode     商城编码
     * @param productId 商品ID（companySpuId）
     * @param limit     返回数量上限，默认6个
     * @return 推荐商品列表
     */
    @GetMapping("/spu")
    public ProductRecommendListResponse getRecommendProductsForSpu(@RequestParam String scode,
                                                                   @RequestParam Long productId,
                                                                   @RequestParam(defaultValue = "6") Integer limit) {
        log.info("获取SPU级别推荐商品列表，scode: {}, productId: {}, limit: {}", scode, productId, limit);
        
        // 根据scode获取商城信息
        BizCompanyMall mall = mallService.getByCode(scode);
        if (mall == null) {
            throw new BizException("商城不存在");
        }

        // 查询当前商城下的商品，按指定数量返回
        List<MallSpuBaseVO> records = mallProductViewMapper.selectMallSpuBaseListWithParams(
                mall.getCompanyId(),
                null, // minPrice
                null, // maxPrice
                "company_spu_status DESC, create_time DESC", // orderBy
                0,    // offset
                limit // limit
        );

        // 转换为DTO
        List<ProductController.ProductDto> recommendList = records.stream()
                .map(ProductController::convertSpuBaseToDto)
                .collect(Collectors.toList());

        // 如果数据不足，补充mock数据
        if (recommendList.size() < limit) {
            recommendList.addAll(generateMockSpuRecommendProducts(limit - recommendList.size(), productId));
        }

        return new ProductRecommendListResponse(recommendList);
    }

    /**
     * 按商城获取推荐商品列表（商城级别，支持分页）
     *
     * @param scode     商城编码
     * @param productId 商品ID（companySpuId）
     * @param page      页码，从1开始
     * @param pageSize  每页数量，默认10个
     * @return 推荐商品分页列表
     */
    @GetMapping("/mall")
    public ProductRecommendPageResponse getRecommendProductsForMall(@RequestParam String scode,
                                                                    @RequestParam(required = false) Long productId,
                                                                    @RequestParam(defaultValue = "1") Integer page,
                                                                    @RequestParam(defaultValue = "10") Integer pageSize) {
        log.info("获取商城级别推荐商品列表，scode: {}, productId: {}, page: {}, pageSize: {}", scode, productId, page, pageSize);
        
        // 根据scode获取商城信息
        BizCompanyMall mall = mallService.getByCode(scode);
        if (mall == null) {
            throw new BizException("商城不存在");
        }

        // Mock 4页数据的逻辑
        final int TOTAL_MOCK_PAGES = 4;
        final int MOCK_PRODUCTS_PER_PAGE = pageSize;
        
        // 如果请求页码超过4页，返回空数据
        if (page > TOTAL_MOCK_PAGES) {
            return new ProductRecommendPageResponse(new ArrayList<>(), page, pageSize, false, 0);
        }

        // 计算偏移量
        int offset = (page - 1) * pageSize;

        // 先查询真实数据
        List<MallSpuBaseVO> records = mallProductViewMapper.selectMallSpuBaseListWithParams(
                mall.getCompanyId(),
                null, // minPrice
                null, // maxPrice
                "company_spu_status DESC, create_time DESC", // orderBy
                offset, // offset
                pageSize + 1 // 多查一个用于判断是否还有下一页
        );

        // 转换为DTO
        List<ProductController.ProductDto> recommendList = records.stream()
                .map(ProductController::convertSpuBaseToDto)
                .collect(Collectors.toList());

        // 如果真实数据不足一页，用mock数据补充
        if (recommendList.size() < pageSize) {
            int needMockCount = pageSize - recommendList.size();
            List<ProductController.ProductDto> mockProducts = generateMockMallRecommendProducts(needMockCount, page, productId);
            recommendList.addAll(mockProducts);
        }

        // 计算总数和是否有下一页
        int totalProducts = TOTAL_MOCK_PAGES * MOCK_PRODUCTS_PER_PAGE;
        boolean hasNext = page < TOTAL_MOCK_PAGES;

        // 如果当前页数据超过pageSize，截取并设置hasNext
        if (recommendList.size() > pageSize) {
            recommendList = recommendList.subList(0, pageSize);
            hasNext = true;
        }

        log.info("商城推荐商品分页结果：page={}, pageSize={}, actualSize={}, hasNext={}, total={}", 
                page, pageSize, recommendList.size(), hasNext, totalProducts);

        return new ProductRecommendPageResponse(recommendList, page, pageSize, hasNext, totalProducts);
    }

    /**
     * 生成SPU级别的mock推荐商品
     */
    private List<ProductController.ProductDto> generateMockSpuRecommendProducts(int count, Long productId) {
        List<ProductController.ProductDto> mockProducts = new ArrayList<>();
        
        String[] productNames = {
            "相关推荐商品A", "相关推荐商品B", "相关推荐商品C", 
            "相关推荐商品D", "相关推荐商品E", "相关推荐商品F"
        };
        
        String[] mockImages = {
            "https://img.alicdn.com/imgextra/i1/2208857268292/O1CN01QJ5J5M1h7WLZKxJ2M_!!2208857268292.jpg",
            "https://img.alicdn.com/imgextra/i2/2208857268292/O1CN01mXzJ5M1h7WLcKxJ2M_!!2208857268292.jpg",
            "https://img.alicdn.com/imgextra/i3/2208857268292/O1CN01nYzJ5M1h7WLeKxJ2M_!!2208857268292.jpg",
            "https://img.alicdn.com/imgextra/i4/2208857268292/O1CN01oZzJ5M1h7WLgKxJ2M_!!2208857268292.jpg",
            "https://img.alicdn.com/imgextra/i1/2208857268292/O1CN01pAzJ5M1h7WLiKxJ2M_!!2208857268292.jpg",
            "https://img.alicdn.com/imgextra/i2/2208857268292/O1CN01qBzJ5M1h7WLkKxJ2M_!!2208857268292.jpg"
        };
        
        for (int i = 0; i < count && i < productNames.length; i++) {
            ProductController.ProductDto product = new ProductController.ProductDto();
            product.setId((long) (10000 + i));
            product.setName(productNames[i]);
            product.setImage(mockImages[i % mockImages.length]);
            product.setPrice(new BigDecimal("99.90").add(new BigDecimal(i * 10)));
            product.setOriginalPrice(new BigDecimal("159.90").add(new BigDecimal(i * 10)));
            product.setSales(1000 + i * 100);
            product.setStock(500 + i * 50);
            product.setCategoryId(1L);
            product.setTags(List.of("推荐", "热销"));
            product.setDescription("这是基于当前商品的相关推荐商品" + (i + 1));
            
            mockProducts.add(product);
        }
        
        return mockProducts;
    }

    /**
     * 生成商城级别的mock推荐商品（4页数据）
     */
    private List<ProductController.ProductDto> generateMockMallRecommendProducts(int count, int page, Long productId) {
        List<ProductController.ProductDto> mockProducts = new ArrayList<>();
        
        String[] productNames = {
            "精选商品", "热门商品", "推荐商品", "优质商品", "特价商品", 
            "新品商品", "爆款商品", "限时商品", "精品商品", "畅销商品"
        };
        
        String[] mockImages = {
            "https://img.alicdn.com/imgextra/i1/2208857268292/O1CN01QJ5J5M1h7WLZKxJ2M_!!2208857268292.jpg",
            "https://img.alicdn.com/imgextra/i2/2208857268292/O1CN01mXzJ5M1h7WLcKxJ2M_!!2208857268292.jpg",
            "https://img.alicdn.com/imgextra/i3/2208857268292/O1CN01nYzJ5M1h7WLeKxJ2M_!!2208857268292.jpg",
            "https://img.alicdn.com/imgextra/i4/2208857268292/O1CN01oZzJ5M1h7WLgKxJ2M_!!2208857268292.jpg",
            "https://img.alicdn.com/imgextra/i1/2208857268292/O1CN01pAzJ5M1h7WLiKxJ2M_!!2208857268292.jpg",
            "https://img.alicdn.com/imgextra/i2/2208857268292/O1CN01qBzJ5M1h7WLkKxJ2M_!!2208857268292.jpg",
            "https://img.alicdn.com/imgextra/i3/2208857268292/O1CN01rCzJ5M1h7WLmKxJ2M_!!2208857268292.jpg",
            "https://img.alicdn.com/imgextra/i4/2208857268292/O1CN01sDzJ5M1h7WLoKxJ2M_!!2208857268292.jpg",
            "https://img.alicdn.com/imgextra/i1/2208857268292/O1CN01tEzJ5M1h7WLqKxJ2M_!!2208857268292.jpg",
            "https://img.alicdn.com/imgextra/i2/2208857268292/O1CN01uFzJ5M1h7WLsKxJ2M_!!2208857268292.jpg"
        };
        
        // 基于页码生成不同的商品
        int startIndex = (page - 1) * 10;
        
        for (int i = 0; i < count; i++) {
            int globalIndex = startIndex + i;
            int nameIndex = globalIndex % productNames.length;
            int imageIndex = globalIndex % mockImages.length;
            
            ProductController.ProductDto product = new ProductController.ProductDto();
            product.setId((long) (20000 + globalIndex));
            product.setName(productNames[nameIndex] + (globalIndex + 1));
            product.setImage(mockImages[imageIndex]);
            product.setPrice(new BigDecimal("79.90").add(new BigDecimal(globalIndex * 5)));
            product.setOriginalPrice(new BigDecimal("129.90").add(new BigDecimal(globalIndex * 5)));
            product.setSales(800 + globalIndex * 50);
            product.setStock(300 + globalIndex * 30);
            product.setCategoryId(2L);
            product.setTags(List.of("精选", "商城推荐"));
            product.setDescription("这是商城级别的推荐商品，第" + page + "页第" + (i + 1) + "个商品");
            
            mockProducts.add(product);
        }
        
        return mockProducts;
    }

    /**
     * 推荐商品列表响应（不分页）
     */
    @Data
    public static class ProductRecommendListResponse {
        /**
         * 推荐商品列表
         */
        private List<ProductController.ProductDto> list;

        public ProductRecommendListResponse(List<ProductController.ProductDto> list) {
            this.list = list;
        }
    }

    /**
     * 推荐商品分页响应
     */
    @Data
    public static class ProductRecommendPageResponse {
        /**
         * 推荐商品列表
         */
        private List<ProductController.ProductDto> list;
        
        /**
         * 当前页码
         */
        private Integer page;
        
        /**
         * 每页数量
         */
        private Integer pageSize;
        
        /**
         * 是否还有下一页
         */
        private Boolean hasNext;
        
        /**
         * 总数量
         */
        private Integer total;

        public ProductRecommendPageResponse(List<ProductController.ProductDto> list, Integer page, Integer pageSize, Boolean hasNext, Integer total) {
            this.list = list;
            this.page = page;
            this.pageSize = pageSize;
            this.hasNext = hasNext;
            this.total = total;
        }
    }
}
