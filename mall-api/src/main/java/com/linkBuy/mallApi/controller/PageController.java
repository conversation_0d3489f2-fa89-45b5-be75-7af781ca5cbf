package com.linkBuy.mallApi.controller;

import com.google.common.collect.Lists;
import com.linkBuy.common.annotation.ResponseWrapper;
import com.linkBuy.common.exception.BizException;
import com.linkBuy.mallApi.dto.response.MallInfoDto;
import com.linkBuy.mallApi.dto.response.PageInfoDto;
import com.linkBuy.mysql.dao.entity.biz.*;
import com.linkBuy.mysql.dao.service.biz.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequiredArgsConstructor
@ResponseWrapper
@RequestMapping("/mallInfo")
public class PageController {

    private final BizCompanyMallService mallService;
    private final BizMallBottomNavService bottomNavService;
    private final BizMallNavConfigService navConfigService;
    private final BizMallSplashConfigService splashConfigService;
    private final BizMallSplashAdsService splashAdsService;
    private final BizMallCategoryDecorationService categoryDecorationService;
    private final BizMallMiniprogramPageService miniprogramPageService;
    private final BizMallPageWidgetService pageWidgetService;

    /**
     * 获取商城全局配置信息
     * 包含主题、底部导航、开屏广告、分类页装修等全局配置
     * 页面加载后加载，访问期间只加载一次，刷新页面后重新加载
     */
    @GetMapping
    public MallInfoDto mallInfo(@RequestParam String scode) {
        log.info("获取商城全局配置信息，scode: {}", scode);

        // 根据scode查询商城信息
        BizCompanyMall mall = mallService.getByCode(scode);
        if (mall == null) {
            log.warn("未找到商城信息，scode: {}", scode);
            throw new BizException("商城不存在");
        }

        MallInfoDto mallInfoDto = new MallInfoDto();

        // 设置商城基本信息
        mallInfoDto.setMallId(mall.getId());
        mallInfoDto.setMallName(mall.getName());
        mallInfoDto.setMallLogo(mall.getLogoPath());
        mallInfoDto.setMallDesc(mall.getDesc());

        // 设置商城主题
        String theme = StringUtils.hasText(mall.getTheme()) ? mall.getTheme() : "gold";
        mallInfoDto.setThemes(theme);

        // 查询并设置底部导航配置
        setBottomNavigation(mallInfoDto, mall.getId());

        // 查询并设置开屏广告配置
        setSplashConfiguration(mallInfoDto, mall.getId());

        // 查询并设置分类页装修配置
        setCategoryDecoration(mallInfoDto, mall.getId());

        log.info("成功获取商城全局配置信息，mallId: {}, theme: {}, navCount: {}",
                mall.getId(), theme, mallInfoDto.getNavBars() != null ? mallInfoDto.getNavBars().size() : 0);

        return mallInfoDto;
    }

    /**
     * 获取商城首页配置信息
     * 包含微页面和页面组件配置
     * 首页打开时加载
     */
    @GetMapping("/homePage")
    public PageInfoDto homePage(@RequestParam String scode) {
        log.info("获取商城首页配置信息，scode: {}", scode);

        // 根据scode查询商城信息
        BizCompanyMall mall = mallService.getByCode(scode);
        if (mall == null) {
            log.warn("未找到商城信息，scode: {}", scode);
            throw new BizException("商城不存在");
        }

        PageInfoDto pageInfoDto = new PageInfoDto();

        // 查询并设置首页微页面配置
        setHomePage(pageInfoDto, mall.getId());

        log.info("成功获取商城首页配置信息，mallId: {}", mall.getId());

        return pageInfoDto;
    }

    /**
     * 获取商城落地页配置信息
     * 包含微页面和页面组件配置
     * 落地页打开时加载
     */
    @GetMapping("/landingPage")
    public PageInfoDto landingPage(@RequestParam String scode, @RequestParam(required = false) String pageCode) {
        log.info("获取商城落地页配置信息，scode: {}, pageCode: {}", scode, pageCode);

        // 根据scode查询商城信息
        BizCompanyMall mall = mallService.getByCode(scode);
        if (mall == null) {
            log.warn("未找到商城信息，scode: {}", scode);
            throw new BizException("商城不存在");
        }

        PageInfoDto pageInfoDto = new PageInfoDto();

        // 查询并设置指定页面的微页面配置
        if (StringUtils.hasText(pageCode)) {
            setLandingPage(pageInfoDto, mall.getId(), pageCode);
        } else {
            // 如果没有指定pageCode，返回首页
            setHomePage(pageInfoDto, mall.getId());
        }

        log.info("成功获取商城落地页配置信息，mallId: {}, pageCode: {}", mall.getId(), pageCode);

        return pageInfoDto;
    }

    /**
     * 设置底部导航配置（用于MallInfo）
     */
    private void setBottomNavigation(MallInfoDto mallInfoDto, Long mallId) {
        // 查询底部导航项
        List<BizMallBottomNav> bottomNavList = bottomNavService.getByMallId(mallId);
        if (CollectionUtils.isEmpty(bottomNavList)) {
            // 如果没有配置底部导航，使用默认配置
            mallInfoDto.setNavBars(getDefaultNavBars());
        } else {
            // 转换为前端需要的格式
            List<MallInfoDto.NavBarDto> navBars = bottomNavList.stream()
                    .map(nav -> new MallInfoDto.NavBarDto(
                            nav.getNavText(),
                            nav.getIconUrl(),
                            nav.getActiveIconUrl(),
                            nav.getLinkUrl()
                    ))
                    .collect(Collectors.toList());
            mallInfoDto.setNavBars(navBars);
        }

        // 查询底部导航样式配置
        BizMallNavConfig navConfig = navConfigService.getByMallId(mallId);
        if (navConfig == null) {
            navConfig = navConfigService.getDefaultConfig(mallId);
        }

        MallInfoDto.NavConfigDto navConfigDto = new MallInfoDto.NavConfigDto(
                navConfig.getEnabled(),
                navConfig.getDisplayType(),
                navConfig.getNavHeight(),
                navConfig.getFontSize(),
                navConfig.getFontWeight(),
                navConfig.getTextMarginTop(),
                navConfig.getIconSize(),
                navConfig.getBackgroundColor()
        );
        mallInfoDto.setNavConfig(navConfigDto);
    }

    /**
     * 设置开屏广告配置（用于MallInfo）
     */
    private void setSplashConfiguration(MallInfoDto mallInfoDto, Long mallId) {
        // 查询开屏广告配置
        BizMallSplashConfig splashConfig = splashConfigService.getByMallId(mallId);
        if (splashConfig == null) {
            splashConfig = splashConfigService.getDefaultConfig(mallId);
        }

        MallInfoDto.SplashConfigDto splashConfigDto = new MallInfoDto.SplashConfigDto(
                splashConfig.getEnabled(),
                splashConfig.getAutoClose(),
                splashConfig.getDuration(),
                splashConfig.getButtonPosition(),
                splashConfig.getIndicatorStyle()
        );
        mallInfoDto.setSplashConfig(splashConfigDto);

        // 查询开屏广告列表
        List<BizMallSplashAds> splashAdsList = splashAdsService.getByMallId(mallId);
        if (!CollectionUtils.isEmpty(splashAdsList)) {
            List<MallInfoDto.SplashAdsDto> splashAdsDtos = splashAdsList.stream()
                    .map(ads -> new MallInfoDto.SplashAdsDto(
                            ads.getTitle(),
                            ads.getImageUrl(),
                            ads.getLinkUrl(),
                            ads.getLinkType(),
                            ads.getSortOrder()
                    ))
                    .collect(Collectors.toList());
            mallInfoDto.setSplashAds(splashAdsDtos);
        }
    }

    /**
     * 设置分类页装修配置（用于MallInfo）
     */
    private void setCategoryDecoration(MallInfoDto mallInfoDto, Long mallId) {
        BizMallCategoryDecoration categoryDecoration = categoryDecorationService.getByMallId(mallId);
        if (categoryDecoration == null) {
            categoryDecoration = categoryDecorationService.getDefaultConfig(mallId);
        }

        MallInfoDto.CategoryDecorationDto categoryDecorationDto = new MallInfoDto.CategoryDecorationDto(
                categoryDecoration.getCategoryType(),
                categoryDecoration.getCategoryLevel(),
                categoryDecoration.getStyleType(),
                categoryDecoration.getShowSearch(),
                categoryDecoration.getSearchConfig(),
                categoryDecoration.getSearchWidgetData()
        );
        mallInfoDto.setCategoryDecoration(categoryDecorationDto);
    }

    /**
     * 设置首页微页面配置（用于PageInfo）
     */
    private void setHomePage(PageInfoDto pageInfoDto, Long mallId) {
        // 查询首页微页面
        BizMallMiniprogramPage homePage = miniprogramPageService.getHomePage(mallId);
        if (homePage != null) {
            setMiniPageData(pageInfoDto, homePage);
        } else {
            log.warn("商城{}未配置首页微页面", mallId);
        }
    }

    /**
     * 设置落地页微页面配置（用于PageInfo）
     */
    private void setLandingPage(PageInfoDto pageInfoDto, Long mallId, String pageCode) {
        // 根据pageCode查询微页面
        BizMallMiniprogramPage landingPage = miniprogramPageService.getByPageCode(mallId, pageCode);
        if (landingPage != null && landingPage.getStatus() == 1) {
            setMiniPageData(pageInfoDto, landingPage);
        } else {
            log.warn("商城{}未找到页面编码为{}的微页面或页面已禁用", mallId, pageCode);
            // 如果找不到指定页面，尝试返回首页
            setHomePage(pageInfoDto, mallId);
        }
    }

    /**
     * 设置微页面数据（用于PageInfo）
     */
    private void setMiniPageData(PageInfoDto pageInfoDto, BizMallMiniprogramPage miniPage) {
        // 设置微页面基本信息
        PageInfoDto.MiniPageDto miniPageDto = new PageInfoDto.MiniPageDto(
                miniPage.getId(),
                miniPage.getMallId(),
                miniPage.getPageName(),
                miniPage.getPageCode(),
                miniPage.getPageTitle(),
                miniPage.getPageDescription(),
                miniPage.getBackgroundColor(),
                miniPage.getPageConfig(),
                miniPage.getIsHomePage(),
                miniPage.getStatus(),
                miniPage.getSortOrder(),
                miniPage.getRemark()
        );
        pageInfoDto.setMiniPage(miniPageDto);

        // 查询页面组件
        List<BizMallPageWidget> widgets = pageWidgetService.getVisibleByPageId(miniPage.getId());
        if (!CollectionUtils.isEmpty(widgets)) {
            List<PageInfoDto.PageWidgetDto> widgetDtos = widgets.stream()
                    .map(widget -> new PageInfoDto.PageWidgetDto(
                            widget.getId(),
                            widget.getPageId(),
                            widget.getWidgetId(),
                            widget.getWidgetType(),
                            widget.getWidgetData(),
                            widget.getSortOrder(),
                            widget.getIsHidden()
                    ))
                    .collect(Collectors.toList());
            pageInfoDto.setPageWidgets(widgetDtos);
        }
    }

    /**
     * 获取默认底部导航配置
     */
    private List<MallInfoDto.NavBarDto> getDefaultNavBars() {
        MallInfoDto.NavBarDto navBar1 = new MallInfoDto.NavBarDto("首页",
                "https://default-res.oss-cn-hangzhou.aliyuncs.com/h5/uploads/8x429m1r/20241205185237e26c21439.png",
                "https://default-res.oss-cn-hangzhou.aliyuncs.com/h5/uploads/8x429m1r/2024120518523704bf35257.png",
                "/pages/landingPage/landingPage");
        MallInfoDto.NavBarDto navBar2 = new MallInfoDto.NavBarDto("分类",
                "https://default-res.oss-cn-hangzhou.aliyuncs.com/h5/uploads/8x429m1r/20241205185237251915346.png",
                "https://default-res.oss-cn-hangzhou.aliyuncs.com/h5/uploads/8x429m1r/20241205185237bd0958597.png",
                "/pages/category/category");
        MallInfoDto.NavBarDto navBar3 = new MallInfoDto.NavBarDto("购物车",
                "https://default-res.oss-cn-hangzhou.aliyuncs.com/h5/uploads/8x429m1r/2024120518523783e4c9603.png",
                "https://default-res.oss-cn-hangzhou.aliyuncs.com/h5/uploads/8x429m1r/202412051852370f3a85398.png",
                "/pages/cart/cart");
        MallInfoDto.NavBarDto navBar4 = new MallInfoDto.NavBarDto("我的",
                "https://default-res.oss-cn-hangzhou.aliyuncs.com/h5/uploads/8x429m1r/20241205185237761ff7874.png",
                "https://default-res.oss-cn-hangzhou.aliyuncs.com/h5/uploads/8x429m1r/202412051852373164b4529.png",
                "/pages/user/user");
        return Lists.newArrayList(navBar1, navBar2, navBar3, navBar4);
    }
}
