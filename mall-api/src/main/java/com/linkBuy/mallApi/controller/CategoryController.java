package com.linkBuy.mallApi.controller;

import com.linkBuy.common.annotation.ResponseWrapper;
import com.linkBuy.common.exception.BizException;
import com.linkBuy.mysql.dao.entity.biz.BizCompanyMall;
import com.linkBuy.mysql.dao.entity.biz.BizMallCategory;
import com.linkBuy.mysql.dao.entity.biz.BizMallCategoryDecoration;
import com.linkBuy.mysql.dao.entity.system.GenProductCategory;
import com.linkBuy.mysql.dao.service.biz.BizCompanyMallService;
import com.linkBuy.mysql.dao.service.biz.BizMallCategoryDecorationService;
import com.linkBuy.mysql.dao.service.biz.BizMallCategoryService;
import com.linkBuy.mysql.dao.service.system.GenProductCategoryService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequiredArgsConstructor
@ResponseWrapper
@RequestMapping("/mallInfo")
public class CategoryController {

    private final BizCompanyMallService mallService;
    private final BizMallCategoryDecorationService categoryDecorationService;
    private final GenProductCategoryService genProductCategoryService;
    private final BizMallCategoryService bizMallCategoryService;

    /**
     * 获取分类列表
     *
     * @param scode    商城编码
     * @param level    分类层级 1-一级分类 2-二级分类 3-三级分类
     * @param parentId 父分类ID（获取子分类时必传）
     * @return 分类列表
     */
    @GetMapping("/category/list")
    public List<CategoryDto> getCategoryList(@RequestParam String scode,
                                             @RequestParam Integer level,
                                             @RequestParam(required = false) Long parentId) {
        log.info("获取分类列表，scode: {}, level: {}, parentId: {}", scode, level, parentId);

        // 根据scode获取商城信息
        BizCompanyMall mall = mallService.getByCode(scode);
        if (mall == null) {
            throw new BizException("商城不存在");
        }

        // 获取分类装修配置，判断使用标准分类还是自定义分类
        BizMallCategoryDecoration decoration = categoryDecorationService.getByMallId(mall.getId());
        if (decoration == null) {
            decoration = categoryDecorationService.getDefaultConfig(mall.getId());
        }

        String categoryType = decoration.getCategoryType();
        log.info("商城{}使用分类类型: {}", mall.getId(), categoryType);

        if ("standard".equals(categoryType)) {
            // 使用标准商品分类
            return getStandardCategories(level, parentId);
        } else {
            // 使用自定义分类
            return getCustomCategories(mall.getId(), level, parentId);
        }
    }

    /**
     * 获取标准商品分类
     */
    private List<CategoryDto> getStandardCategories(Integer level, Long parentId) {
        List<GenProductCategory> categories;

        if (parentId != null) {
            if (level == 2) {

                categories = genProductCategoryService.getCategoriesByParentId(parentId);
            } else {
                List<Long> lv2Ids = genProductCategoryService.getCategoriesByParentId(parentId).stream().map(GenProductCategory::getId).collect(Collectors.toList());
                categories = genProductCategoryService.getCategoriesByParentIds(lv2Ids);
            }
            // 根据父ID获取子分类
        } else {
            // 根据层级获取分类
            categories = genProductCategoryService.getCategoriesByLevel(level);
        }

        return categories.stream()
                .map(this::convertStandardToDto)
                .toList();
    }

    /**
     * 获取自定义分类
     */
    private List<CategoryDto> getCustomCategories(Long mallId, Integer level, Long parentId) {
        List<BizMallCategory> categories;

        if (parentId != null) {
            // 根据父ID获取子分类
            categories = bizMallCategoryService.getByMallIdAndParentId(mallId, parentId);
        } else {
            // 根据层级获取分类
            categories = bizMallCategoryService.getByMallIdAndLevel(mallId, level);
        }

        return categories.stream()
                .filter(category -> category.getStatus() == 1) // 只返回启用的分类
                .map(this::convertCustomToDto)
                .toList();
    }

    /**
     * 转换标准分类为DTO
     */
    private CategoryDto convertStandardToDto(GenProductCategory category) {
        CategoryDto dto = new CategoryDto();
        dto.setId(category.getId());
        dto.setName(category.getCategoryName());
        dto.setIcon(category.getIcon());
        dto.setParentId(category.getParentId());
        dto.setLevel(category.getLevel());
        dto.setSort(category.getSortOrder());
        dto.setIsShow(true); // 标准分类默认显示
        // TODO:
        dto.setSpuCount(0);
        return dto;
    }

    /**
     * 转换自定义分类为DTO
     */
    private CategoryDto convertCustomToDto(BizMallCategory category) {
        CategoryDto dto = new CategoryDto();
        dto.setId(category.getId());
        dto.setName(category.getCategoryName());
        dto.setIcon(category.getIcon());
        dto.setParentId(category.getParentId());
        dto.setLevel(category.getLevel());
        dto.setSort(category.getSortOrder());
        dto.setIsShow(category.getStatus() == 1);
        // TODO:
        dto.setSpuCount(0);
        return dto;
    }

    /**
     * 分类DTO
     */
    @Data
    public static class CategoryDto {
        /**
         * 分类ID
         */
        private Long id;

        /**
         * 分类名称
         */
        private String name;

        /**
         * 分类图标
         */
        private String icon;

        /**
         * 父分类ID
         */
        private Long parentId;

        /**
         * 分类层级
         */
        private Integer level;

        /**
         * 排序权重
         */
        private Integer sort;

        /**
         * 是否显示
         */
        private Boolean isShow;

        /**
         * 商品数量
         */
        private Integer spuCount;
    }
}
