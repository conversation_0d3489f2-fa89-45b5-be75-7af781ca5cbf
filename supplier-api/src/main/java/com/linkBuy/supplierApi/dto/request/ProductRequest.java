package com.linkBuy.supplierApi.dto.request;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商品请求DTO
 */
@Data
public class ProductRequest {

    /**
     * SPU名称
     */
    @NotBlank(message = "商品名称不能为空")
    private String name;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 类目ID
     */
    private Long categoryId;

    /**
     * 条形码
     */
    private String barcode;

    /**
     * 类型：1-实物商品，2-虚拟商品
     */
    @NotNull(message = "商品类型不能为空")
    private Integer type;

    /**
     * 状态：0-禁用，1-启用，2-仓库中，3-审核中
     */
    private Integer status;



    /**
     * 审核描述
     */
    private String reviewDescribe;

    /**
     * 商品详情描述
     */
    private String description;

    /**
     * 商品特点
     */
    private String features;

    /**
     * 使用说明
     */
    private String instructions;

    /**
     * 是否推荐：0-否，1-是
     */
    private Integer isRecommend;

    /**
     * 规格1名称
     */
    private String spec1Name;

    /**
     * 规格2名称
     */
    private String spec2Name;

    /**
     * 规格3名称
     */
    private String spec3Name;

    /**
     * 体积(cm³)
     */
    private BigDecimal volume;

    /**
     * 重量(kg)
     */
    private BigDecimal weight;

    /**
     * 长度(cm)
     */
    private BigDecimal length;

    /**
     * 宽度(cm)
     */
    private BigDecimal wide;

    /**
     * 高度(cm)
     */
    private BigDecimal tall;

    /**
     * 最小订购量
     */
    private Integer minOrderQuantity;

    /**
     * 最大订购量
     */
    private Integer maxOrderQuantity;

    /**
     * 交货周期(天)
     */
    private Integer leadTime;

    /**
     * 保修期(天)
     */
    private Integer warrantyPeriod;

    /**
     * 保质期(天)
     */
    private Integer shelfLife;

    /**
     * 商品单位
     */
    private String unit;

    /**
     * 运费类型：unified-统一运费，template-运费模板
     */
    private String shippingType;

    /**
     * 统一运费金额
     */
    private BigDecimal shippingFee;

    /**
     * 运费模板ID
     */
    private Long shippingTemplateId;

    /**
     * 售后服务类型：no_return-不可退换，support_return-支持退换
     */
    private String afterSaleType;

    /**
     * 支持的售后服务列表，逗号分隔：return-退货，exchange-换货，refund_only-仅退款，cancel_order-整单退款，return_shipping-退费
     */
    private String afterSaleServices;

    /**
     * SKU列表
     */
    @NotEmpty(message = "SKU列表不能为空")
    @Valid
    private List<SkuRequest> skuList;

    /**
     * 图片列表
     */
    private List<ImageRequest> images;

    /**
     * SKU请求DTO
     */
    @Data
    public static class SkuRequest {
        
        /**
         * SKU编码 - 由系统自动生成，前端不需要传递
         */
        // @NotBlank(message = "SKU编码不能为空")
        private String skuCode;

        /**
         * 成本价
         */
        @NotNull(message = "成本价不能为空")
        private BigDecimal costPrice;

        /**
         * 划线价
         */
        private BigDecimal strikethroughPrice;

        /**
         * 采购价
         */
        @NotNull(message = "采购价不能为空")
        private BigDecimal purchasePrice;

        /**
         * 规格1值
         */
        private String spec1Value;

        /**
         * 规格2值
         */
        private String spec2Value;

        /**
         * 规格3值
         */
        private String spec3Value;

        /**
         * 总库存
         */
        @NotNull(message = "总库存不能为空")
        private Integer totalInventory;

        /**
         * 可用库存
         */
        private Integer availableInventory;

        /**
         * 锁定库存
         */
        private Integer blockedInventory;

        /**
         * 安全库存
         */
        private Integer safetyStock;

        /**
         * SKU图片URL
         */
        private String imgUrl;

        /**
         * 排序权重
         */
        private Integer sortOrder;
    }

    /**
     * 图片请求DTO
     */
    @Data
    public static class ImageRequest {
        
        /**
         * 图片URL
         */
        @NotBlank(message = "图片URL不能为空")
        private String imgUrl;

        /**
         * 是否主图：0-否，1-是
         */
        private Integer isMain;

        /**
         * 排序
         */
        private Integer sortOrder;
    }
} 