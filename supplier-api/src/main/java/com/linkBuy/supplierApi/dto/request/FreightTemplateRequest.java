package com.linkBuy.supplierApi.dto.request;

import lombok.Data;
import javax.validation.Valid;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * 运费模板请求DTO
 */
@Data
public class FreightTemplateRequest {

    /**
     * 模板名称
     */
    @NotBlank(message = "模板名称不能为空")
    @Size(max = 100, message = "模板名称长度不能超过100个字符")
    private String templateName;

    /**
     * 供应商ID（从session中获取，前端不需要传递）
     */
    private Long supplierId;

    /**
     * 计费方式：1-件数计费，2-重量计费，3-体积计费，4-满额包邮
     */
    @NotNull(message = "计费方式不能为空")
    @Min(value = 1, message = "计费方式值无效")
    @Max(value = 4, message = "计费方式值无效")
    private Integer valuationType;

    /**
     * 是否默认模板：0-否，1-是
     */
    private Integer isDefault = 0;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status = 1;

    /**
     * 备注说明
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    /**
     * 运费规则列表
     */
    @Valid
    @NotEmpty(message = "运费规则不能为空")
    private List<FreightRuleRequest> rules;

    /**
     * 运费规则请求DTO
     */
    @Data
    public static class FreightRuleRequest {

        /**
         * 规则ID（更新时需要）
         */
        private Long id;

        /**
         * 规则名称
         */
        @NotBlank(message = "规则名称不能为空")
        @Size(max = 200, message = "规则名称长度不能超过200个字符")
        private String ruleName;

        /**
         * 规则类型：1-指定地区，2-默认地区
         */
        @NotNull(message = "规则类型不能为空")
        @Min(value = 1, message = "规则类型值无效")
        @Max(value = 2, message = "规则类型值无效")
        private Integer ruleType;

        /**
         * 排序权重
         */
        private Integer sortOrder = 0;

        /**
         * 首次数量（件数/重量/体积）
         */
        @DecimalMin(value = "0.01", message = "首次数量必须大于0")
        private BigDecimal firstQuantity;

        /**
         * 首次运费
         */
        @NotNull(message = "首次运费不能为空")
        @DecimalMin(value = "0", message = "首次运费不能为负数")
        private BigDecimal firstFreight;

        /**
         * 续费数量（件数/重量/体积）
         */
        @DecimalMin(value = "0.01", message = "续费数量必须大于0")
        private BigDecimal additionalQuantity;

        /**
         * 续费运费
         */
        @DecimalMin(value = "0", message = "续费运费不能为负数")
        private BigDecimal additionalFreight;

        /**
         * 包邮门槛金额
         */
        @DecimalMin(value = "0", message = "包邮门槛金额不能为负数")
        private BigDecimal freeShippingThreshold;

        /**
         * 基础运费（未达到包邮时收取）
         */
        @DecimalMin(value = "0", message = "基础运费不能为负数")
        private BigDecimal baseFreight;

        /**
         * 最高运费限制
         */
        @DecimalMin(value = "0", message = "最高运费限制不能为负数")
        private BigDecimal maxFreight;

        /**
         * 最低运费限制
         */
        @DecimalMin(value = "0", message = "最低运费限制不能为负数")
        private BigDecimal minFreight;

        /**
         * 关联地区列表
         */
        @Valid
        private List<FreightRuleRegionRequest> regions;
    }

    /**
     * 运费规则地区请求DTO
     */
    @Data
    public static class FreightRuleRegionRequest {

        /**
         * 地区编码
         */
        @NotBlank(message = "地区编码不能为空")
        private String regionCode;

        /**
         * 地区名称
         */
        @NotBlank(message = "地区名称不能为空")
        private String regionName;

        /**
         * 地区级别：1-省，2-市，3-区县
         */
        @NotNull(message = "地区级别不能为空")
        @Min(value = 1, message = "地区级别值无效")
        @Max(value = 3, message = "地区级别值无效")
        private Integer regionLevel;
    }
} 