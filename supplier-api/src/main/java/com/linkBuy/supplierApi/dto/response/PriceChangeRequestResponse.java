package com.linkBuy.supplierApi.dto.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 价格修改申请响应DTO
 */
@Data
public class PriceChangeRequestResponse {

    /**
     * 申请ID
     */
    private Long id;

    /**
     * 申请单号
     */
    private String requestNo;

    /**
     * 申请类型
     */
    private Integer requestType;

    /**
     * 申请类型名称
     */
    private String requestTypeName;

    /**
     * 申请状态
     */
    private Integer status;

    /**
     * 申请状态名称
     */
    private String statusName;

    /**
     * 商品数量
     */
    private Integer productCount;

    /**
     * SKU数量
     */
    private Integer skuCount;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 审核时间
     */
    private LocalDateTime reviewTime;

    /**
     * 审核人姓名
     */
    private String reviewerName;

    /**
     * 审核备注
     */
    private String reviewComment;

    /**
     * 申请原因
     */
    private String requestReason;

    /**
     * 商品名称（每次申请只涉及一个商品）
     */
    private String productName;

    /**
     * 商品编码
     */
    private String productCode;
} 