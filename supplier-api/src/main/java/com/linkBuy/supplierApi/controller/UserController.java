package com.linkBuy.supplierApi.controller;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linkBuy.common.annotation.ResponseWrapper;
import com.linkBuy.common.constants.SecurityConstants;
import com.linkBuy.common.dto.SessionUser;
import com.linkBuy.common.event.v2.EventBus;
import com.linkBuy.common.event.v2.supplier.SupplierUserPasswordEvent;
import com.linkBuy.common.exception.AuthenticationException;
import com.linkBuy.common.util.UserAccountUtils;
import com.linkBuy.mysql.dao.entity.biz.BizSupplier;
import com.linkBuy.mysql.dao.entity.biz.BizSupplierUser;
import com.linkBuy.mysql.dao.service.biz.BizSupplierService;
import com.linkBuy.mysql.dao.service.biz.BizSupplierUserService;
import com.linkBuy.supplierApi.dto.request.SupplierUserCreateRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
@ResponseWrapper
public class UserController {

    private final BizSupplierUserService supplierUserService;
    private final BizSupplierService bizSupplierService;
    private final PasswordEncoder passwordEncoder;
    private final EventBus eventBus;

    @Value("${spring.application.name}")
    private String applicationName;

    /**
     * 验证用户权限：检查被管理用户是否属于当前登录用户的供应商
     */
    private void validateUserBelongsToCurrentSupplier(BizSupplierUser targetUser, SessionUser sessionUser, String operation) {
        if (targetUser == null) {
            throw new AuthenticationException("用户不存在");
        }
        
        if (sessionUser == null || sessionUser.getSupplierId() == null) {
            throw new AuthenticationException("未登录或供应商信息缺失");
        }
        
        if (!targetUser.getSupplierId().equals(sessionUser.getSupplierId())) {
            log.warn("供应商ID不匹配 - 操作: {}, 目标用户ID: {}, 目标用户供应商ID: {}, 当前用户供应商ID: {}, 操作人: {}", 
                    operation, targetUser.getId(), targetUser.getSupplierId(), sessionUser.getSupplierId(), sessionUser.getName());
            throw new AuthenticationException("无权" + operation + "该用户，用户不属于您的供应商");
        }
        
        log.debug("供应商ID验证通过 - 操作: {}, 用户ID: {}, 供应商ID: {}, 操作人: {}", 
                operation, targetUser.getId(), targetUser.getSupplierId(), sessionUser.getName());
    }

    /**
     * 获取供应商用户列表
     */
    @GetMapping("/list")
    @PreAuthorize("isAuthenticated()")
    public IPage<BizSupplierUser> getUserList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String query,
            HttpServletRequest request) {
        
        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null || sessionUser.getSupplierId() == null) {
            throw new AuthenticationException("未登录或供应商信息缺失");
        }

        LambdaQueryWrapper<BizSupplierUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizSupplierUser::getSupplierId, sessionUser.getSupplierId());
        
        // 搜索条件
        if (StringUtils.hasText(query)) {
            wrapper.and(w -> w.like(BizSupplierUser::getUsername, query)
                    .or().like(BizSupplierUser::getEmail, query)
                    .or().like(BizSupplierUser::getMobile, query));
        }
        
        wrapper.orderByDesc(BizSupplierUser::getCreateTime);
        
        Page<BizSupplierUser> pageParam = new Page<>(page, size);
        return supplierUserService.page(pageParam, wrapper);
    }

    /**
     * 获取供应商用户详情
     */
    @GetMapping("/{id}")
    @PreAuthorize("isAuthenticated()")
    public BizSupplierUser getUserDetail(@PathVariable Long id, HttpServletRequest request) {
        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null || sessionUser.getSupplierId() == null) {
            throw new AuthenticationException("未登录或供应商信息缺失");
        }

        BizSupplierUser user = supplierUserService.getById(id);
        
        // 使用通用验证方法
        validateUserBelongsToCurrentSupplier(user, sessionUser, "访问");
        
        // 清除密码字段
        user.setPassword(null);
        return user;
    }

    /**
     * 新增供应商用户
     */
    @PostMapping("/add")
    @PreAuthorize("isAuthenticated()")
    public BizSupplierUser addUser(@Validated @RequestBody SupplierUserCreateRequest request, 
                                   HttpServletRequest httpRequest) {
        SessionUser sessionUser = (SessionUser) httpRequest.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null || sessionUser.getSupplierId() == null) {
            throw new AuthenticationException("未登录或供应商信息缺失");
        }

        // 检查用户名是否已存在（同一供应商下）
        LambdaQueryWrapper<BizSupplierUser> usernameWrapper = new LambdaQueryWrapper<>();
        usernameWrapper.eq(BizSupplierUser::getSupplierId, sessionUser.getSupplierId())
                .eq(BizSupplierUser::getUsername, request.getUsername());
        BizSupplierUser existingByUsername = supplierUserService.getOne(usernameWrapper);
        Assert.isNull(existingByUsername, "用户名已存在");

        // 检查手机号是否已存在（同一供应商下）
        LambdaQueryWrapper<BizSupplierUser> mobileWrapper = new LambdaQueryWrapper<>();
        mobileWrapper.eq(BizSupplierUser::getSupplierId, sessionUser.getSupplierId())
                .eq(BizSupplierUser::getMobile, request.getMobile());
        BizSupplierUser existingByMobile = supplierUserService.getOne(mobileWrapper);
        Assert.isNull(existingByMobile, "手机号已存在");

        // 检查邮箱是否已存在（同一供应商下）
        LambdaQueryWrapper<BizSupplierUser> emailWrapper = new LambdaQueryWrapper<>();
        emailWrapper.eq(BizSupplierUser::getSupplierId, sessionUser.getSupplierId())
                .eq(BizSupplierUser::getEmail, request.getEmail());
        BizSupplierUser existingByEmail = supplierUserService.getOne(emailWrapper);
        Assert.isNull(existingByEmail, "邮箱已存在");

        // 生成随机密码
        String randomPassword = UserAccountUtils.generateRandomPassword();

        BizSupplierUser user = new BizSupplierUser();
        user.setSupplierId(sessionUser.getSupplierId());
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setMobile(request.getMobile());
        user.setPassword(passwordEncoder.encode(randomPassword));
        user.setStatus(1); // 默认启用

        supplierUserService.save(user);
        
        log.info("新增供应商用户成功，供应商ID: {}, 用户名: {}, 邮箱: {}, 操作人: {}", 
                sessionUser.getSupplierId(), user.getUsername(), user.getEmail(), sessionUser.getName());

        // 发布供应商用户创建事件
        publishUserCreatedEvent(user, sessionUser, randomPassword);
        
        // 清除密码字段
        user.setPassword(null);
        return user;
    }

    /**
     * 删除供应商用户
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("isAuthenticated()")
    public void deleteUser(@PathVariable Long id, HttpServletRequest request) {
        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null || sessionUser.getSupplierId() == null) {
            throw new AuthenticationException("未登录或供应商信息缺失");
        }

        BizSupplierUser user = supplierUserService.getById(id);
        
        // 使用通用验证方法
        validateUserBelongsToCurrentSupplier(user, sessionUser, "删除");
        
        Assert.isTrue(user.getStatus() == 0, "启用中账户不能删除");

        // 不能删除自己
        if (user.getId().equals(sessionUser.getUserId())) {
            throw new AuthenticationException("不能删除自己的账号");
        }
        BizSupplier supplier = bizSupplierService.getById(user.getSupplierId());
        if (user.getMobile().equals(supplier.getContactMobile())) {
            throw new AuthenticationException("不能删除主账号");
        }

        // 执行删除（逻辑删除）
        supplierUserService.removeById(id);
        
        log.info("删除供应商用户成功，用户ID: {}, 用户名: {}, 操作人: {}", 
                id, user.getUsername(), sessionUser.getName());

        // 发布用户删除事件
        publishUserDeletedEvent(user, sessionUser);
    }

    /**
     * 重置用户密码
     */
    @PostMapping("/{id}/resetPassword")
    @PreAuthorize("isAuthenticated()")
    public void resetPassword(@PathVariable Long id, HttpServletRequest request) {
        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null || sessionUser.getSupplierId() == null) {
            throw new AuthenticationException("未登录或供应商信息缺失");
        }

        BizSupplierUser user = supplierUserService.getById(id);
        
        // 使用通用验证方法
        validateUserBelongsToCurrentSupplier(user, sessionUser, "重置密码");

        // 生成新的随机密码
        String newPassword = UserAccountUtils.generateRandomPassword();
        user.setPassword(passwordEncoder.encode(newPassword));
        supplierUserService.updateById(user);
        
        log.info("重置供应商用户密码成功，用户ID: {}, 用户名: {}, 新密码: {}, 操作人: {}", 
                id, user.getUsername(), newPassword, sessionUser.getName());

        // 发布密码重置事件
        publishPasswordResetEvent(user, sessionUser, newPassword);
    }

    /**
     * 启用/禁用用户
     */
    @PostMapping("/{id}/status")
    @PreAuthorize("isAuthenticated()")
    public void updateUserStatus(@PathVariable Long id, 
                                @RequestParam Integer status,
                                HttpServletRequest request) {
        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null || sessionUser.getSupplierId() == null) {
            throw new AuthenticationException("未登录或供应商信息缺失");
        }

        BizSupplierUser user = supplierUserService.getById(id);
        BizSupplier supplier = bizSupplierService.getById(user.getSupplierId());
        
        // 使用通用验证方法
        validateUserBelongsToCurrentSupplier(user, sessionUser, "修改状态");

        // 不能禁用自己
        if (user.getId().equals(sessionUser.getUserId()) && status == 0) {
            throw new AuthenticationException("不能禁用自己的账号");
        }
        if (user.getMobile().equals(supplier.getContactMobile()) && status == 0) {
            throw new AuthenticationException("不能禁用主账号");
        }

        // 验证状态值
        if (status != 0 && status != 1) {
            throw new IllegalArgumentException("状态值只能是0（禁用）或1（启用）");
        }

        user.setStatus(status);
        supplierUserService.updateById(user);
        
        String statusText = status == 1 ? "启用" : "禁用";
        log.info("{}供应商用户成功，用户ID: {}, 用户名: {}, 操作人: {}", 
                statusText, id, user.getUsername(), sessionUser.getName());

        // 发布用户状态变更事件
        publishUserStatusChangedEvent(user, sessionUser, status);
    }

    /**
     * 发布用户创建事件
     */
    private void publishUserCreatedEvent(BizSupplierUser user, SessionUser operator, String rawPassword) {
        if (StringUtils.hasText(user.getEmail())) {
            // 获取供应商信息
            BizSupplier supplier = bizSupplierService.getById(user.getSupplierId());

            SupplierUserPasswordEvent event = SupplierUserPasswordEvent.createUserCreatedEvent(
                    applicationName, user.getId(), operator.getName());
            event.setSupplierUserId(user.getId());
            event.setSupplierId(user.getSupplierId());
            event.setSupplierName(supplier != null ? supplier.getName() : "未知供应商");
            event.setUsername(user.getUsername());
            event.setEmail(user.getEmail());
            event.setMobile(user.getMobile());
            event.setRawPassword(rawPassword);
            event.setStatus(user.getStatus());

            eventBus.publishAsync(event);
            log.info("供应商用户创建事件已发布，用户ID: {}, 邮箱: {}", user.getId(), user.getEmail());
        }
    }

    /**
     * 发布用户删除事件
     */
    private void publishUserDeletedEvent(BizSupplierUser user, SessionUser operator) {
        // 获取供应商信息
//        BizSupplier supplier = bizSupplierService.getById(user.getSupplierId());
//
//        // 创建用户删除事件（使用通用构造方法）
//        SupplierUserPasswordEvent event = new SupplierUserPasswordEvent(applicationName, user.getId(), operator.getName());
//        event.setOperationType("DELETE"); // 设置操作类型为删除
//        event.setSupplierUserId(user.getId());
//        event.setSupplierId(user.getSupplierId());
//        event.setSupplierName(supplier != null ? supplier.getName() : "未知供应商");
//        event.setUsername(user.getUsername());
//        event.setEmail(user.getEmail());
//        event.setMobile(user.getMobile());
//        event.setStatus(user.getStatus());
//
//        eventBus.publishEventAsync(event);
//        log.info("供应商用户删除事件已发布，用户ID: {}, 用户名: {}", user.getId(), user.getUsername());
    }

    /**
     * 发布密码重置事件
     */
    private void publishPasswordResetEvent(BizSupplierUser user, SessionUser operator, String newPassword) {
        if (StringUtils.hasText(user.getEmail())) {
            // 获取供应商信息
            BizSupplier supplier = bizSupplierService.getById(user.getSupplierId());

            SupplierUserPasswordEvent event = SupplierUserPasswordEvent.createPasswordResetEvent(
                    applicationName, user.getId(), operator.getName());
            event.setSupplierUserId(user.getId());
            event.setSupplierId(user.getSupplierId());
            event.setSupplierName(supplier != null ? supplier.getName() : "未知供应商");
            event.setUsername(user.getUsername());
            event.setEmail(user.getEmail());
            event.setMobile(user.getMobile());
            event.setRawPassword(newPassword);
            event.setStatus(user.getStatus());

            eventBus.publishAsync(event);
            log.info("供应商用户密码重置事件已发布，用户ID: {}, 邮箱: {}", user.getId(), user.getEmail());
        }
    }

    /**
     * 发布用户状态变更事件
     */
    private void publishUserStatusChangedEvent(BizSupplierUser user, SessionUser operator, Integer newStatus) {
        // 获取供应商信息
//        BizSupplier supplier = bizSupplierService.getById(user.getSupplierId());
//
//        // 创建用户状态变更事件
//        SupplierUserPasswordEvent event = new SupplierUserPasswordEvent(applicationName, user.getId(), operator.getName());
//        event.setOperationType(newStatus == 1 ? "ENABLE" : "DISABLE");
//        event.setSupplierUserId(user.getId());
//        event.setSupplierId(user.getSupplierId());
//        event.setSupplierName(supplier != null ? supplier.getName() : "未知供应商");
//        event.setUsername(user.getUsername());
//        event.setEmail(user.getEmail());
//        event.setMobile(user.getMobile());
//        event.setStatus(newStatus);
//
//        eventBus.publishEventAsync(event);
//        String statusText = newStatus == 1 ? "启用" : "禁用";
//        log.info("供应商用户{}事件已发布，用户ID: {}, 用户名: {}", statusText, user.getId(), user.getUsername());
    }
}
