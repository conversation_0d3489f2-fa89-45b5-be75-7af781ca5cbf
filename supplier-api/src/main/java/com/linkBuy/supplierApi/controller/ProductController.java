package com.linkBuy.supplierApi.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linkBuy.common.annotation.ResponseWrapper;
import com.linkBuy.common.constants.SecurityConstants;
import com.linkBuy.common.dto.SessionUser;
import com.linkBuy.common.exception.BizException;
import com.linkBuy.common.exception.ProductBusinessException;
import com.linkBuy.common.service.ProductBusinessService;
import com.linkBuy.common.service.ProductOperationLogger;
import com.linkBuy.common.service.PriceCalculationService;
import com.linkBuy.common.service.ProductQueryOptimizationService;
import com.linkBuy.common.response.ProductApiResponse;
import com.linkBuy.mysql.dao.entity.biz.*;
import com.linkBuy.mysql.dao.service.biz.*;
import com.linkBuy.mysql.dao.service.common.SpuOperationLogService;
import com.linkBuy.supplierApi.dto.request.ProductRequest;
import com.linkBuy.supplierApi.dto.response.ProductResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品管理控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@ResponseWrapper
@RequestMapping("/product")
@Validated
public class ProductController {

    private final BizSupplierSkuService bizSupplierSkuService;
    private final BizSupplierSpuService bizSupplierSpuService;
    private final BizSupplierService bizSupplierService;
    private final BizSupplierSpuImgService bizSupplierSpuImgService;
    private final BizPriceSpecChangeRequestService bizPriceSpecChangeRequestService;
    private final BizPriceSpecChangeRequestProductService bizPriceSpecChangeRequestProductService;
    private final SpuOperationLogService spuOperationLogService;
    private final BizSupplierSpuOperationLogService bizSupplierSpuOperationLogService;
    private final ProductBusinessService productBusinessService;
    private final ProductOperationLogger productOperationLogger;
    private final PriceCalculationService priceCalculationService;
    private final ProductQueryOptimizationService queryOptimizationService;

    private static final String SEPARATOR = "_";

    /**
     * 获取商品列表
     */
    @GetMapping("/list")
    @PreAuthorize("isAuthenticated()")
    public IPage<ProductResponse> getProductList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String productName,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) Integer status,

            @RequestParam(required = false) Integer type,
            HttpServletRequest request) {

        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);
        
        productOperationLogger.logProductOperation(null, "查询供应商商品列表", 
            sessionUser.getName(), "分页查询", request);

        // 构建查询条件
        LambdaQueryWrapper<BizSupplierSpu> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.hasText(productName), BizSupplierSpu::getName, productName)
                .like(StringUtils.hasText(category), BizSupplierSpu::getCategoryId, category)
                .eq(status != null, BizSupplierSpu::getStatus, status)
                .eq(type != null, BizSupplierSpu::getType, type)
                .eq(BizSupplierSpu::getSupplierId, sessionUser.getSupplierId())
                .orderByDesc(BizSupplierSpu::getCreateTime);

        // 供应商端可以查看所有状态的商品：草稿(2)、审核中(3)、审核拒绝(4)、销售中(1)、已下架(0)
        // 无需额外的状态过滤

        // 分页查询
        Page<BizSupplierSpu> pageParam = new Page<>(page, size);
        IPage<BizSupplierSpu> spuPage = bizSupplierSpuService.page(pageParam, wrapper);

        // 批量优化：转换为响应DTO
        List<ProductResponse> optimizedResponses = convertToResponseBatch(spuPage.getRecords());
        IPage<ProductResponse> result = new Page<>(page, size, spuPage.getTotal());
        result.setRecords(optimizedResponses);
        return result;
    }

    /**
     * 获取商品详情
     */
    @GetMapping("/{id}")
    @PreAuthorize("isAuthenticated()")
    public ProductResponse getProductDetail(@PathVariable @NotNull Long id, HttpServletRequest request) {
        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);

        productOperationLogger.logProductOperation(id, "查询供应商商品详情", 
            sessionUser.getName(), "详情查询", request);

        // 获取SPU基本信息
        BizSupplierSpu spu = bizSupplierSpuService.getById(id);
        if (spu == null) {
            throw new ProductBusinessException("商品不存在");
        }
        
        // 使用公共服务验证供应商权限
        productBusinessService.validateSupplierPermission(spu.getSupplierId(), 
            sessionUser.getSupplierId(), spu.getName());

        // 获取SKU列表
        List<BizSupplierSku> skuList = bizSupplierSkuService.list(
                new LambdaQueryWrapper<BizSupplierSku>()
                        .eq(BizSupplierSku::getSpuId, id)
                        .orderByAsc(BizSupplierSku::getSortOrder)
                        .orderByAsc(BizSupplierSku::getCreateTime)
        );

        // 获取商品图片
        List<BizSupplierSpuImg> images = bizSupplierSpuImgService.list(
                new LambdaQueryWrapper<BizSupplierSpuImg>()
                        .eq(BizSupplierSpuImg::getSpuId, id)
                        .orderByDesc(BizSupplierSpuImg::getIsMain)
                        .orderByAsc(BizSupplierSpuImg::getCreateTime)
        );

        return convertToDetailResponse(spu, skuList, images);
    }

    /**
     * 创建商品
     */
    @PostMapping("/add")
    @PreAuthorize("isAuthenticated()")
    @Transactional(rollbackFor = Exception.class)
    public ProductResponse createProduct(@Valid @RequestBody ProductRequest request, HttpServletRequest httpRequest) {
        SessionUser sessionUser = (SessionUser) httpRequest.getAttribute(SecurityConstants.USER_KEY);

        productOperationLogger.logProductOperation(null, "创建供应商商品", 
            sessionUser.getName(), "商品创建", httpRequest);

        // 设置供应商ID
        request.setSupplierId(sessionUser.getSupplierId());

        // 参数校验（创建商品时需要验证所有字段）
        validateProductRequestForCreate(request, null);

        // 创建SPU
        BizSupplierSpu spu = new BizSupplierSpu();
        BeanUtils.copyProperties(request, spu);
        // 新建商品时不设置spuCode，等审核通过后再生成
        // spu.setSpuCode(null); // spuCode在审核通过时自动生成
        // 设置默认状态：草稿
        spu.setStatus(2);
        spu.setCreateTime(LocalDateTime.now());
        spu.setUpdateTime(LocalDateTime.now());
        spu.setIsDeleted(0);

        bizSupplierSpuService.save(spu);

        // 创建SKU
        createProductSkus(spu.getId(), request.getSkuList());

        // 创建商品图片
        createProductImages(spu.getId(), request.getImages());

        // 记录创建日志
        spuOperationLogService.recordReviewLogWithRequest(spu.getId(), sessionUser.getSupplierId(), "CREATE",
                null, 2, null, "创建新商品", httpRequest);

        return getProductDetail(spu.getId(), httpRequest);
    }

    /**
     * 更新商品
     * <p>
     * 限制编辑字段：
     * - 商品名称 (name)
     * - 单位 (unit)
     * - 轮播图 (images)
     * - 长、宽、高、体积、重量 (length, wide, tall, volume, weight)
     * - 库存、安全库存 (totalInventory, availableInventory, blockedInventory, safetyStock)
     * - SKU划线价、图片、排序 (strikethroughPrice, imgUrl, sortOrder)
     * - 运费设置 (shippingType, shippingFee, shippingTemplateId)
     * - 售后设置 (afterSaleType, afterSaleServices)
     * - 商品详情 (description)
     * <p>
     * 其他字段（如分类、规格、成本价、采购价等）不可编辑
     */
    @PutMapping("/{id}")
    @PreAuthorize("isAuthenticated()")
    @Transactional(rollbackFor = Exception.class)
    public ProductResponse updateProduct(@PathVariable @NotNull Long id,
                                         @Valid @RequestBody ProductRequest request,
                                         HttpServletRequest httpRequest) {
        SessionUser sessionUser = (SessionUser) httpRequest.getAttribute(SecurityConstants.USER_KEY);

        productOperationLogger.logProductOperation(id, "更新供应商商品", 
            sessionUser.getName(), "商品更新", httpRequest);

        // 检查商品是否存在
        BizSupplierSpu existingSpu = bizSupplierSpuService.getById(id);
        if (existingSpu == null) {
            throw new ProductBusinessException("商品不存在");
        }
        
        // 使用公共服务验证供应商权限
        productBusinessService.validateSupplierPermission(existingSpu.getSupplierId(), 
            sessionUser.getSupplierId(), existingSpu.getName());

        // 使用公共服务检查商品编辑权限
        productBusinessService.validateProductEditPermission(existingSpu.getStatus(), existingSpu.getName());

        // 设置供应商ID
        request.setSupplierId(sessionUser.getSupplierId());

        // 参数校验
        validateProductRequest(request, id);

        // 更新SPU基本信息 - 只允许更新指定字段
        BizSupplierSpu spu = new BizSupplierSpu();
        spu.setId(id);

        // 允许编辑的字段：
        // 1. 商品名称
        spu.setName(request.getName());

        // 2. 单位
        spu.setUnit(request.getUnit());

        // 3. 商品详情
        spu.setDescription(request.getDescription());

        // 4. 长、宽、高、体积、重量
        spu.setVolume(request.getVolume());
        spu.setWeight(request.getWeight());
        spu.setLength(request.getLength());
        spu.setWide(request.getWide());
        spu.setTall(request.getTall());

        // 5. 运费设置
        spu.setShippingType(request.getShippingType());
        spu.setShippingFee(request.getShippingFee());
        spu.setShippingTemplateId(request.getShippingTemplateId());

        // 6. 售后设置
        spu.setAfterSaleType(request.getAfterSaleType());
        spu.setAfterSaleServices(request.getAfterSaleServices());

        spu.setUpdateTime(LocalDateTime.now());

        bizSupplierSpuService.updateById(spu);

        // 更新SKU（保持原有ID）
        updateProductSkus(id, request.getSkuList());

        // 更新商品图片（保持原有ID）
        updateProductImages(id, request.getImages());

        // 记录更新日志
        spuOperationLogService.recordReviewLogWithRequest(id, sessionUser.getSupplierId(), "UPDATE",
                existingSpu.getStatus(), existingSpu.getStatus(),
                null, "更新商品信息", httpRequest);

        return getProductDetail(id, httpRequest);
    }

    /**
     * 删除商品
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("isAuthenticated()")
    @Transactional(rollbackFor = Exception.class)
    public String deleteProduct(@PathVariable @NotNull Long id, HttpServletRequest request) {
        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);

        productOperationLogger.logProductOperation(id, "删除供应商商品", 
            sessionUser.getName(), "商品删除", request);

        // 检查商品是否存在
        BizSupplierSpu spu = bizSupplierSpuService.getById(id);
        if (spu == null) {
            throw new ProductBusinessException("商品不存在");
        }
        
        // 使用公共服务验证供应商权限
        productBusinessService.validateSupplierPermission(spu.getSupplierId(), 
            sessionUser.getSupplierId(), spu.getName());

        // 使用公共服务检查删除权限
        productBusinessService.validateProductDeletePermission(spu.getStatus(), spu.getName());

        // 使用逻辑删除
        bizSupplierSpuService.removeById(id);

        // 删除关联的SKU和图片
        deleteProductSkus(id);
        deleteProductImages(id);

        // 记录删除日志
        spuOperationLogService.recordReviewLogWithRequest(id, sessionUser.getSupplierId(), "DELETE",
                spu.getStatus(), null, null, "删除商品", request);

        return "删除成功";
    }

    /**
     * 批量删除商品
     */
    @DeleteMapping("/batch")
    @PreAuthorize("isAuthenticated()")
    @Transactional(rollbackFor = Exception.class)
    public String batchDeleteProducts(@RequestBody List<Long> ids, HttpServletRequest request) {
        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);

        productOperationLogger.logProductOperation(null, "批量删除供应商商品", 
            sessionUser.getName(), "批量删除", request);

        if (CollectionUtils.isEmpty(ids)) {
            throw new ProductBusinessException("请选择要删除的商品");
        }

        // 检查所有商品权限和删除权限
        List<BizSupplierSpu> spuList = bizSupplierSpuService.listByIds(ids);
        for (BizSupplierSpu spu : spuList) {
            productBusinessService.validateSupplierPermission(spu.getSupplierId(), 
                sessionUser.getSupplierId(), spu.getName());
            productBusinessService.validateProductDeletePermission(spu.getStatus(), spu.getName());
        }

        // 批量删除
        bizSupplierSpuService.removeByIds(ids);

        // 删除关联数据
        for (Long id : ids) {
            deleteProductSkus(id);
            deleteProductImages(id);

            // 记录删除日志
            spuOperationLogService.recordReviewLogWithRequest(id, sessionUser.getSupplierId(), "DELETE",
                    null, null, null, "批量删除商品", request);
        }

        return "批量删除成功";
    }

    /**
     * 获取商品统计信息
     */
    @GetMapping("/statistics")
    @PreAuthorize("isAuthenticated()")
    public Map<String, Object> getProductStatistics(HttpServletRequest request) {
        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);

        productOperationLogger.logProductOperation(null, "查询供应商商品统计", 
            sessionUser.getName(), "统计查询", request);

        LambdaQueryWrapper<BizSupplierSpu> baseWrapper = new LambdaQueryWrapper<>();
        baseWrapper.eq(BizSupplierSpu::getSupplierId, sessionUser.getSupplierId());

        Map<String, Object> statistics = new HashMap<>();

        // 总商品数
        statistics.put("total", bizSupplierSpuService.count(baseWrapper));

        // 销售中
        statistics.put("selling", bizSupplierSpuService.count(
                baseWrapper.clone().eq(BizSupplierSpu::getStatus, 1)
        ));

        // 审核中
        statistics.put("reviewing", bizSupplierSpuService.count(
                baseWrapper.clone().eq(BizSupplierSpu::getStatus, 3)
        ));

        // 草稿
        statistics.put("draft", bizSupplierSpuService.count(
                baseWrapper.clone().eq(BizSupplierSpu::getStatus, 2)
        ));

        // 审核拒绝
        statistics.put("rejected", bizSupplierSpuService.count(
                baseWrapper.clone().eq(BizSupplierSpu::getStatus, 4)
        ));

        // 已下架
        statistics.put("offline", bizSupplierSpuService.count(
                baseWrapper.clone().eq(BizSupplierSpu::getStatus, 0)
        ));

        return statistics;
    }

    /**
     * 提交审核
     */
    @PostMapping("/{id}/submit-review")
    @PreAuthorize("isAuthenticated()")
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> submitReview(@PathVariable @NotNull Long id,
                                            @RequestBody Map<String, String> request,
                                            HttpServletRequest httpRequest) {
        SessionUser sessionUser = (SessionUser) httpRequest.getAttribute(SecurityConstants.USER_KEY);

        productOperationLogger.logProductOperation(id, "提交供应商商品审核", 
            sessionUser.getName(), "提交审核", httpRequest);

        // 检查商品是否存在
        BizSupplierSpu spu = bizSupplierSpuService.getById(id);
        if (spu == null) {
            throw new ProductBusinessException("商品不存在");
        }
        
        // 使用公共服务验证供应商权限
        productBusinessService.validateSupplierPermission(spu.getSupplierId(), 
            sessionUser.getSupplierId(), spu.getName());

        // 使用公共服务检查状态转换权限
        if (!productBusinessService.canTransitionStatus(spu.getStatus(), 3)) {
            throw new ProductBusinessException("商品" + spu.getName() + "当前状态不允许提交审核");
        }

        // 验证商品信息是否完整
        validateProductForReview(spu, id);

        // 更新商品状态
        BizSupplierSpu updateSpu = new BizSupplierSpu();
        updateSpu.setId(id);
        updateSpu.setStatus(3); // 审核中
        updateSpu.setUpdateTime(LocalDateTime.now());
        bizSupplierSpuService.updateById(updateSpu);

        // 记录审核日志
        spuOperationLogService.recordReviewLogWithRequest(id, sessionUser.getSupplierId(), "SUBMIT", spu.getStatus(), 3,
                null, (String) request.get("submitReason"), httpRequest);

        Map<String, Object> result = new HashMap<>();
        result.put("message", "提交审核成功");
        result.put("productId", id);
        result.put("productName", spu.getName());
        result.put("newStatus", 3);
        return result;
    }

    /**
     * 批量提交审核
     */
    @PostMapping("/batch/submit-review")
    @PreAuthorize("isAuthenticated()")
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchSubmitReview(@RequestBody Map<String, Object> request,
                                                 HttpServletRequest httpRequest) {
        SessionUser sessionUser = (SessionUser) httpRequest.getAttribute(SecurityConstants.USER_KEY);

        @SuppressWarnings("unchecked")
        List<Long> ids = (List<Long>) request.get("ids");
        String submitReason = (String) request.get("submitReason");

        productOperationLogger.logProductOperation(null, "批量提交供应商商品审核", 
            sessionUser.getName(), "批量提交审核", httpRequest);

        if (CollectionUtils.isEmpty(ids)) {
            throw new ProductBusinessException("请选择要提交审核的商品");
        }

        // 检查所有商品是否属于当前供应商且状态允许提交审核
        List<BizSupplierSpu> spuList = bizSupplierSpuService.listByIds(ids);
        List<String> errorMessages = new ArrayList<>();
        List<Long> validIds = new ArrayList<>();

        for (BizSupplierSpu spu : spuList) {
            if (!spu.getSupplierId().equals(sessionUser.getSupplierId())) {
                errorMessages.add("无权操作商品：" + spu.getName());
                continue;
            }

            if (spu.getStatus() != 2 && spu.getStatus() != 4) {
                errorMessages.add("商品 " + spu.getName() + " 不是草稿或审核拒绝状态，无法提交审核");
                continue;
            }

            validIds.add(spu.getId());
        }

        if (!errorMessages.isEmpty()) {
            throw new ProductBusinessException("部分商品无法提交审核：" + String.join("; ", errorMessages));
        }

        // 批量更新状态
        bizSupplierSpuService.update()
                .set("status", 3)
                .set("update_time", LocalDateTime.now())
                .in("id", validIds)
                .update();

        // 记录审核日志
        for (Long spuId : validIds) {
            spuOperationLogService.recordReviewLogWithRequest(spuId, sessionUser.getSupplierId(), "BATCH_SUBMIT", null, 3,
                    null, submitReason, httpRequest);
        }

        Map<String, Object> result = new HashMap<>();
        result.put("message", "批量提交审核成功");
        result.put("successCount", validIds.size());
        result.put("totalCount", ids.size());
        return result;
    }

    /**
     * 撤回审核
     */
    @PostMapping("/{id}/withdraw-review")
    @PreAuthorize("isAuthenticated()")
    @Transactional(rollbackFor = Exception.class)
    public String withdrawReview(@PathVariable @NotNull Long id,
                                 @RequestBody Map<String, String> request,
                                 HttpServletRequest httpRequest) {
        SessionUser sessionUser = (SessionUser) httpRequest.getAttribute(SecurityConstants.USER_KEY);

        productOperationLogger.logProductOperation(id, "撤回供应商商品审核", 
            sessionUser.getName(), "撤回审核", httpRequest);

        // 检查商品是否存在
        BizSupplierSpu spu = bizSupplierSpuService.getById(id);
        if (spu == null) {
            throw new ProductBusinessException("商品不存在");
        }
        
        // 使用公共服务验证供应商权限
        productBusinessService.validateSupplierPermission(spu.getSupplierId(), 
            sessionUser.getSupplierId(), spu.getName());

        // 使用公共服务检查状态转换权限
        if (!productBusinessService.canTransitionStatus(spu.getStatus(), 2)) {
            throw new ProductBusinessException("商品" + spu.getName() + "当前状态不允许撤回审核");
        }

        // 更新商品状态
        BizSupplierSpu updateSpu = new BizSupplierSpu();
        updateSpu.setId(id);
        updateSpu.setStatus(2); // 草稿
        updateSpu.setUpdateTime(LocalDateTime.now());
        bizSupplierSpuService.updateById(updateSpu);

        // 记录审核日志
        spuOperationLogService.recordReviewLogWithRequest(id, sessionUser.getSupplierId(), "WITHDRAW", 3, 2,
                null, request.get("withdrawReason"), httpRequest);

        return "撤回审核成功";
    }

    /**
     * 获取审核记录（操作日志）
     */
    @GetMapping("/{id}/review-logs")
    @PreAuthorize("isAuthenticated()")
    public List<Map<String, Object>> getReviewLogs(@PathVariable @NotNull Long id,
                                                   @RequestParam(defaultValue = "20") Integer limit,
                                                   HttpServletRequest request) {
        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);

        productOperationLogger.logProductOperation(id, "查询供应商商品审核日志", 
            sessionUser.getName(), "审核日志查询", request);

        // 检查商品是否存在
        BizSupplierSpu spu = bizSupplierSpuService.getById(id);
        if (spu == null) {
            throw new ProductBusinessException("商品不存在");
        }
        
        // 使用公共服务验证供应商权限
        productBusinessService.validateSupplierPermission(spu.getSupplierId(), 
            sessionUser.getSupplierId(), spu.getName());

        // 查询操作日志
        List<BizSupplierSpuOperationLog> operationLogs = bizSupplierSpuOperationLogService.getOperationLogsBySpuId(id, limit);

        // 转换为响应格式
        List<Map<String, Object>> result = new ArrayList<>();
        for (BizSupplierSpuOperationLog log : operationLogs) {
            Map<String, Object> logMap = new HashMap<>();
            logMap.put("id", log.getId());
            logMap.put("operationType", log.getOperationType());
            logMap.put("operationCategory", log.getOperationCategory());
            logMap.put("operatorName", log.getOperatorName());
            logMap.put("operatorType", log.getOperatorType());
            logMap.put("operationDesc", log.getOperationDesc());
            logMap.put("operationComment", log.getOperationComment());
            logMap.put("beforeStatus", log.getBeforeStatus());
            logMap.put("afterStatus", log.getAfterStatus());
            logMap.put("operationTime", log.getOperationTime());
            logMap.put("relatedRequestNo", log.getRelatedRequestNo());
            result.add(logMap);
        }

        return result;
    }

    // ==================== 私有方法 ====================

    /**
     * 验证商品信息是否完整，可以提交审核
     */
    private void validateProductForReview(BizSupplierSpu spu, Long spuId) {
        // 检查商品基本信息
        if (!StringUtils.hasText(spu.getName())) {
            throw new ProductBusinessException("商品名称不能为空");
        }

        // 检查是否有SKU
        List<BizSupplierSku> skuList = bizSupplierSkuService.list(
                new LambdaQueryWrapper<BizSupplierSku>()
                        .eq(BizSupplierSku::getSpuId, spuId)
        );

        if (CollectionUtils.isEmpty(skuList)) {
            throw new ProductBusinessException("商品必须至少有一个SKU才能提交审核");
        }

        // 检查SKU信息完整性
        for (BizSupplierSku sku : skuList) {
            if (sku.getCostPrice() == null || sku.getCostPrice().compareTo(BigDecimal.ZERO) < 0) {
                throw new ProductBusinessException("SKU " + sku.getSkuCode() + " 的成本价不能为空或负数");
            }
            if (sku.getPurchasePrice() == null || sku.getPurchasePrice().compareTo(BigDecimal.ZERO) < 0) {
                throw new ProductBusinessException("SKU " + sku.getSkuCode() + " 的采购价不能为空或负数");
            }
            if (sku.getTotalInventory() == null || sku.getTotalInventory() < 0) {
                throw new ProductBusinessException("SKU " + sku.getSkuCode() + " 的库存不能为空或负数");
            }
        }

        // 检查是否有商品图片
        List<BizSupplierSpuImg> images = bizSupplierSpuImgService.list(
                new LambdaQueryWrapper<BizSupplierSpuImg>()
                        .eq(BizSupplierSpuImg::getSpuId, spuId)
        );

        if (CollectionUtils.isEmpty(images)) {
            throw new ProductBusinessException("商品必须至少有一张图片才能提交审核");
        }
    }

    /**
     * 验证商品编辑权限
     */
    private void validateProductEditPermission(BizSupplierSpu existingSpu, ProductRequest request) {
        Integer status = existingSpu.getStatus();

        // 审核中状态不允许编辑
        if (status == 3) {
            throw new ProductBusinessException("审核中的商品不允许编辑");
        }

        // 注意：现在商品更新只允许编辑指定字段（商品名称、单位、轮播图、长宽高体积重量、库存安全库存、运费、售后设置、商品详情）
        // 其他字段在updateProduct方法中已经被限制，这里不需要额外验证
    }

    /**
     * 验证商品请求参数（用于创建商品）
     */
    private void validateProductRequestForCreate(ProductRequest request, Long spuId) {
        // 验证供应商是否存在
        if (bizSupplierService.getById(request.getSupplierId()) == null) {
            throw new ProductBusinessException("供应商不存在");
        }

        // 验证商品编码是否重复 - 由于spuCode由系统自动生成，跳过此验证
        // LambdaQueryWrapper<BizSupplierSpu> wrapper = new LambdaQueryWrapper<>();
        // wrapper.eq(BizSupplierSpu::getSpuCode, request.getSpuCode())
        //        .eq(BizSupplierSpu::getSupplierId, request.getSupplierId());

        // if (spuId != null) {
        //     wrapper.ne(BizSupplierSpu::getId, spuId);
        // }

        // if (bizSupplierSpuService.count(wrapper) > 0) {
        //     throw new BizException("商品编码已存在");
        // }

        // 验证SKU列表（创建时需要验证所有字段）
        validateSkuListForCreate(request.getSkuList());

        // 验证物流设置
        validateShippingSettings(request);
    }

    /**
     * 验证商品请求参数（用于更新商品）
     */
    private void validateProductRequest(ProductRequest request, Long spuId) {
        // 验证供应商是否存在
        if (bizSupplierService.getById(request.getSupplierId()) == null) {
            throw new BizException("供应商不存在");
        }

        // 验证SKU列表（更新时只验证库存相关字段）
        validateSkuList(request.getSkuList());

        // 验证物流设置
        validateShippingSettings(request);
    }

    /**
     * 验证SKU列表（用于创建商品）
     */
    private void validateSkuListForCreate(List<ProductRequest.SkuRequest> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            throw new ProductBusinessException("SKU列表不能为空");
        }

        // 验证SKU编码不重复 - 由于skuCode由系统自动生成，跳过此验证
        // Set<String> skuCodes = new HashSet<>();
        for (ProductRequest.SkuRequest sku : skuList) {
            // if (skuCodes.contains(sku.getSkuCode())) {
            //     throw new BizException("SKU编码不能重复：" + sku.getSkuCode());
            // }
            // skuCodes.add(sku.getSkuCode());

            // 验证价格（创建时需要验证所有字段）
            if (sku.getCostPrice() == null || sku.getCostPrice().compareTo(BigDecimal.ZERO) < 0) {
                throw new ProductBusinessException("成本价不能为负数");
            }
            if (sku.getPurchasePrice() == null || sku.getPurchasePrice().compareTo(BigDecimal.ZERO) < 0) {
                throw new ProductBusinessException("采购价不能为负数");
            }

            // 验证库存
            if (sku.getTotalInventory() == null || sku.getTotalInventory() < 0) {
                throw new ProductBusinessException("库存不能为负数");
            }
            if (sku.getSafetyStock() != null && sku.getSafetyStock() < 0) {
                throw new ProductBusinessException("安全库存不能为负数");
            }
        }
    }

    /**
     * 验证SKU列表（用于更新商品）
     */
    private void validateSkuList(List<ProductRequest.SkuRequest> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            throw new ProductBusinessException("SKU列表不能为空");
        }

        // 验证SKU编码不重复 - 由于skuCode由系统自动生成，跳过此验证
        // Set<String> skuCodes = new HashSet<>();
        for (ProductRequest.SkuRequest sku : skuList) {
            // if (skuCodes.contains(sku.getSkuCode())) {
            //     throw new BizException("SKU编码不能重复：" + sku.getSkuCode());
            // }
            // skuCodes.add(sku.getSkuCode());

            // 验证库存和划线价（商品更新时验证库存、划线价相关字段）
            if (sku.getTotalInventory() == null || sku.getTotalInventory() < 0) {
                throw new ProductBusinessException("库存不能为负数");
            }
            if (sku.getSafetyStock() != null && sku.getSafetyStock() < 0) {
                throw new ProductBusinessException("安全库存不能为负数");
            }
            // 验证划线价
            if (sku.getStrikethroughPrice() != null && sku.getStrikethroughPrice().compareTo(BigDecimal.ZERO) < 0) {
                throw new ProductBusinessException("划线价不能为负数");
            }
        }
    }

    /**
     * 验证物流设置
     */
    private void validateShippingSettings(ProductRequest request) {
        // 验证运费类型
        if (StringUtils.hasText(request.getShippingType())) {
            if (!"unified".equals(request.getShippingType()) && !"template".equals(request.getShippingType())) {
                throw new ProductBusinessException("运费类型只能是 unified 或 template");
            }

            // 如果是统一运费，验证运费金额
            if ("unified".equals(request.getShippingType())) {
                if (request.getShippingFee() == null || request.getShippingFee().compareTo(BigDecimal.ZERO) < 0) {
                    throw new ProductBusinessException("统一运费金额不能为空或负数");
                }
            }

            // 如果是运费模板，验证模板ID
            if ("template".equals(request.getShippingType())) {
                if (request.getShippingTemplateId() == null || request.getShippingTemplateId() <= 0) {
                    throw new ProductBusinessException("运费模板ID不能为空或无效");
                }
            }
        }

        // 验证售后服务类型
        if (StringUtils.hasText(request.getAfterSaleType())) {
            if (!"no_return".equals(request.getAfterSaleType()) && !"support_return".equals(request.getAfterSaleType())) {
                throw new ProductBusinessException("售后服务类型只能是 no_return 或 support_return");
            }
        }

        // 验证售后服务列表格式
        if (StringUtils.hasText(request.getAfterSaleServices())) {
            String[] services = request.getAfterSaleServices().split(",");
            for (String service : services) {
                String trimmedService = service.trim();
                if (!trimmedService.isEmpty() &&
                        !"return".equals(trimmedService) &&
                        !"exchange".equals(trimmedService) &&
                        !"refund_only".equals(trimmedService) &&
                        !"cancel_order".equals(trimmedService) &&
                        !"return_shipping".equals(trimmedService)) {
                    throw new ProductBusinessException("无效的售后服务类型: " + trimmedService);
                }
            }
        }
    }

    /**
     * 批量更新商品状态
     */
    private String batchUpdateProductStatus(List<Long> ids, Integer status, Long supplierId, String successMessage) {
        return batchUpdateProductStatusWithRequest(ids, status, supplierId, successMessage, null);
    }

    /**
     * 批量更新商品状态（带请求参数）
     */
    private String batchUpdateProductStatusWithRequest(List<Long> ids, Integer status, Long supplierId, String successMessage, HttpServletRequest request) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ProductBusinessException("请选择要操作的商品");
        }

        // 检查所有商品是否属于当前供应商
        List<BizSupplierSpu> spuList = bizSupplierSpuService.listByIds(ids);
        for (BizSupplierSpu spu : spuList) {
            if (!spu.getSupplierId().equals(supplierId)) {
                throw new ProductBusinessException("无权操作商品：" + spu.getName());
            }

            // 检查商品状态是否允许操作
            if (spu.getStatus() == 3) {
                throw new ProductBusinessException("审核中的商品不能进行状态变更：" + spu.getName());
            }

            // 供应商不能直接设置商品为销售中状态，需要通过审核流程
            if (status == 1) {
                throw new ProductBusinessException("供应商不能直接上架商品，请通过审核流程：" + spu.getName());
            }
        }

        // 批量更新状态
        bizSupplierSpuService.update()
                .set("status", status)
                .set("update_time", LocalDateTime.now())
                .in("id", ids)
                .update();

        // 记录状态变更日志
        String action = determineStatusAction(status);
        for (BizSupplierSpu spu : spuList) {
            spuOperationLogService.recordReviewLogWithRequest(spu.getId(), supplierId, action,
                    spu.getStatus(), status, null, successMessage, request);
        }

        return successMessage;
    }

    /**
     * 创建商品SKU
     */
    private void createProductSkus(Long spuId, List<ProductRequest.SkuRequest> skuRequests) {
        if (CollectionUtils.isEmpty(skuRequests)) {
            return;
        }

        List<BizSupplierSku> skuList = new ArrayList<>();
        for (int i = 0; i < skuRequests.size(); i++) {
            ProductRequest.SkuRequest skuRequest = skuRequests.get(i);
            BizSupplierSku sku = BizSupplierSku.builder()
                    // .skuCode(skuRequest.getSkuCode()) // skuCode由系统自动生成
                    .spuId(spuId)
                    .costPrice(skuRequest.getCostPrice())
                    .strikethroughPrice(skuRequest.getStrikethroughPrice())
                    .purchasePrice(skuRequest.getPurchasePrice())
                    .spec1Value(skuRequest.getSpec1Value())
                    .spec2Value(skuRequest.getSpec2Value())
                    .spec3Value(skuRequest.getSpec3Value())
                    .totalInventory(skuRequest.getTotalInventory())
                    .availableInventory(skuRequest.getAvailableInventory())
                    .blockedInventory(skuRequest.getBlockedInventory())
                    .safetyStock(skuRequest.getSafetyStock())
                    .imgUrl(skuRequest.getImgUrl())
                    .sortOrder(skuRequest.getSortOrder() != null ? skuRequest.getSortOrder() : i + 1)
                    .build();
            sku.setCreateTime(LocalDateTime.now());
            sku.setUpdateTime(LocalDateTime.now());
            sku.setIsDeleted(0);
            skuList.add(sku);
        }

        bizSupplierSkuService.saveBatch(skuList);
    }

    /**
     * 更新商品SKU（保持原有ID，避免影响其他业务关联）
     */
    private void updateProductSkus(Long spuId, List<ProductRequest.SkuRequest> skuRequests) {
        if (CollectionUtils.isEmpty(skuRequests)) {
            // 如果新的SKU列表为空，则删除所有现有SKU
            deleteProductSkus(spuId);
            return;
        }

        // 获取现有的SKU列表
        List<BizSupplierSku> existingSkus = bizSupplierSkuService.list(
                new LambdaQueryWrapper<BizSupplierSku>()
                        .eq(BizSupplierSku::getSpuId, spuId)
        );

        // 创建现有SKU的映射（以skuCode为key）
        Map<String, BizSupplierSku> existingSkuMap = existingSkus.stream()
                .collect(Collectors.toMap(BizSupplierSku::getSkuCode, sku -> sku, (existing, replacement) -> existing));

        // 收集新的SKU编码
        Set<String> newSkuCodes = skuRequests.stream()
                .map(ProductRequest.SkuRequest::getSkuCode)
                .collect(Collectors.toSet());

        // 删除不再存在的SKU
        List<Long> skuIdsToDelete = existingSkus.stream()
                .filter(sku -> !newSkuCodes.contains(sku.getSkuCode()))
                .map(BizSupplierSku::getId)
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(skuIdsToDelete)) {
            bizSupplierSkuService.removeByIds(skuIdsToDelete);
        }

        // 更新SKU（商品更新时不创建新SKU）
        List<BizSupplierSku> skusToUpdate = new ArrayList<>();

        for (int i = 0; i < skuRequests.size(); i++) {
            ProductRequest.SkuRequest skuRequest = skuRequests.get(i);
            BizSupplierSku existingSku = existingSkuMap.get(skuRequest.getSkuCode());

            if (existingSku != null) {
                // 更新现有SKU - 允许更新库存、划线价、图片和排序字段
                existingSku.setTotalInventory(skuRequest.getTotalInventory());
                existingSku.setAvailableInventory(skuRequest.getAvailableInventory());
                existingSku.setBlockedInventory(skuRequest.getBlockedInventory());
                existingSku.setSafetyStock(skuRequest.getSafetyStock());
                existingSku.setStrikethroughPrice(skuRequest.getStrikethroughPrice());
                existingSku.setImgUrl(skuRequest.getImgUrl());
                existingSku.setSortOrder(skuRequest.getSortOrder() != null ? skuRequest.getSortOrder() : i + 1);
                existingSku.setUpdateTime(LocalDateTime.now());
                skusToUpdate.add(existingSku);
            } else {
                // 商品更新时不允许创建新SKU，因为这涉及到规格等不可编辑的字段
                throw new ProductBusinessException("商品更新时不允许添加新的SKU，SKU编码: " + skuRequest.getSkuCode());
            }
        }

        // 批量更新现有SKU
        if (!CollectionUtils.isEmpty(skusToUpdate)) {
            bizSupplierSkuService.updateBatchById(skusToUpdate);
        }
    }

    /**
     * 创建商品图片
     */
    private void createProductImages(Long spuId, List<ProductRequest.ImageRequest> imageRequests) {
        if (CollectionUtils.isEmpty(imageRequests)) {
            return;
        }

        List<BizSupplierSpuImg> images = imageRequests.stream()
                .map(imageRequest -> {
                    BizSupplierSpuImg image = new BizSupplierSpuImg();
                    BeanUtils.copyProperties(imageRequest, image);
                    image.setSpuId(spuId);
                    image.setCreateTime(LocalDateTime.now());
                    return image;
                })
                .collect(Collectors.toList());

        bizSupplierSpuImgService.saveBatch(images);
    }

    /**
     * 更新商品图片（保持原有ID，避免影响其他业务关联）
     */
    private void updateProductImages(Long spuId, List<ProductRequest.ImageRequest> imageRequests) {
        if (CollectionUtils.isEmpty(imageRequests)) {
            // 如果新的图片列表为空，则删除所有现有图片
            deleteProductImages(spuId);
            return;
        }

        // 获取现有的图片列表
        List<BizSupplierSpuImg> existingImages = bizSupplierSpuImgService.list(
                new LambdaQueryWrapper<BizSupplierSpuImg>()
                        .eq(BizSupplierSpuImg::getSpuId, spuId)
        );

        // 创建现有图片的映射（以imgUrl为key，因为图片通常以URL作为唯一标识）
        Map<String, BizSupplierSpuImg> existingImageMap = existingImages.stream()
                .collect(Collectors.toMap(BizSupplierSpuImg::getImgUrl, img -> img, (existing, replacement) -> existing));

        // 收集新的图片URL
        Set<String> newImageUrls = imageRequests.stream()
                .map(ProductRequest.ImageRequest::getImgUrl)
                .collect(Collectors.toSet());

        // 删除不再存在的图片
        List<Long> imageIdsToDelete = existingImages.stream()
                .filter(img -> !newImageUrls.contains(img.getImgUrl()))
                .map(BizSupplierSpuImg::getId)
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(imageIdsToDelete)) {
            bizSupplierSpuImgService.removeByIds(imageIdsToDelete);
        }

        // 更新或创建图片
        List<BizSupplierSpuImg> imagesToSave = new ArrayList<>();
        List<BizSupplierSpuImg> imagesToUpdate = new ArrayList<>();

        for (ProductRequest.ImageRequest imageRequest : imageRequests) {
            BizSupplierSpuImg existingImage = existingImageMap.get(imageRequest.getImgUrl());

            if (existingImage != null) {
                // 更新现有图片信息
                existingImage.setIsMain(imageRequest.getIsMain());
                existingImage.setSortOrder(imageRequest.getSortOrder());
                existingImage.setUpdateTime(LocalDateTime.now());
                imagesToUpdate.add(existingImage);
            } else {
                // 创建新图片
                BizSupplierSpuImg newImage = new BizSupplierSpuImg();
                BeanUtils.copyProperties(imageRequest, newImage);
                newImage.setSpuId(spuId);
                newImage.setCreateTime(LocalDateTime.now());
                newImage.setUpdateTime(LocalDateTime.now());
                imagesToSave.add(newImage);
            }
        }

        // 批量保存新图片
        if (!CollectionUtils.isEmpty(imagesToSave)) {
            bizSupplierSpuImgService.saveBatch(imagesToSave);
        }

        // 批量更新现有图片
        if (!CollectionUtils.isEmpty(imagesToUpdate)) {
            bizSupplierSpuImgService.updateBatchById(imagesToUpdate);
        }
    }

    /**
     * 删除商品的所有SKU
     */
    private void deleteProductSkus(Long spuId) {
        bizSupplierSkuService.remove(
                new LambdaQueryWrapper<BizSupplierSku>()
                        .eq(BizSupplierSku::getSpuId, spuId)
        );
    }

    /**
     * 删除商品的所有图片
     */
    private void deleteProductImages(Long spuId) {
        bizSupplierSpuImgService.remove(
                new LambdaQueryWrapper<BizSupplierSpuImg>()
                        .eq(BizSupplierSpuImg::getSpuId, spuId)
        );
    }

    /**
     * 转换为响应DTO（列表用）
     */
    private ProductResponse convertToResponse(BizSupplierSpu spu) {
        ProductResponse response = new ProductResponse();
        BeanUtils.copyProperties(spu, response);

        // 获取主图
        BizSupplierSpuImg mainImage = bizSupplierSpuImgService.getOne(
                new LambdaQueryWrapper<BizSupplierSpuImg>()
                        .eq(BizSupplierSpuImg::getSpuId, spu.getId())
                        .eq(BizSupplierSpuImg::getIsMain, 1)
                        .last("LIMIT 1")
        );
        if (mainImage != null) {
            response.setMainImageUrl(mainImage.getImgUrl());
        }

        // 获取SKU统计信息
        List<BizSupplierSku> skuList = bizSupplierSkuService.list(
                new LambdaQueryWrapper<BizSupplierSku>()
                        .eq(BizSupplierSku::getSpuId, spu.getId())
        );

        if (!CollectionUtils.isEmpty(skuList)) {
            // 计算价格范围
            BigDecimal minPrice = skuList.stream()
                    .map(BizSupplierSku::getStrikethroughPrice)
                    .filter(Objects::nonNull)
                    .min(BigDecimal::compareTo)
                    .orElse(BigDecimal.ZERO);

            BigDecimal maxPrice = skuList.stream()
                    .map(BizSupplierSku::getStrikethroughPrice)
                    .filter(Objects::nonNull)
                    .max(BigDecimal::compareTo)
                    .orElse(BigDecimal.ZERO);

            response.setMinPrice(minPrice);
            response.setMaxPrice(maxPrice);

            // 计算总库存
            Integer totalStock = skuList.stream()
                    .map(BizSupplierSku::getTotalInventory)
                    .filter(Objects::nonNull)
                    .mapToInt(Integer::intValue)
                    .sum();

            response.setTotalStock(totalStock);
        }

        return response;
    }

    /**
     * 转换为详情响应DTO
     */
    private ProductResponse convertToDetailResponse(BizSupplierSpu spu, List<BizSupplierSku> skuList, List<BizSupplierSpuImg> images) {
        ProductResponse response = convertToResponse(spu);

        // 设置SKU列表
        if (!CollectionUtils.isEmpty(skuList)) {
            List<ProductResponse.SkuResponse> skuResponses = skuList.stream()
                    .map(sku -> {
                        ProductResponse.SkuResponse skuResponse = new ProductResponse.SkuResponse();
                        BeanUtils.copyProperties(sku, skuResponse);
                        return skuResponse;
                    })
                    .collect(Collectors.toList());
            response.setSkuList(skuResponses);
        }

        // 设置图片列表
        if (!CollectionUtils.isEmpty(images)) {
            List<ProductResponse.ImageResponse> imageResponses = images.stream()
                    .map(image -> {
                        ProductResponse.ImageResponse imageResponse = new ProductResponse.ImageResponse();
                        BeanUtils.copyProperties(image, imageResponse);
                        return imageResponse;
                    })
                    .collect(Collectors.toList());
            response.setImages(imageResponses);
        }

        return response;
    }

    /**
     * 检查商品是否有待审核的改价申请
     */
    @GetMapping("/check-pending-price-requests")
    @PreAuthorize("isAuthenticated()")
    public Map<String, Object> checkPendingPriceRequests(@RequestParam("spuIds") String spuIdsStr,
                                                         HttpServletRequest request) {
        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);

        // 解析SPU ID字符串为Long列表
        List<Long> spuIds = new ArrayList<>();
        if (StringUtils.hasText(spuIdsStr)) {
            try {
                String[] spuIdArray = spuIdsStr.split(",");
                for (String spuIdStr : spuIdArray) {
                    if (StringUtils.hasText(spuIdStr.trim())) {
                        spuIds.add(Long.parseLong(spuIdStr.trim()));
                    }
                }
            } catch (NumberFormatException e) {
                log.warn("解析SPU ID失败: {}", spuIdsStr, e);
                Map<String, Object> result = new HashMap<>();
                result.put("spuPendingMap", new HashMap<>());
                result.put("spuRequestNoMap", new HashMap<>());
                return result;
            }
        }

        // 构建结果：SPU ID -> 是否有待审核申请的映射
        Map<Long, Boolean> spuPendingMap = new HashMap<>();
        Map<Long, String> spuRequestNoMap = new HashMap<>();

        // 初始化所有SPU为无待审核申请
        for (Long spuId : spuIds) {
            spuPendingMap.put(spuId, false);
            spuRequestNoMap.put(spuId, null);
        }

        if (spuIds.isEmpty()) {
            Map<String, Object> result = new HashMap<>();
            result.put("spuPendingMap", spuPendingMap);
            result.put("spuRequestNoMap", spuRequestNoMap);
            return result;
        }

        // 先查找包含指定SPU的商品明细记录
        List<BizPriceSpecChangeRequestProduct> productRecords = bizPriceSpecChangeRequestProductService.list(
                new LambdaQueryWrapper<BizPriceSpecChangeRequestProduct>()
                        .in(BizPriceSpecChangeRequestProduct::getSpuId, spuIds)
        );

        if (productRecords.isEmpty()) {
            Map<String, Object> result = new HashMap<>();
            result.put("spuPendingMap", spuPendingMap);
            result.put("spuRequestNoMap", spuRequestNoMap);
            return result;
        }

        // 获取申请ID列表
        List<Long> requestIds = productRecords.stream()
                .map(BizPriceSpecChangeRequestProduct::getRequestId)
                .distinct()
                .collect(Collectors.toList());

        // 查询这些申请中状态为待审核的
        List<BizPriceSpecChangeRequest> pendingRequests = bizPriceSpecChangeRequestService.list(
                new LambdaQueryWrapper<BizPriceSpecChangeRequest>()
                        .eq(BizPriceSpecChangeRequest::getSupplierId, sessionUser.getSupplierId())
                        .eq(BizPriceSpecChangeRequest::getStatus, 1) // 1-待审核
                        .in(BizPriceSpecChangeRequest::getId, requestIds)
        );

        // 构建申请ID到申请编号的映射
        Map<Long, String> requestIdToNoMap = pendingRequests.stream()
                .collect(Collectors.toMap(
                        BizPriceSpecChangeRequest::getId,
                        BizPriceSpecChangeRequest::getRequestNo
                ));

        // 设置SPU的待审核状态
        for (BizPriceSpecChangeRequestProduct productRecord : productRecords) {
            if (requestIdToNoMap.containsKey(productRecord.getRequestId())) {
                spuPendingMap.put(productRecord.getSpuId(), true);
                spuRequestNoMap.put(productRecord.getSpuId(), requestIdToNoMap.get(productRecord.getRequestId()));
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("spuPendingMap", spuPendingMap);
        result.put("spuRequestNoMap", spuRequestNoMap);

        return result;
    }

    /**
     * 根据状态确定操作动作
     */
    private String determineStatusAction(Integer status) {
        if (status == null) {
            return "UPDATE";
        }

        switch (status) {
            case 0:
                return "OFFLINE";  // 下架
            case 1:
                return "ONLINE";   // 上架
            case 2:
                return "WAREHOUSE"; // 放入仓库
            default:
                return "UPDATE";
        }
    }

    // ==================== 性能优化相关方法 ====================

    /**
     * 批量转换SPU为响应DTO（性能优化版本）
     * 解决N+1查询问题：将原来的1+N×2次查询优化为1+2次查询
     */
    private List<ProductResponse> convertToResponseBatch(List<BizSupplierSpu> spuList) {
        if (CollectionUtils.isEmpty(spuList)) {
            return new ArrayList<>();
        }

        log.debug("批量转换{}个供应商商品DTO，优化查询性能", spuList.size());

        // 提取SPU ID列表
        List<Long> spuIds = queryOptimizationService.extractIds(spuList, BizSupplierSpu::getId);

        // 批量查询主图信息 - 避免N+1查询
        Map<Long, String> mainImageMap = batchGetMainImages(spuIds);

        // 批量查询SKU信息 - 避免N+1查询
        Map<Long, List<BizSupplierSku>> spuSkuMap = batchGetSupplierSkus(spuIds);

        // 转换为优化服务需要的数据结构
        Map<Long, List<ProductQueryOptimizationService.SupplierSkuInfo>> optimizedSpuSkuMap = 
            convertToOptimizedSkuMap(spuSkuMap);

        // 批量计算价格和库存统计
        Map<Long, ProductQueryOptimizationService.SupplierProductStats> statsMap = 
            queryOptimizationService.batchCalculateSupplierProductStats(optimizedSpuSkuMap);

        // 转换为DTO列表
        return spuList.stream()
            .map(spu -> convertToResponseOptimized(spu, mainImageMap, statsMap))
            .collect(Collectors.toList());
    }

    /**
     * 批量获取商品主图信息
     */
    private Map<Long, String> batchGetMainImages(List<Long> spuIds) {
        if (CollectionUtils.isEmpty(spuIds)) {
            return new HashMap<>();
        }

        List<BizSupplierSpuImg> mainImages = bizSupplierSpuImgService.list(
            new LambdaQueryWrapper<BizSupplierSpuImg>()
                .in(BizSupplierSpuImg::getSpuId, spuIds)
                .eq(BizSupplierSpuImg::getIsMain, 1)
        );

        return mainImages.stream()
            .collect(Collectors.toMap(
                BizSupplierSpuImg::getSpuId,
                BizSupplierSpuImg::getImgUrl,
                (existing, replacement) -> existing
            ));
    }

    /**
     * 批量获取商品SKU信息
     */
    private Map<Long, List<BizSupplierSku>> batchGetSupplierSkus(List<Long> spuIds) {
        if (CollectionUtils.isEmpty(spuIds)) {
            return new HashMap<>();
        }

        List<BizSupplierSku> allSkus = bizSupplierSkuService.list(
            new LambdaQueryWrapper<BizSupplierSku>()
                .in(BizSupplierSku::getSpuId, spuIds)
        );

        return allSkus.stream()
            .collect(Collectors.groupingBy(BizSupplierSku::getSpuId));
    }

    /**
     * 转换为优化服务需要的SKU数据结构
     */
    private Map<Long, List<ProductQueryOptimizationService.SupplierSkuInfo>> convertToOptimizedSkuMap(
            Map<Long, List<BizSupplierSku>> spuSkuMap) {
        
        Map<Long, List<ProductQueryOptimizationService.SupplierSkuInfo>> result = new HashMap<>();
        
        for (Map.Entry<Long, List<BizSupplierSku>> entry : spuSkuMap.entrySet()) {
            Long spuId = entry.getKey();
            List<BizSupplierSku> skuList = entry.getValue();
            
            List<ProductQueryOptimizationService.SupplierSkuInfo> optimizedSkuList = skuList.stream()
                .map(sku -> {
                    ProductQueryOptimizationService.SupplierSkuInfo info = 
                        new ProductQueryOptimizationService.SupplierSkuInfo();
                    info.setSkuId(sku.getId());
                    info.setSpuId(sku.getSpuId());
                    info.setPurchasePrice(sku.getPurchasePrice());
                    info.setStrikethroughPrice(sku.getStrikethroughPrice());
                    info.setTotalInventory(sku.getTotalInventory());
                    info.setAvailableInventory(sku.getAvailableInventory());
                    return info;
                })
                .collect(Collectors.toList());
            
            result.put(spuId, optimizedSkuList);
        }
        
        return result;
    }

    /**
     * 优化版本的响应DTO转换方法
     */
    private ProductResponse convertToResponseOptimized(
            BizSupplierSpu spu,
            Map<Long, String> mainImageMap,
            Map<Long, ProductQueryOptimizationService.SupplierProductStats> statsMap) {
        
        ProductResponse response = new ProductResponse();
        BeanUtils.copyProperties(spu, response);

        // 从批量查询结果中获取主图
        String mainImage = mainImageMap.get(spu.getId());
        if (mainImage != null) {
            response.setMainImageUrl(mainImage);
        }

        // 从批量计算结果中获取统计信息
        ProductQueryOptimizationService.SupplierProductStats stats = statsMap.get(spu.getId());
        if (stats != null) {
            response.setMinPrice(stats.getMinPrice());
            response.setMaxPrice(stats.getMaxPrice());
            response.setTotalStock(stats.getTotalStock());
        }

        return response;
    }
}
