package com.linkBuy.supplierApi.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.linkBuy.common.annotation.ResponseWrapper;
import com.linkBuy.common.constants.SecurityConstants;
import com.linkBuy.common.dto.SessionUser;
import com.linkBuy.common.event.v2.EventBus;
import com.linkBuy.common.event.v2.supplier.SupplierUserPasswordEvent;
import com.linkBuy.common.exception.BizException;
import com.linkBuy.common.util.UserAccountUtils;
import com.linkBuy.common.util.ValidationUtils;
import com.linkBuy.mysql.dao.entity.biz.BizSupplier;
import com.linkBuy.mysql.dao.entity.biz.BizSupplierLicense;
import com.linkBuy.mysql.dao.entity.biz.BizSupplierUser;
import com.linkBuy.mysql.dao.service.biz.BizSupplierLicenseService;
import com.linkBuy.mysql.dao.service.biz.BizSupplierService;
import com.linkBuy.mysql.dao.service.biz.BizSupplierUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 供应商信息管理控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@ResponseWrapper
@RequestMapping("/supplier")
public class SupplierController {

    private final BizSupplierService bizSupplierService;
    private final BizSupplierLicenseService bizSupplierLicenseService;
    private final BizSupplierUserService bizSupplierUserService;
    private final EventBus eventBus;
    private final RedisTemplate<String, Object> redisTemplate;

    @Value("${spring.application.name}")
    private String applicationName;

    /**
     * 获取当前供应商信息
     */
    @GetMapping("/info")
    @PreAuthorize("isAuthenticated()")
    public BizSupplier getSupplierInfo(HttpServletRequest request) {
        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null || sessionUser.getSupplierId() == null) {
            throw new BizException("未登录或供应商信息缺失");
        }

        BizSupplier supplier = bizSupplierService.getById(sessionUser.getSupplierId());
        if (supplier == null) {
            throw new BizException("供应商信息不存在");
        }

        return supplier;
    }

    /**
     * 更新供应商基本信息
     */
    @PutMapping("/update")
    @PreAuthorize("isAuthenticated()")
    public BizSupplier updateSupplierInfo(@RequestBody BizSupplier supplier, HttpServletRequest request) {
        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null || sessionUser.getSupplierId() == null) {
            throw new BizException("未登录或供应商信息缺失");
        }

        // 验证手机号和邮箱格式
        ValidationUtils.validateContactInfo(supplier.getContactMobile(), supplier.getContactEmail());

        // 获取原有供应商信息
        BizSupplier originalSupplier = bizSupplierService.getById(sessionUser.getSupplierId());
        if (originalSupplier == null) {
            throw new BizException("供应商信息不存在");
        }

        // 设置ID，确保只更新当前供应商
        supplier.setId(sessionUser.getSupplierId());
        
        // 防止修改敏感字段
        supplier.setCode(originalSupplier.getCode());
        supplier.setStatus(originalSupplier.getStatus());
        supplier.setSupplierType(originalSupplier.getSupplierType());
        supplier.setCompanyId(originalSupplier.getCompanyId());
        supplier.setCreateTime(originalSupplier.getCreateTime());
        supplier.setUpdateTime(LocalDateTime.now());

        // 更新供应商信息
        bizSupplierService.updateById(supplier);
        
        log.info("供应商更新基本信息成功，供应商ID: {}, 供应商名称: {}", supplier.getId(), supplier.getName());

        return bizSupplierService.getById(sessionUser.getSupplierId());
    }

    /**
     * 新增供应商
     *
     * @param supplier 供应商信息
     * @return 新增的供应商
     */
    @PostMapping("/add")
    @PreAuthorize("isAuthenticated()")
    public BizSupplier addSupplier(@RequestBody BizSupplier supplier, HttpServletRequest request) {

        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null || sessionUser.getSupplierId() == null) {
            throw new BizException("未登录或供应商信息缺失");
        }

        // 验证手机号和邮箱格式
        ValidationUtils.validateContactInfo(supplier.getContactMobile(), supplier.getContactEmail());

        // 检查供应商名称是否重复
        validateSupplierUniqueness(supplier, null);

        // 设置默认状态为启用
        if (supplier.getStatus() == null) {
            supplier.setStatus(1);
        }

        // 平台管理系统默认创建平台供应商
        supplier.setSupplierType(2);
        supplier.setCompanyId(null); // 平台供应商不关联客户

        // 自动生成供应商编码
        supplier.setCode(generateSupplierCode());

        // 保存供应商
        bizSupplierService.save(supplier);

        // 创建供应商用户账号
        createSupplierUser(supplier);

        return supplier;
    }

    /**
     * 生成供应商编码
     * 格式：SUP + 时间戳后6位 + UUID去掉横线后取前8位 + 2位随机字母
     * 例如：SUP123456a1b2c3d4XY
     *
     * @return 供应商编码
     */
    private String generateSupplierCode() {
        String code;
        int maxAttempts = 10; // 最大重试次数
        int attempts = 0;

        do {
            // 获取当前时间戳的后6位
            long timestamp = System.currentTimeMillis();
            String timestampSuffix = String.valueOf(timestamp).substring(7); // 取后6位

            // 生成UUID并取前8位（去掉横线）
            String uuid = UUID.randomUUID().toString().replace("-", "");
            String uuidPrefix = uuid.substring(0, 8);

            // 生成2位随机大写字母
            SecureRandom secureRandom = new SecureRandom();
            StringBuilder randomLetters = new StringBuilder();
            for (int i = 0; i < 2; i++) {
                char randomChar = (char) ('A' + secureRandom.nextInt(26));
                randomLetters.append(randomChar);
            }

            // 组合最终编码：SUP + 时间戳后6位 + UUID前8位 + 2位随机字母
            code = "SUP" + timestampSuffix + uuidPrefix + randomLetters;

            attempts++;

            // 检查编码是否已存在
            LambdaQueryWrapper<BizSupplier> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(BizSupplier::getCode, code);
            boolean exists = bizSupplierService.count(wrapper) > 0;

            if (!exists) {
                break; // 编码唯一，跳出循环
            }

            if (attempts >= maxAttempts) {
                log.warn("生成供应商编码重试次数达到上限，使用当前编码: {}", code);
                break;
            }

            // 如果编码重复，等待1毫秒后重试（确保时间戳不同）
            try {
                Thread.sleep(1);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        } while (true);

        log.info("生成供应商编码: {}, 重试次数: {}", code, attempts);
        return code;
    }

    /**
     * 为供应商创建用户账号
     *
     * @param supplier 供应商信息
     * @return 创建的用户信息
     */
    private BizSupplierUser createSupplierUser(BizSupplier supplier) {
        if (!UserAccountUtils.isValidMobile(supplier.getContactMobile())) {
            log.warn("供应商联系人手机号为空，无法创建用户账号，供应商ID: {}", supplier.getId());
            return null;
        }

        // 检查当前供应商是否已有该手机号的账号
        LambdaQueryWrapper<BizSupplierUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizSupplierUser::getSupplierId, supplier.getId());
        wrapper.eq(BizSupplierUser::getMobile, supplier.getContactMobile());
        BizSupplierUser existingUser = bizSupplierUserService.getOne(wrapper);

        if (existingUser != null) {
            // 如果账号存在但被禁用，则启用它
            if (existingUser.getStatus() == 0) {
                existingUser.setStatus(1);
                existingUser.setEmail(supplier.getContactEmail()); // 更新邮箱信息
                bizSupplierUserService.updateById(existingUser);
                log.info("启用供应商已有用户账号，供应商ID: {}, 用户名: {}", supplier.getId(), existingUser.getUsername());
                return existingUser;
            } else {
                // 账号存在且可用，不需要新增
                log.info("供应商已有可用用户账号，无需创建，供应商ID: {}, 用户名: {}", supplier.getId(), existingUser.getUsername());
                return existingUser;
            }
        }

        // 生成随机密码
        String rawPassword = UserAccountUtils.generateRandomPassword();
        String encodedPassword = UserAccountUtils.encodePassword(rawPassword);

        // 创建新用户
        BizSupplierUser user = new BizSupplierUser();
        user.setSupplierId(supplier.getId());
        user.setUsername(supplier.getContactMobile()); // 使用手机号作为用户名
        user.setPassword(encodedPassword);
        user.setEmail(supplier.getContactEmail());
        user.setMobile(supplier.getContactMobile());
        user.setStatus(1); // 启用状态

        bizSupplierUserService.save(user);

        log.info("为供应商创建用户账号成功，供应商ID: {}, 用户名: {}, 原始密码: {}",
                supplier.getId(), user.getUsername(), rawPassword);

        // 发布供应商用户创建事件
        if (StringUtils.hasText(user.getEmail())) {
            SupplierUserPasswordEvent event = SupplierUserPasswordEvent.createUserCreatedEvent(applicationName, user.getId(), "系统");
            event.setSupplierUserId(user.getId());
            event.setSupplierId(supplier.getId());
            event.setSupplierName(supplier.getName());
            event.setUsername(user.getUsername());
            event.setEmail(user.getEmail());
            event.setMobile(user.getMobile());
            event.setRawPassword(rawPassword);
            event.setStatus(user.getStatus());

            eventBus.publishAsync(event);
            log.info("供应商用户创建事件已发布，用户ID: {}, 邮箱: {}", user.getId(), user.getEmail());
        }

        return user;
    }

    /**
     * 验证供应商信息的唯一性
     *
     * @param supplier  供应商信息
     * @param excludeId 排除的供应商ID（用于更新时排除自身）
     */
    private void validateSupplierUniqueness(BizSupplier supplier, Long excludeId) {
        // 检查供应商名称是否重复
        if (StringUtils.hasText(supplier.getName())) {
            LambdaQueryWrapper<BizSupplier> nameWrapper = new LambdaQueryWrapper<>();
            nameWrapper.eq(BizSupplier::getName, supplier.getName());
            if (excludeId != null) {
                nameWrapper.ne(BizSupplier::getId, excludeId);
            }
            long nameCount = bizSupplierService.count(nameWrapper);
            if (nameCount > 0) {
                throw new BizException("供应商名称已存在：" + supplier.getName());
            }
        }

        // 检查统一社会信用代码是否重复
        if (StringUtils.hasText(supplier.getUsciCode())) {
            LambdaQueryWrapper<BizSupplier> usciWrapper = new LambdaQueryWrapper<>();
            usciWrapper.eq(BizSupplier::getUsciCode, supplier.getUsciCode());
            if (excludeId != null) {
                usciWrapper.ne(BizSupplier::getId, excludeId);
            }
            long usciCount = bizSupplierService.count(usciWrapper);
            if (usciCount > 0) {
                throw new BizException("统一社会信用代码已存在：" + supplier.getUsciCode());
            }
        }
    }

    /**
     * 获取供应商资质列表
     */
    @GetMapping("/licenses")
    @PreAuthorize("isAuthenticated()")
    public List<BizSupplierLicense> getSupplierLicenseList(HttpServletRequest request) {
        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null || sessionUser.getSupplierId() == null) {
            throw new BizException("未登录或供应商信息缺失");
        }

        return bizSupplierLicenseService.list(
            new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<BizSupplierLicense>()
                .eq(BizSupplierLicense::getSupplierId, sessionUser.getSupplierId())
                .orderByDesc(BizSupplierLicense::getCreateTime)
        );
    }

    /**
     * 新增供应商资质
     */
    @PostMapping("/license")
    @PreAuthorize("isAuthenticated()")
    public BizSupplierLicense addSupplierLicense(@RequestBody BizSupplierLicense license, HttpServletRequest request) {
        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null || sessionUser.getSupplierId() == null) {
            throw new BizException("未登录或供应商信息缺失");
        }

        // 设置供应商ID，确保只添加当前供应商的资质
        license.setSupplierId(sessionUser.getSupplierId());
        license.setCreateTime(LocalDateTime.now());
        license.setUpdateTime(LocalDateTime.now());

        // 验证必填字段
        if (!StringUtils.hasText(license.getLicenseType())) {
            throw new BizException("资质类型不能为空");
        }
        if (!StringUtils.hasText(license.getFilePath())) {
            throw new BizException("资质图片不能为空");
        }

        bizSupplierLicenseService.save(license);
        
        log.info("供应商新增资质成功，供应商ID: {}, 资质类型: {}", sessionUser.getSupplierId(), license.getLicenseType());

        return license;
    }

    /**
     * 删除供应商资质
     */
    @DeleteMapping("/license/{id}")
    @PreAuthorize("isAuthenticated()")
    public void deleteSupplierLicense(@PathVariable Long id, HttpServletRequest request) {
        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null || sessionUser.getSupplierId() == null) {
            throw new BizException("未登录或供应商信息缺失");
        }

        // 验证资质是否存在且属于当前供应商
        BizSupplierLicense license = bizSupplierLicenseService.getById(id);
        if (license == null) {
            throw new BizException("资质不存在");
        }
        if (!license.getSupplierId().equals(sessionUser.getSupplierId())) {
            throw new BizException("无权删除该资质");
        }

        bizSupplierLicenseService.removeById(id);
        
        log.info("供应商删除资质成功，供应商ID: {}, 资质ID: {}", sessionUser.getSupplierId(), id);
    }

    /**
     * 更新供应商资质
     */
    @PutMapping("/license/{id}")
    @PreAuthorize("isAuthenticated()")
    public BizSupplierLicense updateSupplierLicense(@PathVariable Long id, @RequestBody BizSupplierLicense license, HttpServletRequest request) {
        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null || sessionUser.getSupplierId() == null) {
            throw new BizException("未登录或供应商信息缺失");
        }

        // 验证资质是否存在且属于当前供应商
        BizSupplierLicense existingLicense = bizSupplierLicenseService.getById(id);
        if (existingLicense == null) {
            throw new BizException("资质不存在");
        }
        if (!existingLicense.getSupplierId().equals(sessionUser.getSupplierId())) {
            throw new BizException("无权修改该资质");
        }

        // 设置ID和供应商ID，确保只更新当前供应商的资质
        license.setId(id);
        license.setSupplierId(sessionUser.getSupplierId());
        license.setUpdateTime(LocalDateTime.now());

        // 验证必填字段
        if (!StringUtils.hasText(license.getLicenseType())) {
            throw new BizException("资质类型不能为空");
        }
        if (!StringUtils.hasText(license.getFilePath())) {
            throw new BizException("资质图片不能为空");
        }

        bizSupplierLicenseService.updateById(license);
        
        log.info("供应商更新资质成功，供应商ID: {}, 资质ID: {}", sessionUser.getSupplierId(), id);

        return bizSupplierLicenseService.getById(id);
    }

    /**
     * 获取供应商统计信息
     */
    @GetMapping("/statistics")
    @PreAuthorize("isAuthenticated()")
    public java.util.Map<String, Object> getSupplierStatistics(HttpServletRequest request) {
        SessionUser sessionUser = (SessionUser) request.getAttribute(SecurityConstants.USER_KEY);
        if (sessionUser == null || sessionUser.getSupplierId() == null) {
            throw new BizException("未登录或供应商信息缺失");
        }

        java.util.Map<String, Object> statistics = new java.util.HashMap<>();
        
        // TODO: 实现供应商统计信息，如商品数量、订单数量等
        // 这里可以根据实际业务需求添加更多统计数据
        
        return statistics;
    }
} 