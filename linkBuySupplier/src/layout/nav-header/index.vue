<template>
  <div class="nav-header">
    <div class="content">
      <div class="left">
        <span class="nav-header-logo">{{ supplierName }}</span>
      </div>

      <!-- 中间菜单区，左对齐 -->
      <div class="center-menu">
        <span v-for="menu in menuList" :key="menu.key" :class="['menu-item', { active: menu.isSelected }]"
          @click="handleMenuClick(menu.key)">
          <el-icon v-if="menu.icon" class="menu-icon">
            <component :is="menu.icon" />
          </el-icon>
          {{ menu.name }}
        </span>
      </div>

      <!-- 右侧用户信息固定右对齐 -->
      <div class="right">
        <user-info />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import UserInfo from '@/layout/nav-header/user-info.vue'
import { useUser } from '@/store/user'
import { useMenuStore, TopMenu } from '@/store/menu'
import { ElIcon } from 'element-plus'
import { getCurrentScode } from '@/utils/url'

const userStore = useUser()
const supplierName = userStore.getSupplierName()

export default defineComponent({
  name: 'nav-header',
  components: {
    UserInfo,
    ElIcon
  },
  props: {
    topMenus: {
      type: Array as () => TopMenu[],
      required: true,
      default: () => []
    }
  },
  emits: ['foldChange'],
  setup(props, { emit }) {
    const router = useRouter()
    const menuStore = useMenuStore()

    // const activeMenu = ref('')

    const userStore = useUser()

    const userInfo = computed(() => userStore.getUserInfo())

    // 计算属性：确保菜单数据已加载
    const menuList = computed(() => {
      if (!menuStore.isLoaded) {
        return []
      }
      return props.topMenus
    })

    const handleMenuClick = (key: string) => {
      const scode = getCurrentScode()
      
      if (key === '0') {
        // 点击首页，跳转到 / 并设置当前路径
        menuStore.setCurrentRoutePath('/')
        if (scode) {
          router.push({
            path: '/',
            query: { scode }
          })
        } else {
          router.push({
            path: '/'
          })
        }
      } else {
        // 点击其他菜单，只设置当前路径，不进行路由跳转
        const target = props.topMenus.find(menu => menu.key === key)
        if (target) {
          menuStore.setCurrentRoutePath(target.route)
          if (!target.route.endsWith("noJump")) {
            if (target.route) {
              if (scode) {
                router.push({
                  path: target.route,
                  query: { scode }
                })
              } else {
                router.push({
                  path: target.route
                })
              }
            }
          }
        }
      }
    }

    onMounted(async () => {
      // 确保菜单数据已加载
      if (!menuStore.isLoaded) {
        await menuStore.fetchMenu()
      }
    })

    return {
      menuStore,
      supplierName,
      // activeMenu,
      menuList,
      userInfo,
      handleMenuClick
    }
  }
})
</script>


<style lang="scss" scoped>
.nav-header {
  width: 100%;

  &-logo {
    font-family: PingFang SC;
    font-size: 18px;
    color: var(--el-color-primary);
    font-weight: 700;
    margin-left: 8px;
    width: 170px;
    text-align: left;
  }

  .content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
    // padding: 0 20px;
  }

  .left {
    display: flex;
    align-items: center;

    .service-icon {
      width: 28px;
      height: 28px;
      border-radius: 8px;
      box-shadow: 0px 2px 4px var(--el-svg-monochrome-grey);
      background: var(--el-color-white);
    }

    .user-info-item.orgination {
      width: 118px;
      margin-left: 20px;

      :deep(.el-select__wrapper) {
        box-shadow: none;
        background-color: #F8F9FB !important;
        border-radius: 4px;
        height: 28px;
      }
    }
  }

  .center-menu {
    display: flex;
    align-items: center;
    margin-left: 20px; // 与左边 left 保持间距

    .menu-item {
      width: 85px;
      margin: 0 12px;
      cursor: pointer;
      position: relative;
      font-family: Helvetica;
      font-size: 16px;
      color: var(--el-text-color-primary);
      font-weight: 700;
      display: flex;
      align-items: center;

      .menu-icon {
        margin-right: 5px; // 图标与文字之间的间距
      }

      &.active {
        color: var(--el-color-primary);
        font-weight: bold;
      }

      &.active::after {
        content: '';
        position: absolute;
        bottom: -6px;
        left: 0;
        right: 0;
        height: 2px;
        background: var(--el-color-primary);
        margin: auto;
        width: 60%;
        border-radius: 1px;
      }
    }
  }

  .right {
    margin-left: auto;
    display: flex;
    align-items: center;
  }
}
</style>
