<template>
  <div class="menu-item" :class="{ 'is-selected': menu.isSelected }">
    <div 
      class="menu-item-content"
      :style="{ paddingLeft: `${level * 16}px` }"
      @click="handleClick"
    >
      <el-icon v-if="menu.icon" class="menu-icon">
        <component :is="menu.icon" />
      </el-icon>
      <span class="menu-label">{{ menu.name }}</span>
      <el-icon 
        v-if="hasChildren" 
        class="arrow-icon"
        :class="{ 'is-expanded': isExpanded }"
      >
        <arrow-down />
      </el-icon>
    </div>
    
    <div v-if="hasChildren && isExpanded" class="submenu">
      <menu-item
        v-for="child in menu.children"
        :key="child.id"
        :menu="child"
        :level="level + 1"
        @select="$emit('select', $event)"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch } from 'vue'
import type { MenuDto } from '@/types/menu'
import { ArrowDown } from '@element-plus/icons-vue'

export default defineComponent({
  name: 'MenuItem',
  components: {
    ArrowDown
  },
  props: {
    menu: {
      type: Object as () => MenuDto,
      required: true
    },
    level: {
      type: Number,
      default: 1
    }
  },
  emits: ['select'],
  setup(props, { emit }) {
    const isExpanded = ref(false)
    
    const hasChildren = computed(() => {
      return props.menu.children && props.menu.children.length > 0
    })

    // 检查是否有子菜单被选中，递归查找
    const hasSelectedChild = (menuItems: MenuDto[] | null | undefined): boolean => {
      if (!menuItems) return false;
      for (const item of menuItems) {
        if (item.isSelected) return true;
        if (item.children && item.children.length > 0) {
          if (hasSelectedChild(item.children)) return true;
        }
      }
      return false;
    };

    // 监听 menu prop 的变化，检查是否有选中的子菜单，并展开当前菜单项
    watch(
      () => props.menu,
      (newMenu) => {
        if (hasChildren.value && hasSelectedChild(newMenu.children)) {
          isExpanded.value = true;
        }
      },
      { immediate: true, deep: true }
    );

    const handleClick = () => {
      if (hasChildren.value) {
        isExpanded.value = !isExpanded.value
      } else {
        // 触发选择事件
        emit('select', props.menu)
      }
    }

    return {
      isExpanded,
      hasChildren,
      handleClick
    }
  }
})
</script>

<style scoped lang="scss">
.menu-item {
  background-color: inherit;

  .menu-item-content {
    display: flex;
    align-items: center;
    padding: 6px 16px;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      background-color: rgba(91, 143, 249, 0.1);
    }
  }

  &.is-selected {
    > .menu-item-content {
      background-color: var(--el-color-primary-light-4);
      color: var(--el-color-primary);
      font-weight: 600;
    }
  }

  .menu-icon {
    margin-right: 8px;
    font-size: 18px;
  }

  .menu-label {
    flex: 1;
    font-size: 14px;
  }

  .arrow-icon {
    margin-left: 8px;
    transition: transform 0.3s;
    
    &.is-expanded {
      transform: rotate(180deg);
    }
  }

  .submenu {
    background-color: transparent;
  }
}
</style> 