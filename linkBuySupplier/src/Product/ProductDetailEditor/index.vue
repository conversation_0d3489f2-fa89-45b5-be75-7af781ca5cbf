<template>
  <div class="product-detail-editor">
    <div class="detail-editor-container">
      <!-- 左侧富文本编辑器 -->
      <div class="editor-section">
        <div class="editor-wrapper">
          <QuillEditor 
            ref="detailQuillEditor" 
            v-model:content="content" 
            :options="editorOption" 
            theme="snow" 
            contentType="html"
            style="height: 100%; min-height: 400px;"
          />
        </div>
      </div>

      <!-- 右侧预览区域 -->
      <div class="preview-section">
        <div class="section-header">
          <h4>移动端预览</h4>
        </div>
        <div class="preview-wrapper">
          <div class="mobile-preview">
            <div class="preview-content" v-html="previewContent"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详情编辑器图片选择器 -->
    <ImageSelector 
      v-model:visible="imageSelectVisible" 
      title="选择商品详情图片" 
      :multiple="true" 
      @select="handleImageSelected"
      @cancel="imageSelectVisible = false" 
    />
  </div>
</template>

<script setup>
import { ref, computed, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { QuillEditor } from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css'
import ImageSelector from '@/components/ImageSelector/index.vue'

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const content = ref(props.modelValue)
const detailQuillEditor = ref(null)
const imageSelectVisible = ref(false)

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  content.value = newVal
})

// 监听 content 变化并同步到父组件
watch(content, (newVal) => {
  emit('update:modelValue', newVal)
})

// 计算属性：处理富文本编辑器内容
const previewContent = computed(() => {
  if (typeof content.value === 'string') {
    return content.value
  }
  return content.value || ''
})

// 富文本编辑器配置
const editorOption = {
  modules: {
    toolbar: {
      container: [
        ['bold', 'italic', 'underline', 'strike'],
        ['blockquote', 'code-block'],
        [{ header: 1 }, { header: 2 }],
        [{ list: 'ordered' }, { list: 'bullet' }],
        [{ script: 'sub' }, { script: 'super' }],
        [{ indent: '-1' }, { indent: '+1' }],
        [{ direction: 'rtl' }],
        [{ size: ['small', false, 'large', 'huge'] }],
        [{ header: [1, 2, 3, 4, 5, 6, false] }],
        [{ color: [] }, { background: [] }],
        [{ font: [] }],
        [{ align: [] }],
        ['clean'],
        ['link', 'image', 'video', 'formula'],
        ['detailImage']
      ]
    },
  },
  placeholder: "请输入商品详情",
}

// 富文本编辑器图片选择处理
const handleImageSelected = (images) => {
  console.log('选择的详情图片:', images)

  // 如果是数组（多选模式），处理多张图片
  if (Array.isArray(images)) {
    if (!images || !images.length) {
      ElMessage.error('未选择图片')
      return
    }

    try {
      const quill = detailQuillEditor.value.getQuill()
      if (!quill) {
        console.error('未找到Quill编辑器实例')
        return
      }

      // 获取当前光标位置
      const range = quill.getSelection()
      let insertIndex = range ? range.index : quill.getLength() - 1

      // 依次插入每张图片
      images.forEach((img, index) => {
        if (img && (img.url || img.path)) {
          const imageUrl = img.url || img.path
          // 插入图片
          quill.insertEmbed(insertIndex, 'image', imageUrl)
          // 在图片后添加换行
          insertIndex += 1
          if (index < images.length - 1) {
            quill.insertText(insertIndex, '\n')
            insertIndex += 1
          }
        }
      })

      // 将光标移动到最后一张图片后
      quill.setSelection(insertIndex, 0)

      ElMessage.success(`已插入${images.length}张图片`)
    } catch (error) {
      console.error('插入图片时出错:', error)
      ElMessage.error('插入图片失败')
    }
    return
  }

  // 单张图片处理逻辑
  if (!images || !(images.url || images.path)) {
    ElMessage.error('图片数据无效')
    return
  }

  try {
    const quill = detailQuillEditor.value.getQuill()
    if (!quill) {
      console.error('未找到Quill编辑器实例')
      return
    }

    const imageUrl = images.url || images.path
    // 在当前光标位置插入图片
    const range = quill.getSelection()
    if (range) {
      quill.insertEmbed(range.index, 'image', imageUrl)
      quill.setSelection(range.index + 1)
    } else {
      quill.insertEmbed(quill.getLength() - 1, 'image', imageUrl)
    }

    ElMessage.success('图片已插入')
  } catch (error) {
    console.error('插入图片时出错:', error)
    ElMessage.error('插入图片失败')
  }
}

// 初始化富文本编辑器自定义按钮
const initDetailEditor = () => {
  setTimeout(() => {
    const toolbar = document.querySelector('.ql-toolbar')
    if (toolbar) {
      const detailImageBtn = toolbar.querySelector('.ql-detailImage')
      if (detailImageBtn) {
        detailImageBtn.innerHTML = `<svg viewBox="0 0 18 18" width="18" height="18"><rect class="ql-stroke" height="10" width="12" x="3" y="4"></rect><circle class="ql-fill" cx="6" cy="7" r="1"></circle><polyline class="ql-even ql-fill" points="5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12"></polyline></svg>`
        detailImageBtn.onclick = () => {
          nextTick(() => {
            imageSelectVisible.value = true
          })
        }
      }
    }
  }, 500)
}

// 暴露方法给父组件
defineExpose({
  initEditor: initDetailEditor,
  getContent: () => content.value,
  setContent: (newContent) => {
    content.value = newContent
  }
})

// 组件挂载后初始化编辑器
nextTick(() => {
  initDetailEditor()
})
</script>

<style scoped lang="scss">
.product-detail-editor {
  height: 100%;
  max-height: 100%;
  overflow: hidden;
  // 商品详情抽屉样式
  .detail-editor-container {
    display: flex;
    gap: 20px;
    height: 100%;
    // height: calc(100vh - 160px); // 撑满屏幕高度，减去更多空间防止滚动
    // max-height: calc(100vh - 160px);
    overflow: hidden;
  }

  .editor-section {
    width: 50%;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
  }

  .preview-section {
    width: 50%;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
  }

  .section-header {
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--el-border-color-lighter);

    h4 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }

  .editor-wrapper {
    height: 100%;
    flex: 1;
    overflow: hidden;
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
    // height: calc(100vh - 240px); // 动态计算编辑器高度
    // max-height: calc(100vh - 240px); 
    display: flex;
    flex-direction: column;

    :deep(.ql-toolbar) {
      flex-shrink: 0;
      border-bottom: 1px solid var(--el-border-color);
    }

    :deep(.ql-container) {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }

    :deep(.ql-editor) {
      flex: 1;
      overflow-y: auto;
      padding: 16px;
      font-size: 14px;
      line-height: 1.6;
      max-height: none;
    }
  }

  .preview-wrapper {
    flex: 1;
    overflow: hidden;
    height: calc(100vh - 240px); // 与编辑器高度一致
    max-height: calc(100vh - 240px);
  }

  .mobile-preview {
    width: 100%;
    height: 100%;
    background: #f5f5f5;
    overflow: hidden;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
  }

  .preview-content {
    width: 350px;
    height: calc(100vh - 350px);
    background: white;
    border-radius: 25px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    padding: 20px 15px;
    box-sizing: border-box;
    position: relative;
    border: 8px solid #2c2c2c;
    overflow-y: auto;
    
    // 模拟手机顶部刘海
    &::before {
      content: '';
      position: absolute;
      top: -8px;
      left: 50%;
      transform: translateX(-50%);
      width: 100px;
      height: 18px;
      background: #2c2c2c;
      border-radius: 0 0 10px 10px;
      z-index: 1;
    }
    
    // 模拟手机底部指示条
    &::after {
      content: '';
      position: absolute;
      bottom: -8px;
      left: 50%;
      transform: translateX(-50%);
      width: 80px;
      height: 4px;
      background: #2c2c2c;
      border-radius: 2px;
    }

    // 预览内容样式
    :deep(h1) {
      font-size: 22px;
      font-weight: 600;
      margin: 0 0 16px 0;
      color: #1a1a1a;
      line-height: 1.4;
    }

    :deep(h2) {
      font-size: 18px;
      font-weight: 600;
      margin: 20px 0 12px 0;
      color: #1a1a1a;
      line-height: 1.4;
    }

    :deep(h3) {
      font-size: 16px;
      font-weight: 600;
      margin: 16px 0 8px 0;
      color: #1a1a1a;
      line-height: 1.4;
    }

    :deep(p) {
      font-size: 14px;
      line-height: 1.7;
      margin: 0 !important;
      margin-block-start: 0 !important;
      margin-block-end: 0 !important;
      margin-inline-start: 0 !important;
      margin-inline-end: 0 !important;
      color: #333;
      word-wrap: break-word;
    }

    :deep(img) {
      max-width: 100%;
      height: auto;
      display: block;
      margin: 0 !important;
      border-radius: 0;
      box-shadow: none;
    }

    :deep(ul), :deep(ol) {
      padding-left: 20px;
      margin: 16px 0;
    }

    :deep(li) {
      font-size: 14px;
      line-height: 1.7;
      margin: 6px 0;
      color: #333;
    }

    :deep(blockquote) {
      border-left: 4px solid #409eff;
      padding-left: 16px;
      margin: 20px 0;
      font-style: italic;
      color: #666;
      background: #f8f9fa;
      padding: 12px 16px;
      border-radius: 4px;
    }

    :deep(strong) {
      font-weight: 600;
      color: #1a1a1a;
    }

    :deep(em) {
      font-style: italic;
      color: #666;
    }

    // 处理空内容的情况
    &:empty::before {
      // content: '请在左侧编辑器中输入商品详情内容';
      color: #999;
      font-size: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      // height: 200px;
      text-align: center;
    }
  }

  // 富文本编辑器自定义按钮样式
  :deep(.ql-detailImage) {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 6px;

    svg {
      pointer-events: none;
    }
  }

  // 响应式适配
  @media (max-height: 900px) {
    .detail-editor-container {
      height: calc(100vh - 140px);
      max-height: calc(100vh - 140px);
    }
    
    .editor-wrapper,
    .preview-wrapper {
      height: calc(100vh - 220px);
      max-height: calc(100vh - 220px);
    }
    
    .preview-content {
      height: calc(100vh - 300px);
      max-height: calc(100vh - 300px);
      min-height: 280px;
    }
  }

  @media (max-height: 700px) {
    .detail-editor-container {
      height: calc(100vh - 120px);
      max-height: calc(100vh - 120px);
    }
    
    .editor-wrapper,
    .preview-wrapper {
      height: calc(100vh - 180px);
      max-height: calc(100vh - 180px);
    }
    
    .preview-content {
      height: calc(100vh - 260px);
      max-height: calc(100vh - 260px);
      min-height: 200px;
    }
  }
}
</style> 