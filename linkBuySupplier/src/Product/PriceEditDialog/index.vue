<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="90%"
    :before-close="handleClose"
    class="price-edit-dialog"
    top="5vh"
  >
    <div class="dialog-content">
      <!-- 商品基本信息 -->
      <div class="product-info-section" v-if="selectedProducts.length === 1">
        <div class="product-header">
          <div class="product-image">
            <el-image 
              :src="selectedProducts[0].productImage" 
              fit="cover"
              style="width: 80px; height: 80px; border-radius: 8px;"
            />
          </div>
          <div class="product-details">
            <h3 class="product-name">{{ selectedProducts[0].productName }}</h3>
            <div class="product-meta">
              <span class="product-category">{{ selectedProducts[0].category }}</span>
              <el-tag :type="getStatusTagType(selectedProducts[0].status)" size="small">
                {{ getStatusText(selectedProducts[0].status) }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 批量编辑提示 -->
      <div class="batch-info-section" v-else-if="selectedProducts.length > 1">
        <el-alert
          title="批量改价"
          :description="`已选择 ${selectedProducts.length} 个商品进行批量价格修改`"
          type="info"
          :closable="false"
          show-icon
        />
      </div>

      <!-- 编辑表单 -->
    <el-form 
        ref="editFormRef"
        :model="editForm"
        :rules="editFormRules"
      label-width="120px"
        class="edit-form"
    >
        <!-- 申请说明 -->
        <el-form-item label="申请说明" prop="requestReason">
          <el-input
            v-model="editForm.requestReason"
            type="textarea"
            :rows="3"
            placeholder="请简要说明本次价格修改的原因和目的"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <!-- 价格修改区域 -->
        <div class="price-edit-section">
          <div class="section-title">
            <h4>价格修改</h4>
            <p>修改商品的价格信息（不允许修改规格类型和SKU结构）</p>
          </div>

          <!-- 规格类型显示（只读） -->
          <el-form-item label="商品规格">
            <el-tag :type="currentProduct?.specType === 'multiple' ? 'warning' : 'info'">
              {{ currentProduct?.specType === 'multiple' ? '多规格商品' : '单规格商品' }}
            </el-tag>
            <span class="spec-tip">（规格类型不可修改，只能编辑现有SKU的价格）</span>
      </el-form-item>

          <!-- 单规格价格编辑 -->
          <template v-if="currentProduct?.specType === 'single'">
            <div class="single-spec-form">
              <h5 class="subsection-title">价格设置</h5>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item>
                    <template #label>
                      <span>成本价</span>
                      <el-tooltip content="一件代发结算成本，用于一件代发结算" placement="top">
                        <el-icon class="price-help-icon">
                          <QuestionFilled />
                        </el-icon>
                      </el-tooltip>
                    </template>
                    <el-input v-model="editForm.costPrice" placeholder="请输入成本价">
                      <template #suffix>元</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item>
                    <template #label>
                      <span>采购价</span>
                      <el-tooltip content="采购价是商品的进货成本，用于前置仓采购" placement="top">
                        <el-icon class="price-help-icon">
                          <QuestionFilled />
                        </el-icon>
                      </el-tooltip>
                    </template>
                    <el-input v-model="editForm.purchasePrice" placeholder="请输入采购价">
                      <template #suffix>元</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="划线价">
                    <el-input v-model="editForm.strikethroughPrice" placeholder="请输入划线价">
                      <template #suffix>元</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
          
              <!-- 显示其他信息（只读） -->
              <h5 class="subsection-title">其他信息（只读）</h5>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="库存">
                    <el-input v-model="editForm.totalInventory" placeholder="库存" disabled />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="安全库存">
                    <el-input v-model="editForm.safetyStock" placeholder="安全库存" disabled />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </template>

          <!-- 多规格价格编辑 -->
          <template v-else-if="currentProduct?.specType === 'multiple'">
            <div class="multiple-spec-form">
              <h5 class="subsection-title">SKU价格设置</h5>
              <div class="spec-info">
                <p>当前商品共有 <strong>{{ skuList.length }}</strong> 个SKU，请逐个设置价格：</p>
              </div>
              
              <!-- SKU列表表格 -->
              <el-table :data="skuList" border class="sku-table">
                <el-table-column label="规格组合" min-width="200">
                  <template #default="{ row }">
                    <div class="spec-combination">
                      <div v-if="row.spec1Value" class="spec-item">
                        {{ row.spec1Name }}: {{ row.spec1Value }}
                      </div>
                      <div v-if="row.spec2Value" class="spec-item">
                        {{ row.spec2Name }}: {{ row.spec2Value }}
                      </div>
                      <div v-if="row.spec3Value" class="spec-item">
                        {{ row.spec3Name }}: {{ row.spec3Value }}
                      </div>
                    </div>
                  </template>
                </el-table-column>
                
                <el-table-column label="SKU图片" width="100">
                  <template #default="{ row }">
                    <el-image 
                      v-if="row.imgUrl" 
                      :src="row.imgUrl" 
                      fit="cover"
                      style="width: 50px; height: 50px; border-radius: 4px;"
          />
                    <span v-else class="no-image">无图片</span>
                  </template>
                </el-table-column>

                <el-table-column width="150">
                  <template #header>
                    <span>成本价</span>
                    <el-tooltip content="一件代发结算成本" placement="top">
                      <el-icon class="price-help-icon">
                        <QuestionFilled />
                      </el-icon>
                    </el-tooltip>
                  </template>
                  <template #default="{ row }">
                    <el-input v-model="row.costPrice" placeholder="成本价" size="small">
                      <template #suffix>元</template>
                    </el-input>
                  </template>
                </el-table-column>

                <el-table-column width="150">
                  <template #header>
                    <span>采购价</span>
                    <el-tooltip content="商品的进货成本" placement="top">
                      <el-icon class="price-help-icon">
                        <QuestionFilled />
                      </el-icon>
                    </el-tooltip>
                  </template>
                  <template #default="{ row }">
                    <el-input v-model="row.purchasePrice" placeholder="采购价" size="small">
                      <template #suffix>元</template>
                    </el-input>
                  </template>
                </el-table-column>

                <el-table-column label="划线价" width="150">
                  <template #default="{ row }">
                    <el-input v-model="row.strikethroughPrice" placeholder="划线价" size="small">
                      <template #suffix>元</template>
                    </el-input>
                  </template>
                </el-table-column>

                <!-- 只读信息列 -->
                <el-table-column label="库存" width="100">
                  <template #default="{ row }">
                    <span class="readonly-info">{{ row.totalInventory || 0 }}</span>
                  </template>
                </el-table-column>

                <el-table-column label="安全库存" width="100">
                  <template #default="{ row }">
                    <span class="readonly-info">{{ row.safetyStock || 0 }}</span>
                  </template>
                </el-table-column>

                <el-table-column label="操作" width="120" fixed="right">
                  <template #default="{ row, $index }">
          <el-button 
                      type="danger" 
            size="small" 
                      plain
                      :disabled="skuList.length <= 1"
                      @click="handleDeleteSku($index)"
          >
                      删除
          </el-button>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 添加新规格 -->
              <div class="add-spec-section">
                <h6>添加新规格</h6>
                <el-form :model="newSpecForm" inline size="small">
                  <el-form-item v-if="currentProduct?.originalData?.spec1Name" :label="currentProduct.originalData.spec1Name">
                    <el-input 
                      v-model="newSpecForm.spec1Value" 
                      :placeholder="`输入${currentProduct.originalData.spec1Name}`"
                      style="width: 120px;"
                    />
      </el-form-item>
                  <el-form-item v-if="currentProduct?.originalData?.spec2Name" :label="currentProduct.originalData.spec2Name">
                    <el-input 
                      v-model="newSpecForm.spec2Value" 
                      :placeholder="`输入${currentProduct.originalData.spec2Name}`"
                      style="width: 120px;"
                    />
      </el-form-item>
                  <el-form-item v-if="currentProduct?.originalData?.spec3Name" :label="currentProduct.originalData.spec3Name">
                    <el-input 
                      v-model="newSpecForm.spec3Value" 
                      :placeholder="`输入${currentProduct.originalData.spec3Name}`"
                      style="width: 120px;"
                    />
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="handleAddSpec">添加规格</el-button>
                  </el-form-item>
                </el-form>
              </div>

              <!-- 批量设置价格 -->
              <div class="batch-price-setting">
                <h6>批量设置价格</h6>
                <el-row :gutter="20">
                  <el-col :span="6">
                    <el-form-item label="成本价">
                      <el-input v-model="batchPrice.costPrice" placeholder="批量设置成本价">
                        <template #suffix>元</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="采购价">
                      <el-input v-model="batchPrice.purchasePrice" placeholder="批量设置采购价">
                        <template #suffix>元</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="划线价">
                      <el-input v-model="batchPrice.strikethroughPrice" placeholder="批量设置划线价">
                        <template #suffix>元</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-button type="primary" @click="applyBatchPrice">应用到所有SKU</el-button>
                  </el-col>
                </el-row>
              </div>
            </div>
          </template>
        </div>
    </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          :loading="loading" 
          :disabled="!hasPriceChanges()"
          @click="handleSubmitRequest"
        >
          提交改价申请
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { QuestionFilled } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  selectedProducts: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:visible', 'confirm'])

// 内部可见性状态
const visible = ref(false)

// 表单引用
const editFormRef = ref()

// 当前商品（单个商品编辑时使用）
const currentProduct = ref(null)

// SKU列表
const skuList = ref([])

// 表单数据
const editForm = reactive({
  requestReason: '',
  
  // 单规格价格字段
  costPrice: '',
  purchasePrice: '',
  strikethroughPrice: '',
  totalInventory: '',
  safetyStock: ''
})

// 批量价格设置
const batchPrice = reactive({
  costPrice: '',
  purchasePrice: '',
  strikethroughPrice: ''
})

// 新规格表单
const newSpecForm = reactive({
  spec1Value: '',
  spec2Value: '',
  spec3Value: ''
})

// 表单验证规则
const editFormRules = {
  requestReason: [
    { required: true, message: '请填写申请说明', trigger: 'blur' },
    { min: 4, message: '申请说明至少需要4个字符', trigger: 'blur' }
  ]
}

// 加载状态
const loading = ref(false)

// 计算属性
const dialogTitle = computed(() => {
  if (props.selectedProducts.length === 1) {
    return `改价申请 - ${props.selectedProducts[0].productName}`
  } else if (props.selectedProducts.length > 1) {
    return `批量改价申请 - ${props.selectedProducts.length}个商品`
  }
  return '改价申请'
})

// 计算属性：是否有价格变化
const hasChanges = computed(() => {
  return hasPriceChanges()
})

// 监听props的visible变化
watch(() => props.visible, (newVal) => {
  visible.value = newVal
  if (newVal) {
    initializeForm()
  }
}, { immediate: true })

// 监听内部visible变化，同步到父组件
watch(visible, (newVal) => {
  emit('update:visible', newVal)
  // 当弹窗关闭时重置表单
  if (!newVal) {
    resetForm()
  }
})

// 方法
const initializeForm = async () => {
  if (props.selectedProducts.length === 1) {
    const product = props.selectedProducts[0]
    const originalData = product.originalData
    
    // 判断规格类型 - 修正判断逻辑
    let specType = 'single'
    
    // 检查是否有规格名称定义
    if (originalData?.spec1Name || originalData?.spec2Name || originalData?.spec3Name) {
      specType = 'multiple'
    }
    // 或者检查SKU是否有规格值
    else if (originalData?.skuList && originalData.skuList.length > 0) {
      const hasSpecValues = originalData.skuList.some(sku => 
        sku.spec1Value || sku.spec2Value || sku.spec3Value
      )
      if (hasSpecValues) {
        specType = 'multiple'
      }
    }
    
    currentProduct.value = {
      ...product,
      specType: specType
    }
    
    // 加载SKU数据
    if (originalData?.skuList && Array.isArray(originalData.skuList)) {
      if (specType === 'single') {
        // 单规格商品，填充表单
        const sku = originalData.skuList[0]
        if (sku) {
          editForm.costPrice = sku.costPrice?.toString() || ''
          editForm.purchasePrice = sku.purchasePrice?.toString() || ''
          editForm.strikethroughPrice = sku.strikethroughPrice?.toString() || ''
          editForm.totalInventory = sku.totalInventory?.toString() || ''
          editForm.safetyStock = sku.safetyStock?.toString() || ''
        }
      } else {
        // 多规格商品，加载SKU列表
        skuList.value = originalData.skuList.map(sku => ({
          id: sku.id,
          skuCode: sku.skuCode,
          spec1Name: originalData.spec1Name || sku.spec1Name,
          spec1Value: sku.spec1Value,
          spec2Name: originalData.spec2Name || sku.spec2Name,
          spec2Value: sku.spec2Value,
          spec3Name: originalData.spec3Name || sku.spec3Name,
          spec3Value: sku.spec3Value,
          costPrice: sku.costPrice?.toString() || '',
          purchasePrice: sku.purchasePrice?.toString() || '',
          strikethroughPrice: sku.strikethroughPrice?.toString() || '',
          totalInventory: sku.totalInventory || 0,
          safetyStock: sku.safetyStock || 0,
          imgUrl: sku.imgUrl || ''
        }))
      }
    }
  } else if (props.selectedProducts.length > 1) {
    // 批量编辑时的处理逻辑
    currentProduct.value = {
      specType: 'batch'
    }
  }
}

// 应用批量价格设置
const applyBatchPrice = () => {
  if (!batchPrice.costPrice && !batchPrice.purchasePrice && !batchPrice.strikethroughPrice) {
    ElMessage.warning('请至少填写一个价格字段')
    return
  }
  
  skuList.value.forEach(sku => {
    if (batchPrice.costPrice) {
      sku.costPrice = batchPrice.costPrice
    }
    if (batchPrice.purchasePrice) {
      sku.purchasePrice = batchPrice.purchasePrice
    }
    if (batchPrice.strikethroughPrice) {
      sku.strikethroughPrice = batchPrice.strikethroughPrice
    }
  })
  
  ElMessage.success('批量价格设置已应用到所有SKU')
  
  // 清空批量设置表单
  batchPrice.costPrice = ''
  batchPrice.purchasePrice = ''
  batchPrice.strikethroughPrice = ''
}

// 添加新规格
const handleAddSpec = () => {
  // 验证规格值是否填写
  const hasSpec1 = currentProduct.value?.originalData?.spec1Name && newSpecForm.spec1Value.trim()
  const hasSpec2 = currentProduct.value?.originalData?.spec2Name && newSpecForm.spec2Value.trim()
  const hasSpec3 = currentProduct.value?.originalData?.spec3Name && newSpecForm.spec3Value.trim()
  
  if (!hasSpec1 && !hasSpec2 && !hasSpec3) {
    ElMessage.warning('请至少填写一个规格值')
    return
  }
  
  // 构建规格组合字符串用于检查重复
  const newSpecCombination = [
    hasSpec1 ? `${currentProduct.value.originalData.spec1Name}:${newSpecForm.spec1Value.trim()}` : '',
    hasSpec2 ? `${currentProduct.value.originalData.spec2Name}:${newSpecForm.spec2Value.trim()}` : '',
    hasSpec3 ? `${currentProduct.value.originalData.spec3Name}:${newSpecForm.spec3Value.trim()}` : ''
  ].filter(Boolean).join('|')
  
  // 检查是否已存在相同规格组合
  const existingCombination = skuList.value.some(sku => {
    const existingSpec = [
      sku.spec1Value ? `${sku.spec1Name}:${sku.spec1Value}` : '',
      sku.spec2Value ? `${sku.spec2Name}:${sku.spec2Value}` : '',
      sku.spec3Value ? `${sku.spec3Name}:${sku.spec3Value}` : ''
    ].filter(Boolean).join('|')
    return existingSpec === newSpecCombination
  })
  
  if (existingCombination) {
    ElMessage.warning('该规格组合已存在')
    return
  }
  
  // 添加新SKU（不生成SKU编码，由后端在审批通过时自动生成）
  const newSku = {
    id: null, // 新增SKU，暂时没有ID
    skuCode: null, // 新增SKU不设置编码，由后端审批通过时自动生成
    spec1Name: currentProduct.value.originalData.spec1Name || '',
    spec1Value: hasSpec1 ? newSpecForm.spec1Value.trim() : '',
    spec2Name: currentProduct.value.originalData.spec2Name || '',
    spec2Value: hasSpec2 ? newSpecForm.spec2Value.trim() : '',
    spec3Name: currentProduct.value.originalData.spec3Name || '',
    spec3Value: hasSpec3 ? newSpecForm.spec3Value.trim() : '',
    costPrice: '',
    purchasePrice: '',
    strikethroughPrice: '',
    totalInventory: 0,
    safetyStock: 0,
    imgUrl: '',
    isNew: true // 标记为新增SKU
  }
  
  skuList.value.push(newSku)
  
  // 清空新规格表单
  Object.assign(newSpecForm, {
    spec1Value: '',
    spec2Value: '',
    spec3Value: ''
  })
  
  ElMessage.success('新规格添加成功')
}

// 删除规格
const handleDeleteSku = (index) => {
  if (skuList.value.length <= 1) {
    ElMessage.warning('至少需要保留一个SKU')
    return
  }
  
  const sku = skuList.value[index]
  const specCombination = [
    sku.spec1Value ? `${sku.spec1Name}:${sku.spec1Value}` : '',
    sku.spec2Value ? `${sku.spec2Name}:${sku.spec2Value}` : '',
    sku.spec3Value ? `${sku.spec3Name}:${sku.spec3Value}` : ''
  ].filter(Boolean).join(' ')
  
  ElMessageBox.confirm(
    `确定要删除规格"${specCombination}"吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    skuList.value.splice(index, 1)
    ElMessage.success('规格删除成功')
  }).catch(() => {
    // 用户取消删除
  })
}

const handleClose = () => {
  visible.value = false
  resetForm()
}

const handleSubmitRequest = async () => {
  try {
    await editFormRef.value.validate()
    
    if (props.selectedProducts.length === 0) {
      ElMessage.warning('请选择要修改的商品')
      return
    }
    
    // 检查是否有价格变化
    if (!hasPriceChanges()) {
      ElMessage.warning('请至少修改一个价格字段后再提交')
      return
    }
    
    loading.value = true
    
    // 构建请求参数
    const requestData = {
      requestType: 'price', // 固定为价格修改
      requestReason: editForm.requestReason,
      spuIdList: props.selectedProducts.map(product => product.id),
      productData: []
    }
    
    // 处理商品数据
    if (props.selectedProducts.length === 1) {
      const product = props.selectedProducts[0]
      const productData = {
        spuId: product.id,
        specType: currentProduct.value.specType
      }
      
      if (currentProduct.value.specType === 'single') {
        // 单规格商品
        productData.singleSku = {
          costPrice: editForm.costPrice ? Number(editForm.costPrice) : undefined,
          purchasePrice: editForm.purchasePrice ? Number(editForm.purchasePrice) : undefined,
          strikethroughPrice: editForm.strikethroughPrice ? Number(editForm.strikethroughPrice) : undefined
        }
      } else {
        // 多规格商品
        productData.skuList = skuList.value.map(sku => ({
          id: sku.id,
          skuCode: sku.skuCode,
          spec1Name: sku.spec1Name,
          spec1Value: sku.spec1Value,
          spec2Name: sku.spec2Name,
          spec2Value: sku.spec2Value,
          spec3Name: sku.spec3Name,
          spec3Value: sku.spec3Value,
          costPrice: sku.costPrice ? Number(sku.costPrice) : undefined,
          purchasePrice: sku.purchasePrice ? Number(sku.purchasePrice) : undefined,
          strikethroughPrice: sku.strikethroughPrice ? Number(sku.strikethroughPrice) : undefined,
          isNew: sku.isNew || false, // 标记是否为新增SKU
          imgUrl: sku.imgUrl
        }))
        
        // 记录原始SKU列表用于对比
        const originalSkuIds = currentProduct.value.originalData.skuList.map(sku => sku.id)
        const currentSkuIds = skuList.value.filter(sku => sku.id).map(sku => sku.id)
        
        // 找出被删除的SKU
        const deletedSkuIds = originalSkuIds.filter(id => !currentSkuIds.includes(id))
        
        if (deletedSkuIds.length > 0) {
          productData.deletedSkuIds = deletedSkuIds
        }
      }
      
      requestData.productData.push(productData)
    } else {
      // 批量编辑的处理逻辑
      props.selectedProducts.forEach(product => {
        requestData.productData.push({
          spuId: product.id,
          specType: product.originalData?.skuList?.length > 1 ? 'multiple' : 'single'
          // 批量编辑的具体逻辑需要根据实际需求实现
        })
      })
    }
    
    // 发送确认事件给父组件
    emit('confirm', requestData)
    
    // 不在这里关闭弹框，由父组件根据提交结果决定是否关闭
    // visible.value = false
    // resetForm()
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(editForm, {
    requestReason: '',
    costPrice: '',
    purchasePrice: '',
    strikethroughPrice: '',
    totalInventory: '',
    safetyStock: ''
  })
  
  Object.assign(batchPrice, {
    costPrice: '',
    purchasePrice: '',
    strikethroughPrice: ''
  })
  
  Object.assign(newSpecForm, {
    spec1Value: '',
    spec2Value: '',
    spec3Value: ''
  })
  
  currentProduct.value = null
  skuList.value = []
  
  if (editFormRef.value) {
    editFormRef.value.clearValidate()
  }
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  if (status === 'draft') return 'info'
  if (status === 'rejected') return 'danger'
  if (status === 'reviewing') return 'warning'
  if (status === 'selling') return 'success'
  if (status === 'offline') return ''
  return 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  if (status === 'draft') return '草稿'
  if (status === 'rejected') return '审批拒绝'
  if (status === 'reviewing') return '审核中'
  if (status === 'selling') return '销售中'
  if (status === 'offline') return '已下架'
  return '未知状态'
}

// 检查是否有价格变化
const hasPriceChanges = () => {
  if (props.selectedProducts.length === 1) {
    const product = props.selectedProducts[0]
    const originalData = product.originalData
    
    if (currentProduct.value?.specType === 'single') {
      // 单规格商品价格变化检查
      const originalSku = originalData?.skuList?.[0]
      if (!originalSku) return false
      
      // 比较成本价
      const originalCostPrice = originalSku.costPrice || 0
      const newCostPrice = editForm.costPrice ? Number(editForm.costPrice) : 0
      const costPriceChanged = newCostPrice !== originalCostPrice
      
      // 比较采购价
      const originalPurchasePrice = originalSku.purchasePrice || 0
      const newPurchasePrice = editForm.purchasePrice ? Number(editForm.purchasePrice) : 0
      const purchasePriceChanged = newPurchasePrice !== originalPurchasePrice
      
      // 比较划线价
      const originalStrikethroughPrice = originalSku.strikethroughPrice || 0
      const newStrikethroughPrice = editForm.strikethroughPrice ? Number(editForm.strikethroughPrice) : 0
      const strikethroughPriceChanged = newStrikethroughPrice !== originalStrikethroughPrice
      
      return costPriceChanged || purchasePriceChanged || strikethroughPriceChanged
    } else if (currentProduct.value?.specType === 'multiple') {
      // 多规格商品价格变化检查
      const originalSkuList = originalData?.skuList || []
      
      // 检查现有SKU的价格是否有变化
      for (const currentSku of skuList.value) {
        if (currentSku.isNew) {
          // 新增的SKU算作有变化
          return true
        }
        
        const originalSku = originalSkuList.find(sku => sku.id === currentSku.id)
        if (!originalSku) continue
        
        // 比较成本价
        const originalCostPrice = originalSku.costPrice || 0
        const newCostPrice = currentSku.costPrice ? Number(currentSku.costPrice) : 0
        const costPriceChanged = newCostPrice !== originalCostPrice
        
        // 比较采购价
        const originalPurchasePrice = originalSku.purchasePrice || 0
        const newPurchasePrice = currentSku.purchasePrice ? Number(currentSku.purchasePrice) : 0
        const purchasePriceChanged = newPurchasePrice !== originalPurchasePrice
        
        // 比较划线价
        const originalStrikethroughPrice = originalSku.strikethroughPrice || 0
        const newStrikethroughPrice = currentSku.strikethroughPrice ? Number(currentSku.strikethroughPrice) : 0
        const strikethroughPriceChanged = newStrikethroughPrice !== originalStrikethroughPrice
        
        if (costPriceChanged || purchasePriceChanged || strikethroughPriceChanged) {
          return true
        }
      }
      
      // 检查是否有SKU被删除
      const originalSkuIds = originalSkuList.map(sku => sku.id)
      const currentSkuIds = skuList.value.filter(sku => sku.id).map(sku => sku.id)
      const hasDeletedSkus = originalSkuIds.some(id => !currentSkuIds.includes(id))
      
      return hasDeletedSkus
    }
  } else if (props.selectedProducts.length > 1) {
    // 批量编辑的价格变化检查
    // TODO: 根据实际的批量编辑逻辑实现
    return true // 暂时返回true，允许批量编辑提交
  }
  
  return false
}
</script>

<style scoped lang="scss">
.price-edit-dialog {
  .dialog-content {
    max-height: 70vh;
    overflow-y: auto;
    
    .product-info-section {
      margin-bottom: 24px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;
      
      .product-header {
      display: flex;
        gap: 16px;
        align-items: flex-start;

        .product-details {
          flex: 1;
          
          .product-name {
            margin: 0 0 8px 0;
            font-size: 16px;
            font-weight: 600;
            color: #333;
          }
          
          .product-meta {
            display: flex;
            gap: 12px;
            align-items: center;
            
            .product-category {
        color: #666;
              font-size: 14px;
            }
          }
        }
      }
    }
    
    .batch-info-section {
      margin-bottom: 24px;
      }

    .edit-form {
      .section-title {
        margin: 24px 0 16px 0;
        padding-bottom: 8px;
        border-bottom: 2px solid #409eff;
        
        h4 {
          margin: 0 0 4px 0;
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }
        
        p {
          margin: 0;
          font-size: 13px;
          color: #666;
        }
      }
      
      .spec-tip {
        margin-left: 8px;
        font-size: 12px;
        color: #999;
        font-style: italic;
      }
      
      .single-spec-form {
        .subsection-title {
          margin: 16px 0 12px 0;
          font-size: 14px;
          font-weight: 600;
          color: #666;
          padding-bottom: 8px;
          border-bottom: 1px solid #e4e7ed;
        }
      }
      
      .multiple-spec-form {
        .subsection-title {
          margin: 16px 0 12px 0;
          font-size: 14px;
          font-weight: 600;
          color: #666;
          padding-bottom: 8px;
          border-bottom: 1px solid #e4e7ed;
        }
        
        .spec-info {
          margin-bottom: 16px;
          
          p {
            margin: 0;
            color: #666;
            font-size: 14px;
          }
        }
        
        .sku-table {
          margin-bottom: 20px;
          
          .spec-combination {
            .spec-item {
              display: block;
              font-size: 12px;
              color: #666;
              margin-bottom: 2px;
              padding: 2px 6px;
              background: #f5f7fa;
              border-radius: 3px;
              
              &:last-child {
                margin-bottom: 0;
              }
        }
      }

          .no-image {
            font-size: 12px;
            color: #999;
          }
          
          .readonly-info {
            color: #999;
            font-size: 13px;
      }
    }

        .add-spec-section {
          margin-top: 20px;
          padding: 16px;
          background: #f0f9ff;
          border: 1px solid #e1f5fe;
          border-radius: 6px;
          
          h6 {
            margin: 0 0 12px 0;
            font-size: 14px;
            font-weight: 600;
            color: #333;
          }
        }
        
        .batch-price-setting {
          padding: 16px;
          background: #f8f9fa;
          border-radius: 6px;
          
          h6 {
            margin: 0 0 12px 0;
            font-size: 14px;
            font-weight: 600;
            color: #333;
          }
        }
      }
      
      .price-help-icon {
        margin-left: 4px;
        color: #909399;
        cursor: help;
        font-size: 14px;
        
        &:hover {
          color: #409eff;
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 0 0 0;
    border-top: 1px solid #e4e7ed;
  }
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 0 20px;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}

:deep(.el-input--small .el-input__inner) {
  height: 32px;
  line-height: 32px;
}
</style> 