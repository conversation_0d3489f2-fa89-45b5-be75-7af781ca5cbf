# Product 产品管理组件

这个目录包含了所有与产品管理相关的 Vue 组件。

## 组件列表

### ProductSearchBar
- **路径**: `./ProductSearchBar/index.vue`
- **功能**: 产品搜索栏，包含搜索表单和操作按钮
- **特性**: 支持搜索、重置、批量操作功能

### ProductStatusTabs
- **路径**: `./ProductStatusTabs/index.vue`
- **功能**: 产品状态标签页
- **特性**: 显示不同状态的产品数量，支持状态切换

### ProductTable
- **路径**: `./ProductTable/index.vue`
- **功能**: 产品列表表格和分页
- **特性**: 支持选择、编辑、删除操作，图片预览功能

### CarouselImageManager
- **路径**: `./CarouselImageManager/index.vue`
- **功能**: 轮播图管理
- **特性**: 支持图片上传、预览、设置主图、删除

### ProductSpecConfig
- **路径**: `./ProductSpecConfig/index.vue`
- **功能**: 多规格配置管理
- **特性**: 动态添加规格项和规格值，自动生成SKU列表

### ProductDetailEditor
- **路径**: `./ProductDetailEditor/index.vue`
- **功能**: 富文本编辑器和移动端预览
- **特性**: 支持图片插入功能

### SpuContentEditor
- **路径**: `./SpuContentEditor/index.vue`
- **功能**: SPU内容编辑器，基于Quill的富文本编辑器
- **特性**: 支持富文本编辑、图片插入、移动端预览、工具栏自定义

## 使用方式

### 单独导入
```javascript
import ProductSearchBar from '@/Product/ProductSearchBar/index.vue'
import ProductTable from '@/Product/ProductTable/index.vue'
```

### 统一导入（推荐）
```javascript
import {
  ProductSearchBar,
  ProductStatusTabs,
  ProductTable,
  CarouselImageManager,
  ProductSpecConfig,
  ProductDetailEditor,
  SpuContentEditor
} from '@/Product'
```

## 组件依赖

所有组件都基于以下技术栈：
- Vue 3 Composition API
- Element Plus 组件库
- SCSS 样式预处理器

## 注意事项

1. 所有组件都支持 v-model 双向绑定
2. 组件间通过 emit 事件进行通信
3. 使用了 Element Plus 的表单验证和消息提示
4. 图片预览功能已优化 z-index 层级问题 