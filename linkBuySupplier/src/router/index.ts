import { createRouter, createWebHashHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { getToken, setToken } from '@/utils/auth'
import { useUser } from '@/store/user'
import { getSupplierInfo } from '@/api/common'
import { superLogin } from '@/api/auth'
import { getCurrentScode, hasValidScode, buildRouteWithScode } from '@/utils/url'
import { constantRoutes } from './routes-data'


const router = createRouter({
  routes: constantRoutes,
  history: createWebHashHistory()
})

// 导航守卫
router.beforeEach(async (to) => {
  console.log('路由守卫 - 目标路径:', to.path)
  
  // 优先处理 /not-found 路径，无需任何检查
  if (to.path === '/not-found') {
    console.log('允许访问 /not-found')
    return true
  }
  
  const userStore = useUser()
  
  // 处理 scode 和超级登录逻辑
  const scode = getCurrentScode()
  const superLoginCode = to.query.loginCode as string
  
  // 如果没有 scode，跳转到 not-found
  if (!scode || !hasValidScode()) {
    console.log('没有有效的scode，跳转到not-found')
    return '/not-found'
  }
  
  console.log('当前scode:', scode)
  
  // 根据当前scode获取对应的token和用户信息
  let token = getToken(scode)
  let userInfo = userStore.getUserInfo(scode)

  // 确保有供应商信息（无论是否超级登录）
  if (!userStore.getSupplierName()) {
    try {
      console.log('获取供应商信息')
      const { data: supplierData, code: supplierCode } = await getSupplierInfo(scode)
      if (supplierCode === 200 && supplierData && supplierData.name) {
        userStore.setSupplierName(supplierData.name, scode)
        console.log('获取供应商信息成功:', supplierData.name)
      } else {
        console.log('供应商信息为空，跳转到not-found')
        return '/not-found'
      }
    } catch (error) {
      console.error('获取供应商信息失败:', error)
      return '/not-found'
    }
  }

  // 处理超级登录
  if (superLoginCode) {
    try {
      console.log('开始超级登录流程')
      
      // 执行超级登录
      const { data, code } = await superLogin({ loginCode: superLoginCode, scode: scode })
      if (code === 200 && data && data.token) {
        // 保存用户信息到 store（按scode存储）
        userStore.setUserInfo(data, scode)
        // 保存 token 到本地存储（按scode存储）
        setToken(data.token, scode)
        token = data.token
        userInfo = data
        
        // 清除URL中的超级登录参数，重定向到目标路径，保留scode
        const newQuery = { ...to.query }
        delete newQuery.loginCode
        // 确保scode参数保留
        if (scode) {
          newQuery.scode = scode
        }
        return { path: to.path, query: newQuery, replace: true }
      } else {
        console.error('超级登录失败')
        return buildRouteWithScode('/login', scode)
      }
    } catch (error) {
      console.error('超级登录过程中发生错误:', error)
      return buildRouteWithScode('/login', scode)
    }
  }

  if (scode && userStore.getIsFirst()) {
    const { data: supplierData, code: supplierCode } = await getSupplierInfo(scode)
    if (supplierCode === 200 && supplierData && supplierData.name) {
      userStore.setSupplierName(supplierData.name, scode)
    }
    userStore.setIsFirst(false)
  }

  // 允许访问的公开路径
  const publicPaths = ['/login', '/register', '/resetPassword']
  if (publicPaths.includes(to.path)) {
    return true
  }

  // 没有token时跳转到登录页，保留scode参数
  if (!token) {
    console.log('没有token，跳转到登录页')
    return buildRouteWithScode('/login', scode)
  }

  // 没有用户信息时重新获取
  if (!userInfo) {
    await userStore.refreshUserInfoAction(scode)
    userInfo = userStore.getUserInfo(scode)
  }

  if (!userInfo) {
    console.log('没有用户信息，跳转到登录页')
    return buildRouteWithScode('/login', scode)
  }

  return true
})

// 添加路由导航守卫，确保所有路由跳转都包含scode参数
router.beforeResolve((to, from) => {
  // 如果目标路由没有scode参数，但当前URL有scode，则自动添加
  const currentScode = getCurrentScode()
  
  if (currentScode && !to.query.scode) {
    console.log('自动为路由添加scode参数:', currentScode)
    return {
      ...to,
      query: {
        ...to.query,
        scode: currentScode
      }
    }
  }
  
  return true
})

// 路由跳转完成后的处理（如果需要可以在这里添加其他逻辑）
// router.afterEach((to) => {
//   // 可以在这里添加路由跳转完成后的处理逻辑
// });

export default router
