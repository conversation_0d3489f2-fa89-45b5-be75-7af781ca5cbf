<template>
  <div class="freight-area-config">
    <div class="area-config">
      <!-- 表头 -->
      <div class="area-header" :class="{ 'freight-only': valuationType === 4 }">
        <div class="header-cell area-name-header">配送区域</div>
        <div class="header-cell" v-if="valuationType === 1">首件 (件)</div>
        <div class="header-cell" v-else-if="valuationType === 2">首重 (Kg)</div>
        <div class="header-cell" v-else-if="valuationType === 3">首体积 (m³)</div>
        <div class="header-cell" v-else-if="valuationType === 4">包邮金额 (元)</div>

        <div class="header-cell" v-if="valuationType !== 4">运费 (元)</div>

        <div class="header-cell" v-if="valuationType === 1">续件 (件)</div>
        <div class="header-cell" v-else-if="valuationType === 2">续重 (Kg)</div>
        <div class="header-cell" v-else-if="valuationType === 3">续体积 (m³)</div>
        <div class="header-cell" v-else-if="valuationType === 4">未包邮运费 (元)</div>

        <div class="header-cell" v-if="valuationType !== 4">续费 (元)</div>
        <div class="header-cell actions-header">操作</div>
      </div>

      <!-- 区域行 -->
      <div 
        v-for="(area, index) in areas" 
        :key="index" 
        class="area-row"
        :class="{ 'freight-only': valuationType === 4 }"
      >
        <div class="area-name" :class="{ 'default-area': area.isDefault }">
          <el-icon class="area-icon">
            <Location />
          </el-icon>
          <span class="area-text">{{ area.areaName }}</span>
          <el-tag v-if="area.isDefault" size="small" type="info" class="default-tag">默认</el-tag>
        </div>

        <!-- 件数计费 -->
        <template v-if="valuationType === 1">
          <div class="area-input">
            <el-input-number 
              v-model="area.firstNum" 
              :min="1" 
              placeholder="首件" 
              size="default"
              controls-position="right" 
              @change="handleAreaChange"
            />
          </div>
          <div class="area-input">
            <el-input-number 
              v-model="area.firstFreight" 
              :min="0" 
              :precision="2" 
              placeholder="运费"
              size="default" 
              controls-position="right" 
              @change="handleAreaChange"
            />
          </div>
          <div class="area-input">
            <el-input-number 
              v-model="area.additionalNum" 
              :min="1" 
              placeholder="续件" 
              size="default"
              controls-position="right" 
              @change="handleAreaChange"
            />
          </div>
          <div class="area-input">
            <el-input-number 
              v-model="area.additionalFreight" 
              :min="0" 
              :precision="2" 
              placeholder="续费"
              size="default" 
              controls-position="right" 
              @change="handleAreaChange"
            />
          </div>
        </template>

        <!-- 重量计费 -->
        <template v-else-if="valuationType === 2">
          <div class="area-input">
            <el-input-number 
              v-model="area.firstWeight" 
              :min="0.1" 
              :precision="1" 
              placeholder="首重"
              size="default" 
              controls-position="right" 
              @change="handleAreaChange"
            />
          </div>
          <div class="area-input">
            <el-input-number 
              v-model="area.firstFreight" 
              :min="0" 
              :precision="2" 
              placeholder="运费"
              size="default" 
              controls-position="right" 
              @change="handleAreaChange"
            />
          </div>
          <div class="area-input">
            <el-input-number 
              v-model="area.additionalWeight" 
              :min="0.1" 
              :precision="1" 
              placeholder="续重"
              size="default" 
              controls-position="right" 
              @change="handleAreaChange"
            />
          </div>
          <div class="area-input">
            <el-input-number 
              v-model="area.additionalFreight" 
              :min="0" 
              :precision="2" 
              placeholder="续费"
              size="default" 
              controls-position="right" 
              @change="handleAreaChange"
            />
          </div>
        </template>

        <!-- 体积计费 -->
        <template v-else-if="valuationType === 3">
          <div class="area-input">
            <el-input-number 
              v-model="area.firstVolume" 
              :min="0.01" 
              :precision="2" 
              placeholder="首体积"
              size="default" 
              controls-position="right" 
              @change="handleAreaChange"
            />
          </div>
          <div class="area-input">
            <el-input-number 
              v-model="area.firstFreight" 
              :min="0" 
              :precision="2" 
              placeholder="运费"
              size="default" 
              controls-position="right" 
              @change="handleAreaChange"
            />
          </div>
          <div class="area-input">
            <el-input-number 
              v-model="area.additionalVolume" 
              :min="0.01" 
              :precision="2" 
              placeholder="续体积"
              size="default" 
              controls-position="right" 
              @change="handleAreaChange"
            />
          </div>
          <div class="area-input">
            <el-input-number 
              v-model="area.additionalFreight" 
              :min="0" 
              :precision="2" 
              placeholder="续费"
              size="default" 
              controls-position="right" 
              @change="handleAreaChange"
            />
          </div>
        </template>

        <!-- 满额包邮 -->
        <template v-else-if="valuationType === 4">
          <div class="area-input">
            <el-input-number 
              v-model="area.freeShippingAmount" 
              :min="0" 
              :precision="2" 
              placeholder="包邮金额"
              size="default" 
              controls-position="right" 
              @change="handleAreaChange"
            />
          </div>
          <div class="area-input">
            <el-input-number 
              v-model="area.unfreeShippingFreight" 
              :min="0" 
              :precision="2" 
              placeholder="运费"
              size="default" 
              controls-position="right" 
              @change="handleAreaChange"
            />
          </div>
        </template>

        <div class="area-actions">
                          <el-button link size="small" style="color: var(--el-color-primary)" @click="handleEditArea(area, index)">
            <el-icon>
              <Edit />
            </el-icon>
            编辑
          </el-button>
          <el-button 
            v-if="!area.isDefault" 
            link 
            type="danger" 
            size="small" 
            @click="handleRemoveArea(index)"
          >
            <el-icon>
              <Delete />
            </el-icon>
            删除
          </el-button>
        </div>
      </div>
    </div>

    <!-- 添加区域按钮 -->
    <div class="add-area-section">
      <el-button type="primary" @click="handleAddArea" class="add-area-btn" size="large">
        <el-icon>
          <Plus />
        </el-icon>
        指定配送区域和运费
      </el-button>
    </div>

    <!-- 计费说明 -->
    <div class="area-tip">
      <el-icon class="tip-icon">
        <InfoFilled />
      </el-icon>
      <div class="tip-content">
        <div class="tip-title">计费说明：</div>
        <div class="tip-text">
          <span v-if="valuationType === 1">
            根据商品件数计算运费。当购买数量不足首件数量时，按首件费用计算；超过部分按续件数量和续件费用计算。
          </span>
          <span v-else-if="valuationType === 2">
            根据商品重量计算运费。当购买重量不足首重时，按首重费用计算；超过部分按续重和续重费用计算。
          </span>
          <span v-else-if="valuationType === 3">
            根据商品体积计算运费。当购买体积不足首体积时，按首体积费用计算；超过部分按续体积和续体积费用计算。
          </span>
          <span v-else-if="valuationType === 4">
            当订单金额达到包邮金额时免运费，否则按照未包邮运费收取。
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Location, Edit, Delete, Plus, InfoFilled } from '@element-plus/icons-vue'

interface AreaItem {
  id?: number
  areaName: string
  areaCode: string
  isDefault?: boolean
  // 件数计费
  firstNum?: number
  additionalNum?: number
  // 重量计费
  firstWeight?: number
  additionalWeight?: number
  // 体积计费
  firstVolume?: number
  additionalVolume?: number
  // 通用
  firstFreight?: number
  additionalFreight?: number
  // 满额包邮
  freeShippingAmount?: number
  unfreeShippingFreight?: number
}

interface Props {
  areas: AreaItem[]
  valuationType: number
}

interface Emits {
  (e: 'update:areas', areas: AreaItem[]): void
  (e: 'edit-area', area: AreaItem, index: number): void
  (e: 'remove-area', index: number): void
  (e: 'add-area'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const handleAreaChange = () => {
  emit('update:areas', props.areas)
}

const handleEditArea = (area: AreaItem, index: number) => {
  emit('edit-area', area, index)
}

const handleRemoveArea = (index: number) => {
  emit('remove-area', index)
}

const handleAddArea = () => {
  emit('add-area')
}
</script>

<style scoped lang="scss">
.freight-area-config {
  width: 100%;
}

.area-config {
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  padding: 20px;
  background-color: #fafbfc;
  margin-bottom: 20px;

  .area-header {
    display: grid;
    grid-template-columns: 200px repeat(4, 140px) 120px;
    gap: 16px;
    border-bottom: 2px solid #e4e7ed;
    font-weight: 600;
    font-size: 14px;
    color: #606266;

    &.freight-only {
      grid-template-columns: 200px 140px 140px 120px;
    }

    .header-cell {
      text-align: center;
      padding: 0 8px;
    }

    .area-name-header {
      text-align: left;
    }

    .actions-header {
      text-align: center;
    }
  }

  .area-row {
    display: grid;
    grid-template-columns: 200px repeat(4, 140px) 120px;
    gap: 16px;
    padding: 16px 0;
    align-items: center;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    &.freight-only {
      grid-template-columns: 200px 140px 140px 120px;
    }

    .area-name {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      background-color: #fff;
      border-radius: 8px;
      border: 1px solid #e9ecef;
      min-height: 44px;

      .area-icon {
        margin-right: 8px;
        color: #409eff;
        font-size: 16px;
      }

      .area-text {
        font-size: 14px;
        font-weight: 500;
        color: #303133;
        word-break: break-all;
        line-height: 1.4;
      }

      .default-tag {
        margin-left: 8px;
      }
    }

    .default-area {
      background-color: #f0f9ff;
      border-color: #b3d8ff;
    }

    .area-input {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 44px;

      :deep(.el-input-number) {
        width: 100%;
      }
    }

    .area-actions {
      display: flex;
      gap: 8px;
      justify-content: center;

      .el-button {
        padding: 4px 8px;

        .el-icon {
          margin-right: 4px;
        }
      }
    }
  }
}

.add-area-section {
  text-align: center;
  margin-bottom: 20px;
}

.add-area-btn {
  width: 100%;
  height: 48px;
  border-style: dashed;
  border-width: 2px;
  border-color: #dcdfe6;
  background-color: transparent;
  color: #606266;
  font-size: 14px;
  font-weight: 400;

  &:hover {
    border-color: #409eff;
    color: #409eff;
    background-color: #f0f9ff;
  }

  .el-icon {
    margin-right: 8px;
  }
}

.area-tip {
  background: linear-gradient(135deg, #f0f9ff 0%, #e8f4fd 100%);
  border: 1px solid #b3d8ff;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: flex-start;

  .tip-icon {
    margin-right: 12px;
    margin-top: 2px;
    color: #409eff;
    font-size: 18px;
    flex-shrink: 0;
  }

  .tip-content {
    flex: 1;
  }

  .tip-title {
    font-weight: 600;
    font-size: 14px;
    color: #303133;
    margin-bottom: 8px;
  }

  .tip-text {
    font-size: 13px;
    color: #606266;
    line-height: 1.6;
  }
}
</style> 