<template>
  <div class="page-card-panel">
    <el-card class="box-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="title">{{ title }}</span>
          <slot name="header-operation"></slot>
        </div>
      </template>
      <slot></slot>
    </el-card>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
export default defineComponent({
  name: 'page-card',
  props: {
    title: {
      type: String
    }
  },
  components: {},
  setup() {
    return {}
  }
})
</script>

<style lang="scss" scoped>
.box-card {
  border: none;
  border-radius: 2px;
  :deep(.el-card__header) {
    padding: 14px 20px;
    border: none;
    box-shadow: 0px 1px 6px 0px rgba(0,0,0,0.06);
  }
  :deep(.el-card__body) {
    padding: 20px;
  }
}
.card-header {
  .title {
    font-weight: bold;
  }
}
</style>
