<template>
  <div class="all login-bg">
    <div class="login-band">
      <div class="login-text">
        <p>数字化供应商平台</p>
        <span class="m-t-43">化繁为简 · 高效工作</span>
      </div>
      <div class="login-container">
        <div class="login-box">
          <div>
            <h2 class="login-title">欢迎登录系统</h2>
          </div>
          <form class="login-form" @submit.prevent="handleLogin">
            <div class="form-group">
              <div>
                <div class="input-wrapper">
                  <div class="input-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M17 2H7C5.89 2 5 2.9 5 4V20C5 21.1 5.89 22 7 22H17C18.1 22 19 21.1 19 20V4C19 2.9 18.1 2 17 2ZM17 18H7V6H17V18ZM9 7H15V8H9V7ZM9 9H15V10H9V9ZM9 11H13V12H9V11Z" fill="#999"/>
                    </svg>
                  </div>
                  <input id="phone" v-model="phone" type="tel" class="input-field" :class="{ 'error': phoneError }"
                    placeholder="请输入管理员手机号" @blur="validatePhone" />
                </div>
                <div v-if="phoneError" :class="['error-message', 'not-registered']">
                  <span v-html="keyForErrorMsg[phoneError]"></span>
                </div>
              </div>
              <div>
                <div class="password-header">
                  <label for="password" class="input-label"></label>
                  <router-link class="forgot-password" to="/resetPassword">忘记密码？</router-link>
                </div>
                <div class="input-wrapper">
                  <div class="input-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M18 8H17V6C17 3.24 14.76 1 12 1S7 3.24 7 6V8H6C4.9 8 4 8.9 4 10V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V10C20 8.9 19.1 8 18 8ZM12 17C10.9 17 10 16.1 10 15S10.9 13 12 13S14 13.9 14 15S13.1 17 12 17ZM15.1 8H8.9V6C8.9 4.29 10.29 2.9 12 2.9S15.1 4.29 15.1 6V8Z" fill="#999"/>
                    </svg>
                  </div>
                  <input id="password" v-model="password" :type="showPassword ? 'text' : 'password'" class="input-field"
                    :class="{ 'error': passwordError }" placeholder="请输入密码" @blur="validatePassword" />
                  <div class="password-toggle" @click="showPassword = !showPassword">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path v-if="!showPassword" d="M12 4.5C7 4.5 2.73 7.61 1 12C2.73 16.39 7 19.5 12 19.5S21.27 16.39 23 12C21.27 7.61 17 4.5 12 4.5ZM12 17C9.24 17 7 14.76 7 12S9.24 7 12 7S17 9.24 17 12S14.76 17 12 17ZM12 9C10.34 9 9 10.34 9 12S10.34 15 12 15S15 13.66 15 12S13.66 9 12 9Z" fill="#999"/>
                      <path v-else d="M12 7C9.24 7 7 9.24 7 12C7 12.65 7.13 13.26 7.36 13.82L9.13 12.05C9.13 12.03 9.12 12.02 9.12 12C9.12 10.34 10.46 9 12.12 9C12.14 9 12.15 9.01 12.17 9.01L13.94 7.24C13.32 7.09 12.67 7 12 7ZM2.01 3.87L4.69 6.55C3.06 7.83 1.77 9.53 1 12C2.73 16.39 7 19.5 12 19.5C13.52 19.5 14.97 19.13 16.31 18.45L18.42 20.56L19.73 19.25L3.42 2.94L2.01 3.87ZM7.53 9.8L9.08 11.35C9.03 11.56 9 11.78 9 12C9 13.66 10.34 15 12 15C12.22 15 12.44 14.97 12.65 14.92L14.2 16.47C13.53 16.8 12.79 17 12 17C9.24 17 7 14.76 7 12C7 11.21 7.2 10.47 7.53 9.8ZM11.84 9L13 10.16C12.67 10.06 12.34 10 12 10C10.34 10 9 11.34 9 13C9 13.34 9.06 13.67 9.16 14L10.84 12.32C10.84 12.21 10.84 12.11 10.84 12C10.84 10.34 12.18 9 13.84 9C13.95 9 14.05 9 14.16 9L11.84 9Z" fill="#999"/>
                    </svg>
                  </div>
                </div>
                <div v-if="passwordError" class="error-message invalid-password">
                  <span v-html="keyForErrorMsg[passwordError]"></span>
                </div>
              </div>
              <div class="remember-me">
                <el-checkbox v-model="rememberMe" size="large" />&nbsp;
                <span>记住账号</span>
              </div>
            </div>

            <button type="submit" class="login-button">
              登录
            </button>
          </form>
          
          <!-- 供应商名称 -->
          <div class="supplier-name">
            {{ supplierName }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import LoginHeader from './components/header.vue'
import { login as loginApi } from '@/api/auth.ts'
import { ElMessage } from 'element-plus'
import { setToken } from '@/utils/auth'
import { useUser } from '@/store/user'
import { useMenuStore } from '@/store/menu'
import { getCurrentScode } from '@/utils/url'
// 定义错误消息类型
// type ErrorMessageKey = 'phoneRequired' | 'invalidPhone' | 'phoneNotRegistered' | 'invalidPassword' | 'pwdRequired' | 'pwdLength'

interface LoginAttempt {
  count: number
  timestamp: number
  lockUntil?: number
}

export default defineComponent({
  name: 'login',
  components: { LoginHeader },
  setup() {
    const router = useRouter()
    const userStore = useUser()
    const mobile = ref('')
    const phoneError = ref('')
    const password = ref('')
    const passwordError = ref('')
    const rememberMe = ref(false)
    const showPassword = ref(false)
    const supplierName = userStore.getSupplierName()

    // 从 localStorage 获取记住的登录信息
    onMounted(() => {
      const savedLogin = localStorage.getItem('rememberedLogin')
      if (savedLogin) {
        const { savedPhone, savedPassword } = JSON.parse(savedLogin)
        mobile.value = savedPhone
        password.value = savedPassword
        rememberMe.value = true
      }
    })

    // 记录登录尝试
    const loginAttempts: Record<string, LoginAttempt> = {}
    const MAX_ATTEMPTS = 10
    const LOCK_DURATION = 5 * 60 * 1000 // 5分钟
    const ATTEMPT_WINDOW = 5 * 60 * 1000 // 5分钟内的尝试次数

    const checkLoginAttempts = (userPhone: string): boolean => {
      const now = Date.now()
      const attempt = loginAttempts[userPhone]

      if (!attempt) {
        loginAttempts[userPhone] = { count: 0, timestamp: now }
        return true
      }

      if (attempt.lockUntil && now < attempt.lockUntil) {
        const remainingMinutes = Math.ceil((attempt.lockUntil - now) / 60000)
        passwordError.value = 'pwdRequired'
        ElMessage.error(`操作过于频繁，请${remainingMinutes}分钟后再试`)
        return false
      }

      if (now - attempt.timestamp > ATTEMPT_WINDOW) {
        loginAttempts[userPhone] = { count: 0, timestamp: now }
        return true
      }

      attempt.count++

      if (attempt.count >= MAX_ATTEMPTS) {
        attempt.lockUntil = now + LOCK_DURATION
        passwordError.value = 'pwdRequired'
        ElMessage.error('操作过于频繁，请5分钟后再试')
        return false
      }

      return true
    }

    const validatePhoneFormat = (phone: string): boolean => {
      const phoneRegex = /^1[3-9]\d{9}$/
      return phoneRegex.test(phone)
    }

    // 定义错误信息对应的key
    const keyForErrorMsg: Record<string, string> = {
      phoneRequired: '手机号不能为空',
      invalidPhone: '手机号格式不正确',
      phoneNotRegistered: '手机号未注册',
      invalidPassword: '密码格式不正确',
      pwdRequired: '密码不能为空',
      pwdLength: '密码长度必须在8-20位之间',
    }

    const validatePhone = async () => {
      phoneError.value = ''

      if (!mobile.value) {
        phoneError.value = 'phoneRequired'
        return
      }

      if (!validatePhoneFormat(mobile.value)) {
        phoneError.value = 'invalidPhone'
        return
      }
    }

    const validatePassword = () => {
      passwordError.value = ''

      if (!password.value) {
        passwordError.value = 'pwdRequired'
        return
      }

      // 检查密码长度（8-20位）
      if (password.value.length < 6 || password.value.length > 20) {
        passwordError.value = 'pwdLength'
        return
      }
    }

    const handleLogin = async () => {
      validatePassword()
      validatePhone()

      if (passwordError.value || phoneError.value) {
        return
      }

      if (!checkLoginAttempts(mobile.value)) {
        return
      }

      const res = await loginApi({
        mobile: mobile.value,
        password: password.value,
      })

      if (res.code === 200) {
        const scode = getCurrentScode()
        // 为当前scode设置token
        setToken(res.data.token, scode || undefined)

        if (rememberMe.value) {
          localStorage.setItem('rememberedLogin', JSON.stringify({
            savedPhone: mobile.value,
            savedPassword: password.value
          }))
        } else {
          localStorage.removeItem('rememberedLogin')
        }

        userStore.setUserInfo(res.data, scode || undefined)
        // 加载菜单
        const menuStore = useMenuStore()
        await menuStore.fetchMenu()

        // 登录成功后跳转到首页，保留scode参数
        if (scode) {
          router.push({ path: '/', query: { scode } })
        } else {
          router.push('/')
        }
      }
    }

    return {
      supplierName,
      router,
      phone: mobile,
      password,
      rememberMe,
      showPassword,
      handleLogin,
      phoneError,
      passwordError,
      keyForErrorMsg,
      validatePhone,
      validatePassword,
    }
  }
})
</script>

<style lang="scss" scoped>
.all {
  min-height: 100vh;
  width: 100%;
  background-image: linear-gradient(180deg, var(--el-color-white) 0%, var(--el-bg-color-base) 100%);
}

.login-bg {
  min-height: 100vh;
  width: 100vw;
  background: url('@/assets/images/login_bg.png') no-repeat center center;
  background-size: cover;
  position: relative;
}

.login-band {
  width: 100%;
  max-width: 1602px;
  height: 470px;
  background: rgba(46, 91, 238, .75);
  border-radius: 0 0 0 0;
  margin: 0 auto;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 2;
}

.login-text {
  color: #fff;
  font-size: 36px;
  font-weight: bold;
  margin-left: 160px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 340px;
  margin-bottom: 200px;
}
.login-text .m-t-43 {
  font-size: 20px;
  font-weight: 400;
  margin-top: 43px;
  letter-spacing: 2px;
}

.login-container {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 700px;
  height: 100%;
  position: relative;
  z-index: 3;
}

.login-box {
  background: var(--el-color-white);
  border: 1px solid var(--el-color-white);
  box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.1);
  width: 480px;
  height: 529px;
  margin-bottom: 60px;
  margin-right: 160px;
}

h2 {
  font-family: Helvetica-Bold;
  font-size: 24px;
  color: var(--el-text-color-primary);
  letter-spacing: 0;
  font-weight: 700;
  text-align: center;
}

.login-form {
  margin-top: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.input-label {
  display: block;
  font-size: 16px;
  color: var(--el-text-color-primary);
  letter-spacing: 0;
  font-weight: 400;
  margin-left: 40px;
  font-family: Helvetica;
}

.input-wrapper {
  position: relative;
  margin-left: 40px;
  margin-top: 10px;
}

.input-icon {
  position: absolute;
  top: 50%;
  left: 12px;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  z-index: 1;
}

.input-field {
  width: 400px;
  height: 44px;
  padding: 0.5rem 44px 0.5rem 44px; /* 左右都留出空间给图标 */
  border-radius: 6px;
  background: var(--el-color-white);
  border: 1px solid var(--el-border-color-base);

  &:focus {
    outline: none;
    border-color: var(--el-color-primary);
    background: rgba(91, 143, 249, 0.06);
    border: 1px solid var(--el-color-primary);
    box-shadow: 0 0 0 2px rgba(91, 143, 249, 0.1);
  }
  
  /* 手机号输入框不需要右侧padding */
  &#phone {
    padding-right: 0.75rem;
  }
}

.password-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.forgot-password {
  font-family: Helvetica;
  font-size: 14px;
  color: var(--el-color-primary);
  letter-spacing: 0;
  text-align: right;
  font-weight: 400;
  margin-right: 40px;
  text-decoration: none;
}

.forgot-password:hover {
  color: var(--el-color-primary-light-1);
}

.remember-me {
  display: flex;
  align-items: center;
  margin-bottom: 0 !important;
  padding-left: 40px;

  .checkbox {
    height: 18px;
    width: 18px;
    background: var(--el-color-white);
    border: 1px solid var(--el-border-color-base);
    border-radius: 4px;
    margin-left: 40px;
  }

  .checkbox-label {
    margin-left: 10px;
    font-family: Helvetica;
    font-size: 16px;
    color: var(--el-text-color-primary);
    letter-spacing: 0;
    line-height: 18px;
    font-weight: 400;
  }

  :deep(.el-checkbox__inner) {
    width: 18px !important;
    height: 18px !important;
  }

  :deep(.el-checkbox__inner:after) {
    left: 6px !important;
    top: 2px !important;
    height: 8px !important;
  }
}

.login-button {
  width: 400px;
  height: 44px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: var(--el-color-primary);
  color: var(--el-color-white);
  font-size: 16px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  box-shadow: inset 0px -1px 2px 0px rgba(0, 73, 64, 1);
  box-shadow: inset 0px 1px 1px 0px rgba(28, 141, 127, 1);
  border-radius: 6px;
  margin-left: 40px;

  &:hover {
    background-color: var(--el-color-primary-light-1);
  }

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

.signup-prompt {
  text-align: center;
  font-family: Helvetica;
  font-size: 14px;
  color: var(--el-text-color-primary);
  letter-spacing: 0;
  line-height: 18px;
  font-weight: 400;
}

.divider {
  position: relative;
  margin-top: -10px;
}

.divider-line {
  position: absolute;
  top: 50%;
  width: 400px;
  border: 1px solid var(--el-border-color-base);
  margin: 0 40px;
}

.divider-text {
  position: relative;
  display: flex;
  justify-content: center;
  font-size: 0.875rem;
}

.divider-text span {
  padding: 0 31px;
  background-color: var(--el-color-white);
  font-family: Helvetica;
  font-size: 16px;
  color: var(--el-text-color-primary);
  letter-spacing: 0;
  line-height: 18px;
  font-weight: 400;
}

.input-field.error {
  border-color: var(--el-color-danger);
  background: var(--el-color-white);
}

.error-message {
  margin-left: 40px;
  margin-top: 2px;
  font-family: Helvetica;
  font-size: 13px;
  line-height: 18px;
  position: absolute;
  width: 400px;

  &.invalid-email,
  &.invalid-password {
    color: var(--el-color-danger);
  }

  &.not-registered {
    color: var(--el-color-danger);
    display: flex;
    align-items: center;
    gap: 4px;

    :deep(.signup-link) {
      color: var(--el-color-primary) !important;
      text-decoration: none;
      margin-left: 4px;
      font-family: Helvetica;
      font-size: 14px;
      line-height: 18px;
      margin-left: 4px;
      display: inline-block;
      cursor: pointer;
    }

    span {
      display: inline;
    }
  }
}

.signup-link {
  color: var(--el-color-primary);
  text-decoration: none;
  margin-left: 4px;
  font-family: Helvetica;
  font-size: 14px;
  line-height: 18px;
  margin-left: 4px;
  display: inline-block;

  &:hover {
    text-decoration: underline;
    color: var(--el-color-primary-light-1);
  }
}

.form-group>div {
  position: relative;
  margin-bottom: 5px;
}

.login-title {
  font-family: Helvetica-Bold;
  font-size: 32px;
  color: #222;
  letter-spacing: 0;
  font-weight: 700;
  text-align: left;
  margin: 40px 0 32px 40px;
  position: relative;
  line-height: 1.2;
}

.login-title::after {
  content: '';
  display: block;
  width: 56px;
  height: 4px;
  background: #2e5bee;
  border-radius: 2px;
  margin-top: 8px;
  margin-left: 0;
}

.password-toggle {
  position: absolute;
  top: 50%;
  right: 12px;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  cursor: pointer;
  z-index: 1;
  
  &:hover svg path {
    fill: var(--el-color-primary);
  }
}

.supplier-name {
  text-align: center;
  font-family: Helvetica;
  font-size: 14px;
  color: var(--el-text-color-regular);
  letter-spacing: 0;
  line-height: 18px;
  font-weight: 400;
  margin-top: 30px;
  padding-bottom: 20px;
  border-top: 1px solid var(--el-border-color-lighter);
  padding-top: 20px;
}
</style>
