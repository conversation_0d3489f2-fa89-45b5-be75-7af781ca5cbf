<template>
  <div class="price-change-requests">
    <!-- 搜索和操作区域 -->
    <div class="search-section">
      <el-card class="search-card" shadow="never">
        <div class="search-header">
          <div class="search-title">
            <h3>价格修改申请</h3>
            <p class="search-description">查看和管理您提交的价格修改申请</p>
          </div>
        </div>
        
        <div class="search-form-wrapper">
          <el-form :model="searchForm" inline class="search-form">
            <el-form-item label="申请状态">
              <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 150px">
                <el-option label="待审核" :value="1" />
                <el-option label="审核通过" :value="2" />
                <el-option label="审核拒绝" :value="3" />
                <el-option label="已撤回" :value="4" />
              </el-select>
            </el-form-item>
            <el-form-item label="商品名称">
              <el-input v-model="searchForm.productName" placeholder="请输入商品名称" style="width: 200px" clearable />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" :loading="tableLoading">
                搜索
              </el-button>
              <el-button @click="handleReset">
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
    </div>

    <!-- 申请列表表格 -->
    <div class="table-section">
      <el-card class="table-card" shadow="never">
        <div class="table-header">
          <div class="table-title">
            <span>申请列表</span>
            <el-tag v-if="pagination.total > 0" type="info" class="total-count">
              共 {{ pagination.total }} 条记录
            </el-tag>
          </div>
        </div>

        <div class="table-wrapper">
          <el-table 
            :data="tableData" 
            :loading="tableLoading" 
            v-loading="tableLoading"
            stripe
            border
            style="width: 100%"
            :header-cell-style="{ background: '#fafafa', color: '#606266', fontWeight: '600' }"
          >
            <el-table-column prop="requestNo" label="申请单号" width="180" fixed="left">
              <template #default="{ row }">
                <el-link type="primary" @click="handleViewDetail(row)">
                  {{ row.requestNo }}
                </el-link>
              </template>
            </el-table-column>
            <el-table-column prop="requestTypeName" label="申请类型" width="100" align="center">
              <template #default="{ row }">
                <el-tag size="small" type="info">{{ row.requestTypeName }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="productName" label="商品名称" min-width="200" show-overflow-tooltip>
              <template #default="{ row }">
                <div class="product-info">
                  <div class="product-name">{{ row.productName || '-' }}</div>
                  <div v-if="row.productCode" class="product-code">{{ row.productCode }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="skuCount" label="SKU数量" width="100" align="center" />
            <el-table-column prop="statusName" label="状态" width="120" align="center">
              <template #default="{ row }">
                <el-tag :type="getStatusTagType(row.status)" size="small">
                  {{ row.statusName }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="submitTime" label="提交时间" width="160" align="center">
              <template #default="{ row }">
                <div class="time-info">
                  <div class="date">{{ row.submitTime }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="reviewTime" label="审核时间" width="160" align="center">
              <template #default="{ row }">
                <div v-if="row.reviewTime" class="time-info">
                  <div class="date">{{ row.reviewTime }}</div>
                </div>
                <span v-else class="no-data">-</span>
              </template>
            </el-table-column>
            <el-table-column prop="reviewerName" label="审核人" width="120" align="center">
              <template #default="{ row }">
                <span v-if="row.reviewerName" class="reviewer-name">{{ row.reviewerName }}</span>
                <span v-else class="no-data">-</span>
              </template>
            </el-table-column>
            <el-table-column prop="requestReason" label="申请原因" min-width="200" show-overflow-tooltip>
              <template #default="{ row }">
                <div class="reason-text">{{ row.requestReason }}</div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="180" fixed="right" align="center">
              <template #default="{ row }">
                <div class="action-buttons">
                  <el-button link type="primary" size="small" @click="handleViewDetail(row)">
                    查看详情
                  </el-button>
                  <el-button 
                    v-if="row.status === 1" 
                    link 
                    type="warning" 
                    size="small"
                    @click="handleWithdraw(row)"
                  >
                    撤回申请
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            background
          />
        </div>
      </el-card>
    </div>

    <!-- 申请详情抽屉 -->
    <el-drawer
      v-model="detailDrawerVisible"
      title="价格修改申请详情"
      size="75%"
      :before-close="handleCloseDetail"
      class="detail-drawer"
    >
      <div v-if="currentDetail" class="detail-content">
        <!-- 申请基本信息 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>申请信息</span>
              <el-tag :type="getStatusTagType(currentDetail.status)" class="status-tag">
                {{ currentDetail.statusName }}
              </el-tag>
            </div>
          </template>
          <el-descriptions :column="2" border class="detail-descriptions">
            <el-descriptions-item label="申请单号">
              <el-text type="primary" class="request-no">{{ currentDetail.requestNo }}</el-text>
            </el-descriptions-item>
            <el-descriptions-item label="申请类型">
              <el-tag size="small" type="info">{{ currentDetail.requestTypeName }}</el-tag>
            </el-descriptions-item>

            <!-- 商品信息 -->
            <el-descriptions-item label="商品名称" v-if="currentDetail.productDetails && currentDetail.productDetails.length > 0">
              <div class="product-name-info">
                <el-text type="primary" class="product-name">{{ currentDetail.productDetails[0].productName }}</el-text>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="商品编码" v-if="currentDetail.productDetails && currentDetail.productDetails.length > 0">
              <el-text type="info" class="product-code">{{ currentDetail.productDetails[0].productCode || '待生成' }}</el-text>
            </el-descriptions-item>
            <el-descriptions-item label="SKU变更" v-if="currentDetail.productDetails && currentDetail.productDetails.length > 0">
              <div class="sku-change-summary">
                <span v-if="currentDetail.productDetails[0].skuAddCount > 0" class="change-item add">
                  新增{{ currentDetail.productDetails[0].skuAddCount }}个
                </span>
                <span v-if="currentDetail.productDetails[0].skuUpdateCount > 0" class="change-item update">
                  修改{{ currentDetail.productDetails[0].skuUpdateCount }}个
                </span>
                <span v-if="currentDetail.productDetails[0].skuDeleteCount > 0" class="change-item delete">
                  删除{{ currentDetail.productDetails[0].skuDeleteCount }}个
                </span>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="SKU数量">
              {{ currentDetail.skuCount }}
            </el-descriptions-item>
            <el-descriptions-item label="申请状态">
              <el-tag :type="getStatusTagType(currentDetail.status)">
                {{ currentDetail.statusName }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="提交时间">
              {{ formatDateTime(currentDetail.submitTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="审核时间">
              <span v-if="currentDetail.reviewTime">{{ formatDateTime(currentDetail.reviewTime) }}</span>
              <span v-else class="no-data">-</span>
            </el-descriptions-item>
            <el-descriptions-item label="审核人">
              <span v-if="currentDetail.reviewerName">{{ currentDetail.reviewerName }}</span>
              <span v-else class="no-data">-</span>
            </el-descriptions-item>
            
            <el-descriptions-item label="申请原因" :span="2">
              <div class="reason-content">{{ currentDetail.requestReason }}</div>
            </el-descriptions-item>
            <el-descriptions-item v-if="currentDetail.productDetails && currentDetail.productDetails[0].priceChangeSummary" label="价格变更摘要" :span="2">
              <div class="price-change-summary">{{ currentDetail.productDetails[0].priceChangeSummary }}</div>
            </el-descriptions-item>
            <el-descriptions-item v-if="currentDetail.reviewComment" label="审核意见" :span="2">
              <div class="review-comment">
                <el-alert :type="currentDetail.status === 2 ? 'success' : 'error'" :closable="false">
                  {{ currentDetail.reviewComment }}
                </el-alert>
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- SKU价格变更明细 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>价格变更明细</span>
              <span class="record-count">共 {{ currentDetail.skuDetails?.length || 0 }} 条变更记录</span>
            </div>
          </template>
          <div class="price-change-table">
            <el-table 
              :data="currentDetail.skuDetails" 
              border 
              stripe
              :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontWeight: '600' }"
            >
              <el-table-column prop="specCombination" label="规格组合" width="150" show-overflow-tooltip>
                <template #default="{ row }">
                  <div class="spec-combination">{{ row.specCombination || '-' }}</div>
                </template>
              </el-table-column>
              <el-table-column prop="skuCode" label="SKU编码" width="150" fixed="left">
                <template #default="{ row }">
                  <el-text type="primary" class="sku-code">{{ row.skuCode || '待生成' }}</el-text>
                </template>
              </el-table-column>
              <el-table-column prop="operationTypeName" label="操作类型" width="100" align="center">
                <template #default="{ row }">
                  <el-tag size="small" type="info">{{ row.operationTypeName }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="成本价变更" width="200" align="center">
                <template #default="{ row }">
                  <div v-if="hasPriceChange(row.oldCostPrice, row.newCostPrice)" class="price-change">
                    <span class="old-price">{{ formatPrice(row.oldCostPrice) }}</span>
                    <span class="arrow-icon">→</span>
                    <span :class="getPriceChangeClass(row.oldCostPrice, row.newCostPrice)">{{ formatPrice(row.newCostPrice) }}</span>
                  </div>
                  <span v-else class="no-change">无变更</span>
                </template>
              </el-table-column>
              <el-table-column label="采购价变更" width="200" align="center">
                <template #default="{ row }">
                  <div v-if="hasPriceChange(row.oldPurchasePrice, row.newPurchasePrice)" class="price-change">
                    <span class="old-price">{{ formatPrice(row.oldPurchasePrice) }}</span>
                    <span class="arrow-icon">→</span>
                    <span :class="getPriceChangeClass(row.oldPurchasePrice, row.newPurchasePrice)">{{ formatPrice(row.newPurchasePrice) }}</span>
                  </div>
                  <span v-else class="no-change">无变更</span>
                </template>
              </el-table-column>
              <el-table-column label="划线价变更" width="200" align="center">
                <template #default="{ row }">
                  <div v-if="hasPriceChange(row.oldStrikethroughPrice, row.newStrikethroughPrice)" class="price-change">
                    <span class="old-price">{{ formatPrice(row.oldStrikethroughPrice) }}</span>
                    <span class="arrow-icon">→</span>
                    <span :class="getPriceChangeClass(row.oldStrikethroughPrice, row.newStrikethroughPrice)">{{ formatPrice(row.newStrikethroughPrice) }}</span>
                  </div>
                  <span v-else class="no-change">无变更</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </div>
    </el-drawer>

    <!-- 撤回申请对话框 -->
    <el-dialog
      v-model="withdrawDialogVisible"
      title="撤回申请"
      width="500px"
      :before-close="handleCancelWithdraw"
      class="withdraw-dialog"
    >
      <div class="withdraw-content">
        <el-alert type="warning" :closable="false" class="withdraw-alert">
          <template #title>
            <span>确定要撤回此价格修改申请吗？</span>
          </template>
        </el-alert>
        
        <el-form ref="withdrawFormRef" :model="withdrawForm" :rules="withdrawFormRules" label-width="100px" class="withdraw-form">
          <el-form-item label="撤回原因" prop="withdrawReason">
            <el-input
              v-model="withdrawForm.withdrawReason"
              type="textarea"
              :rows="4"
              placeholder="请详细说明撤回原因，以便后续处理..."
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancelWithdraw">取消</el-button>
          <el-button type="warning" @click="handleConfirmWithdraw" :loading="withdrawLoading">
            确定撤回
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowRight } from '@element-plus/icons-vue'
import {
  getPriceChangeRequests,
  getPriceChangeRequestDetail,
  withdrawPriceChangeRequest
} from '@/api/product'

// 搜索表单
const searchForm = reactive({
  status: null,
  productName: ''
})

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 加载状态
const tableLoading = ref(false)
const withdrawLoading = ref(false)

// 表格数据
const tableData = ref([])

// 申请详情
const detailDrawerVisible = ref(false)
const currentDetail = ref(null)

// 撤回申请
const withdrawDialogVisible = ref(false)
const currentWithdrawRequest = ref(null)
const withdrawForm = reactive({
  withdrawReason: ''
})
const withdrawFormRef = ref(null)

// 撤回表单验证规则
const withdrawFormRules = {
  withdrawReason: [
    { required: true, message: '请输入撤回原因', trigger: 'blur' },
    { min: 4, message: '撤回原因至少4个字符', trigger: 'blur' }
  ]
}

// 加载申请列表
const loadRequestList = async () => {
  try {
    tableLoading.value = true
    
    const params = {
      page: pagination.currentPage,
      size: pagination.pageSize,
      status: searchForm.status,
      productName: searchForm.productName
    }
    
    const response = await getPriceChangeRequests(params)
    if (response.code === 200) {
      tableData.value = response.data.records
      pagination.total = response.data.total
    } else {
      ElMessage.error(response.msg || '获取申请列表失败')
    }
  } catch (error) {
    console.error('获取申请列表失败:', error)
    ElMessage.error('获取申请列表失败')
  } finally {
    tableLoading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1
  loadRequestList()
}

// 重置
const handleReset = () => {
  searchForm.status = null
  searchForm.productName = ''
  pagination.currentPage = 1
  loadRequestList()
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadRequestList()
}

const handleCurrentChange = (page) => {
  pagination.currentPage = page
  loadRequestList()
}

// 查看详情
const handleViewDetail = async (row) => {
  try {
    const response = await getPriceChangeRequestDetail(row.id)
    if (response.code === 200) {
      currentDetail.value = response.data
      detailDrawerVisible.value = true
    } else {
      ElMessage.error(response.msg || '获取申请详情失败')
    }
  } catch (error) {
    console.error('获取申请详情失败:', error)
    ElMessage.error('获取申请详情失败')
  }
}

// 关闭详情
const handleCloseDetail = () => {
  detailDrawerVisible.value = false
  currentDetail.value = null
}

// 撤回申请
const handleWithdraw = (row) => {
  currentWithdrawRequest.value = row
  withdrawForm.withdrawReason = ''
  withdrawDialogVisible.value = true
}

// 取消撤回
const handleCancelWithdraw = () => {
  withdrawDialogVisible.value = false
  currentWithdrawRequest.value = null
  withdrawForm.withdrawReason = ''
}

// 确认撤回
const handleConfirmWithdraw = async () => {
  try {
    await withdrawFormRef.value.validate()
    
    withdrawLoading.value = true
    const response = await withdrawPriceChangeRequest(
      currentWithdrawRequest.value.id,
      { withdrawReason: withdrawForm.withdrawReason }
    )
    
    if (response.code === 200) {
      ElMessage.success('申请撤回成功')
      withdrawDialogVisible.value = false
      currentWithdrawRequest.value = null
      withdrawForm.withdrawReason = ''
      loadRequestList()
    } else {
      ElMessage.error(response.msg || '撤回申请失败')
    }
  } catch (error) {
    console.error('撤回申请失败:', error)
    ElMessage.error('撤回申请失败')
  } finally {
    withdrawLoading.value = false
  }
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  switch (status) {
    case 1: return 'warning' // 待审核
    case 2: return 'success' // 审核通过
    case 3: return 'danger'  // 审核拒绝
    case 4: return 'info'    // 已撤回
    default: return 'info'
  }
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 格式化日期
const formatDate = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 格式化时间
const formatTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 格式化价格
const formatPrice = (price) => {
  if (price === null || price === undefined) return '-'
  return `¥${Number(price).toFixed(2)}`
}

// 判断价格是否有变化
const hasPriceChange = (oldPrice, newPrice) => {
  // 如果新价格为null或undefined，说明没有设置新价格
  if (newPrice === null || newPrice === undefined) {
    return false
  }
  
  // 如果旧价格为null或undefined，但新价格有值，说明是新增价格
  if (oldPrice === null || oldPrice === undefined) {
    return Number(newPrice) !== 0 // 只有新价格不为0才算有变化
  }
  
  // 两个价格都有值，比较是否相等
  return Number(oldPrice) !== Number(newPrice)
}

// 获取价格变更的样式类
const getPriceChangeClass = (oldPrice, newPrice) => {
  if (oldPrice === null || newPrice === null) return 'new-price'
  
  const oldValue = Number(oldPrice)
  const newValue = Number(newPrice)
  
  if (newValue > oldValue) {
    return 'price-increase' // 价格上涨，红色
  } else if (newValue < oldValue) {
    return 'price-decrease' // 价格下降，绿色
  } else {
    return 'new-price' // 价格不变，默认颜色
  }
}

// 获取变更类型名称
const getChangeTypeName = (changeType) => {
  switch (changeType) {
    case 1: return '仅价格'
    case 2: return '仅规格'
    case 3: return '价格+规格'
    default: return '未知'
  }
}

// 获取变更类型标签类型
const getChangeTypeTagType = (changeType) => {
  switch (changeType) {
    case 1: return 'primary'  // 仅价格
    case 2: return 'warning'  // 仅规格
    case 3: return 'danger'   // 价格+规格
    default: return 'info'
  }
}

// 页面初始化
onMounted(() => {
  loadRequestList()
})
</script>

<style lang="scss" scoped>
.price-change-requests {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 120px);

  // 搜索区域样式
  .search-section {
    margin-bottom: 20px;

    .search-card {
      border: 1px solid #e4e7ed;
      border-radius: 8px;

      :deep(.el-card__body) {
        padding: 24px;
      }

      .search-header {
        margin-bottom: 20px;

        .search-title {
          h3 {
            margin: 0 0 8px 0;
            font-size: 20px;
            font-weight: 600;
            color: #303133;
            display: flex;
            align-items: center;
            
            &::before {
              content: '';
              width: 4px;
              height: 20px;
              background: linear-gradient(135deg, #409eff, #67c23a);
              margin-right: 12px;
              border-radius: 2px;
            }
          }

          .search-description {
            margin: 0;
            font-size: 14px;
            color: #909399;
            padding-left: 16px;
          }
        }
      }

      .search-form-wrapper {
        .search-form {
          :deep(.el-form-item) {
            margin-bottom: 0;
            margin-right: 24px;

            &:last-child {
              margin-right: 0;
            }
          }

          :deep(.el-form-item__label) {
            font-weight: 500;
            color: #606266;
          }

          
        }
      }
    }
  }

  // 表格区域样式
  .table-section {
    .table-card {
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      overflow: hidden;

      :deep(.el-card__body) {
        padding: 0;
      }

      .table-header {
        padding: 20px 24px;
        background: #fafafa;
        border-bottom: 1px solid #e4e7ed;

        .table-title {
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 16px;
          font-weight: 600;
          color: #303133;

          .total-count {
            font-size: 12px;
            font-weight: normal;
          }
        }
      }

      .table-wrapper {
        .el-table {
          border: none;

          :deep(.el-table__header) {
            th {
              border-bottom: 2px solid #e4e7ed;
            }
          }

          :deep(.el-table__body) {
            tr {
              &:hover {
                background-color: #f5f7fa;
              }
            }

            td {
              border-bottom: 1px solid #f0f2f5;
            }
          }

          

          .time-info {
            .date {
              font-size: 13px;
              color: #303133;
              font-weight: 500;
            }

            .time {
              font-size: 12px;
              color: #909399;
              margin-top: 2px;
            }
          }

          .no-data {
            color: #c0c4cc;
            font-style: italic;
          }

          .reviewer-name {
            color: #606266;
            font-weight: 500;
          }

          .reason-text {
            color: #606266;
            line-height: 1.4;
          }

          .product-info {
            .product-name {
              font-weight: 600;
              color: #303133;
              margin-bottom: 4px;
              line-height: 1.4;
            }

            .product-code {
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
              font-size: 12px;
              color: #909399;
              font-weight: 500;
            }
          }

          .action-buttons {
            display: flex;
            justify-content: center;
            gap: 8px;

            
          }
        }
      }

      .pagination-wrapper {
        padding: 20px 24px;
        display: flex;
        justify-content: flex-end;
        border-top: 1px solid #e4e7ed;
        background: #fafafa;
      }
    }
  }

  // 详情抽屉样式
  :deep(.detail-drawer) {
    .el-drawer__header {
      padding: 20px 24px;
      border-bottom: 1px solid #e4e7ed;
      margin-bottom: 0;

      .el-drawer__title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }
    }

    .el-drawer__body {
      padding: 0;
      background: #f5f7fa;
    }
  }

  .detail-content {
    padding: 20px;
    height: 100%;
    overflow-y: auto;

    .detail-card {
      margin-bottom: 20px;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      overflow: hidden;

      &:last-child {
        margin-bottom: 0;
      }

      :deep(.el-card__header) {
        background: #fafafa;
        border-bottom: 1px solid #e4e7ed;
        padding: 16px 20px;

        .card-header {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 16px;
          font-weight: 600;
          color: #303133;

                     .status-tag {
             margin-left: auto;
           }

           .record-count {
             font-size: 12px;
             color: #909399;
             font-weight: normal;
           }
        }
      }

      :deep(.el-card__body) {
        padding: 24px;
      }

      .detail-descriptions {
        :deep(.el-descriptions__label) {
          font-weight: 600;
          color: #606266;
          background: #f8f9fa;
        }

        :deep(.el-descriptions__content) {
          color: #303133;
        }

        .request-no {
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-weight: 600;
        }

        .product-name-info {
          .product-name {
            font-weight: 600;
            font-size: 15px;
          }
        }

        .product-code {
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-weight: 500;
        }

        .sku-change-summary {
          display: flex;
          gap: 8px;
          flex-wrap: wrap;

          .change-item {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 12px;
            font-weight: 500;
            
            &.add {
              background: #e6f7ff;
              color: #1890ff;
              border: 1px solid #91d5ff;
            }

            &.update {
              background: #fff7e6;
              color: #fa8c16;
              border: 1px solid #ffd591;
            }
            
            &.delete {
              background: #fff2f0;
              color: #ff4d4f;
              border: 1px solid #ffb3b3;
            }
          }
        }

        .price-change-summary {
          background: #f0f9ff;
          padding: 8px 12px;
          border-radius: 4px;
          border: 1px solid #d6e4ff;
          color: #303133;
          line-height: 1.5;
        }

        .reason-content {
          background: #f8f9fa;
          padding: 12px;
          border-radius: 6px;
          border-left: 4px solid #409eff;
          line-height: 1.6;
        }

        .review-comment {
          :deep(.el-alert) {
            border-radius: 6px;

            .el-alert__content {
              line-height: 1.6;
            }
          }
        }
      }

      .price-change-table {
        .el-table {
          border-radius: 6px;
          overflow: hidden;

          .sku-code {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-weight: 600;
          }

          .spec-combination {
            color: #606266;
            font-size: 13px;
          }

          .price-change {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 4px 0;

                       .old-price {
             color: #606266;
             text-decoration: line-through;
             font-size: 13px;
           }

           .arrow-icon {
             color: #409eff;
             font-size: 16px;
             font-weight: bold;
           }

           .new-price {
             color: #606266;
             font-weight: 600;
             font-size: 14px;
           }

           .price-increase {
             color: #f56c6c;
             font-weight: 600;
             font-size: 14px;
           }

           .price-decrease {
             color: #67c23a;
             font-weight: 600;
             font-size: 14px;
           }
          }

          .no-change {
            color: #c0c4cc;
            font-style: italic;
            font-size: 13px;
          }

          
        }
      }
    }
  }

  // 撤回对话框样式
  :deep(.withdraw-dialog) {
    .el-dialog__header {
      padding: 20px 24px 0;

      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }
    }

    .el-dialog__body {
      padding: 20px 24px;
    }
  }

  .withdraw-content {
         .withdraw-alert {
       margin-bottom: 20px;
       border-radius: 6px;
     }

    .withdraw-form {
      :deep(.el-form-item__label) {
        font-weight: 600;
        color: #606266;
      }

      :deep(.el-textarea__inner) {
        border-radius: 6px;
        border: 1px solid #dcdfe6;

        &:focus {
          border-color: #409eff;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px 24px;
         border-top: 1px solid #e4e7ed;
     margin: 0 -24px -20px;
     background: #fafafa;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .price-change-requests {
    .search-form {
      :deep(.el-form-item) {
        margin-right: 16px;
      }
    }

    .table-wrapper {
      overflow-x: auto;
    }
  }
}

@media (max-width: 768px) {
  .price-change-requests {
    padding: 12px;

    .search-card {
      :deep(.el-card__body) {
        padding: 16px;
      }
    }

    .search-form {
      :deep(.el-form-item) {
        margin-bottom: 12px;
        margin-right: 0;
        width: 100%;
      }
    }

    .table-header {
      padding: 16px !important;
    }

    .pagination-wrapper {
      padding: 16px !important;
      justify-content: center;
    }
  }
}
</style> 