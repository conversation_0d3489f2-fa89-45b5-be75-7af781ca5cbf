<template>
  <div class="account-layout">
    <el-container class="page">
      <el-header  class="page-header">
        <nav-header :top-menus="topMenus" />
      </el-header>
      <el-container class="layout-content">
        <el-aside width="160px">
          <p class="layout-content-title">账户设置</p>
          <account-menu :collapse="false" />
        </el-aside>
        <el-main class="page-content">
          <div class="page-info" v-if="!$route.meta.hideWhiteBg">
            <router-view></router-view>
          </div>
          <router-view v-else></router-view>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue'
import AccountMenu from '@/views/account/accountMenu.vue'
import NavHeader from '@/layout/nav-header/index.vue'
import MobBreadcrumb from '@/base-ui/breadcrumb'
import { useMenuStore } from '@/store/menu'

const menuStore = useMenuStore()

// 使用menu store中的topMenus
const topMenus = computed(() => menuStore.topMenus)
console.log("topMenus", topMenus)

// 直接使用 menu store 中的 activeSideMenus
const sideMenus = computed(() => menuStore.activeSideMenus)
export default defineComponent({
  name: 'layout',
  props: {},
  components: {
    AccountMenu,
    NavHeader,
    MobBreadcrumb
  },
  setup() {

    return {
      topMenus
    }
  }
})
</script>

<style scoped lang="scss">
.account-layout {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--el-bg-color-base);
  .layout-content {
    height: calc(100% - 52px);
    width: 1078px;
    margin: 0 auto;
    background-color: var(--el-bg-color-base);
    padding: 30px 0;
    &-title{
      font-size: 16px;
      font-weight: 600;
    }
  }

  .page {
    height: 100%;
    &-header {
      background-color: var(--el-color-white);
    }
  }

  .page-content {
    height: 100%;
    .page-info {
      background-color: var(--el-color-white);
      padding: 10px;
      border-radius: 5px;
      margin-top: 10px;
      height: calc(100% - 34px);
      box-shadow: 0px 1px 6px 0px rgba(0, 0, 0, 0.06);
      overflow-y: scroll;
    }
  }

  .el-header,
  .el-footer {
    display: flex;
    color: var(--el-text-color-primary);
    text-align: center;
    align-items: center;
  }

  .el-header {
    height: 52px !important;
  }

  .el-aside {
    overflow-x: hidden;
    overflow-y: auto;
    text-align: left;
    cursor: pointer;
    transition: width 0.3s linear;
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      display: none;
    }
  }

  .el-main {
    color: var(--el-text-color-primary);
    background-color: var(--el-bg-color-base);
    padding: 0;
    margin-left: 50px;
  }
}
</style>
