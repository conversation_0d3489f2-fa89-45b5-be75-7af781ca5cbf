import type { IDataType } from '@/service/request/types'
import $http from '@/service'

// 商品响应DTO
export interface ProductResponse {
  id: number
  supplierId: number
  name: string
  spuCode: string
  categoryId: string
  barcode?: string
  type: number
  status: number
  reviewDescribe?: string
  description?: string
  features?: string
  instructions?: string
  isRecommend: number
  spec1Name?: string
  spec2Name?: string
  spec3Name?: string
  volume?: number
  weight?: number
  length?: number
  wide?: number
  tall?: number
  minOrderQuantity?: number
  maxOrderQuantity?: number
  leadTime?: number
  warrantyPeriod?: number
  shelfLife?: number
  createTime: string
  updateTime: string
  // 列表展示用字段
  mainImageUrl?: string
  minPrice?: number
  maxPrice?: number
  totalStock?: number
  // 详情展示用字段
  skuList?: SkuResponse[]
  images?: ImageResponse[]
}

// SKU响应DTO
export interface SkuResponse {
  id: number
  spuId: number
  skuCode: string
  costPrice: number
  strikethroughPrice?: number
  purchasePrice: number
  spec1Value?: string
  spec2Value?: string
  spec3Value?: string
  totalInventory: number
  availableInventory?: number
  blockedInventory?: number
  safetyStock?: number
  imgUrl?: string
  sortOrder?: number
  createTime: string
  updateTime: string
}

// 图片响应DTO
export interface ImageResponse {
  id: number
  spuId: number
  imgUrl: string
  isMain: number
  sortOrder?: number
  createTime: string
}

// 商品请求DTO
export interface ProductRequest {
  supplierId?: number
  name: string
  spuCode: string
  categoryId?: string
  barcode?: string
  type?: number
  status?: number
  reviewDescribe?: string
  description?: string
  features?: string
  instructions?: string
  isRecommend?: number
  spec1Name?: string
  spec2Name?: string
  spec3Name?: string
  volume?: number
  weight?: number
  length?: number
  wide?: number
  tall?: number
  minOrderQuantity?: number
  maxOrderQuantity?: number
  leadTime?: number
  warrantyPeriod?: number
  shelfLife?: number
  skuList: SkuRequest[]
  images?: ImageRequest[]
}

// SKU请求DTO
export interface SkuRequest {
  skuCode: string
  costPrice: number
  strikethroughPrice?: number
  purchasePrice: number
  spec1Value?: string
  spec2Value?: string
  spec3Value?: string
  totalInventory: number
  availableInventory?: number
  blockedInventory?: number
  safetyStock?: number
  imgUrl?: string
  sortOrder?: number
}

// 图片请求DTO
export interface ImageRequest {
  imgUrl: string
  isMain?: number
  sortOrder?: number
}

// 商品列表查询参数
export interface ProductListParams {
  page?: number
  size?: number
  productName?: string
  category?: string
  status?: number
  type?: number
}

// 商品统计信息
export interface ProductStatistics {
  total: number
  selling: number
  reviewing: number
  warehouse: number
  offline: number
  draft?: number
  rejected?: number
}

// 审核记录
export interface ReviewLog {
  id: number
  productId: number
  status: number
  reviewReason?: string
  reviewTime: string
  createTime: string
}

// 审核请求参数
export interface ReviewRequest {
  submitReason?: string
  withdrawReason?: string
}

// 分页响应
export interface PageResponse<T> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
}

// 获取商品列表
export function getProductList(params: ProductListParams) {
  return $http.get<IDataType<PageResponse<ProductResponse>>>({
    url: '/product/list',
    params
  })
}

// 获取商品详情
export function getProductDetail(id: number) {
  return $http.get<IDataType<ProductResponse>>({
    url: `/product/${id}`
  })
}

// 创建商品
export function createProduct(data: ProductRequest) {
  return $http.post<IDataType<ProductResponse>>({
    url: '/product/add',
    data
  })
}

// 更新商品
export function updateProduct(id: number, data: ProductRequest) {
  return $http.put<IDataType<ProductResponse>>({
    url: `/product/${id}`,
    data
  })
}

// 删除商品
export function deleteProduct(id: number) {
  return $http.delete<IDataType<string>>({
    url: `/product/${id}`
  })
}

// 批量删除商品
export function batchDeleteProducts(ids: number[]) {
  return $http.delete<IDataType<string>>({
    url: '/product/batch',
    data: ids
  })
}



// 批量下架商品
export function batchOfflineProducts(ids: number[]) {
  return $http.put<IDataType<string>>({
    url: '/product/batch/offline',
    data: ids
  })
}



// 获取商品统计信息
export function getProductStatistics() {
  return $http.get<IDataType<ProductStatistics>>({
    url: '/product/statistics'
  })
}

// 提交商品审核
export function submitProductReview(id: number, data: ReviewRequest) {
  return $http.post<IDataType<{
    message: string
    productId: number
    productName: string
    newStatus: number
  }>>({
    url: `/product/${id}/submit-review`,
    data
  })
}

// 批量提交商品审核
export function batchSubmitProductReview(data: {
  ids: number[]
  submitReason?: string
}) {
  return $http.post<IDataType<{
    message: string
    successCount: number
    totalCount: number
  }>>({
    url: '/product/batch/submit-review',
    data
  })
}

// 修改商品价格
export interface UpdatePriceRequest {
  priceMethod: number // 调价方式 1-公式改价
  basePrice: string // 基准价格类型 suggested-建议价 cost-成本价 purchase-采购价
  multiplier: number // 价格倍数
  adjustment: number // 上调价
  roundingMethod: number // 价格取整方式 1-四舍五入 2-向上取整 3-向下取整
  decimalPlaces: number // 小数点位数
  spuIdList: number[] // 商品SPU ID列表
}

// 单个商品修改价格
export function updateProductPrice(id: number,data: UpdatePriceRequest) {
  return $http.post<IDataType<{
    message: string
    successCount: number
    totalCount: number
  }>>({
    url: `/product/${id}/update-price`,
    data
  })
}

// 撤回商品审核
export function withdrawProductReview(id: number, data: ReviewRequest) {
  return $http.post<IDataType<string>>({
    url: `/product/${id}/withdraw-review`,
    data
  })
}

// 获取商品审核记录
export function getProductReviewLogs(id: number) {
  return $http.get<IDataType<ReviewLog[]>>({
    url: `/product/${id}/review-logs`
  })
}

// 价格修改申请请求DTO
export interface PriceChangeRequest {
  requestType: 'price'
  requestReason: string
  spuIdList: number[]
  productData: Array<{
    spuId: number
    specType: 'single' | 'multiple'
    singleSku?: {
      costPrice?: number
      purchasePrice?: number
      strikethroughPrice?: number
    }
    skuList?: Array<{
      id: number
      costPrice?: number
      purchasePrice?: number
      strikethroughPrice?: number
    }>
  }>
}

// 规格调整申请请求DTO
export interface SpecChangeRequest {
  requestType: 'spec'
  requestReason: string
  spuIdList: number[]
  productData: {
    specType: 'single' | 'multiple'
    singleSku?: {
      costPrice?: number
      purchasePrice?: number
      strikethroughPrice?: number
      totalInventory?: number
      safetyStock?: number
    }
    specList?: any[]
    skuList?: any[]
  }
}

// 提交价格修改申请（新接口）
export function submitPriceChangeRequest(data: {
  productIds: number[]
  requestReason: string
  skuChanges: Array<{
    spuId: number
    skuId: number
    skuCode: string
    oldCostPrice?: number
    newCostPrice?: number
    oldPurchasePrice?: number
    newPurchasePrice?: number
    oldStrikethroughPrice?: number
    newStrikethroughPrice?: number
  }>
}) {
  return $http.post<IDataType<{
    message: string
    requestId: number
    requestNo: string
    productCount: number
    skuCount: number
  }>>({
    url: '/product/submit-price-change-request',
    data
  })
}

// 获取价格修改申请列表
export function getPriceChangeRequests(params: {
  page?: number
  size?: number
  status?: number
  productName?: string
}) {
  return $http.get<IDataType<PageResponse<{
    id: number
    requestNo: string
    requestType: number
    requestTypeName: string
    status: number
    statusName: string
    productCount: number
    skuCount: number
    submitTime: string
    reviewTime?: string
    reviewerName?: string
    reviewComment?: string
    requestReason: string
  }>>>({
    url: '/product/price-change-requests',
    params
  })
}

// 获取价格修改申请详情
export function getPriceChangeRequestDetail(requestId: number) {
  return $http.get<IDataType<{
    id: number
    requestNo: string
    requestType: number
    requestTypeName: string
    status: number
    statusName: string
    productCount: number
    skuCount: number
    submitTime: string
    reviewTime?: string
    reviewerName?: string
    reviewComment?: string
    requestReason: string
    expireTime?: string
    applyTime?: string
    applyStatus?: number
    applyError?: string
    productDetails: Array<{
      id: number
      spuId: number
      productName: string
      productCode: string
      changeType: number
      skuAddCount?: number
      skuUpdateCount?: number
      skuDeleteCount?: number
      priceChangeSummary?: string
    }>
    skuDetails: Array<{
      id: number
      skuId: number
      skuCode: string
      operationType: number
      operationTypeName: string
      specCombination?: string
      oldCostPrice?: number
      newCostPrice?: number
      oldPurchasePrice?: number
      newPurchasePrice?: number
      oldStrikethroughPrice?: number
      newStrikethroughPrice?: number
      changeFields: string
    }>
  }>>({
    url: `/product/price-change-requests/${requestId}`
  })
}

// 撤回价格修改申请
export function withdrawPriceChangeRequest(requestId: number, data: {
  withdrawReason: string
}) {
  return $http.post<IDataType<{
    message: string
    requestId: number
    requestNo: string
  }>>({
    url: `/product/price-change-requests/${requestId}/withdraw`,
    data
  })
}

// 检查商品是否有待审核的改价申请
export function checkPendingPriceRequests(spuIds: number[]) {
  return $http.get<IDataType<{
    spuPendingMap: Record<number, boolean>
    spuRequestNoMap: Record<number, string | null>
  }>>({
    url: '/product/check-pending-price-requests',
    params: {
      spuIds: spuIds.join(',')
    }
  })
}