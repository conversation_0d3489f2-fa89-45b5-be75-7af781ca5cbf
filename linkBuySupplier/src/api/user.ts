import $http from '@/service'

export interface IDataType<T> {
  code: number
  msg: string
  data: T
}

export interface IUser {
  id: number
  username: string
  email: string
  mobile: string
  status: number
  createTime: string
  updateTime: string
  supplierId?: number
}

export interface SupplierUserListParam {
  page: number
  size: number
  query?: string
}

export interface SupplierUserCreateParam {
  username: string
  email: string
  mobile: string
}

// ==================== 系统用户管理（保留用于兼容性） ====================
// 获取用户列表（分页+搜索）
export function getUserList(params: any) {
  return $http.get({ url: '/system/users', params })
}

// 获取用户详情
export function getUserDetail(id: number) {
  return $http.get({ url: `/system/users/${id}` })
}

// 新增用户
export function addUser(data: any) {
  return $http.post({ url: '/system/users', data })
}

// 更新用户
export function updateUser(id: number, data: any) {
  return $http.patch({ url: `/system/users/${id}`, data })
}

// 获取用户角色
export function getUserRoles(id: number) {
  return $http.get({ url: `/system/users/${id}/roles` })
}

// 绑定用户角色
export function bindUserRoles(id: number, roleIds: number[]) {
  return $http.post({ url: `/system/users/${id}/bindRole`, data: roleIds })
}

// ==================== 供应商用户管理 ====================
// 获取供应商用户列表
export function getSupplierUserList(params: SupplierUserListParam) {
  return $http.get<IDataType<{ records: IUser[]; total: number }>>({
    url: '/user/list',
    params
  })
}

// 获取供应商用户详情
export function getSupplierUserDetail(id: number) {
  return $http.get<IDataType<IUser>>({
    url: `/user/${id}`
  })
}

// 新增供应商用户
export function addSupplierUser(data: SupplierUserCreateParam) {
  return $http.post<IDataType<IUser>>({
    url: '/user/add',
    data
  })
}

// 删除供应商用户
export function deleteSupplierUser(id: number) {
  return $http.delete<IDataType<void>>({
    url: `/user/${id}`
  })
}

// 重置供应商用户密码
export function resetSupplierUserPassword(id: number) {
  return $http.post<IDataType<void>>({
    url: `/user/${id}/resetPassword`
  })
}

// 启用/禁用供应商用户
export function updateSupplierUserStatus(id: number, status: number) {
  return $http.post<IDataType<void>>({
    url: `/user/${id}/status`,
    params: { status }
  })
}
