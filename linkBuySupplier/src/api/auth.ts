import type { IDataType } from '@/service/request/types'
import $http from '@/service'

export interface IUser {
  userId: string
  name: string
  email: string
  mobile: string
  avatar: string
  token: string
  permissions: string[]
}

export interface LoginParam {
  mobile: string
  password: string
}

export interface SuperLoginParam {
  scode: string
  loginCode: string
}

// 普通登录
export function login(data: LoginParam) {
  return $http.post<IDataType<IUser>>({
    url: '/user/login',
    data
  })
}

// 超级登录
export function superLogin(data: SuperLoginParam) {
  return $http.post<IDataType<IUser>>({
    url: '/user/superLogin',
    data
  })
}

// 获取用户信息
export function getUserInfo() {
  return $http.get<IDataType<IUser>>({
    url: '/user/info',
    showLoading: true
  })
}

// 退出登录
export function logout() {
  return $http.post<IDataType<void>>({
    url: '/user/logout'
  })
}

// 修改密码
export function setPassword(data: { oldPassword: string; newPassword: string }) {
  return $http.post<IDataType<void>>({
    url: '/user/setPassword',
    data
  })
}

// 刷新token
export function refreshToken() {
  return $http.post<IDataType<{ token: string }>>({
    url: '/api/auth/refresh'
  })
}

// 刷新token
export function changePassword() {
  return $http.post<IDataType<{ token: string }>>({
    url: '/api/auth/changePassword'
  })
}
