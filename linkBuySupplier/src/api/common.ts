import type { MenuDto } from '@/types/menu'
import type { IDataType } from '@/service/request/types'
import type { IBizSupplier, IRegion } from '@/types/common'
import $http from '@/service'

// 单位接口类型
export interface IUnit {
  id: number
  unitName: string
  sortOrder: number
  createTime?: string
  updateTime?: string
}

export function getAllMenu() {
  return $http.get<IDataType<MenuDto[]>>({
    url: `common/allMenus`,
  })
}

export function getSupplierInfo(scode: string | undefined) {
  return $http.get<IDataType<IBizSupplier>>({
    url: `common/supplierInfo?scode=${scode}`,
  })
}

export function getAllProvinces() {
  return $http.get<IDataType<IRegion[]>>({
    url: `common/allProvinces`,
  })
}

export function getAllUnits() {
  return $http.get<IDataType<IUnit[]>>({
    url: `common/allUnits`,
  })
}
