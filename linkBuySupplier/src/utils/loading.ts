import { ElLoading } from 'element-plus'

let loadingInstance: any = null

export const showLoading = () => {
  loadingInstance = ElLoading.service({
    lock: true,
    text: '加载中....',
    background: 'rgba(0, 0, 0, 0.5)'
  })
}

export const hideLoading = () => {
  loadingInstance?.close()
}

export const withLoading = async (fn: () => Promise<any>) => {
  showLoading()
  try {
    await fn()
  } finally {
    hideLoading()
  }
}
