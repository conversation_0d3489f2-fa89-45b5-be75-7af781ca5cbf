import localCache from './local-cache'
import { logout as logout<PERSON><PERSON>, refreshToken as refreshToken<PERSON><PERSON> } from '@/api/auth'
import $http from '@/service'
import { getCurrentScode, buildRouteWithScode } from './url'

const TokenKeyPrefix = 'supplier_token_'

// 获取当前scode对应的token key
function getTokenKey(scode?: string): string {
  const currentScode = scode || getCurrentScode()
  return currentScode ? `${TokenKeyPrefix}${currentScode}` : TokenKeyPrefix + 'default'
}

export function getToken(scode?: string) {
  const tokenKey = getTokenKey(scode)
  return localCache.getCache(tokenKey)
}

export function setToken(token: string, scode?: string) {
  const tokenKey = getTokenKey(scode)
  localCache.setCache(tokenKey, token)
}

export function removeToken(scode?: string) {
  const tokenKey = getTokenKey(scode)
  localCache.removeCache(tokenKey)
}

export function logout(href = window.location.href) {
  const scode = getCurrentScode()
  
  logoutApi().then(() => {
    // 只移除当前scode对应的token
    removeToken(scode || undefined)
  })

  // 登出时保留scode参数
  if (scode) {
    const loginRoute = buildRouteWithScode('/login', scode)
    location.href = window.origin + '/#' + loginRoute.path + '?' + new URLSearchParams(loginRoute.query).toString()
  } else {
    location.href = window.origin + '/#/login'
  }
}

// 检查token是否即将过期
const isTokenExpiringSoon = (token: string): boolean => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    const expirationTime = payload.exp * 1000 // 转换为毫秒
    const currentTime = Date.now()
    // 如果token将在5分钟内过期，则刷新
    return expirationTime - currentTime < 5 * 60 * 1000
  } catch {
    return false
  }
}

// 刷新token
const refreshToken = async (scode?: string) => {
  try {
    const res = await refreshTokenApi()
    if (res.code === 200) {
      setToken(res.data.token, scode)
    }
  } catch (error) {
    console.error('刷新token失败:', error)
    logout()
  }
}

// 刷新token并重试请求
const refreshTokenAndRetry = async (config: any) => {
  try {
    const scode = getCurrentScode()
    await refreshToken(scode || undefined)
    // 使用新token重试请求
    return $http.request(config)
  } catch (error) {
    logout()
    return Promise.reject(error)
  }
}

// 检查token是否有效
export const isTokenValid = (token: string): boolean => {
  if (!token) return false
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    return payload.exp * 1000 > Date.now()
  } catch {
    return false
  }
}

// 获取token中的用户信息
export const getTokenPayload = (token: string): any => {
  try {
    return JSON.parse(atob(token.split('.')[1]))
  } catch {
    return null
  }
}

// 获取所有已登录的scode列表
export const getLoggedScodes = (): string[] => {
  const allKeys = Object.keys(localStorage)
  return allKeys
    .filter(key => key.startsWith(TokenKeyPrefix))
    .map(key => key.replace(TokenKeyPrefix, ''))
    .filter(scode => scode !== 'default' && getToken(scode))
}

// 清除指定scode的token
export const clearTokenForScode = (scode: string) => {
  removeToken(scode)
}

// 清除所有token
export const clearAllTokens = () => {
  const scodes = getLoggedScodes()
  scodes.forEach(scode => removeToken(scode))
  // 也清除默认token
  removeToken()
}
