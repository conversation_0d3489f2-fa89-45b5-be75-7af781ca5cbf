import { App } from 'vue'

import PageQuestion from '@/components/page-question/index.vue'
import PageCard from '@/components/page-card/index.vue'
import PageModal from '@/components/page-modal/index.vue'

const components = [PageQuestion, PageCard,PageModal]

const registerComponents = (app: App): void => {
  for (const cpn of components) {
    app.component(cpn.name, cpn)
  }
}

export default registerComponents
