import { App } from 'vue'
import registerComponents from './register-components'
import registerElement from './register-element'
import registerDirectives from './register-directives'
import registerBaseui from './register-baseui'

const globalRegister = (app: App): void => {
  app.use(registerElement)
  app.use(registerComponents)
  app.use(registerDirectives)
  app.use(registerBaseui)
}

export default globalRegister
