import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getAllMenu } from '@/api/common'
import type { MenuDto } from '@/types/menu'
import type { IDataType } from '@/service/request/types'
import { useUser } from '@/store/user'

export interface TopMenu {
  key: string
  name: string
  route: string
  icon: string,
  isSelected: boolean
}

export const useMenuStore = defineStore('menu', () => {
  // 菜单列表
  const menuList = ref<MenuDto[]>([])
  // 是否已加载菜单
  const isLoaded = ref(false)
  // 当前选中的菜单
  const activeMenu = ref<string>('')
  // 当前激活的顶级菜单的路径
  const activeTopMenuPath = ref<string>('')
  const currentRoutePath = ref<string>('')

  // 获取用户权限
  const userStore = useUser()
  const userPermissions = computed(() => userStore.getUserInfo()?.permissions || [])

  // 过滤有权限的菜单
  const filterMenusByPermission = (menus: MenuDto[]): MenuDto[] => {
    return menus.filter(menu => {
      // 检查当前菜单是否有权限
      const hasPermission = true
      
      // 如果有子菜单，递归过滤（创建新的对象，避免修改原始数据）
      if (menu.children && menu.children.length > 0) {
        const filteredChildren = filterMenusByPermission(menu.children)
        // 创建新的菜单对象，而不是修改原始对象
        const newMenu = { ...menu, children: filteredChildren }
        // 如果子菜单都被过滤掉了，且当前菜单也没有权限，则过滤掉当前菜单
        return (hasPermission && menu.isVisible && menu.isEnabled) || filteredChildren.length > 0
      }
      
      return hasPermission && menu.isVisible && menu.isEnabled
    }).map(menu => {
      // 确保返回的是新对象，避免修改原始数据
      if (menu.children && menu.children.length > 0) {
        return { ...menu, children: filterMenusByPermission(menu.children) }
      }
      return { ...menu }
    })
  }

  // 恢复 setCurrentRoutePath 方法
  const setCurrentRoutePath = (path: string) => {
    currentRoutePath.value = path
  }

  // 头部菜单列表
  const topMenus = computed<TopMenu[]>(() => {
    if (!Array.isArray(menuList.value)) return []
    // 过滤有权限的菜单（创建新数组，避免修改原始数据）
    const filteredMenus = filterMenusByPermission([...menuList.value])
    return filteredMenus.map(menu => ({
      key: String(menu.id),
      name: menu.name,
      route: menu.path,
      icon: menu.icon,
      isSelected: isMenuSelected(menu, currentRoutePath.value)
    }))
  })

  // 判断菜单是否被选中
  const isMenuSelected = (menu: MenuDto, currentPath: string): boolean => {
    if (!currentPath) return false
    
    // 递归查找当前路径是否在菜单的子菜单中
    const findInChildren = (menuItem: MenuDto): boolean => {
      if (menuItem.path.split('?')[0] === currentPath) return true
      if (menuItem.children) {
        return menuItem.children.some(child => findInChildren(child))
      }
      return false
    }
    
    return findInChildren(menu)
  }

  // 当前激活的侧边栏菜单列表
  const activeSideMenus = computed<MenuDto[]>(() => {
    if (!Array.isArray(menuList.value) || !currentRoutePath.value) return []
    
    // 递归查找当前路径所属的顶级菜单
    const findTopMenu = (menus: MenuDto[]): MenuDto | undefined => {
      for (const menu of menus) {
        // 先检查当前路径是否匹配顶级菜单或其子菜单
        if (isMenuSelected(menu, currentRoutePath.value)) {
          return menu
        }
      }
      return undefined
    }

    const topMenu = findTopMenu(menuList.value)
    if (!topMenu?.children) return []

    // 过滤有权限的子菜单（创建新对象，避免修改原始数据）
    const filteredChildren = filterMenusByPermission([...topMenu.children])

    // 设置子菜单的选中状态
    return filteredChildren.map(child => ({
      ...child,
      isSelected: Boolean(isMenuSelected(child, currentRoutePath.value)),
      children: child.children?.map(grandChild => ({
        ...grandChild,
        isSelected: Boolean(isMenuSelected(grandChild, currentRoutePath.value))
      }))
    }))
  })

  /**
   * 获取菜单数据
   */
  const fetchMenu = async () => {
    // if (isLoaded.value) return
    try {
      const response = await getAllMenu() as IDataType<MenuDto[]>
      // 确保response.data是数组
      if (response && response.code === 200 && Array.isArray(response.data)) {
        menuList.value = response.data
        isLoaded.value = true
      } else {
        menuList.value = []
        isLoaded.value = false
      }
    } catch (error) {
      menuList.value = []
      isLoaded.value = false
    }
  }

  const reloadMenu = async () => {
    isLoaded.value = false
    fetchMenu()
  }

  /**
   * 设置当前激活的顶级菜单路径
   */
  const setActiveTopMenuPath = (path: string) => {
    activeTopMenuPath.value = path
  }

  /**
   * 根据路径查找菜单 (用于面包屑或高亮)
   */
  const findMenuByPath = (path: string): MenuDto | undefined => {
    if (!Array.isArray(menuList.value)) return undefined
    const find = (menus: MenuDto[]): MenuDto | undefined => {
      for (const menu of menus) {
        if (menu.path === path) {
          return menu
        }
        if (menu.children) {
          const found = find(menu.children)
          if (found) {
            return found
          }
        }
      }
      return undefined
    }
    return find(menuList.value)
  }

  /**
   * 获取面包屑导航
   */
  const getBreadcrumbs = (path: string): MenuDto[] => {
    if (!Array.isArray(menuList.value)) return []
    const breadcrumbs: MenuDto[] = []
    const find = (menus: MenuDto[], targetPath: string): boolean => {
      for (const menu of menus) {
        if (menu.path === targetPath) {
          breadcrumbs.unshift(menu)
          return true
        }
        if (menu.children) {
          if (find(menu.children, targetPath)) {
            breadcrumbs.unshift(menu)
            return true
          }
        }
      }
      return false
    }
    find(menuList.value, path)
    return breadcrumbs
  }

  return {
    menuList,
    isLoaded,
    activeMenu,
    topMenus,
    activeSideMenus,
    activeTopMenuPath,
    fetchMenu,
    reloadMenu,
    setActiveTopMenuPath,
    findMenuByPath,
    getBreadcrumbs,
    setCurrentRoutePath,
    currentRoutePath
  }
})