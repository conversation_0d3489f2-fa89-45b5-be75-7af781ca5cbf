import{d as g,a as L,r as M,c as f,w as R,_ as C,b as r,o,e as p,f as y,g as c,h as m,i as q,j as v,t as E,k as d,n as k,l as D,F as w,m as B,u as P,p as F,q as N,s as V,M as j,v as z,x as A}from"./index-ecce2fce.js";import{N as U}from"./index-1c1b8d5e.js";const H=g({name:"MenuItem",components:{ArrowDown:L},props:{menu:{type:Object,required:!0},level:{type:Number,default:1}},emits:["select"],setup(e,{emit:a}){const t=M(!1),u=f(()=>e.menu.children&&e.menu.children.length>0),l=n=>{if(!n)return!1;for(const s of n)if(s.isSelected||s.children&&s.children.length>0&&l(s.children))return!0;return!1};return R(()=>e.menu,n=>{u.value&&l(n.children)&&(t.value=!0)},{immediate:!0,deep:!0}),{isExpanded:t,hasChildren:u,handleClick:()=>{u.value?t.value=!t.value:a("select",e.menu)}}}});const O={class:"menu-label"},W={key:0,class:"submenu"};function G(e,a,t,u,l,_){const n=r("el-icon"),s=r("arrow-down"),h=r("menu-item",!0);return o(),p("div",{class:k(["menu-item",{"is-selected":e.menu.isSelected}])},[y("div",{class:"menu-item-content",style:D({paddingLeft:`${e.level*16}px`}),onClick:a[0]||(a[0]=(...i)=>e.handleClick&&e.handleClick(...i))},[e.menu.icon?(o(),c(n,{key:0,class:"menu-icon"},{default:m(()=>[(o(),c(q(e.menu.icon)))]),_:1})):v("",!0),y("span",O,E(e.menu.name),1),e.hasChildren?(o(),c(n,{key:1,class:k(["arrow-icon",{"is-expanded":e.isExpanded}])},{default:m(()=>[d(s)]),_:1},8,["class"])):v("",!0)],4),e.hasChildren&&e.isExpanded?(o(),p("div",W,[(o(!0),p(w,null,B(e.menu.children,i=>(o(),c(h,{key:i.id,menu:i,level:e.level+1,onSelect:a[1]||(a[1]=b=>e.$emit("select",b))},null,8,["menu","level"]))),128))])):v("",!0)],2)}const J=C(H,[["render",G],["__scopeId","data-v-339af3b0"]]),K=g({name:"nav-menu",components:{MenuItem:J},props:{sideMenus:{type:Array,required:!0,default:()=>[]}},setup(e){const a=P(),t=F(),u=N(),l=f(()=>t.isLoaded?e.sideMenus:[]);return{route:u,menuList:l,handleSelect:n=>{if(n.path){const s=V();s?a.push({path:n.path,query:{scode:s}}):a.push(n.path),t.setCurrentRoutePath(n.path)}}}}});const Q={class:"nav-menu"},T={class:"menu-list"};function X(e,a,t,u,l,_){const n=r("menu-item");return o(),p("div",Q,[y("div",T,[(o(!0),p(w,null,B(e.menuList,s=>(o(),c(n,{key:s.id,menu:s,level:1,onSelect:e.handleSelect},null,8,["menu","onSelect"]))),128))])])}const Y=C(K,[["render",X],["__scopeId","data-v-c9db8f48"]]),Z=g({name:"layout",props:{},components:{NavMenu:Y,NavHeader:U,MobBreadcrumb:j},setup(){var h;const e=M(!1),a=N(),t=F();(h=z().getUserInfo())==null||h.permissions;const l=f(()=>t.topMenus),_=f(()=>t.activeSideMenus),n=i=>{e.value=!e.value},s=f(()=>t.getBreadcrumbs(a.path));return A(async()=>{t.isLoaded||await t.fetchMenu()}),{menuStore:t,isCollapse:e,topMenus:l,sideMenus:_,handleFoldChange:n,breadcrumbs:s}}});const x={class:"layout"},ee={key:1,class:"page-info"};function ne(e,a,t,u,l,_){const n=r("nav-header"),s=r("el-header"),h=r("nav-menu"),i=r("el-aside"),b=r("mob-breadcrumb"),$=r("router-view"),I=r("el-main"),S=r("el-container");return o(),p("div",x,[d(S,{class:"page"},{default:m(()=>[d(s,{class:"page-header"},{default:m(()=>[d(n,{"top-menus":e.topMenus,onFoldChange:e.handleFoldChange},null,8,["top-menus","onFoldChange"])]),_:1}),d(S,{class:"layout-content"},{default:m(()=>[e.menuStore.currentRoutePath!="/dashboard"?(o(),c(i,{key:0,width:"220px",style:{"padding-top":"5px","background-color":"#f0f2f5"}},{default:m(()=>[d(h,{collapse:e.isCollapse,"side-menus":e.sideMenus},null,8,["collapse","side-menus"])]),_:1})):v("",!0),d(I,{class:"page-content"},{default:m(()=>[e.menuStore.currentRoutePath!="/dashboard"?(o(),c(b,{key:0,breadcrumbs:e.breadcrumbs},null,8,["breadcrumbs"])):v("",!0),e.$route.meta.hideWhiteBg?(o(),c($,{key:2})):(o(),p("div",ee,[d($)]))]),_:1})]),_:1})]),_:1})])}const oe=C(Z,[["render",ne],["__scopeId","data-v-4b0e242c"]]);export{oe as default};
