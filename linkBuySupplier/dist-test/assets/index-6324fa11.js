import{_ as we,r as d,z as Ce,E as r,x as xe,b as v,B as $e,o as u,e as y,f as s,k as t,h as n,G as ze,F as D,m as R,g as h,C as S,j,R as q,n as X,D as Ue,t as I,K as Y,y as f,H as B,Z as Se,I as Ie,J as Be,a6 as Ge,P as ee}from"./index-ecce2fce.js";import{l as Me}from"./lodash-4bdee65f.js";import{a as Fe,g as De,c as Te,d as Ne,r as Ee,e as Ke,f as Ae,h as Le,i as Re,u as je,b as qe}from"./imageCenter-077d01e8.js";const Pe={class:"image-center-container"},He={class:"group-list"},Je={class:"group-name-actions"},Ze={class:"rename-popover-content"},Oe={class:"rename-popover-actions"},Qe={class:"group-name"},We={key:1,class:"group-name"},Xe={key:1,class:"group-name"},Ye={class:"group-popover-menu"},ea=["onClick"],aa=["onClick"],la={class:"image-list-wrapper"},ta={class:"image-list"},na={class:"image-list-toolbar"},oa={class:"images"},sa=["onClick","onMouseenter","onMouseleave"],ia=["src","alt"],ra={key:0,class:"image-mask"},ua=["title"],da={class:"rename-popover-content"},ca={class:"rename-popover-actions"},pa={class:"image-name-text"},ma={key:1,class:"image-name-text"},va={class:"pagination-bar"},fa={class:"pagination-actions"},_a={class:"pagination"},ga={__name:"index",setup(ya){const k=d([]),_=d("all"),b=d([]),c=Ce({page:1,pageSize:55,total:0}),G=d(!1),V=d([]),T=d(!1),w=d(!1),C=d(""),N=d(!1),P=d(""),x=d(""),$=d(""),M=d(""),H=Me.debounce(g,600),E=d(null),z=d(!1),U=d("");async function F(){const l=await De(),e=[{id:"all",name:"全部",hover:!1,moreVisible:!1,renameVisible:!1},{id:"ungroup",name:"未分组",hover:!1,moreVisible:!1,renameVisible:!1}];l&&l.data?(k.value=[...e,...l.data.map(o=>({...o,hover:!1,moreVisible:!1,renameVisible:!1}))],k.value.length>0&&(_.value=k.value[0].id)):(k.value=e,_.value="all")}async function g(){T.value=!0;const l={tagId:_.value,name:M.value,page:c.page,pageSize:c.pageSize};try{const e=await Fe(l);if(e&&e.data){const o=e.data;o.records?(b.value=o.records.map(p=>({...p,url:p.path,checked:!1,hover:!1,renameVisible:!1})),c.total=o.total,o.size&&o.size!==c.pageSize&&(c.pageSize=o.size),o.current&&o.current!==c.page&&(c.page=o.current)):(b.value=[],console.error("返回的数据格式不符合预期"))}}catch(e){console.error("获取图片列表失败:",e),r.error("获取图片列表失败"),b.value=[]}finally{T.value=!1,b.value.length===0&&(G.value=!1)}}xe(()=>{F(),g()});function ae(l){_.value=l,c.page=1,g()}function le(l){b.value.forEach(e=>e.checked=l),J()}function te(l){l.checked=!l.checked,J(),G.value=b.value.length>0&&b.value.every(e=>e.checked)}function J(){V.value=b.value.filter(l=>l.checked)}async function ne(){try{await q.confirm("确定要删除选中的图片吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const l=V.value.map(o=>o.id),e=await Te({ids:l});e&&e.data&&(r.success("已删除"),g())}catch{}}function oe(){z.value=!0,U.value=""}async function se(){if(!U.value){r.warning("请选择目标分组");return}const l=V.value.map(o=>o.id),e=await Ne({ids:l,targetTagId:U.value});e&&e.data&&(r.success("已移动"),g(),z.value=!1)}async function ie(l){$.value=l.name,l.renameVisible=!0,ee(()=>{const e=document.querySelector(".image-rename-popover .el-input__inner");e==null||e.focus()})}async function Z(l){if(!$.value.trim()){r.warning("请输入图片名称");return}const e=await Ee({id:l.id,name:$.value});e&&e.data&&(r.success("重命名成功"),l.renameVisible=!1,g())}async function re(l){try{await q.confirm("确定要删除该图片吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await Ke({id:l.id});e&&e.data&&(r.success("已删除"),g())}catch{}}function ue(l){P.value=l.url,N.value=!0}function de(l){c.pageSize=l,c.page=1,g()}async function ce(){if(!C.value.trim()){r.warning("请输入分组名称");return}const l=await Ae({name:C.value});l&&l.data&&(r.success("添加成功"),w.value=!1,C.value="",F())}async function O(l,e){if(l==="rename")x.value=e.name,e.renameVisible=!0,e.moreVisible=!1,ee(()=>{const o=document.querySelector(".group-rename-popover .el-input__inner");o==null||o.focus()});else if(l==="delete")try{await q.confirm("确定要删除该分组吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const o=await Le({id:e.id});o&&o.data&&(r.success("删除分组成功"),F())}catch{}}async function Q(l){if(!x.value.trim()){r.warning("请输入分组名称");return}const e=await Re({id:l.id,name:x.value});e&&e.data&&(r.success("重命名成功"),l.renameVisible=!1,F())}function pe(){H()}function me(){M.value="",H()}function ve(){E.value&&E.value.click()}async function fe(l){const e=l.target.files;if(!e||e.length===0)return;const o=Array.from(e);try{if(o.length===1){r.info("开始上传文件...");const p=o[0],m=await je(p,_.value!=="all"&&_.value!=="ungroup"?_.value:void 0,p.name);r.success("上传成功"),g()}else{r.info(`开始批量上传 ${o.length} 个文件...`);const p=await qe(o,_.value!=="all"&&_.value!=="ungroup"?_.value:void 0);r.success(`成功上传 ${p.data.length} 个文件`),g()}}catch(p){r.error(p.message||"上传失败"),console.error("上传错误:",p)}finally{l.target.value=""}}return(l,e)=>{const o=v("el-icon"),p=v("el-input"),m=v("el-button"),K=v("el-popover"),_e=v("el-menu-item"),ge=v("el-menu"),A=v("el-link"),L=v("el-tooltip"),ye=v("el-checkbox"),be=v("el-pagination"),W=v("el-dialog"),ke=v("el-option"),he=v("el-select"),Ve=$e("loading");return u(),y("div",Pe,[s("div",He,[t(ge,{"default-active":_.value,class:"group-menu",onSelect:ae},{default:n(()=>[(u(!0),y(D,null,R(k.value,a=>(u(),h(_e,{style:{height:"40px",position:"relative",padding:"0 0px"},key:a.id,index:a.id+"",onMouseenter:i=>a.hover=!0,onMouseleave:i=>a.hover=!1},{default:n(()=>[s("div",{class:X(["group-item-content",{active:_.value===a.id}])},[t(o,null,{default:n(()=>[t(S(Ue))]),_:1}),s("div",Je,[a.id!=="all"&&a.id!=="ungroup"?(u(),y(D,{key:0},[a.renameVisible?(u(),h(K,{key:0,placement:"bottom",visible:a.renameVisible,"onUpdate:visible":i=>a.renameVisible=i,teleported:!1,trigger:"manual","popper-class":"group-rename-popover"},{reference:n(()=>[s("span",Qe,I(a.name),1)]),default:n(()=>[s("div",Ze,[t(p,{modelValue:x.value,"onUpdate:modelValue":e[0]||(e[0]=i=>x.value=i),placeholder:"分类名称",maxlength:"20","show-word-limit":"",style:{"margin-bottom":"16px"},onKeyup:Y(i=>Q(a),["enter"])},null,8,["modelValue","onKeyup"]),s("div",Oe,[t(m,{link:"",style:{color:"var(--el-color-primary)"},onClick:i=>a.renameVisible=!1,class:"rename-cancel-btn"},{default:n(()=>e[14]||(e[14]=[f("取消")])),_:2,__:[14]},1032,["onClick"]),t(m,{type:"primary",size:"small",onClick:i=>Q(a)},{default:n(()=>e[15]||(e[15]=[f("确定")])),_:2,__:[15]},1032,["onClick"])])])]),_:2},1032,["visible","onUpdate:visible"])):(u(),y("span",We,I(a.name),1))],64)):(u(),y("span",Xe,I(a.name),1))]),a.id!=="all"&&a.id!=="ungroup"?(u(),h(K,{key:0,class:"group-popover",placement:"bottom-end",visible:a.moreVisible,"onUpdate:visible":i=>a.moreVisible=i,teleported:!1,"popper-class":"group-action-popover",trigger:"hover","show-after":0,"hide-after":0},{reference:n(()=>e[16]||(e[16]=[s("span",{class:"group-more"},"···",-1)])),default:n(()=>[s("div",Ye,[s("div",{class:"group-popover-item",onClick:B(i=>O("rename",a),["stop"])},"重命名",8,ea),s("div",{class:"group-popover-item danger",onClick:B(i=>O("delete",a),["stop"])},"删除分组",8,aa)])]),_:2},1032,["visible","onUpdate:visible"])):j("",!0)],2)]),_:2},1032,["index","onMouseenter","onMouseleave"]))),128))]),_:1},8,["default-active"]),t(m,{class:"add-group-btn",type:"primary",plain:"",onClick:e[1]||(e[1]=a=>w.value=!0)},{default:n(()=>e[17]||(e[17]=[f("添加分组")])),_:1,__:[17]})]),s("div",la,[s("div",ta,[s("div",na,[t(p,{modelValue:M.value,"onUpdate:modelValue":e[2]||(e[2]=a=>M.value=a),placeholder:"请输入图片名称",clearable:"",style:{width:"220px","margin-right":"16px"},onInput:pe,onClear:me},null,8,["modelValue"]),t(m,{type:"primary",onClick:ve},{default:n(()=>e[18]||(e[18]=[f("上传图片")])),_:1,__:[18]})]),ze((u(),y("div",oa,[(u(!0),y(D,null,R(b.value,a=>(u(),y("div",{key:a.id,class:"image-item"},[s("div",{class:"image-thumb-wrapper",onClick:i=>te(a),onMouseenter:i=>a.hover=!0,onMouseleave:i=>a.hover=!1,style:{cursor:"pointer"}},[s("img",{src:a.url,alt:a.name,class:"image-thumb"},null,8,ia),a.checked?(u(),y("div",ra,e[19]||(e[19]=[s("svg",{class:"check-icon",viewBox:"0 0 48 48"},[s("circle",{cx:"24",cy:"24",r:"22",fill:"none"}),s("polyline",{points:"14,26 22,34 34,18",fill:"none",stroke:"#fff","stroke-width":"4","stroke-linecap":"round","stroke-linejoin":"round"})],-1)]))):j("",!0),s("div",{class:X(["image-actions",{show:a.hover}])},[t(L,{content:"重命名",placement:"top",enterable:!1},{default:n(()=>[t(A,{type:"primary",onClick:B(i=>ie(a),["stop"])},{default:n(()=>[t(o,null,{default:n(()=>[t(S(Se))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024),t(L,{content:"查看",placement:"top",enterable:!1},{default:n(()=>[t(A,{type:"primary",onClick:B(i=>ue(a),["stop"])},{default:n(()=>[t(o,null,{default:n(()=>[t(S(Ie))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024),t(L,{content:"删除",placement:"top",enterable:!1},{default:n(()=>[t(A,{type:"danger",onClick:B(i=>re(a),["stop"])},{default:n(()=>[t(o,null,{default:n(()=>[t(S(Be))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)],2)],40,sa),s("div",{class:"image-name",title:a.name},[a.renameVisible?(u(),h(K,{key:0,placement:"bottom",visible:a.renameVisible,"onUpdate:visible":i=>a.renameVisible=i,teleported:!1,trigger:"manual","popper-class":"image-rename-popover"},{reference:n(()=>[s("span",pa,I(a.name),1)]),default:n(()=>[s("div",da,[t(p,{modelValue:$.value,"onUpdate:modelValue":e[3]||(e[3]=i=>$.value=i),placeholder:"图片名称",maxlength:"50","show-word-limit":"",style:{"margin-bottom":"16px"},onKeyup:Y(i=>Z(a),["enter"])},null,8,["modelValue","onKeyup"]),s("div",ca,[t(m,{link:"",style:{color:"var(--el-color-primary)"},onClick:i=>a.renameVisible=!1,class:"rename-cancel-btn"},{default:n(()=>e[20]||(e[20]=[f("取消")])),_:2,__:[20]},1032,["onClick"]),t(m,{type:"primary",size:"small",onClick:i=>Z(a)},{default:n(()=>e[21]||(e[21]=[f("确定")])),_:2,__:[21]},1032,["onClick"])])])]),_:2},1032,["visible","onUpdate:visible"])):(u(),y("span",ma,I(a.name),1))],8,ua)]))),128))])),[[Ve,T.value]])]),s("div",va,[s("div",fa,[t(ye,{modelValue:G.value,"onUpdate:modelValue":e[4]||(e[4]=a=>G.value=a),onChange:le},{default:n(()=>e[22]||(e[22]=[f("当页全选")])),_:1,__:[22]},8,["modelValue"]),t(m,{type:"danger",onClick:ne,disabled:!V.value.length},{default:n(()=>e[23]||(e[23]=[f("删除")])),_:1,__:[23]},8,["disabled"]),t(m,{type:"primary",onClick:oe,disabled:!V.value.length},{default:n(()=>e[24]||(e[24]=[f("移动")])),_:1,__:[24]},8,["disabled"])]),s("div",_a,[t(be,{"current-page":c.page,"onUpdate:currentPage":e[5]||(e[5]=a=>c.page=a),"page-size":c.pageSize,"onUpdate:pageSize":e[6]||(e[6]=a=>c.pageSize=a),total:c.total,"pager-count":7,background:"",onSizeChange:de,onCurrentChange:g},null,8,["current-page","page-size","total"])])])]),t(W,{modelValue:w.value,"onUpdate:modelValue":e[9]||(e[9]=a=>w.value=a),title:"添加分组",width:"300px"},{footer:n(()=>[t(m,{onClick:e[8]||(e[8]=a=>w.value=!1)},{default:n(()=>e[25]||(e[25]=[f("取消")])),_:1,__:[25]}),t(m,{type:"primary",onClick:ce},{default:n(()=>e[26]||(e[26]=[f("确定")])),_:1,__:[26]})]),default:n(()=>[t(p,{modelValue:C.value,"onUpdate:modelValue":e[7]||(e[7]=a=>C.value=a),placeholder:"请输入分组名称"},null,8,["modelValue"])]),_:1},8,["modelValue"]),N.value?(u(),h(S(Ge),{key:0,"url-list":[P.value],"z-index":3e3,onClose:e[10]||(e[10]=a=>N.value=!1)},null,8,["url-list"])):j("",!0),s("input",{type:"file",ref_key:"uploadInput",ref:E,style:{display:"none"},accept:"image/*",multiple:"",onChange:fe},null,544),t(W,{modelValue:z.value,"onUpdate:modelValue":e[13]||(e[13]=a=>z.value=a),title:"选择目标分组",width:"300px"},{footer:n(()=>[t(m,{onClick:e[12]||(e[12]=a=>z.value=!1)},{default:n(()=>e[27]||(e[27]=[f("取消")])),_:1,__:[27]}),t(m,{type:"primary",onClick:se},{default:n(()=>e[28]||(e[28]=[f("确定")])),_:1,__:[28]})]),default:n(()=>[t(he,{modelValue:U.value,"onUpdate:modelValue":e[11]||(e[11]=a=>U.value=a),placeholder:"请选择分组",style:{width:"100%"}},{default:n(()=>[(u(!0),y(D,null,R(k.value.filter(a=>a&&a.id&&a.id!=="all"&&a.id!=="ungroup"&&a.id!==void 0),a=>(u(),h(ke,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])])}}},Va=we(ga,[["__scopeId","data-v-0e56d12c"]]);export{Va as default};
