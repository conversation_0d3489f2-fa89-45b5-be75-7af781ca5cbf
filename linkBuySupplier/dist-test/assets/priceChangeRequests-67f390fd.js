import{k as ue,l as ce,m as pe}from"./product-cffcfc89.js";import{_ as _e,z as K,r as v,x as me,b as c,B as he,o as i,e as u,f as s,k as a,h as t,E as k,y as d,g,t as n,j as h,G as fe,n as L}from"./index-ecce2fce.js";const ve={class:"price-change-requests"},ge={class:"search-section"},we={class:"search-form-wrapper"},ye={class:"table-section"},be={class:"table-header"},ke={class:"table-title"},Ce={class:"table-wrapper"},Ne={class:"product-info"},De={class:"product-name"},Pe={key:0,class:"product-code"},Re={class:"time-info"},Se={class:"date"},Ve={key:0,class:"time-info"},ze={class:"date"},xe={key:1,class:"no-data"},qe={key:0,class:"reviewer-name"},Te={key:1,class:"no-data"},Ue={class:"reason-text"},Fe={class:"action-buttons"},We={class:"pagination-wrapper"},Be={key:0,class:"detail-content"},Ke={class:"card-header"},Le={class:"product-name-info"},$e={class:"sku-change-summary"},Ee={key:0,class:"change-item add"},je={key:1,class:"change-item update"},Ae={key:2,class:"change-item delete"},Me={key:0},Ge={key:1,class:"no-data"},Ie={key:0},He={key:1,class:"no-data"},Je={class:"reason-content"},Oe={class:"price-change-summary"},Qe={class:"review-comment"},Xe={class:"card-header"},Ye={class:"record-count"},Ze={class:"price-change-table"},et={class:"spec-combination"},tt={key:0,class:"price-change"},at={class:"old-price"},lt={key:1,class:"no-change"},st={key:0,class:"price-change"},ot={class:"old-price"},nt={key:1,class:"no-change"},rt={key:0,class:"price-change"},it={class:"old-price"},dt={key:1,class:"no-change"},ut={class:"withdraw-content"},ct={class:"dialog-footer"},pt={__name:"priceChangeRequests",setup(_t){const f=K({status:null,productName:""}),_=K({currentPage:1,pageSize:10,total:0}),P=v(!1),U=v(!1),$=v([]),S=v(!1),o=v(null),R=v(!1),V=v(null),w=K({withdrawReason:""}),E=v(null),Q={withdrawReason:[{required:!0,message:"请输入撤回原因",trigger:"blur"},{min:4,message:"撤回原因至少4个字符",trigger:"blur"}]},C=async()=>{try{P.value=!0;const r={page:_.currentPage,size:_.pageSize,status:f.status,productName:f.productName},l=await ue(r);l.code===200?($.value=l.data.records,_.total=l.data.total):k.error(l.msg||"获取申请列表失败")}catch(r){console.error("获取申请列表失败:",r),k.error("获取申请列表失败")}finally{P.value=!1}},X=()=>{_.currentPage=1,C()},Y=()=>{f.status=null,f.productName="",_.currentPage=1,C()},Z=r=>{_.pageSize=r,_.currentPage=1,C()},ee=r=>{_.currentPage=r,C()},j=async r=>{try{const l=await ce(r.id);l.code===200?(o.value=l.data,S.value=!0):k.error(l.msg||"获取申请详情失败")}catch(l){console.error("获取申请详情失败:",l),k.error("获取申请详情失败")}},te=()=>{S.value=!1,o.value=null},ae=r=>{V.value=r,w.withdrawReason="",R.value=!0},A=()=>{R.value=!1,V.value=null,w.withdrawReason=""},le=async()=>{try{await E.value.validate(),U.value=!0;const r=await pe(V.value.id,{withdrawReason:w.withdrawReason});r.code===200?(k.success("申请撤回成功"),R.value=!1,V.value=null,w.withdrawReason="",C()):k.error(r.msg||"撤回申请失败")}catch(r){console.error("撤回申请失败:",r),k.error("撤回申请失败")}finally{U.value=!1}},F=r=>{switch(r){case 1:return"warning";case 2:return"success";case 3:return"danger";case 4:return"info";default:return"info"}},M=r=>r?new Date(r).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}):"-",N=r=>r==null?"-":`¥${Number(r).toFixed(2)}`,W=(r,l)=>l==null?!1:r==null?Number(l)!==0:Number(r)!==Number(l),B=(r,l)=>{if(r===null||l===null)return"new-price";const y=Number(r),z=Number(l);return z>y?"price-increase":z<y?"price-decrease":"new-price"};return me(()=>{C()}),(r,l)=>{const y=c("el-option"),z=c("el-select"),x=c("el-form-item"),G=c("el-input"),D=c("el-button"),I=c("el-form"),q=c("el-card"),b=c("el-tag"),se=c("el-link"),p=c("el-table-column"),H=c("el-table"),oe=c("el-pagination"),T=c("el-text"),m=c("el-descriptions-item"),J=c("el-alert"),ne=c("el-descriptions"),re=c("el-drawer"),ie=c("el-dialog"),de=he("loading");return i(),u("div",ve,[s("div",ge,[a(q,{class:"search-card",shadow:"never"},{default:t(()=>[l[9]||(l[9]=s("div",{class:"search-header"},[s("div",{class:"search-title"},[s("h3",null,"价格修改申请"),s("p",{class:"search-description"},"查看和管理您提交的价格修改申请")])],-1)),s("div",we,[a(I,{model:f,inline:"",class:"search-form"},{default:t(()=>[a(x,{label:"申请状态"},{default:t(()=>[a(z,{modelValue:f.status,"onUpdate:modelValue":l[0]||(l[0]=e=>f.status=e),placeholder:"请选择状态",clearable:"",style:{width:"150px"}},{default:t(()=>[a(y,{label:"待审核",value:1}),a(y,{label:"审核通过",value:2}),a(y,{label:"审核拒绝",value:3}),a(y,{label:"已撤回",value:4})]),_:1},8,["modelValue"])]),_:1}),a(x,{label:"商品名称"},{default:t(()=>[a(G,{modelValue:f.productName,"onUpdate:modelValue":l[1]||(l[1]=e=>f.productName=e),placeholder:"请输入商品名称",style:{width:"200px"},clearable:""},null,8,["modelValue"])]),_:1}),a(x,null,{default:t(()=>[a(D,{type:"primary",onClick:X,loading:P.value},{default:t(()=>l[7]||(l[7]=[d(" 搜索 ")])),_:1,__:[7]},8,["loading"]),a(D,{onClick:Y},{default:t(()=>l[8]||(l[8]=[d(" 重置 ")])),_:1,__:[8]})]),_:1})]),_:1},8,["model"])])]),_:1,__:[9]})]),s("div",ye,[a(q,{class:"table-card",shadow:"never"},{default:t(()=>[s("div",be,[s("div",ke,[l[10]||(l[10]=s("span",null,"申请列表",-1)),_.total>0?(i(),g(b,{key:0,type:"info",class:"total-count"},{default:t(()=>[d(" 共 "+n(_.total)+" 条记录 ",1)]),_:1})):h("",!0)])]),s("div",Ce,[fe((i(),g(H,{data:$.value,loading:P.value,stripe:"",border:"",style:{width:"100%"},"header-cell-style":{background:"#fafafa",color:"#606266",fontWeight:"600"}},{default:t(()=>[a(p,{prop:"requestNo",label:"申请单号",width:"180",fixed:"left"},{default:t(({row:e})=>[a(se,{type:"primary",onClick:O=>j(e)},{default:t(()=>[d(n(e.requestNo),1)]),_:2},1032,["onClick"])]),_:1}),a(p,{prop:"requestTypeName",label:"申请类型",width:"100",align:"center"},{default:t(({row:e})=>[a(b,{size:"small",type:"info"},{default:t(()=>[d(n(e.requestTypeName),1)]),_:2},1024)]),_:1}),a(p,{prop:"productName",label:"商品名称","min-width":"200","show-overflow-tooltip":""},{default:t(({row:e})=>[s("div",Ne,[s("div",De,n(e.productName||"-"),1),e.productCode?(i(),u("div",Pe,n(e.productCode),1)):h("",!0)])]),_:1}),a(p,{prop:"skuCount",label:"SKU数量",width:"100",align:"center"}),a(p,{prop:"statusName",label:"状态",width:"120",align:"center"},{default:t(({row:e})=>[a(b,{type:F(e.status),size:"small"},{default:t(()=>[d(n(e.statusName),1)]),_:2},1032,["type"])]),_:1}),a(p,{prop:"submitTime",label:"提交时间",width:"160",align:"center"},{default:t(({row:e})=>[s("div",Re,[s("div",Se,n(e.submitTime),1)])]),_:1}),a(p,{prop:"reviewTime",label:"审核时间",width:"160",align:"center"},{default:t(({row:e})=>[e.reviewTime?(i(),u("div",Ve,[s("div",ze,n(e.reviewTime),1)])):(i(),u("span",xe,"-"))]),_:1}),a(p,{prop:"reviewerName",label:"审核人",width:"120",align:"center"},{default:t(({row:e})=>[e.reviewerName?(i(),u("span",qe,n(e.reviewerName),1)):(i(),u("span",Te,"-"))]),_:1}),a(p,{prop:"requestReason",label:"申请原因","min-width":"200","show-overflow-tooltip":""},{default:t(({row:e})=>[s("div",Ue,n(e.requestReason),1)]),_:1}),a(p,{label:"操作",width:"180",fixed:"right",align:"center"},{default:t(({row:e})=>[s("div",Fe,[a(D,{link:"",type:"primary",size:"small",onClick:O=>j(e)},{default:t(()=>l[11]||(l[11]=[d(" 查看详情 ")])),_:2,__:[11]},1032,["onClick"]),e.status===1?(i(),g(D,{key:0,link:"",type:"warning",size:"small",onClick:O=>ae(e)},{default:t(()=>l[12]||(l[12]=[d(" 撤回申请 ")])),_:2,__:[12]},1032,["onClick"])):h("",!0)])]),_:1})]),_:1},8,["data","loading"])),[[de,P.value]])]),s("div",We,[a(oe,{"current-page":_.currentPage,"onUpdate:currentPage":l[2]||(l[2]=e=>_.currentPage=e),"page-size":_.pageSize,"onUpdate:pageSize":l[3]||(l[3]=e=>_.pageSize=e),"page-sizes":[10,20,50,100],total:_.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Z,onCurrentChange:ee,background:""},null,8,["current-page","page-size","total"])])]),_:1})]),a(re,{modelValue:S.value,"onUpdate:modelValue":l[4]||(l[4]=e=>S.value=e),title:"价格修改申请详情",size:"75%","before-close":te,class:"detail-drawer"},{default:t(()=>[o.value?(i(),u("div",Be,[a(q,{class:"detail-card",shadow:"never"},{header:t(()=>[s("div",Ke,[l[13]||(l[13]=s("span",null,"申请信息",-1)),a(b,{type:F(o.value.status),class:"status-tag"},{default:t(()=>[d(n(o.value.statusName),1)]),_:1},8,["type"])])]),default:t(()=>[a(ne,{column:2,border:"",class:"detail-descriptions"},{default:t(()=>[a(m,{label:"申请单号"},{default:t(()=>[a(T,{type:"primary",class:"request-no"},{default:t(()=>[d(n(o.value.requestNo),1)]),_:1})]),_:1}),a(m,{label:"申请类型"},{default:t(()=>[a(b,{size:"small",type:"info"},{default:t(()=>[d(n(o.value.requestTypeName),1)]),_:1})]),_:1}),o.value.productDetails&&o.value.productDetails.length>0?(i(),g(m,{key:0,label:"商品名称"},{default:t(()=>[s("div",Le,[a(T,{type:"primary",class:"product-name"},{default:t(()=>[d(n(o.value.productDetails[0].productName),1)]),_:1})])]),_:1})):h("",!0),o.value.productDetails&&o.value.productDetails.length>0?(i(),g(m,{key:1,label:"商品编码"},{default:t(()=>[a(T,{type:"info",class:"product-code"},{default:t(()=>[d(n(o.value.productDetails[0].productCode||"待生成"),1)]),_:1})]),_:1})):h("",!0),o.value.productDetails&&o.value.productDetails.length>0?(i(),g(m,{key:2,label:"SKU变更"},{default:t(()=>[s("div",$e,[o.value.productDetails[0].skuAddCount>0?(i(),u("span",Ee," 新增"+n(o.value.productDetails[0].skuAddCount)+"个 ",1)):h("",!0),o.value.productDetails[0].skuUpdateCount>0?(i(),u("span",je," 修改"+n(o.value.productDetails[0].skuUpdateCount)+"个 ",1)):h("",!0),o.value.productDetails[0].skuDeleteCount>0?(i(),u("span",Ae," 删除"+n(o.value.productDetails[0].skuDeleteCount)+"个 ",1)):h("",!0)])]),_:1})):h("",!0),a(m,{label:"SKU数量"},{default:t(()=>[d(n(o.value.skuCount),1)]),_:1}),a(m,{label:"申请状态"},{default:t(()=>[a(b,{type:F(o.value.status)},{default:t(()=>[d(n(o.value.statusName),1)]),_:1},8,["type"])]),_:1}),a(m,{label:"提交时间"},{default:t(()=>[d(n(M(o.value.submitTime)),1)]),_:1}),a(m,{label:"审核时间"},{default:t(()=>[o.value.reviewTime?(i(),u("span",Me,n(M(o.value.reviewTime)),1)):(i(),u("span",Ge,"-"))]),_:1}),a(m,{label:"审核人"},{default:t(()=>[o.value.reviewerName?(i(),u("span",Ie,n(o.value.reviewerName),1)):(i(),u("span",He,"-"))]),_:1}),a(m,{label:"申请原因",span:2},{default:t(()=>[s("div",Je,n(o.value.requestReason),1)]),_:1}),o.value.productDetails&&o.value.productDetails[0].priceChangeSummary?(i(),g(m,{key:3,label:"价格变更摘要",span:2},{default:t(()=>[s("div",Oe,n(o.value.productDetails[0].priceChangeSummary),1)]),_:1})):h("",!0),o.value.reviewComment?(i(),g(m,{key:4,label:"审核意见",span:2},{default:t(()=>[s("div",Qe,[a(J,{type:o.value.status===2?"success":"error",closable:!1},{default:t(()=>[d(n(o.value.reviewComment),1)]),_:1},8,["type"])])]),_:1})):h("",!0)]),_:1})]),_:1}),a(q,{class:"detail-card",shadow:"never"},{header:t(()=>{var e;return[s("div",Xe,[l[14]||(l[14]=s("span",null,"价格变更明细",-1)),s("span",Ye,"共 "+n(((e=o.value.skuDetails)==null?void 0:e.length)||0)+" 条变更记录",1)])]}),default:t(()=>[s("div",Ze,[a(H,{data:o.value.skuDetails,border:"",stripe:"","header-cell-style":{background:"#f5f7fa",color:"#606266",fontWeight:"600"}},{default:t(()=>[a(p,{prop:"specCombination",label:"规格组合",width:"150","show-overflow-tooltip":""},{default:t(({row:e})=>[s("div",et,n(e.specCombination||"-"),1)]),_:1}),a(p,{prop:"skuCode",label:"SKU编码",width:"150",fixed:"left"},{default:t(({row:e})=>[a(T,{type:"primary",class:"sku-code"},{default:t(()=>[d(n(e.skuCode||"待生成"),1)]),_:2},1024)]),_:1}),a(p,{prop:"operationTypeName",label:"操作类型",width:"100",align:"center"},{default:t(({row:e})=>[a(b,{size:"small",type:"info"},{default:t(()=>[d(n(e.operationTypeName),1)]),_:2},1024)]),_:1}),a(p,{label:"成本价变更",width:"200",align:"center"},{default:t(({row:e})=>[W(e.oldCostPrice,e.newCostPrice)?(i(),u("div",tt,[s("span",at,n(N(e.oldCostPrice)),1),l[15]||(l[15]=s("span",{class:"arrow-icon"},"→",-1)),s("span",{class:L(B(e.oldCostPrice,e.newCostPrice))},n(N(e.newCostPrice)),3)])):(i(),u("span",lt,"无变更"))]),_:1}),a(p,{label:"采购价变更",width:"200",align:"center"},{default:t(({row:e})=>[W(e.oldPurchasePrice,e.newPurchasePrice)?(i(),u("div",st,[s("span",ot,n(N(e.oldPurchasePrice)),1),l[16]||(l[16]=s("span",{class:"arrow-icon"},"→",-1)),s("span",{class:L(B(e.oldPurchasePrice,e.newPurchasePrice))},n(N(e.newPurchasePrice)),3)])):(i(),u("span",nt,"无变更"))]),_:1}),a(p,{label:"划线价变更",width:"200",align:"center"},{default:t(({row:e})=>[W(e.oldStrikethroughPrice,e.newStrikethroughPrice)?(i(),u("div",rt,[s("span",it,n(N(e.oldStrikethroughPrice)),1),l[17]||(l[17]=s("span",{class:"arrow-icon"},"→",-1)),s("span",{class:L(B(e.oldStrikethroughPrice,e.newStrikethroughPrice))},n(N(e.newStrikethroughPrice)),3)])):(i(),u("span",dt,"无变更"))]),_:1})]),_:1},8,["data"])])]),_:1})])):h("",!0)]),_:1},8,["modelValue"]),a(ie,{modelValue:R.value,"onUpdate:modelValue":l[6]||(l[6]=e=>R.value=e),title:"撤回申请",width:"500px","before-close":A,class:"withdraw-dialog"},{footer:t(()=>[s("div",ct,[a(D,{onClick:A},{default:t(()=>l[19]||(l[19]=[d("取消")])),_:1,__:[19]}),a(D,{type:"warning",onClick:le,loading:U.value},{default:t(()=>l[20]||(l[20]=[d(" 确定撤回 ")])),_:1,__:[20]},8,["loading"])])]),default:t(()=>[s("div",ut,[a(J,{type:"warning",closable:!1,class:"withdraw-alert"},{title:t(()=>l[18]||(l[18]=[s("span",null,"确定要撤回此价格修改申请吗？",-1)])),_:1}),a(I,{ref_key:"withdrawFormRef",ref:E,model:w,rules:Q,"label-width":"100px",class:"withdraw-form"},{default:t(()=>[a(x,{label:"撤回原因",prop:"withdrawReason"},{default:t(()=>[a(G,{modelValue:w.withdrawReason,"onUpdate:modelValue":l[5]||(l[5]=e=>w.withdrawReason=e),type:"textarea",rows:4,placeholder:"请详细说明撤回原因，以便后续处理...",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])])]),_:1},8,["modelValue"])])}}},ft=_e(pt,[["__scopeId","data-v-5b7152f4"]]);export{ft as default};
