import{$ as e}from"./index-ecce2fce.js";function c(t){return e.get({url:"/product/list",params:t})}function n(t){return e.get({url:`/product/${t}`})}function s(t){return e.post({url:"/product/add",data:t})}function i(t,r){return e.put({url:`/product/${t}`,data:r})}function o(t){return e.delete({url:`/product/${t}`})}function a(t){return e.delete({url:"/product/batch",data:t})}function d(){return e.get({url:"/product/statistics"})}function p(t,r){return e.post({url:`/product/${t}/submit-review`,data:r})}function g(t){return e.post({url:"/product/batch/submit-review",data:t})}function l(t,r){return e.post({url:`/product/${t}/withdraw-review`,data:r})}function h(t){return e.get({url:`/product/${t}/review-logs`})}function f(t){return e.post({url:"/product/submit-price-change-request",data:t})}function w(t){return e.get({url:"/product/price-change-requests",params:t})}function P(t){return e.get({url:`/product/price-change-requests/${t}`})}function b(t,r){return e.post({url:`/product/price-change-requests/${t}/withdraw`,data:r})}function m(t){return e.get({url:"/product/check-pending-price-requests",params:{spuIds:t.join(",")}})}export{d as a,a as b,m as c,g as d,n as e,o as f,c as g,p as h,s as i,h as j,w as k,P as l,b as m,f as s,i as u,l as w};
