import{d as G,r as w,b as V,o as u,e as c,f as l,F as W,m as Y,n as X,k as e,h as i,t as E,g as q,i as De,l as Ue,C as $,X as Se,y as C,j as Q,_ as J,Y as te,Z as Ae,J as ae,O as oe,a0 as ie,w as Z,c as ze,a1 as Re,a2 as xe,a3 as We,E as x,z as le,x as Be,B as Qe,K as Ee,G as Ie,a4 as je,a5 as Ke,R as Me}from"./index-ecce2fce.js";import{l as Oe}from"./lodash-4bdee65f.js";import{g as Le,a as Pe,d as qe,u as Ge,c as Je}from"./freightTemplate-a7b50573.js";const Xe={class:"valuation-type-selector"},Ye={class:"valuation-options"},Ze=["onClick"],He={class:"option-header"},et={class:"option-title"},tt={class:"option-desc"},lt={class:"option-example"},at={key:0,class:"form-tip"},ot=G({__name:"ValuationTypeSelector",props:{modelValue:{},disabled:{type:Boolean,default:!1}},emits:["update:modelValue","change"],setup(K,{emit:I}){const U=K,k=I,v=w([{value:1,label:"件数计费",desc:"按商品件数计算运费",example:"例：首件1件8元，续件1件2元",icon:"Box",color:"#409eff"},{value:2,label:"重量计费",desc:"按商品重量计算运费",example:"例：首重1kg 10元，续重1kg 5元",icon:"Goods",color:"#67c23a"},{value:3,label:"体积计费",desc:"按商品体积计算运费",example:"例：首体积0.01m³ 12元，续体积0.01m³ 6元",icon:"Box",color:"#e6a23c"},{value:4,label:"满额包邮",desc:"满足金额条件免运费",example:"例：满100元包邮，不满收8元运费",icon:"Present",color:"#f56c6c"}]),N=h=>{U.disabled||(k("update:modelValue",h),k("change",h))};return(h,S)=>{const m=V("el-radio"),y=V("el-icon");return u(),c("div",Xe,[l("div",Ye,[(u(!0),c(W,null,Y(v.value,n=>(u(),c("div",{key:n.value,class:X(["valuation-option",{active:h.modelValue===n.value,disabled:h.disabled}]),onClick:o=>!h.disabled&&N(n.value)},[l("div",He,[e(m,{"model-value":h.modelValue,value:n.value,disabled:h.disabled,class:"option-radio",onChange:N},{default:i(()=>[l("span",et,E(n.label),1)]),_:2},1032,["model-value","value","disabled"]),e(y,{class:"option-icon",style:Ue({color:n.color})},{default:i(()=>[(u(),q(De(n.icon)))]),_:2},1032,["style"])]),l("div",tt,E(n.desc),1),l("div",lt,E(n.example),1)],10,Ze))),128))]),h.disabled?(u(),c("div",at,[e(y,null,{default:i(()=>[e($(Se))]),_:1}),S[0]||(S[0]=C(" 编辑模式下计费方式不可更改 "))])):Q("",!0)])}}});const it=J(ot,[["__scopeId","data-v-4b3db2ee"]]),nt={class:"freight-area-config"},st={class:"area-config"},dt={key:0,class:"header-cell"},rt={key:1,class:"header-cell"},ut={key:2,class:"header-cell"},mt={key:3,class:"header-cell"},pt={key:4,class:"header-cell"},ct={key:5,class:"header-cell"},ft={key:6,class:"header-cell"},gt={key:7,class:"header-cell"},vt={key:8,class:"header-cell"},ht={key:9,class:"header-cell"},_t={class:"area-text"},yt={class:"area-input"},Vt={class:"area-input"},bt={class:"area-input"},Ct={class:"area-input"},Ft={class:"area-input"},kt={class:"area-input"},wt={class:"area-input"},Tt={class:"area-input"},$t={class:"area-input"},Nt={class:"area-input"},Dt={class:"area-input"},Ut={class:"area-input"},St={class:"area-input"},At={class:"area-input"},zt={class:"area-actions"},Rt={class:"add-area-section"},xt={class:"area-tip"},Wt={class:"tip-content"},Bt={class:"tip-text"},Qt={key:0},Et={key:1},It={key:2},jt={key:3},Kt=G({__name:"FreightAreaConfig",props:{areas:{},valuationType:{}},emits:["update:areas","edit-area","remove-area","add-area"],setup(K,{emit:I}){const U=K,k=I,v=()=>{k("update:areas",U.areas)},N=(m,y)=>{k("edit-area",m,y)},h=m=>{k("remove-area",m)},S=()=>{k("add-area")};return(m,y)=>{const n=V("el-icon"),o=V("el-tag"),s=V("el-input-number"),b=V("el-button");return u(),c("div",nt,[l("div",st,[l("div",{class:X(["area-header",{"freight-only":m.valuationType===4}])},[y[0]||(y[0]=l("div",{class:"header-cell area-name-header"},"配送区域",-1)),m.valuationType===1?(u(),c("div",dt,"首件 (件)")):m.valuationType===2?(u(),c("div",rt,"首重 (Kg)")):m.valuationType===3?(u(),c("div",ut,"首体积 (m³)")):m.valuationType===4?(u(),c("div",mt,"包邮金额 (元)")):Q("",!0),m.valuationType!==4?(u(),c("div",pt,"运费 (元)")):Q("",!0),m.valuationType===1?(u(),c("div",ct,"续件 (件)")):m.valuationType===2?(u(),c("div",ft,"续重 (Kg)")):m.valuationType===3?(u(),c("div",gt,"续体积 (m³)")):m.valuationType===4?(u(),c("div",vt,"未包邮运费 (元)")):Q("",!0),m.valuationType!==4?(u(),c("div",ht,"续费 (元)")):Q("",!0),y[1]||(y[1]=l("div",{class:"header-cell actions-header"},"操作",-1))],2),(u(!0),c(W,null,Y(m.areas,(a,p)=>(u(),c("div",{key:p,class:X(["area-row",{"freight-only":m.valuationType===4}])},[l("div",{class:X(["area-name",{"default-area":a.isDefault}])},[e(n,{class:"area-icon"},{default:i(()=>[e($(te))]),_:1}),l("span",_t,E(a.areaName),1),a.isDefault?(u(),q(o,{key:0,size:"small",type:"info",class:"default-tag"},{default:i(()=>y[2]||(y[2]=[C("默认")])),_:1,__:[2]})):Q("",!0)],2),m.valuationType===1?(u(),c(W,{key:0},[l("div",yt,[e(s,{modelValue:a.firstNum,"onUpdate:modelValue":r=>a.firstNum=r,min:1,placeholder:"首件",size:"default","controls-position":"right",onChange:v},null,8,["modelValue","onUpdate:modelValue"])]),l("div",Vt,[e(s,{modelValue:a.firstFreight,"onUpdate:modelValue":r=>a.firstFreight=r,min:0,precision:2,placeholder:"运费",size:"default","controls-position":"right",onChange:v},null,8,["modelValue","onUpdate:modelValue"])]),l("div",bt,[e(s,{modelValue:a.additionalNum,"onUpdate:modelValue":r=>a.additionalNum=r,min:1,placeholder:"续件",size:"default","controls-position":"right",onChange:v},null,8,["modelValue","onUpdate:modelValue"])]),l("div",Ct,[e(s,{modelValue:a.additionalFreight,"onUpdate:modelValue":r=>a.additionalFreight=r,min:0,precision:2,placeholder:"续费",size:"default","controls-position":"right",onChange:v},null,8,["modelValue","onUpdate:modelValue"])])],64)):m.valuationType===2?(u(),c(W,{key:1},[l("div",Ft,[e(s,{modelValue:a.firstWeight,"onUpdate:modelValue":r=>a.firstWeight=r,min:.1,precision:1,placeholder:"首重",size:"default","controls-position":"right",onChange:v},null,8,["modelValue","onUpdate:modelValue"])]),l("div",kt,[e(s,{modelValue:a.firstFreight,"onUpdate:modelValue":r=>a.firstFreight=r,min:0,precision:2,placeholder:"运费",size:"default","controls-position":"right",onChange:v},null,8,["modelValue","onUpdate:modelValue"])]),l("div",wt,[e(s,{modelValue:a.additionalWeight,"onUpdate:modelValue":r=>a.additionalWeight=r,min:.1,precision:1,placeholder:"续重",size:"default","controls-position":"right",onChange:v},null,8,["modelValue","onUpdate:modelValue"])]),l("div",Tt,[e(s,{modelValue:a.additionalFreight,"onUpdate:modelValue":r=>a.additionalFreight=r,min:0,precision:2,placeholder:"续费",size:"default","controls-position":"right",onChange:v},null,8,["modelValue","onUpdate:modelValue"])])],64)):m.valuationType===3?(u(),c(W,{key:2},[l("div",$t,[e(s,{modelValue:a.firstVolume,"onUpdate:modelValue":r=>a.firstVolume=r,min:.01,precision:2,placeholder:"首体积",size:"default","controls-position":"right",onChange:v},null,8,["modelValue","onUpdate:modelValue"])]),l("div",Nt,[e(s,{modelValue:a.firstFreight,"onUpdate:modelValue":r=>a.firstFreight=r,min:0,precision:2,placeholder:"运费",size:"default","controls-position":"right",onChange:v},null,8,["modelValue","onUpdate:modelValue"])]),l("div",Dt,[e(s,{modelValue:a.additionalVolume,"onUpdate:modelValue":r=>a.additionalVolume=r,min:.01,precision:2,placeholder:"续体积",size:"default","controls-position":"right",onChange:v},null,8,["modelValue","onUpdate:modelValue"])]),l("div",Ut,[e(s,{modelValue:a.additionalFreight,"onUpdate:modelValue":r=>a.additionalFreight=r,min:0,precision:2,placeholder:"续费",size:"default","controls-position":"right",onChange:v},null,8,["modelValue","onUpdate:modelValue"])])],64)):m.valuationType===4?(u(),c(W,{key:3},[l("div",St,[e(s,{modelValue:a.freeShippingAmount,"onUpdate:modelValue":r=>a.freeShippingAmount=r,min:0,precision:2,placeholder:"包邮金额",size:"default","controls-position":"right",onChange:v},null,8,["modelValue","onUpdate:modelValue"])]),l("div",At,[e(s,{modelValue:a.unfreeShippingFreight,"onUpdate:modelValue":r=>a.unfreeShippingFreight=r,min:0,precision:2,placeholder:"运费",size:"default","controls-position":"right",onChange:v},null,8,["modelValue","onUpdate:modelValue"])])],64)):Q("",!0),l("div",zt,[e(b,{link:"",size:"small",style:{color:"var(--el-color-primary)"},onClick:r=>N(a,p)},{default:i(()=>[e(n,null,{default:i(()=>[e($(Ae))]),_:1}),y[3]||(y[3]=C(" 编辑 "))]),_:2,__:[3]},1032,["onClick"]),a.isDefault?Q("",!0):(u(),q(b,{key:0,link:"",type:"danger",size:"small",onClick:r=>h(p)},{default:i(()=>[e(n,null,{default:i(()=>[e($(ae))]),_:1}),y[4]||(y[4]=C(" 删除 "))]),_:2,__:[4]},1032,["onClick"]))])],2))),128))]),l("div",Rt,[e(b,{type:"primary",onClick:S,class:"add-area-btn",size:"large"},{default:i(()=>[e(n,null,{default:i(()=>[e($(oe))]),_:1}),y[5]||(y[5]=C(" 指定配送区域和运费 "))]),_:1,__:[5]})]),l("div",xt,[e(n,{class:"tip-icon"},{default:i(()=>[e($(ie))]),_:1}),l("div",Wt,[y[6]||(y[6]=l("div",{class:"tip-title"},"计费说明：",-1)),l("div",Bt,[m.valuationType===1?(u(),c("span",Qt," 根据商品件数计算运费。当购买数量不足首件数量时，按首件费用计算；超过部分按续件数量和续件费用计算。 ")):m.valuationType===2?(u(),c("span",Et," 根据商品重量计算运费。当购买重量不足首重时，按首重费用计算；超过部分按续重和续重费用计算。 ")):m.valuationType===3?(u(),c("span",It," 根据商品体积计算运费。当购买体积不足首体积时，按首体积费用计算；超过部分按续体积和续体积费用计算。 ")):m.valuationType===4?(u(),c("span",jt," 当订单金额达到包邮金额时免运费，否则按照未包邮运费收取。 ")):Q("",!0)])])])])}}});const Mt=J(Kt,[["__scopeId","data-v-c5cc399c"]]),Ot={key:0,class:"default-area-display"},Lt={class:"selected-area-text"},Pt={key:1,style:{display:"flex",gap:"12px","align-items":"center",width:"100%"}},qt={class:"area-display",style:{flex:"1","min-width":"0"}},Gt={key:0,class:"selected-area-text"},Jt={key:1,class:"placeholder-text"},Xt={class:"input-with-unit"},Yt={class:"input-with-unit"},Zt={class:"input-with-unit"},Ht={class:"input-with-unit"},el={class:"input-with-unit"},tl={class:"input-with-unit"},ll={class:"input-with-unit"},al={class:"input-with-unit"},ol={class:"input-with-unit"},il={class:"input-with-unit"},nl={class:"input-with-unit"},sl={class:"input-with-unit"},dl={class:"input-with-unit"},rl={class:"input-with-unit"},ul={class:"dialog-footer"},ml=G({__name:"AreaConfigDialog",props:{modelValue:{type:Boolean},valuationType:{},formData:{}},emits:["update:modelValue","submit","open-region-selector"],setup(K,{emit:I}){const U=K,k=I,v=w(!1),N=w(),h={areaName:[{required:!0,message:"请输入区域名称",trigger:"blur"}]};Z(()=>U.modelValue,n=>{v.value=n}),Z(v,n=>{k("update:modelValue",n)});const S=()=>{v.value=!1},m=async()=>{try{await N.value.validate(),k("submit",U.formData)}catch(n){console.error("表单验证失败:",n)}},y=()=>{k("open-region-selector")};return(n,o)=>{const s=V("el-tag"),b=V("el-button"),a=V("el-form-item"),p=V("el-input-number"),r=V("el-form"),B=V("el-dialog");return u(),q(B,{modelValue:v.value,"onUpdate:modelValue":o[14]||(o[14]=f=>v.value=f),title:"配置配送区域",width:"800px","close-on-click-modal":!1,"align-center":"",onClose:S},{footer:i(()=>[l("div",ul,[e(b,{onClick:S},{default:i(()=>o[31]||(o[31]=[C("取消")])),_:1,__:[31]}),e(b,{type:"primary",onClick:m},{default:i(()=>o[32]||(o[32]=[C("保存")])),_:1,__:[32]})])]),default:i(()=>[e(r,{model:n.formData,rules:h,ref_key:"formRef",ref:N,"label-width":"120px"},{default:i(()=>[e(a,{label:"区域名称",prop:"areaName",class:"area-name-form-item"},{default:i(()=>[n.formData.isDefault?(u(),c("div",Ot,[l("div",Lt,E(n.formData.areaName),1),e(s,{size:"small",type:"info"},{default:i(()=>o[15]||(o[15]=[C("默认区域，不可修改")])),_:1,__:[15]})])):(u(),c("div",Pt,[l("div",qt,[n.formData.areaName?(u(),c("div",Gt,E(n.formData.areaName),1)):(u(),c("div",Jt," 请选择配送区域 "))]),e(b,{type:"primary",onClick:y},{default:i(()=>o[16]||(o[16]=[C("选择区域")])),_:1,__:[16]})]))]),_:1}),n.valuationType===1?(u(),c(W,{key:0},[e(a,{label:"首件数量",prop:"firstNum",class:"number-form-item"},{default:i(()=>[l("div",Xt,[e(p,{modelValue:n.formData.firstNum,"onUpdate:modelValue":o[0]||(o[0]=f=>n.formData.firstNum=f),min:1,"controls-position":"right",style:{width:"150px"}},null,8,["modelValue"]),o[17]||(o[17]=l("span",{class:"unit-text"},"件",-1))])]),_:1}),e(a,{label:"首件运费",prop:"firstFreight",class:"number-form-item"},{default:i(()=>[l("div",Yt,[e(p,{modelValue:n.formData.firstFreight,"onUpdate:modelValue":o[1]||(o[1]=f=>n.formData.firstFreight=f),min:0,precision:2,"controls-position":"right",style:{width:"150px"}},null,8,["modelValue"]),o[18]||(o[18]=l("span",{class:"unit-text"},"元",-1))])]),_:1}),e(a,{label:"续件数量",prop:"additionalNum",class:"number-form-item"},{default:i(()=>[l("div",Zt,[e(p,{modelValue:n.formData.additionalNum,"onUpdate:modelValue":o[2]||(o[2]=f=>n.formData.additionalNum=f),min:1,"controls-position":"right",style:{width:"150px"}},null,8,["modelValue"]),o[19]||(o[19]=l("span",{class:"unit-text"},"件",-1))])]),_:1}),e(a,{label:"续件运费",prop:"additionalFreight",class:"number-form-item"},{default:i(()=>[l("div",Ht,[e(p,{modelValue:n.formData.additionalFreight,"onUpdate:modelValue":o[3]||(o[3]=f=>n.formData.additionalFreight=f),min:0,precision:2,"controls-position":"right",style:{width:"150px"}},null,8,["modelValue"]),o[20]||(o[20]=l("span",{class:"unit-text"},"元",-1))])]),_:1})],64)):n.valuationType===2?(u(),c(W,{key:1},[e(a,{label:"首重",prop:"firstWeight",class:"number-form-item"},{default:i(()=>[l("div",el,[e(p,{modelValue:n.formData.firstWeight,"onUpdate:modelValue":o[4]||(o[4]=f=>n.formData.firstWeight=f),min:.1,precision:1,"controls-position":"right",style:{width:"150px"}},null,8,["modelValue"]),o[21]||(o[21]=l("span",{class:"unit-text"},"Kg",-1))])]),_:1}),e(a,{label:"首重运费",prop:"firstFreight",class:"number-form-item"},{default:i(()=>[l("div",tl,[e(p,{modelValue:n.formData.firstFreight,"onUpdate:modelValue":o[5]||(o[5]=f=>n.formData.firstFreight=f),min:0,precision:2,"controls-position":"right",style:{width:"150px"}},null,8,["modelValue"]),o[22]||(o[22]=l("span",{class:"unit-text"},"元",-1))])]),_:1}),e(a,{label:"续重",prop:"additionalWeight",class:"number-form-item"},{default:i(()=>[l("div",ll,[e(p,{modelValue:n.formData.additionalWeight,"onUpdate:modelValue":o[6]||(o[6]=f=>n.formData.additionalWeight=f),min:.1,precision:1,"controls-position":"right",style:{width:"150px"}},null,8,["modelValue"]),o[23]||(o[23]=l("span",{class:"unit-text"},"Kg",-1))])]),_:1}),e(a,{label:"续重运费",prop:"additionalFreight",class:"number-form-item"},{default:i(()=>[l("div",al,[e(p,{modelValue:n.formData.additionalFreight,"onUpdate:modelValue":o[7]||(o[7]=f=>n.formData.additionalFreight=f),min:0,precision:2,"controls-position":"right",style:{width:"150px"}},null,8,["modelValue"]),o[24]||(o[24]=l("span",{class:"unit-text"},"元",-1))])]),_:1})],64)):n.valuationType===3?(u(),c(W,{key:2},[e(a,{label:"首体积",prop:"firstVolume",class:"number-form-item"},{default:i(()=>[l("div",ol,[e(p,{modelValue:n.formData.firstVolume,"onUpdate:modelValue":o[8]||(o[8]=f=>n.formData.firstVolume=f),min:.01,precision:2,"controls-position":"right",style:{width:"150px"}},null,8,["modelValue"]),o[25]||(o[25]=l("span",{class:"unit-text"},"m³",-1))])]),_:1}),e(a,{label:"首体积运费",prop:"firstFreight",class:"number-form-item"},{default:i(()=>[l("div",il,[e(p,{modelValue:n.formData.firstFreight,"onUpdate:modelValue":o[9]||(o[9]=f=>n.formData.firstFreight=f),min:0,precision:2,"controls-position":"right",style:{width:"150px"}},null,8,["modelValue"]),o[26]||(o[26]=l("span",{class:"unit-text"},"元",-1))])]),_:1}),e(a,{label:"续体积",prop:"additionalVolume",class:"number-form-item"},{default:i(()=>[l("div",nl,[e(p,{modelValue:n.formData.additionalVolume,"onUpdate:modelValue":o[10]||(o[10]=f=>n.formData.additionalVolume=f),min:.01,precision:2,"controls-position":"right",style:{width:"150px"}},null,8,["modelValue"]),o[27]||(o[27]=l("span",{class:"unit-text"},"m³",-1))])]),_:1}),e(a,{label:"续体积运费",prop:"additionalFreight",class:"number-form-item"},{default:i(()=>[l("div",sl,[e(p,{modelValue:n.formData.additionalFreight,"onUpdate:modelValue":o[11]||(o[11]=f=>n.formData.additionalFreight=f),min:0,precision:2,"controls-position":"right",style:{width:"150px"}},null,8,["modelValue"]),o[28]||(o[28]=l("span",{class:"unit-text"},"元",-1))])]),_:1})],64)):n.valuationType===4?(u(),c(W,{key:3},[e(a,{label:"包邮金额",prop:"freeShippingAmount",class:"number-form-item"},{default:i(()=>[l("div",dl,[e(p,{modelValue:n.formData.freeShippingAmount,"onUpdate:modelValue":o[12]||(o[12]=f=>n.formData.freeShippingAmount=f),min:0,precision:2,"controls-position":"right",style:{width:"150px"}},null,8,["modelValue"]),o[29]||(o[29]=l("span",{class:"unit-text"},"元",-1))])]),_:1}),e(a,{label:"不包邮运费",prop:"unfreeShippingFreight",class:"number-form-item"},{default:i(()=>[l("div",rl,[e(p,{modelValue:n.formData.unfreeShippingFreight,"onUpdate:modelValue":o[13]||(o[13]=f=>n.formData.unfreeShippingFreight=f),min:0,precision:2,"controls-position":"right",style:{width:"150px"}},null,8,["modelValue"]),o[30]||(o[30]=l("span",{class:"unit-text"},"元",-1))])]),_:1})],64)):Q("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue"])}}});const pl=J(ml,[["__scopeId","data-v-cf063453"]]),cl={class:"region-selector"},fl={class:"region-content"},gl={class:"region-left"},vl={class:"region-title"},hl={class:"region-list"},_l=["onClick"],yl={class:"region-name"},Vl={class:"region-right"},bl={class:"region-title"},Cl={class:"selected-regions"},Fl={key:0,class:"empty-state"},kl={class:"region-name"},wl={class:"dialog-footer"},Tl=G({__name:"RegionSelector",props:{modelValue:{type:Boolean},allRegions:{},selectedRegions:{},excludeRegionCodes:{default:()=>[]}},emits:["update:modelValue","update:selectedRegions","confirm"],setup(K,{emit:I}){const U=K,k=I,v=w(!1),N=w(""),h=w([]),S=ze(()=>{const b=h.value.map(r=>r.code),a=U.excludeRegionCodes||[];let p=U.allRegions.filter(r=>!b.includes(r.code)&&!a.includes(r.code));return N.value&&(p=p.filter(r=>r.name.includes(N.value))),p});Z(()=>U.modelValue,b=>{v.value=b,b&&(h.value=[...U.selectedRegions],N.value="")}),Z(v,b=>{k("update:modelValue",b)});const m=b=>{h.value.find(a=>a.code===b.code)||(h.value.push(b),k("update:selectedRegions",h.value))},y=b=>{const a=h.value.findIndex(p=>p.code===b.code);a>-1&&(h.value.splice(a,1),k("update:selectedRegions",h.value))},n=()=>{h.value=[...S.value],k("update:selectedRegions",h.value)},o=()=>{if(h.value.length===0){x.warning("请至少选择一个区域");return}k("confirm",h.value),v.value=!1},s=()=>{v.value=!1};return(b,a)=>{const p=V("el-icon"),r=V("el-input"),B=V("el-button"),f=V("el-dialog");return u(),q(f,{modelValue:v.value,"onUpdate:modelValue":a[1]||(a[1]=z=>v.value=z),title:"区域选择",width:"900px","close-on-click-modal":!1,"align-center":"",onClose:s},{footer:i(()=>[l("div",wl,[e(B,{onClick:n},{default:i(()=>a[7]||(a[7]=[C("全部选择")])),_:1,__:[7]}),e(B,{onClick:s},{default:i(()=>a[8]||(a[8]=[C("取消")])),_:1,__:[8]}),e(B,{type:"primary",onClick:o},{default:i(()=>a[9]||(a[9]=[C("确认")])),_:1,__:[9]})])]),default:i(()=>[l("div",cl,[l("div",fl,[l("div",gl,[l("div",vl,[e(p,{style:{"margin-right":"8px"}},{default:i(()=>[e($(te))]),_:1}),a[2]||(a[2]=C(" 地区选择 "))]),e(r,{modelValue:N.value,"onUpdate:modelValue":a[0]||(a[0]=z=>N.value=z),placeholder:"输入地区关键字",style:{"margin-bottom":"16px"},clearable:""},{prefix:i(()=>[e(p,null,{default:i(()=>[e($(Re))]),_:1})]),_:1},8,["modelValue"]),l("div",hl,[(u(!0),c(W,null,Y(S.value,z=>(u(),c("div",{key:z.code,class:"region-item",onClick:H=>m(z)},[l("span",yl,E(z.name),1),e(B,{link:"",size:"small",style:{color:"var(--el-color-primary)"}},{default:i(()=>[e(p,{style:{"margin-right":"4px"}},{default:i(()=>[e($(oe))]),_:1}),a[3]||(a[3]=C(" 选择 "))]),_:1,__:[3]})],8,_l))),128))])]),l("div",Vl,[l("div",bl,[e(p,{style:{"margin-right":"8px"}},{default:i(()=>[e($(xe))]),_:1}),a[4]||(a[4]=C(" 已选择 "))]),l("div",Cl,[h.value.length===0?(u(),c("div",Fl,[e(p,{style:{"font-size":"48px",color:"#c0c4cc","margin-bottom":"16px"}},{default:i(()=>[e($(We))]),_:1}),a[5]||(a[5]=l("div",null,"暂无数据",-1))])):Q("",!0),(u(!0),c(W,null,Y(h.value,z=>(u(),c("div",{key:z.code,class:"selected-region-item"},[l("span",kl,E(z.name),1),e(B,{link:"",size:"small",style:{color:"var(--el-color-danger)"},onClick:H=>y(z)},{default:i(()=>[e(p,{style:{"margin-right":"4px"}},{default:i(()=>[e($(ae))]),_:1}),a[6]||(a[6]=C(" 删除 "))]),_:2,__:[6]},1032,["onClick"])]))),128))])])])])]),_:1},8,["modelValue"])}}});const $l=J(Tl,[["__scopeId","data-v-9ebaee78"]]),Nl={class:"freight-template-container"},Dl={class:"header"},Ul={class:"pagination"},Sl={class:"drawer-content"},Al={class:"form-section"},zl={class:"section-title"},Rl={class:"form-row"},xl={class:"form-row"},Wl={class:"form-section"},Bl={class:"section-title"},Ql={class:"form-section"},El={class:"section-title"},Il={class:"form-row"},jl={class:"drawer-footer"},Kl=G({__name:"index",setup(K){const I=w([]),U=w(0),k=w(1),v=w(10),N=w(""),h=w(),S=w(!1),m=w(!1),y=w(!1),n=w(),o=w(!1),s=le({id:null,templateName:"",valuationType:1,remark:"",areas:[]}),b=w(!1),a=w(-1),p=le({areaName:"",areaCode:"",isDefault:!1,firstNum:1,additionalNum:1,firstWeight:1,additionalWeight:1,firstVolume:.01,additionalVolume:.01,firstFreight:0,additionalFreight:0,freeShippingAmount:100,unfreeShippingFreight:8}),r=w(!1),B=w([]),f=w([]),z=async()=>{try{const d=await Ke();d.data&&(f.value=d.data.map(t=>({code:t.regionCode,name:t.regionName})))}catch(d){console.error("获取省份数据失败:",d),x.error("获取省份数据失败")}},H=()=>s.areas.filter((d,t)=>t!==a.value).flatMap(d=>d.areaCode?d.areaCode.split(","):[]),ne={templateName:[{required:!0,message:"请输入模板名称",trigger:"blur"}],valuationType:[{required:!0,message:"请选择计费方式",trigger:"change"}]},se=Oe.debounce(()=>{k.value=1,M()},600),M=async()=>{S.value=!0;try{const d=await Le({page:k.value,size:v.value,name:N.value,valuationType:h.value});I.value=d.data.records||[],U.value=d.data.total||0}finally{S.value=!1}},ee=()=>{se()},de=()=>{N.value="",h.value=void 0,k.value=1,M()},re=d=>{v.value=d,k.value=1,M()},ue=d=>({1:"件数计费",2:"重量计费",3:"体积计费",4:"满额包邮"})[d]||"未知",me=d=>d?new Date(d).toLocaleString("zh-CN"):"",pe=()=>{y.value=!1,Object.assign(s,{id:null,templateName:"",valuationType:1,remark:"",areas:[{id:0,areaName:"全国统一运费",areaCode:"nationwide",isDefault:!0,firstNum:1,firstFreight:8,additionalNum:1,additionalFreight:0,firstWeight:1,additionalWeight:1,firstVolume:.01,additionalVolume:.01,freeShippingAmount:100,unfreeShippingFreight:8}]}),m.value=!0},ce=async d=>{var t;y.value=!0;try{const g=(await Pe(d.id)).data,j=((t=g.rules)==null?void 0:t.map(F=>{var L;const A={id:F.id,areaName:F.ruleName,areaCode:F.ruleType===2?"nationwide":((L=F.regions)==null?void 0:L.map(P=>P.regionCode).join(","))||"",isDefault:F.ruleType===2,firstFreight:F.firstFreight||0,firstNum:1,additionalNum:1,firstWeight:1,additionalWeight:1,firstVolume:.01,additionalVolume:.01,freeShippingAmount:100,unfreeShippingFreight:8};return g.valuationType===1?(A.firstNum=F.firstQuantity||1,A.additionalNum=F.additionalQuantity||1,A.additionalFreight=F.additionalFreight||0):g.valuationType===2?(A.firstWeight=F.firstQuantity||1,A.additionalWeight=F.additionalQuantity||1,A.additionalFreight=F.additionalFreight||0):g.valuationType===3?(A.firstVolume=F.firstQuantity||.01,A.additionalVolume=F.additionalQuantity||.01,A.additionalFreight=F.additionalFreight||0):g.valuationType===4&&(A.freeShippingAmount=F.freeShippingThreshold||100,A.unfreeShippingFreight=F.baseFreight||8),A}))||[];let T=j.find(F=>F.isDefault);T||(T={id:0,areaName:"全国统一运费",areaCode:"nationwide",isDefault:!0,firstNum:1,firstFreight:8,additionalNum:1,additionalFreight:0,firstWeight:1,additionalWeight:1,firstVolume:.01,additionalVolume:.01,freeShippingAmount:100,unfreeShippingFreight:8},j.push(T));const O=[...j.filter(F=>!F.isDefault),T];Object.assign(s,{id:g.id,templateName:g.templateName,valuationType:g.valuationType,remark:g.remark,areas:O}),m.value=!0}catch(D){console.error("获取模板详情失败:",D),x.error("获取模板详情失败")}},fe=async d=>{try{await Me.confirm(`确定要删除运费模板 ${d.templateName} 吗？删除后无法恢复。`,"删除运费模板",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await qe(d.id),x.success("删除成功"),M()}catch(t){t!=="cancel"&&(console.error("删除失败:",t),x.error("删除失败"))}},ge=d=>{s.areas.forEach(t=>{d===1?(t.firstNum=t.firstNum||1,t.additionalNum=t.additionalNum||1):d===2?(t.firstWeight=t.firstWeight||1,t.additionalWeight=t.additionalWeight||1):d===3?(t.firstVolume=t.firstVolume||.01,t.additionalVolume=t.additionalVolume||.01):d===4&&(t.freeShippingAmount=t.freeShippingAmount||100,t.unfreeShippingFreight=t.unfreeShippingFreight||8)})},ve=()=>{a.value=-1,he(),b.value=!0},he=()=>{Object.assign(p,{areaName:"",areaCode:"",isDefault:!1,firstNum:1,additionalNum:1,firstWeight:1,additionalWeight:1,firstVolume:.01,additionalVolume:.01,firstFreight:0,additionalFreight:0,freeShippingAmount:100,unfreeShippingFreight:8})},_e=(d,t)=>{a.value=t,Object.assign(p,{...d}),b.value=!0},ye=d=>{if(s.areas[d].isDefault){x.warning("默认区域不可删除");return}s.areas.splice(d,1)},Ve=()=>{if(p.areaCode){const d=p.areaCode.split(",");B.value=f.value.filter(t=>d.includes(t.code))}else B.value=[];r.value=!0},be=d=>{const t=d.map(g=>g.name).join("、"),D=d.map(g=>g.code).join(",");p.areaName=t,p.areaCode=D,r.value=!1},Ce=async d=>{if(d.areaCode&&d.areaCode!=="nationwide"){const D=d.areaCode.split(","),g=s.areas.filter((T,R)=>R!==a.value).flatMap(T=>T.areaCode?T.areaCode.split(","):[]),j=D.filter(T=>g.includes(T));if(j.length>0){const T=j.map(R=>{var O;return(O=f.value.find(F=>F.code===R))==null?void 0:O.name}).filter(Boolean).join("、");x.error(`以下区域已存在配送规则，请勿重复添加：${T}`);return}}const t={...d};if(a.value>=0)s.areas[a.value]=t;else{const D=s.areas.findIndex(g=>g.isDefault);D>=0?s.areas.splice(D,0,t):s.areas.push(t)}b.value=!1,x.success("区域配置保存成功")},Fe=async()=>{if(await n.value.validate(),s.areas.length===0){x.warning("请至少配置一个配送区域");return}o.value=!0;try{const d={templateName:s.templateName,valuationType:s.valuationType,isDefault:0,status:1,remark:s.remark,rules:s.areas.map((t,D)=>{const g={ruleName:t.areaName,ruleType:t.isDefault?2:1,sortOrder:t.isDefault?999:D,firstFreight:t.firstFreight||0,regions:[]};if(s.valuationType===1?(g.firstQuantity=t.firstNum||1,g.additionalQuantity=t.additionalNum||1,g.additionalFreight=t.additionalFreight||0):s.valuationType===2?(g.firstQuantity=t.firstWeight||1,g.additionalQuantity=t.additionalWeight||1,g.additionalFreight=t.additionalFreight||0):s.valuationType===3?(g.firstQuantity=t.firstVolume||.01,g.additionalQuantity=t.additionalVolume||.01,g.additionalFreight=t.additionalFreight||0):s.valuationType===4&&(g.freeShippingThreshold=t.freeShippingAmount||100,g.baseFreight=t.unfreeShippingFreight||8),t.areaCode&&t.areaCode!=="nationwide"){const j=t.areaCode.split(",");g.regions=j.map(T=>{const R=f.value.find(O=>O.code===T);return{regionCode:T,regionName:R?R.name:"未知地区",regionLevel:1}})}return g})};y.value?(await Ge(s.id,d)).code===200&&(x.success("更新成功"),m.value=!1,M()):(await Je(d)).code===200&&(x.success("创建成功"),m.value=!1,M())}catch(d){console.error("提交失败:",d),x.error("提交失败，请检查参数")}finally{o.value=!1}},ke=d=>({1:"",2:"success",3:"warning",4:"danger"})[d]||"";return Be(()=>{M(),z()}),(d,t)=>{const D=V("el-input"),g=V("el-option"),j=V("el-select"),T=V("el-button"),R=V("el-table-column"),O=V("el-tag"),F=V("el-table"),A=V("el-pagination"),L=V("el-icon"),P=V("el-form-item"),we=V("el-form"),Te=V("el-drawer"),$e=Qe("loading");return u(),c("div",Nl,[l("div",Dl,[e(D,{modelValue:N.value,"onUpdate:modelValue":t[0]||(t[0]=_=>N.value=_),placeholder:"搜索模板名称",clearable:"",style:{width:"200px","margin-right":"10px"},onKeyup:Ee(ee,["enter"]),onInput:ee,onClear:de},null,8,["modelValue"]),e(j,{modelValue:h.value,"onUpdate:modelValue":t[1]||(t[1]=_=>h.value=_),placeholder:"计费方式",clearable:"",style:{width:"120px","margin-right":"10px"},onChange:ee},{default:i(()=>[e(g,{label:"件数计费",value:1}),e(g,{label:"重量计费",value:2}),e(g,{label:"体积计费",value:3}),e(g,{label:"满额包邮",value:4})]),_:1},8,["modelValue"]),e(T,{type:"primary",onClick:t[2]||(t[2]=_=>pe())},{default:i(()=>t[13]||(t[13]=[C("新增运费模板")])),_:1,__:[13]})]),Ie((u(),q(F,{height:"calc(100vh - 230px)",data:I.value,style:{"margin-top":"16px"}},{default:i(()=>[e(R,{prop:"templateName",label:"模板名称",width:"250"}),e(R,{prop:"valuationType",label:"计费方式",width:"100"},{default:i(({row:_})=>[e(O,{type:ke(_.valuationType),size:"small"},{default:i(()=>[C(E(ue(_.valuationType)),1)]),_:2},1032,["type"])]),_:1}),e(R,{prop:"remark",label:"备注"}),e(R,{prop:"createTime",label:"创建时间",width:"180"},{default:i(({row:_})=>[C(E(me(_.createTime)),1)]),_:1}),e(R,{label:"操作",width:"200"},{default:i(({row:_})=>[e(T,{link:"",size:"small",style:{color:"var(--el-color-primary)"},onClick:Ne=>ce(_)},{default:i(()=>t[14]||(t[14]=[C("编辑")])),_:2,__:[14]},1032,["onClick"]),e(T,{link:"",size:"small",style:{color:"var(--el-color-danger)"},onClick:Ne=>fe(_)},{default:i(()=>t[15]||(t[15]=[C("删除")])),_:2,__:[15]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[$e,S.value]]),l("div",Ul,[e(A,{"current-page":k.value,"onUpdate:currentPage":t[3]||(t[3]=_=>k.value=_),"page-size":v.value,"onUpdate:pageSize":t[4]||(t[4]=_=>v.value=_),total:U.value,background:"","page-sizes":[10,20,50,100],onSizeChange:re,onCurrentChange:M},null,8,["current-page","page-size","total"])]),e(Te,{modelValue:m.value,"onUpdate:modelValue":t[10]||(t[10]=_=>m.value=_),title:y.value?"编辑运费模板":"新增运费模板",size:"80%",class:"freight-drawer"},{footer:i(()=>[l("div",jl,[e(T,{onClick:t[9]||(t[9]=_=>m.value=!1)},{default:i(()=>t[19]||(t[19]=[C("取消")])),_:1,__:[19]}),e(T,{type:"primary",onClick:Fe,loading:o.value},{default:i(()=>[C(E(y.value?"更新模板":"创建模板"),1)]),_:1},8,["loading"])])]),default:i(()=>[l("div",Sl,[e(we,{model:s,rules:ne,ref_key:"templateFormRef",ref:n,"label-width":"120px",class:"freight-form"},{default:i(()=>[l("div",Al,[l("div",zl,[e(L,{class:"section-icon"},{default:i(()=>[e($(ie))]),_:1}),t[16]||(t[16]=C(" 基本信息 "))]),l("div",Rl,[e(P,{label:"模板名称",prop:"templateName",class:"form-item-template-name"},{default:i(()=>[e(D,{modelValue:s.templateName,"onUpdate:modelValue":t[5]||(t[5]=_=>s.templateName=_),placeholder:"请输入模板名称，如：全国包邮、江浙沪包邮等",maxlength:"50","show-word-limit":"",size:"large"},null,8,["modelValue"])]),_:1})]),l("div",xl,[e(P,{label:"计费方式",prop:"valuationType",class:"form-item-full"},{default:i(()=>[e($(it),{modelValue:s.valuationType,"onUpdate:modelValue":t[6]||(t[6]=_=>s.valuationType=_),disabled:y.value,onChange:ge},null,8,["modelValue","disabled"])]),_:1})])]),l("div",Wl,[l("div",Bl,[e(L,{class:"section-icon"},{default:i(()=>[e($(te))]),_:1}),t[17]||(t[17]=C(" 配送区域设置 "))]),e(P,{label:"配送区域",prop:"areas",class:"area-form-item"},{default:i(()=>[e($(Mt),{areas:s.areas,"onUpdate:areas":t[7]||(t[7]=_=>s.areas=_),"valuation-type":s.valuationType,onEditArea:_e,onRemoveArea:ye,onAddArea:ve},null,8,["areas","valuation-type"])]),_:1})]),l("div",Ql,[l("div",El,[e(L,{class:"section-icon"},{default:i(()=>[e($(je))]),_:1}),t[18]||(t[18]=C(" 备注信息 "))]),l("div",Il,[e(P,{label:"备注",class:"form-item-full"},{default:i(()=>[e(D,{modelValue:s.remark,"onUpdate:modelValue":t[8]||(t[8]=_=>s.remark=_),type:"textarea",placeholder:"请输入备注内容，如适用范围、特殊说明等",rows:4,maxlength:"200","show-word-limit":"",resize:"none"},null,8,["modelValue"])]),_:1})])])]),_:1},8,["model"])])]),_:1},8,["modelValue","title"]),e($(pl),{modelValue:b.value,"onUpdate:modelValue":t[11]||(t[11]=_=>b.value=_),"valuation-type":s.valuationType,"form-data":p,onSubmit:Ce,onOpenRegionSelector:Ve},null,8,["modelValue","valuation-type","form-data"]),e($($l),{modelValue:r.value,"onUpdate:modelValue":t[12]||(t[12]=_=>r.value=_),"all-regions":f.value,"selected-regions":B.value,"exclude-region-codes":H(),onConfirm:be},null,8,["modelValue","all-regions","selected-regions","exclude-region-codes"])])}}});const Pl=J(Kl,[["__scopeId","data-v-d8d46295"]]);export{Pl as default};
