import{d as S,u as q,r as i,z as B,b as u,o as U,e as k,k as o,h as l,f as y,H as I,y as v,t as N,E as b,s as R,_ as F}from"./index-ecce2fce.js";const M={class:"reset-password-container"},T={class:"verification-code-container"},z=S({__name:"index",setup(D){const w=q(),d=i(),f=i(!1),p=i(!1),m=i(60),c=i("发送验证码"),a=B({email:"",verificationCode:"",newPassword:"",confirmPassword:""}),P={email:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],verificationCode:[{required:!0,message:"请输入验证码",trigger:"blur"},{min:6,max:6,message:"验证码长度应为6位",trigger:"blur"}],newPassword:[{required:!0,validator:(s,e,r)=>{var t;e===""?r(new Error("请输入密码")):(a.confirmPassword!==""&&((t=d.value)==null||t.validateField("confirmPassword")),r())},trigger:"blur"},{min:6,message:"密码长度不能小于6位",trigger:"blur"}],confirmPassword:[{required:!0,validator:(s,e,r)=>{e===""?r(new Error("请再次输入密码")):e!==a.newPassword?r(new Error("两次输入密码不一致!")):r()},trigger:"blur"}]},V=()=>{p.value=!0,m.value=60;const s=setInterval(()=>{m.value--,c.value=`${m.value}秒后重试`,m.value<=0&&(clearInterval(s),p.value=!1,c.value="发送验证码")},1e3)},C=async()=>{var s;try{await((s=d.value)==null?void 0:s.validateField("email")),b.success("验证码已发送到您的邮箱（模拟）"),V()}catch{}},g=()=>{const s=R();s?w.push({path:"/login",query:{scode:s}}):w.push("/login")},x=async()=>{if(d.value)try{await d.value.validate(),f.value=!0,b.success("密码重置成功，请使用新密码登录（模拟）"),g()}catch{}finally{f.value=!1}};return(s,e)=>{const r=u("el-input"),t=u("el-form-item"),_=u("el-button"),h=u("el-form"),E=u("el-card");return U(),k("div",M,[o(E,{class:"reset-password-card"},{header:l(()=>e[4]||(e[4]=[y("h2",null,"重置密码",-1)])),default:l(()=>[o(h,{ref_key:"formRef",ref:d,model:a,rules:P,"label-width":"100px",onSubmit:I(x,["prevent"])},{default:l(()=>[o(t,{label:"邮箱",prop:"email"},{default:l(()=>[o(r,{modelValue:a.email,"onUpdate:modelValue":e[0]||(e[0]=n=>a.email=n),placeholder:"请输入注册邮箱"},null,8,["modelValue"])]),_:1}),o(t,{label:"验证码",prop:"verificationCode"},{default:l(()=>[y("div",T,[o(r,{modelValue:a.verificationCode,"onUpdate:modelValue":e[1]||(e[1]=n=>a.verificationCode=n),placeholder:"请输入验证码"},null,8,["modelValue"]),o(_,{type:"primary",disabled:p.value,onClick:C},{default:l(()=>[v(N(c.value),1)]),_:1},8,["disabled"])])]),_:1}),o(t,{label:"新密码",prop:"newPassword"},{default:l(()=>[o(r,{modelValue:a.newPassword,"onUpdate:modelValue":e[2]||(e[2]=n=>a.newPassword=n),type:"password",placeholder:"请输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),o(t,{label:"确认密码",prop:"confirmPassword"},{default:l(()=>[o(r,{modelValue:a.confirmPassword,"onUpdate:modelValue":e[3]||(e[3]=n=>a.confirmPassword=n),type:"password",placeholder:"请再次输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),o(t,null,{default:l(()=>[o(_,{type:"primary","native-type":"submit",loading:f.value},{default:l(()=>e[5]||(e[5]=[v(" 重置密码 ")])),_:1,__:[5]},8,["loading"]),o(_,{onClick:g},{default:l(()=>e[6]||(e[6]=[v("返回登录")])),_:1,__:[6]})]),_:1})]),_:1},8,["model"])]),_:1})])}}});const j=F(z,[["__scopeId","data-v-238df8ef"]]);export{j as default};
