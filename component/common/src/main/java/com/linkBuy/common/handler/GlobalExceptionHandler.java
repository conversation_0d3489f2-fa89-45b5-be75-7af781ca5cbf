package com.linkBuy.common.handler;

import com.linkBuy.common.dto.Result;
import com.linkBuy.common.exception.AuthenticationException;
import com.linkBuy.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理自定义认证异常
     */
    @ExceptionHandler(AuthenticationException.class)
    public ResponseEntity<Object> handleAuthenticationException(AuthenticationException ex) {
        log.warn("认证异常: {}", ex.getMessage());
        return ResponseEntity.badRequest().body(Result.fail(ex.getCode(), ex.getMessage()));
    }

    /**
     * 处理Spring Security BadCredentialsException异常
     */
    @ExceptionHandler(BadCredentialsException.class)
    public Result<Void> handleBadCredentialsException(BadCredentialsException e) {
        log.warn("认证失败: {}", e.getMessage());
        return Result.fail(401, "用户名或密码错误");
    }

    /**
     * 处理Spring Security DisabledException异常
     */
    @ExceptionHandler(DisabledException.class)
    public Result<Void> handleDisabledException(DisabledException e) {
        log.warn("账号已禁用: {}", e.getMessage());
        return Result.fail(401, "账号已被禁用");
    }

    /**
     * 处理Spring Security InsufficientAuthenticationException异常
     */
    @ExceptionHandler(InsufficientAuthenticationException.class)
    public Result<Void> handleInsufficientAuthenticationException(InsufficientAuthenticationException e) {
        log.warn("认证不足: {}", e.getMessage());
        return Result.fail(401, "未登录或登录已过期");
    }

    /**
     * 处理Spring Security权限异常
     */
    @ExceptionHandler(AccessDeniedException.class)
    public Result<Void> handleAccessDeniedException(AccessDeniedException e) {
        log.warn("权限不足: {}", e.getMessage(), e);
        return Result.fail(403, "权限不足，无法访问");
    }

    /**
     * 处理参数校验异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<Void> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        List<FieldError> fieldErrors = e.getBindingResult().getFieldErrors();
        String message = fieldErrors.stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        return Result.fail(message);
    }

    /**
     * 处理参数校验异常
     */
    @ExceptionHandler(BindException.class)
    public Result<Void> handleBindException(BindException e) {
        List<FieldError> fieldErrors = e.getBindingResult().getFieldErrors();
        String message = fieldErrors.stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        return Result.fail(message);
    }

    /**
     * 处理参数校验异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public Result<Void> handleConstraintViolationException(ConstraintViolationException e) {
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        String message = violations.stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining(", "));
        return Result.fail(message);
    }

    /**
     * 处理JWT token过期异常
     */
    @ExceptionHandler(io.jsonwebtoken.ExpiredJwtException.class)
    public Result<Void> handleExpiredJwtException(io.jsonwebtoken.ExpiredJwtException e) {
        log.warn("Token已过期: {}", e.getMessage());
        return Result.fail(401, "登录已过期，请重新登录");
    }

    /**
     * 处理JWT token签名无效异常
     */
    @ExceptionHandler(io.jsonwebtoken.SignatureException.class)
    public Result<Void> handleSignatureException(io.jsonwebtoken.SignatureException e) {
        log.warn("Token签名无效: {}", e.getMessage());
        return Result.fail(401, "无效的登录凭证");
    }

    /**
     * 处理JWT token格式异常
     */
    @ExceptionHandler(io.jsonwebtoken.MalformedJwtException.class)
    public Result<Void> handleMalformedJwtException(io.jsonwebtoken.MalformedJwtException e) {
        log.warn("Token格式错误: {}", e.getMessage());
        return Result.fail(401, "无效的登录凭证");
    }

    /**
     * 处理JWT token其他异常
     */
    @ExceptionHandler(io.jsonwebtoken.JwtException.class)
    public Result<Void> handleJwtException(io.jsonwebtoken.JwtException e) {
        log.warn("Token验证失败: {}", e.getMessage());
        return Result.fail(401, "登录验证失败");
    }

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public Result<Void> handleRuntimeException(RuntimeException e) {
        log.error("运行时异常：", e);
        return Result.fail("未知异常");
    }
    /**
     * 处理业务异常
     */
    @ExceptionHandler(BizException.class)
    public Result<Void> handleRuntimeException(BizException e) {
        log.error("业务异常：", e);
        return Result.fail(e.getMessage());
    }

    /**
     * 参数校验异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public Result<Void> illegalArgumentExceptionException(IllegalArgumentException e) {
        log.error("参数校验异常：", e);
        return Result.fail(e.getMessage());
    }

    /**
     * 处理未知异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Object> handleException(Exception ex) {
        log.error("系统异常：", ex);
        return ResponseEntity.badRequest().body(Result.fail("系统繁忙，请稍后再试"));
    }
} 