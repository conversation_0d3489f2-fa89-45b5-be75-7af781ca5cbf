package com.linkBuy.common.event.v2;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.List;

/**
 * 事件处理器接口 - 重构版本
 * 
 * 设计原则：
 * 1. 支持泛型，类型安全
 * 2. 支持异步处理
 * 3. 支持批量处理
 * 4. 支持条件过滤
 */
@FunctionalInterface
public interface EventHandler<T extends Event> {
    
    /**
     * 处理事件
     * 
     * @param event 事件对象
     * @param context 处理上下文
     * @return 处理结果
     */
    EventHandleResult handle(T event, EventHandleContext context);
    
    /**
     * 是否支持该事件类型
     * 默认通过泛型推断
     * 
     * @param eventType 事件类型
     * @return 是否支持
     */
    default boolean supports(Class<? extends Event> eventType) {
        return true; // 由框架通过泛型推断
    }
    
    /**
     * 事件过滤条件
     * 
     * @param event 事件对象
     * @return 是否应该处理该事件
     */
    default boolean filter(T event) {
        return true;
    }
    
    /**
     * 获取处理器优先级
     * 数字越小优先级越高
     * 
     * @return 优先级
     */
    default int getPriority() {
        return 100;
    }
    
    /**
     * 是否支持批量处理
     * 
     * @return 是否支持批量处理
     */
    default boolean supportsBatch() {
        return false;
    }
    
    /**
     * 批量处理事件
     * 只有当 supportsBatch() 返回 true 时才会被调用
     * 
     * @param events 事件列表
     * @param context 处理上下文
     * @return 批量处理结果
     */
    default BatchEventHandleResult handleBatch(List<T> events, EventHandleContext context) {
        throw new UnsupportedOperationException("Batch processing not supported");
    }
} 