package com.linkBuy.common.service;

import com.linkBuy.common.exception.ProductBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * 商品业务公共服务
 * 提供商品相关的通用业务逻辑
 */
@Slf4j
@Service
public class ProductBusinessService {

    /**
     * 商品状态枚举
     */
    public enum ProductStatus {
        OFFLINE(0, "已下架"),
        ONLINE(1, "销售中"),
        DRAFT(2, "草稿"),
        REVIEWING(3, "审核中"),
        REJECTED(4, "审核拒绝");

        private final Integer code;
        private final String name;

        ProductStatus(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static ProductStatus fromCode(Integer code) {
            for (ProductStatus status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            throw new ProductBusinessException("无效的商品状态码: " + code);
        }
    }

    /**
     * 价格变更申请状态枚举
     */
    public enum PriceChangeStatus {
        PENDING(1, "待审核"),
        APPROVED(2, "审核通过"),
        REJECTED(3, "审核拒绝"),
        WITHDRAWN(4, "已撤回");

        private final Integer code;
        private final String name;

        PriceChangeStatus(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static PriceChangeStatus fromCode(Integer code) {
            for (PriceChangeStatus status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            throw new ProductBusinessException("无效的价格变更状态码: " + code);
        }
    }

    /**
     * 验证商品状态转换是否合法
     */
    public boolean canTransitionStatus(Integer fromStatus, Integer toStatus) {
        if (fromStatus == null || toStatus == null) {
            return false;
        }

        ProductStatus from = ProductStatus.fromCode(fromStatus);
        ProductStatus to = ProductStatus.fromCode(toStatus);

        switch (from) {
            case DRAFT:
                // 草稿 -> 审核中
                return to == ProductStatus.REVIEWING;
            case REVIEWING:
                // 审核中 -> 销售中/审核拒绝/草稿(撤回)
                return to == ProductStatus.ONLINE || to == ProductStatus.REJECTED || to == ProductStatus.DRAFT;
            case REJECTED:
                // 审核拒绝 -> 审核中
                return to == ProductStatus.REVIEWING;
            case ONLINE:
                // 销售中 -> 已下架
                return to == ProductStatus.OFFLINE;
            case OFFLINE:
                // 已下架 -> 销售中
                return to == ProductStatus.ONLINE;
            default:
                return false;
        }
    }

    /**
     * 验证价格变更状态转换是否合法
     */
    public boolean canTransitionPriceChangeStatus(Integer fromStatus, Integer toStatus) {
        if (fromStatus == null || toStatus == null) {
            return false;
        }

        PriceChangeStatus from = PriceChangeStatus.fromCode(fromStatus);
        PriceChangeStatus to = PriceChangeStatus.fromCode(toStatus);

        switch (from) {
            case PENDING:
                // 待审核 -> 审核通过/审核拒绝/已撤回
                return to == PriceChangeStatus.APPROVED || to == PriceChangeStatus.REJECTED || to == PriceChangeStatus.WITHDRAWN;
            default:
                // 其他状态不允许转换
                return false;
        }
    }

    /**
     * 验证供应商权限
     */
    public void validateSupplierPermission(Long spuSupplierId, Long currentSupplierId, String spuName) {
        if (spuSupplierId == null || currentSupplierId == null) {
            throw new ProductBusinessException("供应商信息不完整");
        }
        
        if (!spuSupplierId.equals(currentSupplierId)) {
            throw new ProductBusinessException("无权操作商品：" + (spuName != null ? spuName : "未知商品"));
        }
    }

    /**
     * 验证客户公司权限
     */
    public void validateCompanyPermission(Long spuCompanyId, Long currentCompanyId, String spuName) {
        if (spuCompanyId == null || currentCompanyId == null) {
            throw new ProductBusinessException("公司信息不完整");
        }
        
        if (!spuCompanyId.equals(currentCompanyId)) {
            throw new ProductBusinessException("无权操作商品：" + (spuName != null ? spuName : "未知商品"));
        }
    }

    /**
     * 验证商品是否可以编辑
     */
    public void validateProductEditPermission(Integer currentStatus, String spuName) {
        if (currentStatus == null) {
            throw new ProductBusinessException("商品状态信息缺失");
        }

        ProductStatus status = ProductStatus.fromCode(currentStatus);
        if (status == ProductStatus.REVIEWING) {
            throw new ProductBusinessException("审核中的商品不允许编辑：" + (spuName != null ? spuName : ""));
        }
    }

    /**
     * 验证商品是否可以删除
     */
    public void validateProductDeletePermission(Integer currentStatus, String spuName) {
        if (currentStatus == null) {
            throw new ProductBusinessException("商品状态信息缺失");
        }

        ProductStatus status = ProductStatus.fromCode(currentStatus);
        if (status == ProductStatus.REVIEWING) {
            throw new ProductBusinessException("审核中的商品不能删除：" + (spuName != null ? spuName : ""));
        }
    }

    /**
     * 验证商品是否可以提交审核
     */
    public void validateProductSubmitReviewPermission(Integer currentStatus, String spuName) {
        if (currentStatus == null) {
            throw new ProductBusinessException("商品状态信息缺失");
        }

        ProductStatus status = ProductStatus.fromCode(currentStatus);
        if (status != ProductStatus.DRAFT && status != ProductStatus.REJECTED) {
            throw new ProductBusinessException("只有草稿或审核拒绝状态的商品才能提交审核：" + (spuName != null ? spuName : ""));
        }
    }

    /**
     * 验证价格变更申请是否可以撤回
     */
    public void validatePriceChangeWithdrawPermission(Integer currentStatus, String requestNo) {
        if (currentStatus == null) {
            throw new ProductBusinessException("申请状态信息缺失");
        }

        PriceChangeStatus status = PriceChangeStatus.fromCode(currentStatus);
        if (status != PriceChangeStatus.PENDING) {
            throw new ProductBusinessException("只有待审核状态的申请才能撤回：" + (requestNo != null ? requestNo : ""));
        }
    }

    /**
     * 验证价格是否有变化
     */
    public boolean hasPriceChange(BigDecimal oldPrice, BigDecimal newPrice) {
        // 如果新价格为null，说明没有设置新价格
        if (newPrice == null) {
            return false;
        }

        // 如果旧价格为null，但新价格有值，说明是新增价格
        if (oldPrice == null) {
            return newPrice.compareTo(BigDecimal.ZERO) != 0; // 只有新价格不为0才算有变化
        }

        // 两个价格都有值，比较是否相等
        return oldPrice.compareTo(newPrice) != 0;
    }

    /**
     * 验证价格是否有效
     */
    public void validatePrice(BigDecimal price, String priceType) {
        if (price != null && price.compareTo(BigDecimal.ZERO) < 0) {
            throw new ProductBusinessException(priceType + "不能为负数");
        }
    }

    /**
     * 验证库存是否有效
     */
    public void validateInventory(Integer inventory, String inventoryType) {
        if (inventory != null && inventory < 0) {
            throw new ProductBusinessException(inventoryType + "不能为负数");
        }
    }

    /**
     * 获取状态名称
     */
    public String getStatusName(Integer status) {
        try {
            return ProductStatus.fromCode(status).getName();
        } catch (Exception e) {
            return "未知状态";
        }
    }

    /**
     * 获取价格变更状态名称
     */
    public String getPriceChangeStatusName(Integer status) {
        try {
            return PriceChangeStatus.fromCode(status).getName();
        } catch (Exception e) {
            return "未知状态";
        }
    }

    /**
     * 获取可用的状态转换列表
     */
    public List<Integer> getAvailableTransitions(Integer currentStatus) {
        if (currentStatus == null) {
            return Arrays.asList();
        }

        ProductStatus status = ProductStatus.fromCode(currentStatus);
        switch (status) {
            case DRAFT:
                return Arrays.asList(ProductStatus.REVIEWING.getCode());
            case REVIEWING:
                return Arrays.asList(ProductStatus.ONLINE.getCode(), ProductStatus.REJECTED.getCode(), ProductStatus.DRAFT.getCode());
            case REJECTED:
                return Arrays.asList(ProductStatus.REVIEWING.getCode());
            case ONLINE:
                return Arrays.asList(ProductStatus.OFFLINE.getCode());
            case OFFLINE:
                return Arrays.asList(ProductStatus.ONLINE.getCode());
            default:
                return Arrays.asList();
        }
    }
} 