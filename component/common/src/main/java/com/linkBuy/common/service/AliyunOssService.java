package com.linkBuy.common.service;

import cn.hutool.core.lang.Assert;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import com.linkBuy.common.enums.SysConfigKeyEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PreDestroy;
import java.io.IOException;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AliyunOssService {

    private final SysConfigServiceInterface sysConfigService;

    // 缓存OSS客户端，避免频繁创建
    private final ConcurrentHashMap<String, OSS> ossClientCache = new ConcurrentHashMap<>();

    /**
     * 获取OSS客户端
     * 每次都会检查配置是否变化，如果变化则创建新客户端
     */
    private OSS getOssClient() {
        String endpoint = sysConfigService.getValueByKey(SysConfigKeyEnum.ALIYUN_OSS_DOMAIN.getCode());
        String accessKeyId = sysConfigService.getValueByKey(SysConfigKeyEnum.ALIYUN_OSS_ACCESS_KEY.getCode());
        String accessKeySecret = sysConfigService.getValueByKey(SysConfigKeyEnum.ALIYUN_OSS_SECRET_KEY.getCode());

        // 生成配置的唯一标识
        String configKey = generateConfigKey(endpoint, accessKeyId, accessKeySecret);

        // 检查是否有旧的客户端需要清理
        cleanupOldClients(configKey);

        // 从缓存中获取客户端，如果不存在则创建新的
        return ossClientCache.computeIfAbsent(configKey, key -> {
            log.info("创建新的OSS客户端，endpoint: {}, accessKeyId: {}", endpoint, accessKeyId);
            return new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        });
    }

    public OssUploadResult upload(MultipartFile file, String fsAccountName, String objectName) throws IOException {
        Assert.isTrue(!file.isEmpty(), "请选择要上传的文件");

        String originalFilename = file.getOriginalFilename();
        Assert.notNull(originalFilename, "文件名不能为空");
        String extension;
        assert originalFilename != null;
        if (originalFilename.contains(".")) {
            extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        } else {
            extension = "";
        }
        if (Strings.isBlank(objectName)) {
            objectName = fsAccountName + "/" + UUID.randomUUID() + extension;
        }
        PutObjectRequest putObjectRequest = new PutObjectRequest(getBucketName(), objectName, file.getInputStream());

        PutObjectResult result = getOssClient().putObject(putObjectRequest);

        if (result != null) {
            return new OssUploadResult(objectName, "https://" + getBucketName() + "." + getEndpoint().replace("http://", "").replace("https://", "") + "/" + objectName);
        } else {
            throw new RuntimeException("文件上传失败");
        }
    }

    /**
     * 获取Bucket名称
     */
    public String getBucketName() {
        return sysConfigService.getValueByKey(SysConfigKeyEnum.ALIYUN_OSS_BUCKET.getCode());
    }

    /**
     * 获取域名
     */
    public String getEndpoint() {
        return sysConfigService.getValueByKey(SysConfigKeyEnum.ALIYUN_OSS_DOMAIN.getCode());
    }

    /**
     * 清理旧的客户端
     * 如果当前配置与缓存中的不同，清理旧的客户端
     */
    private void cleanupOldClients(String currentConfigKey) {
        if (ossClientCache.size() > 1 || (ossClientCache.size() == 1 && !ossClientCache.containsKey(currentConfigKey))) {
            log.info("检测到配置变化，清理旧的OSS客户端");
            ossClientCache.entrySet().removeIf(entry -> {
                if (!entry.getKey().equals(currentConfigKey)) {
                    try {
                        entry.getValue().shutdown();
                        log.info("已关闭旧的OSS客户端: {}", entry.getKey());
                        return true;
                    } catch (Exception e) {
                        log.warn("关闭OSS客户端时发生异常", e);
                        return true;
                    }
                }
                return false;
            });
        }
    }

    /**
     * 生成配置的唯一标识
     */
    private String generateConfigKey(String endpoint, String accessKeyId, String accessKeySecret) {
        return String.format("%s_%s_%s",
                endpoint != null ? endpoint : "null",
                accessKeyId != null ? accessKeyId : "null",
                accessKeySecret != null ? accessKeySecret.hashCode() : "null");
    }

    @PreDestroy
    public void destroy() {
        log.info("销毁OSS客户端管理器");
        ossClientCache.values().forEach(client -> {
            try {
                client.shutdown();
            } catch (Exception e) {
                log.warn("关闭OSS客户端时发生异常", e);
            }
        });
        ossClientCache.clear();
    }

    @Data
    @ToString
    @AllArgsConstructor
    public static class OssUploadResult {
        private String ossKey;
        private String fileName;
    }
}
