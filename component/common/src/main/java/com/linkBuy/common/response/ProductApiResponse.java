package com.linkBuy.common.response;

import lombok.Data;

/**
 * 商品API统一响应结果包装器
 * 提供统一的API响应格式
 */
@Data
public class ProductApiResponse<T> {
    
    /**
     * 响应状态码
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 时间戳
     */
    private Long timestamp;

    public ProductApiResponse() {
        this.timestamp = System.currentTimeMillis();
    }

    public ProductApiResponse(Integer code, String message) {
        this();
        this.code = code;
        this.message = message;
    }

    public ProductApiResponse(Integer code, String message, T data) {
        this(code, message);
        this.data = data;
    }

    /**
     * 成功响应
     */
    public static <T> ProductApiResponse<T> success() {
        return new ProductApiResponse<>(200, "操作成功");
    }

    /**
     * 成功响应（带数据）
     */
    public static <T> ProductApiResponse<T> success(T data) {
        return new ProductApiResponse<>(200, "操作成功", data);
    }

    /**
     * 成功响应（带消息和数据）
     */
    public static <T> ProductApiResponse<T> success(String message, T data) {
        return new ProductApiResponse<>(200, message, data);
    }

    /**
     * 失败响应
     */
    public static <T> ProductApiResponse<T> error(String message) {
        return new ProductApiResponse<>(500, message);
    }

    /**
     * 失败响应（带状态码）
     */
    public static <T> ProductApiResponse<T> error(Integer code, String message) {
        return new ProductApiResponse<>(code, message);
    }

    /**
     * 参数错误响应
     */
    public static <T> ProductApiResponse<T> badRequest(String message) {
        return new ProductApiResponse<>(400, message);
    }

    /**
     * 权限不足响应
     */
    public static <T> ProductApiResponse<T> forbidden(String message) {
        return new ProductApiResponse<>(403, message);
    }

    /**
     * 资源不存在响应
     */
    public static <T> ProductApiResponse<T> notFound(String message) {
        return new ProductApiResponse<>(404, message);
    }

    /**
     * 业务异常响应
     */
    public static <T> ProductApiResponse<T> businessError(String message) {
        return new ProductApiResponse<>(1001, message);
    }

    /**
     * 价格变更异常响应
     */
    public static <T> ProductApiResponse<T> priceChangeError(String message) {
        return new ProductApiResponse<>(1002, message);
    }

    /**
     * 商品状态异常响应
     */
    public static <T> ProductApiResponse<T> statusError(String message) {
        return new ProductApiResponse<>(1003, message);
    }

    /**
     * 权限不足响应
     */
    public static <T> ProductApiResponse<T> permissionDenied(String message) {
        return new ProductApiResponse<>(1004, message);
    }

    /**
     * 数据验证失败响应
     */
    public static <T> ProductApiResponse<T> validationError(String message) {
        return new ProductApiResponse<>(1005, message);
    }

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return this.code != null && this.code == 200;
    }

    /**
     * 判断是否失败
     */
    public boolean isError() {
        return !isSuccess();
    }
} 