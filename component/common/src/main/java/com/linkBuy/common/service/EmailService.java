package com.linkBuy.common.service;

import com.linkBuy.common.enums.SysConfigKeyEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.util.Date;
import java.util.List;
import java.util.Properties;

/**
 * 邮件发送服务
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class EmailService {

    private final SysConfigServiceInterface sysConfigService;

    /**
     * 发送简单文本邮件
     *
     * @param to      收件人邮箱
     * @param subject 邮件主题
     * @param content 邮件内容
     * @return 是否发送成功
     */
    public boolean sendTextEmail(String to, String subject, String content) {
        return sendEmail(to, subject, content, false);
    }

    /**
     * 发送HTML邮件
     *
     * @param to      收件人邮箱
     * @param subject 邮件主题
     * @param content HTML邮件内容
     * @return 是否发送成功
     */
    public boolean sendHtmlEmail(String to, String subject, String content) {
        return sendEmail(to, subject, content, true);
    }

    /**
     * 批量发送邮件
     *
     * @param toList  收件人邮箱列表
     * @param subject 邮件主题
     * @param content 邮件内容
     * @param isHtml  是否为HTML格式
     * @return 发送成功的数量
     */
    public int sendBatchEmail(List<String> toList, String subject, String content, boolean isHtml) {
        if (toList == null || toList.isEmpty()) {
            log.warn("收件人列表为空，无法发送邮件");
            return 0;
        }

        int successCount = 0;
        for (String to : toList) {
            if (sendEmail(to, subject, content, isHtml)) {
                successCount++;
            }
        }

        log.info("批量发送邮件完成，总数: {}, 成功: {}", toList.size(), successCount);
        return successCount;
    }

    /**
     * 发送用户账号创建通知邮件
     *
     * @param to       收件人邮箱
     * @param mobile 用户名
     * @param password 密码
     * @param userType 用户类型（供应商/客户）
     * @return 是否发送成功
     */
    public boolean sendAccountCreatedEmail(String to, String mobile, String password, String userType) {
        if (!StringUtils.hasText(to)) {
            log.warn("收件人邮箱为空，无法发送账号创建通知邮件");
            return false;
        }

        String subject = "LinkBuy系统 - 您的账号已创建";
        String content = buildAccountCreatedEmailContent(mobile, password, userType);
        
        return sendHtmlEmail(to, subject, content);
    }

    /**
     * 发送密码重置通知邮件
     *
     * @param to          收件人邮箱
     * @param mobile    用户名
     * @param newPassword 新密码
     * @param userType    用户类型（供应商/客户）
     * @return 是否发送成功
     */
    public boolean sendPasswordResetEmail(String to, String mobile, String newPassword, String userType) {
        if (!StringUtils.hasText(to)) {
            log.warn("收件人邮箱为空，无法发送密码重置通知邮件");
            return false;
        }

        String subject = "LinkBuy系统 - 密码重置通知";
        String content = buildPasswordResetEmailContent(mobile, newPassword, userType);
        
        return sendHtmlEmail(to, subject, content);
    }

    /**
     * 发送供应商用户创建通知邮件
     *
     * @param to           收件人邮箱
     * @param supplierName 供应商名称
     * @param username     用户名
     * @return 是否发送成功
     */
    public boolean sendSupplierUserCreatedNotification(String to, String supplierName, String username) {
        if (!StringUtils.hasText(to)) {
            log.warn("收件人邮箱为空，无法发送供应商用户创建通知邮件");
            return false;
        }

        String subject = "供应商用户账号创建通知";
        String content = buildSupplierUserCreatedEmailContent(supplierName, username);
        
        return sendHtmlEmail(to, subject, content);
    }

    /**
     * 发送客户用户创建通知邮件
     *
     * @param to          收件人邮箱
     * @param companyName 客户名称
     * @param username    用户名
     * @return 是否发送成功
     */
    public boolean sendCompanyUserCreatedNotification(String to, String companyName, String username) {
        if (!StringUtils.hasText(to)) {
            log.warn("收件人邮箱为空，无法发送客户用户创建通知邮件");
            return false;
        }

        String subject = "客户用户账号创建通知";
        String content = buildCompanyUserCreatedEmailContent(companyName, username);
        
        return sendHtmlEmail(to, subject, content);
    }

    /**
     * 核心邮件发送方法
     */
    private boolean sendEmail(String to, String subject, String content, boolean isHtml) {
        if (!StringUtils.hasText(to) || !StringUtils.hasText(subject) || !StringUtils.hasText(content)) {
            log.warn("邮件参数不完整，无法发送邮件");
            return false;
        }

        try {
            // 创建邮件会话
            Session session = createMailSession();
            
            // 创建邮件消息
            MimeMessage message = new MimeMessage(session);
            
            // 设置发件人
            String fromEmail = getFromEmail();
            String fromName = getFromName();
            message.setFrom(new InternetAddress(fromEmail, fromName));
            
            // 设置收件人
            message.setRecipient(Message.RecipientType.TO, new InternetAddress(to));
            
            // 设置邮件主题
            message.setSubject(subject, "UTF-8");
            
            // 设置邮件内容
            if (isHtml) {
                message.setContent(content, "text/html;charset=UTF-8");
            } else {
                message.setText(content, "UTF-8");
            }
            
            // 设置发送时间
            message.setSentDate(new Date());
            
            // 发送邮件
            Transport.send(message);
            
            log.info("邮件发送成功，收件人: {}, 主题: {}", to, subject);
            return true;
            
        } catch (Exception e) {
            log.error("邮件发送失败，收件人: {}, 主题: {}, 错误: {}", to, subject, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 创建邮件会话
     */
    private Session createMailSession() {
        String smtpHost = getSmtpHost();
        String smtpPort = getSmtpPort();
        String smtpAuth = getSmtpAuth();
        String starttlsEnable = getStarttlsEnable();
        String username = getSmtpUsername();
        String password = getSmtpPassword();
        
        // 记录配置信息用于诊断
        log.info("邮件配置 - Host: {}, Port: {}, Auth: {}, STARTTLS: {}, Username: {}, Password长度: {}", 
                smtpHost, smtpPort, smtpAuth, starttlsEnable, username, 
                password != null ? password.length() : 0);
        
        Properties props = new Properties();
        props.put("mail.smtp.host", smtpHost);
        props.put("mail.smtp.port", smtpPort);
        props.put("mail.smtp.auth", smtpAuth);
        props.put("mail.smtp.starttls.enable", starttlsEnable);
        props.put("mail.smtp.ssl.trust", smtpHost);
        
        // 设置连接超时
        props.put("mail.smtp.connectiontimeout", "30000");
        props.put("mail.smtp.timeout", "30000");
        props.put("mail.smtp.writetimeout", "30000");

        return Session.getInstance(props, new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                log.debug("执行邮箱认证: {}", username);
                return new PasswordAuthentication(username, password);
            }
        });
    }

    // 从数据库获取配置的方法
    private String getSmtpHost() {
        String value = sysConfigService.getValueByKey(SysConfigKeyEnum.EMAIL_SMTP_HOST.getCode());
        return StringUtils.hasText(value) ? value : "smtp.qq.com";
    }

    private String getSmtpPort() {
        String value = sysConfigService.getValueByKey(SysConfigKeyEnum.EMAIL_SMTP_PORT.getCode());
        return StringUtils.hasText(value) ? value : "587";
    }

    private String getSmtpUsername() {
        return sysConfigService.getValueByKey(SysConfigKeyEnum.EMAIL_SMTP_USERNAME.getCode());
    }

    private String getSmtpPassword() {
        return sysConfigService.getValueByKey(SysConfigKeyEnum.EMAIL_SMTP_PASSWORD.getCode());
    }

    private String getFromEmail() {
        return sysConfigService.getValueByKey(SysConfigKeyEnum.EMAIL_FROM_ADDRESS.getCode());
    }

    private String getFromName() {
        String value = sysConfigService.getValueByKey(SysConfigKeyEnum.EMAIL_FROM_NAME.getCode());
        return StringUtils.hasText(value) ? value : "LinkBuy系统";
    }

    private String getSmtpAuth() {
        String value = sysConfigService.getValueByKey(SysConfigKeyEnum.EMAIL_SMTP_AUTH.getCode());
        return StringUtils.hasText(value) ? value : "true";
    }

    private String getStarttlsEnable() {
        String value = sysConfigService.getValueByKey(SysConfigKeyEnum.EMAIL_SMTP_STARTTLS_ENABLE.getCode());
        return StringUtils.hasText(value) ? value : "true";
    }

    /**
     * 验证邮件配置是否正确
     */
    public boolean validateEmailConfig() {
        String smtpHost = getSmtpHost();
        String smtpUsername = getSmtpUsername();
        String smtpPassword = getSmtpPassword();
        String fromEmail = getFromEmail();
        
        if (!StringUtils.hasText(smtpHost) || !StringUtils.hasText(smtpUsername) 
            || !StringUtils.hasText(smtpPassword) || !StringUtils.hasText(fromEmail)) {
            log.warn("邮件配置不完整，请检查配置");
            return false;
        }
        
        // 针对163邮箱的特殊验证
        if (smtpHost.contains("163.com")) {
            if (smtpPassword.length() < 10) {
                log.warn("163邮箱密码长度过短，可能不是授权码。163邮箱需要使用授权码而不是登录密码");
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 测试邮件连接
     */
    public boolean testEmailConnection() {
        try {
            Session session = createMailSession();
            Transport transport = session.getTransport("smtp");
            transport.connect(getSmtpHost(), getSmtpUsername(), getSmtpPassword());
            transport.close();
            log.info("邮件服务器连接测试成功");
            return true;
        } catch (Exception e) {
            log.error("邮件服务器连接测试失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送测试邮件
     *
     * @param to 收件人邮箱
     * @return 是否发送成功
     */
    public boolean sendTestEmail(String to) {
        String subject = "LinkBuy系统 - 邮件服务测试";
        String content = """
            <h3>邮件服务测试</h3>
            <p>如果您收到这封邮件，说明LinkBuy系统的邮件服务配置正确。</p>
            <p>发送时间：%s</p>
            """.formatted(new Date());
        
        return sendHtmlEmail(to, subject, content);
    }

    /**
     * 构建账号创建邮件内容
     */
    private String buildAccountCreatedEmailContent(String username, String password, String userType) {
        return String.format("""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>账号创建通知</title>
            </head>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">
                        LinkBuy系统 - 账号创建通知
                    </h2>
                    
                    <p>您好！</p>
                    
                    <p>您的%s管理账号已成功创建，以下是您的登录信息：</p>
                    
                    <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                        <p><strong>登录手机号：</strong> %s</p>
                        <p><strong>初始密码：</strong> <span style="color: #e74c3c; font-weight: bold;">%s</span></p>
                    </div>
                    
                    <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
                        <p style="margin: 0;"><strong>安全提醒：</strong></p>
                        <ul style="margin: 10px 0;">
                            <li>请妥善保管您的登录信息</li>
                            <li>建议首次登录后立即修改密码</li>
                            <li>请勿将账号信息泄露给他人</li>
                        </ul>
                    </div>
                    
                    <p>如有任何问题，请联系系统管理员。</p>
                    
                    <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                    <p style="color: #7f8c8d; font-size: 12px;">
                        此邮件由LinkBuy系统自动发送，请勿回复。<br>
                        发送时间：%s
                    </p>
                </div>
            </body>
            </html>
            """, userType, username, password, new Date());
    }

    /**
     * 构建密码重置邮件内容
     */
    private String buildPasswordResetEmailContent(String username, String newPassword, String userType) {
        return String.format("""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>密码重置通知</title>
            </head>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #2c3e50; border-bottom: 2px solid #e74c3c; padding-bottom: 10px;">
                        LinkBuy系统 - 密码重置通知
                    </h2>
                    
                    <p>您好！</p>
                    
                    <p>您的%s管理账号密码已被重置，以下是您的新登录信息：</p>
                    
                    <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                        <p><strong>登录手机号：</strong> %s</p>
                        <p><strong>新密码：</strong> <span style="color: #e74c3c; font-weight: bold;">%s</span></p>
                    </div>
                    
                    <div style="background-color: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin: 20px 0;">
                        <p style="margin: 0;"><strong>重要提醒：</strong></p>
                        <ul style="margin: 10px 0;">
                            <li>请立即使用新密码登录系统</li>
                            <li>建议登录后立即修改为您熟悉的密码</li>
                            <li>如非本人操作，请立即联系系统管理员</li>
                        </ul>
                    </div>
                    
                    <p>如有任何问题，请联系系统管理员。</p>
                    
                    <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                    <p style="color: #7f8c8d; font-size: 12px;">
                        此邮件由LinkBuy系统自动发送，请勿回复。<br>
                        发送时间：%s
                    </p>
                </div>
            </body>
            </html>
            """, userType, username, newPassword, new Date());
    }

    /**
     * 构建供应商用户创建邮件内容
     */
    private String buildSupplierUserCreatedEmailContent(String supplierName, String username) {
        return String.format("""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>供应商用户账号创建通知</title>
            </head>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">
                        供应商用户账号创建通知
                    </h2>
                    
                    <p>尊敬的用户，</p>
                    
                    <p>您在供应商 <strong>%s</strong> 下的账号已成功创建。</p>
                    
                    <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                        <p><strong>登录手机号：</strong> %s</p>
                    </div>
                    
                    <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
                        <p style="margin: 0;"><strong>温馨提示：</strong></p>
                        <ul style="margin: 10px 0;">
                            <li>请及时登录系统并修改初始密码</li>
                            <li>如有疑问，请联系系统管理员</li>
                        </ul>
                    </div>
                    
                    <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                    <p style="color: #7f8c8d; font-size: 12px;">
                        此邮件由LinkBuy系统自动发送，请勿回复。<br>
                        发送时间：%s
                    </p>
                </div>
            </body>
            </html>
            """, supplierName, username, new Date());
    }

    /**
     * 构建客户用户创建邮件内容
     */
    private String buildCompanyUserCreatedEmailContent(String companyName, String username) {
        return String.format("""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>客户用户账号创建通知</title>
            </head>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">
                        客户用户账号创建通知
                    </h2>
                    
                    <p>尊敬的用户，</p>
                    
                    <p>您在客户 <strong>%s</strong> 下的账号已成功创建。</p>
                    
                    <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                        <p><strong>登录手机号：</strong> %s</p>
                    </div>
                    
                    <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
                        <p style="margin: 0;"><strong>温馨提示：</strong></p>
                        <ul style="margin: 10px 0;">
                            <li>请及时登录系统并修改初始密码</li>
                            <li>如有疑问，请联系系统管理员</li>
                        </ul>
                    </div>
                    
                    <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                    <p style="color: #7f8c8d; font-size: 12px;">
                        此邮件由LinkBuy系统自动发送，请勿回复。<br>
                        发送时间：%s
                    </p>
                </div>
            </body>
            </html>
            """, companyName, username, new Date());
    }

    /**
     * 构建供应商资质过期邮件内容
     */
    private String buildSupplierLicenseExpiredEmailContent(String supplierName, String licenseName, String expireDate) {
        return String.format("""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>供应商资质过期通知</title>
            </head>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #2c3e50; border-bottom: 2px solid #e74c3c; padding-bottom: 10px;">
                        供应商资质过期通知
                    </h2>
                    
                    <p>尊敬的用户，</p>
                    
                    <p>供应商 <strong>%s</strong> 的资质证书已过期，请及时更新。</p>
                    
                    <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                        <p><strong>资质名称：</strong> %s</p>
                        <p><strong>过期日期：</strong> <span style="color: #e74c3c; font-weight: bold;">%s</span></p>
                    </div>
                    
                    <div style="background-color: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 20px 0;">
                        <p style="margin: 0;"><strong>重要提醒：</strong></p>
                        <p style="margin: 10px 0;">请尽快上传新的资质证书，以免影响业务正常进行。</p>
                    </div>
                    
                    <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                    <p style="color: #7f8c8d; font-size: 12px;">
                        此邮件由LinkBuy系统自动发送，请勿回复。<br>
                        发送时间：%s
                    </p>
                </div>
            </body>
            </html>
            """, supplierName, licenseName, expireDate, new Date());
    }

    /**
     * 构建供应商资质即将过期邮件内容
     */
    private String buildSupplierLicenseExpiringEmailContent(String supplierName, String licenseName, String expireDate) {
        return String.format("""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>供应商资质即将过期提醒</title>
            </head>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #2c3e50; border-bottom: 2px solid #ffc107; padding-bottom: 10px;">
                        供应商资质即将过期提醒
                    </h2>
                    
                    <p>尊敬的用户，</p>
                    
                    <p>供应商 <strong>%s</strong> 的资质证书即将过期，请及时更新。</p>
                    
                    <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                        <p><strong>资质名称：</strong> %s</p>
                        <p><strong>过期日期：</strong> <span style="color: #ffc107; font-weight: bold;">%s</span></p>
                    </div>
                    
                    <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
                        <p style="margin: 0;"><strong>温馨提醒：</strong></p>
                        <p style="margin: 10px 0;">为避免影响业务正常进行，请尽快准备并上传新的资质证书。</p>
                    </div>
                    
                    <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                    <p style="color: #7f8c8d; font-size: 12px;">
                        此邮件由LinkBuy系统自动发送，请勿回复。<br>
                        发送时间：%s
                    </p>
                </div>
            </body>
            </html>
            """, supplierName, licenseName, expireDate, new Date());
    }
} 