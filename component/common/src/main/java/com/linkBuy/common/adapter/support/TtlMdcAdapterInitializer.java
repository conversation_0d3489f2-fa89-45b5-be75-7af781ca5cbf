package com.linkBuy.common.adapter.support;

import com.linkBuy.common.adapter.TtlMdcAdapter;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.lang.NonNull;

/**
 * 初始化TtlMDCAdapter实例，并替换MDC中的adapter对象
 *
 * <AUTHOR>
 * @since 2023/8/3
 */
public class TtlMdcAdapterInitializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    @Override
    public void initialize(@NonNull ConfigurableApplicationContext applicationContext) {
        //加载TtlMDCAdapter实例
        TtlMdcAdapter.getInstance();
    }

}
