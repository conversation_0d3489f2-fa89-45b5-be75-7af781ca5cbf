package com.linkBuy.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum SysConfigKeyEnum {

    ALIYUN_OSS_BUCKET("ALIYUN_OSS_BUCKET", "阿里云OSS_BUCKET"),
    ALIYUN_OSS_ACCESS_KEY("ALIYUN_OSS_ACCESS_KEK", "阿里云OSS_BUCKET_ACCESS_KEK"),
    ALIYUN_OSS_SECRET_KEY("ALIYUN_OSS_SECRET_KEY", "阿里云OSS_ACCESS_SECRET_KEY"),
    ALIYUN_OSS_DOMAIN("ALIYUN_OSS_DOMAIN", "阿里云OSS__ACCESS_DOMAIN"),

    ALIYUN_SMS_SECRET_KEY("ALIYUN_SMS_ACCESS_KEY", "阿里云_短信_ACCESS_KEY"),
    ALIY<PERSON>_SMS_ACCESS_SECRET("ALIYUN_SMS_ACCESS_SECRET", "阿里云_短信_ACCESS_SECRET"),
    ALIYUN_SMS_SIGN_NAME("ALIYUN_SMS_SIGN_NAME", "阿里云_短信_签名"),
    ALIYUN_SMS_TEMPLATE_CODE("ALIYUN_SMS_TEMPLATE_CODE", "阿里云_短信_验证码模板"),

    KUAIDI100_CUSTOMER("KUAIDI100_CUSTOMER", "快递100_CUSTOMER"),
    KUAIDI100_APPKEY("KUAIDI100_APPKEY", "快递100_APPKEY"),
    
    // 邮件配置
    EMAIL_SMTP_HOST("EMAIL_SMTP_HOST", "邮件SMTP服务器"),
    EMAIL_SMTP_PORT("EMAIL_SMTP_PORT", "邮件SMTP端口"),
    EMAIL_SMTP_USERNAME("EMAIL_SMTP_USERNAME", "邮件SMTP用户名"),
    EMAIL_SMTP_PASSWORD("EMAIL_SMTP_PASSWORD", "邮件SMTP密码"),
    EMAIL_FROM_ADDRESS("EMAIL_FROM_ADDRESS", "发件人邮箱"),
    EMAIL_FROM_NAME("EMAIL_FROM_NAME", "发件人名称"),
    EMAIL_SMTP_AUTH("EMAIL_SMTP_AUTH", "邮件SMTP认证"),
    EMAIL_SMTP_STARTTLS_ENABLE("EMAIL_SMTP_STARTTLS_ENABLE", "邮件SMTP启用TLS"),

    // 腾讯地图配置
    TENCENT_MAP_KEY("TENCENT_MAP_KEY", "腾讯地图Key"),
    TENCENT_MAP_SECRET_KEY("TENCENT_MAP_SECRET_KEY", "腾讯地图Secret Key"),
    ;

    SysConfigKeyEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 值
     * <p>
     * 获取枚举值
     */
    private final String code;

    /**
     * 描述
     * 获取枚举描述
     */
    private final String name;

    /**
     * 获取值与描述Map
     *
     * @return {@code Map<Integer, String>} 值与描述Map
     */
    public static Map<String, String> getNameMap() {
        return Arrays.stream(SysConfigKeyEnum.values())
                .collect(Collectors.toMap(SysConfigKeyEnum::getCode, SysConfigKeyEnum::getName));
    }

    /**
     * 获取值与枚举对象Map
     *
     * @return {@code Map<Integer, ContentTypeEnum>} 值与枚举对象Map
     */
    public static Map<String, SysConfigKeyEnum> getEnumMap() {
        return Arrays.stream(SysConfigKeyEnum.values())
                .collect(Collectors.toMap(SysConfigKeyEnum::getCode, logicDeleteEnum -> logicDeleteEnum));
    }

    /**
     * 获取枚举对象
     *
     * @param code 枚举值
     * @return 枚举
     */
    public static SysConfigKeyEnum getEnum(String code) {
        return getEnumMap().get(code);
    }
}