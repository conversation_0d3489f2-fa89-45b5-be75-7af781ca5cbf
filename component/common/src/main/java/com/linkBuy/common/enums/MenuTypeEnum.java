package com.linkBuy.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum MenuTypeEnum {

    MENU("MENU", "菜单"),
    API("API", "接口"),
    BUTTON("BUTTON", "按钮");

    MenuTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 值
     * <p>
     * 获取枚举值
     */
    private final String code;

    /**
     * 描述
     * 获取枚举描述
     */
    private final String name;

    /**
     * 获取值与描述Map
     *
     * @return {@code Map<Integer, String>} 值与描述Map
     */
    public static Map<String, String> getNameMap() {
        return Arrays.stream(MenuTypeEnum.values())
                .collect(Collectors.toMap(MenuTypeEnum::getCode, MenuTypeEnum::getName));
    }

    /**
     * 获取值与枚举对象Map
     *
     * @return {@code Map<Integer, ContentTypeEnum>} 值与枚举对象Map
     */
    public static Map<String, MenuTypeEnum> getEnumMap() {
        return Arrays.stream(MenuTypeEnum.values())
                .collect(Collectors.toMap(MenuTypeEnum::getCode, logicDeleteEnum -> logicDeleteEnum));
    }

    /**
     * 获取枚举对象
     *
     * @param code 枚举值
     * @return 枚举
     */
    public static MenuTypeEnum getEnum(String code) {
        return getEnumMap().get(code);
    }
}