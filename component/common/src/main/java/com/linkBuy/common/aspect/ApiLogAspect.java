package com.linkBuy.common.aspect;

import com.linkBuy.common.dto.SessionUser;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;

/**
 * API日志切面
 */
@Aspect
@Component
@Slf4j
public class ApiLogAspect {

    /**
     * 定义切点 - 所有controller包下的类
     */
    @Pointcut("execution(* com.linkBuy.*.controller..*.*(..))")
    public void apiPointcut() {
    }

    /**
     * 环绕通知
     */
    @Around("apiPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();

        // 获取当前请求
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();

        // 获取请求信息
        String requestURI = request.getRequestURI();
        String requestMethod = request.getMethod();
        String remoteAddr = getRemoteAddr(request);

        // 获取类名、方法名
        String className = joinPoint.getTarget().getClass().getName();
        String methodName = joinPoint.getSignature().getName();

        // 获取参数
        Object[] args = joinPoint.getArgs();

        // 获取当前用户信息
        SessionUser user = (SessionUser) request.getAttribute("SESSION_USER");
        String userId = user != null ? user.getUserId().toString() : "未登录";

        // 打印请求日志
        log.info("API请求 | IP: {} | 用户ID: {} | {} {} | {}.{} | 参数: {}",
                remoteAddr, userId, requestMethod, requestURI, className, methodName, Arrays.toString(args));

        // 执行目标方法
        Object result;
        try {
            result = joinPoint.proceed();
        } catch (Exception e) {

            // 计算执行时间
            long endTime = System.currentTimeMillis();
            long executeTime = endTime - startTime;

            // 打印响应日志
            log.error("API异常 | {} {} | 耗时: {}ms", requestMethod, requestURI, executeTime);
            throw e;
        }

        // 计算执行时间
        long endTime = System.currentTimeMillis();
        long executeTime = endTime - startTime;

        // 打印响应日志
        log.info("API响应 | {} {} | 耗时: {}ms", requestMethod, requestURI, executeTime);

        return result;
    }

    /**
     * 异常通知
     */
    @AfterThrowing(pointcut = "apiPointcut()", throwing = "e")
    public void afterThrowing(JoinPoint joinPoint, Throwable e) {
        // 获取当前请求
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();

        // 获取请求信息
        String requestURI = request.getRequestURI();
        String requestMethod = request.getMethod();
        String remoteAddr = getRemoteAddr(request);

        // 获取类名、方法名
        String className = joinPoint.getTarget().getClass().getName();
        String methodName = joinPoint.getSignature().getName();

        // 获取参数
        Object[] args = joinPoint.getArgs();

        // 打印异常日志
        log.error("API异常 | IP: {} | {} {} | {}.{} | 参数: {} | 异常: {}",
                remoteAddr, requestMethod, requestURI, className, methodName, Arrays.toString(args), e.getMessage(), e);
    }

    /**
     * 获取客户端真实IP
     */
    private String getRemoteAddr(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        // 如果是多级代理，取第一个IP
        if (ip != null && ip.indexOf(",") > 0) {
            ip = ip.substring(0, ip.indexOf(","));
        }
        return ip;
    }
} 