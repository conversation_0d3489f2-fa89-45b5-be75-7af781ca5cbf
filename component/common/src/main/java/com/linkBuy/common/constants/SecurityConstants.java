package com.linkBuy.common.constants;

import org.springframework.util.AntPathMatcher;

import java.util.Arrays;
import java.util.List;

/**
 * 安全相关常量
 */
public class SecurityConstants {
    
    private static final AntPathMatcher pathMatcher = new AntPathMatcher();

    public static final String TOKEN_NAME = "token";
    public static final String USER_KEY = "SESSION_USER";
    public static final List<String> WHITE_LIST_URLS = Arrays.asList(
            "/pageInfo",
            "/user/login",
            "/user/superLogin",
            "/common/**",
            "/user/register",
            "/error",
            "/swagger-ui/**",
            "/swagger-resources/**",
            "/v2/api-docs",
            "/webjars/**",
            "/mallInfo/**"
    );
    
    /**
     * 检查给定的路径是否匹配白名单
     */
    public static boolean isWhiteListUrl(String path) {
        return WHITE_LIST_URLS.stream()
                .anyMatch(pattern -> pathMatcher.match(pattern, path));
    }
} 