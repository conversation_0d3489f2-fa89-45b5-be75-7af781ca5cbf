package com.linkBuy.common.event.v2;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 事件处理器注解
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface Handler {
    
    /**
     * 处理器名称
     */
    String value() default "";
    
    /**
     * 是否异步处理
     */
    boolean async() default true;
    
    /**
     * 最大重试次数
     */
    int maxRetries() default 3;
    
    /**
     * 重试间隔（毫秒）
     */
    long retryInterval() default 1000;
    
    /**
     * 是否启用
     */
    boolean enabled() default true;
    
    /**
     * 处理超时时间（毫秒）
     */
    long timeout() default 30000;
    
    /**
     * 是否记录性能指标
     */
    boolean metrics() default true;
} 