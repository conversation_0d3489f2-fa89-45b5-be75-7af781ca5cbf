package com.linkBuy.mysql.dao.service.biz;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linkBuy.mysql.dao.entity.biz.BizFreightTemplateArea;
import com.linkBuy.mysql.dao.mapper.biz.BizFreightTemplateAreaMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 运费模版区域 Service实现类
 */
@Service
public class BizFreightTemplateAreaService extends ServiceImpl<BizFreightTemplateAreaMapper, BizFreightTemplateArea> {

    public List<BizFreightTemplateArea> getByFreightTemplate(Long id){
        LambdaUpdateWrapper<BizFreightTemplateArea> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(BizFreightTemplateArea::getTemplateId,id);
        return baseMapper.selectList(wrapper);
    }
}
