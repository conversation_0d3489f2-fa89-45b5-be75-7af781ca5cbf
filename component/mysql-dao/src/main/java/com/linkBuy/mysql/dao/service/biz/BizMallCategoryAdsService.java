package com.linkBuy.mysql.dao.service.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linkBuy.mysql.dao.entity.biz.BizMallCategoryAds;
import com.linkBuy.mysql.dao.mapper.biz.BizMallCategoryAdsMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商城分类页广告配置Service实现类
 */
@Service
public class BizMallCategoryAdsService extends ServiceImpl<BizMallCategoryAdsMapper, BizMallCategoryAds> {
    
    /**
     * 根据商城ID和分类信息获取广告列表
     */
    public List<BizMallCategoryAds> getByMallIdAndCategory(Long mallId, Long categoryId, String categoryType) {
        if (mallId == null) {
            return null;
        }
        LambdaQueryWrapper<BizMallCategoryAds> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizMallCategoryAds::getMallId, mallId)
               .eq(BizMallCategoryAds::getStatus, 1);
        
        if (categoryId != null) {
            wrapper.eq(BizMallCategoryAds::getCategoryId, categoryId);
        } else {
            wrapper.isNull(BizMallCategoryAds::getCategoryId);
        }
        
        if (categoryType != null) {
            wrapper.eq(BizMallCategoryAds::getCategoryType, categoryType);
        }
        
        wrapper.orderByAsc(BizMallCategoryAds::getSortOrder)
               .orderByDesc(BizMallCategoryAds::getCreateTime);
        return baseMapper.selectList(wrapper);
    }
    
    /**
     * 获取全局默认广告
     */
    public List<BizMallCategoryAds> getGlobalAds(Long mallId) {
        return getByMallIdAndCategory(mallId, null, "global");
    }
    
    /**
     * 根据商城ID获取所有广告（包括禁用的）
     */
    public List<BizMallCategoryAds> getAllByMallId(Long mallId) {
        if (mallId == null) {
            return null;
        }
        LambdaQueryWrapper<BizMallCategoryAds> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizMallCategoryAds::getMallId, mallId)
               .orderByAsc(BizMallCategoryAds::getSortOrder)
               .orderByDesc(BizMallCategoryAds::getCreateTime);
        return baseMapper.selectList(wrapper);
    }
    
    /**
     * 批量删除指定分类的广告
     */
    public boolean deleteByCategoryInfo(Long mallId, Long categoryId, String categoryType) {
        if (mallId == null) {
            return false;
        }
        LambdaQueryWrapper<BizMallCategoryAds> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizMallCategoryAds::getMallId, mallId);
        
        if (categoryId != null) {
            wrapper.eq(BizMallCategoryAds::getCategoryId, categoryId);
        } else {
            wrapper.isNull(BizMallCategoryAds::getCategoryId);
        }
        
        if (categoryType != null) {
            wrapper.eq(BizMallCategoryAds::getCategoryType, categoryType);
        }
        
        return remove(wrapper);
    }
    
    /**
     * 批量删除商城的所有分类页广告
     */
    public boolean deleteByMallId(Long mallId) {
        if (mallId == null) {
            return false;
        }
        LambdaQueryWrapper<BizMallCategoryAds> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizMallCategoryAds::getMallId, mallId);
        return remove(wrapper);
    }
} 