package com.linkBuy.mysql.dao.service.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linkBuy.mysql.dao.entity.biz.BizCompanySpu;
import com.linkBuy.mysql.dao.mapper.biz.BizCompanySpuMapper;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 客户公司SPU Service实现类
 */
@Service
public class BizCompanySpuService extends ServiceImpl<BizCompanySpuMapper, BizCompanySpu> {

    /**
     * 统计客户商品分类数量
     * 按客户ID和分类ID分组统计启用状态的商品数量
     *
     * @return Map<String, Long> key为"companyId_categoryId"，value为商品数量
     */
    public Map<String, Long> getCompanyCategorySpuCount() {
        // 使用Mapper的自定义方法进行聚合查询
        List<Map<String, Object>> results = this.baseMapper.selectCompanyCategorySpuCount();

        Map<String, Long> countMap = new HashMap<>();
        for (Map<String, Object> result : results) {
            Long companyId = (Long) result.get("company_id");
            Long categoryId = (Long) result.get("category_id");

            // 处理count字段
            Object countObj = result.get("spu_count");
            if (countObj != null && companyId != null && categoryId != null) {
                Long count = ((Number) countObj).longValue();
                if (count > 0) {
                    countMap.put(companyId + "_" + categoryId, count);
                }
            }
        }

        return countMap;
    }

    /**
     * 直接使用SQL更新客户SPU的价格和利润率范围
     * 基于客户SKU数据进行聚合计算并更新
     *
     * @return 更新的记录数
     */
    public int updateSpuPriceAndProfitFromSku() {
        return this.baseMapper.updateSpuPriceAndProfitFromSku();
    }

    /**
     * 直接使用SQL更新客户SPU的划线价
     * 基于供应商SKU数据的最小值进行更新
     *
     * @return 更新的记录数
     */
    public int updateSpuStrikethroughPriceFromSupplierSku() {
        return this.baseMapper.updateSpuStrikethroughPriceFromSupplierSku();
    }

    /**
     * 分页查询商品选择器列表（支持复杂排序）
     *
     * @param page          分页参数
     * @param companyId     客户公司ID
     * @param categoryId    分类ID
     * @param spuCode       SPU编码
     * @param supplierId    供应商ID
     * @param platform      平台类型
     * @param minPrice      最低价格
     * @param maxPrice      最高价格
     * @param minProfitRate 最低利润率
     * @param maxProfitRate 最高利润率
     * @param productSpuIds 商品名称匹配的SPU ID列表
     * @param sortField     排序字段
     * @param sortOrder     排序方向
     * @return 分页结果
     */
    public IPage<BizCompanySpu> selectSelectorPage(Page<BizCompanySpu> page, Long companyId, Long categoryId, String spuCode, Long supplierId, Integer platform, BigDecimal minPrice, BigDecimal maxPrice, BigDecimal minProfitRate, BigDecimal maxProfitRate, List<Long> productSpuIds, String sortField, String sortOrder) {

        LambdaQueryWrapper<BizCompanySpu> wrapper = new LambdaQueryWrapper<>();

        // 基础筛选条件
        wrapper.eq(BizCompanySpu::getCompanyId, companyId);
        wrapper.eq(BizCompanySpu::getStatus, 1);

        if (categoryId != null) {
            wrapper.eq(BizCompanySpu::getCategoryId, categoryId);
        }

        if (spuCode != null && !spuCode.trim().isEmpty()) {
            wrapper.like(BizCompanySpu::getSpuCode, spuCode.trim());
        }

        if (supplierId != null) {
            wrapper.eq(BizCompanySpu::getSupplierId, supplierId);
        }

        if (platform != null) {
            wrapper.eq(BizCompanySpu::getSource, platform);
        }

        // 价格筛选：使用价格区间进行筛选
        if (minPrice != null) {
            // 商品的最高价格要大于等于筛选的最低价格
            wrapper.ge(BizCompanySpu::getSalePriceMax, minPrice);
        }

        if (maxPrice != null) {
            // 商品的最低价格要小于等于筛选的最高价格
            wrapper.le(BizCompanySpu::getSalePriceMin, maxPrice);
        }

        // 利润率筛选：使用利润率区间进行筛选
        if (minProfitRate != null) {
            // 商品的最高利润率要大于等于筛选的最低利润率
            wrapper.ge(BizCompanySpu::getProfitRateMax, minProfitRate);
        }

        if (maxProfitRate != null) {
            // 商品的最低利润率要小于等于筛选的最高利润率
            wrapper.le(BizCompanySpu::getProfitRateMin, maxProfitRate);
        }

        if (productSpuIds != null && !productSpuIds.isEmpty()) {
            wrapper.in(BizCompanySpu::getSpuId, productSpuIds);
        }

        // 排序处理
        if (sortField != null && !sortField.trim().isEmpty() && sortOrder != null && !sortOrder.trim().isEmpty()) {

            boolean isAsc = "asc".equalsIgnoreCase(sortOrder);

            switch (sortField) {
                case "spuCode":
                    if (isAsc) {
                        wrapper.orderByAsc(BizCompanySpu::getSpuCode);
                    } else {
                        wrapper.orderByDesc(BizCompanySpu::getSpuCode);
                    }
                    break;
                case "salePriceMin":
                case "salePrice": // 保持向后兼容
                    // 按最低销售价格排序
                    if (isAsc) {
                        wrapper.orderByAsc(BizCompanySpu::getSalePriceMin);
                    } else {
                        wrapper.orderByDesc(BizCompanySpu::getSalePriceMin);
                    }
                    break;
                case "profitRate":
                    // 按最低利润率排序
                    if (isAsc) {
                        wrapper.orderByAsc(BizCompanySpu::getProfitRateMin);
                    } else {
                        wrapper.orderByDesc(BizCompanySpu::getProfitRateMin);
                    }
                    break;
                // case "stock":
                //     // 库存排序需要关联查询，暂时不支持，使用创建时间排序
                //     wrapper.orderByDesc(BizCompanySpu::getCreateTime);
                //     break;
                default:
                    wrapper.orderByDesc(BizCompanySpu::getCreateTime);
                    break;
            }
        } else {
            wrapper.orderByDesc(BizCompanySpu::getCreateTime);
        }
        wrapper.orderByDesc(BizCompanySpu::getId);

        return this.page(page, wrapper);
    }

    /**
     * 根据客户公司ID和标准分类ID获取SPU列表
     * 
     * @param companyId 客户公司ID
     * @param categoryId 标准分类ID
     * @return SPU列表
     */
    public List<BizCompanySpu> getSpusByCategoryId(Long companyId, Long categoryId) {
        if (companyId == null || categoryId == null) {
            return null;
        }
        LambdaQueryWrapper<BizCompanySpu> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizCompanySpu::getCompanyId, companyId)
                .eq(BizCompanySpu::getCategoryId, categoryId)
                .eq(BizCompanySpu::getStatus, 1)
                .orderByDesc(BizCompanySpu::getCreateTime);
        return this.list(wrapper);
    }

    /**
     * 统计指定客户公司下每个分类的SPU数量
     * 使用数据库GROUP BY一次性统计，减少查询次数
     * 
     * @param companyId 客户公司ID
     * @return Map<Long, Long> key为分类ID，value为SPU数量
     */
    public Map<Long, Long> getSpuCountByCategory(Long companyId) {
        if (companyId == null) {
            return new HashMap<>();
        }
        
        // 使用Mapper的自定义方法进行分组统计查询
        List<Map<String, Object>> results = this.baseMapper.selectSpuCountByCategory(companyId);
        
        Map<Long, Long> categoryCountMap = new HashMap<>();
        for (Map<String, Object> result : results) {
            Long categoryId = (Long) result.get("category_id");
            Object countObj = result.get("spu_count");
            
            if (categoryId != null && countObj != null) {
                Long count = ((Number) countObj).longValue();
                if (count > 0) {
                    categoryCountMap.put(categoryId, count);
                }
            }
        }
        
        return categoryCountMap;
    }

    /**
     * 商品选择器分页查询（使用连表查询，支持商品名称搜索）
     *
     * @param page 分页参数
     * @param companyId 客户公司ID
     * @param categoryId 分类ID
     * @param spuCode SPU编码
     * @param supplierId 供应商ID
     * @param platform 平台类型
     * @param minPrice 最低价格
     * @param maxPrice 最高价格
     * @param minProfitRate 最低利润率
     * @param maxProfitRate 最高利润率
     * @param productName 商品名称
     * @param sortField 排序字段
     * @param sortOrder 排序方向
     * @return 分页结果
     */
    public IPage<BizCompanySpu> selectSelectorPageWithJoin(
            Page<BizCompanySpu> page,
            Long companyId,
            Long categoryId,
            String spuCode,
            Long supplierId,
            Integer platform,
            BigDecimal minPrice,
            BigDecimal maxPrice,
            BigDecimal minProfitRate,
            BigDecimal maxProfitRate,
            String productName,
            String sortField,
            String sortOrder) {

        return this.baseMapper.selectSelectorPageWithJoin(
                page, companyId, categoryId, spuCode, supplierId, platform,
                minPrice, maxPrice, minProfitRate, maxProfitRate, productName,
                sortField, sortOrder
        );
    }
}