package com.linkBuy.mysql.dao.entity.biz;

import com.baomidou.mybatisplus.annotation.TableName;
import com.linkBuy.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 页面组件实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_mall_page_widget")
public class BizMallPageWidget extends BaseEntity {
    
    /**
     * 页面ID
     */
    private Long pageId;
    
    /**
     * 组件ID（前端生成的唯一标识）
     */
    private String widgetId;
    
    /**
     * 组件类型
     */
    private String widgetType;
    
    /**
     * 组件数据JSON
     */
    private String widgetData;
    
    /**
     * 排序权重
     */
    private Integer sortOrder;
    
    /**
     * 是否隐藏：0-显示，1-隐藏
     */
    private Integer isHidden;
} 