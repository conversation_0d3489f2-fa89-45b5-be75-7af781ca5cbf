package com.linkBuy.mysql.dao.entity.biz;

import com.baomidou.mybatisplus.annotation.TableName;
import com.linkBuy.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商城底部导航项实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_mall_bottom_nav")
public class BizMallBottomNav extends BaseEntity {
    /**
     * 商城ID
     */
    private Long mallId;
    
    /**
     * 导航文字
     */
    private String navText;
    
    /**
     * 默认图标URL
     */
    private String iconUrl;
    
    /**
     * 选中图标URL
     */
    private String activeIconUrl;
    
    /**
     * 跳转链接
     */
    private String linkUrl;
    
    /**
     * 链接类型：page-页面，url-外链
     */
    private String linkType;
    
    /**
     * 排序权重
     */
    private Integer sortOrder;
    
    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;
} 