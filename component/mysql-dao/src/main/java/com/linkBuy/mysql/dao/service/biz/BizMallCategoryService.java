package com.linkBuy.mysql.dao.service.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linkBuy.mysql.dao.entity.biz.BizMallCategory;
import com.linkBuy.mysql.dao.mapper.biz.BizMallCategoryMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 商城自定义分类Service实现类
 */
@Service
public class BizMallCategoryService extends ServiceImpl<BizMallCategoryMapper, BizMallCategory> {

    /**
     * 根据商城ID获取分类列表
     */
    public List<BizMallCategory> getByMallId(Long mallId) {
        if (mallId == null) {
            return null;
        }
        LambdaQueryWrapper<BizMallCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizMallCategory::getMallId, mallId)
                .orderByAsc(BizMallCategory::getSortOrder)
                .orderByDesc(BizMallCategory::getCreateTime);
        return baseMapper.selectList(wrapper);
    }

    /**
     * 根据商城ID和父分类ID获取子分类列表
     */
    public List<BizMallCategory> getByMallIdAndParentId(Long mallId, Long parentId) {
        if (mallId == null) {
            return null;
        }
        LambdaQueryWrapper<BizMallCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizMallCategory::getMallId, mallId);

        if (parentId == null) {
            wrapper.isNull(BizMallCategory::getParentId);
        } else {
            wrapper.eq(BizMallCategory::getParentId, parentId);
        }

        wrapper.orderByAsc(BizMallCategory::getSortOrder)
                .orderByDesc(BizMallCategory::getCreateTime);
        return baseMapper.selectList(wrapper);
    }

    /**
     * 根据商城ID和分类级别获取分类列表
     */
    public List<BizMallCategory> getByMallIdAndLevel(Long mallId, Integer level) {
        if (mallId == null || level == null) {
            return null;
        }
        LambdaQueryWrapper<BizMallCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizMallCategory::getMallId, mallId)
                .eq(BizMallCategory::getLevel, level)
                .orderByAsc(BizMallCategory::getSortOrder)
                .orderByDesc(BizMallCategory::getCreateTime);
        return baseMapper.selectList(wrapper);
    }

    /**
     * 根据分类编码和商城ID获取分类（用于重复检查）
     */
    public BizMallCategory getByCategoryCodeAndMallId(String categoryCode, Long mallId) {
        if (!StringUtils.hasText(categoryCode) || mallId == null) {
            return null;
        }
        LambdaQueryWrapper<BizMallCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizMallCategory::getCategoryCode, categoryCode)
                .eq(BizMallCategory::getMallId, mallId);
        return baseMapper.selectOne(wrapper);
    }

    /**
     * 根据分类名称和商城ID获取分类（用于重复检查）
     */
    public BizMallCategory getByCategoryNameAndMallId(String categoryName, Long mallId) {
        if (!StringUtils.hasText(categoryName) || mallId == null) {
            return null;
        }
        LambdaQueryWrapper<BizMallCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizMallCategory::getCategoryName, categoryName)
                .eq(BizMallCategory::getMallId, mallId);
        return baseMapper.selectOne(wrapper);
    }

    /**
     * 更新分类状态
     */
    public boolean updateStatus(Long id, Integer status) {
        if (id == null || status == null) {
            return false;
        }
        LambdaUpdateWrapper<BizMallCategory> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(BizMallCategory::getId, id)
                .set(BizMallCategory::getStatus, status);
        return update(wrapper);
    }

    /**
     * 软删除分类
     */
    public boolean softDelete(Long id) {
        if (id == null) {
            return false;
        }
        LambdaUpdateWrapper<BizMallCategory> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(BizMallCategory::getId, id)
                .set(BizMallCategory::getIsDeleted, 1);
        return update(wrapper);
    }

    /**
     * 批量软删除分类
     */
    public boolean batchSoftDelete(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        LambdaUpdateWrapper<BizMallCategory> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(BizMallCategory::getId, ids)
                .set(BizMallCategory::getIsDeleted, 1);
        return update(wrapper);
    }

    /**
     * 根据父分类ID获取子分类数量
     */
    public long countByParentId(Long parentId) {
        if (parentId == null) {
            return 0;
        }
        LambdaQueryWrapper<BizMallCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizMallCategory::getParentId, parentId);
        return baseMapper.selectCount(wrapper);
    }

    /**
     * 更新分类排序
     */
    public boolean updateSortOrder(Long id, Integer sortOrder) {
        if (id == null || sortOrder == null) {
            return false;
        }
        LambdaUpdateWrapper<BizMallCategory> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(BizMallCategory::getId, id)
                .set(BizMallCategory::getSortOrder, sortOrder);
        return update(wrapper);
    }
} 