package com.linkBuy.mysql.dao.service.biz;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linkBuy.mysql.dao.entity.biz.BizSupplierSpu;
import com.linkBuy.mysql.dao.mapper.biz.BizSupplierSpuMapper;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 供应商SPU Service实现类
 */
@Service
public class BizSupplierSpuService extends ServiceImpl<BizSupplierSpuMapper, BizSupplierSpu> {

    /**
     * 统计平台商品分类数量
     * 按分类ID分组统计启用状态的商品数量
     *
     * @return Map<String, Long> key为categoryId，value为商品数量
     */
    public Map<String, Long> getPlatformCategorySpuCount() {
        // 使用Mapper的自定义方法进行聚合查询
        List<Map<String, Object>> results = this.baseMapper.selectPlatformCategorySpuCount();

        Map<String, Long> countMap = new HashMap<>();
        for (Map<String, Object> result : results) {
            Long categoryId = (Long) result.get("category_id");

            // 处理count字段
            Object countObj = result.get("spu_count");
            if (countObj != null && categoryId != null) {
                Long count = ((Number) countObj).longValue();
                if (count > 0) {
                    countMap.put(String.valueOf(categoryId), count);
                }
            }
        }

        return countMap;
    }

    /**
     * 根据供应商ID列表获取SPU列表
     * 
     * @param ids 供应商ID列表
     * @return SPU列表
     */
    public List<BizSupplierSpu> getBySupplierIds(List<Long> ids) {
        LambdaUpdateWrapper<BizSupplierSpu> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(BizSupplierSpu::getSupplierId, ids)
                .eq(BizSupplierSpu::getStatus, 1);
        return baseMapper.selectList(wrapper);
    }

    /**
     * 同步供应商SPU的主图URL
     * 将biz_supplier_spu_img表中的主图URL同步到biz_supplier_spu表的main_img_url字段
     * 使用SQL直接更新，确保数据一致性
     * 
     * @return 更新的记录数
     */
    public int syncMainImageFromSpuImg() {
        return this.baseMapper.syncMainImageFromSpuImg();
    }
}
