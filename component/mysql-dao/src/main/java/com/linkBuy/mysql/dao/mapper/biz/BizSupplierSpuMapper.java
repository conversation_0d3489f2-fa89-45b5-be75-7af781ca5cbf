package com.linkBuy.mysql.dao.mapper.biz;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linkBuy.mysql.dao.entity.biz.BizSupplierSpu;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

/**
 * 供应商SPU Mapper接口
 */
@Mapper
public interface BizSupplierSpuMapper extends BaseMapper<BizSupplierSpu> {

    /**
     * 统计平台商品分类数量
     * 
     * @return 统计结果列表，每个Map包含category_id、spu_count
     */
    @Select("SELECT category_id, COUNT(*) as spu_count " +
            "FROM biz_supplier_spu " +
            "WHERE status = 1 AND category_id IS NOT NULL AND is_deleted = 0 " +
            "GROUP BY category_id " +
            "HAVING COUNT(*) > 0")
    List<Map<String, Object>> selectPlatformCategorySpuCount();

    /**
     * 同步供应商SPU的主图URL
     * 将biz_supplier_spu_img表中的主图URL同步到biz_supplier_spu表的main_img_url字段
     * 只更新不一致的记录，确保数据一致性
     * 
     * @return 更新的记录数
     */
    @Update("UPDATE biz_supplier_spu spu " +
            "INNER JOIN biz_supplier_spu_img img ON spu.id = img.spu_id " +
            "SET spu.main_img_url = img.img_url, " +
            "    spu.update_time = NOW() " +
            "WHERE img.is_main = 1 " +
            "  AND img.is_deleted = 0 " +
            "  AND spu.is_deleted = 0 " +
            "  AND (spu.main_img_url IS NULL OR spu.main_img_url != img.img_url)")
    int syncMainImageFromSpuImg();
} 