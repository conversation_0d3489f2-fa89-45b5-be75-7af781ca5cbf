package com.linkBuy.mysql.dao.service.system;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linkBuy.mysql.dao.entity.system.SysUserRole;
import com.linkBuy.mysql.dao.mapper.system.SysUserRoleMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户角色关联Service实现类
 */
@Service
public class SysUserRoleService extends ServiceImpl<SysUserRoleMapper, SysUserRole> {

    /**
     * 获取角色已关联的用户ID列表
     *
     * @param roleId 角色ID
     * @return 用户ID列表
     */
    public List<Long> getUserIdsByRoleId(Long roleId) {
        LambdaQueryWrapper<SysUserRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserRole::getRoleId, roleId);
        return list(wrapper).stream()
                .map(SysUserRole::getUserId)
                .collect(Collectors.toList());
    }

    /**
     * 更新角色关联用户
     *
     * @param roleId 角色ID
     * @param userIds 新的用户ID列表
     * @return 是否更新成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRoleUsers(Long roleId, List<Long> userIds) {
        // 1. 删除原有用户关联
        LambdaQueryWrapper<SysUserRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserRole::getRoleId, roleId);
        remove(wrapper);

        // 2. 添加新的用户关联
        if (userIds != null && !userIds.isEmpty()) {
            List<SysUserRole> userRoles = userIds.stream()
                    .map(userId -> {
                        SysUserRole userRole = new SysUserRole();
                        userRole.setRoleId(roleId);
                        userRole.setUserId(userId);
                        return userRole;
                    })
                    .collect(Collectors.toList());
            return saveBatch(userRoles);
        }
        return true;
    }

    public List<SysUserRole> listByUserId(Long userId) {
        LambdaQueryWrapper<SysUserRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserRole::getUserId, userId);
        return list(wrapper);
    }

    public void removeByUserIdAndRoleIds(Long userId, List<Long> roleIds) {
        LambdaQueryWrapper<SysUserRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserRole::getUserId, userId)
                .in(SysUserRole::getRoleId, roleIds);
        this.remove(wrapper);
    }
}