package com.linkBuy.mysql.dao.entity.biz;

import com.baomidou.mybatisplus.annotation.TableName;
import com.linkBuy.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * SPU操作日志实体类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_supplier_spu_operation_log")
public class BizSupplierSpuOperationLog extends BaseEntity {

    /**
     * SPU ID
     */
    private Long spuId;

    /**
     * 操作类型：CREATE,UPDATE,DELETE,SUBMIT_REVIEW,REVIEW_PASS,REVIEW_REJECT,WITHDRAW_REVIEW,
     * SUBMIT_PRICE_CHANGE,WITHDRAW_PRICE_CHANGE,PRICE_CHANGE_PASS,PRICE_CHANGE_REJECT,ONLINE,OFFLINE,WAREHOUSE
     */
    private String operationType;

    /**
     * 操作分类：BASIC,REVIEW,PRICE_CHANGE,STATUS
     */
    private String operationCategory;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 操作人类型：SUPPLIER,ADMIN,SYSTEM
     */
    private String operatorType;

    /**
     * 操作描述
     */
    private String operationDesc;

    /**
     * 操作备注/原因
     */
    private String operationComment;

    /**
     * 操作前状态
     */
    private Integer beforeStatus;

    /**
     * 操作后状态
     */
    private Integer afterStatus;

    /**
     * 操作前数据（JSON格式）
     */
    private String beforeData;

    /**
     * 操作后数据（JSON格式）
     */
    private String afterData;

    /**
     * 变更字段列表（JSON格式）
     */
    private String changeFields;

    /**
     * 关联的申请ID（改价申请等）
     */
    private Long relatedRequestId;

    /**
     * 关联的申请单号
     */
    private String relatedRequestNo;

    /**
     * 操作时间
     */
    private LocalDateTime operationTime;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 客户端信息
     */
    private String clientInfo;

    /**
     * 操作类型枚举
     */
    public enum OperationType {
        CREATE("CREATE", "创建"),
        UPDATE("UPDATE", "更新"),
        DELETE("DELETE", "删除"),
        SUBMIT_REVIEW("SUBMIT_REVIEW", "提交审核"),
        REVIEW_PASS("REVIEW_PASS", "审核通过"),
        REVIEW_REJECT("REVIEW_REJECT", "审核拒绝"),
        WITHDRAW_REVIEW("WITHDRAW_REVIEW", "撤回审核"),
        SUBMIT_PRICE_CHANGE("SUBMIT_PRICE_CHANGE", "提交改价申请"),
        WITHDRAW_PRICE_CHANGE("WITHDRAW_PRICE_CHANGE", "撤回改价申请"),
        PRICE_CHANGE_PASS("PRICE_CHANGE_PASS", "改价申请通过"),
        PRICE_CHANGE_REJECT("PRICE_CHANGE_REJECT", "改价申请拒绝"),
        ONLINE("ONLINE", "上架"),
        OFFLINE("OFFLINE", "下架"),
        WAREHOUSE("WAREHOUSE", "放入仓库"),
        COMPANY_REVIEW_PASS("COMPANY_REVIEW_PASS", "客户审核通过"),
        COMPANY_REVIEW_REJECT("COMPANY_REVIEW_REJECT", "客户审核拒绝"),
        COMPANY_BATCH_REVIEW("COMPANY_BATCH_REVIEW", "客户批量审核"),
        COMPANY_PRICE_UPDATE("COMPANY_PRICE_UPDATE", "客户调价"),
        COMPANY_BATCH_PRICE_UPDATE("COMPANY_BATCH_PRICE_UPDATE", "客户批量调价"),
        COMPANY_STATUS_UPDATE("COMPANY_STATUS_UPDATE", "客户状态变更"),
        COMPANY_BATCH_STATUS_UPDATE("COMPANY_BATCH_STATUS_UPDATE", "客户批量状态变更"),
        COMPANY_PRICE_CHANGE_REVIEW("COMPANY_PRICE_CHANGE_REVIEW", "客户价格变更审核");

        private final String code;
        private final String desc;

        OperationType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 操作分类枚举
     */
    public enum OperationCategory {
        BASIC("BASIC", "基础操作"),
        REVIEW("REVIEW", "审核操作"),
        PRICE_CHANGE("PRICE_CHANGE", "改价操作"),
        STATUS("STATUS", "状态操作"),
        COMPANY_MANAGEMENT("COMPANY_MANAGEMENT", "客户管理操作");

        private final String code;
        private final String desc;

        OperationCategory(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 操作人类型枚举
     */
    public enum OperatorType {
        SUPPLIER("SUPPLIER", "供应商"),
        ADMIN("ADMIN", "管理员"),
        SYSTEM("SYSTEM", "系统"),
        CUSTOMER("CUSTOMER", "客户"),
        COMPANY_ADMIN("COMPANY_ADMIN", "客户管理员");

        private final String code;
        private final String desc;

        OperatorType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
} 