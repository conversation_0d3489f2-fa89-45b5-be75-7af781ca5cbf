package com.linkBuy.mysql.dao.service.system;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linkBuy.mysql.dao.entity.system.SysFsAccountTag;
import com.linkBuy.mysql.dao.mapper.system.SysFsAccountTagMapper;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotBlank;

@Service
public class SysFsAccountTagService extends ServiceImpl<SysFsAccountTagMapper, SysFsAccountTag> {
    public boolean removeByIdAndAccountCode(@NotBlank(message = "ID不能为空") String id, @NotBlank(message = "accountCode不能为空") String accountCode) {
        LambdaQueryWrapper<SysFsAccountTag> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysFsAccountTag::getId, id);
        wrapper.eq(SysFsAccountTag::getFsAccountCode, accountCode);

        return this.remove(wrapper);
    }
} 