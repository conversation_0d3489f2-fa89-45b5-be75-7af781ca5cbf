package com.linkBuy.mysql.dao.entity.biz;

import com.baomidou.mybatisplus.annotation.*;
import com.linkBuy.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 优惠券分类关联实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("biz_mall_coupon_category")
public class BizMallCouponCategory extends BaseEntity {

    /**
     * 优惠券ID
     */
    @TableField("coupon_id")
    private Long couponId;

    /**
     * 分类ID
     */
    @TableField("category_id")
    private Long categoryId;

    /**
     * 关联类型：1-可用分类，2-不可用分类
     */
    @TableField("relation_type")
    private Integer relationType;
}