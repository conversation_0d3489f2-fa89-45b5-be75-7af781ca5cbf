package com.linkBuy.mysql.dao.entity.biz;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.linkBuy.common.entity.BaseEntity;

import java.util.List;

/**
 * 商城自定义分类实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_mall_category")
public class BizMallCategory extends BaseEntity {
    
    /**
     * 商城ID
     */
    private Long mallId;
    
    /**
     * 分类编码
     */
    private String categoryCode;
    
    /**
     * 分类名称
     */
    private String categoryName;
    
    /**
     * 父分类ID
     */
    private Long parentId;
    
    /**
     * 分类级别
     */
    private Integer level;
    
    /**
     * 排序权重
     */
    private Integer sortOrder;
    
    /**
     * 图标
     */
    private String icon;
    
    /**
     * 描述
     */
    @TableField("`description`")
    private String description;
    
    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;
    
    /**
     * 子分类列表，非数据库字段
     */
    @TableField(exist = false)
    private List<BizMallCategory> children;
    
    /**
     * 绑定的标准商品分类列表，非数据库字段
     */
    @TableField(exist = false)
    private List<Long> boundProductCategoryIds;
} 