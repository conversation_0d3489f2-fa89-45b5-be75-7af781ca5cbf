package com.linkBuy.mysql.dao.service.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linkBuy.mysql.dao.entity.biz.BizSupplierUser;
import com.linkBuy.mysql.dao.mapper.biz.BizSupplierUserMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 供应商用户Service实现类
 */
@Service
public class BizSupplierUserService extends ServiceImpl<BizSupplierUserMapper, BizSupplierUser> {
    
    public BizSupplierUser getByUsername(String username) {
        if (!StringUtils.hasText(username)) {
            return null;
        } else {
            LambdaQueryWrapper<BizSupplierUser> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(BizSupplierUser::getUsername, username);
            return baseMapper.selectOne(wrapper);
        }
    }
    
    /**
     * 根据手机号查询供应商用户
     */
    public BizSupplierUser getByMobile(String mobile) {
        if (!StringUtils.hasText(mobile)) {
            return null;
        }
        LambdaQueryWrapper<BizSupplierUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizSupplierUser::getMobile, mobile);
        return baseMapper.selectOne(wrapper);
    }
    
    /**
     * 根据供应商ID查询用户列表
     */
    public List<BizSupplierUser> getBySupplierId(Long supplierId) {
        if (supplierId == null) {
            return null;
        }
        LambdaQueryWrapper<BizSupplierUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizSupplierUser::getSupplierId, supplierId);

        wrapper.orderByDesc(BizSupplierUser::getStatus);
        wrapper.orderByAsc(BizSupplierUser::getCreateTime);

        return baseMapper.selectList(wrapper);
    }
    
    /**
     * 禁用供应商的所有用户
     */
    public void disableUsersBySupplierId(Long supplierId) {
        if (supplierId == null) {
            return;
        }
        LambdaUpdateWrapper<BizSupplierUser> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(BizSupplierUser::getSupplierId, supplierId);
        wrapper.set(BizSupplierUser::getStatus, 0); // 0表示禁用
        baseMapper.update(null, wrapper);
    }
    
    /**
     * 根据供应商ID和手机号禁用用户
     */
    public void disableUserBySupplierIdAndMobile(Long supplierId, String mobile) {
        if (supplierId == null || !StringUtils.hasText(mobile)) {
            return;
        }
        LambdaUpdateWrapper<BizSupplierUser> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(BizSupplierUser::getSupplierId, supplierId);
        wrapper.eq(BizSupplierUser::getMobile, mobile);
        wrapper.set(BizSupplierUser::getStatus, 0); // 0表示禁用
        baseMapper.update(null, wrapper);
    }
} 