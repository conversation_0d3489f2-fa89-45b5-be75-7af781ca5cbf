package com.linkBuy.mysql.dao.mapper.biz;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linkBuy.mysql.dao.entity.biz.BizCompanySpu;
import com.linkBuy.mysql.dao.vo.mall.MallSpuBaseVO;
import com.linkBuy.mysql.dao.vo.mall.MallCategorySpuVO;
import com.linkBuy.mysql.dao.vo.mall.MallSkuDetailVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 商城商品视图查询Mapper接口
 * 基于商城商品视图设计方案实现的4类核心查询API
 */
@Mapper
public interface MallProductViewMapper extends BaseMapper<BizCompanySpu> {

    /**
     * 1. 分类商品查询API
     * 根据商城装修配置查询分类商品（支持自定义分类和标准分类）
     *
     * @param page 分页参数
     * @param mallId 商城ID
     * @param categoryId 分类ID
     * @param categoryType 分类类型：custom-自定义分类，standard-标准分类
     * @return 分类商品分页结果
     */
    IPage<MallCategorySpuVO> selectCategoryProducts(
            Page<MallCategorySpuVO> page,
            @Param("mallId") Long mallId,
            @Param("categoryId") Long categoryId,
            @Param("categoryType") String categoryType
    );

    /**
     * 2. 商品搜索API - 全文搜索
     * 支持搜索未绑定自定义分类的商品
     *
     * @param page 分页参数
     * @param companyId 客户ID
     * @param keyword 搜索关键词
     * @param useFullText 是否使用全文搜索
     * @return 搜索结果分页
     */
    IPage<MallSpuBaseVO> searchProducts(
            Page<MallSpuBaseVO> page,
            @Param("companyId") Long companyId,
            @Param("keyword") String keyword,
            @Param("useFullText") Boolean useFullText
    );

    /**
     * 3. 分类内搜索API - 自定义分类内搜索
     *
     * @param page 分页参数
     * @param mallId 商城ID
     * @param categoryId 自定义分类ID
     * @param keyword 搜索关键词
     * @return 搜索结果分页
     */
    IPage<MallCategorySpuVO> searchInCustomCategory(
            Page<MallCategorySpuVO> page,
            @Param("mallId") Long mallId,
            @Param("categoryId") Long categoryId,
            @Param("keyword") String keyword
    );

    /**
     * 3. 分类内搜索API - 标准分类内搜索
     *
     * @param page 分页参数
     * @param companyId 客户ID
     * @param categoryId 标准分类ID
     * @param keyword 搜索关键词
     * @return 搜索结果分页
     */
    IPage<MallSpuBaseVO> searchInStandardCategory(
            Page<MallSpuBaseVO> page,
            @Param("companyId") Long companyId,
            @Param("categoryId") Long categoryId,
            @Param("keyword") String keyword
    );

    /**
     * 4. 商品详情API - 获取商品基础信息
     *
     * @param companySpuId 客户商品ID
     * @param companyId 客户ID
     * @return 商品基础信息
     */
    MallSpuBaseVO getProductDetail(
            @Param("companySpuId") Long companySpuId,
            @Param("companyId") Long companyId
    );

    /**
     * 4. 商品详情API - 获取商品SKU列表
     *
     * @param companySpuId 客户商品ID
     * @return SKU详情列表
     */
    List<MallSkuDetailVO> getProductSkus(@Param("spuId") Long companySpuId);

    /**
     * 获取商城商品基础信息列表（用于缓存预热等场景）
     *
     * @param companyId 客户ID
     * @param limit 限制数量
     * @return 商品基础信息列表
     */
    List<MallSpuBaseVO> selectMallSpuBaseList(
            @Param("companyId") Long companyId,
            @Param("limit") Integer limit
    );

    /**
     * 根据商品ID列表批量获取商品信息
     *
     * @param companySpuIds 商品ID列表
     * @return 商品基础信息列表
     */
    List<MallSpuBaseVO> selectMallSpuBaseByIds(@Param("companySpuIds") List<Long> companySpuIds);

    // ==================== 支持筛选和排序的参数化查询方法 ====================

    /**
     * 分类商品查询API - 支持筛选和排序
     *
     * @param mallId 商城ID
     * @param categoryId 分类ID
     * @param categoryType 分类类型
     * @param keyword 搜索关键词
     * @param minPrice 最低价格
     * @param maxPrice 最高价格
     * @param orderBy 排序字段
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 分类商品列表
     */
    List<MallCategorySpuVO> selectCategoryProductsWithParams(
            @Param("mallId") Long mallId,
            @Param("categoryId") Long categoryId,
            @Param("categoryType") String categoryType,
            @Param("keyword") String keyword,
            @Param("minPrice") BigDecimal minPrice,
            @Param("maxPrice") BigDecimal maxPrice,
            @Param("orderBy") String orderBy,
            @Param("offset") Integer offset,
            @Param("limit") Integer limit
    );

    /**
     * 分类商品查询API - 计算总数
     */
    Long countCategoryProducts(
            @Param("mallId") Long mallId,
            @Param("categoryId") Long categoryId,
            @Param("categoryType") String categoryType,
            @Param("keyword") String keyword,
            @Param("minPrice") BigDecimal minPrice,
            @Param("maxPrice") BigDecimal maxPrice
    );

    /**
     * 商品搜索API - 支持筛选和排序
     */
    List<MallSpuBaseVO> searchProductsWithParams(
            @Param("companyId") Long companyId,
            @Param("keyword") String keyword,
            @Param("useFullText") Boolean useFullText,
            @Param("minPrice") BigDecimal minPrice,
            @Param("maxPrice") BigDecimal maxPrice,
            @Param("orderBy") String orderBy,
            @Param("offset") Integer offset,
            @Param("limit") Integer limit
    );

    /**
     * 商品搜索API - 计算总数
     */
    Long countSearchProducts(
            @Param("companyId") Long companyId,
            @Param("keyword") String keyword,
            @Param("useFullText") Boolean useFullText,
            @Param("minPrice") BigDecimal minPrice,
            @Param("maxPrice") BigDecimal maxPrice
    );

    /**
     * 获取商城商品基础信息列表 - 支持筛选和排序
     */
    List<MallSpuBaseVO> selectMallSpuBaseListWithParams(
            @Param("companyId") Long companyId,
            @Param("minPrice") BigDecimal minPrice,
            @Param("maxPrice") BigDecimal maxPrice,
            @Param("orderBy") String orderBy,
            @Param("offset") Integer offset,
            @Param("limit") Integer limit
    );

    /**
     * 获取商城商品基础信息列表 - 计算总数
     */
    Long countMallSpuBaseList(
            @Param("companyId") Long companyId,
            @Param("minPrice") BigDecimal minPrice,
            @Param("maxPrice") BigDecimal maxPrice
    );
} 