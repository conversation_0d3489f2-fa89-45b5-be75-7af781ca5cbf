package com.linkBuy.mysql.dao.entity.biz;

import com.baomidou.mybatisplus.annotation.TableName;
import com.linkBuy.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户用户实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_company_user")
public class BizCompanyUser extends BaseEntity {
    private Long companyId;
    private String username;
    private String password;
    private String email;
    private String mobile;
    private Integer status;
} 