package com.linkBuy.mysql.dao.service.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linkBuy.mysql.dao.entity.biz.BizCompanyUser;
import com.linkBuy.mysql.dao.entity.biz.BizSupplierUser;
import com.linkBuy.mysql.dao.mapper.biz.BizCompanyUserMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 客户用户Service实现类
 */
@Service
public class BizCompanyUserService extends ServiceImpl<BizCompanyUserMapper, BizCompanyUser> {
    
    public BizCompanyUser getByUsername(String username) {
        if (!StringUtils.hasText(username)) {
            return null;
        } else {
            LambdaQueryWrapper<BizCompanyUser> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(BizCompanyUser::getUsername, username);
            return baseMapper.selectOne(wrapper);
        }
    }
    
    /**
     * 根据手机号查询客户用户
     */
    public BizCompanyUser getByMobile(String mobile) {
        if (!StringUtils.hasText(mobile)) {
            return null;
        }
        LambdaQueryWrapper<BizCompanyUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizCompanyUser::getMobile, mobile);
        return baseMapper.selectOne(wrapper);
    }
    
    /**
     * 根据客户ID查询用户列表
     */
    public List<BizCompanyUser> getByCompanyId(Long companyId) {
        if (companyId == null) {
            return null;
        }
        LambdaQueryWrapper<BizCompanyUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizCompanyUser::getCompanyId, companyId);
        wrapper.orderByDesc(BizCompanyUser::getStatus);
        wrapper.orderByAsc(BizCompanyUser::getCreateTime);
        return baseMapper.selectList(wrapper);
    }
    
    /**
     * 禁用客户的所有用户
     */
    public void disableUsersByCompanyId(Long companyId) {
        if (companyId == null) {
            return;
        }
        LambdaUpdateWrapper<BizCompanyUser> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(BizCompanyUser::getCompanyId, companyId);
        wrapper.set(BizCompanyUser::getStatus, 0); // 0表示禁用
        baseMapper.update(null, wrapper);
    }
    
    /**
     * 根据客户ID和手机号禁用用户
     */
    public void disableUserByCompanyIdAndMobile(Long companyId, String mobile) {
        if (companyId == null || !StringUtils.hasText(mobile)) {
            return;
        }
        LambdaUpdateWrapper<BizCompanyUser> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(BizCompanyUser::getCompanyId, companyId);
        wrapper.eq(BizCompanyUser::getMobile, mobile);
        wrapper.set(BizCompanyUser::getStatus, 0); // 0表示禁用
        baseMapper.update(null, wrapper);
    }
} 