package com.linkBuy.mysql.dao.entity.system;

import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import com.linkBuy.common.entity.BaseEntity;
import lombok.EqualsAndHashCode;

/**
 * 快递公司实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_gen_express_company")
public class GenExpressCompany extends BaseEntity {
    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 公司编码
     */
    private String companyCode;

    /**
     * 公司图标
     */
    private String icon;

    /**
     * 是否启用
     */
    private Boolean isEnabled;
}