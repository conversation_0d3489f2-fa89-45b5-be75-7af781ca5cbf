package com.linkBuy.mysql.dao.entity.system;

import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import com.linkBuy.common.entity.BaseEntity;
import lombok.EqualsAndHashCode;

/**
 * 系统字典项实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_dict_item")
public class SysDictItem extends BaseEntity {
    
    /**
     * 字典ID
     */
    private Long dictId;
    
    /**
     * 项名称
     */
    private String itemName;
    
    /**
     * 项值
     */
    private String itemValue;
    
    /**
     * 排序
     */
    private Integer sortOrder;
} 