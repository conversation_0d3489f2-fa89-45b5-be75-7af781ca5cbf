package com.linkBuy.mysql.dao.mapper.biz;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linkBuy.mysql.dao.entity.biz.BizMallCategorySpu;
import com.linkBuy.mysql.dao.vo.mall.MallCategorySpuVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 商城自定义分类与SPU绑定关系Mapper接口
 */
@Mapper
public interface BizMallCategorySpuMapper extends BaseMapper<BizMallCategorySpu> {

    /**
     * 分页查询商城分类下的商品
     *
     * @param page 分页参数
     * @param mallId 商城ID
     * @param categoryId 分类ID
     * @return 分类商品分页结果
     */
    IPage<MallCategorySpuVO> selectCategorySpuPage(
            Page<MallCategorySpuVO> page,
            @Param("mallId") Long mallId,
            @Param("categoryId") Long categoryId
    );

    /**
     * 获取商城分类下的商品数量
     *
     * @param mallId 商城ID
     * @param categoryId 分类ID
     * @return 商品数量
     */
    @Select("SELECT COUNT(*) FROM biz_mall_category_spu mcs " +
            "JOIN biz_company_spu cs ON mcs.company_spu_id = cs.id " +
            "WHERE mcs.mall_id = #{mallId} " +
            "AND mcs.mall_category_id = #{categoryId} " +
            "AND mcs.is_deleted = 0 " +
            "AND cs.is_deleted = 0 " +
            "AND cs.status = 1")
    Integer countCategoryProducts(@Param("mallId") Long mallId, @Param("categoryId") Long categoryId);

    /**
     * 获取商城下所有分类的商品数量统计
     *
     * @param mallId 商城ID
     * @return 分类商品数量统计
     */
    @Select("SELECT mcs.mall_category_id, COUNT(*) as product_count " +
            "FROM biz_mall_category_spu mcs " +
            "JOIN biz_company_spu cs ON mcs.company_spu_id = cs.id " +
            "WHERE mcs.mall_id = #{mallId} " +
            "AND mcs.is_deleted = 0 " +
            "AND cs.is_deleted = 0 " +
            "AND cs.status = 1 " +
            "GROUP BY mcs.mall_category_id")
    List<java.util.Map<String, Object>> countProductsByCategory(@Param("mallId") Long mallId);

    /**
     * 获取推荐商品列表
     *
     * @param mallId 商城ID
     * @param limit 限制数量
     * @return 推荐商品列表
     */
    List<MallCategorySpuVO> selectFeaturedProducts(
            @Param("mallId") Long mallId,
            @Param("limit") Integer limit
    );

    /**
     * 批量更新商品在分类中的排序
     *
     * @param mallId 商城ID
     * @param categoryId 分类ID
     * @param spuSortList 商品排序列表
     * @return 更新条数
     */
    int batchUpdateSortOrder(
            @Param("mallId") Long mallId,
            @Param("categoryId") Long categoryId,
            @Param("spuSortList") List<java.util.Map<String, Object>> spuSortList
    );

    /**
     * 检查商品是否已绑定到指定分类
     *
     * @param mallId 商城ID
     * @param categoryId 分类ID
     * @param companySpuId 商品ID
     * @return 是否已绑定
     */
    @Select("SELECT COUNT(*) > 0 FROM biz_mall_category_spu " +
            "WHERE mall_id = #{mallId} " +
            "AND mall_category_id = #{categoryId} " +
            "AND company_spu_id = #{companySpuId} " +
            "AND is_deleted = 0")
    Boolean isProductBoundToCategory(
            @Param("mallId") Long mallId,
            @Param("categoryId") Long categoryId,
            @Param("companySpuId") Long companySpuId
    );
} 