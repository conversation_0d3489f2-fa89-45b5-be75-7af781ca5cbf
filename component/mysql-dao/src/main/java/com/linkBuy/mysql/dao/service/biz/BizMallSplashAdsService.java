package com.linkBuy.mysql.dao.service.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linkBuy.mysql.dao.entity.biz.BizMallSplashAds;
import com.linkBuy.mysql.dao.mapper.biz.BizMallSplashAdsMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商城开屏广告内容Service实现类
 */
@Service
public class BizMallSplashAdsService extends ServiceImpl<BizMallSplashAdsMapper, BizMallSplashAds> {
    
    /**
     * 根据商城ID获取开屏广告列表
     */
    public List<BizMallSplashAds> getByMallId(Long mallId) {
        if (mallId == null) {
            return null;
        }
        LambdaQueryWrapper<BizMallSplashAds> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizMallSplashAds::getMallId, mallId)
               .eq(BizMallSplashAds::getStatus, 1)
               .orderByAsc(BizMallSplashAds::getSortOrder)
               .orderByDesc(BizMallSplashAds::getCreateTime);
        return baseMapper.selectList(wrapper);
    }
    
    /**
     * 根据商城ID获取所有开屏广告（包括禁用的）
     */
    public List<BizMallSplashAds> getAllByMallId(Long mallId) {
        if (mallId == null) {
            return null;
        }
        LambdaQueryWrapper<BizMallSplashAds> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizMallSplashAds::getMallId, mallId)
               .orderByAsc(BizMallSplashAds::getSortOrder)
               .orderByDesc(BizMallSplashAds::getCreateTime);
        return baseMapper.selectList(wrapper);
    }
    
    /**
     * 更新广告状态
     */
    public boolean updateStatus(Long id, Integer status) {
        if (id == null || status == null) {
            return false;
        }
        LambdaUpdateWrapper<BizMallSplashAds> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(BizMallSplashAds::getId, id)
               .set(BizMallSplashAds::getStatus, status);
        return update(wrapper);
    }
    
    /**
     * 批量删除商城的开屏广告
     */
    public boolean deleteByMallId(Long mallId) {
        if (mallId == null) {
            return false;
        }
        LambdaQueryWrapper<BizMallSplashAds> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizMallSplashAds::getMallId, mallId);
        return remove(wrapper);
    }
} 