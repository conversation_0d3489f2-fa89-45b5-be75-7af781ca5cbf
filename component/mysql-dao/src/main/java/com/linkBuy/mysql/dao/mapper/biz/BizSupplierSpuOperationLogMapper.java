package com.linkBuy.mysql.dao.mapper.biz;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linkBuy.mysql.dao.entity.biz.BizSupplierSpuOperationLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * SPU操作日志 Mapper接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface BizSupplierSpuOperationLogMapper extends BaseMapper<BizSupplierSpuOperationLog> {

    /**
     * 根据SPU ID查询操作日志列表
     * 
     * @param spuId SPU ID
     * @param limit 限制条数
     * @return 操作日志列表
     */
    @Select("SELECT * FROM biz_supplier_spu_operation_log " +
            "WHERE spu_id = #{spuId} AND is_deleted = 0 " +
            "ORDER BY operation_time DESC " +
            "LIMIT #{limit}")
    List<BizSupplierSpuOperationLog> selectBySpuIdWithLimit(@Param("spuId") Long spuId, @Param("limit") Integer limit);

    /**
     * 根据操作类型查询操作日志列表
     * 
     * @param operationType 操作类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制条数
     * @return 操作日志列表
     */
    @Select("SELECT * FROM biz_supplier_spu_operation_log " +
            "WHERE operation_type = #{operationType} " +
            "AND operation_time >= #{startTime} " +
            "AND operation_time <= #{endTime} " +
            "AND is_deleted = 0 " +
            "ORDER BY operation_time DESC " +
            "LIMIT #{limit}")
    List<BizSupplierSpuOperationLog> selectByOperationTypeAndTimeRange(
            @Param("operationType") String operationType,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("limit") Integer limit);

    /**
     * 根据操作人ID查询操作日志列表
     * 
     * @param operatorId 操作人ID
     * @param limit 限制条数
     * @return 操作日志列表
     */
    @Select("SELECT * FROM biz_supplier_spu_operation_log " +
            "WHERE operator_id = #{operatorId} AND is_deleted = 0 " +
            "ORDER BY operation_time DESC " +
            "LIMIT #{limit}")
    List<BizSupplierSpuOperationLog> selectByOperatorIdWithLimit(@Param("operatorId") Long operatorId, @Param("limit") Integer limit);

    /**
     * 根据关联申请ID查询操作日志列表
     * 
     * @param relatedRequestId 关联申请ID
     * @return 操作日志列表
     */
    @Select("SELECT * FROM biz_supplier_spu_operation_log " +
            "WHERE related_request_id = #{relatedRequestId} AND is_deleted = 0 " +
            "ORDER BY operation_time DESC")
    List<BizSupplierSpuOperationLog> selectByRelatedRequestId(@Param("relatedRequestId") Long relatedRequestId);
} 