package com.linkBuy.mysql.dao.entity.system;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.linkBuy.common.entity.BaseEntity;

import java.util.List;

/**
 * 商品分类实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_gen_product_category")
public class GenProductCategory extends BaseEntity {

    /**
     * 分类编码
     */
    private String categoryCode;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 父分类ID
     */
    private Long parentId;

    /**
     * 分类等级
     */
    private Integer level;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 图标
     */
    private String icon;

    /**
     * 描述
     */
    private String description;

    /**
     * 子分类列表，非数据库字段
     */
    @TableField(exist = false)
    private List<GenProductCategory> children;
    /**
     * 分类下spu总数
     */
    @TableField(exist = false)
    private Long spuCount;
} 