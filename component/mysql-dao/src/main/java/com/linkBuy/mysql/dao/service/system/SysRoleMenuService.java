package com.linkBuy.mysql.dao.service.system;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linkBuy.mysql.dao.entity.system.SysRoleMenu;
import com.linkBuy.mysql.dao.mapper.system.SysRoleMenuMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色权限关联Service实现类
 */
@Service
public class SysRoleMenuService extends ServiceImpl<SysRoleMenuMapper, SysRoleMenu> {

    /**
     * 获取角色已分配的权限ID列表
     *
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    public List<Long> getMenuIdsByRoleId(Long roleId) {
        LambdaQueryWrapper<SysRoleMenu> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRoleMenu::getRoleId, roleId);
        return list(wrapper).stream()
                .map(SysRoleMenu::getMenuId)
                .collect(Collectors.toList());
    }

    /**
     * 获取角色已分配的权限ID列表（与getMenuIdsByRoleId一致，为兼容Controller调用）
     */
    public List<Long> getPermissionIdsByRoleId(Long roleId) {
        return getMenuIdsByRoleId(roleId);
    }

    /**
     * 更新角色权限
     *
     * @param roleId 角色ID
     * @param menuIds 新的菜单ID列表
     * @return 是否更新成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRolePermissions(Long roleId, List<Long> menuIds) {
        // 1. 删除原有权限关联
        LambdaQueryWrapper<SysRoleMenu> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRoleMenu::getRoleId, roleId);
        remove(wrapper);

        // 2. 添加新的权限关联
        if (menuIds != null && !menuIds.isEmpty()) {
            List<SysRoleMenu> rolePermissions = menuIds.stream()
                    .map(menuId -> {
                        SysRoleMenu rolePermission = new SysRoleMenu();
                        rolePermission.setRoleId(roleId);
                        rolePermission.setMenuId(menuId);
                        return rolePermission;
                    })
                    .collect(Collectors.toList());
            return saveBatch(rolePermissions);
        }
        return true;
    }

    /**
     * 批量新增角色权限
     */
    @Transactional(rollbackFor = Exception.class)
    public void addRolePermissions(Long roleId, List<Long> permissionIds) {
        if (permissionIds == null || permissionIds.isEmpty()) return;
        List<SysRoleMenu> list = permissionIds.stream()
                .map(menuId -> {
                    SysRoleMenu rm = new SysRoleMenu();
                    rm.setRoleId(roleId);
                    rm.setMenuId(menuId);
                    return rm;
                })
                .collect(Collectors.toList());
        this.saveBatch(list);
    }

    /**
     * 批量删除角色权限
     */
    @Transactional(rollbackFor = Exception.class)
    public void removeRolePermissions(Long roleId, List<Long> permissionIds) {
        if (permissionIds == null || permissionIds.isEmpty()) return;
        LambdaQueryWrapper<SysRoleMenu> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRoleMenu::getRoleId, roleId)
               .in(SysRoleMenu::getMenuId, permissionIds);
        this.baseMapper.delete(wrapper);
    }

    /**
     * 根据角色ID列表查询角色-菜单关联记录
     *
     * @param roleIds 角色ID列表
     * @return 角色-菜单关联记录列表
     */
    public List<SysRoleMenu> listByRoleIds(List<Long> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        LambdaQueryWrapper<SysRoleMenu> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(SysRoleMenu::getRoleId, roleIds);
        
        return list(wrapper);
    }
}