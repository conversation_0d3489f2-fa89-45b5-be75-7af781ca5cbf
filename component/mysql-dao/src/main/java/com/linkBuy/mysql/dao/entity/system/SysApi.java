package com.linkBuy.mysql.dao.entity.system;

import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import com.linkBuy.common.entity.BaseEntity;
import lombok.EqualsAndHashCode;

/**
 * 系统API实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_api")
public class SysApi extends BaseEntity {
    
    /**
     * API名称
     */
    private String apiName;
    
    /**
     * API路径
     */
    private String apiPath;
    
    /**
     * API方法
     */
    private String apiMethod;
    
    /**
     * 描述
     */
    private String description;
} 