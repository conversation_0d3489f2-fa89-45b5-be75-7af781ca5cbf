package com.linkBuy.mysql.dao.entity.biz;

import com.baomidou.mybatisplus.annotation.TableName;
import com.linkBuy.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 供应商资质实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_supplier_license")
public class BizSupplierLicense extends BaseEntity {
    private Long supplierId;
    private String licenseName;
    private String licenseType;
    private String licenseNumber;
    private String filePath;
    private LocalDate issueDate;
    private LocalDate expireDate;
    private String issuingAuthority;
    private Integer status;
    private String remark;
} 