package com.linkBuy.mysql.dao.service.system;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linkBuy.mysql.dao.entity.system.GenUnit;
import com.linkBuy.mysql.dao.mapper.system.GenUnitMapper;

import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 计量单位Service实现类
 */
@Service
public class GenUnitService extends ServiceImpl<GenUnitMapper, GenUnit> {
    
    /**
     * 获取所有计量单位列表，按排序字段排序
     *
     * @return 计量单位列表
     */
    public List<GenUnit> listAllUnits() {
        LambdaQueryWrapper<GenUnit> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByAsc(GenUnit::getSortOrder);
        return this.list(wrapper);
    }
} 