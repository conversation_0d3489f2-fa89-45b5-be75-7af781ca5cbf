package com.linkBuy.mysql.dao.entity.biz;

import com.baomidou.mybatisplus.annotation.TableName;
import com.linkBuy.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 价格规格修改申请主表实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_price_spec_change_request")
public class BizPriceSpecChangeRequest extends BaseEntity {

    /**
     * 申请单号，格式：PSCR+时间戳+随机数
     */
    private String requestNo;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 提交人ID
     */
    private Long submitterId;

    /**
     * 提交人姓名
     */
    private String submitterName;

    /**
     * 申请类型：1-价格修改，2-规格调整，3-价格+规格
     */
    private Integer requestType;

    /**
     * 申请说明/原因
     */
    private String requestReason;

    /**
     * 涉及商品数量
     */
    private Integer productCount;

    /**
     * 涉及SKU数量
     */
    private Integer skuCount;

    /**
     * 总价格变动金额
     */
    private BigDecimal totalPriceChangeAmount;

    /**
     * 平均价格变动比例
     */
    private BigDecimal avgPriceChangeRate;

    /**
     * 最大价格变动比例
     */
    private BigDecimal maxPriceChangeRate;

    /**
     * 申请状态：1-待审核，2-审核通过，3-审核拒绝，4-已撤回，5-已过期
     */
    private Integer status;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 审核时间
     */
    private LocalDateTime reviewTime;

    /**
     * 审核人ID
     */
    private Long reviewerId;

    /**
     * 审核人姓名
     */
    private String reviewerName;

    /**
     * 审核意见
     */
    private String reviewComment;

    /**
     * 过期时间（审核通过后的有效期）
     */
    private LocalDateTime expireTime;

    /**
     * 应用时间（实际生效时间）
     */
    private LocalDateTime applyTime;

    /**
     * 应用状态：0-未应用，1-已应用，2-应用失败
     */
    private Integer applyStatus;

    /**
     * 应用失败原因
     */
    private String applyError;
} 