package com.linkBuy.mysql.dao.service.system;

import com.linkBuy.common.entity.BaseMenu;
import com.linkBuy.mysql.dao.dto.MenuStructureDto;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface MenuServiceInterface {
    List<BaseMenu> getAllMenuTree();

    List<BaseMenu> getAllMenus();

    @Transactional(rollbackFor = Exception.class)
    void updateMenuSort(List<Long> menuIds);

    @Transactional(rollbackFor = Exception.class)
    void deleteMenu(Long id);

    @Transactional(rollbackFor = Exception.class)
    void updateMenuStructure(List<MenuStructureDto> menuStructures);

    @Transactional(rollbackFor = Exception.class)
    boolean saveOrUpdate(BaseMenu menu);
}
