package com.linkBuy.mysql.dao.entity.system;

import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import com.linkBuy.common.entity.BaseEntity;
import lombok.EqualsAndHashCode;

/**
 * 系统功能实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_function")
public class SysFunction extends BaseEntity {
    
    /**
     * 功能名称
     */
    private String functionName;
    
    /**
     * 功能编码
     */
    private String functionCode;
    
    /**
     * 路由路径
     */
    private String routePath;
    
    /**
     * 视图路径
     */
    private String viewPath;
    
    /**
     * 描述
     */
    private String description;
} 