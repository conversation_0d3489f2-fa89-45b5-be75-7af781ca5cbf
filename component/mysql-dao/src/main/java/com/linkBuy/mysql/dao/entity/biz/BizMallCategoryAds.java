package com.linkBuy.mysql.dao.entity.biz;

import com.baomidou.mybatisplus.annotation.TableName;
import com.linkBuy.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商城分类页广告配置实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_mall_category_ads")
public class BizMallCategoryAds extends BaseEntity {
    /**
     * 商城ID
     */
    private Long mallId;
    
    /**
     * 分类ID，NULL表示全局默认广告
     */
    private Long categoryId;
    
    /**
     * 分类类型：global-全局，standard-标准分类，custom-自定义分类
     */
    private String categoryType;
    
    /**
     * 广告图片URL
     */
    private String imageUrl;
    
    /**
     * 跳转链接
     */
    private String linkUrl;
    
    /**
     * 链接描述文字
     */
    private String linkText;
    
    /**
     * 链接类型：none-无跳转，page-页面，url-外链
     */
    private String linkType;
    
    /**
     * 排序权重
     */
    private Integer sortOrder;
    
    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;
} 