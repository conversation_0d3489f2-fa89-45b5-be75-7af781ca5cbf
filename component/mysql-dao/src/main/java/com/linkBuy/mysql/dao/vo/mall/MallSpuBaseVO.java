package com.linkBuy.mysql.dao.vo.mall;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商城商品基础视图VO
 * 对应 v_mall_spu_base 视图
 */
@Data
public class MallSpuBaseVO {

    /**
     * 基础信息
     */
    private Long companySpuId;
    private Long companyId;
    private Long supplierSpuId;
    private Long supplierId;
    private String spuCode;

    /**
     * 商品信息 (来自供应商SPU)
     */
    private String productName;
    private String mainImgUrl;
    private String description;
    private String features;
    private String unit;
    private String productType;
    private String barcode;

    /**
     * 规格信息
     */
    private String spec1Name;
    private String spec2Name;
    private String spec3Name;

    /**
     * 价格信息 (来自客户层)
     */
    private BigDecimal strikethroughPrice;
    private BigDecimal salePriceMin;
    private BigDecimal salePriceMax;
    private BigDecimal profitRateMin;
    private BigDecimal profitRateMax;

    /**
     * 分类信息
     */
    private Long standardCategoryId;
    private String standardCategoryName;
    private Long standardParentCategoryId;

    /**
     * 状态信息
     */
    private Integer companySpuStatus;
    private Integer supplierSpuStatus;
    private Integer source;

    /**
     * 运费和售后
     */
    private String shippingType;
    private BigDecimal shippingFee;
    private String afterSaleType;
    private String afterSaleServices;

    /**
     * 物理属性
     */
    private BigDecimal volume;
    private BigDecimal weight;
    private BigDecimal length;
    private BigDecimal wide;
    private BigDecimal tall;

    /**
     * 库存信息
     */
    private Integer totalInventory;
    private Integer availableInventory;
    private Integer blockedInventory;

    /**
     * 销量信息
     */
    private Integer totalSales;

    /**
     * 时间信息
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
} 