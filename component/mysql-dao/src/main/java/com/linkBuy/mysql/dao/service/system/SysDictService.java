package com.linkBuy.mysql.dao.service.system;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linkBuy.mysql.dao.entity.system.SysDict;
import com.linkBuy.mysql.dao.mapper.system.SysDictMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 系统字典Service实现类
 */
@Service
public class SysDictService extends ServiceImpl<SysDictMapper, SysDict> {
    public SysDict getSysDictByCode(String code) {
        if (!StringUtils.hasText(code)) {
            return null;
        }
        LambdaQueryWrapper<SysDict> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysDict::getDictCode, code);
        return getOne(wrapper);
    }
} 