package com.linkBuy.mysql.dao.entity.biz;

import com.baomidou.mybatisplus.annotation.TableName;
import com.linkBuy.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 平台商品分类统计实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_platform_product_category")
public class BizPlatformProductCategory extends BaseEntity {

    /**
     * 商品分类ID
     */
    private Long categoryId;

    /**
     * 平台供应商在此分类下的商品数量
     */
    private Long spuCount;
} 