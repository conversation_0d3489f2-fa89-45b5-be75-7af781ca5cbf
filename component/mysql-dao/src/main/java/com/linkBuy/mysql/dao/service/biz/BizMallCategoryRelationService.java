package com.linkBuy.mysql.dao.service.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linkBuy.mysql.dao.entity.biz.BizMallCategoryRelation;
import com.linkBuy.mysql.dao.mapper.biz.BizMallCategoryRelationMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 商城分类关联Service实现类
 */
@Service
public class BizMallCategoryRelationService extends ServiceImpl<BizMallCategoryRelationMapper, BizMallCategoryRelation> {
    
    /**
     * 根据商城分类ID获取关联的标准商品分类ID列表
     */
    public List<Long> getProductCategoryIdsByMallCategoryId(Long mallCategoryId) {
        if (mallCategoryId == null) {
            return null;
        }
        LambdaQueryWrapper<BizMallCategoryRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizMallCategoryRelation::getMallCategoryId, mallCategoryId)
               .eq(BizMallCategoryRelation::getIsDeleted, 0);
        
        List<BizMallCategoryRelation> relations = baseMapper.selectList(wrapper);
        return relations.stream()
                .map(BizMallCategoryRelation::getProductCategoryId)
                .collect(Collectors.toList());
    }
    
    /**
     * 根据标准商品分类ID获取关联的商城分类ID列表
     */
    public List<Long> getMallCategoryIdsByProductCategoryId(Long productCategoryId) {
        if (productCategoryId == null) {
            return null;
        }
        LambdaQueryWrapper<BizMallCategoryRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizMallCategoryRelation::getProductCategoryId, productCategoryId)
               .eq(BizMallCategoryRelation::getIsDeleted, 0);
        
        List<BizMallCategoryRelation> relations = baseMapper.selectList(wrapper);
        return relations.stream()
                .map(BizMallCategoryRelation::getMallCategoryId)
                .collect(Collectors.toList());
    }
    
    /**
     * 根据商城ID获取所有分类关联关系
     */
    public List<BizMallCategoryRelation> getByMallId(Long mallId) {
        if (mallId == null) {
            return null;
        }
        LambdaQueryWrapper<BizMallCategoryRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizMallCategoryRelation::getMallId, mallId)
               .eq(BizMallCategoryRelation::getIsDeleted, 0)
               .orderByDesc(BizMallCategoryRelation::getCreateTime);
        return baseMapper.selectList(wrapper);
    }
    
    /**
     * 检查关联关系是否存在
     */
    public boolean existsRelation(Long mallCategoryId, Long productCategoryId) {
        if (mallCategoryId == null || productCategoryId == null) {
            return false;
        }
        LambdaQueryWrapper<BizMallCategoryRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizMallCategoryRelation::getMallCategoryId, mallCategoryId)
               .eq(BizMallCategoryRelation::getProductCategoryId, productCategoryId)
               .eq(BizMallCategoryRelation::getIsDeleted, 0);
        return baseMapper.selectCount(wrapper) > 0;
    }
    
    /**
     * 创建分类关联关系
     */
    public boolean createRelation(Long mallId, Long mallCategoryId, Long productCategoryId) {
        if (mallId == null || mallCategoryId == null || productCategoryId == null) {
            return false;
        }
        
        // 检查是否已存在关联关系
        if (existsRelation(mallCategoryId, productCategoryId)) {
            return true;
        }
        
        BizMallCategoryRelation relation = new BizMallCategoryRelation();
        relation.setMallId(mallId);
        relation.setMallCategoryId(mallCategoryId);
        relation.setProductCategoryId(productCategoryId);
        
        return save(relation);
    }
    
    /**
     * 批量创建分类关联关系
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchCreateRelations(Long mallId, Long mallCategoryId, List<Long> productCategoryIds) {
        if (mallId == null || mallCategoryId == null || productCategoryIds == null || productCategoryIds.isEmpty()) {
            return false;
        }
        
        List<BizMallCategoryRelation> relations = productCategoryIds.stream()
                .filter(productCategoryId -> !existsRelation(mallCategoryId, productCategoryId))
                .map(productCategoryId -> {
                    BizMallCategoryRelation relation = new BizMallCategoryRelation();
                    relation.setMallId(mallId);
                    relation.setMallCategoryId(mallCategoryId);
                    relation.setProductCategoryId(productCategoryId);
                    return relation;
                })
                .collect(Collectors.toList());
        
        return relations.isEmpty() || saveBatch(relations);
    }
    
    /**
     * 删除分类关联关系
     */
    public boolean deleteRelation(Long mallCategoryId, Long productCategoryId) {
        if (mallCategoryId == null || productCategoryId == null) {
            return false;
        }
        LambdaUpdateWrapper<BizMallCategoryRelation> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(BizMallCategoryRelation::getMallCategoryId, mallCategoryId)
               .eq(BizMallCategoryRelation::getProductCategoryId, productCategoryId)
               .set(BizMallCategoryRelation::getIsDeleted, 1);
        return update(wrapper);
    }
    
    /**
     * 删除商城分类的所有关联关系
     */
    public boolean deleteByMallCategoryId(Long mallCategoryId) {
        if (mallCategoryId == null) {
            return false;
        }
        LambdaUpdateWrapper<BizMallCategoryRelation> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(BizMallCategoryRelation::getMallCategoryId, mallCategoryId)
               .set(BizMallCategoryRelation::getIsDeleted, 1);
        return update(wrapper);
    }
    
    /**
     * 更新商城分类的关联关系
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRelations(Long mallId, Long mallCategoryId, List<Long> productCategoryIds) {
        if (mallId == null || mallCategoryId == null) {
            return false;
        }
        
        // 先删除现有的关联关系
        deleteByMallCategoryId(mallCategoryId);
        
        // 如果有新的关联关系，则创建
        if (productCategoryIds != null && !productCategoryIds.isEmpty()) {
            return batchCreateRelations(mallId, mallCategoryId, productCategoryIds);
        }
        
        return true;
    }
    
    /**
     * 根据商城分类ID统计关联的标准商品分类数量
     */
    public long countByMallCategoryId(Long mallCategoryId) {
        if (mallCategoryId == null) {
            return 0;
        }
        LambdaQueryWrapper<BizMallCategoryRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizMallCategoryRelation::getMallCategoryId, mallCategoryId)
               .eq(BizMallCategoryRelation::getIsDeleted, 0);
        return baseMapper.selectCount(wrapper);
    }
} 