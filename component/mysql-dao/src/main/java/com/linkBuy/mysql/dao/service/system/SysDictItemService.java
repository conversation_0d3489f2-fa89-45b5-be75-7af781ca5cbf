package com.linkBuy.mysql.dao.service.system;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linkBuy.mysql.dao.entity.system.SysDictItem;
import com.linkBuy.mysql.dao.mapper.system.SysDictItemMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 系统字典项Service实现类
 */
@Service
public class SysDictItemService extends ServiceImpl<SysDictItemMapper, SysDictItem> {
    public List<SysDictItem> listByDictId(Long id) {
        if (id == null) {
            return null;
        }
        LambdaQueryWrapper<SysDictItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysDictItem::getDictId, id);
        return list(wrapper);
    }
} 