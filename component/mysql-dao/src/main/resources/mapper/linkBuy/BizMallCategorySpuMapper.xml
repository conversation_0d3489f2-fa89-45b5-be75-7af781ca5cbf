<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linkBuy.mysql.dao.mapper.biz.BizMallCategorySpuMapper">

    <!-- 商城分类商品视图结果映射 -->
    <resultMap id="MallCategorySpuResultMap" type="com.linkBuy.mysql.dao.vo.mall.MallCategorySpuVO">
        <!-- 基础商品信息 -->
        <id column="company_spu_id" property="companySpuId"/>
        <result column="company_id" property="companyId"/>
        <result column="supplier_spu_id" property="supplierSpuId"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="spu_code" property="spuCode"/>
        <result column="product_name" property="productName"/>
        <result column="main_img_url" property="mainImgUrl"/>
        <result column="description" property="description"/>
        <result column="features" property="features"/>
        <result column="unit" property="unit"/>
        <result column="product_type" property="productType"/>
        <result column="barcode" property="barcode"/>
        <result column="spec1_name" property="spec1Name"/>
        <result column="spec2_name" property="spec2Name"/>
        <result column="spec3_name" property="spec3Name"/>
        <result column="strikethrough_price" property="strikethroughPrice"/>
        <result column="sale_price_min" property="salePriceMin"/>
        <result column="sale_price_max" property="salePriceMax"/>
        <result column="profit_rate_min" property="profitRateMin"/>
        <result column="profit_rate_max" property="profitRateMax"/>
        <result column="standard_category_id" property="standardCategoryId"/>
        <result column="standard_category_name" property="standardCategoryName"/>
        <result column="standard_parent_category_id" property="standardParentCategoryId"/>
        <result column="company_spu_status" property="companySpuStatus"/>
        <result column="supplier_spu_status" property="supplierSpuStatus"/>
        <result column="source" property="source"/>
        <result column="shipping_type" property="shippingType"/>
        <result column="shipping_fee" property="shippingFee"/>
        <result column="after_sale_type" property="afterSaleType"/>
        <result column="after_sale_services" property="afterSaleServices"/>
        <result column="volume" property="volume"/>
        <result column="weight" property="weight"/>
        <result column="length" property="length"/>
        <result column="wide" property="wide"/>
        <result column="tall" property="tall"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        
        <!-- 分类相关信息 -->
        <result column="mall_id" property="mallId"/>
        <result column="mall_category_id" property="mallCategoryId"/>
        <result column="custom_category_name" property="customCategoryName"/>
        <result column="custom_parent_category_id" property="customParentCategoryId"/>
        <result column="custom_category_level" property="customCategoryLevel"/>
        <result column="bind_type" property="bindType"/>
        <result column="source_category_id" property="sourceCategoryId"/>
        <result column="category_sort_order" property="categorySortOrder"/>
        <result column="is_featured" property="isFeatured"/>
        <result column="decoration_category_type" property="decorationCategoryType"/>
        <result column="style_type" property="styleType"/>
        <result column="show_search" property="showSearch"/>
    </resultMap>

    <!-- 分页查询商城分类下的商品 -->
    <select id="selectCategorySpuPage" resultMap="MallCategorySpuResultMap">
        SELECT 
            -- 商城和分类信息
            mcs.mall_id,
            mcs.mall_category_id,
            mc.category_name as custom_category_name,
            mc.parent_id as custom_parent_category_id,
            mc.level as custom_category_level,
            
            -- 基础商品信息
            cs.id as company_spu_id,
            cs.company_id,
            cs.spu_id as supplier_spu_id,
            cs.supplier_id,
            cs.spu_code,
            ss.name as product_name,
            ss.main_img_url,
            ss.description,
            ss.features,
            ss.unit,
            ss.type as product_type,
            ss.barcode,
            ss.spec1_name,
            ss.spec2_name, 
            ss.spec3_name,
            cs.strikethrough_price,
            cs.sale_price_min,
            cs.sale_price_max,
            cs.profit_rate_min,
            cs.profit_rate_max,
            cs.category_id as standard_category_id,
            pc.category_name as standard_category_name,
            pc.parent_id as standard_parent_category_id,
            cs.status as company_spu_status,
            ss.status as supplier_spu_status,
            cs.source,
            ss.shipping_type,
            ss.shipping_fee,
            ss.after_sale_type,
            ss.after_sale_services,
            ss.volume,
            ss.weight,
            ss.length,
            ss.wide,
            ss.tall,
            cs.create_time,
            cs.update_time,
            
            -- 绑定信息
            mcs.bind_type,
            mcs.source_category_id,
            mcs.sort_order as category_sort_order,
            mcs.is_featured,
            
            -- 分类装修配置
            mcd.category_type as decoration_category_type,
            mcd.style_type,
            mcd.show_search
            
        FROM biz_mall_category_spu mcs
        JOIN biz_company_spu cs ON mcs.company_spu_id = cs.id
        JOIN biz_supplier_spu ss ON cs.spu_id = ss.id
        JOIN t_gen_product_category pc ON cs.category_id = pc.id
        JOIN biz_mall_category mc ON mcs.mall_category_id = mc.id
        LEFT JOIN biz_mall_category_decoration mcd ON mcs.mall_id = mcd.mall_id 
            AND mcd.is_deleted = 0 
            AND mcd.status = 1
        WHERE mcs.is_deleted = 0 
          AND cs.is_deleted = 0
          AND ss.is_deleted = 0
          AND pc.is_deleted = 0
          AND mc.is_deleted = 0
          AND cs.status = 1
          AND ss.status = 1
          AND mc.status = 1
          AND mcs.mall_id = #{mallId}
          AND mcs.mall_category_id = #{categoryId}
        ORDER BY mcs.sort_order, cs.create_time DESC
    </select>

    <!-- 获取推荐商品列表 -->
    <select id="selectFeaturedProducts" resultMap="MallCategorySpuResultMap">
        SELECT 
            -- 商城和分类信息
            mcs.mall_id,
            mcs.mall_category_id,
            mc.category_name as custom_category_name,
            mc.parent_id as custom_parent_category_id,
            mc.level as custom_category_level,
            
            -- 基础商品信息
            cs.id as company_spu_id,
            cs.company_id,
            cs.spu_id as supplier_spu_id,
            cs.supplier_id,
            cs.spu_code,
            ss.name as product_name,
            ss.main_img_url,
            ss.description,
            ss.features,
            ss.unit,
            ss.type as product_type,
            ss.barcode,
            ss.spec1_name,
            ss.spec2_name, 
            ss.spec3_name,
            cs.strikethrough_price,
            cs.sale_price_min,
            cs.sale_price_max,
            cs.profit_rate_min,
            cs.profit_rate_max,
            cs.category_id as standard_category_id,
            pc.category_name as standard_category_name,
            pc.parent_id as standard_parent_category_id,
            cs.status as company_spu_status,
            ss.status as supplier_spu_status,
            cs.source,
            ss.shipping_type,
            ss.shipping_fee,
            ss.after_sale_type,
            ss.after_sale_services,
            ss.volume,
            ss.weight,
            ss.length,
            ss.wide,
            ss.tall,
            cs.create_time,
            cs.update_time,
            
            -- 绑定信息
            mcs.bind_type,
            mcs.source_category_id,
            mcs.sort_order as category_sort_order,
            mcs.is_featured,
            
            -- 分类装修配置
            mcd.category_type as decoration_category_type,
            mcd.style_type,
            mcd.show_search
            
        FROM biz_mall_category_spu mcs
        JOIN biz_company_spu cs ON mcs.company_spu_id = cs.id
        JOIN biz_supplier_spu ss ON cs.spu_id = ss.id
        JOIN t_gen_product_category pc ON cs.category_id = pc.id
        JOIN biz_mall_category mc ON mcs.mall_category_id = mc.id
        LEFT JOIN biz_mall_category_decoration mcd ON mcs.mall_id = mcd.mall_id 
            AND mcd.is_deleted = 0 
            AND mcd.status = 1
        WHERE mcs.is_deleted = 0 
          AND cs.is_deleted = 0
          AND ss.is_deleted = 0
          AND pc.is_deleted = 0
          AND mc.is_deleted = 0
          AND cs.status = 1
          AND ss.status = 1
          AND mc.status = 1
          AND mcs.mall_id = #{mallId}
          AND mcs.is_featured = 1  -- 只查询推荐商品
        ORDER BY mcs.sort_order, cs.create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 批量更新商品在分类中的排序 -->
    <update id="batchUpdateSortOrder">
        <foreach collection="spuSortList" item="item" separator=";">
            UPDATE biz_mall_category_spu 
            SET sort_order = #{item.sortOrder}, update_time = NOW()
            WHERE mall_id = #{mallId} 
              AND mall_category_id = #{categoryId}
              AND company_spu_id = #{item.companySpuId}
              AND is_deleted = 0
        </foreach>
    </update>

</mapper> 