<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linkBuy.mysql.dao.mapper.biz.MallProductViewMapper">

    <!-- 商城商品基础视图结果映射 -->
    <resultMap id="MallSpuBaseResultMap" type="com.linkBuy.mysql.dao.vo.mall.MallSpuBaseVO">
        <id column="company_spu_id" property="companySpuId"/>
        <result column="company_id" property="companyId"/>
        <result column="supplier_spu_id" property="supplierSpuId"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="spu_code" property="spuCode"/>
        <result column="product_name" property="productName"/>
        <result column="main_img_url" property="mainImgUrl"/>
        <result column="description" property="description"/>
        <result column="features" property="features"/>
        <result column="unit" property="unit"/>
        <result column="product_type" property="productType"/>
        <result column="barcode" property="barcode"/>
        <result column="spec1_name" property="spec1Name"/>
        <result column="spec2_name" property="spec2Name"/>
        <result column="spec3_name" property="spec3Name"/>
        <result column="strikethrough_price" property="strikethroughPrice"/>
        <result column="sale_price_min" property="salePriceMin"/>
        <result column="sale_price_max" property="salePriceMax"/>
        <result column="profit_rate_min" property="profitRateMin"/>
        <result column="profit_rate_max" property="profitRateMax"/>
        <result column="standard_category_id" property="standardCategoryId"/>
        <result column="standard_category_name" property="standardCategoryName"/>
        <result column="standard_parent_category_id" property="standardParentCategoryId"/>
        <result column="company_spu_status" property="companySpuStatus"/>
        <result column="supplier_spu_status" property="supplierSpuStatus"/>
        <result column="source" property="source"/>
        <result column="shipping_type" property="shippingType"/>
        <result column="shipping_fee" property="shippingFee"/>
        <result column="after_sale_type" property="afterSaleType"/>
        <result column="after_sale_services" property="afterSaleServices"/>
        <result column="volume" property="volume"/>
        <result column="weight" property="weight"/>
        <result column="length" property="length"/>
        <result column="wide" property="wide"/>
        <result column="tall" property="tall"/>
        <result column="total_inventory" property="totalInventory"/>
        <result column="available_inventory" property="availableInventory"/>
        <result column="blocked_inventory" property="blockedInventory"/>
        <result column="total_sales" property="totalSales"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 商城分类商品视图结果映射 -->
    <resultMap id="MallCategorySpuResultMap" type="com.linkBuy.mysql.dao.vo.mall.MallCategorySpuVO" extends="MallSpuBaseResultMap">
        <result column="mall_id" property="mallId"/>
        <result column="mall_category_id" property="mallCategoryId"/>
        <result column="custom_category_name" property="customCategoryName"/>
        <result column="custom_parent_category_id" property="customParentCategoryId"/>
        <result column="custom_category_level" property="customCategoryLevel"/>
        <result column="bind_type" property="bindType"/>
        <result column="source_category_id" property="sourceCategoryId"/>
        <result column="category_sort_order" property="categorySortOrder"/>
        <result column="is_featured" property="isFeatured"/>
        <result column="decoration_category_type" property="decorationCategoryType"/>
        <result column="style_type" property="styleType"/>
        <result column="show_search" property="showSearch"/>
    </resultMap>

    <!-- 商城SKU详情视图结果映射 -->
    <resultMap id="MallSkuDetailResultMap" type="com.linkBuy.mysql.dao.vo.mall.MallSkuDetailVO">
        <id column="company_sku_id" property="companySkuId"/>
        <result column="company_id" property="companyId"/>
        <result column="supplier_sku_id" property="supplierSkuId"/>
        <result column="company_spu_id" property="companySpuId"/>
        <result column="sale_price" property="salePrice"/>
        <result column="profit_rate" property="profitRate"/>
        <result column="min_order_quantity" property="minOrderQuantity"/>
        <result column="max_order_quantity" property="maxOrderQuantity"/>
        <result column="sku_status" property="skuStatus"/>
        <result column="sku_source" property="skuSource"/>
        <result column="sku_code" property="skuCode"/>
        <result column="cost_price" property="costPrice"/>
        <result column="sku_strikethrough_price" property="skuStrikethroughPrice"/>
        <result column="purchase_price" property="purchasePrice"/>
        <result column="spec1_value" property="spec1Value"/>
        <result column="spec2_value" property="spec2Value"/>
        <result column="spec3_value" property="spec3Value"/>
        <result column="total_inventory" property="totalInventory"/>
        <result column="available_inventory" property="availableInventory"/>
        <result column="blocked_inventory" property="blockedInventory"/>
        <result column="safety_stock" property="safetyStock"/>
        <result column="sales_count" property="salesCount"/>
        <result column="virtual_sales_count" property="virtualSalesCount"/>
        <result column="total_sales" property="totalSales"/>
        <result column="sku_img_url" property="skuImgUrl"/>
        <result column="sku_sort_order" property="skuSortOrder"/>
        <result column="product_name" property="productName"/>
        <result column="spec1_name" property="spec1Name"/>
        <result column="spec2_name" property="spec2Name"/>
        <result column="spec3_name" property="spec3Name"/>
    </resultMap>

    <!-- 商城商品基础视图SQL片段 -->
    <sql id="MallSpuBaseViewSql">
        SELECT
            -- 基础信息
            cs.id as company_spu_id,
            cs.company_id,
            cs.spu_id as supplier_spu_id,
            cs.supplier_id,
            cs.spu_code,

            -- 商品信息 (来自供应商SPU)
            ss.name as product_name,
            ss.main_img_url,
            ss.description,
            ss.features,
            ss.unit,
            ss.type as product_type,
            ss.barcode,

            -- 规格信息
            ss.spec1_name,
            ss.spec2_name,
            ss.spec3_name,

            -- 价格信息 (来自客户层)
            cs.strikethrough_price,
            cs.sale_price_min,
            cs.sale_price_max,
            cs.profit_rate_min,
            cs.profit_rate_max,

            -- 分类信息
            cs.category_id as standard_category_id,
            pc.category_name as standard_category_name,
            pc.parent_id as standard_parent_category_id,

            -- 状态信息
            cs.status as company_spu_status,
            ss.status as supplier_spu_status,
            cs.source,

            -- 运费和售后
            ss.shipping_type,
            ss.shipping_fee,
            ss.after_sale_type,
            ss.after_sale_services,

            -- 物理属性
            ss.volume,
            ss.weight,
            ss.length,
            ss.wide,
            ss.tall,

            -- 库存信息 (SPU级别聚合)
            COALESCE(sku_stats.total_inventory, 0) as total_inventory,
            COALESCE(sku_stats.available_inventory, 0) as available_inventory,
            COALESCE(sku_stats.blocked_inventory, 0) as blocked_inventory,

            -- 销量信息 (SPU级别聚合)
            COALESCE(sku_stats.total_sales, 0) as total_sales,

            -- 时间信息
            cs.create_time,
            cs.update_time

        FROM biz_company_spu cs
        JOIN biz_supplier_spu ss ON cs.spu_id = ss.id
        JOIN t_gen_product_category pc ON cs.category_id = pc.id
        LEFT JOIN (
            SELECT
                ssku.spu_id,
                SUM(ssku.total_inventory) as total_inventory,
                SUM(ssku.available_inventory) as available_inventory,
                SUM(ssku.blocked_inventory) as blocked_inventory,
                SUM(COALESCE(ssku.sales_count, 0) + COALESCE(ssku.virtual_sales_count, 0)) as total_sales
            FROM biz_supplier_sku ssku
            WHERE ssku.is_deleted = 0
            GROUP BY ssku.spu_id
        ) sku_stats ON ss.id = sku_stats.spu_id
        WHERE cs.is_deleted = 0
          AND ss.is_deleted = 0
          AND pc.is_deleted = 0
          AND cs.status = 1  -- 客户启用
          AND ss.status = 1  -- 供应商销售中
    </sql>

    <!-- 搜索关键词SQL片段 -->
    <sql id="SearchKeywordsSql">
        CONCAT_WS(' ', 
            ss.name, 
            ss.features, 
            pc.category_name,
            cs.spu_code,
            ss.barcode
        ) as search_keywords
    </sql>

    <!-- 1. 分类商品查询API -->
    <select id="selectCategoryProducts" resultMap="MallCategorySpuResultMap">
        SELECT 
            -- 商城和分类信息
            mcs.mall_id,
            mcs.mall_category_id,
            mc.category_name as custom_category_name,
            mc.parent_id as custom_parent_category_id,
            mc.level as custom_category_level,
            
            -- 商品信息 (继承基础视图)
            pb.*,
            
            -- 绑定信息
            mcs.bind_type,
            mcs.source_category_id,
            mcs.sort_order as category_sort_order,
            mcs.is_featured,
            
            -- 分类装修配置
            mcd.category_type as decoration_category_type,
            mcd.style_type,
            mcd.show_search
            
        FROM biz_mall_category_spu mcs
        JOIN (
            <include refid="MallSpuBaseViewSql"/>
        ) pb ON mcs.company_spu_id = pb.company_spu_id
        JOIN biz_mall_category mc ON mcs.mall_category_id = mc.id
        LEFT JOIN biz_mall_category_decoration mcd ON mcs.mall_id = mcd.mall_id 
            AND mcd.is_deleted = 0 
            AND mcd.status = 1
        WHERE mcs.is_deleted = 0 
          AND mc.is_deleted = 0
          AND mc.status = 1
          AND mcs.mall_id = #{mallId}
        <choose>
            <when test="categoryType == 'custom'">
                AND mcs.mall_category_id = #{categoryId}
            </when>
            <when test="categoryType == 'standard'">
                AND pb.standard_category_id = #{categoryId}
            </when>
        </choose>
        <!-- 关键词搜索 -->
        <if test="keyword != null and keyword != ''">
            AND (
                pb.product_name LIKE CONCAT('%', #{keyword}, '%')
                OR pb.features LIKE CONCAT('%', #{keyword}, '%')
                OR pb.standard_category_name LIKE CONCAT('%', #{keyword}, '%')
                OR pb.spu_code LIKE CONCAT('%', #{keyword}, '%')
                OR pb.barcode LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>
        <!-- 价格区间筛选 -->
        <if test="minPrice != null">
            AND pb.sale_price_min &gt;= #{minPrice}
        </if>
        <if test="maxPrice != null">
            AND pb.sale_price_min &lt;= #{maxPrice}
        </if>

        <!-- 动态排序 -->
        <choose>
            <when test="orderBy != null and orderBy != ''">
                ORDER BY ${orderBy}
            </when>
            <otherwise>
                ORDER BY mcs.sort_order, pb.create_time DESC
            </otherwise>
        </choose>
        <!-- 分页 -->
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- 2. 商品搜索API -->
    <select id="searchProducts" resultMap="MallSpuBaseResultMap">
        <include refid="MallSpuBaseViewSql"/>
        AND cs.company_id = #{companyId}
        <if test="keyword != null and keyword != ''">
            <choose>
                <when test="useFullText == true">
                    AND MATCH(
                        <include refid="SearchKeywordsSql"/>
                    ) AGAINST(#{keyword} IN NATURAL LANGUAGE MODE)
                </when>
                <otherwise>
                    AND (
                        ss.name LIKE CONCAT('%', #{keyword}, '%')
                        OR ss.features LIKE CONCAT('%', #{keyword}, '%')
                        OR pc.category_name LIKE CONCAT('%', #{keyword}, '%')
                        OR cs.spu_code LIKE CONCAT('%', #{keyword}, '%')
                        OR ss.barcode LIKE CONCAT('%', #{keyword}, '%')
                    )
                </otherwise>
            </choose>
        </if>
        <!-- 价格区间筛选 -->
        <if test="minPrice != null">
            AND cs.sale_price_min &gt;= #{minPrice}
        </if>
        <if test="maxPrice != null">
            AND cs.sale_price_min &lt;= #{maxPrice}
        </if>

        <!-- 动态排序 -->
        <choose>
            <when test="orderBy != null and orderBy != ''">
                ORDER BY ${orderBy}
            </when>
            <otherwise>
                ORDER BY cs.sale_price_min
            </otherwise>
        </choose>
        <!-- 分页 -->
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- 3. 分类内搜索API - 自定义分类内搜索 -->
    <select id="searchInCustomCategory" resultMap="MallCategorySpuResultMap">
        SELECT 
            -- 商城和分类信息
            mcs.mall_id,
            mcs.mall_category_id,
            mc.category_name as custom_category_name,
            mc.parent_id as custom_parent_category_id,
            mc.level as custom_category_level,
            
            -- 商品信息 (继承基础视图)
            pb.*,
            
            -- 绑定信息
            mcs.bind_type,
            mcs.source_category_id,
            mcs.sort_order as category_sort_order,
            mcs.is_featured,
            
            -- 分类装修配置
            mcd.category_type as decoration_category_type,
            mcd.style_type,
            mcd.show_search
            
        FROM biz_mall_category_spu mcs
        JOIN (
            <include refid="MallSpuBaseViewSql"/>
        ) pb ON mcs.company_spu_id = pb.company_spu_id
        JOIN biz_mall_category mc ON mcs.mall_category_id = mc.id
        LEFT JOIN biz_mall_category_decoration mcd ON mcs.mall_id = mcd.mall_id 
            AND mcd.is_deleted = 0 
            AND mcd.status = 1
        WHERE mcs.is_deleted = 0 
          AND mc.is_deleted = 0
          AND mc.status = 1
          AND mcs.mall_id = #{mallId}
          AND mcs.mall_category_id = #{categoryId}
        <if test="keyword != null and keyword != ''">
          AND (
              pb.product_name LIKE CONCAT('%', #{keyword}, '%')
              OR pb.features LIKE CONCAT('%', #{keyword}, '%')
              OR pb.standard_category_name LIKE CONCAT('%', #{keyword}, '%')
              OR pb.spu_code LIKE CONCAT('%', #{keyword}, '%')
              OR pb.barcode LIKE CONCAT('%', #{keyword}, '%')
          )
        </if>
        <!-- 价格区间筛选 -->
        <if test="minPrice != null">
            AND pb.sale_price_min &gt;= #{minPrice}
        </if>
        <if test="maxPrice != null">
            AND pb.sale_price_min &lt;= #{maxPrice}
        </if>
        <!-- 动态排序 -->
        <choose>
            <when test="orderBy != null and orderBy != ''">
                ORDER BY ${orderBy}
            </when>
            <otherwise>
                ORDER BY mcs.sort_order, pb.create_time DESC
            </otherwise>
        </choose>
        <!-- 分页 -->
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- 3. 分类内搜索API - 标准分类内搜索 -->
    <select id="searchInStandardCategory" resultMap="MallSpuBaseResultMap">
        <include refid="MallSpuBaseViewSql"/>
        AND cs.company_id = #{companyId}
        AND cs.category_id = #{categoryId}
        <if test="keyword != null and keyword != ''">
            AND (
                ss.name LIKE CONCAT('%', #{keyword}, '%')
                OR ss.features LIKE CONCAT('%', #{keyword}, '%')
                OR pc.category_name LIKE CONCAT('%', #{keyword}, '%')
                OR cs.spu_code LIKE CONCAT('%', #{keyword}, '%')
                OR ss.barcode LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>
        <!-- 价格区间筛选 -->
        <if test="minPrice != null">
            AND cs.sale_price_min &gt;= #{minPrice}
        </if>
        <if test="maxPrice != null">
            AND cs.sale_price_min &lt;= #{maxPrice}
        </if>
        <!-- 动态排序 -->
        <choose>
            <when test="orderBy != null and orderBy != ''">
                ORDER BY ${orderBy}
            </when>
            <otherwise>
                ORDER BY cs.create_time DESC
            </otherwise>
        </choose>
        <!-- 分页 -->
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- 4. 商品详情API - 获取商品基础信息 -->
    <select id="getProductDetail" resultMap="MallSpuBaseResultMap">
        <include refid="MallSpuBaseViewSql"/>
        AND cs.id = #{companySpuId} 
        AND cs.company_id = #{companyId}
    </select>

    <!-- 4. 商品详情API - 获取商品SKU列表 -->
    <select id="getProductSkus" resultMap="MallSkuDetailResultMap">
        SELECT 
            -- SKU基础信息
            csku.id as company_sku_id,
            csku.company_id,
            csku.sku_id as supplier_sku_id,
            csku.spu_id as company_spu_id,
            
            -- SKU价格信息
            csku.sale_price,
            csku.profit_rate,
            csku.min_order_quantity,
            csku.max_order_quantity,
            csku.status as sku_status,
            csku.source as sku_source,
            
            -- 供应商SKU信息
            ssku.sku_code,
            ssku.cost_price,
            ssku.strikethrough_price as sku_strikethrough_price,
            ssku.purchase_price,
            ssku.spec1_value,
            ssku.spec2_value,
            ssku.spec3_value,
            ssku.total_inventory,
            ssku.available_inventory,
            ssku.blocked_inventory,
            ssku.safety_stock,
            ssku.sales_count,
            ssku.virtual_sales_count,
            (COALESCE(ssku.sales_count, 0) + COALESCE(ssku.virtual_sales_count, 0)) as total_sales,
            ssku.img_url as sku_img_url,
            ssku.sort_order as sku_sort_order
            
        FROM biz_company_sku csku
        JOIN biz_supplier_sku ssku ON csku.sku_id = ssku.id
        WHERE csku.is_deleted = 0 
          AND ssku.is_deleted = 0
          AND csku.status = 1
          AND csku.spu_id = #{spuId}
        ORDER BY ssku.sort_order
    </select>

    <!-- 获取商城商品基础信息列表 -->
    <select id="selectMallSpuBaseList" resultMap="MallSpuBaseResultMap">
        <include refid="MallSpuBaseViewSql"/>
        <if test="companyId != null">
            AND cs.company_id = #{companyId}
        </if>
        <!-- 价格区间筛选 -->
        <if test="minPrice != null">
            AND cs.sale_price_min &gt;= #{minPrice}
        </if>
        <if test="maxPrice != null">
            AND cs.sale_price_min &lt;= #{maxPrice}
        </if>
        <!-- 动态排序 -->
        <choose>
            <when test="orderBy != null and orderBy != ''">
                ORDER BY ${orderBy}
            </when>
            <otherwise>
                ORDER BY cs.create_time DESC
            </otherwise>
        </choose>
        <!-- 分页 -->
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
        <if test="limit != null and offset == null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据商品ID列表批量获取商品信息 -->
    <select id="selectMallSpuBaseByIds" resultMap="MallSpuBaseResultMap">
        <include refid="MallSpuBaseViewSql"/>
        AND cs.id IN
        <foreach collection="companySpuIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY cs.create_time DESC
    </select>

    <!-- ==================== 支持筛选和排序的参数化查询方法 ==================== -->

    <!-- 分类商品查询API - 支持筛选和排序 -->
    <select id="selectCategoryProductsWithParams" resultMap="MallCategorySpuResultMap">
        SELECT 
            -- 商城和分类信息
            mcs.mall_id,
            mcs.mall_category_id,
            mc.category_name as custom_category_name,
            mc.parent_id as custom_parent_category_id,
            mc.level as custom_category_level,
            
            -- 商品信息 (继承基础视图)
            pb.*,
            
            -- 绑定信息
            mcs.bind_type,
            mcs.source_category_id,
            mcs.sort_order as category_sort_order,
            mcs.is_featured,
            
            -- 分类装修配置
            mcd.category_type as decoration_category_type,
            mcd.style_type,
            mcd.show_search
            
        FROM biz_mall_category_spu mcs
        JOIN (
            <include refid="MallSpuBaseViewSql"/>
        ) pb ON mcs.company_spu_id = pb.company_spu_id
        JOIN biz_mall_category mc ON mcs.mall_category_id = mc.id
        LEFT JOIN biz_mall_category_decoration mcd ON mcs.mall_id = mcd.mall_id 
            AND mcd.is_deleted = 0 
            AND mcd.status = 1
        WHERE mcs.is_deleted = 0 
          AND mc.is_deleted = 0
          AND mc.status = 1
          AND mcs.mall_id = #{mallId}
        <choose>
            <when test="categoryType == 'custom'">
                AND mcs.mall_category_id = #{categoryId}
            </when>
            <when test="categoryType == 'standard'">
                AND pb.standard_category_id = #{categoryId}
            </when>
        </choose>
        <!-- 关键词搜索 -->
        <if test="keyword != null and keyword != ''">
            AND (
                pb.product_name LIKE CONCAT('%', #{keyword}, '%')
                OR pb.features LIKE CONCAT('%', #{keyword}, '%')
                OR pb.standard_category_name LIKE CONCAT('%', #{keyword}, '%')
                OR pb.spu_code LIKE CONCAT('%', #{keyword}, '%')
                OR pb.barcode LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>
        <!-- 价格区间筛选 -->
        <if test="minPrice != null">
            AND pb.sale_price_min &gt;= #{minPrice}
        </if>
        <if test="maxPrice != null">
            AND pb.sale_price_min &lt;= #{maxPrice}
        </if>
        <!-- 动态排序 -->
        <choose>
            <when test="orderBy != null and orderBy != ''">
                ORDER BY ${orderBy}
            </when>
            <otherwise>
                ORDER BY mcs.sort_order, pb.create_time DESC
            </otherwise>
        </choose>
        <!-- 分页 -->
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- 分类商品查询API - 计算总数 -->
    <select id="countCategoryProducts" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM biz_mall_category_spu mcs
        JOIN (
            <include refid="MallSpuBaseViewSql"/>
        ) pb ON mcs.company_spu_id = pb.company_spu_id
        JOIN biz_mall_category mc ON mcs.mall_category_id = mc.id
        WHERE mcs.is_deleted = 0 
          AND mc.is_deleted = 0
          AND mc.status = 1
          AND mcs.mall_id = #{mallId}
        <choose>
            <when test="categoryType == 'custom'">
                AND mcs.mall_category_id = #{categoryId}
            </when>
            <when test="categoryType == 'standard'">
                AND pb.standard_category_id = #{categoryId}
            </when>
        </choose>
        <!-- 关键词搜索 -->
        <if test="keyword != null and keyword != ''">
            AND (
                pb.product_name LIKE CONCAT('%', #{keyword}, '%')
                OR pb.features LIKE CONCAT('%', #{keyword}, '%')
                OR pb.standard_category_name LIKE CONCAT('%', #{keyword}, '%')
                OR pb.spu_code LIKE CONCAT('%', #{keyword}, '%')
                OR pb.barcode LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>
        <!-- 价格区间筛选 -->
        <if test="minPrice != null">
            AND pb.sale_price_min &gt;= #{minPrice}
        </if>
        <if test="maxPrice != null">
            AND pb.sale_price_min &lt;= #{maxPrice}
        </if>
    </select>

    <!-- 商品搜索API - 支持筛选和排序 -->
    <select id="searchProductsWithParams" resultMap="MallSpuBaseResultMap">
        <include refid="MallSpuBaseViewSql"/>
        AND cs.company_id = #{companyId}
        <if test="keyword != null and keyword != ''">
            <choose>
                <when test="useFullText == true">
                    AND MATCH(
                        <include refid="SearchKeywordsSql"/>
                    ) AGAINST(#{keyword} IN NATURAL LANGUAGE MODE)
                </when>
                <otherwise>
                    AND (
                        ss.name LIKE CONCAT('%', #{keyword}, '%')
                        OR ss.features LIKE CONCAT('%', #{keyword}, '%')
                        OR pc.category_name LIKE CONCAT('%', #{keyword}, '%')
                        OR cs.spu_code LIKE CONCAT('%', #{keyword}, '%')
                        OR ss.barcode LIKE CONCAT('%', #{keyword}, '%')
                    )
                </otherwise>
            </choose>
        </if>
        <!-- 价格区间筛选 -->
        <if test="minPrice != null">
            AND cs.sale_price_min &gt;= #{minPrice}
        </if>
        <if test="maxPrice != null">
            AND cs.sale_price_min &lt;= #{maxPrice}
        </if>
        <!-- 动态排序 -->
        <choose>
            <when test="orderBy != null and orderBy != ''">
                ORDER BY ${orderBy}
            </when>
            <otherwise>
                ORDER BY cs.sale_price_min
            </otherwise>
        </choose>
        <!-- 分页 -->
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- 商品搜索API - 计算总数 -->
    <select id="countSearchProducts" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM biz_company_spu cs
        JOIN biz_supplier_spu ss ON cs.spu_id = ss.id
        JOIN t_gen_product_category pc ON cs.category_id = pc.id
        WHERE cs.is_deleted = 0 
          AND ss.is_deleted = 0
          AND pc.is_deleted = 0
          AND cs.status = 1
          AND ss.status = 1
          AND cs.company_id = #{companyId}
        <if test="keyword != null and keyword != ''">
            <choose>
                <when test="useFullText == true">
                    AND MATCH(
                        <include refid="SearchKeywordsSql"/>
                    ) AGAINST(#{keyword} IN NATURAL LANGUAGE MODE)
                </when>
                <otherwise>
                    AND (
                        ss.name LIKE CONCAT('%', #{keyword}, '%')
                        OR ss.features LIKE CONCAT('%', #{keyword}, '%')
                        OR pc.category_name LIKE CONCAT('%', #{keyword}, '%')
                        OR cs.spu_code LIKE CONCAT('%', #{keyword}, '%')
                        OR ss.barcode LIKE CONCAT('%', #{keyword}, '%')
                    )
                </otherwise>
            </choose>
        </if>
        <!-- 价格区间筛选 -->
        <if test="minPrice != null">
            AND cs.sale_price_min &gt;= #{minPrice}
        </if>
        <if test="maxPrice != null">
            AND cs.sale_price_min &lt;= #{maxPrice}
        </if>
    </select>

    <!-- 获取商城商品基础信息列表 - 支持筛选和排序 -->
    <select id="selectMallSpuBaseListWithParams" resultMap="MallSpuBaseResultMap">
        <include refid="MallSpuBaseViewSql"/>
        <if test="companyId != null">
            AND cs.company_id = #{companyId}
        </if>
        <!-- 价格区间筛选 -->
        <if test="minPrice != null">
            AND cs.sale_price_min &gt;= #{minPrice}
        </if>
        <if test="maxPrice != null">
            AND cs.sale_price_min &lt;= #{maxPrice}
        </if>
        <!-- 动态排序 -->
        <choose>
            <when test="orderBy != null and orderBy != ''">
                ORDER BY ${orderBy}
            </when>
            <otherwise>
                ORDER BY cs.create_time DESC
            </otherwise>
        </choose>
        <!-- 分页 -->
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- 获取商城商品基础信息列表 - 计算总数 -->
    <select id="countMallSpuBaseList" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM biz_company_spu cs
        JOIN biz_supplier_spu ss ON cs.spu_id = ss.id
        JOIN t_gen_product_category pc ON cs.category_id = pc.id
        WHERE cs.is_deleted = 0 
          AND ss.is_deleted = 0
          AND pc.is_deleted = 0
          AND cs.status = 1
          AND ss.status = 1
        <if test="companyId != null">
            AND cs.company_id = #{companyId}
        </if>
        <!-- 价格区间筛选 -->
        <if test="minPrice != null">
            AND cs.sale_price_min &gt;= #{minPrice}
        </if>
        <if test="maxPrice != null">
            AND cs.sale_price_min &lt;= #{maxPrice}
        </if>
    </select>

</mapper> 