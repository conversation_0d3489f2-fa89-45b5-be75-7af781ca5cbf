package com.linkBuy.redis.serializer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.ParserConfig;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

/**
 * 通用的FastJSON Redis序列化器
 * 支持任意Object类型的序列化和反序列化
 */
public class GenericFastJsonRedisSerializer implements RedisSerializer<Object> {

    public static final Charset DEFAULT_CHARSET = StandardCharsets.UTF_8;

    static {
        // 开启全局AutoType支持，但建议在生产环境中谨慎使用
        ParserConfig.getGlobalInstance().setAutoTypeSupport(true);
        // 添加受信任的包前缀，提高安全性
        ParserConfig.getGlobalInstance().addAccept("com.linkBuy.");
        ParserConfig.getGlobalInstance().addAccept("java.lang.");
        ParserConfig.getGlobalInstance().addAccept("java.util.");
        ParserConfig.getGlobalInstance().addAccept("java.time.");
    }

    @Override
    public byte[] serialize(Object object) throws SerializationException {
        if (object == null) {
            return new byte[0];
        }
        try {
            // 使用WriteClassName特性来保存类型信息，便于反序列化时恢复原始类型
            return JSON.toJSONString(object, SerializerFeature.WriteClassName).getBytes(DEFAULT_CHARSET);
        } catch (Exception ex) {
            throw new SerializationException("Could not serialize: " + ex.getMessage(), ex);
        }
    }

    @Override
    public Object deserialize(byte[] bytes) throws SerializationException {
        if (bytes == null || bytes.length <= 0) {
            return null;
        }
        try {
            String str = new String(bytes, DEFAULT_CHARSET);
            // 使用parseObject自动识别类型
            return JSON.parseObject(str, Object.class);
        } catch (Exception ex) {
            throw new SerializationException("Could not deserialize: " + ex.getMessage(), ex);
        }
    }
} 